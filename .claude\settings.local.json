{"permissions": {"allow": ["Bash(npm init:*)", "Bash(npm install:*)", "Bash(npx tailwindcss:*)", "Bash(./node_modules/.bin/tailwindcss:*)", "Bash(npm run build-css-prod:*)", "Bash(pnpm init:*)", "Bash(pnpm add:*)", "Bash(pnpm run:*)", "Bash(pnpm exec tailwindcss:*)", "Bash(pnpm remove:*)", "Bash(dotnet build)", "<PERSON><PERSON>(wget:*)", "<PERSON><PERSON>(curl:*)", "Read(//tmp/**)", "Bash(npm run build-css:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(dotnet build:*)", "Bash(csharp:*)", "Bash(find:*)", "Bash(dotnet clean:*)", "Bash(dotnet run)", "Bash(dotnet run:*)", "Bash(dotnet msbuild:*)", "Bash(tree:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(dos2unix:*)", "Bash(dotnet restore:*)", "mcp__ide__getDiagnostics", "Bash(node:*)", "<PERSON><PERSON>(timeout:*)", "Bash(pwsh:*)", "Bash(grep:*)", "Bash(./check_missing_keys.sh:*)", "Bash(bash:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(comm:*)", "Bash(for key in MarkAsClosed MarkAsResolved)", "Bash(do echo \"=== $key ===\")", "Bash(done)", "Bash(for key in UpdateError WaitingForResponse Spam)", "Bash(for key in AllStatus BusinessCooperation CareerInquiry Closed Company Complaint ContactInfo CreatedAt DealResult DealResultPlaceholder DealResultSaved Email Filter GeneralInquiry Important InProgress JobTitle MarkAsClosed MarkAsImportant MarkAsInProgress MarkAsResolved MarkAsWaitingForResponse MediaInquiry MessageContent MessageDetails MessageInfo MessageManagement MessageManagementDescription Name New NewMessage NewMessages NoMessages NoTitle Other PartnershipInquiry Phone ProcessedAt ProductInquiry RemoveImportant Resolved SaveDealResult Search SearchPlaceholder ServiceInquiry SourcePage Spam StatusFilter TechnicalSupport Today Total Type Unknown UpdatedAt WaitingForCustomer WaitingForResponse)", "Bash(do if grep -q \"name=\"\"$key\"\"\" src/Web/Resources/AdminResource.ja.resx)", "Bash(then echo \"✓ $key\")", "Bash(else echo \"✗ MISSING: $key\")", "Bash(fi)", "Bash(xmllint:*)", "Bash(for key in CareerInquiry Closed MarkAsClosed MarkAsResolved MarkAsWaitingForResponse MediaInquiry Name PartnershipInquiry ProductInquiry RemoveImportant Resolved ServiceInquiry Spam TechnicalSupport Total WaitingForResponse)", "Bash(do)", "<PERSON><PERSON>(echo:*)", "Bash(for lang in \"\" \".en\" \".ja\")", "Bash(file=\"/mnt/d/Codes/MlSoft.Sites/src/Web/Resources/AdminResource$lang.resx\")", "Bash(if grep -q \"name=\"\"$key\"\"\" \"$file\")", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(else)", "Bash(for key in CareerInquiry Closed MarkAsClosed MarkAsResolved MarkAsWaitingForResponse MediaInquiry PartnershipInquiry ProductInquiry RemoveImportant Resolved ServiceInquiry Spam TechnicalSupport Total WaitingForResponse)", "Bash(do grep -A1 \"name=\"\"$key\"\"\" /mnt/d/Codes/MlSoft.Sites/src/Web/Resources/AdminResource.resx)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(powershell:*)", "WebSearch", "Bash(for key in FormFields_BackgroundColor_Primary FormFields_BackgroundColor_Secondary FormFields_BackgroundColor_Accent FormFields_BackgroundColor_Gray FormFields_BackgroundColor_Transparent WhiteText BlackText PrimaryText GrayText AlignLeft AlignCenter AlignRight SmallSize MediumSize LargeSize TightSpacing NormalSpacing LooseSpacing NoBorderRadius SmallBorderRadius NormalBorderRadius LargeBorderRadius FullBorderRadius FadeInAnimation SlideInLeftAnimation SlideInRightAnimation ScaleInAnimation)", "Bash(do echo \"=== 检查 $key ===\")", "Bash(if grep -q \"name=\"\"$key\"\"\" FormResource.resx)", "Bash(then echo \"✓ $key 存在\")", "Bash(else echo \"✗ 缺失: $key\")", "Bash(for key in PageNumber ItemsPerPage Total DisplayRange Url Parameters ShowFirstLast ShowPrevNext ShowPageNumbers ShowTotal)"], "deny": [], "ask": []}}