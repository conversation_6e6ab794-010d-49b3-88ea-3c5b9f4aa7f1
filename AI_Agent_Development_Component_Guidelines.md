# Component Development Guidelines

This guide provides comprehensive instructions for AI agents to develop components in the ASP.NET Core MVC web application using Flowbite + Tailwind CSS framework.

## Component Architecture Overview

A component in this system consists of three main parts:

1. **Entity (ViewModel)**: Located in `src/Web/ViewModels/Components/`
2. **ViewComponent**: Located in `src/Web/ViewComponents/`
3. **Views**: Located in `src/Web/Views/Shared/Components/`

## 1. Entity (ViewModel) Development

### Location
- **Path**: `src/Web/ViewModels/Components/`
- **Naming**: `{ComponentName}ComponentViewModel.cs`

### Structure Requirements
- Properties should be nullable where appropriate
- Include default values for essential properties
- Use descriptive property names
- Follow C# naming conventions

### Example Structure
```csharp
namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class HeroComponentViewModel
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Description { get; set; }
        public string? BackgroundImage { get; set; }
        public string? BackgroundVideo { get; set; }

        // Button configuration
        public string? PrimaryButtonText { get; set; }
        public string? PrimaryButtonUrl { get; set; }
        public string? SecondaryButtonText { get; set; }
        public string? SecondaryButtonUrl { get; set; }

        // Display settings with defaults
        public bool ShowScrollIndicator { get; set; } = true;
        public string? OverlayOpacity { get; set; } = "0.5";
        public string? TextAlignment { get; set; } = "center";
        public bool ShowOverlay { get; set; } = true;
        public string? Height { get; set; } = "large";
        public bool AnimationEnabled { get; set; } = true;
        public bool ParallaxEnabled { get; set; } = false;
        public float ParallaxSpeed { get; set; } = 0.5f;
    }
}
```

## 2. ViewComponent Development

### Location
- **Path**: `src/Web/ViewComponents/`
- **Naming**: `{ComponentName}ViewComponent.cs`

### Requirements
- **MUST** inherit from `BaseViewComponent`
- **MUST** implement `InvokeAsync` method
- **MUST** call `InvokeViewAsync<T>` with the appropriate ViewModel type
- Include proper dependency injection for required services

### Template Structure
```csharp
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using MlSoft.Sites.Web.Services;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class {ComponentName}ViewComponent : BaseViewComponent
    {
        public {ComponentName}ViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<{ComponentName}ViewComponent> logger) : base(componentConfigService, logger)
        {
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            return await base.InvokeViewAsync<{ComponentName}ComponentViewModel>(model, "{ComponentId}", variant);
        }
    }
}
```

### Key Points
- The `componentId` parameter should match the folder name in Views
- The generic type `<T>` should match your ViewModel class
- Always use `async/await` pattern
- Include proper error handling through the base class

## 3. View Development

### Structure
Each component view consists of:
- **Folder**: `src/Web/Views/Shared/Components/{ComponentId}/`
- **View File**: `{Variant}.cshtml` (e.g., `Default.cshtml`, `TwoCols.cshtml`)
- **Configuration File**: `{variant}.json` (e.g., `default.json`, `twocols.json`)

### View File Requirements

#### Model Declaration
```csharp
@model MlSoft.Sites.Web.ViewModels.Components.{ComponentName}ComponentViewModel
```

#### Required Imports
```csharp
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedRes
```

#### Data Processing Pattern
```csharp
@{
    // Extract data from ViewModel with null-safe defaults
    var title = string.IsNullOrEmpty(Model?.Title) ? "Default Title" : Model?.Title;
    var subtitle = string.IsNullOrEmpty(Model?.Subtitle) ? "" : Model?.Subtitle;
    var showOverlay = Model?.ShowOverlay ?? true;

    // Generate CSS classes based on properties
    var heightClass = GetHeightClass(height);
    var textAlignClass = JObjectHelper.GetTextAlignmentClass(textAlignment);

    // Create unique IDs for the component
    var uniqueId = JObjectHelper.GenerateId("componentname");

    // Helper methods for dynamic class generation
    string GetHeightClass(string height) => height switch
    {
        "small" => "min-h-[50vh]",
        "medium" => "min-h-[70vh]",
        "large" => "min-h-[90vh]",
        "full" => "min-h-screen",
        _ => "min-h-[90vh]"
    };
}
```

### Critical Styling Requirements

#### 1. Flowbite Component Usage (MANDATORY)
This project uses **Flowbite v3.1.2** as the primary UI component library. When developing components:

- **ALWAYS** use Flowbite's pre-built components when available
- **NEVER** recreate components that Flowbite already provides
- Reference the [Flowbite Documentation](https://flowbite.com/docs/getting-started/introduction/) for component usage
- Use Flowbite's JavaScript initialization when required

**Common Flowbite Components to Use:**
```html
<!-- Buttons -->
<button type="button" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">Primary</button>

<!-- Cards -->
<div class="max-w-sm bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
    <div class="p-5">
        <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Card Title</h5>
        <p class="mb-3 font-normal text-gray-700 dark:text-gray-400">Card content</p>
    </div>
</div>

<!-- Forms -->
<div class="mb-5">
    <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Your email</label>
    <input type="email" id="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<EMAIL>" required />
</div>

<!-- Modals -->
<div id="default-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal content -->
        </div>
    </div>
</div>

<!-- Dropdowns -->
<button id="dropdownDefaultButton" data-dropdown-toggle="dropdown" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" type="button">
    Dropdown button
    <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
    </svg>
</button>
```

**Flowbite JavaScript Initialization:**
```javascript
// Initialize Flowbite components after DOM load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dropdowns
    if (window.Dropdown) {
        const dropdownElements = document.querySelectorAll('[data-dropdown-toggle]');
        dropdownElements.forEach(element => {
            new Dropdown(element);
        });
    }
    
    // Initialize modals
    if (window.Modal) {
        const modalElements = document.querySelectorAll('[data-modal-toggle]');
        modalElements.forEach(element => {
            new Modal(element);
        });
    }
});
```

#### 2. Theme Variables (MANDATORY)
- **NEVER** use hardcoded color classes like `blue-600`, `red-500`, etc.
- **ALWAYS** use theme variables: `primary-600`, `primary-700`, `primary-500`
- Flowbite components already use these theme variables by default

```html
<!-- ❌ WRONG -->
<button class="bg-blue-600 hover:bg-blue-700 focus:ring-blue-500">

<!-- ✅ CORRECT -->
<button class="bg-primary-600 hover:bg-primary-700 focus:ring-primary-500">
```

#### 3. Dark Mode Support (MANDATORY)
All elements **MUST** include dark mode variants:

```html
<!-- Form inputs -->
<input class="border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">

<!-- Labels -->
<label class="text-gray-700 dark:text-gray-300">

<!-- Containers -->
<div class="bg-white dark:bg-gray-800">

<!-- Checkboxes/Radio -->
<input type="checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
```

#### 4. Responsive Design
Use Tailwind's responsive prefixes:
```html
<div class="text-4xl md:text-6xl">
<div class="flex flex-col sm:flex-row">
<div class="px-4 sm:px-6 lg:px-8">
```

#### 5. Accessibility
- Include proper ARIA labels
- Use semantic HTML elements
- Provide alt text for images
- Ensure keyboard navigation support

```html
<button aria-label="Toggle dark mode" class="...">
<img src="..." alt="Company logo" class="...">
<nav aria-label="Main navigation">
```

### Configuration File (JSON)

Each variant requires a corresponding JSON configuration file that defines:
- Component metadata (names, descriptions)
- Form field definitions for the admin interface
- Validation rules
- File upload configurations

#### Template Structure
```json
{
  "ComponentId": "ComponentName",
  "Id": "VariantName",
  "Names": {
    "zh": "中文名称",
    "en": "English Name",
    "ja": "日本語名"
  },
  "Descriptions": {
    "zh": "中文描述",
    "en": "English Description",
    "ja": "日本語説明"
  },
  "formFields": [
    {
      "name": "Title",
      "type": "multilingual-text",
      "label": "@SharedResource:FormFields_Title",
      "display": {
        "group": "@SharedResource:FormGroups_BasicInfo",
        "width": "col-span-12",
        "order": 1,
        "layout": "inline",
        "collapsed": true,
        "helpText": "@SharedResource:FormFields_TitleHelpText"
      },
      "validation": {
        "required": true,
        "maxLength": 100,
        "minLength": 1
      }
    }
  ]
}
```

#### Available Field Types
- `text`: Single-line text input
- `multilingual-text`: Text input with language tabs
- `textarea`: Multi-line text input
- `multilingual-textarea`: Textarea with language tabs
- `multilingual-richtext`: Rich text editor with language tabs
- `repeater`: Repeater field
- `select`: Dropdown selection
- `checkbox`: Boolean toggle
- `number`: Numeric input
- `image`: Image file upload
- `video`: Video file upload
- `file`: General file upload

#### JSON Form Field Display Rules

**Layout Configuration:**
- **Multilingual text/textarea fields**: Use `display: "inline"` to show all languages in one row
- **Multilingual rich text fields**: Use `display: "stacked"` for vertical layout
- **Configuration options** (checkbox, select, etc.): Can display 3-4 items per row

Example configurations:
```json
{
  "name": "Title",
  "type": "multilingual-text",
  "display": {
    "layout": "inline",
    "width": "col-span-12"
  }
},
{
  "name": "Description",
  "type": "multilingual-richtext",
  "display": {
    "layout": "stacked",
    "width": "col-span-12"
  }
},
{
  "name": "ShowOverlay",
  "type": "checkbox",
  "display": {
    "width": "col-span-3"
  }
}
```

#### File Upload Configuration
```json
{
  "name": "BackgroundImage",
  "type": "image",
  "fileConfig": {
    "folder": "hero",
    "types": ["image/*"],
    "maxSize": "5MB",
    "multiple": false,
    "preview": true
  }
}
```

#### Prescriptive Display Rules (MANDATORY)

- **Multilingual fields** (`multilingual-text`, `multilingual-textarea`, `multilingual-richtext`)
  - **width**: `col-span-12`
  - **layout**: `inline` (rich text may use `stacked` if needed for readability)
  - **collapsed**: `true`
  - Example:
  ```json
  {
    "name": "Title",
    "type": "multilingual-text",
    "display": {
      "width": "col-span-12",
      "layout": "inline",
      "collapsed": true
    }
  }
  ```

- **URL/text and file fields** (e.g., URL, `text`, `image`, `file`, `video`)
  - **width**: `col-span-12`
  - **layout**: `inline`
  - **collapsed**: `true`
  - Example:
  ```json
  {
    "name": "LinkUrl",
    "type": "text",
    "display": {
      "width": "col-span-12",
      "layout": "inline",
      "collapsed": true
    }
  }
  ```
  ```json
  {
    "name": "BannerImage",
    "type": "image",
    "display": {
      "width": "col-span-12",
      "layout": "inline",
      "collapsed": true
    }
  }
  ```

- **Other controls** (`checkbox`, `select`, etc.)
  - **width**: `col-span-3` or `col-span-4`
  - **layout**: `inline`
  - **collapsed**: `true`
  - Example:
  ```json
  {
    "name": "ShowOverlay",
    "type": "checkbox",
    "display": {
      "width": "col-span-3",
      "layout": "inline",
      "collapsed": true
    }
  }
  ```

## 4. Multilingual Support

### Resource File Integration
- Use resource keys with `@SharedResource:`, `@FormResource:`, etc.
- Follow the pattern: `@ResourceFile:KeyName`
- Ensure all text content is localizable

### Text Resource Lookup Priority
When looking for text resources, follow this priority order:

1. **Primary**: `src/Web/Resources/FormResource.resx`, `FormResource.en.resx`, `FormResource.ja.resx`
2. **Secondary**: `AdminResource.resx`, `AdminResource.en.resx`, `AdminResource.ja.resx`
3. **Fallback**: `SharedResource.resx`, `SharedResource.ja.resx`, `SharedResource.en.resx`
4. **If not found**: Add the missing resource to FormResource files

Example usage:
```csharp
@FormResource["FieldName"]  // First priority
@AdminResource["FieldName"] // If not found in FormResource
@SharedResource["FieldName"] // Final fallback
```

### Dynamic Language Generation
For multilingual form fields, use dynamic generation:

```csharp
@foreach (var lang in (SupportedLanguage[])ViewData["SupportedLanguages"])
{
    <div>
        <label for="<EMAIL>">@AdminRes["Title"] (@lang.Name)</label>
        <input id="<EMAIL>" name="<EMAIL>" />
    </div>
}
```

## 5. JavaScript Integration

### File Upload (MANDATORY)
Use the global `AdminUpload.bind` utility:

```javascript
document.addEventListener('DOMContentLoaded', function() {
    var fileInput = document.getElementById('file_myField');
    var fileListEl = document.getElementById('filelist_myField');
    if (fileInput && fileListEl && window.AdminUpload) {
        AdminUpload.bind(fileInput, {
            fileListEl: fileListEl,
            fileConfig: {
                types: ['image/*'],
                multiple: true,
                folder: 'component',
                maxSize: '10MB',
                preview: true
            }
        });
    }
});
```

### Dialog System (MANDATORY)
Use the custom Dialog system instead of native alerts:

```javascript
// Success notifications
Dialog.notify('Operation completed successfully', 'success');

// Error messages
Dialog.error('Operation failed', 'Error');

// Confirmation dialogs
const confirmed = await Dialog.confirm('Are you sure?');
if (confirmed) {
    // User confirmed
}
```

### Resource Access Pattern
```javascript
const message = window.Resources?.Admin?.PropertyName || 'Fallback text';
const validationError = window.Resources?.Shared?.ValidationError || 'Error';
```

## 6. Development Workflow

### Step-by-Step Process

1. **Create ViewModel**
   - Define properties with appropriate types
   - Include default values
   - Add XML documentation if needed

2. **Create ViewComponent**
   - Inherit from BaseViewComponent
   - Implement InvokeAsync method
   - Use proper dependency injection

3. **Create View File**
   - Follow naming convention: `{Variant}.cshtml`
   - Include proper model declaration
   - Implement responsive design with dark mode support
   - Use theme variables for colors

4. **Create Configuration File**
   - Define component metadata
   - Configure form fields for admin interface
   - Set up validation rules
   - Configure file upload settings if needed

5. **Testing Checklist**
   - ✅ Test in light and dark modes
   - ✅ Test responsive behavior on different screen sizes
   - ✅ Verify all theme variants work correctly
   - ✅ Test multilingual functionality
   - ✅ Validate form field configurations
   - ✅ Test file upload functionality if applicable

## 7. Best Practices

### Code Organization
- Keep helper methods within the view file scope
- Use meaningful variable names
- Comment complex logic
- Follow consistent indentation

### Performance Considerations
- Use `loading="eager"` for above-the-fold images
- Include `fetchpriority="high"` for critical images
- Minimize DOM queries in JavaScript
- Use CSS animations instead of JavaScript when possible

### Security
- Always validate file uploads
- Sanitize user input
- Use proper CSRF protection
- Avoid exposing sensitive information

### Maintainability
- Use consistent naming conventions
- Keep component logic separate from presentation
- Document complex configurations
- Follow the established patterns

## 8. Common Patterns

### Conditional Rendering
```csharp
@if (!string.IsNullOrEmpty(backgroundImage))
{
    <div class="background-container">
        <img src="@ProcessFilePath(backgroundImage)" alt="" />
    </div>
}
```

### Dynamic CSS Classes
```csharp
var buttonClass = type switch
{
    "primary" => "bg-primary-600 hover:bg-primary-700 text-white",
    "outline" => "border-primary-600 text-primary-600 hover:bg-primary-50",
    _ => "bg-primary-600 hover:bg-primary-700 text-white"
};
```

### File Path Processing
```csharp
string ProcessFilePath(string filePath) =>
    string.IsNullOrEmpty(filePath) ? "" :
    filePath.StartsWith("/") ? filePath : $"/{filePath}";
```

## 9. Validation Checklist

Before completing component development, ensure:

- [ ] ✅ **Uses Flowbite components instead of custom implementations**
- [ ] ✅ Flowbite JavaScript properly initialized if required
- [ ] ✅ Includes Tailwind CSS classes with theme variables
- [ ] ✅ Works in both light and dark modes
- [ ] ✅ **NO hardcoded color classes** (blue-, red-, green-, etc.)
- [ ] ✅ Text content uses resource files
- [ ] ✅ Multilingual fields use dynamic generation
- [ ] ✅ Responsive design implemented
- [ ] ✅ JavaScript uses Dialog system and AdminUpload utility
- [ ] ✅ Configuration JSON file properly structured
- [ ] ✅ All file paths processed correctly
- [ ] ✅ Accessibility features included
- [ ] ✅ Performance optimizations applied

## 10. Example Component Structure

```
src/Web/Views/Shared/Components/Hero/
├── Default.cshtml          # Main variant view
├── default.json           # Configuration for Default variant
├── TwoCols.cshtml         # Alternative variant view
└── twocols.json           # Configuration for TwoCols variant

src/Web/ViewModels/Components/
└── HeroComponentViewModel.cs

src/Web/ViewComponents/
└── HeroViewComponent.cs
```

This structure ensures consistency, maintainability, and adherence to the application's architectural patterns. Follow these guidelines to create robust, themeable, and multilingual components that integrate seamlessly with the existing system.