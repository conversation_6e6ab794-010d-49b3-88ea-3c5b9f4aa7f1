# AI Agent Development Guidelines

## Project Overview

This is an ASP.NET Core MVC web application using Flowbite + Tailwind CSS framework for Japanese corporate websites, particularly for chemical industry companies.

## Development Constraints (MUST FOLLOW)

### 1. UI Framework & Styling
- **Primary Framework**: Flowbite + Tailwind CSS
- **Requirement**: Use Flowbite components and Tailwind CSS classes whenever possible
- **Documentation**: Refer to [Flowbite Components](https://flowbite.com/docs/components/) for available components

### 2. Theme System
- **Multi-theme Support**: The site supports theme switching
- **Theme Files Location**: `wwwroot/css/themes/`
- **Active Theme**: `wwwroot/css/active-theme.css`
- **CRITICAL**: When adding new styles, you MUST update:
  - `wwwroot/css/active-theme.css`
  - ALL theme files in `wwwroot/css/themes/` directory
- **Available Themes**:
  - `business-blue.css` / `business-blue-dark.css`
  - `elegant-purple.css`
  - `nature-green.css` / `nature-green-dark.css`
  - `warm-orange.css`

#### Theme Variables Usage (CRITICAL)
- **REQUIREMENT**: ALL color styles MUST use theme variables, NOT hardcoded colors
- **Primary Colors**: Use `primary-*` classes (e.g., `primary-600`, `primary-700`, `primary-500`)
- **FORBIDDEN**: Hardcoded color classes like `blue-600`, `blue-500`, `red-600` etc.
- **Common Problem Areas**:
  - Button styles: Use `bg-primary-600 hover:bg-primary-700` instead of `bg-blue-600 hover:bg-blue-700`
  - Focus states: Use `focus:ring-primary-500 focus:border-primary-500` instead of `focus:ring-blue-500 focus:border-blue-500`
  - Tab navigation: Use `text-primary-600 border-primary-500` instead of `text-blue-600 border-blue-500`
  - Form inputs: Use `focus:border-primary-500` instead of `focus:border-blue-500`
  - Alert/notification backgrounds: Use `bg-primary-50 border-primary-200` instead of `bg-blue-50 border-blue-200`
- **JavaScript Considerations**: When adding dynamic styles via JS, also use primary theme classes
- **Validation**: Before completing any task, search for hardcoded color classes (blue-, red-, green-, etc.) and replace with theme variables

### 3. Dark Mode Support
- **Requirement**: ALL UI development must support both light and dark modes
- **Implementation**: Use Tailwind's dark mode classes (`dark:`)
- **Testing**: Verify both modes work correctly before completing tasks

#### Critical Dark Mode Requirements (MANDATORY)
- **Form Inputs**: ALL form inputs (input, select, textarea) MUST include dark mode styles:
  ```css
  class="border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
  ```
- **Labels**: ALL labels must include dark text variants:
  ```css
  class="text-gray-700 dark:text-gray-300"
  ```
- **Checkboxes/Radio**: Must include a full set of classes for proper styling in both modes:
  ```css
  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
  ```
- **Containers**: All background containers must have dark variants:
  ```css
  class="bg-white dark:bg-gray-800"
  ```
- **Inactive/Default Elements**: Ensure that default states for elements like buttons or tabs also have appropriate `dark:` mode styles for text, borders, and backgrounds. Do not only style the `hover:` or `active:` states.

**❌ FORBIDDEN**: Form elements without dark mode support
**✅ REQUIRED**: Every interactive element must have appropriate dark mode styling

### 4. CSS Modification Rules
- **Restricted File**: `wwwroot/css/input.css` - DO NOT modify unless absolutely necessary
- **Custom Styles**: Add to theme files following the pattern above

### 5. JavaScript Guidelines
- **Global JS**: Place common/global JavaScript in `wwwroot/js/site.js`
- **Component-specific JS**: Keep with respective components when possible

#### Theming in JavaScript (e.g., for Charts)
- **CRITICAL RULE**: DO NOT hardcode colors (e.g., hex codes like `#4e73df` or tailwind color names like `'blue-500'`) in JavaScript. This breaks theme and dark mode functionality.
- **REQUIREMENT**: Dynamically retrieve theme colors from CSS variables.

**✅ CORRECT USAGE**: Create a helper function to read CSS variables and use it to configure libraries like Chart.js.
```javascript
// 1. Helper function to get theme colors
function getThemeColor(name) {
    // Reads a CSS variable like --color-primary-600
    return getComputedStyle(document.documentElement).getPropertyValue(`--color-${name}`).trim();
}

// 2. Use the helper in your chart configuration
const myChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        datasets: [{
            backgroundColor: [
                getThemeColor('primary-600'),
                getThemeColor('success-600'),
                getThemeColor('warning-500')
            ]
        }]
    }
});
```

**❌ WRONG (Avoid)**:
```javascript
// DO NOT hardcode colors in JavaScript
const myChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        datasets: [{
            backgroundColor: [
                '#4e73df',       // Hardcoded Hex
                '#1cc88a',
                '#f6c23e'
            ]
        }]
    }
});
```

#### JavaScript Resource Pattern (MANDATORY)
- **CRITICAL RULE**: Use `window.Resources?.Admin?.PropertyName` pattern for accessing localized strings in JavaScript
- **GLOBAL RESOURCE LOADING**: Resource files are automatically loaded in `_AdminLayout.cshtml` by language, DO NOT duplicate loading in individual pages
- **RESOURCE FILE STRUCTURE**: Resources are organized in `/wwwroot/js/resources/` directory:
  - `adminresource.{language}.js` - Admin area resources
  - `sharedresource.{language}.js` - Shared resources
  - `formresource.{language}.js` - Form-related resources
  - `accountresource.{language}.js` - Account-related resources

**✅ CORRECT USAGE**:
```javascript
// Direct usage - resources are already loaded globally
const processingMessage = window.Resources?.Admin?.PleaseWaitProcessing || 'Please wait...';
const validationError = window.Resources?.Admin?.ValidationError || 'Validation error';
const saveSuccess = window.Resources?.Admin?.SaveBasicInfoSuccess || 'Saved successfully';

// For conditional checks
if (window.Resources?.Admin?.ComponentType) {
    showMessage(window.Resources.Admin.ComponentType);
}
```

**❌ WRONG (Avoid)**:
```javascript
// DO NOT include resource script tags in individual pages
<script src="~/js/resources/adminresource.zh.js"></script>

// DO NOT define inline resource objects
const AdminMessages = {
    PleaseWaitProcessing: '@Html.Raw(AdminRes["PleaseWaitProcessing"])',
    // ... other properties
};
```

**Development Workflow**:
1. **Add Resources**: Continue adding text keys to .resx files as before
2. **Auto-Generation**: System automatically generates updated .js resource files
3. **Usage**: Directly reference `window.Resources.{AreaName}.{PropertyName}` in JavaScript
4. **Fallbacks**: Always include fallback values using `||` operator
5. **Error Prevention**: Use optional chaining `?.` to prevent runtime errors

**Benefits**:
- Centralized resource management through layout
- Automatic language switching
- No duplicate resource loading
- Consistent API across all pages
- Better maintainability and performance

#### Dialog System (MANDATORY)
- **CRITICAL RULE**: DO NOT use native `alert()` or `confirm()` functions
- **REQUIREMENT**: Use the custom Dialog system implemented in `site.js`
- **GLOBAL INITIALIZATION**: Dialog system is automatically initialized in `_AdminLayout.cshtml`, DO NOT initialize in individual pages
- **Available Methods**:
  - `Dialog.alert(message, title, type)` - Replaces `alert()`
  - `Dialog.confirm(message, title, options)` - Replaces `confirm()`
  - `Dialog.warning(message, title)` - Warning dialogs
  - `Dialog.error(message, title)` - Error dialogs
  - `Dialog.success(message, title)` - Success dialogs
  - `Dialog.info(message, title)` - Information dialogs
  - `Dialog.notify(message, type, duration)` - Bottom-right notifications with auto-dismiss

**✅ CORRECT USAGE**:
```javascript
// Direct usage - Dialog system is initialized globally
Dialog.alert('Message here');
const confirmed = await Dialog.confirm('Are you sure?');
Dialog.notify('Operation completed', 'success');
```

**❌ WRONG (Avoid)**:
```javascript
// DO NOT initialize Dialog system in individual pages
Dialog.init({
    alert: window.Resources?.Shared?.Alert || 'Alert',
    // ... other properties
});

// DO NOT use native functions
alert('Message');
confirm('Are you sure?');
```

#### Success Message Convention (NEW)
- **CRITICAL RULE**: For frontend operations that succeed, use `Dialog.notify()` instead of `alert()` or `Dialog.success()`
- **For operation failures**: Continue using `alert()` or `Dialog.error()` as before
- **Notify Parameters**:
  - `message` (string): The message to display
  - `type` (string, optional): 'success', 'info', 'warning', 'error' (default: 'success')
  - `duration` (number, optional): Auto-dismiss time in milliseconds (default: 3000)

**Usage Examples**:
```javascript
// Instead of: alert('Hello World');
Dialog.alert('Hello World');

// Instead of: if (confirm('Are you sure?'))
const result = await Dialog.confirm('Are you sure?');
if (result) {
    // User confirmed
}

// Error messages (keep using alert/error)
Dialog.error('Operation failed', 'Error');

// Success messages (NEW: use notify instead of Dialog.success)
// ❌ OLD WAY: Dialog.success('Operation completed successfully');
// ✅ NEW WAY:
Dialog.notify('Operation completed successfully', 'success');

// Other notify examples:
Dialog.notify('File uploaded successfully', 'success', 5000);
Dialog.notify('Settings saved', 'success');
Dialog.notify('Processing complete', 'info');
```



**Benefits**:
- Consistent visual design across the application
- Automatic theme support (colors adapt to current theme)
- Multi-language support
- Async/await compatible
- Better UX with animations and keyboard navigation

#### TinyMCE Editor Usage (MANDATORY)
- CRITICAL RULE: 非特别情况，后台页面初始化富文本请统一使用共用方法 `AdminTinyMCE`，不要在各自页面中直接调用 `tinymce.init()`。
- Initializer Location: `wwwroot/js/admin.js`（全局对象 `window.AdminTinyMCE`）
- Dark Mode & Language: 由共用方法自动处理（根据 `document.documentElement.lang` 与暗色样式类）
- Selector Convention: 在需要的 `<textarea>` 上添加类名 `tinymce-editor`

✅ 标准用法（批量初始化推荐）
```html
<!-- 给需要的 textarea 添加类名 -->
<textarea class="tinymce-editor"></textarea>

<script>
document.addEventListener('DOMContentLoaded', function() {
  if (window.AdminTinyMCE) {
    AdminTinyMCE.initAll('.tinymce-editor', { menubar: false });
  }
});
</script>
```

✅ 标准用法（单个元素）
```javascript
const el = document.querySelector('#content');
if (el && window.AdminTinyMCE) {
  AdminTinyMCE.init(el, { height: 300 });
}
```

❌ 避免（除非特殊需求）
```javascript
// 不要在页面内手写 tinymce.init 全量配置
tinymce.init({ selector: '.tinymce-editor', /* ... */ });
```

Form 提交前同步内容（仍需保留）
```javascript
// 提交前确保内容回写到 textarea
if (typeof tinymce !== 'undefined') {
  tinymce.triggerSave();
}
```

说明
- 共用方法已内置：语言、暗色主题、默认插件与工具栏、内容样式、安全 ID 处理。
- 页面确需自定义时，通过 `AdminTinyMCE.init / initAll` 传入配置增量覆盖，避免复制粘贴整段 tinymce 配置。
- 历史页面若已有自定义初始化，可逐步改造为优先调用 `AdminTinyMCE`，并保留原逻辑作为回退以降低风险。

#### File Upload Utility (MANDATORY)
- CRITICAL RULE: 后台页面涉及文件/图片/视频上传，统一使用全局工具 `AdminUpload.bind`，不要在各页面各自编写上传/拖拽/删除逻辑。
- Initializer Location: `wwwroot/js/admin.js`（全局对象 `window.AdminUpload`）
- Server API: 统一走 `/api/FileUpload/upload` 与 `/api/FileUpload/delete`，参数由工具封装。

✅ 标准用法（绑定到现有的 input[type="file"]）
```html
<div class="flex items-center justify-center w-full">
  <label class="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 dark:border-gray-600 transition-colors">
    <!-- 内部提示内容省略 -->
  </label>
  <input id="file_myField" type="file" name="myField" class="hidden" />
</div>
<div id="filelist_myField" class="mt-4 space-y-2"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  var fileInput = document.getElementById('file_myField');
  var fileListEl = document.getElementById('filelist_myField');
  if (fileInput && fileListEl && window.AdminUpload) {
    AdminUpload.bind(fileInput, {
      fileListEl: fileListEl,
      fileConfig: {
        types: ['image/*'],    // 接受的类型，示例：['image/*','application/pdf']
        multiple: true,        // 是否多选
        folder: 'news',        // 服务器保存目录分类
        maxSize: '10MB',       // 最大大小提示（服务器仍应校验）
        preview: true          // 是否启用预览按钮（图片/视频/PDF 可预览）
      },
      getResource: function(key, fallback, ns) {
        // 建议直接透传到全局资源，也可使用 fallback
        return window.Resources?.Shared?.[key] || window.Resources?.Admin?.[key] || fallback || key;
      }
    });
  }
});
</script>
```

说明与约束
- `AdminUpload.bind(input, options)` 仅需提供：
  - `fileListEl`: 文件列表容器元素
  - `fileConfig`: 上传约束（见示例）；与后端约定的 `folder`、`types`、`maxSize` 会作为表单字段提交
  - `getResource`: 可选 i18n 回调，未提供则使用英文回退
- 工具内置：
  - 选择/拖拽文件、进度占位、失败提示、成功渲染文件卡片
  - 统一删除流程（确认 -> 调用 `/api/FileUpload/delete` -> DOM 移除 -> 通知）
  - 文件图标/大小格式化、可预览类型判断（image/pdf/video）
- 页面如需完全自定义 UI，请仍然调用 `AdminUpload.bind` 获取统一行为，避免重复实现事件与请求逻辑。

与动态表单集成
- 动态表单渲染器 `form-field-renderer.js` 已内置优先调用 `AdminUpload.bind`，若全局工具不可用则自动回退到内部实现，业务无需额外处理。

### 6. Multilingual Support
- **Supported Languages**:
  - `zh` (Chinese - Default)
  - `en` (English)
  - `ja` (Japanese)
- **Resource Files Location**: `src/Web/Resources/`
- **File Pattern**: `SharedResource.[language].resx`
- **REQUIREMENT**: ALL text content must be stored in resource files
- **Action Required**: If resource file doesn't exist for a language, create it

#### Dynamic Multilingual Interface Generation (CRITICAL)
- **CRITICAL RULE**: For multilingual form fields, use dynamic generation via `@foreach` loop, NOT hardcoded language lists
- **REQUIREMENT**: Use `(SupportedLanguage[])ViewData["SupportedLanguages"]` to iterate through supported languages
- **Pattern**: Three approved patterns for multilingual interfaces:
  1. **Inline Field Pattern**: Best for simple forms with few fields
  2. **Tab Pattern**: Best for complex forms with many fields
  3. **Modal Tab Pattern**: Best for modal dialogs with multilingual fields

**✅ CORRECT - Modal Tab Pattern (Dynamic Generation)**:
```html
<!-- Dynamic multilingual tabs in modal -->
<div class="border-b border-gray-200 dark:border-gray-600 mb-4">
    <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
        @{
            var supportedLanguages = (SupportedLanguage[])ViewData["SupportedLanguages"];
            var isFirst = true;
        }
        @foreach (var lang in supportedLanguages)
        {
            <button type="button" onclick="switchModalLanguageTab('@lang.Code')"
                    class="modal-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-lang="@lang.Code">
                @lang.Emoji @lang.Name
            </button>
            isFirst = false;
        }
    </nav>
</div>

<!-- Dynamic multilingual content -->
@{
    isFirst = true;
}
@foreach (var lang in supportedLanguages)
{
    <div id="<EMAIL>" class="modal-lang-content @(isFirst ? "" : "hidden")">
        <div class="grid grid-cols-1 gap-4">
            <div>
                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    @AdminRes["FieldName"] (@lang.Name) @if(lang.Code == ViewData["CurrentLanguage].ToString()){
                    <span class="text-red-500">*</span>
                }
                </label>
                <input type="text" id="<EMAIL>" name="<EMAIL>"
                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                       required="@(lang.Code == ViewData["DefaultLanguage"].ToString())" />
            </div>
        </div>
    </div>
    isFirst = false;
}
```

**JavaScript for Modal Tab Switching**:
```javascript
// Modal-specific language tab switching
window.switchModalLanguageTab = function(lang) {
    // Remove active class from all modal language tab buttons
    document.querySelectorAll('.modal-lang-tab-button').forEach(btn => {
        btn.classList.remove('active', 'border-primary-500', 'text-primary-600');
        btn.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    });

    // Add active class to selected button
    const activeBtn = document.querySelector(`.modal-lang-tab-button[data-lang="${lang}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active', 'border-primary-500', 'text-primary-600');
        activeBtn.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    }

    // Hide all modal language content
    document.querySelectorAll('.modal-lang-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Show selected modal language content
    const activeContent = document.getElementById(`modal-lang-${lang}`);
    if (activeContent) {
        activeContent.classList.remove('hidden');
    }
};

// Initialize first language tab when modal opens
function openModal() {
    // ... other modal setup code ...

    // Initialize first language tab
    const firstLangBtn = document.querySelector('.modal-lang-tab-button');
    if (firstLangBtn) {
        switchModalLanguageTab(firstLangBtn.getAttribute('data-lang'));
    }
}
```

**✅ CORRECT - Tab Pattern (Dynamic Generation)**:
```html
<!-- 动态生成标签页导航 -->
<nav class="-mb-px flex space-x-8">
    @{
        var supportedLanguages = (SupportedLanguage[])ViewData["SupportedLanguages"];
        var isFirst = true;
    }
    @foreach (var lang in supportedLanguages)
    {
        <button onclick="switchLanguageTab('@lang.Code')"
                class="lang-tab-button @(isFirst ? "active" : "") ..."
                data-lang="@lang.Code">
            @lang.Emoji @lang.Name
        </button>
        isFirst = false;
    }
</nav>

<!-- 动态生成标签页内容 -->
@{
    isFirst = true;
}
@foreach (var lang in supportedLanguages)
{
    <div id="<EMAIL>" class="lang-content @(isFirst ? "" : "hidden")">
        <input id="<EMAIL>" name="<EMAIL>" />
    </div>
    isFirst = false;
}
```

**✅ CORRECT - Inline Field Pattern (Dynamic Generation)**:
```html
<!-- 多语言经营理念 -->
<div>
    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">@AdminRes["Philosophy"]</label>
    <div class="space-y-4">
        @foreach (var lang in (SupportedLanguage[])ViewData["SupportedLanguages"])
        {
            <div>
                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    @AdminRes["Philosophy"] (@lang.Name)
                </label>
                <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
            </div>
        }
    </div>
</div>
```

**❌ WRONG - Hardcoded Language Lists**:
```html
<!-- 不要这样硬编码语言 -->
<button onclick="switchLanguageTab('zh')">中文</button>
<button onclick="switchLanguageTab('en')">English</button>
<button onclick="switchLanguageTab('ja')">日本語</button>
```

**Benefits of Dynamic Generation**:
- Automatically supports future language additions
- Consistent field naming and structure
- Reduces code duplication and maintenance overhead
- Ensures all supported languages are included
- Maintains type safety with SupportedLanguage enum

#### Resource File Organization Strategy
**Recommended Hybrid Approach:**
- **SharedResource**: Common UI texts (buttons, status messages, validation messages, etc.)
  - Examples: `ToggleDarkMode`, `Save`, `Cancel`, `Loading`, `Error`
- **ComponentResource**: Component-specific business texts (when needed)
  - Examples: Component titles, descriptions, specialized terminology
- **PageResource**: Page-level specific texts (when needed)
  - Examples: Page-specific titles, descriptions, content

**File Organization Structure:**
```
Resources/
├── Components/
│   ├── HeaderResource.cs
│   ├── HeaderResource.resx
│   ├── HeaderResource.en.resx
│   ├── HeaderResource.ja.resx
│   ├── FooterResource.cs
│   └── FooterResource.[language].resx
├── Pages/
│   ├── HomePageResource.cs
│   └── HomePageResource.[language].resx
├── SharedResource.cs
├── SharedResource.resx
├── SharedResource.en.resx
└── SharedResource.ja.resx
```

**Advantages:**
- Avoids duplication of common texts
- Maintains appropriate modular management
- Facilitates maintenance and translation work
- Reduces file count, keeps project structure clean
- File names match class names for better tooling support

#### Multilingual URL Generation
**CRITICAL RULE**: For URL generation in Admin controllers, use `@Html.MultilingualUrl()` instead of `@Url.Action()` or `@Url.Content()`

**Problem**: Admin controllers use custom routes like:
```csharp
[Route("Admin/[controller]")]
[Route("{culture}/Admin/[controller]")]
```

Standard `@Url.Action()` generates incorrect URLs without culture prefix and Admin path.

**Solution**: Use the custom `MultilingualUrl` extension method:
```csharp
// ❌ WRONG - generates /PageManage/Publish/ 
@Url.Action("Publish", "PageManage")

// ❌ WRONG - hardcoded path, no culture support
@Url.Content("~/Admin/PageManage/Publish/")

// ✅ CORRECT - generates /zh/Admin/PageManage/Publish/ (or /en/, /ja/)
@Html.MultilingualUrl("Publish", "PageManage")
```

**Usage in JavaScript**:
```javascript
// For AJAX calls in Admin pages
const response = await fetch(`@Html.MultilingualUrl("Publish", "PageManage")/${id}`, {
    method: 'POST',
    // ... other options
});
```

**Benefits**:
- Automatically includes current culture prefix (zh, en, ja)
- Correctly handles Admin path structure
- Maintains consistency with routing configuration
- Supports all languages without hardcoding

## File Structure Reference

```
src/Web/
├── wwwroot/
│   ├── css/
│   │   ├── active-theme.css          # Current active theme
│   │   ├── input.css                 # DO NOT MODIFY
│   │   ├── output.css                # Generated file
│   │   └── themes/                   # Theme files
│   │       ├── business-blue.css
│   │       ├── business-blue-dark.css
│   │       ├── elegant-purple.css
│   │       ├── nature-green.css
│   │       ├── nature-green-dark.css
│   │       └── warm-orange.css
│   └── js/
│       └── site.js                   # Global JavaScript
├── Resources/
│   ├── SharedResource.resx           # Default (zh)
│   ├── SharedResource.en.resx        # English
│   └── SharedResource.ja.resx        # Japanese
└── Views/
    └── Shared/
        └── Components/               # Reusable components
```

## Development Workflow

1. **Before Starting**: Check existing components and themes
2. **UI Development**: 
   - Use Flowbite components first
   - Apply Tailwind CSS classes
   - Include dark mode variants
3. **Styling**: 
   - Reuse existing styles when possible
   - If new styles needed, update ALL theme files
4. **Text Content**: Store in appropriate resource files
5. **Testing**: Verify in all themes and both light/dark modes

## Key Considerations

- **Japanese Corporate Standards**: Professional, conservative design approach
- **Chemical Industry Focus**: Emphasis on safety, compliance, and technical documentation
- **Responsive Design**: Ensure compatibility across devices
- **Performance**: Optimize for loading speed and user experience

## Quick Reference Commands

```bash
# Build CSS (if needed)
npm run build-css

# Development server
dotnet run

# Build project
dotnet build
```

## Validation Checklist

Before completing any UI task, verify:
- [ ] Uses Flowbite components where applicable
- [ ] Includes Tailwind CSS classes
- [ ] Works in both light and dark modes
- [ ] All themes updated if new styles added
- [ ] **NO hardcoded color classes** (blue-, red-, green-, etc.) - use theme variables instead
- [ ] Text content stored in resource files
- [ ] **Multilingual fields use dynamic generation with `@foreach` loop, NOT hardcoded language tabs**
- [ ] Responsive design implemented
- [ ] No modification to `input.css` (unless essential)
- [ ] JavaScript uses primary theme classes for dynamic styling
- [ ] **Success operations use `Dialog.notify()` instead of `alert()` or `Dialog.success()`**
- [ ] **Failure operations continue using `Dialog.error()` or `alert()`**