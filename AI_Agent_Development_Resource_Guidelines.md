# AI Agent Development Resource Guidelines

## 资源管理开发规范

本文档规定了在开发组件时如何正确处理资源项，确保资源的一致性、避免重复和提高可维护性。

## 核心原则

### 1. 避免重复资源项 (CRITICAL)
- **严格禁止** 添加已存在的资源项
- **必须** 在添加新资源前检查现有资源
- **优先** 复用现有资源而非创建新资源

### 2. 复用现有资源 (PRIORITY)
- **能复用尽量复用** - 寻找功能相似的现有资源键值
- **语义匹配** - 选择语义最接近的现有资源
- **一致性** - 保持整个项目的资源命名和分组一致性

## 资源检查流程

### 第一步：文本内容检查
在添加新资源前，先检查文本内容是否已存在：

```bash
# 检查中文文本内容是否已存在
grep -q "要添加的中文文本" /path/to/FormResource.resx && echo "已存在" || echo "不存在"
```

**示例检查**：
```bash
echo "检查要添加的中文文本内容是否已存在:"
echo "移动端列数 - $(grep -q "移动端列数" FormResource.resx && echo "已存在" || echo "不存在")"
echo "背景样式 - $(grep -q "背景样式" FormResource.resx && echo "已存在" || echo "不存在")"
echo "浅色 - $(grep -q "浅色" FormResource.resx && echo "已存在" || echo "不存在")"
```

### 第二步：键值存在性检查
检查JSON配置中引用的资源键值是否存在：

```bash
# 检查键值是否存在
grep -c 'name="ResourceKeyName"' /path/to/FormResource.resx
```

**返回值含义**：
- `0` = 键值不存在，需要添加或找替代
- `1+` = 键值已存在，可以直接使用

### 第三步：寻找可复用的现有资源
当需要的键值不存在时，寻找功能相似的现有资源：

```bash
# 按功能分类查找现有资源
echo "包含Layout的键值:"
grep -n "Layout" FormResource.resx | grep "name="

echo "包含Background的键值:"
grep -n "Background" FormResource.resx | grep "name="
```

## 资源复用策略

### 1. 分组资源复用
| 需要的分组 | 可复用的现有分组 | 示例 |
|------------|------------------|------|
| `FormGroups_Layout` | `FormGroups_Appearance` | 布局相关配置归入外观设置 |
| `FormGroups_Display` | `FormGroups_Appearance` | 显示相关配置归入外观设置 |
| `FormGroups_Style` | `FormGroups_Appearance` | 样式相关配置归入外观设置 |

### 2. 字段标签资源复用
| 需要的标签 | 可复用的现有标签 | 说明 |
|------------|------------------|------|
| `FormFields_BackgroundStyle` | `CompanyBasicInfo_BackgroundStyle` | 背景样式配置 |
| `FormFields_LayoutType` | `FormFields_Layout` | 布局类型配置 |
| `FormFields_DisplayMode` | `FormFields_Layout` | 显示模式配置 |

### 3. 选项值资源复用
| 需要的选项 | 可复用的现有选项 | 示例 |
|------------|------------------|------|
| `BackgroundStyle_Light` | `BreadcrumbBackgroundStyleLight` | 浅色背景 |
| `BackgroundStyle_Dark` | `BreadcrumbBackgroundStyleDark` | 深色背景 |
| `BackgroundStyle_Gradient` | `Background_Gray` | 渐变背景（可用相近选项） |

## JSON配置修正示例

### ❌ 错误做法 - 使用不存在的资源
```json
{
  "name": "BackgroundStyle",
  "type": "select",
  "label": "@FormResource:FormFields_BackgroundStyle",
  "display": {
    "group": "@FormResource:FormGroups_Layout"
  },
  "options": [
    { "value": "light", "label": "@FormResource:BackgroundStyle_Light" },
    { "value": "dark", "label": "@FormResource:BackgroundStyle_Dark" },
    { "value": "gradient", "label": "@FormResource:BackgroundStyle_Gradient" }
  ]
}
```

### ✅ 正确做法 - 使用现有资源
```json
{
  "name": "BackgroundStyle",
  "type": "select",
  "label": "@FormResource:CompanyBasicInfo_BackgroundStyle",
  "display": {
    "group": "@FormResource:FormGroups_Appearance"
  },
  "options": [
    { "value": "light", "label": "@FormResource:BreadcrumbBackgroundStyleLight" },
    { "value": "dark", "label": "@FormResource:BreadcrumbBackgroundStyleDark" },
    { "value": "gradient", "label": "@FormResource:Background_Gray" }
  ]
}
```

## 资源添加规范

### 何时可以添加新资源
只有在以下情况下才可以添加新资源：

1. **功能唯一性** - 所需功能确实没有现有资源可以满足
2. **文本唯一性** - 所需的中文文本内容在现有资源中不存在
3. **语义准确性** - 现有资源的语义与所需功能差距过大

### 新资源添加流程

#### 1. 三语言资源文件同步添加
必须同时更新所有语言的资源文件：
- `FormResource.resx` (中文)
- `FormResource.en.resx` (英文)
- `FormResource.ja.resx` (日文)

#### 2. 资源命名规范
遵循现有的命名模式：

**分组命名**：
- `FormGroups_` + 功能名称
- 示例：`FormGroups_Content`, `FormGroups_Appearance`

**字段标签命名**：
- `FormFields_` + 字段名称
- 示例：`FormFields_Title`, `FormFields_Description`

**选项值命名**：
- 功能前缀 + `_` + 选项名称
- 示例：`BackgroundStyle_Light`, `Layout_Grid`

#### 3. 中性命名原则 (CRITICAL)
**优先使用中性、通用的命名**，以便其他组件复用：

**✅ 推荐的中性命名**：
```xml
<!-- 通用性强，多组件可复用 -->
<data name="FormFields_DisplayStyle" xml:space="preserve">
  <value>显示样式</value>
</data>
<data name="FormFields_ItemLayout" xml:space="preserve">
  <value>项目布局</value>
</data>
<data name="FormFields_ShowLogos" xml:space="preserve">
  <value>显示标志</value>
</data>
<data name="FormFields_EnableFilter" xml:space="preserve">
  <value>启用筛选</value>
</data>
```

**❌ 避免的特定命名**：
```xml
<!-- 特定性太强，复用性差 -->
<data name="CustomerCases_ShowCompanyLogos" xml:space="preserve">
  <value>显示公司Logo</value>
</data>
<data name="NewsSection_EnableCategoryFilter" xml:space="preserve">
  <value>启用新闻分类筛选</value>
</data>
```

**中性命名策略**：

| 替代方案 | 特定命名 | 中性命名 | 复用场景 |
|----------|----------|----------|----------|
| 显示控制 | `ShowCompanyLogos` | `ShowLogos` | 公司案例、合作伙伴、客户展示 |
| 筛选功能 | `ShowIndustryFilter` | `EnableCategoryFilter` | 新闻分类、产品分类、案例分类 |
| 布局样式 | `CaseDisplayMode` | `ItemDisplayMode` | 案例展示、产品展示、新闻展示 |
| 数据来源 | `ProjectDescription` | `ItemDescription` | 项目、产品、服务描述 |

**命名优先级**：
1. **最高优先级** - 使用现有的中性资源
2. **次优先级** - 创建新的中性资源供多组件使用
3. **最后选择** - 创建组件特定资源（仅当功能确实独特时）

#### 3. 添加位置规范
- **按功能分组** - 将相关资源添加在一起
- **添加注释** - 使用 `<!-- 功能模块注释 -->` 标识资源组
- **保持顺序** - 按照现有资源文件的结构顺序添加

### 示例：正确的资源添加

```xml
<!-- Customer Cases Component -->
<data name="CustomerCases" xml:space="preserve">
  <value>客户案例</value>
</data>
<data name="CompanyName" xml:space="preserve">
  <value>公司名称</value>
</data>
<data name="ProjectTitle" xml:space="preserve">
  <value>项目标题</value>
</data>
```

## 验证清单

在完成资源配置后，必须进行以下验证：

### ✅ 资源存在性验证
```bash
# 验证所有引用的资源都存在
echo "验证资源引用:"
echo "ResourceKey1: $(grep -c 'name="ResourceKey1"' FormResource.resx)"
echo "ResourceKey2: $(grep -c 'name="ResourceKey2"' FormResource.resx)"
```

### ✅ 资源唯一性验证
```bash
# 验证没有重复资源
echo "验证资源唯一性:"
echo "重复的ResourceKey: $(grep -c 'name="ResourceKey"' FormResource.resx)"
# 结果应该都是 1，如果大于 1 表示有重复
```

### ✅ 多语言一致性验证
```bash
# 验证三个语言文件的资源键值数量一致
echo "中文资源数: $(grep -c 'name="' FormResource.resx)"
echo "英文资源数: $(grep -c 'name="' FormResource.en.resx)"
echo "日文资源数: $(grep -c 'name="' FormResource.ja.resx)"
```

## 常见问题和解决方案

### 问题1: 需要的分组不存在
**解决方案**: 使用现有的语义最接近的分组
```
需要: FormGroups_Layout
使用: FormGroups_Appearance
```

### 问题2: 需要的字段标签不存在
**解决方案**: 寻找功能相似的现有标签
```
需要: FormFields_BackgroundStyle
使用: CompanyBasicInfo_BackgroundStyle
```

### 问题3: 需要的选项值不存在
**解决方案**: 使用语义相近的现有选项
```
需要: BackgroundStyle_Gradient
使用: Background_Gray (作为渐变的替代选项)
```

### 问题4: 文本内容已存在但键值不同
**解决方案**: 查找包含相同文本的现有键值并使用
```bash
# 查找包含特定文本的键值
grep -B1 -A1 "目标文本内容" FormResource.resx
```

## 最佳实践

### 1. 开发前准备
- 在创建JSON配置前，先全面调研现有资源
- 建立组件所需资源的清单和映射表
- 优先考虑资源复用方案
- **评估资源的通用性** - 思考其他组件是否可能需要类似功能

### 2. 开发中检查
- 每添加一个资源引用，立即验证其存在性
- 发现不存在的资源时，优先寻找可复用的替代资源
- **如需创建新资源，优先考虑中性命名** - 便于其他组件复用
- 记录所有的资源映射决策

### 3. 开发后验证
- 运行完整的资源验证脚本
- 检查所有语言文件的一致性
- 确认没有引入重复或冗余的资源
- **评估新增资源的复用潜力** - 考虑是否需要重构为更通用的命名

## 工具命令参考

### 快速资源检查命令
```bash
# 检查JSON中所有资源引用
grep -o "@[^:]*:[^\"]*" component.json | sort | uniq

# 批量检查资源存在性
for resource in $(grep -o "@FormResource:[^\"]*" component.json | sed 's/@FormResource://g'); do
  echo "$resource: $(grep -c "name=\"$resource\"" FormResource.resx)"
done

# 查找相似资源
grep -i "关键词" FormResource.resx | grep "name="
```

### 资源文件维护命令
```bash
# 统计各语言资源文件的资源数量
for lang in "" ".en" ".ja"; do
  echo "FormResource$lang.resx: $(grep -c 'name="' FormResource$lang.resx)"
done

# 查找可能的重复资源
grep -o 'name="[^"]*"' FormResource.resx | sort | uniq -d
```

---

**重要提醒**: 严格遵循这些规范是确保项目资源管理质量的关键。每个开发者都有责任在添加或修改资源时遵循这些规则，确保资源系统的整洁性和可维护性。

**特别强调**:
- **中性命名原则** - 新资源键值应使用通用、中性的命名，避免组件特定的命名，以提高资源的复用性
- **复用优先原则** - 始终优先考虑复用现有资源，只有在确实没有合适的现有资源时才创建新资源
- **一致性原则** - 保持资源命名、分组和结构的一致性，遵循项目已建立的命名模式