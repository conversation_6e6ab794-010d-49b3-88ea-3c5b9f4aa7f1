# 多标签页面懒加载开发规范

## 概述

本规范基于 CompanyInfo/Index.cshtml 多标签页面的成功实现，定义了企业网站管理后台中多标签页面的标准开发模式，特别是采用懒加载技术提升性能的实现方法。

## 核心设计原则

### 1. 性能优化原则
- **首屏快速加载**：初始页面只加载第一个标签页内容
- **按需加载**：其他标签页内容在用户点击时动态加载
- **缓存机制**：已加载的标签页内容缓存在客户端，避免重复请求

### 2. 可维护性原则
- **物理文件分离**：每个标签页内容独立存储为 .cshtml 文件
- **模块化架构**：控制器、服务、视图分离
- **统一接口**：标准化的 API 端点和数据结构

### 3. 用户体验原则
- **平滑切换**：标签页切换无刷新体验
- **加载状态**：显示加载动画和错误处理
- **URL 同步**：支持浏览器前进后退和直接访问特定标签页

## 技术架构

### 架构组件
```
Controller (懒加载 API)
    ↓
ViewRenderService (视图渲染)
    ↓
Partial Views (物理文件)
    ↓
JavaScript (前端控制)
    ↓
用户界面 (标签页显示)
```

## 实现规范

### 1. 目录结构规范

```
Views/Admin/{ModuleName}/
├── Index.cshtml                 # 主页面
├── Partials/                   # 标签页部分视图
│   ├── _{TabName}Tab.cshtml    # 各标签页内容
│   ├── _{TabName}Tab.cshtml
│   └── ...
└── Edit.cshtml                 # 其他相关视图
```

**示例**：
```
Views/Admin/CompanyInfo/
├── Index.cshtml
├── Partials/
│   ├── _CompanyHistoryTab.cshtml
│   ├── _ExecutiveOrganizationTab.cshtml
│   ├── _ContactInfoTab.cshtml
│   ├── _CSRActivitiesTab.cshtml
│   └── _InvestorRelationsTab.cshtml
```

### 2. 控制器实现规范

#### 主要方法结构
```csharp
[Authorize]
public class {Module}Controller : BaseController
{
    private readonly IViewRenderService _viewRenderService;

    // 主页面 - 只加载基本信息
    public async Task<IActionResult> Index()
    {
        // 只加载第一个标签页的数据
        var basicInfo = await LoadBasicInfoAsync();

        var viewModel = new {Module}ViewModel
        {
            BasicInfo = basicInfo,
            // 其他标签页数据为空或占位符
        };

        return View(viewModel);
    }

    // 懒加载 API 端点
    [HttpGet("tab/{tabName}")]
    public async Task<IActionResult> GetTabContent(string tabName)
    {
        try
        {
            var partialViewName = GetPartialViewName(tabName);
            var model = await GetTabDataAsync(tabName);

            ViewData["SupportedLanguages"] = SupportedLanguages;

            var html = await _viewRenderService.RenderPartialViewAsync(
                partialViewName, model, ViewData);

            return Json(new { success = true, html = html });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading tab content: {TabName}", tabName);
            return Json(new {
                success = false,
                message = AdminRes["LoadTabError"]
            });
        }
    }

    // 标签页名称映射
    private string GetPartialViewName(string tabName)
    {
        return tabName switch
        {
            "tab-name-1" => "Admin/{Module}/Partials/_Tab1",
            "tab-name-2" => "Admin/{Module}/Partials/_Tab2",
            _ => throw new ArgumentException($"Invalid tab name: {tabName}")
        };
    }

    // 标签页数据获取
    private async Task<object> GetTabDataAsync(string tabName)
    {
        return tabName switch
        {
            "tab-name-1" => await GetTab1DataAsync(),
            "tab-name-2" => await GetTab2DataAsync(),
            _ => null
        };
    }
}
```

### 3. ViewRenderService 规范

**必需注册服务**：
```csharp
// Program.cs
builder.Services.AddScoped<IViewRenderService, ViewRenderService>();
```

**服务实现**：
```csharp
public interface IViewRenderService
{
    Task<string> RenderPartialViewAsync<TModel>(
        string partialViewName,
        TModel model,
        ViewDataDictionary viewData = null,
        ITempDataDictionary tempData = null);
}
```

### 4. 主页面视图规范

#### HTML 结构
```html
<!-- 标签页导航 -->
<div class="border-b border-gray-200 mb-6">
    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button onclick="switchTab('basic-info')"
                class="tab-button active ...">
            @AdminRes["BasicInfo"]
        </button>
        <button onclick="switchTab('tab-name-1')"
                class="tab-button ...">
            @AdminRes["TabName1"]
        </button>
        <!-- 更多标签页按钮 -->
    </nav>
</div>

<!-- 标签页内容容器 -->
<div id="basic-info-tab" class="tab-content">
    <!-- 第一个标签页的完整内容 -->
</div>

<div id="tab-name-1-tab" class="tab-content hidden">
    <!-- 懒加载占位符 -->
    <div class="loading-placeholder flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-400">@AdminRes["Loading"]...</span>
    </div>
</div>
```

#### JavaScript 实现
```javascript
// 标签页加载状态追踪
const loadedTabs = new Set(['basic-info']); // 第一个标签已加载

// 标签页切换函数
async function switchTab(tabName) {
    // 更新 URL hash
    window.history.replaceState(null, null, '#' + tabName);

    // 检查是否已加载
    if (loadedTabs.has(tabName)) {
        MlSoftSites.switchTab(tabName);
        return;
    }

    // 懒加载内容
    if (tabName !== 'basic-info') {
        await loadTabContent(tabName);
    }

    MlSoftSites.switchTab(tabName);
}

// 懒加载标签页内容
async function loadTabContent(tabName) {
    const tabContainer = document.getElementById(`${tabName}-tab`);
    if (!tabContainer) return;

    showTabLoading(tabContainer);

    try {
        const response = await fetch(`@Html.MultilingualUrl("tab", "{ControllerName}")/${tabName}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        const result = await response.json();

        if (result.success) {
            tabContainer.innerHTML = result.html;
            loadedTabs.add(tabName);
            initializeTabScripts(tabName);
        } else {
            showTabError(tabContainer, result.message);
        }
    } catch (error) {
        console.error('Error loading tab:', error);
        showTabError(tabContainer, window.Resources?.Admin?.LoadTabError || 'Failed to load tab content');
    }
}

// 显示加载状态
function showTabLoading(tabContainer) {
    tabContainer.innerHTML = `
        <div class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-3 text-gray-600 dark:text-gray-400">${window.Resources?.Admin?.Loading || 'Loading'}...</span>
        </div>
    `;
}

// 显示错误状态
function showTabError(tabContainer, message) {
    tabContainer.innerHTML = `
        <div class="text-center py-12">
            <div class="text-red-600 dark:text-red-400 mb-4">
                <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
                <p class="text-lg">${message}</p>
            </div>
            <button onclick="reloadTab('${tabContainer.id.replace('-tab', '')}')"
                    class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-redo mr-2"></i>
                ${window.Resources?.Admin?.Retry || 'Retry'}
            </button>
        </div>
    `;
}

// 初始化页面
function initializeTab() {
    const hash = window.location.hash.substring(1);
    const validTabs = ['basic-info', 'tab-name-1', 'tab-name-2']; // 根据实际标签页修改

    if (hash && validTabs.includes(hash)) {
        switchTab(hash);
    } else {
        switchTab('basic-info');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTab();

    // 处理浏览器前进后退
    window.addEventListener('hashchange', function() {
        initializeTab();
    });
});
```

### 5. 多语言URL和数据访问规范

#### 5.1 多语言URL生成
**重要原则**：所有API调用必须使用 `@Html.MultilingualUrl()` 方法生成多语言URL

```javascript
// ✅ 正确：使用多语言URL助手方法
const response = await fetch(`@Html.MultilingualUrl("tab", "ControllerName")/${tabName}`, {
    headers: { 'X-Requested-With': 'XMLHttpRequest' }
});

// ✅ 正确：用于CRUD操作的多语言URL
const response = await fetch(`@Html.MultilingualUrl("company-history", "CompanyInfo")/${id}`, {
    method: 'GET',
    headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }
});

// ❌ 错误：硬编码URL路径
const response = await fetch(`/Admin/CompanyInfo/company-history/${id}`, {
    method: 'GET'
});
```

#### 5.2 JSON数据属性命名规范
**重要原则**：JavaScript访问API返回的JSON数据时，属性名应使用camelCase格式

```javascript
// C# API返回的JSON数据结构示例：
{
    "success": true,
    "data": {
        "id": "64f1a2b3c4d5e6f7a8b9c0d1",
        "eventDate": "2023-10-15T00:00:00Z",
        "eventType": 1,
        "displayOrder": 10,
        "imageUrl": "https://example.com/image.jpg",
        "isActive": true,
        "eventTitles": {
            "zh": "中文标题",
            "en": "English Title",
            "ja": "日本語タイトル"
        },
        "eventDescriptions": {
            "zh": "中文描述",
            "en": "English Description",
            "ja": "日本語説明"
        }
    }
}

// ✅ 正确：使用camelCase访问属性
function populateHistoryForm(history) {
    document.getElementById('historyId').value = history.id;  // 小写
    document.getElementById('eventDate').value = history.eventDate.split('T')[0];  // camelCase
    document.getElementById('eventType').value = history.eventType.toString();  // camelCase
    document.getElementById('displayOrder').value = history.displayOrder || 0;  // camelCase
    document.getElementById('imageUrl').value = history.imageUrl || '';  // camelCase
    document.getElementById('isActive').checked = history.isActive;  // camelCase

    // 多语言字段访问
    if (history.eventTitles) {  // camelCase
        document.getElementById('eventTitle_zh').value = history.eventTitles.zh || '';
        document.getElementById('eventTitle_en').value = history.eventTitles.en || '';
        document.getElementById('eventTitle_ja').value = history.eventTitles.ja || '';
    }

    if (history.eventDescriptions) {  // camelCase
        document.getElementById('eventDescription_zh').value = history.eventDescriptions.zh || '';
        document.getElementById('eventDescription_en').value = history.eventDescriptions.en || '';
        document.getElementById('eventDescription_ja').value = history.eventDescriptions.ja || '';
    }
}

// ❌ 错误：使用PascalCase访问属性
function populateHistoryFormWrong(history) {
    document.getElementById('historyId').value = history.Id;  // 错误
    document.getElementById('eventDate').value = history.EventDate.split('T')[0];  // 错误
    document.getElementById('eventType').value = history.EventType.toString();  // 错误
    // ... 其他错误示例
}
```

#### 5.3 API调用标准模式

```javascript
// 获取单个记录的标准模式
async function fetchRecordForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(`@Html.MultilingualUrl("resource-endpoint", "ControllerName")/${id}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateForm(result.data);  // 注意：result.data，不是result.Data
            // 显示模态框或执行其他操作
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.RecordNotFound || 'Record not found');
        }
    } catch (error) {
        console.error('Fetch record error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load record');
    } finally {
        hideLoading();
    }
}

// 删除记录的标准模式
async function deleteRecord(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete || 'Are you sure?');
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(`@Html.MultilingualUrl("resource-endpoint", "ControllerName")/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteSuccess || 'Deleted successfully');
                window.location.reload();  // 或者动态更新UI
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
}
```

### 6. 部分视图规范

#### 文件命名规范
- 文件名：`_{TabName}Tab.cshtml`
- 位置：`Views/Admin/{ModuleName}/Partials/`

#### 部分视图模板
```html
@model {TabDataModel}

<div class="space-y-6">
    <!-- 标签页标题 -->
    <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
            @AdminRes["TabTitle"]
        </h3>
        <!-- 操作按钮 -->
        <button onclick="performTabAction()"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
            <i class="fas fa-plus mr-2"></i>
            @AdminRes["ActionButton"]
        </button>
    </div>

    <!-- 标签页内容 -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <!-- 内容区域 -->
    </div>
</div>

<script>
// 标签页特定的 JavaScript 功能
function performTabAction() {
    // 具体实现
}
</script>
```

### 7. 多语言支持规范

#### 资源文件要求
需要在所有语言的资源文件中添加以下键值：
```xml
<!-- 通用 -->
<data name="Loading" xml:space="preserve">
    <value>加载中</value>
</data>
<data name="LoadTabError" xml:space="preserve">
    <value>加载标签页内容失败</value>
</data>
<data name="Retry" xml:space="preserve">
    <value>重试</value>
</data>

<!-- 标签页特定 -->
<data name="TabName1" xml:space="preserve">
    <value>标签页1</value>
</data>
```

### 8. 路由配置规范

#### 控制器路由属性
```csharp
[Route("Admin/{ModuleName}")]
public class {Module}Controller : BaseController
{
    [HttpGet("")]
    public async Task<IActionResult> Index() { }

    [HttpGet("tab/{tabName}")]
    public async Task<IActionResult> GetTabContent(string tabName) { }
}
```

## 性能优化建议

### 1. 缓存策略
- **客户端缓存**：已加载的标签页内容保存在内存中
- **服务端缓存**：对静态或半静态数据使用适当的缓存机制
- **数据库查询优化**：只查询当前标签页需要的数据

### 2. 加载策略
- **预加载**：可以考虑在空闲时预加载重要的标签页
- **分页加载**：对于大量数据的标签页，实现分页机制
- **增量加载**：支持数据的增量更新

### 3. 错误处理
- **网络错误**：提供重试机制
- **服务器错误**：显示友好的错误信息
- **数据验证**：前后端双重验证

## 测试规范

### 1. 功能测试
- 标签页正常切换
- 懒加载正确执行
- 数据正确显示
- 错误处理有效

### 2. 性能测试
- 首屏加载时间
- 标签页切换响应时间
- 内存使用情况

### 3. 兼容性测试
- 多浏览器兼容性
- 移动端适配
- 深色模式支持

## 常见问题和解决方案

### 1. 视图解析失败
**问题**：ViewRenderService 找不到部分视图
**解决**：使用相对路径而非绝对路径，如 `"Admin/ModuleName/Partials/_TabName"`

### 2. JavaScript 错误
**问题**：标签页切换失败
**解决**：确保 `MlSoftSites.switchTab` 全局方法可用，检查 DOM 元素 ID 命名

### 3. 数据不刷新
**问题**：标签页数据缓存过期
**解决**：实现缓存失效机制，提供强制刷新选项

### 4. 多语言显示异常
**问题**：资源文件缺失或键值不匹配
**解决**：确保所有语言文件包含必需的键值对

### 5. JSON属性访问错误
**问题**：JavaScript访问API返回数据时属性未定义或为null
**解决**：确保使用camelCase格式访问JSON属性，而不是PascalCase
```javascript
// ❌ 错误
const value = response.data.EventTitle;  // 未定义
// ✅ 正确
const value = response.data.eventTitle;  // camelCase
```

### 6. 多语言URL缺失
**问题**：API调用在多语言环境下路由错误
**解决**：所有API调用必须使用 `@Html.MultilingualUrl()` 方法
```javascript
// ❌ 错误
fetch('/Admin/CompanyInfo/company-history/' + id)
// ✅ 正确
fetch(`@Html.MultilingualUrl("company-history", "CompanyInfo")/${id}`)
```

## 扩展开发指南

### 添加新标签页的步骤

1. **创建部分视图文件**
   ```bash
   touch Views/Admin/{ModuleName}/Partials/_{NewTabName}Tab.cshtml
   ```

2. **更新控制器映射**
   ```csharp
   private string GetPartialViewName(string tabName)
   {
       return tabName switch
       {
           // ... 现有映射
           "new-tab-name" => "Admin/{ModuleName}/Partials/_NewTabTab",
           _ => throw new ArgumentException($"Invalid tab name: {tabName}")
       };
   }
   ```

3. **添加数据获取方法**
   ```csharp
   private async Task<object> GetTabDataAsync(string tabName)
   {
       return tabName switch
       {
           // ... 现有映射
           "new-tab-name" => await GetNewTabDataAsync(),
           _ => null
       };
   }
   ```

4. **更新主页面标签导航**
   ```html
   <button onclick="switchTab('new-tab-name')"
           class="tab-button ...">
       @AdminRes["NewTabName"]
   </button>
   ```

5. **添加标签页容器**
   ```html
   <div id="new-tab-name-tab" class="tab-content hidden">
       <div class="loading-placeholder ..."><!-- 加载占位符 --></div>
   </div>
   ```

6. **更新 JavaScript 配置**
   ```javascript
   const validTabs = ['basic-info', 'existing-tabs', 'new-tab-name'];
   ```

7. **添加多语言资源**
   ```xml
   <data name="NewTabName" xml:space="preserve">
       <value>新标签页名称</value>
   </data>
   ```

## 总结

本规范基于 CompanyInfo 模块的成功实践，为多标签页面懒加载提供了完整的开发指南。遵循此规范可以确保：

- **一致的用户体验**
- **高性能的页面加载**
- **易于维护的代码结构**
- **良好的可扩展性**

在实施时，请严格按照此规范执行，以确保系统的整体一致性和质量。