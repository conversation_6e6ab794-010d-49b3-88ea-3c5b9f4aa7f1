# PowerShell Script to Generate JavaScript Resource Files from RESX
param(
    [string]$ProjectPath = ".",
    [string]$ResourcePath = "src/Web/Resources",
    [string]$OutputPath = "src/Web/wwwroot/js/resources",
    [switch]$Force = $false
)

Write-Host "Starting JavaScript resource generation..." -ForegroundColor Green

# 确保输出目录存在
$FullOutputPath = Join-Path $ProjectPath $OutputPath
if (!(Test-Path $FullOutputPath)) {
    New-Item -ItemType Directory -Path $FullOutputPath -Force | Out-Null
    Write-Host "Created output directory: $FullOutputPath" -ForegroundColor Yellow
}

# 获取所有资源文件
$ResourceFullPath = Join-Path $ProjectPath $ResourcePath
$ResxFiles = Get-ChildItem -Path $ResourceFullPath -Filter "*.resx" -Recurse

if ($ResxFiles.Count -eq 0) {
    Write-Host "No RESX files found in $ResourceFullPath" -ForegroundColor Red
    exit 1
}

Write-Host "Found $($ResxFiles.Count) RESX files" -ForegroundColor Cyan

# 定义语言映射
$LanguageMap = @{
    '' = 'zh'        # 默认为中文
    'en' = 'en'      # 英文
    'ja' = 'ja'      # 日文
}

# 用于存储生成的资源
$GeneratedResources = @{}
$SkippedFiles = 0
$ProcessedFiles = 0

# 检查文件是否需要更新的函数
function Test-NeedsUpdate {
    param(
        [string]$SourceFile,
        [string]$TargetFile
    )

    if ($Force) {
        return $true
    }

    if (!(Test-Path $TargetFile)) {
        return $true
    }

    $SourceLastWrite = (Get-Item $SourceFile).LastWriteTime
    $TargetLastWrite = (Get-Item $TargetFile).LastWriteTime

    return $SourceLastWrite -gt $TargetLastWrite
}

foreach ($ResxFile in $ResxFiles) {
    # 解析文件名获取资源名称和语言
    $BaseName = $ResxFile.BaseName
    $Language = ''
    $ResourceName = $BaseName

    # 检查是否包含语言后缀
    if ($BaseName -match '^(.+)\.([a-z]{2})$') {
        $ResourceName = $Matches[1]
        $Language = $Matches[2]
    }

    # 获取映射的语言代码
    $LangCode = $LanguageMap[$Language]
    if (!$LangCode) {
        Write-Host "Unknown language code: $Language, skipping..." -ForegroundColor Yellow
        continue
    }

    # 计算目标文件路径
    $OutputFileName = "$($ResourceName.ToLower()).$LangCode.js"
    $OutputFilePath = Join-Path $FullOutputPath $OutputFileName

    # 检查是否需要更新
    if (!(Test-NeedsUpdate -SourceFile $ResxFile.FullName -TargetFile $OutputFilePath)) {
        Write-Host "Skipping $($ResxFile.Name) (target is up-to-date)" -ForegroundColor Gray
        $SkippedFiles++

        # 仍然需要记录这个文件用于索引生成
        try {
            [xml]$ResxContent = Get-Content $ResxFile.FullName -Encoding UTF8
            $ResourceCount = ($ResxContent.root.data | Where-Object { $_.name -and $_.value }).Count

            if (!$GeneratedResources[$ResourceName]) {
                $GeneratedResources[$ResourceName] = @()
            }
            $GeneratedResources[$ResourceName] += @{
                Language = $LangCode
                File = $OutputFileName
                Count = $ResourceCount
            }
        } catch {
            Write-Host "  Warning: Could not read resource count for $($ResxFile.Name)" -ForegroundColor Yellow
        }
        continue
    }

    Write-Host "Processing: $($ResxFile.Name)" -ForegroundColor White
    Write-Host "  Resource: $ResourceName, Language: $LangCode" -ForegroundColor Gray

    try {
        # 读取并解析RESX文件
        [xml]$ResxContent = Get-Content $ResxFile.FullName -Encoding UTF8

        # 提取所有数据项
        $Resources = @{}
        foreach ($DataNode in $ResxContent.root.data) {
            if ($DataNode.name -and $DataNode.value) {
                $Resources[$DataNode.name] = $DataNode.value
            }
        }

        if ($Resources.Count -eq 0) {
            Write-Host "  No resources found in $($ResxFile.Name)" -ForegroundColor Yellow
            continue
        }

        # 生成JavaScript内容
        $JsContent = @"
// Auto-generated from $($ResxFile.Name)
// Generated at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

window.Resources = window.Resources || {};
window.Resources.$($ResourceName -replace 'Resource$', '') = {
"@

        $ResourceEntries = @()
        foreach ($Key in $Resources.Keys | Sort-Object) {
            $Value = $Resources[$Key] -replace '"', '\"' -replace "`r`n", "\n" -replace "`n", "\n"
            $ResourceEntries += "    `"$Key`": `"$Value`""
        }

        $JsContent += "`n" + ($ResourceEntries -join ",`n") + "`n};"

        # 写入文件
        $JsContent | Out-File -FilePath $OutputFilePath -Encoding UTF8 -Force

        Write-Host "  Generated: $OutputFileName ($($Resources.Count) resources)" -ForegroundColor Green
        $ProcessedFiles++

        # 记录生成的文件
        if (!$GeneratedResources[$ResourceName]) {
            $GeneratedResources[$ResourceName] = @()
        }
        $GeneratedResources[$ResourceName] += @{
            Language = $LangCode
            File = $OutputFileName
            Count = $Resources.Count
        }

    } catch {
        Write-Host "  Error processing $($ResxFile.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 检查是否需要更新索引文件
$IndexFilePath = Join-Path $FullOutputPath "index.js"
$IndexNeedsUpdate = $Force

if (!$IndexNeedsUpdate -and (Test-Path $IndexFilePath)) {
    $IndexLastWrite = (Get-Item $IndexFilePath).LastWriteTime
    foreach ($ResxFile in $ResxFiles) {
        if ($ResxFile.LastWriteTime -gt $IndexLastWrite) {
            $IndexNeedsUpdate = $true
            break
        }
    }
} else {
    $IndexNeedsUpdate = $true
}

# 生成索引文件
if ($IndexNeedsUpdate) {
    Write-Host "`nGenerating resource index..." -ForegroundColor Cyan

    $IndexContent = @"
// Auto-generated resource index
// Generated at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

window.ResourceIndex = {
"@

    $IndexEntries = @()
    foreach ($ResourceName in $GeneratedResources.Keys | Sort-Object) {
        $Languages = $GeneratedResources[$ResourceName] | ForEach-Object { "`"$($_.Language)`"" }
        $IndexEntries += "    `"$ResourceName`": [$($Languages -join ', ')]"
    }

    $IndexContent += "`n" + ($IndexEntries -join ",`n") + "`n};"

    $IndexContent | Out-File -FilePath $IndexFilePath -Encoding UTF8 -Force

    Write-Host "Generated resource index: index.js" -ForegroundColor Green
} else {
    Write-Host "`nSkipping index.js (up-to-date)" -ForegroundColor Gray
}

# 输出总结
Write-Host "`n=== Generation Summary ===" -ForegroundColor Magenta
Write-Host "Total resource types: $($GeneratedResources.Keys.Count)" -ForegroundColor White
Write-Host "Files processed: $ProcessedFiles" -ForegroundColor Green
Write-Host "Files skipped: $SkippedFiles" -ForegroundColor Gray
if ($Force) {
    Write-Host "Force mode: All files were regenerated" -ForegroundColor Yellow
}

foreach ($ResourceName in $GeneratedResources.Keys | Sort-Object) {
    Write-Host "  $ResourceName`: " -NoNewline -ForegroundColor White
    $Details = $GeneratedResources[$ResourceName] | ForEach-Object { "$($_.Language)($($_.Count))" }
    Write-Host ($Details -join ', ') -ForegroundColor Gray
}

if ($ProcessedFiles -eq 0 -and $SkippedFiles -gt 0) {
    Write-Host "`nAll JavaScript resource files are up-to-date!" -ForegroundColor Green
} else {
    Write-Host "`nJavaScript resource generation completed successfully!" -ForegroundColor Green
}