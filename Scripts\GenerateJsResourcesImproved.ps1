# Improved PowerShell Script to Generate JavaScript Resource Files from RESX
# Only generates resources that are actually used in JavaScript/Views
param(
    [string]$ProjectPath = ".",
    [string]$ResourcePath = "src/Web/Resources",
    [string]$OutputPath = "src/Web/wwwroot/js/resources",
    [string]$ViewsPath = "src/Web/Views",
    [string]$ComponentsPath = "src/Web/Views/Shared/Components",
    [string]$JsAdminPath = "src/Web/wwwroot/js/admin",
    [string]$JsAdminFile = "src/Web/wwwroot/js/admin.js",
    [string]$CacheFile = "Scripts/.resource-cache.json",
    [switch]$Force = $false,
    [switch]$Verbose = $false
)

Write-Host "Starting improved JavaScript resource generation..." -ForegroundColor Green

# 确保输出目录存在
$FullProjectPath = Resolve-Path $ProjectPath
$FullOutputPath = Join-Path $FullProjectPath $OutputPath
$FullResourcePath = Join-Path $FullProjectPath $ResourcePath
$FullViewsPath = Join-Path $FullProjectPath $ViewsPath
$FullComponentsPath = Join-Path $FullProjectPath $ComponentsPath
$FullJsAdminPath = Join-Path $FullProjectPath $JsAdminPath
$FullJsAdminFile = Join-Path $FullProjectPath $JsAdminFile
$FullCacheFile = Join-Path $FullProjectPath $CacheFile

if (!(Test-Path $FullOutputPath)) {
    New-Item -ItemType Directory -Path $FullOutputPath -Force | Out-Null
    Write-Host "Created output directory: $FullOutputPath" -ForegroundColor Yellow
}

# 加载或创建缓存
$Cache = @{
    lastScanTime = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
    resourceFiles = @{}
    usedKeys = @{
        Admin = @()
        Shared = @()
        Account = @()
        Form = @()
    }
    fileDependencies = @{}
}

if ((Test-Path $FullCacheFile) -and !$Force) {
    try {
        $Cache = Get-Content $FullCacheFile -Encoding UTF8 | ConvertFrom-Json
        Write-Host "Loaded cache from $FullCacheFile" -ForegroundColor Cyan
    } catch {
        Write-Host "Warning: Could not load cache file, starting fresh" -ForegroundColor Yellow
    }
}

# 检查资源文件是否有更新
function Test-ResourceFilesChanged {
    $ResourceFiles = Get-ChildItem -Path $FullResourcePath -Filter "*.resx" -Recurse
    $HasChanges = $false

    foreach ($ResxFile in $ResourceFiles) {
        $RelativePath = $ResxFile.FullName.Replace($FullProjectPath, "").TrimStart('\', '/')
        $LastWrite = $ResxFile.LastWriteTime.ToString("yyyy-MM-ddTHH:mm:ssZ")

        if (!$Cache.resourceFiles.$RelativePath -or $Cache.resourceFiles.$RelativePath -ne $LastWrite) {
            $HasChanges = $true
            $Cache.resourceFiles.$RelativePath = $LastWrite
            if ($Verbose) {
                Write-Host "  Resource file changed: $RelativePath" -ForegroundColor Yellow
            }
        }
    }

    return $HasChanges
}

# 检查源代码文件是否有更新
function Test-SourceFilesChanged {
    $SourceFiles = @()

    # 获取所有View文件
    if (Test-Path $FullViewsPath) {
        $SourceFiles += Get-ChildItem -Path $FullViewsPath -Filter "*.cshtml" -Recurse
    }

    # 获取组件JSON配置文件
    if (Test-Path $FullComponentsPath) {
        $SourceFiles += Get-ChildItem -Path $FullComponentsPath -Filter "*.json" -Recurse
    }

    # 获取JS文件
    if (Test-Path $FullJsAdminPath) {
        $SourceFiles += Get-ChildItem -Path $FullJsAdminPath -Filter "*.js" -Recurse
    }

    if (Test-Path $FullJsAdminFile) {
        $SourceFiles += Get-Item $FullJsAdminFile
    }

    $LastCacheTime = [DateTime]::Parse($Cache.lastScanTime)

    foreach ($File in $SourceFiles) {
        if ($File.LastWriteTime -gt $LastCacheTime) {
            if ($Verbose) {
                Write-Host "  Source file changed: $($File.FullName.Replace($FullProjectPath, '').TrimStart('\', '/'))" -ForegroundColor Yellow
            }
            return $true
        }
    }

    return $false
}

# 提取资源键的函数
function Extract-ResourceKeys {
    param([string]$Content, [string]$FilePath)

    $Keys = @{
        Admin = [System.Collections.Generic.HashSet[string]]::new()
        Shared = [System.Collections.Generic.HashSet[string]]::new()
        Account = [System.Collections.Generic.HashSet[string]]::new()
        Form = [System.Collections.Generic.HashSet[string]]::new()
    }

    # Razor语法: @AdminRes["Key"], @SharedRes["Key"], @AccountRes["Key"], @FormRes["Key"]
    $RazorPattern = '@(Admin|Shared|Account|Form)Res\["([^"]+)"\]'
    $RazorMatches = [regex]::Matches($Content, $RazorPattern)
    foreach ($Match in $RazorMatches) {
        $ResourceType = $Match.Groups[1].Value
        $Key = $Match.Groups[2].Value
        $Keys[$ResourceType].Add($Key) | Out-Null
        if ($Verbose) {
            Write-Host "    Found Razor: $ResourceType.$Key" -ForegroundColor DarkGray
        }
    }

    # JavaScript变量: window.Resources.Admin.Key, Resources.Admin.Key, window.Resources.Admin["Key"]
    $JsPatterns = @(
        '(?:window\.)?Resources\.(Admin|Shared|Account|Form)\.([a-zA-Z_][a-zA-Z0-9_]*)',
        '(?:window\.)?Resources\.(Admin|Shared|Account|Form)\["([^"]+)"\]',
        '(?:window\.)?Resources\.(Admin|Shared|Account|Form)\[''([^'']+)''\]'
    )

    foreach ($Pattern in $JsPatterns) {
        $JsMatches = [regex]::Matches($Content, $Pattern)
        foreach ($Match in $JsMatches) {
            $ResourceType = $Match.Groups[1].Value
            $Key = $Match.Groups[2].Value
            $Keys[$ResourceType].Add($Key) | Out-Null
            if ($Verbose) {
                Write-Host "    Found JS: $ResourceType.$Key" -ForegroundColor DarkGray
            }
        }
    }

    # 模板字符串中的资源
    $TemplatePattern = '`[^`]*\$\{(?:window\.)?Resources\.(Admin|Shared|Account|Form)\.([a-zA-Z_][a-zA-Z0-9_]*)\}[^`]*`'
    $TemplateMatches = [regex]::Matches($Content, $TemplatePattern)
    foreach ($Match in $TemplateMatches) {
        $ResourceType = $Match.Groups[1].Value
        $Key = $Match.Groups[2].Value
        $Keys[$ResourceType].Add($Key) | Out-Null
        if ($Verbose) {
            Write-Host "    Found Template: $ResourceType.$Key" -ForegroundColor DarkGray
        }
    }

    return $Keys
}

# 从JSON配置文件中提取资源键的函数
function Extract-ResourceKeysFromJson {
    param([string]$JsonContent, [string]$FilePath)

    $Keys = @{
        Admin = [System.Collections.Generic.HashSet[string]]::new()
        Shared = [System.Collections.Generic.HashSet[string]]::new()
        Account = [System.Collections.Generic.HashSet[string]]::new()
        Form = [System.Collections.Generic.HashSet[string]]::new()
    }

    try {
        # JSON中的资源引用模式: @AdminResource:Key, @SharedResource:Key, @AccountResource:Key, @FormResource:Key
        $JsonResourcePattern = '"@(Admin|Shared|Account|Form)Resource:([^"]+)"'
        $JsonMatches = [regex]::Matches($JsonContent, $JsonResourcePattern)
        
        foreach ($Match in $JsonMatches) {
            $ResourceType = $Match.Groups[1].Value
            $Key = $Match.Groups[2].Value
            $Keys[$ResourceType].Add($Key) | Out-Null
            if ($Verbose) {
                Write-Host "    Found JSON Resource: $ResourceType.$Key" -ForegroundColor DarkGray
            }
        }

        # 也支持简化的资源引用模式: @AdminRes:Key, @SharedRes:Key, @AccountRes:Key, @FormRes:Key
        $JsonResourcePatternShort = '"@(Admin|Shared|Account|Form)Res:([^"]+)"'
        $JsonMatchesShort = [regex]::Matches($JsonContent, $JsonResourcePatternShort)
        
        foreach ($Match in $JsonMatchesShort) {
            $ResourceType = $Match.Groups[1].Value
            $Key = $Match.Groups[2].Value
            $Keys[$ResourceType].Add($Key) | Out-Null
            if ($Verbose) {
                Write-Host "    Found JSON Resource (Short): $ResourceType.$Key" -ForegroundColor DarkGray
            }
        }

    } catch {
        if ($Verbose) {
            Write-Host "    Warning: Could not parse JSON in $FilePath`: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }

    return $Keys
}

# 扫描所有源文件
function Scan-SourceFiles {
    $AllUsedKeys = @{
        Admin = [System.Collections.Generic.HashSet[string]]::new()
        Shared = [System.Collections.Generic.HashSet[string]]::new()
        Account = [System.Collections.Generic.HashSet[string]]::new()
        Form = [System.Collections.Generic.HashSet[string]]::new()
    }

    $FileDependencies = @{}
    $ScannedFiles = 0

    Write-Host "Scanning source files for resource usage..." -ForegroundColor Cyan

    # 扫描View文件
    if (Test-Path $FullViewsPath) {
        $ViewFiles = Get-ChildItem -Path $FullViewsPath -Filter "*.cshtml" -Recurse
        foreach ($ViewFile in $ViewFiles) {
            $Content = Get-Content $ViewFile.FullName -Raw -Encoding UTF8
            $Keys = Extract-ResourceKeys -Content $Content -FilePath $ViewFile.FullName

            $RelativePath = $ViewFile.FullName.Replace($FullProjectPath, "").TrimStart('\', '/')
            $FileDeps = @()

            foreach ($ResourceType in $Keys.Keys) {
                foreach ($Key in $Keys[$ResourceType]) {
                    $AllUsedKeys[$ResourceType].Add($Key) | Out-Null
                    $FileDeps += "$ResourceType.$Key"
                }
            }

            if ($FileDeps.Count -gt 0) {
                $FileDependencies[$RelativePath] = $FileDeps
            }

            $ScannedFiles++
        }
    }

    # 扫描组件JSON配置文件
    if (Test-Path $FullComponentsPath) {
        $JsonFiles = Get-ChildItem -Path $FullComponentsPath -Filter "*.json" -Recurse
        foreach ($JsonFile in $JsonFiles) {
            $JsonContent = Get-Content $JsonFile.FullName -Raw -Encoding UTF8
            $Keys = Extract-ResourceKeysFromJson -JsonContent $JsonContent -FilePath $JsonFile.FullName

            $RelativePath = $JsonFile.FullName.Replace($FullProjectPath, "").TrimStart('\', '/')
            $FileDeps = @()

            foreach ($ResourceType in $Keys.Keys) {
                foreach ($Key in $Keys[$ResourceType]) {
                    $AllUsedKeys[$ResourceType].Add($Key) | Out-Null
                    $FileDeps += "$ResourceType.$Key"
                }
            }

            if ($FileDeps.Count -gt 0) {
                $FileDependencies[$RelativePath] = $FileDeps
                if ($Verbose) {
                    Write-Host "  Component JSON: $RelativePath ($($FileDeps.Count) resources)" -ForegroundColor Cyan
                }
            }

            $ScannedFiles++
        }
    }

    # 扫描JS文件
    $JsFiles = @()
    if (Test-Path $FullJsAdminPath) {
        $JsFiles += Get-ChildItem -Path $FullJsAdminPath -Filter "*.js" -Recurse
    }
    if (Test-Path $FullJsAdminFile) {
        $JsFiles += Get-Item $FullJsAdminFile
    }

    foreach ($JsFile in $JsFiles) {
        $Content = Get-Content $JsFile.FullName -Raw -Encoding UTF8
        $Keys = Extract-ResourceKeys -Content $Content -FilePath $JsFile.FullName

        $RelativePath = $JsFile.FullName.Replace($FullProjectPath, "").TrimStart('\', '/')
        $FileDeps = @()

        foreach ($ResourceType in $Keys.Keys) {
            foreach ($Key in $Keys[$ResourceType]) {
                $AllUsedKeys[$ResourceType].Add($Key) | Out-Null
                $FileDeps += "$ResourceType.$Key"
            }
        }

        if ($FileDeps.Count -gt 0) {
            $FileDependencies[$RelativePath] = $FileDeps
        }

        $ScannedFiles++
    }

    Write-Host "Scanned $ScannedFiles files" -ForegroundColor Green

    # 转换HashSet为Array以便序列化
    $Result = @{
        usedKeys = @{
            Admin = @($AllUsedKeys.Admin)
            Shared = @($AllUsedKeys.Shared)
            Account = @($AllUsedKeys.Account)
            Form = @($AllUsedKeys.Form)
        }
        fileDependencies = $FileDependencies
    }

    return $Result
}

# 从RESX文件加载资源
function Load-ResourcesFromResx {
    param([string]$ResourceType, [string]$Language = "")

    $ResourceFileName = if ($Language) { "$ResourceType.$Language.resx" } else { "$ResourceType.resx" }
    $ResourceFilePath = Join-Path $FullResourcePath $ResourceFileName

    if (!(Test-Path $ResourceFilePath)) {
        return @{}
    }

    try {
        [xml]$ResxContent = Get-Content $ResourceFilePath -Encoding UTF8
        $Resources = @{}

        foreach ($DataNode in $ResxContent.root.data) {
            if ($DataNode.name -and $DataNode.value) {
                $Resources[$DataNode.name] = $DataNode.value
            }
        }

        return $Resources
    } catch {
        Write-Host "Error loading $ResourceFilePath`: $($_.Exception.Message)" -ForegroundColor Red
        return @{}
    }
}

# 生成JavaScript文件
function Generate-JsResourceFile {
    param(
        [string]$ResourceType,
        [string]$Language,
        [string[]]$UsedKeys,
        [hashtable]$AllResources
    )

    $LangCode = switch ($Language) {
        "" { "zh" }
        "en" { "en" }
        "ja" { "ja" }
        default { $Language }
    }

    $OutputFileName = "$($ResourceType.ToLower()).$LangCode.js"
    $OutputFilePath = Join-Path $FullOutputPath $OutputFileName

    # 过滤只包含使用的键
    $FilteredResources = @{}
    $MissingKeys = @()

    foreach ($Key in $UsedKeys) {
        if ($AllResources.ContainsKey($Key)) {
            $FilteredResources[$Key] = $AllResources[$Key]
        } else {
            $MissingKeys += $Key
        }
    }

    if ($MissingKeys.Count -gt 0) {
        Write-Host "  Warning: Missing keys in $ResourceType ($LangCode): $($MissingKeys -join ', ')" -ForegroundColor Yellow
    }

    if ($FilteredResources.Count -eq 0) {
        Write-Host "  No used resources found for $ResourceType ($LangCode), skipping file generation" -ForegroundColor Gray
        return $false
    }

    # 生成JavaScript内容
    $JsContent = @"
// Auto-generated from $ResourceType Resource ($LangCode)
// Generated at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
// Contains only used resources ($($FilteredResources.Count) of $($AllResources.Count) total)

window.Resources = window.Resources || {};
window.Resources.$ResourceType = {
"@

    $ResourceEntries = @()
    foreach ($Key in $FilteredResources.Keys | Sort-Object) {
        $Value = $FilteredResources[$Key] -replace '"', '\"' -replace "`r`n", "\n" -replace "`n", "\n"
        $ResourceEntries += "    `"$Key`": `"$Value`""
    }

    $JsContent += "`n" + ($ResourceEntries -join ",`n") + "`n};"

    # 写入文件
    $JsContent | Out-File -FilePath $OutputFilePath -Encoding UTF8 -Force

    Write-Host "  Generated: $OutputFileName ($($FilteredResources.Count) resources)" -ForegroundColor Green
    return $true
}

# 主逻辑
$ResourceFilesChanged = Test-ResourceFilesChanged
$SourceFilesChanged = Test-SourceFilesChanged

if (!$Force -and !$ResourceFilesChanged -and !$SourceFilesChanged) {
    Write-Host "No changes detected, skipping generation" -ForegroundColor Green
    Write-Host "Use -Force to regenerate anyway" -ForegroundColor Gray
    exit 0
}

if ($ResourceFilesChanged) {
    Write-Host "Resource files have changed" -ForegroundColor Yellow
}

if ($SourceFilesChanged) {
    Write-Host "Source files have changed" -ForegroundColor Yellow
}

# 扫描源文件
$ScanResult = Scan-SourceFiles
$Cache.usedKeys = $ScanResult.usedKeys
$Cache.fileDependencies = $ScanResult.fileDependencies

# 输出使用统计
Write-Host "`nResource usage summary:" -ForegroundColor Cyan
foreach ($ResourceType in $Cache.usedKeys.Keys) {
    $Count = $Cache.usedKeys[$ResourceType].Count
    if ($Count -gt 0) {
        Write-Host "  $ResourceType`: $Count keys used" -ForegroundColor White
        if ($Verbose) {
            Write-Host "    Keys: $($Cache.usedKeys[$ResourceType] -join ', ')" -ForegroundColor DarkGray
        }
    }
}

# 生成语言映射
$LanguageMap = @{
    '' = 'zh'
    'en' = 'en'
    'ja' = 'ja'
}

$GeneratedFiles = 0
$SkippedFiles = 0

# 为每种资源类型和语言生成文件
foreach ($ResourceType in @('AdminResource', 'SharedResource', 'AccountResource', 'FormResource')) {
    $ShortName = $ResourceType -replace 'Resource$', ''
    $UsedKeys = $Cache.usedKeys[$ShortName]

    if ($UsedKeys.Count -eq 0) {
        Write-Host "No used keys for $ResourceType, skipping" -ForegroundColor Gray
        continue
    }

    Write-Host "`nProcessing $ResourceType..." -ForegroundColor White

    foreach ($Language in $LanguageMap.Keys) {
        $AllResources = Load-ResourcesFromResx -ResourceType $ResourceType -Language $Language

        if ($AllResources.Count -eq 0) {
            Write-Host "  No resources found in $ResourceType ($Language), skipping" -ForegroundColor Gray
            $SkippedFiles++
            continue
        }

        $Generated = Generate-JsResourceFile -ResourceType $ShortName -Language $Language -UsedKeys $UsedKeys -AllResources $AllResources

        if ($Generated) {
            $GeneratedFiles++
        } else {
            $SkippedFiles++
        }
    }
}

# 更新缓存时间戳
$Cache.lastScanTime = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")

# 保存缓存
try {
    $Cache | ConvertTo-Json -Depth 10 | Out-File -FilePath $FullCacheFile -Encoding UTF8 -Force
    Write-Host "`nCache saved to $FullCacheFile" -ForegroundColor Cyan
} catch {
    Write-Host "Warning: Could not save cache file: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 输出总结
Write-Host "`n=== Generation Summary ===" -ForegroundColor Magenta
Write-Host "Files generated: $GeneratedFiles" -ForegroundColor Green
Write-Host "Files skipped: $SkippedFiles" -ForegroundColor Gray
Write-Host "Cache file: $FullCacheFile" -ForegroundColor Cyan

if ($Force) {
    Write-Host "Force mode: Regenerated all applicable files" -ForegroundColor Yellow
}

Write-Host "`nImproved JavaScript resource generation completed!" -ForegroundColor Green