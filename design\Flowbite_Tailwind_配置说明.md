# Flowbite + Tailwind CSS 配置说明

## 概述

本文档详细说明了在MlSoft.Sites项目中如何配置和使用Flowbite + Tailwind CSS框架。

## 安装配置

### 1. 安装依赖包

```bash
# 进入Web项目目录
cd src/Web

# 初始化package.json
npm init -y

# 安装Tailwind CSS
npm install -D tailwindcss @tailwindcss/forms @tailwindcss/typography
npm install -D autoprefixer postcss

# 安装Flowbite
npm install flowbite

# 安装构建工具
npm install -D concurrently
```

### 2. 创建配置文件

#### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./Views/**/*.cshtml",
    "./wwwroot/**/*.js",
    "./node_modules/flowbite/**/*.js"
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554'
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
          950: '#030712'
        }
      },
      fontFamily: {
        'sans': ['Inter', 'ui-sans-serif', 'system-ui'],
        'body': ['Inter', 'ui-sans-serif', 'system-ui'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        }
      }
    },
  },
  plugins: [
    require('flowbite/plugin'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

#### postcss.config.js
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

#### package.json scripts
```json
{
  "scripts": {
    "build-css": "tailwindcss -i ./wwwroot/css/site.css -o ./wwwroot/css/site.min.css --watch",
    "build-css-prod": "tailwindcss -i ./wwwroot/css/site.css -o ./wwwroot/css/site.min.css --minify",
    "dev": "concurrently \"npm run build-css\" \"dotnet watch run\""
  }
}
```

### 3. 创建样式文件

#### wwwroot/css/site.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
  }
  
  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-white;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold text-gray-900 dark:text-white;
  }
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply inline-flex items-center px-5 py-2.5 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800;
  }
  
  .btn-secondary {
    @apply py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700;
  }
  
  .card {
    @apply bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700;
  }
  
  .navbar {
    @apply bg-white border-gray-200 dark:bg-gray-900 dark:border-gray-700;
  }
  
  .hero-section {
    @apply bg-white dark:bg-gray-900;
  }
  
  .footer {
    @apply bg-gray-50 dark:bg-gray-800;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
}

/* 日本企业网站特定样式 */
.jp-corporate {
  @apply font-sans;
}

.jp-corporate h1 {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold mb-6;
}

.jp-corporate h2 {
  @apply text-2xl md:text-3xl font-bold mb-4;
}

.jp-corporate h3 {
  @apply text-xl md:text-2xl font-semibold mb-3;
}

.jp-corporate .section-padding {
  @apply py-12 md:py-16 lg:py-20;
}

.jp-corporate .container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* 响应式图片 */
.responsive-img {
  @apply w-full h-auto object-cover;
}

/* 卡片悬停效果 */
.card-hover {
  @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}

/* 渐变背景 */
.gradient-bg {
  @apply bg-gradient-to-r from-blue-600 to-purple-600;
}

/* 文字渐变 */
.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}
```

## 布局文件配置

### Views/Shared/_Layout.cshtml
```html
<!DOCTYPE html>
<html lang="@ViewData["Language"] ?? "ja" class="@ViewData["Theme"]">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - @ViewData["SiteName"]</title>
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="~/css/site.min.css" asp-append-version="true" />
    
    <!-- Flowbite CSS (CDN fallback) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
    
    <!-- Meta tags -->
    <meta name="description" content="@ViewData["MetaDescription"]" />
    <meta name="keywords" content="@ViewData["MetaKeywords"]" />
    
    <!-- Open Graph -->
    <meta property="og:title" content="@ViewData["OgTitle"]" />
    <meta property="og:description" content="@ViewData["OgDescription"]" />
    <meta property="og:image" content="@ViewData["OgImage"]" />
    <meta property="og:type" content="website" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico" />
</head>
<body class="jp-corporate">
    <!-- Skip to main content (accessibility) -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
        メインコンテンツにスキップ
    </a>
    
    <!-- Header -->
    <header>
        @await Html.PartialAsync("_Header")
    </header>
    
    <!-- Main content -->
    <main id="main-content" class="min-h-screen">
        @RenderBody()
    </main>
    
    <!-- Footer -->
    <footer>
        @await Html.PartialAsync("_Footer")
    </footer>
    
    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <!-- Theme toggle script -->
    <script>
        // 深色模式切换
        const themeToggleBtn = document.getElementById('theme-toggle');
        const themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
        const themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');
        
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
            themeToggleLightIcon?.classList.remove('hidden');
        } else {
            themeToggleDarkIcon?.classList.remove('hidden');
        }
        
        themeToggleBtn?.addEventListener('click', function() {
            themeToggleDarkIcon?.classList.toggle('hidden');
            themeToggleLightIcon?.classList.toggle('hidden');
            
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            }
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
```

## 常用Flowbite组件

### 1. 导航栏组件
```html
<!-- Views/Shared/_Header.cshtml -->
<nav class="navbar border-b">
  <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
    <a href="/" class="flex items-center space-x-3 rtl:space-x-reverse">
      <img src="~/images/logo.svg" class="h-8" alt="Logo" />
      <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">
        企業名
      </span>
    </a>
    
    <div class="flex items-center md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
      <!-- 深色模式切换 -->
      <button id="theme-toggle" type="button" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5">
        <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
        </svg>
        <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 17.77L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"></path>
        </svg>
      </button>
      
      <!-- 移动端菜单按钮 -->
      <button data-collapse-toggle="navbar-user" type="button" class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600">
        <svg class="w-5 h-5" fill="none" viewBox="0 0 17 14">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
        </svg>
      </button>
    </div>
    
    <div class="items-center justify-between hidden w-full md:flex md:w-auto md:order-1" id="navbar-user">
      <ul class="flex flex-col font-medium p-4 md:p-0 mt-4 border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700">
        <li>
          <a href="/" class="block py-2 px-3 text-white bg-blue-700 rounded md:bg-transparent md:text-blue-700 md:p-0 md:dark:text-blue-500">ホーム</a>
        </li>
        <li>
          <a href="/company" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700">会社概要</a>
        </li>
        <li>
          <a href="/business" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700">事業内容</a>
        </li>
        <li>
          <a href="/news" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700">ニュース</a>
        </li>
        <li>
          <a href="/contact" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700">お問い合わせ</a>
        </li>
      </ul>
    </div>
  </div>
</nav>
```

### 2. Hero区域组件
```html
<!-- Views/Shared/Components/_HeroComponent.cshtml -->
<section class="hero-section">
  <div class="py-8 px-4 mx-auto max-w-screen-xl text-center lg:py-16 lg:px-6">
    <div class="mx-auto max-w-screen-sm">
      <h1 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 md:text-5xl lg:text-6xl dark:text-white animate-fade-in">
        @Model.Title
      </h1>
      <p class="mb-8 text-lg font-normal text-gray-500 lg:text-xl dark:text-gray-400 animate-fade-in animation-delay-200">
        @Model.Subtitle
      </p>
      <div class="flex flex-col space-y-4 sm:flex-row sm:justify-center sm:space-y-0 sm:space-x-4 animate-fade-in animation-delay-400">
        @if (!string.IsNullOrEmpty(Model.PrimaryButtonText))
        {
          <a href="@Model.PrimaryButtonUrl" class="btn-primary">
            @Model.PrimaryButtonText
            <svg class="w-3.5 h-3.5 ms-2 rtl:rotate-180" fill="none" viewBox="0 0 14 10">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
            </svg>
          </a>
        }
        @if (!string.IsNullOrEmpty(Model.SecondaryButtonText))
        {
          <a href="@Model.SecondaryButtonUrl" class="btn-secondary">
            @Model.SecondaryButtonText
          </a>
        }
      </div>
    </div>
  </div>
</section>
```

### 3. 卡片组件
```html
<!-- Views/Shared/Components/_ContentComponent.cshtml -->
<div class="grid gap-6 mb-6 md:grid-cols-2 lg:grid-cols-3">
  @foreach (var item in Model.ContentBlocks)
  {
    <div class="card card-hover">
      @if (!string.IsNullOrEmpty(item.ImageUrl))
      {
        <a href="@item.Url">
          <img class="rounded-t-lg responsive-img" src="@item.ImageUrl" alt="@item.Title" />
        </a>
      }
      <div class="p-5">
        <a href="@item.Url">
          <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
            @item.Title
          </h5>
        </a>
        <p class="mb-3 font-normal text-gray-700 dark:text-gray-400">
          @item.Description
        </p>
        <a href="@item.Url" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
          詳細を見る
          <svg class="rtl:rotate-180 w-3.5 h-3.5 ms-2" fill="none" viewBox="0 0 14 10">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
          </svg>
        </a>
      </div>
    </div>
  }
</div>
```

## 构建和部署

### 1. 开发环境
```bash
# 启动开发服务器（包含CSS监听）
npm run dev
```

### 2. 生产环境构建
```bash
# 构建压缩的CSS
npm run build-css-prod

# 发布应用
dotnet publish -c Release
```

### 3. Docker配置
```dockerfile
# Dockerfile
FROM node:18-alpine AS node-build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build-css-prod

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/Web/MlSoft.Sites.Web.csproj", "src/Web/"]
RUN dotnet restore "src/Web/MlSoft.Sites.Web.csproj"
COPY . .
WORKDIR "/src/src/Web"
RUN dotnet build "MlSoft.Sites.Web.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "MlSoft.Sites.Web.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
COPY --from=node-build /app/wwwroot/css/site.min.css ./wwwroot/css/
ENTRYPOINT ["dotnet", "MlSoft.Sites.Web.dll"]
```

## 最佳实践

### 1. 性能优化
- 使用Tailwind CSS的JIT模式
- 启用CSS和JS压缩
- 使用CDN加载字体和图标
- 实现图片懒加载

### 2. 无障碍访问
- 使用语义化HTML标签
- 添加适当的ARIA属性
- 确保键盘导航支持
- 提供跳转到主内容的链接

### 3. SEO优化
- 使用适当的HTML结构
- 添加meta标签和Open Graph标签
- 实现结构化数据
- 优化页面加载速度

### 4. 响应式设计
- 移动优先的设计方法
- 使用Tailwind的响应式前缀
- 测试不同设备和屏幕尺寸
- 优化触摸交互

这个配置为MlSoft.Sites项目提供了完整的Flowbite + Tailwind CSS解决方案，支持现代化的UI设计、响应式布局、深色模式和无障碍访问。