# MongoDB 集合名称规范

## 更改说明
已将所有MongoDB集合名称统一调整为首字母大写的Pascal命名方式。

## 集合名称对照表

| 实体类 | 旧集合名称 | 新集合名称 | Service类 |
|--------|------------|------------|-----------|
| Company | companies | **Companies** | CompanyService |
| CompanyLocation | companyLocations | **CompanyLocations** | CompanyLocationService |
| NewsAnnouncement | newsAnnouncements | **NewsAnnouncements** | NewsAnnouncementService |
| ProductService | productServices | **ProductServices** | ProductServiceService |
| BusinessDivision | businessDivisions | **BusinessDivisions** | BusinessDivisionService |
| ComponentDefinition | componentDefinitions | **ComponentDefinitions** | ComponentDefinitionService |
| ComponentTemplate | - | **ComponentTemplates** | ComponentTemplateService |
| PageConfiguration | pageConfigurations | **PageConfigurations** | PageConfigurationService |
| PageVersion | - | **PageVersions** | PageVersionService |
| Executive | - | **Executives** | ExecutiveService |
| OrganizationStructure | - | **OrganizationStructures** | OrganizationStructureService |
| CompanyHistory | - | **CompanyHistories** | CompanyHistoryService |
| FinancialReport | - | **FinancialReports** | FinancialReportService |
| ShareholderMeeting | - | **ShareholderMeetings** | ShareholderMeetingService |
| CSRActivity | - | **CSRActivities** | CSRActivityService |
| JobPosition | - | **JobPositions** | JobPositionService |
| EmployeeInterview | - | **EmployeeInterviews** | EmployeeInterviewService |
| SiteSettings | - | **SiteSettings** | SiteSettingsService |
| SEOSettings | - | **SEOSettings** | SEOSettingsService |

## 新增的Service类

以下Service类是新创建的，为完整的实体管理提供支持：

### 组织架构相关
- `ExecutiveService` - 管理层信息管理
- `OrganizationStructureService` - 组织架构管理

### 企业历史相关
- `CompanyHistoryService` - 企业沿革管理

### 投资者关系相关
- `FinancialReportService` - 财务报告管理
- `ShareholderMeetingService` - 股东大会管理

### CSR相关
- `CSRActivityService` - CSR活动管理

### 招聘相关
- `JobPositionService` - 招聘职位管理
- `EmployeeInterviewService` - 员工访谈管理

### 组件系统相关
- `ComponentTemplateService` - 组件模板管理
- `PageVersionService` - 页面版本控制

### 系统设置相关
- `SiteSettingsService` - 网站设置管理
- `SEOSettingsService` - SEO设置管理

## 命名规范

### 集合命名规范
- 使用Pascal命名法（首字母大写）
- 使用复数形式
- 例如：`Companies`, `NewsAnnouncements`, `ProductServices`

### Service类命名规范
- 实体名称 + "Service"
- 例如：`CompanyService`, `NewsAnnouncementService`

### 命名空间规范
- `MlSoft.Sites.Service.{模块名}`
- 例如：
  - `MlSoft.Sites.Service.Company`
  - `MlSoft.Sites.Service.Organization`
  - `MlSoft.Sites.Service.Investor`

## 依赖注入配置

所有Service类已在`Program.cs`中注册为Scoped服务：

```csharp
// Company Services
builder.Services.AddScoped<CompanyService>();
builder.Services.AddScoped<CompanyLocationService>();

// News Services
builder.Services.AddScoped<NewsAnnouncementService>();

// Business Services
builder.Services.AddScoped<ProductServiceService>();
builder.Services.AddScoped<BusinessDivisionService>();

// Organization Services
builder.Services.AddScoped<MlSoft.Sites.Service.Organization.ExecutiveService>();
builder.Services.AddScoped<MlSoft.Sites.Service.Organization.OrganizationStructureService>();

// ... 其他服务
```

## 使用示例

```csharp
// 在Controller中使用
public class CompanyController : Controller
{
    private readonly CompanyService _companyService;
    
    public CompanyController(CompanyService companyService)
    {
        _companyService = companyService;
    }
    
    public async Task<IActionResult> Index()
    {
        // 数据将存储在MongoDB的"Companies"集合中
        var companies = await _companyService.GetActiveCompaniesAsync();
        return View(companies);
    }
}
```

## 注意事项

1. **数据迁移**：如果已有数据使用旧的集合名称，需要在MongoDB中重命名集合或迁移数据
2. **一致性**：所有新的实体都应遵循相同的命名规范
3. **扩展性**：新增实体时，请遵循相同的命名模式

## MongoDB集合重命名命令

如果需要重命名现有集合，可以使用以下MongoDB命令：

```javascript
// 重命名集合示例
db.companies.renameCollection("Companies")
db.newsAnnouncements.renameCollection("NewsAnnouncements")
db.productServices.renameCollection("ProductServices")
db.businessDivisions.renameCollection("BusinessDivisions")
db.componentDefinitions.renameCollection("ComponentDefinitions")
db.pageConfigurations.renameCollection("PageConfigurations")
db.companyLocations.renameCollection("CompanyLocations")
```