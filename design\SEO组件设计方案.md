 SEO组件设计方案

  基于现有代码结构分析，我设计以下SEO组件方案：

  1. 组件概述

  创建一个标准化的SEO ViewComponent，自动处理TDK（Title、Description、Keywords）、Open Graph和Schema.org标记。

  2. 组件架构

  SEOViewComponent
  - 输入：PageConfigurationId 或 直接的SEO数据
  - 自动从数据库加载SEO设置
  - 支持多语言
  - 渲染HTML meta标签

    3. 支持的SEO元素（完整版）

  Schema.org 结构化数据（新增详细）

  基础类型：
  - WebSite - 网站基础信息
  - Organization - 组织机构信息
  - WebPage - 页面信息
  - BreadcrumbList - 面包屑导航

  业务相关类型：
  - Product - 产品/服务页面
  - Article - 新闻文章
  - JobPosting - 招聘信息
  - ContactPoint - 联系信息
  - Event - 活动/会议

  4. 数据模型扩展（新增Schema支持）

  在SEOSettings中增加Schema.org配置：

  public class SEOSettings
  {
      // ... 现有字段

      // Schema.org 配置
      public Dictionary<string, object> SchemaData { get; set; } = new();
      public List<string> EnabledSchemaTypes { get; set; } = new();
      public bool EnableAutoSchema { get; set; } = true; // 自动生成基础Schema
  }

  5. Schema.org输出示例

  <!-- WebSite Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "会社名",
    "url": "https://example.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://example.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  }
  </script>

  <!-- Organization Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "株式会社○○",
    "url": "https://example.com",
    "logo": "https://example.com/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+81-3-1234-5678",
      "contactType": "customer service"
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "東京都...",
      "addressCountry": "JP"
    }
  }
  </script>

  <!-- BreadcrumbList Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "ホーム",
        "item": "https://example.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "製品情報",
        "item": "https://example.com/products"
      }
    ]
  }
  </script>

  6. 组件使用方式（更新）

  <!-- 启用所有Schema支持 -->
  @await Component.InvokeAsync("SEO", new {
      pageConfigurationId = ViewData["PageId"],
      enableSchema = true,
      schemaTypes = new[] { "WebSite", "Organization", "BreadcrumbList" }
  })

  <!-- 产品页面 -->
  @await Component.InvokeAsync("SEO", new {
      pageConfigurationId = ViewData["PageId"],
      schemaType = "Product",
      schemaData = new {
          name = "製品名",
          description = "製品説明",
          brand = "ブランド名"
      }
  })

  7. 智能Schema生成特性

  - 自动类型推断：基于页面类型自动选择合适的Schema类型
  - 多语言Schema：根据当前语言生成对应的Schema内容
  - 数据源整合：自动从Company、Product等实体获取Schema数据
  - 验证支持：内置Schema.org标准验证