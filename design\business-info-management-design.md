# 业务信息管理功能设计文档

## 概述

基于现有实体定义，设计业务信息管理页面，采用类似 SiteSettings 的 Tab 页面结构，包含业务信息的两个核心模块：业务部门和产品服务。

## 功能结构

### 主页面：业务信息管理 (BusinessInfo)

参考 `src/Web/Views/Admin/SiteSettings/Index.cshtml` 的 Tab 页面设计，创建业务信息管理页面，包含以下2个Tab：

```
业务信息管理
├── 业务部门 (BusinessDivisions)
└── 产品服务 (ProductServices)
```

## Tab页面详细设计

### 1. 业务部门 Tab (BusinessDivisions)

**对应实体**: `BusinessDivision` (Collection)

**功能**:
- 业务部门信息管理
- 事业部介绍维护
- 业务部门排序和状态控制

**表格视图** + **新增/编辑弹窗**:

**主要字段**:
- **部门名称** (`Locale.DivisionName`) - 多语言支持 (中/英/日)
- **部门描述** (`Locale.Description`) - 多语言富文本编辑器
- **服务内容** (`Locale.Services`) - 多语言富文本编辑器
- **部门主图** (`ImageUrl`) - 图片上传
- **部门图标** (`IconUrl`) - 小图标上传
- **显示顺序** (`DisplayOrder`) - 数字输入
- **是否启用** (`IsActive`) - 开关控件

**界面布局**:
- 顶部操作栏：新增业务部门按钮
- 表格显示：部门名称、描述摘要、主图预览、显示顺序、状态、操作按钮
- 编辑弹窗：多语言表单，图片上传区域

### 2. 产品服务 Tab (ProductServices)

**对应实体**: `ProductService` (Collection)

**功能**:
- 产品服务信息管理
- 产品分类和文档管理
- 产品价格和状态控制

**表格视图** + **新增/编辑弹窗**:

**主要字段**:
- **产品名称** (`Locale.ProductName`) - 多语言支持 (中/英/日)
- **产品描述** (`Locale.Description`) - 多语言富文本编辑器
- **产品特性** (`Locale.Features`) - 多语言富文本编辑器
- **技术规格** (`Locale.Specifications`) - 多语言富文本编辑器
- **所属部门** (`BusinessDivisionId`) - 下拉选择（关联业务部门）
- **产品分类** (`Category`) - 下拉选择 (产品/服务/解决方案/技术)
- **产品图片** (`ImageUrls`) - 多图片上传
- **产品文档** (`Documents`) - 文档上传管理
- **产品价格** (`Price`) + **货币单位** (`Currency`) - 数字输入
- **显示顺序** (`DisplayOrder`) - 数字输入
- **是否启用** (`IsActive`) - 开关控件

**界面布局**:
- 顶部操作栏：新增产品服务按钮、分类筛选器
- 表格显示：产品名称、所属部门、分类、价格、图片预览、状态、操作按钮
- 编辑弹窗：多语言表单、图片上传、文档管理、价格设置

## 技术实现要点

### 1. Controller 设计

```csharp
// 新建: src/Web/Controllers/Admin/BusinessInfoController.cs
[Route("Admin/[controller]")]
[Route("{culture}/Admin/[controller]")]
public class BusinessInfoController : BaseController
{
    private readonly IViewRenderService _viewRenderService;
    private readonly IBusinessDivisionService _businessDivisionService;
    private readonly IProductServiceService _productServiceService;

    // Tab页面主视图 - 只加载业务部门数据
    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var businessDivisions = await _businessDivisionService.GetAllAsync();
        
        var viewModel = new BusinessInfoViewModel
        {
            BusinessDivisions = businessDivisions,
            // 产品服务数据为空，懒加载
        };

        ViewData["SupportedLanguages"] = SupportedLanguages;
        return View(viewModel);
    }

    // 懒加载 API 端点
    [HttpGet("tab/{tabName}")]
    public async Task<IActionResult> GetTabContent(string tabName)
    {
        try
        {
            var partialViewName = GetPartialViewName(tabName);
            var model = await GetTabDataAsync(tabName);

            ViewData["SupportedLanguages"] = SupportedLanguages;

            var html = await _viewRenderService.RenderPartialViewAsync(
                partialViewName, model, ViewData);

            return Json(new { success = true, html = html });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading tab content: {TabName}", tabName);
            return Json(new {
                success = false,
                message = AdminRes["LoadTabError"]
            });
        }
    }

    // 业务部门 CRUD API
    [HttpPost("business-division")]
    public async Task<IActionResult> SaveBusinessDivision([FromBody] BusinessDivisionViewModel model) { }

    [HttpGet("business-division/{id}")]
    public async Task<IActionResult> GetBusinessDivision(string id) { }

    [HttpDelete("business-division/{id}")]
    public async Task<IActionResult> DeleteBusinessDivision(string id) { }

    // 产品服务 CRUD API
    [HttpPost("product-service")]
    public async Task<IActionResult> SaveProductService([FromBody] ProductServiceViewModel model) { }

    [HttpGet("product-service/{id}")]
    public async Task<IActionResult> GetProductService(string id) { }

    [HttpDelete("product-service/{id}")]
    public async Task<IActionResult> DeleteProductService(string id) { }

    // 标签页名称映射
    private string GetPartialViewName(string tabName)
    {
        return tabName switch
        {
            "business-divisions" => "Admin/BusinessInfo/Partials/_BusinessDivisionsTab",
            "product-services" => "Admin/BusinessInfo/Partials/_ProductServicesTab",
            _ => throw new ArgumentException($"Invalid tab name: {tabName}")
        };
    }

    // 标签页数据获取
    private async Task<object> GetTabDataAsync(string tabName)
    {
        return tabName switch
        {
            "business-divisions" => await _businessDivisionService.GetAllAsync(),
            "product-services" => await _productServiceService.GetAllAsync(),
            _ => null
        };
    }
}
```

### 2. ViewModel 设计

```csharp
// 新建: src/Web/ViewModels/Admin/BusinessInfoViewModel.cs
public class BusinessInfoViewModel
{
    public List<BusinessDivisionViewModel> BusinessDivisions { get; set; } = new();
    public List<ProductServiceViewModel> ProductServices { get; set; } = new();
}

public class BusinessDivisionViewModel
{
    public string Id { get; set; } = string.Empty;
    public Dictionary<string, BusinessDivisionLocaleFields> Locale { get; set; } = new();
    public string? ImageUrl { get; set; }
    public string? IconUrl { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ProductServiceViewModel
{
    public string Id { get; set; } = string.Empty;
    public string? BusinessDivisionId { get; set; }
    public string? BusinessDivisionName { get; set; } // 用于显示
    public Dictionary<string, ProductServiceLocaleFields> Locale { get; set; } = new();
    public List<string> ImageUrls { get; set; } = new();
    public List<ProductDocumentViewModel> Documents { get; set; } = new();
    public ProductCategory Category { get; set; }
    public decimal? Price { get; set; }
    public string? Currency { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ProductDocumentViewModel
{
    public Dictionary<string, DocumentLocaleFields> Locale { get; set; } = new();
    public string FileUrl { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime UploadDate { get; set; }
}
```

### 3. Service 层调整

现有 Service 已基本覆盖所需功能：
- `BusinessDivisionService` - 业务部门管理
- `ProductServiceService` - 产品服务管理

**需要确认的服务方法**:
```csharp
// BusinessDivisionService 需要的方法
Task<List<BusinessDivision>> GetAllAsync();
Task<BusinessDivision?> GetByIdAsync(string id);
Task<BusinessDivision> CreateAsync(BusinessDivision entity);
Task<BusinessDivision> UpdateAsync(BusinessDivision entity);
Task<bool> DeleteAsync(string id);

// ProductServiceService 需要的方法
Task<List<ProductService>> GetAllAsync();
Task<List<ProductService>> GetByBusinessDivisionIdAsync(string businessDivisionId);
Task<ProductService?> GetByIdAsync(string id);
Task<ProductService> CreateAsync(ProductService entity);
Task<ProductService> UpdateAsync(ProductService entity);
Task<bool> DeleteAsync(string id);
```

### 4. 视图文件结构

```
src/Web/Views/Admin/BusinessInfo/
├── Index.cshtml                           # 主页面（Tab导航）
├── Partials/
│   ├── _BusinessDivisionsTab.cshtml      # 业务部门Tab
│   └── _ProductServicesTab.cshtml        # 产品服务Tab
└── Modals/
    ├── _BusinessDivisionModal.cshtml     # 业务部门编辑弹窗
    └── _ProductServiceModal.cshtml       # 产品服务编辑弹窗
```

## 实体调整建议

经过分析，现有实体定义已较为完善，建议的小幅调整：

### 1. BusinessDivision 实体优化

```csharp
// 在 BusinessDivision.cs 中确认字段完整性
public class BusinessDivision
{
    // ... 现有字段已完整，无需调整 ...

    // 可选：添加业务部门代码字段
    /// <summary>
    /// 业务部门代码 - 用于内部管理和排序
    /// </summary>
    public string? DivisionCode { get; set; }
}
```

### 2. ProductService 实体优化

```csharp
// 在 ProductService.cs 中确认字段完整性
public class ProductService
{
    // ... 现有字段已完整，无需调整 ...

    // 可选：添加产品编号字段
    /// <summary>
    /// 产品编号 - 用于产品识别和管理
    /// </summary>
    public string? ProductCode { get; set; }

    // 可选：添加产品状态枚举
    /// <summary>
    /// 产品状态 - 在售/停产/开发中等
    /// </summary>
    public ProductStatus Status { get; set; } = ProductStatus.Available;
}

// 添加产品状态枚举（如未定义）
public enum ProductStatus
{
    Available = 1,      // 在售
    Discontinued = 2,   // 停产
    Development = 3,    // 开发中
    ComingSoon = 4      // 即将推出
}
```

### 3. 本地化字段优化

```csharp
// 在 BusinessDivisionLocaleFields.cs 中确认字段
public class BusinessDivisionLocaleFields
{
    public string? DivisionName { get; set; }
    public string? Description { get; set; }
    public string? Services { get; set; }
    
    // 可选：添加更多字段
    public string? Mission { get; set; }        // 部门使命
    public string? Vision { get; set; }         // 部门愿景
    public string? KeyFeatures { get; set; }    // 核心特色
}

// 在 ProductServiceLocaleFields.cs 中确认字段
public class ProductServiceLocaleFields
{
    public string? ProductName { get; set; }
    public string? Description { get; set; }
    public string? Features { get; set; }
    public string? Specifications { get; set; }
    
    // 可选：添加更多字段
    public string? Applications { get; set; }   // 应用领域
    public string? Advantages { get; set; }     // 产品优势
    public string? Certifications { get; set; } // 认证信息
}
```

## 多语言资源

需要在以下资源文件中添加业务信息管理相关的本地化字符串：

### AdminResource.resx 新增项目

```xml
<!-- 页面标题 -->
<data name="BusinessInfoTitle" xml:space="preserve">
    <value>业务信息管理</value>
</data>
<data name="BusinessDivisions" xml:space="preserve">
    <value>业务部门</value>
</data>
<data name="ProductServices" xml:space="preserve">
    <value>产品服务</value>
</data>

<!-- 业务部门相关 -->
<data name="DivisionName" xml:space="preserve">
    <value>部门名称</value>
</data>
<data name="DivisionDescription" xml:space="preserve">
    <value>部门描述</value>
</data>
<data name="DivisionServices" xml:space="preserve">
    <value>服务内容</value>
</data>
<data name="DivisionImage" xml:space="preserve">
    <value>部门主图</value>
</data>
<data name="DivisionIcon" xml:space="preserve">
    <value>部门图标</value>
</data>
<data name="AddBusinessDivision" xml:space="preserve">
    <value>新增业务部门</value>
</data>
<data name="EditBusinessDivision" xml:space="preserve">
    <value>编辑业务部门</value>
</data>
<data name="DeleteBusinessDivision" xml:space="preserve">
    <value>删除业务部门</value>
</data>

<!-- 产品服务相关 -->
<data name="ProductName" xml:space="preserve">
    <value>产品名称</value>
</data>
<data name="ProductDescription" xml:space="preserve">
    <value>产品描述</value>
</data>
<data name="ProductFeatures" xml:space="preserve">
    <value>产品特性</value>
</data>
<data name="ProductSpecifications" xml:space="preserve">
    <value>技术规格</value>
</data>
<data name="BusinessDivision" xml:space="preserve">
    <value>所属部门</value>
</data>
<data name="ProductCategory" xml:space="preserve">
    <value>产品分类</value>
</data>
<data name="ProductImages" xml:space="preserve">
    <value>产品图片</value>
</data>
<data name="ProductDocuments" xml:space="preserve">
    <value>产品文档</value>
</data>
<data name="ProductPrice" xml:space="preserve">
    <value>产品价格</value>
</data>
<data name="Currency" xml:space="preserve">
    <value>货币单位</value>
</data>
<data name="AddProductService" xml:space="preserve">
    <value>新增产品服务</value>
</data>
<data name="EditProductService" xml:space="preserve">
    <value>编辑产品服务</value>
</data>
<data name="DeleteProductService" xml:space="preserve">
    <value>删除产品服务</value>
</data>

<!-- 产品分类选项 -->
<data name="Product" xml:space="preserve">
    <value>产品</value>
</data>
<data name="Service" xml:space="preserve">
    <value>服务</value>
</data>
<data name="Solution" xml:space="preserve">
    <value>解决方案</value>
</data>
<data name="Technology" xml:space="preserve">
    <value>技术</value>
</data>

<!-- 文档相关 -->
<data name="DocumentName" xml:space="preserve">
    <value>文档名称</value>
</data>
<data name="DocumentType" xml:space="preserve">
    <value>文档类型</value>
</data>
<data name="FileSize" xml:space="preserve">
    <value>文件大小</value>
</data>
<data name="UploadDate" xml:space="preserve">
    <value>上传日期</value>
</data>
<data name="AddDocument" xml:space="preserve">
    <value>添加文档</value>
</data>
<data name="RemoveDocument" xml:space="preserve">
    <value>移除文档</value>
</data>

<!-- 操作按钮 -->
<data name="Save" xml:space="preserve">
    <value>保存</value>
</data>
<data name="Cancel" xml:space="preserve">
    <value>取消</value>
</data>
<data name="Edit" xml:space="preserve">
    <value>编辑</value>
</data>
<data name="Delete" xml:space="preserve">
    <value>删除</value>
</data>
<data name="View" xml:space="preserve">
    <value>查看</value>
</data>
<data name="Filter" xml:space="preserve">
    <value>筛选</value>
</data>
<data name="Search" xml:space="preserve">
    <value>搜索</value>
</data>

<!-- 状态和消息 -->
<data name="Active" xml:space="preserve">
    <value>启用</value>
</data>
<data name="Inactive" xml:space="preserve">
    <value>禁用</value>
</data>
<data name="NoData" xml:space="preserve">
    <value>暂无数据</value>
</data>
<data name="Loading" xml:space="preserve">
    <value>加载中</value>
</data>
<data name="LoadTabError" xml:space="preserve">
    <value>加载标签页内容失败</value>
</data>
<data name="Retry" xml:space="preserve">
    <value>重试</value>
</data>
<data name="SaveSuccess" xml:space="preserve">
    <value>保存成功</value>
</data>
<data name="DeleteSuccess" xml:space="preserve">
    <value>删除成功</value>
</data>
<data name="SaveError" xml:space="preserve">
    <value>保存失败</value>
</data>
<data name="DeleteError" xml:space="preserve">
    <value>删除失败</value>
</data>
<data name="ConfirmDelete" xml:space="preserve">
    <value>确认删除？</value>
</data>
<data name="ValidationError" xml:space="preserve">
    <value>验证失败</value>
</data>
<data name="PleaseWaitProcessing" xml:space="preserve">
    <value>正在处理，请稍候...</value>
</data>
```

## 路由配置

在现有路由基础上添加：

```csharp
// 在 Program.cs 或路由配置中确认包含
app.MapControllerRoute(
    name: "AdminBusinessInfo",
    pattern: "{culture}/Admin/BusinessInfo/{action=Index}/{id?}",
    constraints: new { culture = "zh|en|ja" }
);
```

## 权限控制

继承现有 BaseController 的权限控制机制，确保只有管理员用户可以访问业务信息管理功能。

## 实现优先级

### 第一阶段：基础功能
1. 创建 BusinessInfoController 和基础 API
2. 实现业务部门 Tab 的 CRUD 功能
3. 实现产品服务 Tab 的 CRUD 功能
4. 添加基础的多语言支持

### 第二阶段：增强功能
1. 实现文件上传功能（图片、文档）
2. 添加产品分类筛选和搜索
3. 优化表格显示和排序功能
4. 完善错误处理和用户反馈

### 第三阶段：高级功能
1. 实现批量操作功能
2. 添加数据导入导出功能
3. 实现产品与业务部门的关联管理
4. 添加数据统计和分析功能

## 总结

此设计文档基于现有实体定义和 SiteSettings 页面结构，提供了完整的业务信息管理功能设计方案。通过 Tab 页面组织两个核心模块，既保持了界面的简洁性，又覆盖了日本企业网站的业务信息需求。

实现时需要重点关注：
1. 多语言支持的一致性
2. 文件上传功能的安全性
3. 数据验证的完整性
4. 用户体验的友好性
5. 响应式设计的兼容性
6. 懒加载性能优化
