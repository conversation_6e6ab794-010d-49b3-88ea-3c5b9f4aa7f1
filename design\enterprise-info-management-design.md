# 企业信息管理功能设计文档

## 概述

基于现有实体定义，设计企业信息管理页面，采用类似 SiteSettings 的 Tab 页面结构，包含企业信息的六个核心模块。

## 功能结构

### 主页面：企业信息管理 (CompanyInfo)

参考 `src/Web/Views/Admin/SiteSettings/Index.cshtml` 的 Tab 页面设计，创建企业信息管理页面，包含以下6个Tab：

```
企业信息管理
├── 基本信息 (BasicInfo)
├── 企业历史 (CompanyHistory)
├── 役員紹介/組織図 (ExecutiveOrganization)
├── 联系信息 (ContactInfo)
├── CSR (CSRActivities)
└── IR情報 (InvestorRelations)
```

## Tab页面详细设计

### 1. 基本信息 Tab (BasicInfo)

**对应实体**: `Company`

**功能**:
- 企业基础信息编辑（多语言支持）
- 企业标识信息管理
- 财务基本信息

**主要字段**:
- **企业代码** (`CompanyCode`) - 必填
- **企业名称** (`Locale.CompanyName`) - 多语言支持 (中/英/日)
- **企业简介** (`Locale.Description`) - 多语言支持
- **经营理念** (`Locale.Philosophy`) - 多语言支持
- **设立日期** (`EstablishedDate`) - 日期选择器
- **工商注册号** (`RegistrationNumber`)
- **注册资本** (`Capital`) + **货币单位** (`Currency`)
- **是否启用** (`IsActive`) - 开关控件

### 2. 企业历史 Tab (CompanyHistory)

**对应实体**: `CompanyHistory` (Collection)

**功能**:
- 企业历史事件管理
- 沿革时间轴维护
- 里程碑事件展示

**表格视图** + **新增/编辑弹窗**:
- **事件日期** (`EventDate`) - 日期选择器
- **事件类型** (`EventType`) - 下拉选择 (创立/上市/重组/新业务等)
- **事件标题** (`Locale.Title`) - 多语言支持
- **事件描述** (`Locale.Description`) - 多语言富文本编辑器
- **事件配图** (`ImageUrl`) - 图片上传
- **显示顺序** (`DisplayOrder`) - 数字输入
- **是否启用** (`IsActive`) - 开关控件

### 3. 役員紹介/組織图 Tab (ExecutiveOrganization)

**对应实体**: `Executive` + `OrganizationStructure`

**功能**:
- 高管信息管理
- 组织架构维护
- 社长消息管理

**分为两个子区域**:

#### 3.1 高管信息 (Executive Management)
**表格视图** + **新增/编辑弹窗**:
- **高管姓名** (`Locale.Name`) - 多语言支持
- **职位** (`Locale.Position`) - 多语言支持
- **高管照片** (`PhotoUrl`) - 图片上传
- **简历** (`Locale.Biography`) - 多语言富文本编辑器
- **致辞内容** (`Locale.Message`) - 多语言富文本编辑器（仅社长）
- **是否社长** (`IsPresident`) - 复选框
- **显示顺序** (`DisplayOrder`) - 数字输入
- **是否启用** (`IsActive`) - 开关控件

#### 3.2 组织架构 (Organization Structure)
**树形结构管理**:
- **部门名称** (`Locale.DepartmentName`) - 多语言支持
- **部门描述** (`Locale.Description`) - 多语言支持
- **上级部门** (`ParentDepartmentId`) - 下拉选择
- **层级** (`Level`) - 自动计算
- **显示顺序** (`DisplayOrder`) - 数字输入
- **是否启用** (`IsActive`) - 开关控件

### 4. 联系信息 Tab (ContactInfo)

**对应实体**: `Company.ContactInfo` + `CompanyLocation`

**功能**:
- 企业联系方式管理
- 多据点信息维护
- 地理位置管理

**分为两个子区域**:

#### 4.1 基础联系信息
- **电话号码** (`ContactInfo.Phone`)
- **传真号码** (`ContactInfo.Fax`)
- **电子邮箱** (`ContactInfo.Email`)
- **官方网站** (`ContactInfo.Website`)
- **邮政编码** (`ContactInfo.PostalCode`)
- **地址** (`ContactInfo.Locale.Address`) - 多语言支持
- **地理坐标** (`ContactInfo.Location.Latitude/Longitude`)

#### 4.2 企业据点信息 (Company Locations)
**表格视图** + **新增/编辑弹窗**:
- **据点名称** (`Locale.Name`) - 多语言支持
- **据点类型** (`LocationType`) - 总部/分部/工厂等
- **联系信息** (包含所有 ContactInfo 字段)
- **是否主要据点** (`IsPrimary`) - 复选框
- **显示顺序** (`DisplayOrder`)
- **是否启用** (`IsActive`)

### 5. CSR Tab (CSRActivities)

**对应实体**: `CSRActivity` (Collection)

**功能**:
- CSR活动管理
- 可持续发展报告
- 社会责任展示

**表格视图** + **新增/编辑弹窗**:
- **活动名称** (`Locale.Title`) - 多语言支持
- **活动描述** (`Locale.Description`) - 多语言富文本编辑器
- **成果总结** (`Locale.Summary`) - 多语言富文本编辑器
- **活动类型** (`Category`) - 环境保护/社会贡献/员工关怀等
- **开始日期** (`StartDate`) - 日期选择器
- **结束日期** (`EndDate`) - 日期选择器（可选）
- **活动图片** (`ImageUrls`) - 多图片上传
- **CSR报告** (`ReportFileUrl`) - 文件上传
- **是否启用** (`IsActive`) - 开关控件

### 6. IR情報 Tab (InvestorRelations)

**对应实体**: `FinancialReport` + `ShareholderMeeting` (Collections)

**功能**:
- 财务报告管理
- 股东大会信息
- 投资者关系维护

**分为两个子区域**:

#### 6.1 财务报告 (Financial Reports)
**表格视图** + **新增/编辑弹窗**:
- **报告类型** (`Type`) - 年报/季报/半年报
- **报告期间** (`Period`) - 年度/季度
- **报告年份** (`Year`) - 年份选择器
- **报告季度** (`Quarter`) - 季度选择器（季报时必填）
- **报告标题** (`Locale.Title`) - 多语言支持
- **报告摘要** (`Locale.Summary`) - 多语言富文本编辑器
- **报告文件** (`ReportFileUrl`) - PDF文件上传
- **发布日期** (`PublishDate`) - 日期选择器
- **营业收入** (`Revenue`) - 数字输入
- **净利润** (`NetIncome`) - 数字输入
- **总资产** (`TotalAssets`) - 数字输入
- **货币单位** (`Currency`) - 下拉选择
- **是否已发布** (`IsPublished`) - 开关控件

#### 6.2 股东大会 (Shareholder Meetings)
**表格视图** + **新增/编辑弹窗**:
- **会议日期** (`MeetingDate`) - 日期时间选择器
- **会议标题** (`Locale.Title`) - 多语言支持
- **会议描述** (`Locale.Description`) - 多语言富文本编辑器
- **会议状态** (`Status`) - 计划中/进行中/已结束
- **会议文档** (`Documents`) - 多文件上传，包含：
  - 文档标题 (`Document.Locale.Title`)
  - 文档描述 (`Document.Locale.Description`)
  - 文档类型 (`Document.Type`) - 议程/决议/材料等
  - 文档文件 (`Document.FileUrl`)

## 技术实现要点

### 1. Controller 设计
```csharp
// 新建: src/Web/Controllers/Admin/CompanyInfoController.cs
[Route("Admin/[controller]")]
[Route("{culture}/Admin/[controller]")]
public class CompanyInfoController : BaseController
{
    // Tab页面主视图
    [HttpGet]
    public async Task<IActionResult> Index() { }

    // 基本信息相关 API
    [HttpPost("basic-info")]
    public async Task<IActionResult> SaveBasicInfo([FromBody] CompanyBasicInfoViewModel model) { }

    // 企业历史相关 API
    [HttpPost("company-history")]
    public async Task<IActionResult> SaveCompanyHistory([FromBody] CompanyHistoryViewModel model) { }

    // 高管信息相关 API
    [HttpPost("executive")]
    public async Task<IActionResult> SaveExecutive([FromBody] ExecutiveViewModel model) { }

    // 组织架构相关 API
    [HttpPost("organization")]
    public async Task<IActionResult> SaveOrganization([FromBody] OrganizationViewModel model) { }

    // 其他 API...
}
```

### 2. ViewModel 设计
```csharp
// 新建: src/Web/ViewModels/Admin/CompanyInfoViewModel.cs
public class CompanyInfoViewModel
{
    public CompanyBasicInfoViewModel BasicInfo { get; set; }
    public List<CompanyHistoryItemViewModel> CompanyHistory { get; set; }
    public ExecutiveOrganizationViewModel ExecutiveOrganization { get; set; }
    public ContactInfoViewModel ContactInfo { get; set; }
    public List<CSRActivityItemViewModel> CSRActivities { get; set; }
    public InvestorRelationsViewModel InvestorRelations { get; set; }
}
```

### 3. Service 层调整

现有 Service 已基本覆盖所需功能：
- `CompanyService` - 企业基本信息
- `CompanyHistoryService` - 企业历史
- `ExecutiveService` - 高管信息
- `OrganizationStructureService` - 组织架构
- `CompanyLocationService` - 企业据点
- `CSRActivityService` - CSR活动
- `FinancialReportService` - 财务报告
- `ShareholderMeetingService` - 股东大会

### 4. 视图文件结构
```
src/Web/Views/Admin/CompanyInfo/
├── Index.cshtml                    # 主页面（Tab导航）
├── Partials/
│   ├── _BasicInfoTab.cshtml        # 基本信息Tab
│   ├── _CompanyHistoryTab.cshtml   # 企业历史Tab
│   ├── _ExecutiveOrgTab.cshtml     # 役員紹介/組織図Tab
│   ├── _ContactInfoTab.cshtml      # 联系信息Tab
│   ├── _CSRTab.cshtml              # CSR Tab
│   └── _InvestorRelationsTab.cshtml # IR情報Tab
└── Modals/
    ├── _CompanyHistoryModal.cshtml
    ├── _ExecutiveModal.cshtml
    ├── _OrganizationModal.cshtml
    ├── _LocationModal.cshtml
    ├── _CSRActivityModal.cshtml
    ├── _FinancialReportModal.cshtml
    └── _ShareholderMeetingModal.cshtml
```

## 实体调整建议

经过分析，现有实体定义已较为完善，建议的小幅调整：

### 1. Company 实体优化
```csharp
// 在 Company.cs 中添加
public class Company
{
    // ... 现有字段 ...

    /// <summary>
    /// 企业LOGO - 添加企业标识图片支持
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 企业规模 - 员工人数等规模信息
    /// </summary>
    public EmployeeScale? EmployeeScale { get; set; }
}

// 添加枚举
public enum EmployeeScale
{
    Small = 1,      // 1-50人
    Medium = 2,     // 51-300人
    Large = 3,      // 301-1000人
    Enterprise = 4  // 1000人以上
}
```

### 2. ContactInfo 增强
```csharp
// 在 ContactInfo.cs 中添加
public class ContactInfo
{
    // ... 现有字段 ...

    /// <summary>
    /// 营业时间 - 多语言支持
    /// </summary>
    public Dictionary<string, ContactInfoLocaleFields>? Locale { get; set; }
}

// 更新 ContactInfoLocaleFields
public class ContactInfoLocaleFields
{
    public string? Address { get; set; }

    /// <summary>
    /// 营业时间描述 - 如"周一至周五 9:00-18:00"
    /// </summary>
    public string? BusinessHours { get; set; }

    /// <summary>
    /// 交通信息 - 如"JR山手线新宿站东口徒步5分钟"
    /// </summary>
    public string? AccessInfo { get; set; }
}
```

### 3. CompanyLocation 实体微调
```csharp
// 在 CompanyLocation.cs 中确认是否有以下字段，如无需添加：
public class CompanyLocation
{
    // ... 现有字段 ...

    /// <summary>
    /// 据点类型
    /// </summary>
    public LocationType LocationType { get; set; }

    /// <summary>
    /// 是否为主要据点
    /// </summary>
    public bool IsPrimary { get; set; }
}

// 添加枚举（如未定义）
public enum LocationType
{
    Headquarters = 1,   // 总部
    Branch = 2,         // 分部
    Factory = 3,        // 工厂
    Office = 4,         // 办事处
    Laboratory = 5      // 实验室
}
```

## 多语言资源

需要在以下资源文件中添加企业信息管理相关的本地化字符串：

### AdminResource.resx 新增项目
```
CompanyInfoTitle = 企业信息管理
BasicInfo = 基本信息
CompanyHistory = 企业历史
ExecutiveOrganization = 役員紹介/組織図
ContactInfo = 联系信息
CSRActivities = CSR
InvestorRelations = IR情報

CompanyCode = 企业代码
CompanyName = 企业名称
CompanyDescription = 企业简介
Philosophy = 经营理念
EstablishedDate = 设立日期
RegistrationNumber = 工商注册号
Capital = 注册资本
Currency = 货币单位
LogoUrl = 企业LOGO
EmployeeScale = 企业规模

EventDate = 事件日期
EventType = 事件类型
EventTitle = 事件标题
EventDescription = 事件描述
EventImage = 事件配图
DisplayOrder = 显示顺序

ExecutiveName = 姓名
Position = 职位
Biography = 简历
Message = 致辞内容
IsPresident = 是否社长
PhotoUrl = 高管照片

DepartmentName = 部门名称
DepartmentDescription = 部门描述
ParentDepartment = 上级部门
Level = 层级

Phone = 电话号码
Fax = 传真号码
Email = 电子邮箱
Website = 官方网站
PostalCode = 邮政编码
Address = 地址
BusinessHours = 营业时间
AccessInfo = 交通信息
Latitude = 纬度
Longitude = 经度

LocationName = 据点名称
LocationType = 据点类型
IsPrimary = 主要据点

ActivityTitle = 活动名称
ActivityDescription = 活动描述
ActivitySummary = 成果总结
CSRCategory = 活动类型
StartDate = 开始日期
EndDate = 结束日期
ActivityImages = 活动图片
ReportFile = CSR报告

ReportType = 报告类型
ReportPeriod = 报告期间
ReportYear = 报告年份
Quarter = 报告季度
ReportTitle = 报告标题
ReportSummary = 报告摘要
ReportFile = 报告文件
PublishDate = 发布日期
Revenue = 营业收入
NetIncome = 净利润
TotalAssets = 总资产
IsPublished = 已发布

MeetingDate = 会议日期
MeetingTitle = 会议标题
MeetingDescription = 会议描述
MeetingStatus = 会议状态
MeetingDocuments = 会议文档
DocumentTitle = 文档标题
DocumentDescription = 文档描述
DocumentType = 文档类型
DocumentFile = 文档文件
```

## 路由配置

在现有路由基础上添加：
```csharp
// 在 Program.cs 或路由配置中确认包含
app.MapControllerRoute(
    name: "AdminCompanyInfo",
    pattern: "{culture}/Admin/CompanyInfo/{action=Index}/{id?}",
    constraints: new { culture = "zh|en|ja" }
);
```

## 权限控制

继承现有 BaseController 的权限控制机制，确保只有管理员用户可以访问企业信息管理功能。

## 总结

此设计文档基于现有实体定义和 SiteSettings 页面结构，提供了完整的企业信息管理功能设计方案。通过 Tab 页面组织六个核心模块，既保持了界面的简洁性，又覆盖了日本企业网站的所有核心信息需求。

实现时需要重点关注：
1. 多语言支持的一致性
2. 文件上传功能的安全性
3. 数据验证的完整性
4. 用户体验的友好性
5. 响应式设计的兼容性