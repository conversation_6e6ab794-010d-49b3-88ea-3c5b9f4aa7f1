# 留言信息功能设计方案

## 1. 功能概述

基于日本企业网站特点，设计一套完整的留言信息管理系统，包含客户留言、询盘管理、反馈处理等功能，符合日本商务环境中的"お問い合わせ"（咨询联系）业务需求。

## 2. 核心功能模块

### 2.1 客户留言模块 (Customer Messages)
- 前台留言表单提交
- 多语言留言支持（日语/英语）
- 留言分类管理（产品咨询、合作洽谈、技术支持等）
- 附件上传功能
- 反垃圾邮件机制

### 2.2 询盘管理模块 (Inquiry Management)
- 商务询盘处理
- 报价需求管理
- 客户联系信息管理
- 询盘状态跟踪

### 2.3 反馈管理模块 (Feedback Management)
- 后台留言处理
- 分配处理人员
- 回复状态管理
- 处理时效统计

### 2.4 通知系统模块 (Notification System)
- 新留言邮件通知
- 管理员消息提醒
- 客户回复确认

## 3. 数据库实体设计

### 3.1 主要实体结构

#### MessageInquiry (留言询盘实体)
```csharp
public class MessageInquiry
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;

    // 基本信息
    public string ContactName { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string? ContactPhone { get; set; }
    public string? CompanyName { get; set; }
    public string? JobTitle { get; set; }

    // 留言内容 - 多语言支持
    public Dictionary<string, MessageInquiryLocaleFields> Locale { get; set; } = new();

    // 分类和状态
    public MessageType Type { get; set; }
    public MessageCategory Category { get; set; }
    public MessageStatus Status { get; set; } = MessageStatus.New;
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    // 来源信息
    public string? SourcePage { get; set; }
    public string? ReferrerUrl { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string? UserAgent { get; set; }

    // 处理信息
    public string? AssignedToUserId { get; set; }
    public DateTime? AssignedAt { get; set; }
    public DateTime? FirstResponseAt { get; set; }
    public DateTime? ResolvedAt { get; set; }

    // 时间记录
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    // 附件和标签
    public List<string> AttachmentUrls { get; set; } = new();
    public List<string> Tags { get; set; } = new();

    // 反垃圾邮件
    public bool IsSpam { get; set; } = false;
    public double SpamScore { get; set; } = 0.0;

    // 跟进记录
    public List<MessageFollowUp> FollowUps { get; set; } = new();

    // 客户信息
    public CustomerInfo? Customer { get; set; }

    // 产品/服务相关
    public List<string> InterestedProductIds { get; set; } = new();
    public List<string> InterestedServiceIds { get; set; } = new();

    // 自动回复
    public bool AutoReplyEnabled { get; set; } = true;
    public DateTime? AutoReplySentAt { get; set; }

    // 评分和满意度
    public int? CustomerRating { get; set; }
    public string? CustomerFeedback { get; set; }
}
```

#### MessageFollowUp (跟进记录实体)
```csharp
public class MessageFollowUp
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public MessageFollowUpType Type { get; set; }
    public Dictionary<string, MessageFollowUpLocaleFields> Locale { get; set; } = new();
    public List<string> AttachmentUrls { get; set; } = new();
    public bool IsInternal { get; set; } = false; // 是否为内部备注
    public bool NotifyCustomer { get; set; } = false; // 是否通知客户
}
```

#### CustomerInfo (客户信息实体)
```csharp
public class CustomerInfo
{
    public string? CustomerId { get; set; }
    public string? CompanyRegistrationNumber { get; set; }
    public string? Industry { get; set; }
    public string? CompanySize { get; set; }
    public string? Country { get; set; }
    public string? Region { get; set; }
    public ContactInfo? ContactInfo { get; set; }
    public DateTime? LastContactDate { get; set; }
    public int TotalInquiries { get; set; } = 0;
    public CustomerType Type { get; set; } = CustomerType.Prospect;
}
```

### 3.2 枚举定义补充

需要在 `CommonEnums.cs` 中添加以下枚举：

```csharp
public enum MessageType
{
    GeneralInquiry = 0,     // 一般咨询
    ProductInquiry = 1,     // 产品咨询
    ServiceInquiry = 2,     // 服务咨询
    TechnicalSupport = 3,   // 技术支持
    BusinessCooperation = 4, // 商务合作
    PartnershipInquiry = 5, // 合作伙伴咨询
    MediaInquiry = 6,       // 媒体咨询
    CareerInquiry = 7,      // 招聘咨询
    Complaint = 8,          // 投诉建议
    Other = 9               // 其他
}

public enum MessageCategory
{
    Sales = 0,              // 销售相关
    Technical = 1,          // 技术相关
    Support = 2,            // 支持服务
    Partnership = 3,        // 合作伙伴
    Media = 4,              // 媒体相关
    HR = 5,                 // 人力资源
    Legal = 6,              // 法务相关
    Finance = 7,            // 财务相关
    General = 8             // 一般事务
}

public enum MessageStatus
{
    New = 0,                // 新留言
    InProgress = 1,         // 处理中
    WaitingForResponse = 2, // 等待回复
    WaitingForCustomer = 3, // 等待客户反馈
    Resolved = 4,           // 已解决
    Closed = 5,             // 已关闭
    Spam = 6                // 垃圾邮件
}

public enum MessagePriority
{
    Low = 0,                // 低优先级
    Normal = 1,             // 普通优先级
    High = 2,               // 高优先级
    Urgent = 3              // 紧急优先级
}

public enum MessageFollowUpType
{
    Reply = 0,              // 回复
    InternalNote = 1,       // 内部备注
    StatusChange = 2,       // 状态变更
    Assignment = 3,         // 分配处理人
    Escalation = 4,         // 升级处理
    Resolution = 5          // 解决方案
}

public enum CustomerType
{
    Prospect = 0,           // 潜在客户
    Lead = 1,               // 意向客户
    Customer = 2,           // 现有客户
    Partner = 3,            // 合作伙伴
    Vendor = 4,             // 供应商
    Media = 5,              // 媒体
    Government = 6,         // 政府机构
    Other = 7               // 其他
}
```

### 3.3 多语言字段定义

需要在 `LocaleFields` 中添加：

```csharp
public class MessageInquiryLocaleFields
{
    /// <summary>
    /// 留言标题 - 客户留言的主题
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// 留言内容 - 客户的详细咨询内容
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 期望服务 - 客户希望获得的服务
    /// </summary>
    public string? ExpectedService { get; set; }

    /// <summary>
    /// 备注信息 - 客户的补充说明
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 产品需求描述 - 对产品的具体需求
    /// </summary>
    public string? ProductRequirement { get; set; }

    /// <summary>
    /// 预算范围描述 - 客户的预算说明
    /// </summary>
    public string? BudgetRange { get; set; }

    /// <summary>
    /// 时间要求 - 客户的时间安排需求
    /// </summary>
    public string? TimeRequirement { get; set; }
}

public class MessageFollowUpLocaleFields
{
    /// <summary>
    /// 跟进标题 - 跟进记录的标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 跟进内容 - 详细的跟进记录内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 解决方案 - 提供给客户的解决方案
    /// </summary>
    public string? Solution { get; set; }

    /// <summary>
    /// 下一步行动 - 后续需要采取的行动
    /// </summary>
    public string? NextAction { get; set; }
}
```

## 4. 服务层设计

### 4.1 MessageInquiryService
```csharp
public class MessageInquiryService : MongoBaseService<MessageInquiry>
{
    // 基础CRUD操作继承自MongoBaseService

    // 专门的业务方法
    Task<MessageInquiry> CreateInquiryAsync(MessageInquiry inquiry);
    Task<bool> AssignToUserAsync(string inquiryId, string userId);
    Task<bool> UpdateStatusAsync(string inquiryId, MessageStatus status);
    Task<bool> AddFollowUpAsync(string inquiryId, MessageFollowUp followUp);
    Task<PagedResult<MessageInquiry>> GetPaginatedAsync(MessageQueryFilter filter);
    Task<List<MessageInquiry>> GetByStatusAsync(MessageStatus status);
    Task<List<MessageInquiry>> GetByAssigneeAsync(string userId);
    Task<MessageStatistics> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    Task<bool> MarkAsSpamAsync(string inquiryId);
    Task<bool> SendAutoReplyAsync(string inquiryId);
}
```

### 4.2 MessageNotificationService
```csharp
public class MessageNotificationService
{
    Task<bool> SendNewMessageNotificationAsync(MessageInquiry inquiry);
    Task<bool> SendAssignmentNotificationAsync(MessageInquiry inquiry, string assigneeUserId);
    Task<bool> SendCustomerReplyAsync(MessageInquiry inquiry, MessageFollowUp reply);
    Task<bool> SendStatusUpdateNotificationAsync(MessageInquiry inquiry);
    Task<bool> SendEscalationNotificationAsync(MessageInquiry inquiry);
}
```

### 4.3 MessageTemplateService
```csharp
public class MessageTemplateService
{
    Task<string> GetAutoReplyTemplateAsync(string language, MessageType type);
    Task<string> GetNotificationTemplateAsync(string templateName, string language);
    Task<bool> UpdateTemplateAsync(string templateName, string language, string content);
}
```

## 5. 控制器设计

### 5.1 前台控制器 (ContactController)
```csharp
[Route("contact")]
public class ContactController : BaseController
{
    [HttpGet]
    public IActionResult Index(); // 显示联系表单

    [HttpPost]
    public async Task<IActionResult> Submit(MessageInquiryViewModel model); // 提交留言

    [HttpGet("thank-you")]
    public IActionResult ThankYou(); // 提交成功页面

    [HttpPost("upload")]
    public async Task<IActionResult> UploadAttachment(IFormFile file); // 上传附件
}
```

### 5.2 后台管理控制器 (AdminMessageController)
```csharp
[Route("admin/messages")]
public class AdminMessageController : BaseController
{
    [HttpGet]
    public async Task<IActionResult> Index(MessageQueryFilter filter); // 留言列表

    [HttpGet("{id}")]
    public async Task<IActionResult> Details(string id); // 留言详情

    [HttpPost("{id}/assign")]
    public async Task<IActionResult> Assign(string id, string userId); // 分配处理人

    [HttpPost("{id}/status")]
    public async Task<IActionResult> UpdateStatus(string id, MessageStatus status); // 更新状态

    [HttpPost("{id}/follow-up")]
    public async Task<IActionResult> AddFollowUp(string id, MessageFollowUpViewModel model); // 添加跟进

    [HttpPost("{id}/reply")]
    public async Task<IActionResult> Reply(string id, MessageReplyViewModel model); // 回复客户

    [HttpGet("statistics")]
    public async Task<IActionResult> Statistics(); // 统计报表

    [HttpPost("bulk-action")]
    public async Task<IActionResult> BulkAction(BulkActionRequest request); // 批量操作
}
```

## 6. 视图模型设计

### 6.1 前台视图模型
```csharp
public class MessageInquiryViewModel
{
    [Required]
    public string ContactName { get; set; } = string.Empty;

    [Required, EmailAddress]
    public string ContactEmail { get; set; } = string.Empty;

    public string? ContactPhone { get; set; }
    public string? CompanyName { get; set; }
    public string? JobTitle { get; set; }

    [Required]
    public string Subject { get; set; } = string.Empty;

    [Required]
    public string Message { get; set; } = string.Empty;

    public MessageType Type { get; set; }
    public MessageCategory Category { get; set; }

    public List<string> InterestedProductIds { get; set; } = new();
    public List<string> InterestedServiceIds { get; set; } = new();

    public bool AcceptPrivacyPolicy { get; set; }
    public List<IFormFile>? Attachments { get; set; }
}
```

### 6.2 后台管理视图模型
```csharp
public class MessageManagementViewModel
{
    public PagedResult<MessageInquiry> Messages { get; set; } = new();
    public MessageQueryFilter Filter { get; set; } = new();
    public MessageStatistics Statistics { get; set; } = new();
    public List<User> AssignableUsers { get; set; } = new();
}

public class MessageDetailsViewModel
{
    public MessageInquiry Inquiry { get; set; } = new();
    public List<MessageFollowUp> FollowUps { get; set; } = new();
    public MessageFollowUpViewModel NewFollowUp { get; set; } = new();
    public List<User> AssignableUsers { get; set; } = new();
}
```

## 7. 数据库集合设计

### 7.1 MongoDB集合名称
```csharp
public static class CollectionNames
{
    public const string MessageInquiries = "messageinquiries";
    public const string MessageTemplates = "messagetemplates";
    public const string MessageSettings = "messagesettings";
}
```

### 7.2 索引设计
```javascript
// 基本查询索引
db.messageinquiries.createIndex({ "Status": 1 })
db.messageinquiries.createIndex({ "CreatedAt": -1 })
db.messageinquiries.createIndex({ "AssignedToUserId": 1 })
db.messageinquiries.createIndex({ "Type": 1 })
db.messageinquiries.createIndex({ "Category": 1 })

// 复合索引
db.messageinquiries.createIndex({ "Status": 1, "CreatedAt": -1 })
db.messageinquiries.createIndex({ "AssignedToUserId": 1, "Status": 1 })
db.messageinquiries.createIndex({ "ContactEmail": 1, "CreatedAt": -1 })

// 文本搜索索引
db.messageinquiries.createIndex({
    "ContactName": "text",
    "CompanyName": "text",
    "Locale.ja.Subject": "text",
    "Locale.ja.Message": "text",
    "Locale.en.Subject": "text",
    "Locale.en.Message": "text"
})
```

## 8. 前端功能设计

### 8.1 联系表单页面
- 响应式设计，支持移动端
- 分步骤表单或单页表单
- 实时表单验证
- 文件上传进度显示
- 多语言切换
- reCAPTCHA集成

### 8.2 后台管理界面
- 留言列表页面（支持筛选、排序、搜索）
- 留言详情页面（显示完整信息和处理历史）
- 快速回复功能
- 批量操作功能
- 统计仪表板
- 导出功能

## 9. 安全考虑

### 9.1 反垃圾邮件
- reCAPTCHA验证
- IP地址限制
- 内容过滤规则
- 邮箱域名验证
- 频率限制

### 9.2 数据保护
- 个人信息加密存储
- 访问日志记录
- 权限控制
- 数据保留策略
- GDPR合规

## 10. 性能优化

### 10.1 缓存策略
- 统计数据缓存
- 模板缓存
- 用户列表缓存

### 10.2 数据分页
- 前台和后台都支持分页
- 懒加载
- 虚拟滚动

## 11. 集成方案

### 11.1 邮件系统集成
- SMTP配置
- 邮件模板管理
- 发送状态跟踪
- 退信处理

### 11.2 CRM系统集成
- 客户数据同步
- 销售线索管理
- 跟进任务创建

### 11.3 工作流集成
- 自动分配规则
- 升级规则
- SLA监控
- 报告生成

## 12. 部署和维护

### 12.1 部署要求
- MongoDB索引创建脚本
- 配置文件模板
- 初始数据脚本
- 权限设置脚本

### 12.2 监控指标
- 新留言数量
- 响应时间
- 解决率
- 客户满意度
- 垃圾邮件比例

这个设计方案完整覆盖了日本企业网站留言系统的核心需求，包括多语言支持、完整的处理流程、统计分析等功能，符合日本商务环境的特点和要求。