# 实体定义调整清单

根据留言信息功能的设计方案，需要对现有实体进行以下调整和新增：

## 1. 需要新增的实体文件

### 1.1 新增实体文件路径：
```
src/Model/Entities/Messages/MessageInquiry.cs
src/Model/Entities/Messages/MessageFollowUp.cs
src/Model/Entities/Messages/CustomerInfo.cs
src/Model/Entities/Messages/MessageTemplate.cs
src/Model/Entities/Messages/MessageSettings.cs
```

### 1.2 新增服务文件路径：
```
src/Service/Messages/MessageInquiryService.cs
src/Service/Messages/MessageNotificationService.cs
src/Service/Messages/MessageTemplateService.cs
src/Service/Messages/MessageSettingsService.cs
```

### 1.3 新增控制器文件路径：
```
src/Web/Controllers/ContactController.cs
src/Web/Controllers/Admin/AdminMessageController.cs
```

### 1.4 新增视图模型文件路径：
```
src/Web/ViewModels/MessageInquiryViewModel.cs
src/Web/ViewModels/Admin/MessageManagementViewModel.cs
src/Web/ViewModels/Admin/MessageDetailsViewModel.cs
src/Web/ViewModels/Admin/MessageFollowUpViewModel.cs
src/Web/ViewModels/Admin/MessageReplyViewModel.cs
```

## 2. 需要修改的现有文件

### 2.1 枚举定义扩展 (`src/Model/Entities/Enums/CommonEnums.cs`)

需要添加以下枚举：
- `MessageType` - 留言类型枚举
- `MessageCategory` - 留言分类枚举
- `MessageStatus` - 留言状态枚举
- `MessagePriority` - 留言优先级枚举
- `MessageFollowUpType` - 跟进记录类型枚举
- `CustomerType` - 客户类型枚举

### 2.2 多语言字段扩展 (`src/Model/Entities/LocaleFields/CompanyLocaleFields.cs`)

需要添加以下多语言字段类：
- `MessageInquiryLocaleFields` - 留言多语言字段
- `MessageFollowUpLocaleFields` - 跟进记录多语言字段

### 2.3 文件夹类型扩展 (`src/Model/Entities/Enums/CommonEnums.cs` 中的 `EnumFolderType`)

需要添加：
```csharp
/// <summary>
/// 留言附件
/// </summary>
public const string Messages = "messages";
```

## 3. 数据库集合配置

### 3.1 需要在服务注册中添加新的集合名称常量：
```csharp
public static class CollectionNames
{
    // 现有集合...

    // 新增留言相关集合
    public const string MessageInquiries = "messageinquiries";
    public const string MessageTemplates = "messagetemplates";
    public const string MessageSettings = "messagesettings";
}
```

### 3.2 MongoDB索引创建脚本
需要创建数据库索引脚本用于优化查询性能。

## 4. 配置文件调整

### 4.1 `appsettings.json` 需要添加邮件配置：
```json
{
  "MessageSettings": {
    "EnableAutoReply": true,
    "EnableNotifications": true,
    "MaxAttachmentSize": 10485760,
    "AllowedFileTypes": [".pdf", ".doc", ".docx", ".txt", ".jpg", ".png"],
    "SpamFilterEnabled": true,
    "DefaultAssigneeUserId": ""
  },
  "EmailSettings": {
    "SmtpServer": "",
    "SmtpPort": 587,
    "Username": "",
    "Password": "",
    "FromEmail": "",
    "FromName": ""
  }
}
```

## 5. 资源文件调整

### 5.1 需要在多语言资源文件中添加留言相关的文本：

#### `src/Web/Resources/SharedResource.ja.resx`
- 留言表单相关文本
- 状态描述文本
- 邮件模板文本

#### `src/Web/Resources/SharedResource.en.resx`
- 对应的英语翻译

#### `src/Web/Resources/AdminResource.ja.resx` 和 `AdminResource.en.resx`
- 后台管理界面相关文本

## 6. 前端资源调整

### 6.1 JavaScript文件
需要新增：
```
src/Web/wwwroot/js/contact-form.js
src/Web/wwwroot/js/admin/message-management.js
```

### 6.2 CSS样式
留言表单和管理界面的样式定义。

## 7. 视图文件

### 7.1 前台视图
```
src/Web/Views/Contact/Index.cshtml
src/Web/Views/Contact/ThankYou.cshtml
```

### 7.2 后台管理视图
```
src/Web/Views/Admin/Message/Index.cshtml
src/Web/Views/Admin/Message/Details.cshtml
src/Web/Views/Admin/Message/Statistics.cshtml
```

## 8. 权限和角色调整

如果项目有权限系统，需要添加留言管理相关的权限：
- 查看留言权限
- 处理留言权限
- 分配留言权限
- 删除留言权限
- 导出留言权限

## 9. 路由配置

### 9.1 需要在路由配置中添加多语言支持的联系页面路由：
- `/contact` (英语)
- `/ja/contact` (日语)

### 9.2 后台管理路由：
- `/admin/messages`
- `/admin/messages/{id}`
- `/admin/messages/statistics`

## 10. 单元测试文件

建议为新功能创建相应的单元测试：
```
tests/Service/Messages/MessageInquiryServiceTests.cs
tests/Controllers/ContactControllerTests.cs
tests/Controllers/Admin/AdminMessageControllerTests.cs
```

## 11. 依赖注入配置

需要在 `Program.cs` 中注册新的服务：
```csharp
// 留言相关服务注册
builder.Services.AddScoped<MessageInquiryService>();
builder.Services.AddScoped<MessageNotificationService>();
builder.Services.AddScoped<MessageTemplateService>();
builder.Services.AddScoped<MessageSettingsService>();
```

## 实施优先级建议

1. **第一阶段**：基础实体和枚举定义
2. **第二阶段**：服务层实现和数据库集合
3. **第三阶段**：前台联系表单功能
4. **第四阶段**：后台管理功能
5. **第五阶段**：邮件通知和模板系统
6. **第六阶段**：统计报表和高级功能

这个调整清单提供了完整的实施路线图，确保新功能能够无缝集成到现有的项目架构中。