# 新闻资讯后台管理实体定义调整建议

## 1. 当前实体分析

基于对现有 `NewsAnnouncement` 实体的分析，针对后台管理功能需求，发现以下需要改进的地方：

### 1.1 现有实体优点
- 基础字段完整，包含多语言支持
- 支持图片管理和缩略图
- 有发布状态和推荐功能
- 包含阅读量统计

### 1.2 后台管理需要改进的地方
- 缺少定时发布功能
- 没有新闻状态管理（草稿、审核等）
- 缺少SEO相关字段
- 没有新闻来源和外部链接支持
- 缺少优先级和评论控制
- 缺少批量操作支持字段
- 多语言字段可以更丰富
- 缺少管理员操作审计字段

## 2. 建议的实体调整

### 2.1 NewsAnnouncement 实体调整

```csharp
using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.News
{
    public class NewsAnnouncement
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        /// <summary>
        /// 新闻公告唯一标识符
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 多语言字段 - 包含新闻标题、正文内容、摘要等本地化内容
        /// 日本企业网站"ニュース / プレスリリース"部分的核心数据
        /// 支持定期更新以显示企业活跃度
        /// </summary>
        public Dictionary<string, NewsAnnouncementLocaleFields> Locale { get; set; } = new();

        /// <summary>
        /// 新闻类型 - 如企业公告、产品更新、媒体报道、CSR活动等分类
        /// 对应"最新情報"、"メディア掲載"、"お知らせ"等不同栏目
        /// </summary>
        public NewsType Type { get; set; }

        /// <summary>
        /// 新闻状态 - 草稿、待审核、已发布、已归档
        /// 支持完整的内容管理流程
        /// </summary>
        public NewsStatus Status { get; set; } = NewsStatus.Draft;

        /// <summary>
        /// 发布日期 - 新闻的正式发布时间
        /// 用于新闻页面按时间排序，体现信息的时效性
        /// </summary>
        public DateTime PublishDate { get; set; }

        /// <summary>
        /// 定时发布时间 - 支持定时发布功能
        /// 允许管理员提前准备内容并设定发布时间
        /// </summary>
        public DateTime? ScheduledPublishDate { get; set; }

        /// <summary>
        /// 发布者ID - 关联发布该新闻的管理员或编辑人员
        /// 用于内部管理和权限控制
        /// </summary>
        public string? AuthorId { get; set; }

        /// <summary>
        /// 新闻来源 - 如内部发布、外部转载等
        /// 用于区分新闻的发布渠道和可信度
        /// </summary>
        public NewsSource Source { get; set; } = NewsSource.Internal;

        /// <summary>
        /// 外部链接 - 如果是转载新闻，提供原文链接
        /// 支持外部新闻的引用和追踪
        /// </summary>
        public string? ExternalUrl { get; set; }

        /// <summary>
        /// 新闻图片集合 - 配套的新闻图片、现场照片等
        /// 增强新闻内容的可读性和吸引力
        /// </summary>
        public List<string> ImageUrls { get; set; } = new();

        /// <summary>
        /// 缩略图 - 用于新闻列表页面展示的预览图片
        /// 提升"ニュース"页面的视觉效果
        /// </summary>
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// 是否已发布 - 控制新闻是否对外公开
        /// 支持草稿状态和定时发布功能
        /// </summary>
        public bool IsPublished { get; set; } = true;

        /// <summary>
        /// 是否为推荐新闻 - 标识重要新闻，用于首页推荐展示
        /// 突出重要的企业动态和里程碑事件
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// 新闻优先级 - 用于排序和推荐算法
        /// 数值越高优先级越高，用于重要新闻的置顶显示
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 阅读量 - 统计新闻的访问次数
        /// 用于分析用户关注度和内容效果
        /// </summary>
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// 评论数量 - 统计新闻的评论数
        /// 用于显示用户参与度和互动情况
        /// </summary>
        public int CommentCount { get; set; } = 0;

        /// <summary>
        /// 是否允许评论 - 控制评论功能开关
        /// 某些敏感或正式公告可能不需要评论功能
        /// </summary>
        public bool AllowComments { get; set; } = true;

        /// <summary>
        /// SEO关键词 - 用于搜索引擎优化
        /// 提高新闻在搜索结果中的排名
        /// </summary>
        public string? SeoKeywords { get; set; }

        /// <summary>
        /// 相关新闻ID列表 - 用于推荐相关新闻
        /// 提高用户粘性和内容发现率
        /// </summary>
        public List<string> RelatedNewsIds { get; set; } = new();

        /// <summary>
        /// 标签列表 - 用于新闻分类和搜索
        /// 支持更灵活的内容组织和发现
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 最后发布时间 - 记录新闻的最后发布时间
    /// 用于追踪新闻的发布历史
    /// </summary>
    public DateTime? LastPublishedAt { get; set; }

    /// <summary>
    /// 创建者ID - 记录新闻的创建者
    /// 用于权限控制和操作审计
    /// </summary>
    public string? CreatedById { get; set; }

    /// <summary>
    /// 最后修改者ID - 记录最后修改新闻的管理员
    /// 用于操作审计和权限控制
    /// </summary>
    public string? LastModifiedById { get; set; }

    /// <summary>
    /// 审核者ID - 记录审核新闻的管理员
    /// 用于审核流程管理
    /// </summary>
    public string? ReviewerId { get; set; }

    /// <summary>
    /// 审核时间 - 记录新闻的审核时间
    /// 用于审核流程追踪
    /// </summary>
    public DateTime? ReviewedAt { get; set; }

    /// <summary>
    /// 审核备注 - 审核时的备注信息
    /// 用于审核意见记录
    /// </summary>
    public string? ReviewNotes { get; set; }

    /// <summary>
    /// 是否允许编辑 - 控制新闻是否允许编辑
    /// 用于已发布重要新闻的保护
    /// </summary>
    public bool AllowEdit { get; set; } = true;

    /// <summary>
    /// 版本号 - 用于版本控制
    /// 支持新闻内容的版本管理
    /// </summary>
    public int Version { get; set; } = 1;
    }
}
```

### 2.2 NewsAnnouncementLocaleFields 多语言字段调整

```csharp
namespace MlSoft.Sites.Model.Entities.LocaleFields
{
    public class NewsAnnouncementLocaleFields
    {
        /// <summary>
        /// 新闻标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 新闻副标题 - 用于更详细的标题描述
        /// </summary>
        public string? Subtitle { get; set; }

        /// <summary>
        /// 新闻摘要 - 用于列表页显示
        /// </summary>
        public string? Summary { get; set; }

        /// <summary>
        /// 新闻正文内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 新闻作者 - 显示新闻的撰写者
        /// </summary>
        public string? Author { get; set; }

        /// <summary>
        /// 新闻来源 - 如"公司内部"、"外部媒体"等
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 标签数组 - 用于内容分类和搜索
        /// </summary>
        public string[]? Tags { get; set; }

        /// <summary>
        /// 分类标签 - 用于更细粒度的内容分类
        /// </summary>
        public string[]? Categories { get; set; }

        /// <summary>
        /// SEO描述 - 用于搜索引擎优化
        /// </summary>
        public string? SeoDescription { get; set; }

        /// <summary>
        /// 新闻位置 - 如"东京总部"、"大阪工厂"等
        /// </summary>
        public string? Location { get; set; }

    /// <summary>
    /// 相关链接 - 新闻中提到的相关链接
    /// </summary>
    public string[]? RelatedLinks { get; set; }

    /// <summary>
    /// 编辑备注 - 编辑时的备注信息
    /// 用于内部沟通和版本管理
    /// </summary>
    public string? EditNotes { get; set; }

    /// <summary>
    /// 审核状态描述 - 审核状态的详细描述
    /// 用于审核流程的详细记录
    /// </summary>
    public string? ReviewStatusDescription { get; set; }
    }
}
```

### 2.3 新增枚举定义

```csharp
namespace MlSoft.Sites.Model.Entities.Enums
{
    /// <summary>
    /// 新闻状态枚举
    /// </summary>
    public enum NewsStatus
    {
        Draft = 0,      // 草稿
        Review = 1,     // 待审核
        Published = 2,  // 已发布
        Archived = 3    // 已归档
    }

    /// <summary>
    /// 新闻来源枚举
    /// </summary>
    public enum NewsSource
    {
        Internal = 0,       // 内部发布
        External = 1,       // 外部转载
        PressRelease = 2,   // 新闻稿
        MediaReport = 3,    // 媒体报道
        Partner = 4         // 合作伙伴
    }

    /// <summary>
    /// 新闻优先级枚举
    /// </summary>
    public enum NewsPriority
    {
        Low = 0,        // 低优先级
        Normal = 1,     // 普通优先级
        High = 2,       // 高优先级
        Urgent = 3      // 紧急优先级
    }

    /// <summary>
    /// 批量操作类型枚举
    /// </summary>
    public enum BatchOperationType
    {
        Publish,        // 发布
        Unpublish,      // 取消发布
        Feature,        // 设为推荐
        Unfeature,      // 取消推荐
        Delete,         // 删除
        Archive,        // 归档
        ChangeStatus,   // 更改状态
        ChangeType      // 更改类型
    }

    /// <summary>
    /// 新闻审核状态枚举
    /// </summary>
    public enum NewsReviewStatus
    {
        Pending = 0,    // 待审核
        Approved = 1,   // 已通过
        Rejected = 2,   // 已拒绝
        NeedRevision = 3 // 需要修改
    }
}
```

## 3. 数据库索引优化建议

### 3.1 复合索引
```csharp
// 在 NewsAnnouncementService 构造函数中添加索引
public NewsAnnouncementService(IMongoDatabase database) : base(database, "NewsAnnouncements")
{
    // 主要查询索引：发布状态 + 发布日期
    var mainIndexKeys = Builders<NewsAnnouncement>.IndexKeys
        .Ascending(x => x.IsPublished)
        .Descending(x => x.PublishDate);
    var mainIndexModel = new CreateIndexModel<NewsAnnouncement>(mainIndexKeys);
    Collection.Indexes.CreateOne(mainIndexModel);

    // 类型查询索引
    var typeIndexKeys = Builders<NewsAnnouncement>.IndexKeys
        .Ascending(x => x.Type)
        .Descending(x => x.PublishDate);
    var typeIndexModel = new CreateIndexModel<NewsAnnouncement>(typeIndexKeys);
    Collection.Indexes.CreateOne(typeIndexModel);

    // 推荐新闻索引
    var featuredIndexKeys = Builders<NewsAnnouncement>.IndexKeys
        .Ascending(x => x.IsFeatured)
        .Descending(x => x.Priority)
        .Descending(x => x.PublishDate);
    var featuredIndexModel = new CreateIndexModel<NewsAnnouncement>(featuredIndexKeys);
    Collection.Indexes.CreateOne(featuredIndexModel);

    // 状态查询索引
    var statusIndexKeys = Builders<NewsAnnouncement>.IndexKeys
        .Ascending(x => x.Status)
        .Descending(x => x.CreatedAt);
    var statusIndexModel = new CreateIndexModel<NewsAnnouncement>(statusIndexKeys);
    Collection.Indexes.CreateOne(statusIndexModel);

    // 标签搜索索引
    var tagsIndexKeys = Builders<NewsAnnouncement>.IndexKeys.Ascending(x => x.Tags);
    var tagsIndexModel = new CreateIndexModel<NewsAnnouncement>(tagsIndexKeys);
    Collection.Indexes.CreateOne(tagsIndexModel);

    // 创建者索引 - 用于按创建者筛选
    var creatorIndexKeys = Builders<NewsAnnouncement>.IndexKeys.Ascending(x => x.CreatedById);
    var creatorIndexModel = new CreateIndexModel<NewsAnnouncement>(creatorIndexKeys);
    Collection.Indexes.CreateOne(creatorIndexModel);

    // 审核状态索引 - 用于审核流程管理
    var reviewIndexKeys = Builders<NewsAnnouncement>.IndexKeys
        .Ascending(x => x.Status)
        .Ascending(x => x.ReviewedAt);
    var reviewIndexModel = new CreateIndexModel<NewsAnnouncement>(reviewIndexKeys);
    Collection.Indexes.CreateOne(reviewIndexModel);

    // 定时发布索引 - 用于定时发布任务
    var scheduledIndexKeys = Builders<NewsAnnouncement>.IndexKeys
        .Ascending(x => x.ScheduledPublishDate)
        .Ascending(x => x.Status);
    var scheduledIndexModel = new CreateIndexModel<NewsAnnouncement>(scheduledIndexKeys);
    Collection.Indexes.CreateOne(scheduledIndexModel);
}
```

## 4. 服务层方法扩展

### 4.1 NewsAnnouncementService 扩展方法

```csharp
public class NewsAnnouncementService : MongoBaseService<NewsAnnouncement>
{
    // 现有方法...

    /// <summary>
    /// 搜索新闻
    /// </summary>
    public async Task<IEnumerable<NewsAnnouncement>> SearchNewsAsync(string searchTerm, int page = 1, int pageSize = 10)
    {
        var filter = Builders<NewsAnnouncement>.Filter.And(
            Builders<NewsAnnouncement>.Filter.Eq(x => x.IsPublished, true),
            Builders<NewsAnnouncement>.Filter.Or(
                Builders<NewsAnnouncement>.Filter.Regex("Locale.*.Title", new BsonRegularExpression(searchTerm, "i")),
                Builders<NewsAnnouncement>.Filter.Regex("Locale.*.Summary", new BsonRegularExpression(searchTerm, "i")),
                Builders<NewsAnnouncement>.Filter.In(x => x.Tags, new[] { searchTerm })
            )
        );

        return await GetPagedAsync(page, pageSize, filter);
    }

    /// <summary>
    /// 获取定时发布的新闻
    /// </summary>
    public async Task<IEnumerable<NewsAnnouncement>> GetScheduledNewsAsync()
    {
        var now = DateTime.UtcNow;
        var filter = Builders<NewsAnnouncement>.Filter.And(
            Builders<NewsAnnouncement>.Filter.Eq(x => x.Status, NewsStatus.Draft),
            Builders<NewsAnnouncement>.Filter.Lte(x => x.ScheduledPublishDate, now),
            Builders<NewsAnnouncement>.Filter.Ne(x => x.ScheduledPublishDate, null)
        );

        return await FindAsync(filter);
    }

    /// <summary>
    /// 获取待审核新闻
    /// </summary>
    public async Task<IEnumerable<NewsAnnouncement>> GetPendingReviewNewsAsync(int page = 1, int pageSize = 10)
    {
        var filter = Builders<NewsAnnouncement>.Filter.Eq(x => x.Status, NewsStatus.Review);
        return await GetPagedAsync(page, pageSize, filter);
    }

    /// <summary>
    /// 获取指定创建者的新闻
    /// </summary>
    public async Task<IEnumerable<NewsAnnouncement>> GetNewsByCreatorAsync(string creatorId, int page = 1, int pageSize = 10)
    {
        var filter = Builders<NewsAnnouncement>.Filter.Eq(x => x.CreatedById, creatorId);
        return await GetPagedAsync(page, pageSize, filter);
    }

    /// <summary>
    /// 获取新闻统计信息
    /// </summary>
    public async Task<Dictionary<string, object>> GetNewsStatisticsAsync()
    {
        var totalCount = await CountAsync(x => x.IsPublished);
        var featuredCount = await CountAsync(x => x.IsPublished && x.IsFeatured);
        var draftCount = await CountAsync(x => x.Status == NewsStatus.Draft);
        var todayCount = await CountAsync(x => x.IsPublished && x.PublishDate.Date == DateTime.UtcNow.Date);

        return new Dictionary<string, object>
        {
            ["TotalPublished"] = totalCount,
            ["Featured"] = featuredCount,
            ["Drafts"] = draftCount,
            ["TodayPublished"] = todayCount
        };
    }

    /// <summary>
    /// 批量更新新闻状态
    /// </summary>
    public async Task<bool> BatchUpdateStatusAsync(List<string> newsIds, NewsStatus status)
    {
        var filter = Builders<NewsAnnouncement>.Filter.In(x => x.Id, newsIds);
        var update = Builders<NewsAnnouncement>.Update
            .Set(x => x.Status, status)
            .Set(x => x.UpdatedAt, DateTime.UtcNow);

        var result = await Collection.UpdateManyAsync(filter, update);
        return result.ModifiedCount > 0;
    }

    /// <summary>
    /// 处理定时发布
    /// </summary>
    public async Task ProcessScheduledPublishingAsync()
    {
        var scheduledNews = await GetScheduledNewsAsync();
        
        foreach (var news in scheduledNews)
        {
            news.Status = NewsStatus.Published;
            news.IsPublished = true;
            news.PublishDate = DateTime.UtcNow;
            news.LastPublishedAt = DateTime.UtcNow;
            news.UpdatedAt = DateTime.UtcNow;

            await UpdateAsync(news.Id, news);
        }
    }

    /// <summary>
    /// 审核新闻
    /// </summary>
    public async Task<bool> ReviewNewsAsync(string id, string reviewerId, NewsReviewStatus reviewStatus, string? reviewNotes = null)
    {
        var news = await GetByIdAsync(id);
        if (news == null) return false;

        news.ReviewerId = reviewerId;
        news.ReviewedAt = DateTime.UtcNow;
        news.ReviewNotes = reviewNotes;
        news.UpdatedAt = DateTime.UtcNow;

        if (reviewStatus == NewsReviewStatus.Approved)
        {
            news.Status = NewsStatus.Published;
            news.IsPublished = true;
        }
        else if (reviewStatus == NewsReviewStatus.Rejected)
        {
            news.Status = NewsStatus.Draft;
        }
        else if (reviewStatus == NewsReviewStatus.NeedRevision)
        {
            news.Status = NewsStatus.Draft;
        }

        return await UpdateAsync(id, news);
    }

    /// <summary>
    /// 获取审核统计信息
    /// </summary>
    public async Task<Dictionary<string, int>> GetReviewStatisticsAsync()
    {
        var pendingCount = await CountAsync(x => x.Status == NewsStatus.Review);
        var approvedCount = await CountAsync(x => x.Status == NewsStatus.Published && x.ReviewedAt.HasValue);
        var rejectedCount = await CountAsync(x => x.Status == NewsStatus.Draft && x.ReviewedAt.HasValue && x.ReviewNotes != null);

        return new Dictionary<string, int>
        {
            ["Pending"] = pendingCount,
            ["Approved"] = approvedCount,
            ["Rejected"] = rejectedCount
        };
    }
}
```

## 5. 迁移策略

### 5.1 数据迁移脚本

```csharp
public class NewsDataMigrationService
{
    public async Task MigrateExistingNewsAsync()
    {
        // 1. 为现有新闻添加默认状态
        var existingNews = await _newsService.GetAllAsync();
        
        foreach (var news in existingNews)
        {
            if (news.Status == NewsStatus.Draft && news.IsPublished)
            {
                news.Status = NewsStatus.Published;
                news.LastPublishedAt = news.PublishDate;
                await _newsService.UpdateAsync(news.Id, news);
            }
        }

        // 2. 创建必要的索引
        await CreateIndexesAsync();
    }

    private async Task CreateIndexesAsync()
    {
        // 索引创建逻辑...
    }
}
```

## 6. 总结

通过以上调整，新闻资讯后台管理功能将具备：

1. **完整的状态管理**：支持草稿、审核、发布、归档的完整流程
2. **定时发布功能**：支持提前准备内容并定时发布
3. **丰富的多语言支持**：更详细的本地化字段
4. **SEO优化**：支持关键词和描述优化
5. **灵活的标签系统**：支持多维度内容分类
6. **完善的审核流程**：支持多级审核和权限控制
7. **批量操作支持**：提高管理效率
8. **操作审计功能**：记录所有管理操作
9. **版本控制**：支持内容版本管理
10. **完善的统计功能**：支持数据分析和决策
11. **高性能查询**：通过索引优化提升查询效率

这些调整将使新闻资讯后台管理功能更加完善和实用，更好地满足日本中小型化工企业官网管理系统的需求。
