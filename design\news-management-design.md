# 新闻资讯后台管理功能设计文档

## 1. 功能概述

新闻资讯后台管理功能是日本中小型化工企业官网管理系统的核心模块之一，为管理员提供完整的新闻内容管理能力。该功能支持多语言内容管理、分类展示、搜索过滤、推荐管理、定时发布等特性，旨在帮助管理员高效管理企业新闻资讯内容。

## 2. 现有实体分析

### 2.1 NewsAnnouncement 实体
当前实体定义较为完整，包含以下核心字段：
- `Id`: 唯一标识符
- `Locale`: 多语言字段字典
- `Type`: 新闻类型（NewsType枚举）
- `PublishDate`: 发布日期
- `AuthorId`: 发布者ID
- `ImageUrls`: 新闻图片集合
- `ThumbnailUrl`: 缩略图
- `IsPublished`: 发布状态
- `IsFeatured`: 推荐状态
- `ViewCount`: 阅读量
- `CreatedAt/UpdatedAt`: 时间戳

### 2.2 NewsAnnouncementLocaleFields 多语言字段
包含以下字段：
- `Title`: 新闻标题
- `Summary`: 新闻摘要
- `Content`: 新闻正文
- `Tags`: 标签数组

### 2.3 NewsType 枚举
当前支持的类型：
- `CompanyNews`: 企业新闻
- `PressRelease`: 新闻发布
- `ProductUpdate`: 产品更新
- `Event`: 活动
- `MediaCoverage`: 媒体报道
- `Announcement`: 公告

## 3. 后台管理功能模块设计

### 3.1 新闻管理页面
- **路径**: `/admin/news`
- **功能**:
  - 新闻列表管理（增删改查）
  - 批量操作（发布、取消发布、推荐、删除）
  - 高级搜索和筛选
  - 草稿管理
  - 发布状态管理

### 3.2 新闻编辑页面
- **路径**: `/admin/news/create` 或 `/admin/news/edit/{id}`
- **功能**:
  - 富文本编辑器
  - 多语言内容编辑
  - 图片上传和管理
  - 标签管理
  - 预览功能
  - 定时发布设置

### 3.3 新闻统计页面
- **路径**: `/admin/news/statistics`
- **功能**:
  - 阅读量统计
  - 新闻类型分布
  - 发布趋势分析
  - 热门新闻排行

## 4. 技术实现方案

### 4.1 控制器设计

#### 4.1.1 AdminNewsController（后台管理）
```csharp
public class AdminNewsController : AdminController
{
    // GET: /admin/news
    public async Task<IActionResult> Index()
    
    // GET: /admin/news/create
    public IActionResult Create()
    
    // POST: /admin/news/create
    public async Task<IActionResult> Create(NewsAnnouncementViewModel model)
    
    // GET: /admin/news/edit/{id}
    public async Task<IActionResult> Edit(string id)
    
    // POST: /admin/news/edit/{id}
    public async Task<IActionResult> Edit(string id, NewsAnnouncementViewModel model)
    
    // POST: /admin/news/delete/{id}
    public async Task<IActionResult> Delete(string id)
    
    // POST: /admin/news/toggle-featured/{id}
    public async Task<IActionResult> ToggleFeatured(string id)
    
    // GET: /admin/news/statistics
    public async Task<IActionResult> Statistics()
    
    // POST: /admin/news/batch-update
    public async Task<IActionResult> BatchUpdate(BatchUpdateRequest request)
    
    // POST: /admin/news/schedule-publish/{id}
    public async Task<IActionResult> SchedulePublish(string id, DateTime publishDate)
}
```

### 4.2 服务层扩展

#### 4.2.1 NewsAnnouncementService 扩展方法
```csharp
public class NewsAnnouncementService : MongoBaseService<NewsAnnouncement>
{
    // 现有方法...
    
    // 新增方法
    public async Task<IEnumerable<NewsAnnouncement>> SearchNewsAsync(string searchTerm, int page = 1, int pageSize = 10)
    public async Task<IEnumerable<NewsAnnouncement>> GetNewsByDateRangeAsync(DateTime startDate, DateTime endDate)
    public async Task<Dictionary<NewsType, int>> GetNewsTypeStatisticsAsync()
    public async Task<IEnumerable<NewsAnnouncement>> GetDraftNewsAsync()
    public async Task<bool> SchedulePublishAsync(string id, DateTime publishDate)
    public async Task<bool> BatchUpdateStatusAsync(List<string> newsIds, NewsStatus status)
    public async Task<Dictionary<string, object>> GetNewsStatisticsAsync()
    public async Task ProcessScheduledPublishingAsync()
}
```

### 4.3 视图模型设计

#### 4.3.1 AdminNewsIndexViewModel
```csharp
public class AdminNewsIndexViewModel
{
    public IEnumerable<NewsAnnouncement> News { get; set; }
    public int CurrentPage { get; set; }
    public int TotalPages { get; set; }
    public NewsType? SelectedType { get; set; }
    public NewsStatus? SelectedStatus { get; set; }
    public string SearchTerm { get; set; }
    public IEnumerable<NewsType> AvailableTypes { get; set; }
    public Dictionary<string, object> Statistics { get; set; }
}
```

#### 4.3.2 NewsAnnouncementViewModel
```csharp
public class NewsAnnouncementViewModel
{
    public string Id { get; set; }
    public NewsType Type { get; set; }
    public DateTime PublishDate { get; set; }
    public string? AuthorId { get; set; }
    public List<string> ImageUrls { get; set; } = new();
    public string? ThumbnailUrl { get; set; }
    public bool IsPublished { get; set; }
    public bool IsFeatured { get; set; }
    public Dictionary<string, NewsAnnouncementLocaleFields> Locale { get; set; } = new();
    public DateTime? ScheduledPublishDate { get; set; }
    public NewsStatus Status { get; set; }
    public int Priority { get; set; }
    public bool AllowComments { get; set; }
    public string? SeoKeywords { get; set; }
    public List<string> Tags { get; set; } = new();
}

#### 4.3.3 BatchUpdateRequest
```csharp
public class BatchUpdateRequest
{
    public List<string> NewsIds { get; set; } = new();
    public string Action { get; set; } // "publish", "unpublish", "featured", "delete", "archive"
    public NewsStatus? Status { get; set; }
    public bool? IsFeatured { get; set; }
}
```

#### 4.3.4 NewsStatisticsViewModel
```csharp
public class NewsStatisticsViewModel
{
    public int TotalPublished { get; set; }
    public int TotalDrafts { get; set; }
    public int TotalFeatured { get; set; }
    public int TodayPublished { get; set; }
    public Dictionary<NewsType, int> TypeDistribution { get; set; }
    public List<NewsAnnouncement> TopViewedNews { get; set; }
    public Dictionary<string, int> MonthlyPublishTrend { get; set; }
}
```

## 5. 后台管理页面设计

### 5.1 新闻管理列表页面
- **布局**: 表格布局，支持排序和筛选
- **功能**:
  - 批量操作工具栏
  - 高级搜索面板
  - 状态指示器
  - 快速编辑功能

### 5.2 新闻编辑页面
- **布局**: 分栏布局（编辑区 + 预览区）
- **组件**:
  - 多语言标签页
  - 富文本编辑器
  - 图片上传组件
  - 标签输入组件
  - 发布设置面板
  - 定时发布设置
  - SEO设置面板
  - 状态管理面板

### 5.3 新闻统计页面
- **布局**: 仪表板布局，包含图表和统计卡片
- **组件**:
  - 统计卡片组件（总数、今日发布、草稿数等）
  - 图表组件（类型分布、发布趋势、阅读量排行）
  - 数据导出功能
  - 时间范围筛选器

## 6. 数据库优化建议

### 6.1 索引优化
```csharp
// 在 NewsAnnouncementService 构造函数中添加索引
public NewsAnnouncementService(IMongoDatabase database) : base(database, "NewsAnnouncements")
{
    // 创建复合索引
    var indexKeys = Builders<NewsAnnouncement>.IndexKeys
        .Ascending(x => x.IsPublished)
        .Descending(x => x.PublishDate);
    
    var indexModel = new CreateIndexModel<NewsAnnouncement>(indexKeys);
    Collection.Indexes.CreateOne(indexModel);
    
    // 创建类型索引
    var typeIndexKeys = Builders<NewsAnnouncement>.IndexKeys.Ascending(x => x.Type);
    var typeIndexModel = new CreateIndexModel<NewsAnnouncement>(typeIndexKeys);
    Collection.Indexes.CreateOne(typeIndexModel);
}
```

### 6.2 缓存策略
- 新闻列表：缓存15分钟
- 统计数据：缓存1小时
- 新闻类型统计：缓存30分钟
- 草稿列表：缓存5分钟

## 7. 实体定义调整建议

### 7.1 NewsAnnouncement 实体调整

#### 7.1.1 新增字段
```csharp
public class NewsAnnouncement
{
    // 现有字段...
    
    /// <summary>
    /// 定时发布时间 - 支持定时发布功能
    /// </summary>
    public DateTime? ScheduledPublishDate { get; set; }
    
    /// <summary>
    /// 新闻来源 - 如内部发布、外部转载等
    /// </summary>
    public string? Source { get; set; }
    
    /// <summary>
    /// 外部链接 - 如果是转载新闻，提供原文链接
    /// </summary>
    public string? ExternalUrl { get; set; }
    
    /// <summary>
    /// SEO关键词 - 用于搜索引擎优化
    /// </summary>
    public string? SeoKeywords { get; set; }
    
    /// <summary>
    /// 新闻优先级 - 用于排序和推荐算法
    /// </summary>
    public int Priority { get; set; } = 0;
    
    /// <summary>
    /// 是否允许评论 - 控制评论功能开关
    /// </summary>
    public bool AllowComments { get; set; } = true;
    
    /// <summary>
    /// 新闻状态 - 草稿、待审核、已发布、已归档
    /// </summary>
    public NewsStatus Status { get; set; } = NewsStatus.Draft;
}
```

#### 7.1.2 新增枚举
```csharp
public enum NewsStatus
{
    Draft = 0,      // 草稿
    Review = 1,     // 待审核
    Published = 2,  // 已发布
    Archived = 3    // 已归档
}

public enum NewsSource
{
    Internal,       // 内部发布
    External,       // 外部转载
    PressRelease,   // 新闻稿
    MediaReport     // 媒体报道
}
```

### 7.2 NewsAnnouncementLocaleFields 调整

```csharp
public class NewsAnnouncementLocaleFields
{
    public string? Title { get; set; }
    public string? Summary { get; set; }
    public string? Content { get; set; }
    public string[]? Tags { get; set; }
    
    // 新增字段
    /// <summary>
    /// 新闻副标题
    /// </summary>
    public string? Subtitle { get; set; }
    
    /// <summary>
    /// 新闻作者
    /// </summary>
    public string? Author { get; set; }
    
    /// <summary>
    /// 新闻来源
    /// </summary>
    public string? Source { get; set; }
    
    /// <summary>
    /// SEO描述
    /// </summary>
    public string? SeoDescription { get; set; }
    
    /// <summary>
    /// 新闻分类标签
    /// </summary>
    public string[]? Categories { get; set; }
}
```

## 8. 实现优先级

### 8.1 第一阶段（核心功能）
1. 创建 AdminNewsController
2. 实现新闻管理列表页面
3. 实现基础的增删改查功能
4. 添加多语言内容编辑支持

### 8.2 第二阶段（增强功能）
1. 实现搜索和筛选功能
2. 添加图片上传和管理
3. 实现批量操作功能
4. 添加新闻统计页面

### 8.3 第三阶段（高级功能）
1. 实现定时发布功能
2. 添加高级统计分析
3. 实现SEO优化功能
4. 优化性能和用户体验

## 9. 测试策略

### 9.1 单元测试
- 服务层方法测试
- 数据验证测试
- 多语言处理测试

### 9.2 集成测试
- 控制器API测试
- 数据库操作测试
- 缓存功能测试
- 批量操作测试

### 9.3 性能测试
- 大量数据加载测试
- 搜索性能测试
- 批量操作性能测试
- 富文本编辑器性能测试

## 10. 部署和维护

### 10.1 数据迁移
- 现有新闻数据迁移脚本
- 索引创建脚本
- 缓存预热脚本

### 10.2 监控指标
- 新闻发布频率
- 管理员操作统计
- 系统性能指标
- 错误日志监控
- 批量操作成功率

---

*本文档版本: 1.0*  
*创建日期: 2024年*  
*最后更新: 2024年*
