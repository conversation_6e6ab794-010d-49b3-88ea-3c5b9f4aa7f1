# 首页配置文件使用说明

## 概述

本文档介绍了为MlSoft Sites化工企业网站首页生成的完整配置文件，包含多语言支持、SEO优化、性能配置和缓存策略。

## 配置文件位置

- **JSON配置**: `/design/pages/home-page-config.json`
- **数据库种子**: `/src/Service/Seeders/DataSeeder.cs`
- **页面控制器**: `/src/Web/Controllers/PageController.cs`
- **动态视图**: `/src/Web/Views/Page/Dynamic.cshtml`

## 配置文件结构

### 1. 基础配置

```json
{
  "pageKey": "home",
  "layoutTemplate": "_Layout",
  "status": "Published",
  "publishDate": "2024-03-01T00:00:00Z"
}
```

### 2. 多语言配置

支持中文(zh)、英文(en)、日文(ja)三种语言：

```json
"locale": {
  "zh": {
    "pageTitle": "欢迎访问 MlSoft Sites - 领先的化工解决方案提供商",
    "metaDescription": "专业的化工解决方案提供商..."
  },
  "en": {
    "pageTitle": "Welcome to MlSoft Sites - Leading Chemical Solutions Provider",
    "metaDescription": "Professional chemical solutions provider..."
  },
  "ja": {
    "pageTitle": "MlSoft Sitesへようこそ - 化学ソリューションのリーディングプロバイダー",
    "metaDescription": "専門的な化学ソリューションプロバイダー..."
  }
}
```

### 3. 页面组件配置

首页包含以下组件：

1. **Hero组件** (DisplayOrder: 1)
   - 展示主标题和副标题
   - 背景图片和CTA按钮
   - 高度600px

2. **企业简介组件** (DisplayOrder: 2)
   - 文本+图片布局
   - 图片位置：右侧
   - 包含公司介绍和ISO认证信息

3. **核心业务组件** (DisplayOrder: 3)
   - 三栏网格布局
   - 基础化工、精细化工、技术服务
   - 使用primary主题色彩

### 4. SEO配置

每种语言都有完整的SEO配置：

```json
"seoConfig": {
  "zh": {
    "metaTitle": "MlSoft Sites - 专业化工解决方案提供商 | 安全环保可持续",
    "metaDescription": "专业的化工解决方案提供商...",
    "metaKeywords": "化工解决方案,化工产品,技术服务,ISO认证",
    "ogTitle": "MlSoft Sites - 专业化工解决方案提供商",
    "ogImage": "/images/og/home-zh.jpg",
    "canonicalUrl": "https://mlsoft-sites.com/",
    "noIndex": false,
    "noFollow": false
  }
}
```

### 5. 性能配置

优化页面加载性能：

```json
"performance": {
  "enableImageLazyLoading": true,
  "enableComponentLazyLoading": false,
  "maxComponentsPerPage": 20,
  "enableBundleOptimization": true,
  "imageQuality": 85,
  "enableWebpFormat": true
}
```

### 6. 缓存配置

智能缓存策略：

```json
"cacheConfig": {
  "cacheDurationMinutes": 60,
  "cacheVaryByParams": ["theme", "culture"],
  "enableCDNCache": true,
  "cacheInvalidationTags": ["home", "global", "components"],
  "enableOutputCache": true,
  "cacheProfile": "HomePage"
}
```

### 7. 访问控制

```json
"accessControl": {
  "requireAuthentication": false,
  "requiredRoles": [],
  "allowedUsers": [],
  "enableGeoRestriction": false
}
```

## 使用方法

### 1. 开发环境自动种子

在开发环境启动时，DataSeeder会自动创建首页配置：

```csharp
public async Task SeedAsync()
{
    await SeedAdminUserAsync();
    await SeedCompanyDataAsync();
    await SeedNewsDataAsync();
    await SeedBusinessDivisionDataAsync();
    await SeedPageConfigurationsAsync(); // 创建首页配置
}
```

### 2. 访问首页

- 默认语言: `https://localhost:5001/`
- 中文: `https://localhost:5001/zh/`
- 英文: `https://localhost:5001/en/`
- 日文: `https://localhost:5001/ja/`

### 3. 页面渲染

Page控制器会：
1. 检查缓存
2. 获取页面配置
3. 验证访问权限
4. 渲染动态组件
5. 应用缓存策略

### 4. 动态路由

系统会自动注册以下路由：
- `page_home_zh` -> `zh/home`
- `page_home_en` -> `en/home`  
- `page_home_ja` -> `ja/home`

## 主题色彩支持

所有组件样式都使用主题变量：
- `text-primary-600` - 主要文本色
- `bg-primary-600` - 主要背景色
- `border-primary-500` - 边框色
- 支持深色模式: `dark:bg-gray-800`

## 自定义组件参数

每个组件的参数都通过JSON配置，支持：
- 多语言文本
- 图片路径
- 布局选项
- 样式设置
- 功能开关

## 缓存失效策略

当内容更新时，可通过以下标签失效缓存：
- `home` - 首页相关缓存
- `global` - 全局缓存
- `components` - 组件缓存

## 性能监控

系统会自动监控：
- 页面渲染时间
- 缓存命中率
- 组件加载数量
- 错误率统计

## 注意事项

1. **图片资源**: 确保配置中引用的图片文件存在于wwwroot目录
2. **组件依赖**: Hero、Content等组件需要预先定义
3. **主题兼容**: 使用primary色彩变量确保主题切换正常
4. **多语言**: 所有文本都需要提供三种语言版本
5. **SEO图片**: OG和Twitter图片需要符合平台规范

这个配置文件为化工企业网站提供了完整的首页解决方案，包含了现代企业网站所需的所有功能和优化措施。