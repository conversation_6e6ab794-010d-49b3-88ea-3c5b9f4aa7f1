
PageConfigContent 对象实体（不是数据表，是个实体，转为JSON字符串后加密存到PageConfiguration表中。
{
  "pageKey": "home",
  "route": "{culture}/",
  "layoutTemplate": "_Layout",
  "components": [
    {
      "componentDefinitionId": "hero_component",
      "templateKey": "Default",
      "parametersJson": "", 
      "displayOrder": 1,
      "isVisible": true
    },
    {
      "componentDefinitionId": "content_component",
      "templateKey": "Default",
      "parametersJson": "",
      "displayOrder": 2,
      "isVisible": true
    },
    {
      "componentDefinitionId": "content_component",
      "templateKey": "Default",
      "parametersJson": "",
      "displayOrder": 3,
      "isVisible": true
    },
    {
      "componentDefinitionId": "media_component",
      "templateKey": "Default",
      "parametersJson": "",
      "displayOrder": 4,
      "isVisible": true
    },
    {
      "componentDefinitionId": "content_component",
      "templateKey": "Default",
      "parametersJson": "",
      "displayOrder": 5,
      "isVisible": true
    },
    {
      "componentDefinitionId": "form_component",
      "templateKey": "Default",
      "parametersJson": "",
      "displayOrder": 6,
      "isVisible": true
    }
  ],
  "generatedViewPath": null,
  "performance": {
    "enableImageLazyLoading": true,
    "enableComponentLazyLoading": false,
    "maxComponentsPerPage": 20,
    "enableBundleOptimization": true,
    "imageQuality": 85,
    "enableWebpFormat": true
  },
  "cacheConfig": {
    "cacheDurationMinutes": 60,
    "cacheVaryByParams": ["theme", "culture"],
    "enableCDNCache": true,
    "cacheInvalidationTags": ["home", "global", "components"],
    "enableOutputCache": true,
    "cacheProfile": "HomePage"
  },
  "accessControl": {
    "requireAuthentication": false,
    "requiredRoles": [],
    "allowedUsers": [],
    "publishDate": "2024-03-01T00:00:00Z",
    "expiryDate": null,
    "allowedCountries": [],
    "enableGeoRestriction": false
  }
}

PageConfiguration 表定义：（字段需转TitleCase)
{
  "_id":"",
  "name":{
    "zh":"",
    "en":"",
    "ja":""
  },
  "config":"PageConfigContent对象的加密配置内容",
  "status": "Published",
  "publishDate": "2024-03-01T00:00:00Z",
  "createdAt": "2024-03-01T00:00:00Z",
  "updatedAt": "2024-03-15T08:30:00Z",
  "createdBy": "admin",
  "updatedBy": "admin"
}