# 招聘信息管理功能设计文档

## 概述

本文档详细设计招聘信息管理功能的后台管理系统，基于现有项目架构和日本企业网站的招聘信息（採用情報）管理需求。

## 功能模块

### 1. 职位管理 (JobPosition Management)

#### 1.1 职位列表页面
- **路由**: `/Admin/RecruitmentInfo` 或 `/Admin/Recruitment`
- **功能特性**:
  - 职位信息列表展示（支持分页、排序、筛选）
  - 按部门、职位类型、雇佣类型筛选
  - 按状态筛选（活跃/非活跃、已过期/未过期）
  - 批量操作（启用/禁用、批量删除）
  - 快速预览职位详情

#### 1.2 职位编辑功能
- **多语言支持**: 中文、日文、英文
- **编辑字段**:
  - 职位基本信息（标题、描述、要求、福利、工作地点）
  - 职位分类（部门、职位类型、雇佣类型、经验要求）
  - 薪资信息（薪资范围、货币单位）
  - 时间管理（发布日期、申请截止日期）
  - 状态控制（是否启用）

#### 1.3 职位状态管理
- **自动状态更新**: 过期职位自动设为非活跃状态
- **手动状态控制**: 管理员可手动启用/禁用职位
- **发布时间控制**: 支持定时发布功能

### 2. 员工访谈管理 (EmployeeInterview Management)

#### 2.1 访谈列表页面
- **功能特性**:
  - 员工访谈列表展示
  - 按部门、工作年限筛选
  - 按发布状态筛选
  - 显示顺序管理
  - 标签管理

#### 2.2 访谈编辑功能
- **多语言支持**: 员工姓名、职位、部门、访谈内容
- **编辑字段**:
  - 员工基本信息（姓名、职位、部门、工作年限）
  - 访谈内容（工作描述、公司印象）
  - 媒体管理（员工照片上传）
  - 标签管理（用于分类和搜索）
  - 显示顺序和发布状态

### 3. 招聘管理统一界面

#### 3.1 主页面结构
采用类似 `AdminNewsController` 的 Tab 页面结构：
- **职位管理 Tab**: 职位信息的增删改查
- **员工访谈 Tab**: 员工访谈的管理
- **统计分析 Tab**: 招聘数据统计和分析

#### 3.2 Modal 弹窗设计
- 职位信息编辑弹窗
- 员工访谈编辑弹窗
- 批量操作确认弹窗

## 技术实现方案

### 1. Controller 设计

#### 1.1 RecruitmentController
```csharp
[Authorize]
[Route("Admin/[controller]")]
[Route("{culture}/Admin/[controller]")]
public class RecruitmentController : BaseController
{
    private readonly JobPositionService _jobPositionService;
    private readonly EmployeeInterviewService _employeeInterviewService;
    // ... 其他依赖注入
}
```

#### 1.2 主要 Action 方法
- `Index()`: 主页面
- `GetJobPositions()`: 获取职位列表 (Ajax)
- `CreateJobPosition()`: 创建职位 (Ajax)
- `UpdateJobPosition()`: 更新职位 (Ajax)
- `DeleteJobPosition()`: 删除职位 (Ajax)
- `GetEmployeeInterviews()`: 获取访谈列表 (Ajax)
- `CreateEmployeeInterview()`: 创建访谈 (Ajax)
- `UpdateEmployeeInterview()`: 更新访谈 (Ajax)
- `DeleteEmployeeInterview()`: 删除访谈 (Ajax)

### 2. Service 层实现

#### 2.1 现有 Service 扩展
基于现有的 `JobPositionService` 和 `EmployeeInterviewService`，添加以下方法：
- 高级搜索和筛选
- 批量操作支持
- 统计数据计算
- 状态自动更新

#### 2.2 缓存策略
- 职位列表缓存（15分钟）
- 员工访谈缓存（30分钟）
- 统计数据缓存（1小时）

### 3. ViewModel 设计

#### 3.1 RecruitmentIndexViewModel
```csharp
public class RecruitmentIndexViewModel
{
    public List<JobPositionViewModel> JobPositions { get; set; }
    public List<EmployeeInterviewViewModel> EmployeeInterviews { get; set; }
    public RecruitmentStatisticsViewModel Statistics { get; set; }
    public FilterOptionsViewModel FilterOptions { get; set; }
}
```

#### 3.2 JobPositionViewModel
扩展现有的职位信息，添加：
- 状态显示字段
- 统计信息（申请数量等）
- 操作按钮状态

#### 3.3 EmployeeInterviewViewModel
扩展员工访谈信息，添加：
- 照片预览 URL
- 标签显示
- 排序控制

### 4. 前端实现

#### 4.1 页面结构
```html
<div class="recruitment-management">
    <!-- Tab 导航 -->
    <div class="tab-navigation">
        <button class="tab-button active" data-tab="job-positions">职位管理</button>
        <button class="tab-button" data-tab="employee-interviews">员工访谈</button>
        <button class="tab-button" data-tab="statistics">统计分析</button>
    </div>

    <!-- Tab 内容 -->
    <div class="tab-content">
        <!-- 职位管理 Tab -->
        <div id="job-positions-tab" class="tab-pane active">
            <!-- 筛选器 -->
            <div class="filters-section"></div>
            <!-- 操作按钮 -->
            <div class="actions-section"></div>
            <!-- 数据表格 -->
            <div class="data-table"></div>
            <!-- 分页 -->
            <div class="pagination-section"></div>
        </div>

        <!-- 员工访谈 Tab -->
        <div id="employee-interviews-tab" class="tab-pane"></div>

        <!-- 统计分析 Tab -->
        <div id="statistics-tab" class="tab-pane"></div>
    </div>
</div>
```

#### 4.2 JavaScript 功能
- Ajax 数据加载
- 实时筛选和搜索
- 拖拽排序（员工访谈）
- 批量选择和操作
- 表单验证和提交

### 5. 多语言资源

#### 5.1 AdminResource 扩展
添加招聘管理相关的多语言资源：
```xml
<!-- 中文 -->
<data name="RecruitmentManagement" xml:space="preserve">
    <value>招聘管理</value>
</data>
<data name="JobPositions" xml:space="preserve">
    <value>职位管理</value>
</data>
<data name="EmployeeInterviews" xml:space="preserve">
    <value>员工访谈</value>
</data>

<!-- 日文 -->
<data name="RecruitmentManagement" xml:space="preserve">
    <value>採用管理</value>
</data>
<data name="JobPositions" xml:space="preserve">
    <value>求人管理</value>
</data>
<data name="EmployeeInterviews" xml:space="preserve">
    <value>社員インタビュー</value>
</data>
```

## 实体定义调整建议

### 1. JobPosition 实体优化

#### 1.1 添加字段
```csharp
public class JobPosition
{
    // 现有字段...

    /// <summary>
    /// 申请人数统计
    /// </summary>
    public int ApplicationCount { get; set; }

    /// <summary>
    /// 职位优先级 - 用于首页推荐显示
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 是否推荐职位 - 在"採用情報"页面突出显示
    /// </summary>
    public bool IsFeatured { get; set; }

    /// <summary>
    /// 职位标签 - 用于更细粒度的分类
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 工作时间描述 - 如"9:00-17:30"、"フレックスタイム制"
    /// </summary>
    public string? WorkingHours { get; set; }

    /// <summary>
    /// 试用期描述 - 日本企业常见的试用期说明
    /// </summary>
    public string? ProbationPeriod { get; set; }

    /// <summary>
    /// 职位状态 - 使用统一的状态枚举
    /// </summary>
    public JobPositionStatus Status { get; set; } = JobPositionStatus.Draft;
}
```

#### 1.2 JobPositionLocaleFields 扩展
```csharp
public class JobPositionLocaleFields
{
    // 现有字段...

    /// <summary>
    /// 职位亮点 - 吸引求职者的关键特色
    /// </summary>
    public string? Highlights { get; set; }

    /// <summary>
    /// 工作环境描述 - 办公环境、团队氛围等
    /// </summary>
    public string? WorkEnvironment { get; set; }

    /// <summary>
    /// 发展机会 - 职业发展路径说明
    /// </summary>
    public string? CareerDevelopment { get; set; }

    /// <summary>
    /// 应聘方式 - 如何申请该职位
    /// </summary>
    public string? ApplicationMethod { get; set; }
}
```

### 2. EmployeeInterview 实体优化

#### 2.1 添加字段
```csharp
public class EmployeeInterview
{
    // 现有字段...

    /// <summary>
    /// 访谈类型 - 如新人访谈、资深员工访谈等
    /// </summary>
    public InterviewType InterviewType { get; set; }

    /// <summary>
    /// 访谈状态 - 草稿、已发布等
    /// </summary>
    public PublishStatus Status { get; set; } = PublishStatus.Draft;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 排序权重 - 用于首页推荐显示
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 是否推荐到首页
    /// </summary>
    public bool IsFeatured { get; set; }
}
```

### 3. 新增枚举定义

#### 3.1 在 CommonEnums.cs 中添加
```csharp
public enum JobPositionStatus
{
    Draft = 0,          // 草稿
    Active = 1,         // 活跃招聘
    Paused = 2,         // 暂停招聘
    Expired = 3,        // 已过期
    Filled = 4,         // 已招满
    Cancelled = 5       // 已取消
}

public enum InterviewType
{
    NewEmployee = 0,    // 新员工访谈
    Veteran = 1,        // 资深员工访谈
    Management = 2,     // 管理层访谈
    Technical = 3,      // 技术人员访谈
    Sales = 4,          // 销售人员访谈
    Other = 5           // 其他
}

public enum PublishStatus
{
    Draft = 0,          // 草稿
    Published = 1,      // 已发布
    Archived = 2        // 已归档
}
```

## 开发优先级

### Phase 1: 基础功能 (Week 1-2)
1. 创建 RecruitmentController 基础结构
2. 实现职位管理基本 CRUD 功能
3. 创建主管理页面和 Tab 结构
4. 实现基础的职位列表和编辑功能

### Phase 2: 高级功能 (Week 3-4)
1. 实现员工访谈管理功能
2. 添加筛选、搜索、排序功能
3. 实现批量操作功能
4. 添加状态自动管理

### Phase 3: 优化完善 (Week 5-6)
1. 实现统计分析功能
2. 完善多语言支持
3. 优化 UI/UX 体验
4. 添加缓存和性能优化

### Phase 4: 集成测试 (Week 7)
1. 功能集成测试
2. 多语言测试
3. 性能测试
4. 用户体验测试

## 总结

本设计文档提供了完整的招聘信息管理功能实现方案，充分考虑了日本企业网站的特殊需求和现有项目架构。通过分阶段的开发计划，可以逐步构建功能完善、用户友好的招聘管理系统。