
# 简化留言功能设计方案（仅后台管理）

## 1. 功能概述

仅实现后台留言管理功能：
- 留言列表（支持筛选、搜索）
- 留言详情查看
- 状态标识和更新
- 处理结果记录

## 2. 数据实体设计

### 2.1 留言实体 (MessageInquiry)

```csharp
// src/Model/Entities/Messages/MessageInquiry.cs
public class MessageInquiry
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;

    // 基本信息
    public string ContactName { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string? ContactPhone { get; set; }
    public string? CompanyName { get; set; }
    public string? JobTitle { get; set; }

    // 留言内容
    public string Message { get; set; } = string.Empty;

    // 分类和状态
    public MessageType Type { get; set; }
    public MessageStatus Status { get; set; } = MessageStatus.New;

    // 来源信息
    public string? SourcePage { get; set; }
    public string? ReferrerUrl { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string? UserAgent { get; set; }

    // 时间记录
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    // 处理结果
    public string DealResult { get; set; } = string.Empty;

    // 管理标识
    public bool IsRead { get; set; } = false;
    public bool IsImportant { get; set; } = false;
    public string? ProcessedByUserId { get; set; }
    public DateTime? ProcessedAt { get; set; }
}
```

### 2.2 枚举定义

```csharp
// 添加到 src/Model/Entities/Enums/CommonEnums.cs

public enum MessageType
{
    GeneralInquiry = 0,     // 一般咨询
    ProductInquiry = 1,     // 产品咨询
    ServiceInquiry = 2,     // 服务咨询
    TechnicalSupport = 3,   // 技术支持
    BusinessCooperation = 4, // 商务合作
    PartnershipInquiry = 5, // 合作伙伴咨询
    MediaInquiry = 6,       // 媒体咨询
    CareerInquiry = 7,      // 招聘咨询
    Complaint = 8,          // 投诉建议
    Other = 9               // 其他
}

public enum MessageStatus
{
    New = 0,                // 新留言
    InProgress = 1,         // 处理中
    WaitingForResponse = 2, // 等待回复
    WaitingForCustomer = 3, // 等待客户反馈
    Resolved = 4,           // 已解决
    Closed = 5,             // 已关闭
    Spam = 6                // 垃圾邮件
}
```

## 3. 服务层设计

### 3.1 MessageInquiryService

```csharp
// src/Service/Messages/MessageInquiryService.cs
public class MessageInquiryService : MongoBaseService<MessageInquiry>
{
    public MessageInquiryService(IMongoDatabase database)
        : base(database, "messageinquiries") { }

    // 获取分页留言列表
    public async Task<PagedResult<MessageInquiry>> GetPagedAsync(int page, int pageSize,
        MessageStatus? status = null, string? search = null)
    {
        var filterBuilder = Builders<MessageInquiry>.Filter;
        var filter = filterBuilder.Empty;

        if (status.HasValue)
        {
            filter = filterBuilder.And(filter, filterBuilder.Eq(m => m.Status, status.Value));
        }

        if (!string.IsNullOrEmpty(search))
        {
            var searchFilter = filterBuilder.Or(
                filterBuilder.Regex(m => m.ContactName, new BsonRegularExpression(search, "i")),
                filterBuilder.Regex(m => m.ContactEmail, new BsonRegularExpression(search, "i")),
                filterBuilder.Regex(m => m.Message, new BsonRegularExpression(search, "i")),
                filterBuilder.Regex(m => m.CompanyName, new BsonRegularExpression(search, "i"))
            );
            filter = filterBuilder.And(filter, searchFilter);
        }

        var total = await _collection.CountDocumentsAsync(filter);
        var messages = await _collection
            .Find(filter)
            .Sort(Builders<MessageInquiry>.Sort.Descending(m => m.CreatedAt))
            .Skip((page - 1) * pageSize)
            .Limit(pageSize)
            .ToListAsync();

        return new PagedResult<MessageInquiry>
        {
            Items = messages,
            Total = (int)total,
            Page = page,
            PageSize = pageSize
        };
    }

    // 更新状态
    public async Task<bool> UpdateStatusAsync(string id, MessageStatus status, string? userId = null)
    {
        var updateBuilder = Builders<MessageInquiry>.Update
            .Set(m => m.Status, status)
            .Set(m => m.UpdatedAt, DateTime.UtcNow);

        if (status != MessageStatus.New)
        {
            updateBuilder = updateBuilder.Set(m => m.IsRead, true);
        }

        if (!string.IsNullOrEmpty(userId))
        {
            updateBuilder = updateBuilder
                .Set(m => m.ProcessedByUserId, userId)
                .Set(m => m.ProcessedAt, DateTime.UtcNow);
        }

        var result = await _collection.UpdateOneAsync(
            Builders<MessageInquiry>.Filter.Eq(m => m.Id, id), updateBuilder);
        return result.ModifiedCount > 0;
    }

    // 更新处理结果
    public async Task<bool> UpdateDealResultAsync(string id, string dealResult, string? userId = null)
    {
        var updateBuilder = Builders<MessageInquiry>.Update
            .Set(m => m.DealResult, dealResult)
            .Set(m => m.UpdatedAt, DateTime.UtcNow);

        if (!string.IsNullOrEmpty(userId))
        {
            updateBuilder = updateBuilder
                .Set(m => m.ProcessedByUserId, userId)
                .Set(m => m.ProcessedAt, DateTime.UtcNow);
        }

        var result = await _collection.UpdateOneAsync(
            Builders<MessageInquiry>.Filter.Eq(m => m.Id, id), updateBuilder);
        return result.ModifiedCount > 0;
    }

    // 标记重要
    public async Task<bool> ToggleImportantAsync(string id)
    {
        var message = await GetByIdAsync(id);
        if (message == null) return false;

        var update = Builders<MessageInquiry>.Update
            .Set(m => m.IsImportant, !message.IsImportant)
            .Set(m => m.UpdatedAt, DateTime.UtcNow);

        var result = await _collection.UpdateOneAsync(
            Builders<MessageInquiry>.Filter.Eq(m => m.Id, id), update);
        return result.ModifiedCount > 0;
    }

    // 获取统计信息
    public async Task<MessageStatistics> GetStatisticsAsync()
    {
        var total = await _collection.CountDocumentsAsync(FilterDefinition<MessageInquiry>.Empty);
        var newCount = await _collection.CountDocumentsAsync(
            Builders<MessageInquiry>.Filter.Eq(m => m.Status, MessageStatus.New));
        var todayCount = await _collection.CountDocumentsAsync(
            Builders<MessageInquiry>.Filter.Gte(m => m.CreatedAt, DateTime.Today));

        return new MessageStatistics
        {
            Total = (int)total,
            New = (int)newCount,
            Today = (int)todayCount
        };
    }
}

public class MessageStatistics
{
    public int Total { get; set; }
    public int New { get; set; }
    public int Today { get; set; }
}

public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int Total { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)Total / PageSize);
}
```

## 4. 控制器设计

```csharp
// src/Web/Controllers/Admin/AdminMessageController.cs
[Route("admin/messages")]
public class AdminMessageController : BaseController
{
    private readonly MessageInquiryService _messageService;

    public AdminMessageController(MessageInquiryService messageService)
    {
        _messageService = messageService;
    }

    [HttpGet]
    public async Task<IActionResult> Index(int page = 1, MessageStatus? status = null, string? search = null)
    {
        const int pageSize = 20;
        var messages = await _messageService.GetPagedAsync(page, pageSize, status, search);
        var statistics = await _messageService.GetStatisticsAsync();

        var viewModel = new AdminMessageIndexViewModel
        {
            Messages = messages,
            Statistics = statistics,
            CurrentStatus = status,
            SearchQuery = search
        };

        return View(viewModel);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Details(string id)
    {
        var message = await _messageService.GetByIdAsync(id);
        if (message == null) return NotFound();

        // 自动标记为已读
        if (!message.IsRead)
        {
            var userId = GetCurrentUserId();
            await _messageService.UpdateStatusAsync(id, MessageStatus.InProgress, userId);
        }

        return View(message);
    }

    [HttpPost("{id}/status")]
    public async Task<IActionResult> UpdateStatus(string id, MessageStatus status)
    {
        var userId = GetCurrentUserId();
        var success = await _messageService.UpdateStatusAsync(id, status, userId);

        if (success)
            TempData["Success"] = "状态更新成功";
        else
            TempData["Error"] = "状态更新失败";

        return RedirectToAction("Details", new { id });
    }

    [HttpPost("{id}/deal-result")]
    public async Task<IActionResult> UpdateDealResult(string id, string dealResult)
    {
        var userId = GetCurrentUserId();
        var success = await _messageService.UpdateDealResultAsync(id, dealResult, userId);

        if (success)
            TempData["Success"] = "处理结果已保存";
        else
            TempData["Error"] = "保存失败";

        return RedirectToAction("Details", new { id });
    }

    [HttpPost("{id}/toggle-important")]
    public async Task<IActionResult> ToggleImportant(string id)
    {
        var success = await _messageService.ToggleImportantAsync(id);
        return RedirectToAction("Details", new { id });
    }
}
```

## 5. 视图模型

```csharp
// src/Web/ViewModels/Admin/AdminMessageIndexViewModel.cs
public class AdminMessageIndexViewModel
{
    public PagedResult<MessageInquiry> Messages { get; set; } = new();
    public MessageStatistics Statistics { get; set; } = new();
    public MessageStatus? CurrentStatus { get; set; }
    public string? SearchQuery { get; set; }
}
```

## 6. 服务注册

```csharp
// 在 Program.cs 中添加
builder.Services.AddScoped<MessageInquiryService>();
```

这个简化设计专注于后台管理，包含：
- 留言列表（支持状态筛选和搜索）
- 留言详情查看
- 状态更新（新留言→处理中→等待回复→已解决→已关闭）
- 重要标记
- 处理结果记录和编辑
