● 非常好的想法！使用物理文件方式存储标签页内容是一个优秀的架构设计。让我分析一下这个方案的优势和具体实现建议：

  🎯 方案优势

  1. 维护性优势

  - ✅ 独立修改：每个标签页是独立文件，修改时不影响其他部分
  - ✅ 版本控制友好：Git可以精确跟踪每个标签页的变更历史
  - ✅ 代码组织清晰：按功能模块分离，便于理解和维护
  - ✅ 热更新支持：修改cshtml文件后立即生效，无需重启应用

  2. 团队协作优势

  - ✅ 并行开发：不同开发者可以同时开发不同标签页
  - ✅ 冲突减少：避免多人同时修改同一个大文件的合并冲突
  - ✅ 责任分明：每个标签页可以分配给专门的开发者

  3. 复用性和扩展性

  - ✅ 组件化：标签页内容可以在其他地方复用
  - ✅ A/B测试：可以轻松创建不同版本的标签页
  - ✅ 动态配置：可以根据配置选择加载不同的标签页文件

  🏗️ 推荐的目录结构

  src/Web/Views/Admin/CompanyInfo/
  ├── Index.cshtml                          # 主页面
  ├── Partials/                            # 标签页内容文件
  │   ├── _BasicInfoTab.cshtml             # 基本信息标签页
  │   ├── _CompanyHistoryTab.cshtml        # 企业历史标签页
  │   ├── _ExecutiveOrganizationTab.cshtml # 高管组织标签页
  │   ├── _ContactInfoTab.cshtml           # 联系信息标签页
  │   ├── _CSRActivitiesTab.cshtml         # CSR活动标签页
  │   └── _InvestorRelationsTab.cshtml     # 投资者关系标签页
  └── Components/                          # 共用组件
      ├── _CompanyHistoryModal.cshtml
      └── _ExecutiveModal.cshtml

  💻 Controller 实现方案

  [Route("Admin/CompanyInfo")]
  public class CompanyInfoController : BaseController
  {
      private readonly IWebHostEnvironment _env;
      private readonly IViewRenderService _viewRenderService; // 自定义服务

      // 主页面 - 只加载基本信息
      [HttpGet]
      public async Task<IActionResult> Index()
      {
          var model = new CompanyInfoViewModel
          {
              BasicInfo = await GetBasicInfoAsync()
          };
          return View(model);
      }

      // 懒加载标签页内容
      [HttpGet("tab/{tabName}")]
      public async Task<IActionResult> GetTabContent(string tabName)
      {
          try
          {
              var partialViewName = GetPartialViewName(tabName);
              var model = await GetTabDataAsync(tabName);

              // 渲染部分视图为HTML字符串
              var html = await _viewRenderService.RenderPartialViewAsync(
                  $"~/Views/Admin/CompanyInfo/Partials/{partialViewName}",
                  model,
                  ViewData,
                  TempData
              );

              return Json(new { success = true, html = html });
          }
          catch (Exception ex)
          {
              _logger.LogError(ex, "Error loading tab content: {TabName}", tabName);
              return Json(new {
                  success = false,
                  message = _adminResource["LoadTabError"]
              });
          }
      }

      private string GetPartialViewName(string tabName)
      {
          return tabName switch
          {
              "company-history" => "_CompanyHistoryTab.cshtml",
              "executive-organization" => "_ExecutiveOrganizationTab.cshtml",
              "contact-info" => "_ContactInfoTab.cshtml",
              "csr-activities" => "_CSRActivitiesTab.cshtml",
              "investor-relations" => "_InvestorRelationsTab.cshtml",
              _ => throw new ArgumentException($"Invalid tab name: {tabName}")
          };
      }

      private async Task<object> GetTabDataAsync(string tabName)
      {
          return tabName switch
          {
              "company-history" => await GetCompanyHistoryDataAsync(),
              "executive-organization" => await GetExecutiveOrganizationDataAsync(),
              "contact-info" => await GetContactInfoDataAsync(),
              "csr-activities" => await GetCSRActivitiesDataAsync(),
              "investor-relations" => await GetInvestorRelationsDataAsync(),
              _ => new { }
          };
      }
  }

  🛠️ IViewRenderService 服务实现

  public interface IViewRenderService
  {
      Task<string> RenderPartialViewAsync<TModel>(
          string partialViewName, 
          TModel model,
          ViewDataDictionary viewData = null,
          ITempDataDictionary tempData = null
      );
  }

  public class ViewRenderService : IViewRenderService
  {
      private readonly IRazorViewEngine _razorViewEngine;
      private readonly ITempDataProvider _tempDataProvider;
      private readonly IServiceProvider _serviceProvider;

      public ViewRenderService(
          IRazorViewEngine razorViewEngine,
          ITempDataProvider tempDataProvider,
          IServiceProvider serviceProvider)
      {
          _razorViewEngine = razorViewEngine;
          _tempDataProvider = tempDataProvider;
          _serviceProvider = serviceProvider;
      }

      public async Task<string> RenderPartialViewAsync<TModel>(
          string partialViewName, 
          TModel model,
          ViewDataDictionary viewData = null,
          ITempDataDictionary tempData = null)
      {
          var actionContext = new ActionContext(
              new DefaultHttpContext { RequestServices = _serviceProvider },
              new RouteData(),
              new ActionDescriptor()
          );

          using var sw = new StringWriter();
          var viewResult = _razorViewEngine.FindView(actionContext, partialViewName, false);

          if (!viewResult.Success)
          {
              throw new FileNotFoundException($"View '{partialViewName}' not found");
          }

          var viewDictionary = new ViewDataDictionary<TModel>(
              new EmptyModelMetadataProvider(),
              new ModelStateDictionary())
          {
              Model = model
          };

          // 合并传入的ViewData
          if (viewData != null)
          {
              foreach (var kvp in viewData)
              {
                  viewDictionary[kvp.Key] = kvp.Value;
              }
          }

          var viewContext = new ViewContext(
              actionContext,
              viewResult.View,
              viewDictionary,
              tempData ?? new TempDataDictionary(actionContext.HttpContext, _tempDataProvider),
              sw,
              new HtmlHelperOptions()
          );

          await viewResult.View.RenderAsync(viewContext);
          return sw.ToString();
      }
  }

  📄 标签页文件示例

  _CompanyHistoryTab.cshtml

  @model List<CompanyHistoryItemViewModel>

  <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["CompanyHistory"]</h3>
      <button onclick="openHistoryModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 
  hover:bg-primary-700">
          <i class="fas fa-plus mr-2"></i>
          @AdminRes["AddHistoryEvent"]
      </button>
  </div>

  <!-- History Table -->
  <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <!-- 现有的表格内容 -->
      </table>
  </div>

  🔧 JavaScript 更新

  // 更新的switchTab函数
  async function switchTab(tabName) {
      // 隐藏所有标签页
      document.querySelectorAll('.tab-content').forEach(tab => {
          tab.classList.add('hidden');
      });

      updateTabButtons(tabName);

      if (loadedTabs.has(tabName)) {
          document.getElementById(`${tabName}-tab`).classList.remove('hidden');
          updateURL(tabName);
          return;
      }

      showTabLoading(tabName);

      try {
          const response = await fetch(`@Html.MultilingualUrl("tab", "CompanyInfo")/${tabName}`);
          const result = await response.json();

          if (result.success) {
              // 直接使用返回的HTML
              document.getElementById(`${tabName}-tab`).innerHTML = result.html;
              loadedTabs.add(tabName);

              hideTabLoading(tabName);
              document.getElementById(`${tabName}-tab`).classList.remove('hidden');

              initializeTabScripts(tabName);
          } else {
              showTabError(tabName, result.message);
          }
      } catch (error) {
          console.error('Error loading tab:', error);
          showTabError(tabName);
      }

      updateURL(tabName);
  }

  📋 服务注册

  // Program.cs 中注册服务
  builder.Services.AddScoped<IViewRenderService, ViewRenderService>();

  🎯 方案总结

  这个方案的核心优势：

  1. 物理文件存储 - 每个标签页独立的 .cshtml 文件
  2. 懒加载机制 - 按需通过API获取渲染后的HTML
  3. 模块化设计 - 清晰的目录结构和职责分离
  4. 易于维护 - 独立修改、版本控制、团队协作友好