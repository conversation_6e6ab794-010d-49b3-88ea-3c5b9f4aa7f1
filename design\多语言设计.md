多语言功能实现完成

  已成功完成ASP.NET Core应用程序的多语言支持，实现了以下功能：

  ✅ 已实现的功能

  1. 配置管理
    - 支持的语言从 appsettings.json 的 SupportedLanguages 读取
    - 包含语言代码、名称、区域设置和表情符号
  2. ASP.NET Core国际化
    - 配置了标准的ASP.NET Core localization中间件
    - 支持URL路径语言检测：/{culture}/{controller}/{action}
    - 默认语言使用根路径：/，非默认语言使用语言代码前缀
  3. URL路由
    - 默认语言：/ (如 /home/<USER>
    - 其他语言：/{code}/ (如 /en/home/<USER>/zh/news/details/1)
    - 自动语言约束验证
  4. 控制器增强
    - BaseController 提供多语言支持方法
    - 自动检测当前语言和区域设置
    - 提供多语言URL生成方法
    - 所有控制器已更新支持多语言构造函数
  5. 服务集成
    - SiteSettingsService 与配置文件同步
    - 站点设置页面显示支持的语言列表
    - 支持语言配置的管理界面
  6. TagHelper组件
    - 创建了 <language-switcher> TagHelper
    - 支持下拉菜单和列表两种显示模式
    - 自动生成正确的多语言URL
  7. 项目兼容性
    - 所有项目文件已更新为.NET 8.0兼容
    - 修复了编译错误，构建成功

  📁 新增/修改的文件

  - src/Model/Configuration/SupportedLanguage.cs - 语言配置模型
  - src/Web/Providers/RouteDataRequestCultureProvider.cs - 路由语言检测
  - src/Web/TagHelpers/LanguageSwitcherTagHelper.cs - 语言切换组件
  - src/Web/Program.cs - 国际化中间件配置
  - src/Web/Controllers/Base/BaseController.cs - 多语言基础控制器
  - src/Service/Settings/SiteSettingsService.cs - 语言配置服务
  - 所有控制器已更新支持多语言

  🔧 使用方法

  在视图中使用语言切换器：
  <language-switcher supported-languages="@ViewBag.SupportedLanguages" 
                    css-class="lang-switcher" 
                    show-flags="true" 
                    show-names="true" 
                    dropdown="false" />

  在控制器中获取当前语言：
  var currentLanguage = GetCurrentLanguage();
  var currentLanguageCode = GetCurrentLanguageCode();
  var isDefault = IsDefaultLanguage();

  生成多语言URL：
  var url = GenerateLocalizedUrl("Details", "News", "en", new { id = 1 });

  当前配置支持的语言：
  - 🇯🇵 日本語 (ja-JP) - 默认语言
  - 🇺🇸 English (en-US)
  - 🇨🇳 中文 (zh-CN)



  - 添加了扩展方法命名空间引用，现在所有视图都可以使用多语言 URL 扩展方法

  使用示例

  在 cshtml 视图中的用法：

  <!-- 生成多语言链接 -->
  <a href="@Html.MultilingualUrl("Index", "Home", "en")">English</a>
  <a href="@Html.MultilingualUrl("About", "Company", "ja")">日本語版について</a>

  <!-- 当前页面的其他语言版本 -->
  <a href="@Html.CurrentPageMultilingualUrl("zh")">中文版</a>

  <!-- 使用语言切换器组件 -->
  <language-switcher dropdown="true" show-flags="true"></language-switcher>

  URL 生成结果：

  根据配置：
  {
    "Code": "zh", "BaseUrl": "/"           // → /Home/Index
    "Code": "en", "BaseUrl": "/en/"        // → /en/Home/Index  
    "Code": "ja", "BaseUrl": "/ja/"        // → /ja/Home/Index
  }

  未来可扩展为子域名：
  {
    "Code": "zh", "BaseUrl": "https://www.example.com/"    // → https://www.example.com/Home/Index
    "Code": "en", "BaseUrl": "https://en.example.com/"     // → https://en.example.com/Home/Index
    "Code": "ja", "BaseUrl": "https://ja.example.com/"     // → https://ja.example.com/Home/Index  
  }

  这样的设计非常灵活，可以支持各种多语言 URL 策略，从简单的路径前缀到完全不同的子域名或域名。