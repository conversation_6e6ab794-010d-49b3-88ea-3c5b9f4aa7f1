多语言资源自动化转换为JavaScript的方案：

  方案设计

  1. MSBuild脚本自动生成JS资源文件

  创建PowerShell脚本: Scripts/GenerateJsResources.ps1
  - 扫描Resources/目录下的所有.resx文件
  - 解析每个资源文件，提取键值对
  - 按语言和资源类型生成对应的JavaScript文件
  - 输出到wwwroot/js/resources/目录

  生成的文件结构:
  wwwroot/js/resources/
  ├── admin.zh.js      // AdminResource 默认语言
  ├── admin.en.js      // AdminResource 英文
  ├── admin.ja.js      // AdminResource 日文
  ├── shared.zh.js     // SharedResource 默认语言
  ├── shared.en.js     // SharedResource 英文
  ├── shared.ja.js     // SharedResource 日文
  ├── form.zh.js       // FormResource 默认语言
  ├── form.en.js       // FormResource 英文
  └── form.ja.js       // FormResource 日文

  JavaScript文件格式:
  window.Resources = window.Resources || {};
  window.Resources.Admin = {
      "BackendManagement": "后台管理",
      "Dashboard": "仪表板",
      // ... 其他资源
  };

  2. 集成到构建流程

  修改MlSoft.Sites.Web.csproj:
  - 添加构建前任务，调用PowerShell脚本
  - 监听.resx文件变化，触发重新生成
  - 支持Debug和Release模式

  <Target Name="GenerateJsResources" BeforeTargets="Build">
      <Exec Command="powershell -ExecutionPolicy Bypass -File Scripts/GenerateJsResources.ps1" />
  </Target>

  3. 在Admin Layout中动态引入

  修改_AdminLayout.cshtml:
  - 获取当前语言代码
  - 动态生成对应的JavaScript资源文件引用
  - 放在</body>之前加载

  @{
      var currentLang = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
  }

  <!-- 资源文件 -->
  <script src="~/js/resources/shared.@(currentLang).js" asp-append-version="true"></script>
  <script src="~/js/resources/admin.@(currentLang).js" asp-append-version="true"></script>
  <script src="~/js/resources/form.@(currentLang).js" asp-append-version="true"></script>

  4. JavaScript中的使用方式

  在管理页面的JavaScript中:
  // 替代之前在CSHTML中写的变量
  console.log(Resources.Admin.BackendManagement);
  console.log(Resources.Form.ValidationRequired);

  // 支持按资源类型访问
  alert(Resources.Shared.ErrorMessage);

  方案优点

  1. 自动化: 构建时自动生成，无需手动维护
  2. 实时更新: 修改.resx文件后自动重新生成
  3. 按需加载: 只加载当前语言的资源
  4. 类型安全: 可以进一步添加TypeScript声明文件
  5. 缓存友好: 生成的JS文件支持版本控制

  后续扩展

  - 可以添加TypeScript声明文件生成
  - 支持嵌套资源结构
  - 添加资源文件压缩和合并
  - 支持按页面或模块拆分资源文件