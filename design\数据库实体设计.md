# 日本企业官网模板系统 - 数据库实体设计

基于日本企业官网特点和动态组件架构方案，设计以下数据库实体，支持多语言存储和MongoDB数据库。

**注意：本设计为单企业架构，一个模板网站系统对应一个企业，无需多租户支持。**

## 1. 企业基础信息实体

### 1.1 企业信息 (Company)
```csharp
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

public class Company
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    [Required]
    public string CompanyCode { get; set; } // 企业唯一标识
    
    // 多语言字段
    public Dictionary<string, CompanyLocaleFields> Locale { get; set; } = new();
    
    public DateTime EstablishedDate { get; set; } // 成立日期
    public string RegistrationNumber { get; set; } // 注册号
    public decimal? Capital { get; set; } // 注册资本
    public string Currency { get; set; } // 货币单位
    
    public ContactInfo ContactInfo { get; set; } // 联系信息
    public List<CompanyLocation> Locations { get; set; } // 据点信息
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}
```

### 1.2 多语言存储结构

#### 1.2.1 多语言字段定义实体
```csharp
// 企业基础信息多语言字段
public class CompanyLocaleFields
{
    public string CompanyName { get; set; }
    public string CompanyDescription { get; set; }
    public string Mission { get; set; }
    public string Vision { get; set; }
    public string Address { get; set; }
}

// 管理层信息多语言字段
public class ExecutiveLocaleFields
{
    public string Name { get; set; }
    public string Position { get; set; }
    public string Biography { get; set; }
    public string Message { get; set; }
}

// 组织架构多语言字段
public class OrganizationLocaleFields
{
    public string DepartmentName { get; set; }
    public string Description { get; set; }
}

// 企业历史多语言字段
public class CompanyHistoryLocaleFields
{
    public string EventTitle { get; set; }
    public string EventDescription { get; set; }
}

// 业务部门多语言字段
public class BusinessDivisionLocaleFields
{
    public string DivisionName { get; set; }
    public string Description { get; set; }
    public string Services { get; set; }
}

// 产品服务多语言字段
public class ProductServiceLocaleFields
{
    public string ProductName { get; set; }
    public string Description { get; set; }
    public string Features { get; set; }
    public string Specifications { get; set; }
}

// 新闻公告多语言字段
public class NewsAnnouncementLocaleFields
{
    public string Title { get; set; }
    public string Summary { get; set; }
    public string Content { get; set; }
    public string[] Tags { get; set; }
}

// 财务报告多语言字段
public class FinancialReportLocaleFields
{
    public string Title { get; set; }
    public string Summary { get; set; }
}

// 股东大会多语言字段
public class ShareholderMeetingLocaleFields
{
    public string MeetingTitle { get; set; }
    public string Agenda { get; set; }
    public string Location { get; set; }
}

// CSR活动多语言字段
public class CSRActivityLocaleFields
{
    public string Title { get; set; }
    public string Description { get; set; }
    public string Impact { get; set; }
}

// 招聘职位多语言字段
public class JobPositionLocaleFields
{
    public string JobTitle { get; set; }
    public string JobDescription { get; set; }
    public string Requirements { get; set; }
    public string Benefits { get; set; }
    public string WorkLocation { get; set; }
}

// 员工访谈多语言字段
public class EmployeeInterviewLocaleFields
{
    public string EmployeeName { get; set; }
    public string Position { get; set; }
    public string Department { get; set; }
    public string InterviewContent { get; set; }
    public string WorkDescription { get; set; }
    public string CompanyImpression { get; set; }
}

// 组件定义多语言字段
public class ComponentDefinitionLocaleFields
{
    public string ComponentName { get; set; }
    public string Description { get; set; }
}

// 组件模板多语言字段
public class ComponentTemplateLocaleFields
{
    public string TemplateName { get; set; }
    public string Description { get; set; }
}

// 页面配置多语言字段
public class PageConfigurationLocaleFields
{
    public string PageTitle { get; set; }
    public string MetaDescription { get; set; }
}

// 网站设置多语言字段
public class SiteSettingsLocaleFields
{
    public string SiteTitle { get; set; }
    public string SiteDescription { get; set; }
}

// SEO设置多语言字段
public class SEOSettingsLocaleFields
{
    public string MetaTitle { get; set; }
    public string MetaDescription { get; set; }
    public string MetaKeywords { get; set; }
    public string OgTitle { get; set; }
    public string OgDescription { get; set; }
}

// 文档多语言字段
public class DocumentLocaleFields
{
    public string DocumentName { get; set; }
}
```

#### 1.2.2 多语言扩展方法
```csharp
public static class LocaleExtensions
{
    // 获取指定语言的值
    public static T GetLocale<T>(this Dictionary<string, T> locale, string languageCode) where T : class, new()
    {
        if (locale.TryGetValue(languageCode, out var value))
            return value;
        
        // 回退策略：ja -> en -> zh -> 第一个可用值
        var fallbackOrder = new[] { "ja", "en", "zh" };
        foreach (var fallback in fallbackOrder)
        {
            if (locale.TryGetValue(fallback, out var fallbackValue))
                return fallbackValue;
        }
        
        return locale.Values.FirstOrDefault() ?? new T();
    }
    
    // 设置指定语言的值
    public static void SetLocale<T>(this Dictionary<string, T> locale, string languageCode, T value)
    {
        locale[languageCode] = value;
    }
    
    // 获取所有可用语言
    public static IEnumerable<string> GetAvailableLanguages<T>(this Dictionary<string, T> locale)
    {
        return locale.Keys;
    }
}
```

### 1.3 联系信息 (ContactInfo)
```csharp
public class ContactInfo
{
    public string Phone { get; set; }
    public string Fax { get; set; }
    public string Email { get; set; }
    public string Website { get; set; }
    public string PostalCode { get; set; }
    public GeoLocation Location { get; set; }
}

// 联系信息多语言字段
public class ContactInfoLocaleFields
{
    public string Address { get; set; }
}

public class GeoLocation
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
}
```

### 1.4 企业据点 (CompanyLocation)
```csharp
public class CompanyLocation
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    // 多语言字段
    public Dictionary<string, CompanyLocationLocaleFields> Locale { get; set; } = new();
    
    public LocationType Type { get; set; } // 总部、分部、工厂等
    public ContactInfo ContactInfo { get; set; }
    public DateTime CreatedAt { get; set; }
}

// 企业据点多语言字段
public class CompanyLocationLocaleFields
{
    public string LocationName { get; set; }
}

public enum LocationType
{
    Headquarters, // 总部
    Branch, // 分部
    Factory, // 工厂
    Office, // 办事处
    Laboratory, // 研究所
    Warehouse // 仓库
}
```

## 2. 组织架构实体

### 2.1 管理层信息 (Executive)
```csharp
public class Executive
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    // 多语言字段
    public Dictionary<string, ExecutiveLocaleFields> Locale { get; set; } = new();
    
    public string PhotoUrl { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsPresident { get; set; } // 是否社长
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}
```

### 2.2 组织架构 (OrganizationStructure)
```csharp
public class OrganizationStructure
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string ParentDepartmentId { get; set; } // 上级部门
    
    // 多语言字段
    public Dictionary<string, OrganizationLocaleFields> Locale { get; set; } = new();
    
    public int Level { get; set; } // 层级
    public int DisplayOrder { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}
```

## 3. 企业历史实体

### 3.1 企业沿革 (CompanyHistory)
```csharp
public class CompanyHistory
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public DateTime EventDate { get; set; }
    public HistoryEventType EventType { get; set; }
    
    // 多语言字段
    public Dictionary<string, CompanyHistoryLocaleFields> Locale { get; set; } = new();
    
    public string ImageUrl { get; set; }
    public int DisplayOrder { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}

public enum HistoryEventType
{
    Establishment, // 成立
    Expansion, // 扩张
    ProductLaunch, // 产品发布
    Acquisition, // 收购
    Partnership, // 合作
    Award, // 获奖
    Milestone, // 里程碑
    Other // 其他
}
```

## 4. 业务相关实体

### 4.1 业务部门 (BusinessDivision)
```csharp
public class BusinessDivision
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    // 多语言字段
    public Dictionary<string, BusinessDivisionLocaleFields> Locale { get; set; } = new();
    
    public string ImageUrl { get; set; }
    public string IconUrl { get; set; }
    public int DisplayOrder { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}
```

### 4.2 产品服务 (ProductService)
```csharp
public class ProductService
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string BusinessDivisionId { get; set; }
    
    // 多语言字段
    public Dictionary<string, ProductServiceLocaleFields> Locale { get; set; } = new();
    
    public List<string> ImageUrls { get; set; }
    public List<ProductDocument> Documents { get; set; } // 资料下载
    public ProductCategory Category { get; set; }
    
    public decimal? Price { get; set; }
    public string Currency { get; set; }
    public int DisplayOrder { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}

public class ProductDocument
{
    // 多语言字段
    public Dictionary<string, DocumentLocaleFields> Locale { get; set; } = new();
    
    public string FileUrl { get; set; }
    public string FileType { get; set; } // PDF, DOC, etc.
    public long FileSize { get; set; }
    public DateTime UploadDate { get; set; }
}

public enum ProductCategory
{
    Product, // 产品
    Service, // 服务
    Solution, // 解决方案
    Technology // 技术
}
```

## 5. 新闻公告实体

### 5.1 新闻公告 (NewsAnnouncement)
```csharp
public class NewsAnnouncement
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    // 多语言字段
    public Dictionary<string, NewsAnnouncementLocaleFields> Locale { get; set; } = new();
    
    public NewsType Type { get; set; }
    public DateTime PublishDate { get; set; }
    public string AuthorId { get; set; }
    
    public List<string> ImageUrls { get; set; }
    public string ThumbnailUrl { get; set; }
    
    public bool IsPublished { get; set; } = true;
    public bool IsFeatured { get; set; } = false; // 是否置顶
    public int ViewCount { get; set; } = 0;
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public enum NewsType
{
    CompanyNews, // 企业新闻
    PressRelease, // 新闻发布
    ProductUpdate, // 产品更新
    Event, // 活动
    MediaCoverage, // 媒体报道
    Announcement // 公告
}
```

## 6. 投资者关系实体

### 6.1 财务报告 (FinancialReport)
```csharp
public class FinancialReport
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public ReportType Type { get; set; }
    public ReportPeriod Period { get; set; }
    public int Year { get; set; }
    public int? Quarter { get; set; } // 季度（如果是季报）
    
    // 多语言字段
    public Dictionary<string, FinancialReportLocaleFields> Locale { get; set; } = new();
    
    public string ReportFileUrl { get; set; } // PDF文件
    public DateTime PublishDate { get; set; }
    
    // 关键财务数据
    public decimal? Revenue { get; set; } // 营收
    public decimal? NetIncome { get; set; } // 净利润
    public decimal? TotalAssets { get; set; } // 总资产
    public string Currency { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public bool IsPublished { get; set; } = true;
}

public enum ReportType
{
    AnnualReport, // 年报
    QuarterlyReport, // 季报
    EarningsReport, // 决算短信
    SecuritiesReport // 有价证券报告书
}

public enum ReportPeriod
{
    Annual, // 年度
    Q1, Q2, Q3, Q4 // 季度
}
```

### 6.2 股东大会 (ShareholderMeeting)
```csharp
public class ShareholderMeeting
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public DateTime MeetingDate { get; set; }
    
    // 多语言字段
    public Dictionary<string, ShareholderMeetingLocaleFields> Locale { get; set; } = new();
    
    public List<MeetingDocument> Documents { get; set; }
    public MeetingStatus Status { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class MeetingDocument
{
    // 多语言字段
    public Dictionary<string, DocumentLocaleFields> Locale { get; set; } = new();
    
    public string FileUrl { get; set; }
    public DocumentType Type { get; set; }
    public DateTime UploadDate { get; set; }
}

public enum DocumentType
{
    Notice, // 通知
    Agenda, // 议程
    Minutes, // 会议纪要
    Resolution, // 决议
    Other // 其他
}

public enum MeetingStatus
{
    Scheduled, // 已安排
    InProgress, // 进行中
    Completed, // 已完成
    Cancelled // 已取消
}
```

## 7. CSR/可持续发展实体

### 7.1 CSR活动 (CSRActivity)
```csharp
public class CSRActivity
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    // 多语言字段
    public Dictionary<string, CSRActivityLocaleFields> Locale { get; set; } = new();
    
    public CSRCategory Category { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    
    public List<string> ImageUrls { get; set; }
    public string ReportFileUrl { get; set; } // CSR报告PDF
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}

public enum CSRCategory
{
    Environment, // 环境保护
    SocialContribution, // 社会贡献
    Governance, // 治理
    EmployeeWelfare, // 员工福利
    CommunitySupport, // 社区支持
    Education, // 教育支持
    DisasterRelief // 灾害救助
}
```

## 8. 招聘信息实体

### 8.1 招聘职位 (JobPosition)
```csharp
public class JobPosition
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string DepartmentId { get; set; }
    
    // 多语言字段
    public Dictionary<string, JobPositionLocaleFields> Locale { get; set; } = new();
    
    public JobType Type { get; set; }
    public EmploymentType EmploymentType { get; set; }
    public ExperienceLevel ExperienceLevel { get; set; }
    
    public decimal? SalaryMin { get; set; }
    public decimal? SalaryMax { get; set; }
    public string Currency { get; set; }
    
    public DateTime PostDate { get; set; }
    public DateTime? ApplicationDeadline { get; set; }
    
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public enum JobType
{
    NewGraduate, // 新卒採用
    MidCareer, // 中途採用
    Internship, // 实习
    PartTime, // 兼职
    Contract // 合同工
}

public enum EmploymentType
{
    FullTime, // 全职
    PartTime, // 兼职
    Contract, // 合同
    Temporary, // 临时
    Internship // 实习
}

public enum ExperienceLevel
{
    Entry, // 入门级
    Junior, // 初级
    Mid, // 中级
    Senior, // 高级
    Executive // 管理层
}
```

### 8.2 员工访谈 (EmployeeInterview)
```csharp
public class EmployeeInterview
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string DepartmentId { get; set; }
    public int YearsOfService { get; set; } // 工作年限
    
    // 多语言字段
    public Dictionary<string, EmployeeInterviewLocaleFields> Locale { get; set; } = new();
    
    public string PhotoUrl { get; set; }
    public List<string> Tags { get; set; } // 标签（如：技术、管理、新人等）
    
    public DateTime InterviewDate { get; set; }
    public int DisplayOrder { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public bool IsPublished { get; set; } = true;
}
```

## 9. 动态组件系统实体

### 9.1 组件定义 (ComponentDefinition)
```csharp
public class ComponentDefinition
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string ComponentKey { get; set; } // 组件唯一标识
    
    // 多语言字段
    public Dictionary<string, ComponentDefinitionLocaleFields> Locale { get; set; } = new();
    
    public string ParameterSchema { get; set; } // JSON Schema定义参数
    public string DefaultParameters { get; set; } // 默认参数JSON
    
    public List<string> AvailableTemplates { get; set; } // 可用模板列表
    public string DefaultTemplate { get; set; }
    
    public ComponentCategory Category { get; set; }
    public string IconUrl { get; set; }
    public string PreviewImageUrl { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}

public enum ComponentCategory
{
    Header, // 头部
    Hero, // 主视觉
    Content, // 内容
    Navigation, // 导航
    Footer, // 底部
    Form, // 表单
    Media, // 媒体
    Layout // 布局
}
```

### 9.2 组件模板 (ComponentTemplate)
```csharp
public class ComponentTemplate
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string ComponentDefinitionId { get; set; }
    public string TemplateKey { get; set; } // 模板唯一标识
    
    // 多语言字段
    public Dictionary<string, ComponentTemplateLocaleFields> Locale { get; set; } = new();
    
    public string ViewPath { get; set; } // Razor视图路径
    public string StyleClasses { get; set; } // TailwindCSS样式类
    public string CustomCss { get; set; } // 自定义CSS
    
    public string PreviewImageUrl { get; set; }
    public bool IsDefault { get; set; } = false;
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}
```

### 9.3 页面配置 (PageConfiguration)
```csharp
public class PageConfiguration
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string PageKey { get; set; } // 页面唯一标识
    
    // 多语言字段
    public Dictionary<string, PageConfigurationLocaleFields> Locale { get; set; } = new();
    
    public string Route { get; set; } // 页面路由
    public string LayoutTemplate { get; set; } // 布局模板
    
    public List<PageComponent> Components { get; set; } // 页面组件配置
    
    public PageStatus Status { get; set; }
    public DateTime? PublishDate { get; set; }
    public string GeneratedViewPath { get; set; } // 生成的Razor页面路径
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; }
    public string UpdatedBy { get; set; }
}

public class PageComponent
{
    public string ComponentDefinitionId { get; set; }
    public string TemplateKey { get; set; }
    public string ParametersJson { get; set; } // 组件参数JSON
    public int DisplayOrder { get; set; }
    public bool IsVisible { get; set; } = true;
}

public enum PageStatus
{
    Draft, // 草稿
    Preview, // 预览
    Published, // 已发布
    Archived // 已归档
}
```

### 9.4 页面版本控制 (PageVersion)
```csharp
public class PageVersion
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string PageConfigurationId { get; set; }
    public int VersionNumber { get; set; }
    public string ConfigurationSnapshot { get; set; } // 配置快照JSON
    
    public string ChangeDescription { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public bool IsCurrentVersion { get; set; } = false;
}
```

## 10. 系统配置实体

### 10.1 网站设置 (SiteSettings)
```csharp
public class SiteSettings
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    // 多语言字段
    public Dictionary<string, SiteSettingsLocaleFields> Locale { get; set; } = new();
    
    public string LogoUrl { get; set; }
    public string FaviconUrl { get; set; }
    public string DefaultLanguage { get; set; } = "ja";
    public List<string> SupportedLanguages { get; set; } = new() { "ja", "en" };
    
    public string ThemeColor { get; set; }
    public string FontFamily { get; set; }
    public Dictionary<string, string> CustomSettings { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

### 10.2 SEO设置 (SEOSettings)
```csharp
public class SEOSettings
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }
    
    public string PageConfigurationId { get; set; }
    
    // 多语言字段
    public Dictionary<string, SEOSettingsLocaleFields> Locale { get; set; } = new();
    
    public string OgImageUrl { get; set; }
    public string CanonicalUrl { get; set; }
    public List<string> AlternateUrls { get; set; } // 多语言页面URL
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

## 总结

以上实体设计涵盖了：

1. **企业基础信息**：公司信息、联系方式、据点等
2. **组织架构**：管理层、部门结构等
3. **企业历史**：沿革记录
4. **业务信息**：业务部门、产品服务等
5. **新闻公告**：企业新闻、媒体报道等
6. **投资者关系**：财务报告、股东大会等
7. **CSR活动**：社会责任相关活动
8. **招聘信息**：职位、员工访谈等
9. **动态组件系统**：组件定义、模板、页面配置等
10. **系统配置**：网站设置、SEO配置等

所有实体都支持多语言存储，适配MongoDB数据库，并遵循C# 9.0语法规范。
## 
11. 使用示例

### 11.1 创建多语言企业信息
```csharp
var company = new Company
{
    CompanyCode = "SAMPLE_CORP",
    EstablishedDate = new DateTime(1990, 4, 1),
    RegistrationNumber = "1234567890",
    Capital = *********,
    Currency = "JPY",
    Locale = new Dictionary<string, CompanyLocaleFields>
    {
        ["ja"] = new CompanyLocaleFields
        {
            CompanyName = "株式会社サンプル",
            CompanyDescription = "私たちは革新的なソリューションを提供する会社です。",
            Mission = "技術で世界を変える",
            Vision = "持続可能な未来の創造",
            Address = "東京都渋谷区渋谷1-1-1"
        },
        ["en"] = new CompanyLocaleFields
        {
            CompanyName = "Sample Corporation",
            CompanyDescription = "We are a company that provides innovative solutions.",
            Mission = "Changing the world through technology",
            Vision = "Creating a sustainable future",
            Address = "1-1-1 Shibuya, Shibuya-ku, Tokyo"
        },
        ["zh"] = new CompanyLocaleFields
        {
            CompanyName = "样本公司",
            CompanyDescription = "我们是一家提供创新解决方案的公司。",
            Mission = "通过技术改变世界",
            Vision = "创造可持续的未来",
            Address = "东京都涩谷区涩谷1-1-1"
        }
    }
};
```

### 11.2 获取指定语言的数据
```csharp
// 获取日语版本的企业信息
var japaneseInfo = company.Locale.GetLocale("ja");
Console.WriteLine(japaneseInfo.CompanyName); // 输出：株式会社サンプル

// 获取英语版本的企业信息
var englishInfo = company.Locale.GetLocale("en");
Console.WriteLine(englishInfo.CompanyName); // 输出：Sample Corporation

// 如果请求的语言不存在，会按照回退策略返回
var koreanInfo = company.Locale.GetLocale("ko"); // 会返回日语版本（回退策略）
```

### 11.3 JSON存储格式示例
```json
{
  "_id": "64a1b2c3d4e5f6789012345",
  "CompanyCode": "SAMPLE_CORP",
  "EstablishedDate": "1990-04-01T00:00:00Z",
  "RegistrationNumber": "1234567890",
  "Capital": *********,
  "Currency": "JPY",
  "Locale": {
    "ja": {
      "CompanyName": "株式会社サンプル",
      "CompanyDescription": "私たちは革新的なソリューションを提供する会社です。",
      "Mission": "技術で世界を変える",
      "Vision": "持続可能な未来の創造",
      "Address": "東京都渋谷区渋谷1-1-1"
    },
    "en": {
      "CompanyName": "Sample Corporation",
      "CompanyDescription": "We are a company that provides innovative solutions.",
      "Mission": "Changing the world through technology",
      "Vision": "Creating a sustainable future",
      "Address": "1-1-1 Shibuya, Shibuya-ku, Tokyo"
    },
    "zh": {
      "CompanyName": "样本公司",
      "CompanyDescription": "我们是一家提供创新解决方案的公司。",
      "Mission": "通过技术改变世界",
      "Vision": "创造可持续的未来",
      "Address": "东京都涩谷区涩谷1-1-1"
    }
  },
  "CreatedAt": "2024-01-01T00:00:00Z",
  "UpdatedAt": "2024-01-01T00:00:00Z",
  "IsActive": true
}
```

### 11.4 MongoDB查询示例
```csharp
// 查询包含特定语言的企业信息
var company = await companyCollection.Find(c => c.Locale.ContainsKey("ja")).FirstOrDefaultAsync();

// 根据日语企业名称搜索
var company = await companyCollection.Find(c => c.Locale["ja"].CompanyName.Contains("サンプル")).FirstOrDefaultAsync();

// 查询已发布的新闻
var publishedNews = await newsCollection.Find(n => n.IsPublished == true).ToListAsync();

// 查询特定类型的招聘职位
var newGraduateJobs = await jobCollection.Find(j => j.Type == JobType.NewGraduate && j.IsActive).ToListAsync();
```

## 总结

这种多语言存储结构的优势：

1. **结构清晰**：每个实体的多语言字段都有明确的定义
2. **类型安全**：强类型的多语言字段定义，避免运行时错误
3. **易于扩展**：可以轻松添加新的语言支持
4. **查询友好**：MongoDB可以直接对嵌套的语言字段进行查询
5. **回退机制**：提供了语言回退策略，确保总能获取到内容
6. **性能优化**：所有语言版本存储在同一文档中，减少查询次数

这个设计完全符合你的要求：使用 `Dictionary<string, 实体>` 的 `Locale` 结构来存储多语言数据，支持C# 9.0语法，适配MongoDB数据库，并且涵盖了日本企业官网的所有核心功能模块。