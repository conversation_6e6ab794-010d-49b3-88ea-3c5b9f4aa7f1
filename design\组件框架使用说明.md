# 动态组件框架使用说明 (Flowbite + Tailwind CSS)

## 概述

基于Flowbite + Tailwind CSS构建的动态组件框架，包含7个基础组件类型：

1. **Header** - 头部组件 (使用Flowbite Navbar)
2. **Hero** - 主视觉组件 (使用Flowbite Hero + Carousel)
3. **Content** - 内容组件 (使用Flowbite Card + Grid)
4. **Navigation** - 导航组件 (使用Flowbite Navigation)
5. **Footer** - 底部组件 (使用Flowbite Footer)
6. **Form** - 表单组件 (使用Flowbite Forms)
7. **Media** - 媒体组件 (使用Flowbite Gallery + Carousel)

## 技术栈
- **CSS框架**: Tailwind CSS 3.x
- **UI组件库**: Flowbite 2.x
- **JavaScript**: Flowbite JS + 原生JavaScript
- **图标**: Heroicons (Tailwind官方图标库)
- **字体**: Inter (Google Fonts)

## 文件结构

### ViewModel 类 (src/Web/Models/Components/)
- `HeaderComponentViewModel.cs` - 头部组件数据模型
- `HeroComponentViewModel.cs` - 主视觉组件数据模型
- `ContentComponentViewModel.cs` - 内容组件数据模型
- `NavigationComponentViewModel.cs` - 导航组件数据模型
- `FooterComponentViewModel.cs` - 底部组件数据模型
- `FormComponentViewModel.cs` - 表单组件数据模型
- `MediaComponentViewModel.cs` - 媒体组件数据模型

### Partial View 文件 (src/Web/Views/Shared/Components/)
- `_HeaderComponent.cshtml` - 头部组件视图
- `_HeroComponent.cshtml` - 主视觉组件视图
- `_ContentComponent.cshtml` - 内容组件视图
- `_NavigationComponent.cshtml` - 导航组件视图
- `_FooterComponent.cshtml` - 底部组件视图
- `_FormComponent.cshtml` - 表单组件视图
- `_MediaComponent.cshtml` - 媒体组件视图

### 服务类
- `ComponentRenderService.cs` - 组件渲染服务，负责动态渲染组件HTML

### 控制器
- `ComponentController.cs` - 组件API控制器，提供组件预览接口

### 样式文件
- `site.css` - 主样式文件，包含Tailwind CSS和Flowbite样式
- `components.css` - 自定义组件样式补充
- `tailwind.config.js` - Tailwind CSS配置文件

## 使用方法

### 1. 在页面中直接使用组件

```csharp
@await Html.PartialAsync("Components/_HeaderComponent", new HeaderComponentViewModel
{
    CompanyName = "株式会社サンプル",
    NavigationItems = new List<NavigationItem>
    {
        new() { Text = "ホーム", Url = "/", IsActive = true },
        new() { Text = "会社概要", Url = "/company" }
    }
})
```

### 2. 通过API动态渲染组件

```javascript
// 调用组件预览API
const response = await fetch('/api/component/preview/header', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        parametersJson: JSON.stringify({
            companyName: "テスト会社",
            navigationItems: [...]
        })
    })
});

const result = await response.json();
// result.html 包含渲染后的HTML
```

### 3. 使用组件渲染服务

```csharp
public class PageController : Controller
{
    private readonly IComponentRenderService _componentRenderService;
    
    public PageController(IComponentRenderService componentRenderService)
    {
        _componentRenderService = componentRenderService;
    }
    
    public async Task<IActionResult> PreviewPage()
    {
        var headerHtml = await _componentRenderService.RenderHeaderComponentAsync(parametersJson);
        var heroHtml = await _componentRenderService.RenderHeroComponentAsync(parametersJson);
        // ...
        return View();
    }
}
```

## API 端点

### 组件预览API
- `POST /api/component/preview` - 通用组件预览
- `POST /api/component/preview/header` - 头部组件预览
- `POST /api/component/preview/hero` - 主视觉组件预览
- `POST /api/component/preview/content` - 内容组件预览
- `POST /api/component/preview/navigation` - 导航组件预览
- `POST /api/component/preview/footer` - 底部组件预览
- `POST /api/component/preview/form` - 表单组件预览
- `POST /api/component/preview/media` - 媒体组件预览

## 测试页面

### 组件展示页面
访问 `/Home/Components` 查看所有组件的静态展示

### 组件测试页面  
访问 `/Home/ComponentTest` 测试组件API功能

## 组件特性 (基于Flowbite)

### Header 组件 (Flowbite Navbar)
- **响应式导航**: 使用Flowbite Navbar组件，支持移动端汉堡菜单
- **Logo展示**: 支持SVG/PNG格式Logo
- **多级菜单**: 支持下拉菜单和巨型菜单(Mega Menu)
- **搜索功能**: 集成Flowbite搜索框组件
- **语言切换**: 使用Flowbite下拉菜单实现
- **样式类**: `navbar`, `navbar-brand`, `navbar-nav`, `navbar-toggler`

### Hero 组件 (Flowbite Hero + Carousel)
- **轮播背景**: 使用Flowbite Carousel实现背景图片轮播
- **渐变遮罩**: Tailwind渐变类实现遮罩效果
- **CTA按钮**: 使用Flowbite Button组件，支持多种样式
- **动画效果**: 使用Tailwind动画类实现淡入效果
- **响应式文字**: 使用Tailwind响应式文字大小类
- **样式类**: `hero-section`, `carousel`, `btn-primary`, `btn-outline`

### Content 组件 (Flowbite Card + Grid)
- **卡片布局**: 使用Flowbite Card组件展示内容
- **网格系统**: 基于Tailwind Grid实现响应式布局
- **图标支持**: 集成Heroicons图标库
- **悬停效果**: 使用Tailwind悬停类实现交互效果
- **阴影效果**: 使用Tailwind阴影类增强视觉层次
- **样式类**: `card`, `grid`, `hover:shadow-lg`, `transition-all`

### Navigation 组件 (Flowbite Navigation)
- **面包屑**: 使用Flowbite Breadcrumb组件
- **侧边栏**: 使用Flowbite Sidebar组件
- **标签页**: 使用Flowbite Tabs组件
- **分页**: 使用Flowbite Pagination组件
- **步骤指示**: 使用Flowbite Stepper组件
- **样式类**: `breadcrumb`, `sidebar`, `tabs`, `pagination`

### Footer 组件 (Flowbite Footer)
- **多列布局**: 使用Tailwind Grid实现响应式列布局
- **社交图标**: 集成Flowbite社交媒体图标
- **链接分组**: 使用Flowbite List Group组件
- **版权信息**: 使用Tailwind文字样式类
- **深色主题**: 支持Flowbite深色模式
- **样式类**: `footer`, `footer-links`, `social-icons`, `dark:bg-gray-900`

### Form 组件 (Flowbite Forms)
- **表单验证**: 使用Flowbite表单验证样式
- **输入组件**: 包含Input、Select、Textarea、Checkbox、Radio
- **文件上传**: 使用Flowbite File Input组件
- **日期选择**: 使用Flowbite Datepicker组件
- **表单布局**: 使用Tailwind Flexbox和Grid布局
- **样式类**: `form-input`, `form-select`, `form-checkbox`, `invalid:border-red-500`

### Media 组件 (Flowbite Gallery + Carousel)
- **图片画廊**: 使用Flowbite Gallery组件
- **轮播图**: 使用Flowbite Carousel组件
- **模态框**: 使用Flowbite Modal展示大图
- **视频播放**: 集成HTML5视频播放器
- **懒加载**: 使用Intersection Observer API
- **样式类**: `gallery`, `carousel`, `modal`, `aspect-video`

## 下一步开发

1. **模板系统** - 为每个组件创建多个模板变体
2. **参数Schema** - 定义JSON Schema用于参数验证
3. **组件定义数据** - 在数据库中存储组件定义
4. **页面生成器** - 根据配置生成完整的Razor页面
5. **编译系统** - 将生成的页面编译为DLL
6. **管理界面** - 创建可视化的页面配置器

## Flowbite组件使用示例

### 1. Header组件示例
```html
<nav class="bg-white border-gray-200 dark:bg-gray-900">
  <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
    <a href="/" class="flex items-center space-x-3 rtl:space-x-reverse">
      <img src="/images/logo.svg" class="h-8" alt="Company Logo" />
      <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">@Model.CompanyName</span>
    </a>
    <button data-collapse-toggle="navbar-default" type="button" class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600">
      <svg class="w-5 h-5" fill="none" viewBox="0 0 17 14">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15"/>
      </svg>
    </button>
  </div>
</nav>
```

### 2. Hero组件示例
```html
<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-screen-xl text-center lg:py-16">
    <h1 class="mb-4 text-4xl font-extrabold tracking-tight leading-none text-gray-900 md:text-5xl lg:text-6xl dark:text-white">
      @Model.Title
    </h1>
    <p class="mb-8 text-lg font-normal text-gray-500 lg:text-xl sm:px-16 lg:px-48 dark:text-gray-400">
      @Model.Subtitle
    </p>
    <div class="flex flex-col space-y-4 sm:flex-row sm:justify-center sm:space-y-0">
      <a href="@Model.PrimaryButtonUrl" class="inline-flex justify-center items-center py-3 px-5 text-base font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-900">
        @Model.PrimaryButtonText
      </a>
    </div>
  </div>
</section>
```

### 3. Card组件示例
```html
<div class="max-w-sm bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
  <a href="@Model.Url">
    <img class="rounded-t-lg" src="@Model.ImageUrl" alt="@Model.Title" />
  </a>
  <div class="p-5">
    <a href="@Model.Url">
      <h5 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">@Model.Title</h5>
    </a>
    <p class="mb-3 font-normal text-gray-700 dark:text-gray-400">@Model.Description</p>
    <a href="@Model.Url" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
      詳細を見る
      <svg class="rtl:rotate-180 w-3.5 h-3.5 ms-2" fill="none" viewBox="0 0 14 10">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
      </svg>
    </a>
  </div>
</div>
```

## 主题定制

### 1. 自定义颜色配置
```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          900: '#1e3a8a'
        }
      }
    }
  }
}
```

### 2. 深色模式支持
```html
<!-- 深色模式切换按钮 -->
<button id="theme-toggle" type="button" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5">
  <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
  </svg>
  <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 17.77L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"></path>
  </svg>
</button>
```

## 性能优化

### 1. CSS优化
- 使用Tailwind CSS的JIT模式，只生成使用的样式
- 启用CSS压缩和Tree Shaking
- 使用CDN加载Flowbite CSS和JS

### 2. JavaScript优化
- 按需加载Flowbite组件
- 使用Intersection Observer实现懒加载
- 压缩和合并JavaScript文件

## 注意事项

1. **依赖管理**: 确保正确安装Tailwind CSS和Flowbite依赖
2. **样式引入**: 在`_Layout.cshtml`中正确引入CSS和JS文件
3. **组件初始化**: 确保Flowbite JavaScript组件正确初始化
4. **响应式设计**: 使用Tailwind响应式前缀确保移动端兼容
5. **深色模式**: 为所有组件添加深色模式支持
6. **无障碍访问**: 使用Flowbite的无障碍访问特性
7. **浏览器兼容**: 确保目标浏览器支持CSS Grid和Flexbox

## 资源链接

- **Tailwind CSS文档**: https://tailwindcss.com/docs
- **Flowbite组件库**: https://flowbite.com/docs/components
- **Heroicons图标**: https://heroicons.com/
- **Tailwind UI**: https://tailwindui.com/ (付费组件)

这个基于Flowbite + Tailwind CSS的组件框架为日本企业官网提供了现代化、响应式的UI解决方案，支持深色模式和无障碍访问，具有良好的扩展性和维护性。