 基于分析现有代码结构，我建议的组件配置数据项设计如下：

  1. 组件定义配置 (ComponentDefinition)

  // 基础信息
  - ComponentKey: string        // 组件标识 (Header, Footer, Hero等)
  - ComponentName: string       // 显示名称
  - Description: string         // 组件描述
  - Category: ComponentCategory // 组件分类 (Header, Footer, Content等)

  // 模板配置
  - AvailableTemplates: List<string>  // 可用模板列表 ["Default", "Modern", "Classic"]
  - DefaultTemplate: string           // 默认模板

  // 参数配置
  - ParameterSchema: string          // JSON Schema 定义参数结构
  - DefaultParameters: string        // 默认参数值 (JSON)

  // 显示配置
  - IconUrl: string                  // 组件图标
  - PreviewImageUrl: string          // 预览图
  - IsActive: bool                   // 是否启用

  2. 页面组件实例配置 (PageComponent)

  // 组件引用
  - ComponentDefinitionId: string    // 关联组件定义
  - TemplateKey: string             // 使用的模板名称

  // 显示控制
  - DisplayOrder: int               // 显示顺序
  - IsVisible: bool                 // 是否可见
  - CssClasses: string             // 自定义CSS类
  - CustomStyles: string           // 内联样式

  // 参数配置
  - ParametersJson: string         // 实际参数值 (JSON)

  // 条件显示
  - DisplayConditions: List<DisplayCondition>  // 显示条件

  3. 全局组件配置 (GlobalComponentSettings)

  // 全局组件实例
  - ComponentType: GlobalComponentType  // Header, Footer, Navigation
  - ComponentDefinitionId: string       // 组件定义ID
  - TemplateKey: string                // 模板key
  - ParametersJson: string             // 参数JSON
  - IsEnabled: bool                    // 是否启用
  - Priority: int                      // 优先级

  4. 站点组件布局配置 (SiteComponentLayout)

  // 布局设置
  - SiteId: string                     // 站点ID
  - LayoutName: string                 // 布局名称 (Default, Mobile, Print)
  - GlobalComponents: List<GlobalComponentInstance>  // 全局组件
  - DefaultPageComponents: List<PageComponentTemplate>  // 默认页面组件

  5. 组件参数类型定义

  // 常见参数类型
  - Text: string                       // 文本
  - RichText: string                   // 富文本
  - Image: MediaInfo                   // 图片
  - Link: LinkInfo                     // 链接
  - Color: string                      // 颜色
  - Boolean: bool                      // 布尔值
  - Number: int/decimal                // 数值
  - List<T>: 列表类型                  // 列表
  - Dictionary<string, object>         // 复杂对象

● 6. 全局组件 vs 普通组件配置差异

  全局组件 (Header, Footer, Navigation)

  - 作用范围: 所有页面共享
  - 配置位置: SiteSettings 或 GlobalComponentSettings 集合
  - 渲染位置: _Layout.cshtml 中固定位置
  - 配置特点:
    - 站点级别配置，一次设置全站生效
    - 支持多套方案切换 (比如节假日版本)
    - 需要缓存机制提升性能
    - 权限控制相对简单

  普通组件 (Hero, Content, Form等)

  - 作用范围: 特定页面或页面类型
  - 配置位置: PageConfiguration.Components 集合
  - 渲染位置: 具体页面内容区域
  - 配置特点:
    - 页面级别配置，每个页面可以不同
    - 支持组件间的排序和条件显示
    - 参数更加灵活多样
    - 需要页面级别的权限控制

  配置数据结构建议
 // 全局组件配置 - 简化版本
 ```
  public class GlobalComponentConfig
  {
      public ComponentType Type { get; set; }  // Header/Footer/Navigation
      public string TemplateKey { get; set; }
      public Dictionary<string, object> Parameters { get; set; }
      public bool IsEnabled { get; set; }
  }
 ```
  // 页面组件配置 - 完整版本  
   ```
  public class PageComponentConfig
  {
      public string ComponentKey { get; set; }
      public string TemplateKey { get; set; }
      public Dictionary<string, object> Parameters { get; set; }
      public int DisplayOrder { get; set; }
      public bool IsVisible { get; set; }
      public List<DisplayCondition> Conditions { get; set; }
      public ResponsiveSettings Responsive { get; set; }
      public string? CssClasses { get; set; }
      public string? CustomStyles { get; set; }
      public DateTime? EffectiveFrom { get; set; }
      public DateTime? EffectiveTo { get; set; }
  }
 ```
  // 显示条件
   ```
  public class DisplayCondition
  {
      public ConditionType Type { get; set; }  // User, Device, Time, Custom
      public string Property { get; set; }     // 属性名
      public string Operator { get; set; }     // 操作符 (==, !=, contains等)
      public object Value { get; set; }        // 比较值
  }
 ```
  // 响应式设置
   ```
  public class ResponsiveSettings
  {
      public bool HideOnMobile { get; set; }
      public bool HideOnTablet { get; set; }
      public bool HideOnDesktop { get; set; }
      public Dictionary<string, object>? MobileParameters { get; set; }
      public Dictionary<string, object>? TabletParameters { get; set; }
  }
 ```
  // 条件类型枚举
   ```
  public enum ConditionType
  {
      User,           // 用户相关 (登录状态、角色等)
      Device,         // 设备类型
      Time,           // 时间条件
      Language,       // 语言设置
      Custom          // 自定义条件
  }
 ```
  实际使用示例

  // 全局Header组件配置
   ```
  var headerConfig = new GlobalComponentConfig
  {
      Type = ComponentType.Header,
      TemplateKey = "Modern",
      Parameters = new Dictionary<string, object>
      {
          ["Logo"] = "/images/logo.png",
          ["CompanyName"] = "株式会社ABC",
          ["ShowLanguageSelector"] = true,
          ["NavigationItems"] = new List<NavigationItem>
          {
              new() { Text = "会社概要", Url = "/company" },
              new() { Text = "事業内容", Url = "/business" }
          }
      },
      IsEnabled = true
  };
 ```
  // 页面Hero组件配置
   ```
  var heroConfig = new PageComponentConfig
  {
      ComponentKey = "Hero",
      TemplateKey = "VideoBackground",
      Parameters = new Dictionary<string, object>
      {
          ["Title"] = "革新的な化学ソリューション",
          ["Subtitle"] = "持続可能な未来を創造",
          ["BackgroundVideo"] = "/videos/hero-bg.mp4",
          ["CallToAction"] = new { Text = "詳しく見る", Url = "/solutions" }
      },
      DisplayOrder = 1,
      IsVisible = true,
      Conditions = new List<DisplayCondition>
      {
          new()
          {
              Type = ConditionType.Time,
              Property = "Hour",
              Operator = ">=",
              Value = 9
          }
      },
      Responsive = new ResponsiveSettings
      {
          HideOnMobile = false,
          MobileParameters = new Dictionary<string, object>
          {
              ["BackgroundVideo"] = null,
              ["BackgroundImage"] = "/images/hero-mobile.jpg"
          }
      }
  };
   ```