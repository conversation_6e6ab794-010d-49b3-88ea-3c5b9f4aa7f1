# 页面数据编辑器优化开发方案

## 📋 项目概述

本文档描述页面数据编辑器的优化方案，旨在改善现有编辑器中控件平铺、占用空间大、用户体验差的问题。通过引入**分组折叠 + 智能布局**的设计模式，实现类似低代码平台的表单编辑器体验。

## 🎯 优化目标

- **空间优化**: 通过分组折叠减少页面占用空间
- **用户体验**: 提供更直观的表单字段布局和编辑体验
- **功能扩展**: 支持文件上传、多语言字段等高级功能
- **维护性**: 通过配置驱动的方式简化后续维护

## 📐 设计原则

### 1. 符合项目开发规范
- 使用 **Flowbite + Tailwind CSS** 组件框架
- 严格使用 `primary-*` 主题变量，支持多主题切换
- 支持亮色/暗色模式
- 使用项目统一的Dialog系统
- 所有文本内容存储在资源文件中，支持多语言

### 2. 配置驱动
- 通过 `variants.json` 配置控制表单布局
- 支持字段分组、排序、样式定制
- 支持多种字段类型和显示模式

### 3. 渐进式增强
- 在现有架构基础上扩展，不破坏现有功能
- 保持API兼容性
- 支持逐步迁移现有组件

## 🏗️ 技术架构

### 核心技术栈
- **后端**: ASP.NET Core MVC + 现有Service层
- **前端**: 原生JavaScript + Flowbite组件
- **样式**: Tailwind CSS + 主题变量系统
- **国际化**: ASP.NET Core资源文件系统

### 架构层次
```
┌─────────────────────────────────────┐
│              前端展示层              │
│  ┌─────────────┬─────────────────┐   │
│  │  组件编辑器  │   表单渲染引擎   │   │
│  └─────────────┴─────────────────┘   │
├─────────────────────────────────────┤
│              配置处理层              │
│  ┌─────────────┬─────────────────┐   │
│  │  变体解析器  │   本地化处理器   │   │
│  └─────────────┴─────────────────┘   │
├─────────────────────────────────────┤
│              数据存储层              │
│  ┌─────────────┬─────────────────┐   │
│  │variants.json│   资源文件系统   │   │
│  └─────────────┴─────────────────┘   │
└─────────────────────────────────────┘
```

## 📋 详细实现方案

### 1. 配置文件结构扩展

#### variants.json 扩展格式
```json
{
  "variants": [
    {
      "Category": "Header",
      "Id": "Default",
      "Names": {
        "zh": "默认样式",
        "en": "Default Style", 
        "ja": "デフォルトスタイル"
      },
      "Descriptions": {
        "zh": "标准页头布局",
        "en": "Standard header layout",
        "ja": "標準ヘッダーレイアウト"
      },
      "formFields": [
        {
          "name": "title",
          "type": "multilingual-text",
          "label": "FormFields_Title",
          "display": {
            "group": "FormGroups_BasicInfo",
            "width": "col-span-8",
            "order": 1,
            "collapsed": false,
            "helpText": "FormFields_TitleHelpText",
            "layout": "inline"
          },
          "validation": {
            "required": true,
            "maxLength": 100
          }
        },
        {
          "name": "subtitle",
          "type": "multilingual-textarea", 
          "label": "FormFields_Subtitle",
          "display": {
            "group": "FormGroups_BasicInfo",
            "width": "col-span-12",
            "rows": 3,
            "order": 2,
            "layout": "stacked"
          }
        },
        {
          "name": "logo",
          "type": "image",
          "label": "FormFields_Logo",
          "display": {
            "group": "FormGroups_MediaContent",
            "width": "col-span-6",
            "order": 10
          },
          "fileConfig": {
            "folder": "header-logos",
            "types": ["image/jpeg", "image/png", "image/webp"],
            "maxSize": "2MB",
            "multiple": false,
            "preview": true,
            "crop": {
              "aspectRatio": "16:9",
              "minWidth": 200
            }
          }
        },
        {
          "name": "backgroundVideo",
          "type": "video", 
          "label": "FormFields_BackgroundVideo",
          "display": {
            "group": "FormGroups_MediaContent",
            "width": "col-span-6",
            "order": 11
          },
          "fileConfig": {
            "folder": "header-videos",
            "types": ["video/mp4", "video/webm"],
            "maxSize": "50MB",
            "multiple": false,
            "preview": true
          }
        },
        {
          "name": "documents",
          "type": "file",
          "label": "FormFields_Documents", 
          "display": {
            "group": "FormGroups_Resources",
            "width": "col-span-12",
            "order": 20
          },
          "fileConfig": {
            "folder": "header-resources",
            "types": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
            "maxSize": "10MB",
            "multiple": true,
            "preview": false
          }
        }
      ]
    }
  ]
}
```

#### 字段类型定义
```typescript
interface FieldTypeDefinition {
  // 基础输入类型
  "text": { component: "TextInput", multiline: false }
  "email": { component: "EmailInput", validation: "email" }
  "number": { component: "NumberInput", min?: number, max?: number }
  "textarea": { component: "TextareaInput", rows?: number, cols?: number }
  "select": { component: "SelectInput", options: SelectOption[] }
  "checkbox": { component: "CheckboxInput", multiple?: boolean }
  "radio": { component: "RadioInput", options: RadioOption[] }
  
  // 多语言字段
  "multilingual-text": { component: "MultilingualInput", layout: "inline" | "stacked" }
  "multilingual-textarea": { component: "MultilingualTextarea", layout: "stacked" }
  
  // 文件上传
  "file": { component: "FileUpload", accept: string[], maxSize: string }
  "image": { component: "ImageUpload", crop?: CropConfig }
  "video": { component: "VideoUpload", preview: boolean }
  
  // 高级输入
  "richtext": { component: "RichTextEditor", toolbar?: string[] }
  "color": { component: "ColorPicker", format: "hex" | "rgb" | "hsl" }
  "date": { component: "DatePicker", format?: string }
  "datetime": { component: "DateTimePicker", format?: string }
}
```

### 2. 资源文件组织

#### SharedResource.resx (中文默认)
```xml
<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 表单分组 -->
  <data name="FormGroups_BasicInfo" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="FormGroups_MediaContent" xml:space="preserve">
    <value>媒体内容</value>
  </data>
  <data name="FormGroups_LayoutSettings" xml:space="preserve">
    <value>布局设置</value>
  </data>
  <data name="FormGroups_Resources" xml:space="preserve">
    <value>资源文件</value>
  </data>
  <data name="FormGroups_SEOSettings" xml:space="preserve">
    <value>SEO设置</value>
  </data>

  <!-- 字段标签 -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>标题</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>副标题</value>
  </data>
  <data name="FormFields_Logo" xml:space="preserve">
    <value>标志图片</value>
  </data>
  <data name="FormFields_BackgroundVideo" xml:space="preserve">
    <value>背景视频</value>
  </data>
  <data name="FormFields_Documents" xml:space="preserve">
    <value>相关文档</value>
  </data>

  <!-- 帮助文本 -->
  <data name="FormFields_TitleHelpText" xml:space="preserve">
    <value>输入页面主标题，支持多语言</value>
  </data>
  <data name="FormFields_SubtitleHelpText" xml:space="preserve">
    <value>输入页面副标题，可选填写</value>
  </data>
  <data name="FormFields_LogoHelpText" xml:space="preserve">
    <value>上传企业标志，推荐尺寸200x100像素</value>
  </data>

  <!-- 文件上传相关 -->
  <data name="FileUpload_SelectFile" xml:space="preserve">
    <value>选择文件</value>
  </data>
  <data name="FileUpload_DragDropZone" xml:space="preserve">
    <value>拖拽文件到此处或点击选择</value>
  </data>
  <data name="FileUpload_DeleteFile" xml:space="preserve">
    <value>删除文件</value>
  </data>
  <data name="FileUpload_DeleteConfirm" xml:space="preserve">
    <value>确定要删除这个文件吗？</value>
  </data>
  <data name="FileUpload_UploadSuccess" xml:space="preserve">
    <value>文件上传成功</value>
  </data>
  <data name="FileUpload_UploadError" xml:space="preserve">
    <value>文件上传失败</value>
  </data>
  <data name="FileUpload_FileTooLarge" xml:space="preserve">
    <value>文件大小超过限制</value>
  </data>
  <data name="FileUpload_InvalidFileType" xml:space="preserve">
    <value>不支持的文件类型</value>
  </data>

  <!-- 表单操作 -->
  <data name="Form_CollapseGroup" xml:space="preserve">
    <value>收起分组</value>
  </data>
  <data name="Form_ExpandGroup" xml:space="preserve">
    <value>展开分组</value>
  </data>
  <data name="Form_ResetField" xml:space="preserve">
    <value>重置字段</value>
  </data>
  <data name="Form_ClearAll" xml:space="preserve">
    <value>清空所有</value>
  </data>
</root>
```

#### SharedResource.en.resx (英文)
```xml
<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 表单分组 -->
  <data name="FormGroups_BasicInfo" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="FormGroups_MediaContent" xml:space="preserve">
    <value>Media Content</value>
  </data>
  <data name="FormGroups_LayoutSettings" xml:space="preserve">
    <value>Layout Settings</value>
  </data>
  <data name="FormGroups_Resources" xml:space="preserve">
    <value>Resource Files</value>
  </data>
  <data name="FormGroups_SEOSettings" xml:space="preserve">
    <value>SEO Settings</value>
  </data>

  <!-- 字段标签 -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>Subtitle</value>
  </data>
  <data name="FormFields_Logo" xml:space="preserve">
    <value>Logo Image</value>
  </data>
  <data name="FormFields_BackgroundVideo" xml:space="preserve">
    <value>Background Video</value>
  </data>
  <data name="FormFields_Documents" xml:space="preserve">
    <value>Related Documents</value>
  </data>

  <!-- 其他英文翻译... -->
</root>
```

#### SharedResource.ja.resx (日文)
```xml
<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 表单分组 -->
  <data name="FormGroups_BasicInfo" xml:space="preserve">
    <value>基本情報</value>
  </data>
  <data name="FormGroups_MediaContent" xml:space="preserve">
    <value>メディアコンテンツ</value>
  </data>
  <data name="FormGroups_LayoutSettings" xml:space="preserve">
    <value>レイアウト設定</value>
  </data>
  <data name="FormGroups_Resources" xml:space="preserve">
    <value>リソースファイル</value>
  </data>
  <data name="FormGroups_SEOSettings" xml:space="preserve">
    <value>SEO設定</value>
  </data>

  <!-- 字段标签 -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>タイトル</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>サブタイトル</value>
  </data>
  <data name="FormFields_Logo" xml:space="preserve">
    <value>ロゴ画像</value>
  </data>
  <data name="FormFields_BackgroundVideo" xml:space="preserve">
    <value>背景ビデオ</value>
  </data>
  <data name="FormFields_Documents" xml:space="preserve">
    <value>関連文書</value>
  </data>

  <!-- 其他日文翻译... -->
</root>
```

### 3. 后端实现

#### 3.1 扩展现有模型

```csharp
// 扩展 ComponentVariant 模型
public class ComponentVariant
{
    public string Category { get; set; }
    public string Id { get; set; }
    public Dictionary<string, string> Names { get; set; }
    public Dictionary<string, string> Descriptions { get; set; }
    
    // 新增表单字段定义
    public List<FormField> FormFields { get; set; } = new();
}

// 新增表单字段模型
public class FormField
{
    public string Name { get; set; }
    public string Type { get; set; }
    public string Label { get; set; }
    public FormFieldDisplay Display { get; set; }
    public FormFieldValidation Validation { get; set; }
    public FileUploadConfig FileConfig { get; set; }
    public List<SelectOption> Options { get; set; }
}

public class FormFieldDisplay
{
    public string Group { get; set; }
    public string Width { get; set; } = "col-span-12";
    public int Order { get; set; }
    public bool Collapsed { get; set; }
    public string HelpText { get; set; }
    public string Layout { get; set; } = "default"; // inline, stacked, etc.
    public int? Rows { get; set; }
    public int? Cols { get; set; }
}

public class FormFieldValidation
{
    public bool Required { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public string Pattern { get; set; }
    public string CustomValidationMessage { get; set; }
}

public class FileUploadConfig
{
    public string Folder { get; set; }
    public List<string> Types { get; set; } = new();
    public string MaxSize { get; set; }
    public bool Multiple { get; set; }
    public bool Preview { get; set; } = true;
    public CropConfig Crop { get; set; }
}

public class CropConfig
{
    public string AspectRatio { get; set; }
    public int? MinWidth { get; set; }
    public int? MinHeight { get; set; }
}

public class SelectOption
{
    public string Value { get; set; }
    public string Label { get; set; }
    public bool Selected { get; set; }
}
```

#### 3.2 扩展变体处理服务

```csharp
public class ComponentVariantService
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<ComponentVariantService> _logger;
    
    public ComponentVariantService(
        IStringLocalizer<SharedResource> localizer,
        ILogger<ComponentVariantService> logger)
    {
        _localizer = localizer;
        _logger = logger;
    }
    
    public ComponentVariant ProcessVariantLocalization(ComponentVariant variant)
    {
        try
        {
            // 处理基本信息本地化
            ProcessBasicLocalization(variant);
            
            // 处理表单字段本地化
            ProcessFormFieldsLocalization(variant);
            
            return variant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing variant localization for {VariantId}", variant.Id);
            return variant;
        }
    }
    
    private void ProcessFormFieldsLocalization(ComponentVariant variant)
    {
        if (variant.FormFields == null) return;
        
        foreach (var field in variant.FormFields)
        {
            // 处理字段标签
            if (!string.IsNullOrEmpty(field.Label))
            {
                field.Label = _localizer[field.Label].Value;
            }
            
            // 处理显示属性
            if (field.Display != null)
            {
                // 处理分组名称
                if (!string.IsNullOrEmpty(field.Display.Group))
                {
                    field.Display.Group = _localizer[field.Display.Group].Value;
                }
                
                // 处理帮助文本
                if (!string.IsNullOrEmpty(field.Display.HelpText))
                {
                    field.Display.HelpText = _localizer[field.Display.HelpText].Value;
                }
            }
            
            // 处理选项本地化
            if (field.Options != null)
            {
                foreach (var option in field.Options)
                {
                    if (!string.IsNullOrEmpty(option.Label))
                    {
                        option.Label = _localizer[option.Label].Value;
                    }
                }
            }
        }
    }
    
    public Dictionary<string, List<FormField>> GroupFieldsByCategory(List<FormField> fields)
    {
        return fields
            .OrderBy(f => f.Display?.Order ?? 0)
            .GroupBy(f => f.Display?.Group ?? "其他")
            .ToDictionary(g => g.Key, g => g.ToList());
    }
}
```

#### 3.3 文件上传控制器

```csharp
[ApiController]
[Route("api/[controller]")]
public class FileUploadController : ControllerBase
{
    private readonly IWebHostEnvironment _webHostEnvironment;
    private readonly ILogger<FileUploadController> _logger;
    private readonly IStringLocalizer<SharedResource> _localizer;
    
    public FileUploadController(
        IWebHostEnvironment webHostEnvironment,
        ILogger<FileUploadController> logger,
        IStringLocalizer<SharedResource> localizer)
    {
        _webHostEnvironment = webHostEnvironment;
        _logger = logger;
        _localizer = localizer;
    }
    
    [HttpPost("upload")]
    public async Task<IActionResult> UploadFile(IFormFile file, [FromForm] string folder, [FromForm] string allowedTypes, [FromForm] string maxSize)
    {
        try
        {
            // 验证文件
            var validationResult = ValidateFile(file, allowedTypes, maxSize);
            if (!validationResult.IsValid)
            {
                return BadRequest(new { error = validationResult.ErrorMessage });
            }
            
            // 生成文件名和路径
            var fileName = GenerateUniqueFileName(file.FileName);
            var uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "uploads", folder);
            Directory.CreateDirectory(uploadsFolder);
            
            var filePath = Path.Combine(uploadsFolder, fileName);
            
            // 保存文件
            using var stream = new FileStream(filePath, FileMode.Create);
            await file.CopyToAsync(stream);
            
            // 返回文件信息
            var relativePath = $"/uploads/{folder}/{fileName}";
            return Ok(new 
            {
                success = true,
                fileName = fileName,
                originalName = file.FileName,
                filePath = relativePath,
                fileSize = file.Length,
                contentType = file.ContentType
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file");
            return StatusCode(500, new { error = _localizer["FileUpload_UploadError"].Value });
        }
    }
    
    [HttpDelete("delete")]
    public IActionResult DeleteFile([FromQuery] string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                return BadRequest(new { error = "File path is required" });
            }
            
            // 确保路径安全
            var safePath = filePath.TrimStart('/');
            var fullPath = Path.Combine(_webHostEnvironment.WebRootPath, safePath);
            
            if (System.IO.File.Exists(fullPath))
            {
                System.IO.File.Delete(fullPath);
                return Ok(new { success = true });
            }
            
            return NotFound(new { error = "File not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
            return StatusCode(500, new { error = _localizer["FileUpload_UploadError"].Value });
        }
    }
    
    private (bool IsValid, string ErrorMessage) ValidateFile(IFormFile file, string allowedTypes, string maxSize)
    {
        if (file == null || file.Length == 0)
        {
            return (false, _localizer["FileUpload_NoFileSelected"].Value);
        }
        
        // 验证文件类型
        if (!string.IsNullOrEmpty(allowedTypes))
        {
            var types = allowedTypes.Split(',').Select(t => t.Trim()).ToList();
            if (!types.Any(t => file.ContentType.StartsWith(t) || file.FileName.EndsWith(t.Replace("*", ""))))
            {
                return (false, _localizer["FileUpload_InvalidFileType"].Value);
            }
        }
        
        // 验证文件大小
        if (!string.IsNullOrEmpty(maxSize))
        {
            var maxSizeBytes = ParseFileSize(maxSize);
            if (file.Length > maxSizeBytes)
            {
                return (false, _localizer["FileUpload_FileTooLarge"].Value);
            }
        }
        
        return (true, string.Empty);
    }
    
    private long ParseFileSize(string sizeString)
    {
        var size = sizeString.ToUpper();
        var number = long.Parse(System.Text.RegularExpressions.Regex.Match(size, @"\d+").Value);
        
        if (size.Contains("KB")) return number * 1024;
        if (size.Contains("MB")) return number * 1024 * 1024;
        if (size.Contains("GB")) return number * 1024 * 1024 * 1024;
        
        return number;
    }
    
    private string GenerateUniqueFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        var randomString = Guid.NewGuid().ToString("N")[..8];
        
        return $"{nameWithoutExtension}_{timestamp}_{randomString}{extension}";
    }
}
```

### 4. 前端实现

#### 4.1 表单渲染引擎

```javascript
/**
 * 表单字段渲染引擎
 * 负责根据配置动态生成表单UI
 */
class FormFieldRenderer {
    constructor(container, localization = {}) {
        this.container = container;
        this.l10n = localization;
        this.fieldGroups = new Map();
        this.fieldInstances = new Map();
    }
    
    /**
     * 渲染完整的表单
     */
    render(formFields) {
        this.container.innerHTML = '';
        
        // 按分组组织字段
        const groups = this.groupFields(formFields);
        
        // 渲染每个分组
        groups.forEach((fields, groupName) => {
            this.renderGroup(groupName, fields);
        });
    }
    
    /**
     * 按分组组织字段
     */
    groupFields(fields) {
        const groups = new Map();
        
        fields
            .sort((a, b) => (a.display?.order || 0) - (b.display?.order || 0))
            .forEach(field => {
                const groupName = field.display?.group || this.l10n.defaultGroup || '其他';
                if (!groups.has(groupName)) {
                    groups.set(groupName, []);
                }
                groups.get(groupName).push(field);
            });
        
        return groups;
    }
    
    /**
     * 渲染分组
     */
    renderGroup(groupName, fields) {
        // 创建Flowbite手风琴组件
        const accordion = document.createElement('div');
        accordion.className = 'border border-gray-200 rounded-lg mb-4 dark:border-gray-700';
        accordion.dataset.group = groupName;
        
        // 检查是否有字段默认折叠
        const isCollapsed = fields.some(f => f.display?.collapsed);
        
        // 创建手风琴头部
        const header = this.createGroupHeader(groupName, isCollapsed);
        accordion.appendChild(header);
        
        // 创建内容区域
        const content = this.createGroupContent(fields, isCollapsed);
        accordion.appendChild(content);
        
        this.container.appendChild(accordion);
        this.fieldGroups.set(groupName, { accordion, content, fields });
    }
    
    /**
     * 创建分组头部
     */
    createGroupHeader(groupName, isCollapsed) {
        const header = document.createElement('h3');
        header.innerHTML = `
            <button type="button" 
                    class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-primary-200 dark:focus:ring-primary-800"
                    aria-expanded="${!isCollapsed}"
                    data-group-toggle="${groupName}">
                <span class="flex items-center">
                    <i class="fas fa-folder mr-3 text-primary-500"></i>
                    ${groupName}
                </span>
                <svg class="w-3 h-3 transition-transform ${isCollapsed ? '' : 'rotate-180'} shrink-0" 
                     fill="none" viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" 
                          stroke-width="2" d="M9 5 5 1 1 5"/>
                </svg>
            </button>
        `;
        
        // 绑定切换事件
        const button = header.querySelector('button');
        button.addEventListener('click', () => this.toggleGroup(groupName));
        
        return header;
    }
    
    /**
     * 创建分组内容
     */
    createGroupContent(fields, isCollapsed) {
        const content = document.createElement('div');
        content.className = `transition-all duration-300 ${isCollapsed ? 'hidden' : ''}`;
        content.dataset.groupContent = fields[0]?.display?.group || '';
        
        // 创建网格容器
        const grid = document.createElement('div');
        grid.className = 'grid grid-cols-12 gap-4 p-5';
        
        // 渲染字段
        fields.forEach(field => {
            const fieldElement = this.renderField(field);
            grid.appendChild(fieldElement);
        });
        
        content.appendChild(grid);
        return content;
    }
    
    /**
     * 切换分组展开/折叠状态
     */
    toggleGroup(groupName) {
        const group = this.fieldGroups.get(groupName);
        if (!group) return;
        
        const content = group.content;
        const button = group.accordion.querySelector('[data-group-toggle]');
        const icon = button.querySelector('svg');
        
        const isHidden = content.classList.contains('hidden');
        
        if (isHidden) {
            // 展开
            content.classList.remove('hidden');
            button.setAttribute('aria-expanded', 'true');
            icon.classList.add('rotate-180');
        } else {
            // 折叠
            content.classList.add('hidden');
            button.setAttribute('aria-expanded', 'false');
            icon.classList.remove('rotate-180');
        }
    }
    
    /**
     * 渲染单个字段
     */
    renderField(field) {
        const fieldContainer = document.createElement('div');
        fieldContainer.className = `${field.display?.width || 'col-span-12'} mb-4`;
        fieldContainer.dataset.fieldName = field.name;
        fieldContainer.dataset.fieldType = field.type;
        
        try {
            // 根据字段类型创建对应的组件
            const fieldComponent = this.createFieldComponent(field);
            fieldContainer.appendChild(fieldComponent);
            
            // 存储字段实例
            this.fieldInstances.set(field.name, {
                field,
                container: fieldContainer,
                component: fieldComponent
            });
        } catch (error) {
            console.error(`Error rendering field ${field.name}:`, error);
            // 渲染错误提示
            fieldContainer.innerHTML = `
                <div class="text-red-500 text-sm p-3 border border-red-300 rounded-md bg-red-50 dark:bg-red-900/20">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    渲染字段失败: ${field.name}
                </div>
            `;
        }
        
        return fieldContainer;
    }
    
    /**
     * 创建字段组件
     */
    createFieldComponent(field) {
        switch (field.type) {
            case 'text':
            case 'email':
            case 'number':
                return this.createTextInput(field);
            case 'textarea':
                return this.createTextareaInput(field);
            case 'multilingual-text':
                return this.createMultilingualInput(field);
            case 'multilingual-textarea':
                return this.createMultilingualTextarea(field);
            case 'select':
                return this.createSelectInput(field);
            case 'checkbox':
                return this.createCheckboxInput(field);
            case 'radio':
                return this.createRadioInput(field);
            case 'file':
            case 'image':
            case 'video':
                return this.createFileUpload(field);
            case 'richtext':
                return this.createRichTextEditor(field);
            case 'color':
                return this.createColorPicker(field);
            case 'date':
            case 'datetime':
                return this.createDatePicker(field);
            default:
                throw new Error(`Unsupported field type: ${field.type}`);
        }
    }
    
    /**
     * 创建文本输入字段
     */
    createTextInput(field) {
        const wrapper = document.createElement('div');
        
        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-gray-900 dark:text-white';
        label.textContent = field.label || field.name;
        label.setAttribute('for', `field_${field.name}`);
        
        // 创建输入框
        const input = document.createElement('input');
        input.type = field.type === 'email' ? 'email' : field.type === 'number' ? 'number' : 'text';
        input.id = `field_${field.name}`;
        input.name = field.name;
        input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
        
        // 设置验证属性
        if (field.validation?.required) {
            input.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        if (field.validation?.maxLength) {
            input.maxLength = field.validation.maxLength;
        }
        if (field.validation?.minLength) {
            input.minLength = field.validation.minLength;
        }
        if (field.validation?.pattern) {
            input.pattern = field.validation.pattern;
        }
        
        wrapper.appendChild(label);
        wrapper.appendChild(input);
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = field.display.helpText;
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建多行文本输入
     */
    createTextareaInput(field) {
        const wrapper = document.createElement('div');
        
        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-gray-900 dark:text-white';
        label.textContent = field.label || field.name;
        label.setAttribute('for', `field_${field.name}`);
        
        // 创建文本域
        const textarea = document.createElement('textarea');
        textarea.id = `field_${field.name}`;
        textarea.name = field.name;
        textarea.className = 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
        textarea.rows = field.display?.rows || 4;
        
        if (field.validation?.required) {
            textarea.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        wrapper.appendChild(textarea);
        
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = field.display.helpText;
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建多语言输入字段
     */
    createMultilingualInput(field) {
        const wrapper = document.createElement('div');
        
        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-gray-900 dark:text-white';
        label.textContent = field.label || field.name;
        
        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        
        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: '中文', emoji: '🇨🇳' },
            { code: 'en', name: 'English', emoji: '🇺🇸' },
            { code: 'ja', name: '日本語', emoji: '🇯🇵' }
        ];
        
        // 根据布局模式创建输入框
        if (field.display?.layout === 'inline') {
            // 内联模式：一行显示多个语言
            const grid = document.createElement('div');
            grid.className = `grid grid-cols-${languages.length} gap-2`;
            
            languages.forEach(lang => {
                const langWrapper = document.createElement('div');
                
                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-xs font-medium text-gray-600 dark:text-gray-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;
                
                const input = document.createElement('input');
                input.type = 'text';
                input.name = `${field.name}[${lang.code}]`;
                input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                input.placeholder = `${lang.name}...`;
                
                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(input);
                grid.appendChild(langWrapper);
            });
            
            wrapper.appendChild(grid);
        } else {
            // 堆叠模式：每个语言占一行
            languages.forEach(lang => {
                const langWrapper = document.createElement('div');
                langWrapper.className = 'mb-3';
                
                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;
                
                const input = document.createElement('input');
                input.type = 'text';
                input.name = `${field.name}[${lang.code}]`;
                input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                input.placeholder = `${this.l10n.enterText || '输入文本'}...`;
                
                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(input);
                wrapper.appendChild(langWrapper);
            });
        }
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = field.display.helpText;
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建多语言文本域
     */
    createMultilingualTextarea(field) {
        const wrapper = document.createElement('div');
        
        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-gray-900 dark:text-white';
        label.textContent = field.label || field.name;
        
        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        
        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: '中文', emoji: '🇨🇳' },
            { code: 'en', name: 'English', emoji: '🇺🇸' },
            { code: 'ja', name: '日本語', emoji: '🇯🇵' }
        ];
        
        // 多语言文本域通常使用堆叠布局
        languages.forEach(lang => {
            const langWrapper = document.createElement('div');
            langWrapper.className = 'mb-3';
            
            const langLabel = document.createElement('label');
            langLabel.className = 'block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300';
            langLabel.textContent = `${lang.emoji} ${lang.name}`;
            
            const textarea = document.createElement('textarea');
            textarea.name = `${field.name}[${lang.code}]`;
            textarea.className = 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
            textarea.rows = field.display?.rows || 3;
            textarea.placeholder = `${this.l10n.enterText || '输入文本'}...`;
            
            langWrapper.appendChild(langLabel);
            langWrapper.appendChild(textarea);
            wrapper.appendChild(langWrapper);
        });
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = field.display.helpText;
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建文件上传组件
     */
    createFileUpload(field) {
        const wrapper = document.createElement('div');
        wrapper.className = 'file-upload-wrapper';
        
        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-gray-900 dark:text-white';
        label.textContent = field.label || field.name;
        
        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        
        // 创建文件上传区域
        const uploadArea = document.createElement('div');
        uploadArea.className = 'flex items-center justify-center w-full';
        
        const dropZone = document.createElement('label');
        dropZone.className = 'flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600 transition-colors';
        dropZone.setAttribute('for', `file_${field.name}`);
        
        // 上传区域内容
        dropZone.innerHTML = `
            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span class="font-semibold">${this.l10n.clickToUpload || '点击上传'}</span> 
                    ${this.l10n.orDragDrop || '或拖拽文件到此处'}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    ${this.getFileTypeDescription(field.fileConfig)}
                </p>
            </div>
        `;
        
        // 隐藏的文件输入
        const fileInput = document.createElement('input');
        fileInput.id = `file_${field.name}`;
        fileInput.type = 'file';
        fileInput.name = field.name;
        fileInput.className = 'hidden';
        
        // 设置文件限制
        if (field.fileConfig?.types) {
            fileInput.accept = field.fileConfig.types.join(',');
        }
        if (field.fileConfig?.multiple) {
            fileInput.multiple = true;
        }
        
        uploadArea.appendChild(dropZone);
        uploadArea.appendChild(fileInput);
        wrapper.appendChild(uploadArea);
        
        // 文件列表容器
        const fileList = document.createElement('div');
        fileList.className = 'mt-4 space-y-2';
        fileList.id = `filelist_${field.name}`;
        wrapper.appendChild(fileList);
        
        // 绑定事件
        this.attachFileUploadEvents(fileInput, field, fileList);
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = field.display.helpText;
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 获取文件类型描述
     */
    getFileTypeDescription(fileConfig) {
        if (!fileConfig?.types || fileConfig.types.length === 0) {
            return this.l10n.allFiles || '所有文件类型';
        }
        
        const typeDescriptions = {
            'image/*': '图片',
            'video/*': '视频', 
            'application/pdf': 'PDF',
            'application/msword': 'Word文档',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档'
        };
        
        const descriptions = fileConfig.types.map(type => 
            typeDescriptions[type] || type
        );
        
        const maxSize = fileConfig.maxSize ? `, ${this.l10n.maxSize || '最大'}${fileConfig.maxSize}` : '';
        
        return `${descriptions.join(', ')}${maxSize}`;
    }
    
    /**
     * 绑定文件上传事件
     */
    attachFileUploadEvents(fileInput, field, fileList) {
        const dropZone = fileInput.parentElement.querySelector('label');
        
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files, field, fileList);
        });
        
        // 拖拽事件
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
        });
        
        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
            this.handleFileSelection(e.dataTransfer.files, field, fileList);
        });
    }
    
    /**
     * 处理文件选择
     */
    async handleFileSelection(files, field, fileList) {
        const fileArray = Array.from(files);
        
        for (const file of fileArray) {
            try {
                await this.uploadFile(file, field, fileList);
            } catch (error) {
                console.error('File upload error:', error);
                Dialog.error(`${this.l10n.uploadError || '上传失败'}: ${file.name}`);
            }
        }
    }
    
    /**
     * 上传单个文件
     */
    async uploadFile(file, field, fileList) {
        // 显示上传进度
        const progressItem = this.createProgressItem(file.name);
        fileList.appendChild(progressItem);
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('folder', field.fileConfig?.folder || 'default');
            formData.append('allowedTypes', (field.fileConfig?.types || []).join(','));
            formData.append('maxSize', field.fileConfig?.maxSize || '10MB');
            
            const response = await fetch('/api/FileUpload/upload', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Upload failed');
            }
            
            const result = await response.json();
            
            // 移除进度条，显示成功的文件项
            fileList.removeChild(progressItem);
            const fileItem = this.createFileItem(result, field);
            fileList.appendChild(fileItem);
            
        } catch (error) {
            // 移除进度条，显示错误
            fileList.removeChild(progressItem);
            throw error;
        }
    }
    
    /**
     * 创建上传进度项
     */
    createProgressItem(fileName) {
        const item = document.createElement('div');
        item.className = 'flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700';
        
        item.innerHTML = `
            <div class="flex-1">
                <p class="text-sm font-medium text-gray-900 dark:text-white">${fileName}</p>
                <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700 mt-1">
                    <div class="bg-primary-600 h-2 rounded-full animate-pulse" style="width: 45%"></div>
                </div>
            </div>
            <div class="ml-3">
                <div class="animate-spin w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full"></div>
            </div>
        `;
        
        return item;
    }
    
    /**
     * 创建文件项
     */
    createFileItem(fileInfo, field) {
        const item = document.createElement('div');
        item.className = 'flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm';
        item.dataset.filePath = fileInfo.filePath;
        
        // 根据文件类型显示不同图标
        const icon = this.getFileIcon(fileInfo.contentType);
        
        item.innerHTML = `
            <div class="flex-shrink-0 mr-3">
                <i class="${icon} text-2xl text-gray-500"></i>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    ${fileInfo.originalName}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    ${this.formatFileSize(fileInfo.fileSize)}
                </p>
            </div>
            <div class="flex items-center space-x-2">
                ${field.fileConfig?.preview && this.isPreviewable(fileInfo.contentType) ? 
                    `<button type="button" class="p-1 text-blue-600 hover:text-blue-700 dark:text-blue-400" 
                             onclick="window.open('${fileInfo.filePath}', '_blank')" 
                             title="${this.l10n.preview || '预览'}">
                        <i class="fas fa-eye"></i>
                    </button>` : ''
                }
                <button type="button" class="p-1 text-red-600 hover:text-red-700 dark:text-red-400" 
                        onclick="this.closest('.file-upload-wrapper').querySelector('.form-field-renderer').deleteFile('${fileInfo.filePath}', this.closest('[data-file-path]'))"
                        title="${this.l10n.deleteFile || '删除文件'}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        return item;
    }
    
    /**
     * 获取文件图标
     */
    getFileIcon(contentType) {
        if (contentType.startsWith('image/')) return 'fas fa-image text-green-500';
        if (contentType.startsWith('video/')) return 'fas fa-video text-blue-500';
        if (contentType.includes('pdf')) return 'fas fa-file-pdf text-red-500';
        if (contentType.includes('word') || contentType.includes('document')) return 'fas fa-file-word text-blue-600';
        return 'fas fa-file text-gray-500';
    }
    
    /**
     * 是否可预览
     */
    isPreviewable(contentType) {
        return contentType.startsWith('image/') || 
               contentType.includes('pdf') || 
               contentType.startsWith('video/');
    }
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 删除文件
     */
    async deleteFile(filePath, fileElement) {
        try {
            const result = await Dialog.confirm(
                this.l10n.deleteFileConfirm || '确定要删除这个文件吗？',
                this.l10n.confirm || '确认'
            );
            
            if (!result) return;
            
            const response = await fetch(`/api/FileUpload/delete?filePath=${encodeURIComponent(filePath)}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Delete failed');
            }
            
            // 从DOM中移除文件项
            fileElement.remove();
            
            Dialog.success(this.l10n.deleteSuccess || '文件删除成功');
            
        } catch (error) {
            console.error('File delete error:', error);
            Dialog.error(`${this.l10n.deleteError || '删除失败'}: ${error.message}`);
        }
    }
    
    /**
     * 获取表单数据
     */
    getFormData() {
        const data = {};
        
        this.fieldInstances.forEach((instance, fieldName) => {
            const field = instance.field;
            const container = instance.container;
            
            try {
                switch (field.type) {
                    case 'multilingual-text':
                    case 'multilingual-textarea':
                        data[fieldName] = this.getMultilingualValue(container, fieldName);
                        break;
                    case 'file':
                    case 'image':
                    case 'video':
                        data[fieldName] = this.getFileValue(container);
                        break;
                    default:
                        const input = container.querySelector(`[name="${fieldName}"]`);
                        data[fieldName] = input ? input.value : '';
                }
            } catch (error) {
                console.error(`Error getting value for field ${fieldName}:`, error);
                data[fieldName] = '';
            }
        });
        
        return data;
    }
    
    /**
     * 获取多语言字段值
     */
    getMultilingualValue(container, fieldName) {
        const values = {};
        const inputs = container.querySelectorAll(`[name^="${fieldName}["]`);
        
        inputs.forEach(input => {
            const match = input.name.match(/\[(\w+)\]$/);
            if (match) {
                values[match[1]] = input.value;
            }
        });
        
        return values;
    }
    
    /**
     * 获取文件字段值
     */
    getFileValue(container) {
        const fileItems = container.querySelectorAll('[data-file-path]');
        const files = [];
        
        fileItems.forEach(item => {
            files.push({
                path: item.dataset.filePath,
                name: item.querySelector('.truncate').textContent
            });
        });
        
        return files;
    }
    
    /**
     * 设置表单数据
     */
    setFormData(data) {
        Object.entries(data).forEach(([fieldName, value]) => {
            const instance = this.fieldInstances.get(fieldName);
            if (!instance) return;
            
            const field = instance.field;
            const container = instance.container;
            
            try {
                switch (field.type) {
                    case 'multilingual-text':
                    case 'multilingual-textarea':
                        this.setMultilingualValue(container, fieldName, value);
                        break;
                    case 'file':
                    case 'image': 
                    case 'video':
                        this.setFileValue(container, value, field);
                        break;
                    default:
                        const input = container.querySelector(`[name="${fieldName}"]`);
                        if (input) input.value = value || '';
                }
            } catch (error) {
                console.error(`Error setting value for field ${fieldName}:`, error);
            }
        });
    }
    
    /**
     * 设置多语言字段值
     */
    setMultilingualValue(container, fieldName, values) {
        if (typeof values === 'object' && values !== null) {
            Object.entries(values).forEach(([lang, value]) => {
                const input = container.querySelector(`[name="${fieldName}[${lang}]"]`);
                if (input) input.value = value || '';
            });
        }
    }
    
    /**
     * 设置文件字段值
     */
    setFileValue(container, files, field) {
        const fileList = container.querySelector(`[id^="filelist_"]`);
        if (!fileList || !Array.isArray(files)) return;
        
        // 清空现有文件
        fileList.innerHTML = '';
        
        // 添加文件项
        files.forEach(file => {
            if (file.path) {
                const fileInfo = {
                    filePath: file.path,
                    originalName: file.name || file.path.split('/').pop(),
                    fileSize: file.size || 0,
                    contentType: file.type || 'application/octet-stream'
                };
                
                const fileItem = this.createFileItem(fileInfo, field);
                fileList.appendChild(fileItem);
            }
        });
    }
}
```

#### 4.2 页面集成


### 5. 后端API扩展

#### 5.1 组件变体API控制器

```csharp
[ApiController]
[Route("api/[controller]")]
public class ComponentsController : ControllerBase
{
    private readonly ComponentVariantService _variantService;
    private readonly ILogger<ComponentsController> _logger;
    
    public ComponentsController(
        ComponentVariantService variantService,
        ILogger<ComponentsController> logger)
    {
        _variantService = variantService;
        _logger = logger;
    }
    
    /// <summary>
    /// 获取组件的特定变体信息
    /// </summary>
    /// <param name="componentId">组件ID</param>
    /// <param name="variantId">变体ID</param>
    /// <returns>变体信息</returns>
    [HttpGet("{componentId}/variants/{variantId}")]
    public async Task<IActionResult> GetComponentVariant(string componentId, string variantId = "Default")
    {
        try
        {
            var variant = await _variantService.GetVariantAsync(componentId, variantId);
            if (variant == null)
            {
                return NotFound(new { error = $"Variant {variantId} not found for component {componentId}" });
            }
            
            // 处理本地化
            var localizedVariant = _variantService.ProcessVariantLocalization(variant);
            
            return Ok(localizedVariant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting variant {VariantId} for component {ComponentId}", variantId, componentId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
    
    /// <summary>
    /// 获取组件的所有变体
    /// </summary>
    /// <param name="componentId">组件ID</param>
    /// <returns>变体列表</returns>
    [HttpGet("{componentId}/variants")]
    public async Task<IActionResult> GetComponentVariants(string componentId)
    {
        try
        {
            var variants = await _variantService.GetVariantsAsync(componentId);
            var localizedVariants = variants.Select(v => _variantService.ProcessVariantLocalization(v)).ToList();
            
            return Ok(localizedVariants);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting variants for component {ComponentId}", componentId);
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}
```

#### 5.2 扩展ComponentVariantService

```csharp
public class ComponentVariantService
{
    private readonly IWebHostEnvironment _environment;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<ComponentVariantService> _logger;
    private readonly IMemoryCache _cache;
    
    public ComponentVariantService(
        IWebHostEnvironment environment,
        IStringLocalizer<SharedResource> localizer,
        ILogger<ComponentVariantService> logger,
        IMemoryCache cache)
    {
        _environment = environment;
        _localizer = localizer;
        _logger = logger;
        _cache = cache;
    }
    
    public async Task<ComponentVariant> GetVariantAsync(string componentId, string variantId = "Default")
    {
        var cacheKey = $"component_variant_{componentId}_{variantId}";
        
        if (_cache.TryGetValue(cacheKey, out ComponentVariant cachedVariant))
        {
            return cachedVariant;
        }
        
        try
        {
            var variantsFilePath = Path.Combine(
                _environment.WebRootPath,
                "Views", "Shared", "Components", componentId, "variants.json");
                
            if (!System.IO.File.Exists(variantsFilePath))
            {
                _logger.LogWarning("Variants file not found: {FilePath}", variantsFilePath);
                return null;
            }
            
            var jsonContent = await System.IO.File.ReadAllTextAsync(variantsFilePath);
            var variantsContainer = JsonSerializer.Deserialize<VariantsContainer>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            var variant = variantsContainer?.Variants?.FirstOrDefault(v => v.Id == variantId);
            
            if (variant != null)
            {
                // 缓存30分钟
                _cache.Set(cacheKey, variant, TimeSpan.FromMinutes(30));
            }
            
            return variant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading variant {VariantId} for component {ComponentId}", variantId, componentId);
            return null;
        }
    }
    
    public async Task<List<ComponentVariant>> GetVariantsAsync(string componentId)
    {
        var cacheKey = $"component_variants_{componentId}";
        
        if (_cache.TryGetValue(cacheKey, out List<ComponentVariant> cachedVariants))
        {
            return cachedVariants;
        }
        
        try
        {
            var variantsFilePath = Path.Combine(
                _environment.WebRootPath,
                "Views", "Shared", "Components", componentId, "variants.json");
                
            if (!System.IO.File.Exists(variantsFilePath))
            {
                return new List<ComponentVariant>();
            }
            
            var jsonContent = await System.IO.File.ReadAllTextAsync(variantsFilePath);
            var variantsContainer = JsonSerializer.Deserialize<VariantsContainer>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            var variants = variantsContainer?.Variants ?? new List<ComponentVariant>();
            
            // 缓存30分钟
            _cache.Set(cacheKey, variants, TimeSpan.FromMinutes(30));
            
            return variants;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading variants for component {ComponentId}", componentId);
            return new List<ComponentVariant>();
        }
    }
    
    public ComponentVariant ProcessVariantLocalization(ComponentVariant variant)
    {
        if (variant == null) return null;
        
        try
        {
            // 创建副本以避免修改缓存的原对象
            var localizedVariant = JsonSerializer.Deserialize<ComponentVariant>(
                JsonSerializer.Serialize(variant));
            
            // 处理表单字段本地化
            ProcessFormFieldsLocalization(localizedVariant);
            
            return localizedVariant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing variant localization for {VariantId}", variant.Id);
            return variant;
        }
    }
    
    private void ProcessFormFieldsLocalization(ComponentVariant variant)
    {
        if (variant.FormFields == null) return;
        
        foreach (var field in variant.FormFields)
        {
            // 处理字段标签
            if (!string.IsNullOrEmpty(field.Label))
            {
                var localizedLabel = _localizer[field.Label];
                if (!localizedLabel.ResourceNotFound)
                {
                    field.Label = localizedLabel.Value;
                }
            }
            
            // 处理显示属性
            if (field.Display != null)
            {
                // 处理分组名称
                if (!string.IsNullOrEmpty(field.Display.Group))
                {
                    var localizedGroup = _localizer[field.Display.Group];
                    if (!localizedGroup.ResourceNotFound)
                    {
                        field.Display.Group = localizedGroup.Value;
                    }
                }
                
                // 处理帮助文本
                if (!string.IsNullOrEmpty(field.Display.HelpText))
                {
                    var localizedHelp = _localizer[field.Display.HelpText];
                    if (!localizedHelp.ResourceNotFound)
                    {
                        field.Display.HelpText = localizedHelp.Value;
                    }
                }
            }
            
            // 处理选项本地化
            if (field.Options != null)
            {
                foreach (var option in field.Options)
                {
                    if (!string.IsNullOrEmpty(option.Label))
                    {
                        var localizedOption = _localizer[option.Label];
                        if (!localizedOption.ResourceNotFound)
                        {
                            option.Label = localizedOption.Value;
                        }
                    }
                }
            }
        }
    }
}

// 辅助类
public class VariantsContainer
{
    public List<ComponentVariant> Variants { get; set; } = new();
}
```

## 📚 开发步骤

### 阶段1: 基础设施搭建 (1-2天)
1. ✅ 扩展现有模型类 (`ComponentVariant`, `FormField` 等)
2. ✅ 添加资源文件 (`SharedResource.*.resx`)
3. ✅ 创建文件上传API (`FileUploadController`)
4. ✅ 扩展组件变体服务 (`ComponentVariantService`)

### 阶段2: 前端表单渲染器 (2-3天)
1. ✅ 创建 `FormFieldRenderer` 类
2. ✅ 实现各种字段类型组件
3. ✅ 实现分组折叠功能
4. ✅ 实现文件上传组件
5. ✅ 集成多语言支持

### 阶段3: 页面编辑器集成 (1-2天)
1. ✅ 修改 `PageConfigurationEditor` 类
2. ✅ 集成新的表单渲染器
3. ✅ 实现属性面板显示
4. ✅ 更新页面模板

### 阶段4: 示例组件迁移 (1-2天)
1. 🔄 更新现有组件的 `variants.json` 文件
2. 🔄 测试各种字段类型
3. 🔄 验证多语言功能
4. 🔄 测试文件上传功能

### 阶段5: 测试和优化 (1-2天)
1. 🔄 端到端功能测试
2. 🔄 多主题测试
3. 🔄 暗色模式测试
4. 🔄 性能优化
5. 🔄 用户体验优化

## 🧪 测试计划

### 功能测试
- [ ] 表单字段渲染正确性
- [ ] 分组折叠展开功能
- [ ] 文件上传和删除功能
- [ ] 多语言切换功能
- [ ] 表单数据保存和加载

### 兼容性测试
- [ ] 多主题支持 (business-blue, elegant-purple, nature-green, warm-orange)
- [ ] 亮色/暗色模式切换
- [ ] 多浏览器兼容性 (Chrome, Firefox, Safari, Edge)
- [ ] 响应式设计测试

### 性能测试
- [ ] 大量字段渲染性能
- [ ] 文件上传性能
- [ ] 内存使用情况
- [ ] 缓存效率

## 📖 使用说明

### 开发人员指南

#### 1. 添加新的字段类型
```javascript
// 在FormFieldRenderer类中添加新的字段类型处理
createCustomInput(field) {
    const wrapper = document.createElement('div');
    // 实现自定义字段组件
    return wrapper;
}

// 在createFieldComponent方法中注册
case 'custom':
    return this.createCustomInput(field);
```

#### 2. 扩展现有组件
```json
// 在组件的variants.json中添加formFields配置
{
  "variants": [{
    "formFields": [
      {
        "name": "newField",
        "type": "text",
        "label": "FormFields_NewField",
        "display": {
          "group": "FormGroups_Advanced",
          "width": "col-span-6",
          "order": 100
        }
      }
    ]
  }]
}
```

#### 3. 添加新的本地化文本
```xml
<!-- 在SharedResource.*.resx中添加 -->
<data name="FormFields_NewField" xml:space="preserve">
    <value>新字段</value>
</data>
```

### 用户使用指南

1. **编辑组件属性**: 点击页面画布中的组件，右侧属性面板显示该组件的编辑表单
2. **分组管理**: 表单字段按功能分组，点击分组标题可折叠/展开
3. **文件上传**: 拖拽文件到上传区域或点击选择文件
4. **多语言编辑**: 多语言字段支持同时编辑多种语言版本
5. **保存更改**: 编辑完成后点击保存按钮应用更改

## ⚠️ 注意事项

### 开发注意事项
1. **严格遵循项目开发规范**: 使用主题变量、支持暗色模式、使用Dialog系统
2. **资源文件维护**: 所有文本必须添加到资源文件，支持多语言
3. **性能考虑**: 大型表单时考虑虚拟滚动或分页加载
4. **错误处理**: 实现完善的错误处理和用户反馈机制

### 用户使用注意事项
1. **文件大小限制**: 注意各类文件的大小限制
2. **必填字段**: 标有红色星号的字段为必填项
3. **数据保存**: 修改后需要点击保存才能生效
4. **浏览器兼容性**: 建议使用现代浏览器以获得最佳体验

## 🔧 故障排除

### 常见问题

#### 1. 表单字段不显示
- 检查 `variants.json` 文件格式是否正确
- 确认字段的 `type` 是否被支持
- 查看浏览器控制台是否有JavaScript错误

#### 2. 文件上传失败
- 检查文件大小是否超过限制
- 确认文件类型是否被允许
- 查看网络连接和服务器状态

#### 3. 多语言文本不显示
- 确认资源文件中存在对应的键值
- 检查资源键名是否正确
- 验证当前语言设置

#### 4. 样式显示异常
- 确认使用了正确的主题变量
- 检查是否有CSS冲突
- 验证暗色模式支持

### 调试建议
1. 开启浏览器开发者工具查看控制台日志
2. 检查网络请求状态
3. 验证JSON配置文件语法
4. 使用断点调试JavaScript代码

---

**此文档将随着开发进度持续更新，请保持关注最新版本。**

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"activeForm": "\u7f16\u5199\u9875\u9762\u6570\u636e\u7f16\u8f91\u5668\u4f18\u5316\u8be6\u7ec6\u5f00\u53d1\u6587\u6863", "content": "\u7f16\u5199\u9875\u9762\u6570\u636e\u7f16\u8f91\u5668\u4f18\u5316\u8be6\u7ec6\u5f00\u53d1\u6587\u6863", "status": "completed"}]