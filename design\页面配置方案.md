基于分析的代码结构，继续完成设计方案：

  一、页面配置定义系统

  1. 现有页面配置模型分析

  核心实体：PageConfiguration.cs:12-46
  public class PageConfiguration
  {
      public string Id { get; set; }
      public string PageKey { get; set; }
      public Dictionary<string, PageConfigurationLocaleFields> Locale { get; set; }
      public string? Route { get; set; }
      public string? LayoutTemplate { get; set; }
      public List<PageComponent> Components { get; set; }
      public PageStatus Status { get; set; }
      public DateTime? PublishDate { get; set; }
      public string? GeneratedViewPath { get; set; }
  }

  2. 增强页面配置系统设计

  建议的完整页面配置模型：

● 详细设计方案：页面配置定义、路由处理、缓存处理

  一、页面配置定义系统

  1. 核心配置模型分析

  当前系统已实现了完整的页面配置架构：

  PageConfiguration.cs:114-118 - 页面配置核心实体：
  - 多语言字段支持：Dictionary<string, PageConfigurationLocaleFields> Locale
  - 路由定义：string? Route
  - 布局模板：string? LayoutTemplate
  - 动态组件系统：List<PageComponent> Components
  - 状态管理：PageStatus Status

  PageComponent.cs:38-45 - 页部件配置：
  - 组件定义ID关联：ComponentDefinitionId
  - 模板变体选择：TemplateKey
  - JSON参数配置：ParametersJson
  - 显示顺序和可见性控制

  2. 页面配置增强建议

  A. 添加SEO和性能配置
  public class PageConfiguration
  {
      // 现有字段...

      // SEO增强配置
      public Dictionary<string, SEOConfiguration>? SEOConfig { get; set; }

      // 性能配置
      public PagePerformanceConfig? Performance { get; set; }

      // 缓存策略
      public PageCacheConfig? CacheConfig { get; set; }

      // 访问控制
      public PageAccessConfig? AccessControl { get; set; }
  }

  public class PagePerformanceConfig
  {
      public bool EnableImageLazyLoading { get; set; } = true;
      public bool EnableComponentLazyLoading { get; set; } = false;
      public int MaxComponentsPerPage { get; set; } = 20;
      public bool EnableBundleOptimization { get; set; } = true;
  }

  public class PageCacheConfig
  {
      public int CacheDurationMinutes { get; set; } = 30;
      public List<string> CacheVaryByParams { get; set; } = new();
      public bool EnableCDNCache { get; set; } = true;
      public List<string> CacheInvalidationTags { get; set; } = new();
  }

  二、路由处理系统

  1. 多语言路由架构分析

  Program.cs:187-195 - 路由配置：
  app.MapControllerRoute(
      name: "localized",
      pattern: "{culture}/{controller=Home}/{action=Index}/{id?}",
      constraints: new { culture = $"^({string.Join("|", supportedLanguages.Select(x => x.Code))})$" }
  );

  RouteDataRequestCultureProvider.cs - 文化识别：
  - URL路径解析：/ja/Company/About → culture=ja
  - 路由值检测：RouteData.Values["culture"]
  - 回退机制：默认语言处理

  2. 路由增强设计

  A. 动态页面路由注册
  public class DynamicPageRouteService
  {
      public async Task RegisterPageRoutesAsync(IEndpointRouteBuilder endpoints)
      {
          var pageConfigs = await _pageConfigService.GetPublishedPagesAsync();

          foreach (var page in pageConfigs)
          {
              foreach (var lang in _supportedLanguages)
              {
                  var route = BuildPageRoute(page, lang);
                  endpoints.MapControllerRoute(
                      name: $"page_{page.PageKey}_{lang.Code}",
                      pattern: route.Pattern,
                      defaults: new {
                          controller = "Page",
                          action = "Render",
                          pageKey = page.PageKey,
                          culture = lang.Code
                      }
                  );
              }
          }
      }
  }

  B. SEO友好URL生成
  public class SEORouteGenerator
  {
      public string GenerateSEOUrl(PageConfiguration page, SupportedLanguage language)
      {
          var localeFields = page.Locale.GetValueOrDefault(language.Code);
          if (localeFields?.PageTitle != null)
          {
              var slug = GenerateSlug(localeFields.PageTitle);
              return $"{language.BaseUrl.TrimEnd('/')}/{slug}";
          }
          return $"{language.BaseUrl.TrimEnd('/')}/{page.PageKey}";
      }

      private string GenerateSlug(string title)
      {
          return title.ToLowerInvariant()
                     .Replace(" ", "-")
                     .RemoveSpecialCharacters()
                     .Truncate(50);
      }
  }

  三、缓存处理系统

  1. 现有缓存机制分析

  ComponentConfigService.cs:20-26 - 内存缓存：
  - IMemoryCache _cache - 组件配置缓存
  - 30分钟缓存时长：CacheDurationMinutes = 30
  - 分层缓存键：component_config_header

  ComponentVariantService.cs:37 - 变体配置缓存：
  - 内存字典缓存：Dictionary<string, List<ComponentVariantOption>> _cache

  2. 多层缓存架构设计

  A. 分层缓存策略
  public class MultiLayerCacheService
  {
      private readonly IMemoryCache _memoryCache;
      private readonly IDistributedCache _distributedCache;
      private readonly ICacheInvalidationService _invalidationService;

      public async Task<T> GetOrSetAsync<T>(
          string key,
          Func<Task<T>> factory,
          CacheOptions options = null)
      {
          // L1: 内存缓存 (最快)
          if (_memoryCache.TryGetValue(key, out T memoryValue))
              return memoryValue;

          // L2: 分布式缓存 (Redis)
          var distributedValue = await _distributedCache.GetAsync<T>(key);
          if (distributedValue != null)
          {
              _memoryCache.Set(key, distributedValue, options?.MemoryExpiry ?? TimeSpan.FromMinutes(5));
              return distributedValue;
          }

          // L3: 数据源
          var value = await factory();
          if (value != null)
          {
              await _distributedCache.SetAsync(key, value, options?.DistributedExpiry ?? TimeSpan.FromHours(1));
              _memoryCache.Set(key, value, options?.MemoryExpiry ?? TimeSpan.FromMinutes(5));
          }

          return value;
      }
  }

  B. 页面缓存策略
  public class PageCacheService
  {
      public async Task<string> GetCachedPageAsync(
          string pageKey,
          string culture,
          string theme,
          Dictionary<string, object> parameters = null)
      {
          var cacheKey = GeneratePageCacheKey(pageKey, culture, theme, parameters);

          return await _cacheService.GetOrSetAsync(
              cacheKey,
              () => RenderPageAsync(pageKey, culture, theme, parameters),
              new CacheOptions
              {
                  MemoryExpiry = TimeSpan.FromMinutes(10),
                  DistributedExpiry = TimeSpan.FromHours(2),
                  Tags = new[] { $"page:{pageKey}", $"culture:{culture}", $"theme:{theme}" }
              });
      }

      private string GeneratePageCacheKey(string pageKey, string culture, string theme, Dictionary<string, object> parameters)
      {
          var paramHash = parameters?.Any() == true ?
              ComputeHash(JsonSerializer.Serialize(parameters)) : "none";
          return $"page:{pageKey}:culture:{culture}:theme:{theme}:params:{paramHash}";
      }
  }

  C. 智能缓存失效
  public class CacheInvalidationService
  {
      public async Task InvalidatePageCacheAsync(string pageKey)
      {
          var tags = new[] { $"page:{pageKey}" };
          await _taggedCache.RemoveByTagAsync(tags);

          // 通知其他实例
          await _messageBus.PublishAsync(new CacheInvalidationMessage
          {
              Tags = tags,
              Reason = "Page configuration updated"
          });
      }

      public async Task InvalidateThemeCacheAsync(string themeId)
      {
          var tags = new[] { $"theme:{themeId}" };
          await _taggedCache.RemoveByTagAsync(tags);
      }
  }

  四、性能优化建议

  1. 数据库查询优化

  public class OptimizedPageConfigService : PageConfigurationService
  {
      public async Task<PageConfiguration> GetPageWithComponentsAsync(string pageKey, string culture)
      {
          // 使用投影查询，只获取需要的字段
          return await FindOneAsync(
              p => p.PageKey == pageKey,
              projection: Builders<PageConfiguration>.Projection
                  .Include(p => p.PageKey)
                  .Include(p => p.Route)
                  .Include($"Locale.{culture}")
                  .Include(p => p.Components)
                  .Include(p => p.Status)
          );
      }
  }

  2. 组件懒加载

  public class LazyComponentLoader
  {
      public async Task<IHtmlContent> RenderComponentAsync(
          PageComponent component,
          bool enableLazyLoading = true)
      {
          if (enableLazyLoading && component.DisplayOrder > 3)
          {
              return new HtmlString($@"
                  <div class='lazy-component' 
                       data-component-id='{component.ComponentDefinitionId}'
                       data-template-key='{component.TemplateKey}'
                       data-parameters='{component.ParametersJson}'>
                      <div class='loading-placeholder'>読み込み中...</div>
                  </div>");
          }

          return await RenderComponentImmediatelyAsync(component);
      }
  }

  五、监控和诊断

  1. 缓存监控

  public class CacheMetricsService
  {
      public CacheMetrics GetCacheMetrics()
      {
          return new CacheMetrics
          {
              MemoryCacheHitRate = _memoryHits / (double)_memoryRequests,
              DistributedCacheHitRate = _distributedHits / (double)_distributedRequests,
              AverageRenderTime = _renderTimes.Average(),
              CacheSize = _memoryCache.Count,
              MostAccessedPages = GetTopPages()
          };
      }
  }

  2. 性能追踪

  public class PageRenderingMiddleware
  {
      public async Task InvokeAsync(HttpContext context, RequestDelegate next)
      {
          var stopwatch = Stopwatch.StartNew();

          await next(context);

          stopwatch.Stop();

          if (context.Request.Path.StartsWithSegments("/page"))
          {
              _logger.LogInformation("Page rendered in {ElapsedMs}ms: {Path}",
                  stopwatch.ElapsedMilliseconds, context.Request.Path);
          }
      }
  }