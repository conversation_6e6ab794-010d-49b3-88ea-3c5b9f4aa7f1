# 页面配置管理功能开发方案

## 概述

本功能旨在为管理员后台提供可视化的页面配置管理界面，支持页面的CRUD操作、组件的可视化编辑，以及安全的数据存储机制。

## 功能需求分析

### 核心功能
1. **页面列表管理**：展示所有页面配置，包含关键信息
2. **页面编辑器**：可视化的组件拖拽和配置界面
3. **数据加密存储**：将页面配置以可逆加密方式存储到数据库
4. **多语言支持**：页面名称和配置支持多语言

### 数据结构
根据 `page-config-demo.json` 和现有 `PageConfiguration` 实体，页面配置包含：
- 页面基本信息（pageKey, route, layoutTemplate）
- 组件列表（componentDefinitionId, templateKey, parametersJson, displayOrder, isVisible）
- 性能配置（图片懒加载、组件优化等）
- 缓存配置
- 访问控制配置

## 技术方案设计

### 1. 数据库层改进

#### 1.1 新增 PageConfigContent 实体
创建新文件：`src/Model/Entities/Pages/PageConfigContent.cs`

这是页面配置的详细内容实体，对应 JSON 配置的完整结构：

```csharp
public class PageConfigContent
{
    public string PageKey { get; set; } = string.Empty;
    public string Route { get; set; } = string.Empty;
    public string LayoutTemplate { get; set; } = "_Layout";
    public List<PageComponentConfig> Components { get; set; } = new();
    public string? GeneratedViewPath { get; set; }
    public PagePerformanceConfig Performance { get; set; } = new();
    public PageCacheConfiguration CacheConfig { get; set; } = new();
    public PageAccessControlConfig AccessControl { get; set; } = new();
}

public class PageComponentConfig
{
    public string ComponentDefinitionId { get; set; } = string.Empty;
    public string TemplateKey { get; set; } = string.Empty;
    public string ParametersJson { get; set; } = string.Empty;
    public int DisplayOrder { get; set; }
    public bool IsVisible { get; set; } = true;
}

public class PagePerformanceConfig
{
    public bool EnableImageLazyLoading { get; set; } = true;
    public bool EnableComponentLazyLoading { get; set; } = false;
    public int MaxComponentsPerPage { get; set; } = 20;
    public bool EnableBundleOptimization { get; set; } = true;
    public int ImageQuality { get; set; } = 85;
    public bool EnableWebpFormat { get; set; } = true;
}

public class PageCacheConfiguration
{
    public int CacheDurationMinutes { get; set; } = 60;
    public List<string> CacheVaryByParams { get; set; } = new();
    public bool EnableCDNCache { get; set; } = true;
    public List<string> CacheInvalidationTags { get; set; } = new();
    public bool EnableOutputCache { get; set; } = true;
    public string CacheProfile { get; set; } = string.Empty;
}

public class PageAccessControlConfig
{
    public bool RequireAuthentication { get; set; } = false;
    public List<string> RequiredRoles { get; set; } = new();
    public List<string> AllowedUsers { get; set; } = new();
    public DateTime? PublishDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public List<string> AllowedCountries { get; set; } = new();
    public bool EnableGeoRestriction { get; set; } = false;
}
```

#### 1.2 简化 PageConfiguration 实体
修改现有的 `PageConfiguration` 实体，使其作为元数据容器：

```csharp
// 在 src/Model/Entities/Pages/PageConfiguration.cs 中修改为：
public class PageConfiguration
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;
    
    // 多语言页面名称
    public Dictionary<string, string> Name { get; set; } = new();
    
    // 加密的PageConfigContent JSON字符串
    public string Config { get; set; } = string.Empty;
    
    public PageStatus Status { get; set; }
    public DateTime? PublishDate { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
}
```

#### 1.3 服务层扩展
扩展 `PageConfigurationService` 以支持：
- 加密/解密 `PageConfigContent` 对象
- 页面配置的完整CRUD操作
- 按状态筛选页面
- `PageConfigContent` 和 `PageConfiguration` 之间的转换

### 2. 加密工具开发

#### 2.1 加密工具类位置
`src/Utility/PageConfigurationCrypto.cs`

#### 2.2 加密方案
- **加密算法**：AES-256-GCM（提供认证加密）
- **密钥管理**：从配置文件获取，支持密钥轮转
- **编码方式**：Base64编码存储

```csharp
public static class PageConfigurationCrypto
{
    /// <summary>
    /// 将PageConfigContent对象加密为字符串
    /// </summary>
    public static string EncryptPageConfig(PageConfigContent configContent, string encryptionKey);
    
    /// <summary>
    /// 将加密字符串解密为PageConfigContent对象
    /// </summary>
    public static PageConfigContent? DecryptPageConfig(string encryptedConfig, string encryptionKey);
    
    /// <summary>
    /// 验证PageConfigContent对象格式是否正确
    /// </summary>
    public static bool ValidatePageConfigContent(PageConfigContent configContent);
    
    /// <summary>
    /// 内部方法：序列化对象为JSON字符串
    /// </summary>
    private static string SerializeToJson(PageConfigContent configContent);
    
    /// <summary>
    /// 内部方法：从JSON字符串反序列化对象
    /// </summary>
    private static PageConfigContent? DeserializeFromJson(string jsonString);
}
```

### 3. 控制器层设计

#### 3.1 新增 PageConfigurationController
路径：`src/Web/Controllers/Admin/PageConfigurationController.cs`

**主要Action：**
- `Index()` - 页面列表
- `Create()` - 新增页面（GET/POST）
- `Edit(string id)` - 编辑页面（GET/POST）
- `Delete(string id)` - 删除页面
- `Publish(string id)` - 发布页面
- `Unpublish(string id)` - 取消发布
- `GetComponentData(string componentId)` - 获取组件配置数据（AJAX）

#### 3.2 API设计
遵循RESTful设计原则：
```
GET    /Admin/PageConfiguration          - 页面列表
GET    /Admin/PageConfiguration/Create   - 新增页面表单
POST   /Admin/PageConfiguration/Create   - 提交新增页面
GET    /Admin/PageConfiguration/Edit/{id} - 编辑页面表单
POST   /Admin/PageConfiguration/Edit/{id} - 提交编辑页面
DELETE /Admin/PageConfiguration/{id}     - 删除页面
POST   /Admin/PageConfiguration/Publish/{id}   - 发布页面
POST   /Admin/PageConfiguration/Unpublish/{id} - 取消发布
```

### 4. 视图模型设计

#### 4.1 PageConfigurationListViewModel
```csharp
public class PageConfigurationListViewModel
{
    public List<PageConfigurationListItemViewModel> Pages { get; set; } = new();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; } = 20;
}

public class PageConfigurationListItemViewModel
{
    public string Id { get; set; } = string.Empty;
    public Dictionary<string, string> Name { get; set; } = new();
    public string PageKey { get; set; } = string.Empty;
    public string Route { get; set; } = string.Empty;
    public PageStatus Status { get; set; }
    public DateTime? PublishDate { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public int ComponentCount { get; set; }
}
```

#### 4.2 PageConfigurationEditViewModel
```csharp
public class PageConfigurationEditViewModel
{
    // PageConfiguration 元数据字段
    public string Id { get; set; } = string.Empty;
    public Dictionary<string, string> Name { get; set; } = new();
    public PageStatus Status { get; set; }
    public DateTime? PublishDate { get; set; }
    
    // PageConfigContent 详细配置字段
    public string PageKey { get; set; } = string.Empty;
    public string Route { get; set; } = string.Empty;
    public string LayoutTemplate { get; set; } = "_Layout";
    public List<PageComponentViewModel> Components { get; set; } = new();
    public string? GeneratedViewPath { get; set; }
    
    // 配置选项
    public PagePerformanceConfigViewModel Performance { get; set; } = new();
    public PageCacheConfigViewModel CacheConfig { get; set; } = new();
    public PageAccessControlViewModel AccessControl { get; set; } = new();
    
    // 可用组件列表（用于编辑器）
    public List<ComponentDefinitionViewModel> AvailableComponents { get; set; } = new();
}

public class PageComponentViewModel
{
    public string ComponentDefinitionId { get; set; } = string.Empty;
    public string TemplateKey { get; set; } = string.Empty;
    public string ParametersJson { get; set; } = string.Empty;
    public int DisplayOrder { get; set; }
    public bool IsVisible { get; set; } = true;
    
    // 用于UI显示的附加字段
    public string ComponentName { get; set; } = string.Empty;
    public string ComponentDescription { get; set; } = string.Empty;
    public List<string> AvailableTemplates { get; set; } = new();
}
```

### 5. 前端UI设计

#### 5.1 页面列表设计
采用 Flowbite 的 Table 组件：
- **布局**：响应式表格布局
- **列字段**：
  - 页面名称（多语言显示）
  - 页面Key
  - 路由
  - 状态（Published/Draft/Disabled）
  - 组件数量
  - 最后更新时间
  - 操作按钮
- **操作功能**：
  - 新增页面按钮
  - 编辑/删除/发布/取消发布
  - 状态筛选
  - 分页导航

#### 5.2 页面编辑器设计
**推荐可视化组件方案：**

##### 方案A：基于 Sortable.js 的拖拽编辑器
- **左侧面板**：可用组件列表（按类型分组）
- **中央区域**：页面预览和组件排序区域
- **右侧面板**：选中组件的配置属性面板

**技术选型：**
- `Sortable.js` - 拖拽排序
- `Alpine.js` - 轻量级响应式交互（已在项目中使用）
- `Flowbite` 组件 - Modal、Form、Tabs等

**界面布局：**
```html
<div class="page-editor-container">
  <!-- 组件库面板 -->
  <div class="component-library">
    <div class="component-category">
      <h3>布局组件</h3>
      <div class="component-item" draggable="true">Hero组件</div>
      <div class="component-item" draggable="true">Header组件</div>
    </div>
  </div>
  
  <!-- 页面预览面板 -->
  <div class="page-preview">
    <div class="sortable-components">
      <!-- 动态生成的组件列表 -->
    </div>
  </div>
  
  <!-- 属性配置面板 -->
  <div class="properties-panel">
    <!-- 选中组件的配置表单 -->
  </div>
</div>
```

##### 方案B：模态框配置方式
如果拖拽实现复杂，可采用简化方案：
- 使用表格展示当前页面组件列表
- 提供"添加组件"按钮，打开模态框选择组件类型
- 每个组件行提供"配置"按钮，打开配置模态框
- 支持组件的上移下移、删除操作

#### 5.3 组件配置表单
根据不同组件类型，动态生成配置表单：
- **Hero组件**：标题、副标题、背景图片、按钮配置
- **Content组件**：富文本内容、布局选项
- **Media组件**：图片/视频URL、显示选项
- **Form组件**：表单字段定义、提交配置

### 6. 安全性考虑

#### 6.1 权限控制
- 所有页面配置操作需要管理员权限
- 使用 `[Authorize]` 特性保护Controller
- 记录操作日志（创建者、修改者、操作时间）

#### 6.2 数据验证
- 页面Key唯一性验证
- JSON配置格式验证
- 路由格式验证
- 组件参数有效性验证

#### 6.3 加密安全
- 定期轮转加密密钥
- 密钥存储在安全配置中（环境变量或KeyVault）
- 加密数据完整性校验

### 7. 性能优化

#### 7.1 缓存策略
- 页面配置列表缓存（15分钟）
- 组件定义缓存（30分钟）
- 发布页面时清除相关缓存

#### 7.2 分页和搜索
- 页面列表支持分页（默认20条/页）
- 支持按页面名称、状态筛选
- 使用索引优化查询性能

#### 7.3 前端性能
- 组件列表懒加载
- 图片预览懒加载
- 表单数据防抖提交

## 开发计划

### 第一阶段：基础架构（2-3天）
1. 创建 `PageConfigContent` 实体类及相关配置类
2. 简化现有 `PageConfiguration` 实体，使其作为元数据容器
3. 创建加密工具类 `PageConfigurationCrypto`
4. 扩展 `PageConfigurationService` 以支持新的实体结构
5. 设计和创建视图模型

### 第二阶段：页面列表功能（2天）
1. 实现页面列表显示
2. 添加基本CRUD操作（新增、删除、发布状态切换）
3. 实现分页和筛选功能
4. 添加多语言资源

### 第三阶段：页面编辑器（3-4天）
1. 设计和实现编辑器UI布局
2. 集成拖拽功能或模态框配置
3. 实现组件配置表单
4. 添加预览功能

### 第四阶段：测试和优化（2天）
1. 单元测试和集成测试
2. 性能优化和缓存实现
3. 安全性测试
4. 用户界面优化

## 文件结构

```
src/
├── Model/
│   └── Entities/Pages/
│       ├── PageConfiguration.cs (修改 - 简化为元数据容器)
│       └── PageConfigContent.cs (新增 - 页面配置详细内容)
├── Service/Pages/
│   └── PageConfigurationService.cs (扩展)
├── Utility/
│   └── PageConfigurationCrypto.cs (新增)
├── Web/
│   ├── Controllers/Admin/
│   │   └── PageConfigurationController.cs (新增)
│   ├── ViewModels/Admin/
│   │   ├── PageConfigurationListViewModel.cs (新增)
│   │   ├── PageConfigurationEditViewModel.cs (新增)
│   │   └── ComponentDefinitionViewModel.cs (新增)
│   ├── Views/Admin/
│   │   └── PageConfiguration/
│   │       ├── Index.cshtml (新增)
│   │       ├── Create.cshtml (新增)
│   │       ├── Edit.cshtml (新增)
│   │       └── _ComponentEditor.cshtml (新增)
│   ├── Resources/
│   │   ├── AdminResource.resx (更新)
│   │   ├── AdminResource.en.resx (更新)
│   │   └── AdminResource.ja.resx (更新)
│   └── wwwroot/js/
│       ├── page-configuration.js (新增)
│       └── component-editor.js (新增)
```

## 配置要求

### appsettings.json 新增配置
```json
{
  "PageConfiguration": {
    "EncryptionKey": "your-256-bit-encryption-key-here",
    "CacheExpirationMinutes": 15,
    "MaxComponentsPerPage": 50
  }
}
```

## 多语言资源键

在 `AdminResource` 中需要添加的资源键：
```
PageConfigurationTitle
PageConfigurationList
CreateNewPage
EditPage
PageName
PageKey
Route
Status
ComponentCount
LastUpdated
Actions
Publish
Unpublish
Published
Draft
Disabled
PageEditor
ComponentLibrary
PropertiesPanel
AddComponent
SavePage
PreviewPage
DeleteConfirm
ComponentConfiguration
MoveUp
MoveDown
RemoveComponent
```

## 总结

本方案遵循了现有项目的架构模式和开发规范，采用了Flowbite + Tailwind CSS的UI框架，支持多语言和主题系统。加密存储确保了配置数据的安全性，可视化编辑器提供了良好的用户体验。整个方案具有良好的扩展性和可维护性，符合企业级应用的开发标准。