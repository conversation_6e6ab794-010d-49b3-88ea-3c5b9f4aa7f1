# MlSoft.Sites 项目结构说明

## 项目概述
这是一个基于 C# 9.0 + ASP.NET Core MVC + MongoDB 的日本企业官网模板系统，支持多语言存储和动态组件架构。

## 项目结构

```
src/
├── MlSoft.Sites.sln                    # 解决方案文件
├── Model/                               # 实体模型项目
│   ├── MlSoft.Sites.Model.csproj
│   ├── Entities/                        # 实体定义
│   │   ├── Business/                    # 业务相关实体
│   │   │   ├── BusinessDivision.cs     # 业务部门
│   │   │   └── ProductService.cs       # 产品服务
│   │   ├── Common/                      # 通用实体
│   │   │   ├── ContactInfo.cs          # 联系信息
│   │   │   └── GeoLocation.cs          # 地理位置
│   │   ├── Company/                     # 企业信息实体
│   │   │   ├── Company.cs              # 企业基础信息
│   │   │   └── CompanyLocation.cs      # 企业据点
│   │   ├── Components/                  # 动态组件实体
│   │   │   ├── ComponentDefinition.cs  # 组件定义
│   │   │   └── ComponentTemplate.cs    # 组件模板
│   │   ├── CSR/                         # CSR活动实体
│   │   │   └── CSRActivity.cs
│   │   ├── Enums/                       # 枚举定义
│   │   │   └── CommonEnums.cs
│   │   ├── History/                     # 企业历史实体
│   │   │   └── CompanyHistory.cs
│   │   ├── Investor/                    # 投资者关系实体
│   │   │   ├── FinancialReport.cs       # 财务报告
│   │   │   └── ShareholderMeeting.cs    # 股东大会
│   │   ├── LocaleFields/                # 多语言字段定义
│   │   │   └── CompanyLocaleFields.cs
│   │   ├── News/                        # 新闻公告实体
│   │   │   └── NewsAnnouncement.cs
│   │   ├── Organization/                # 组织架构实体
│   │   │   ├── Executive.cs             # 管理层信息
│   │   │   └── OrganizationStructure.cs # 组织架构
│   │   ├── Pages/                       # 页面配置实体
│   │   │   ├── PageConfiguration.cs     # 页面配置
│   │   │   └── PageVersion.cs           # 页面版本
│   │   ├── Recruitment/                 # 招聘信息实体
│   │   │   ├── EmployeeInterview.cs     # 员工访谈
│   │   │   └── JobPosition.cs           # 招聘职位
│   │   └── Settings/                    # 系统设置实体
│   │       ├── SEOSettings.cs           # SEO设置
│   │       └── SiteSettings.cs          # 网站设置
│   └── Extensions/                      # 扩展方法
│       └── LocaleExtensions.cs          # 多语言扩展方法
├── Service/                             # 业务服务项目
│   ├── MlSoft.Sites.Service.csproj
│   ├── Base/                            # 基础服务
│   │   └── MongoBaseService.cs          # MongoDB通用基类
│   ├── Business/                        # 业务服务
│   │   ├── BusinessDivisionService.cs
│   │   └── ProductServiceService.cs
│   ├── Company/                         # 企业服务
│   │   ├── CompanyLocationService.cs
│   │   └── CompanyService.cs
│   ├── Components/                      # 组件服务
│   │   └── ComponentDefinitionService.cs
│   ├── News/                            # 新闻服务
│   │   └── NewsAnnouncementService.cs
│   ├── Pages/                           # 页面服务
│   │   └── PageConfigurationService.cs
│   └── Seeders/                         # 数据种子
│       └── DataSeeder.cs                # 示例数据生成器
└── Web/                                 # Web前端项目
    ├── MlSoft.Sites.Web.csproj
    ├── Program.cs                       # 应用程序入口
    ├── appsettings.json                 # 配置文件
    ├── package.json                     # Node.js依赖配置
    ├── tailwind.config.js               # Tailwind CSS配置
    ├── postcss.config.js                # PostCSS配置
    ├── Controllers/                     # 控制器
    │   ├── CompanyController.cs         # 企业信息控制器
    │   ├── HomeController.cs            # 首页控制器
    │   ├── NewsController.cs            # 新闻控制器
    │   └── ComponentController.cs       # 组件API控制器
    ├── Views/                           # 视图
    │   ├── Company/
    │   │   └── Index.cshtml             # 企业列表页面
    │   ├── News/
    │   │   └── Index.cshtml             # 新闻列表页面
    │   ├── Home/
    │   │   ├── Index.cshtml             # 首页
    │   │   ├── Components.cshtml        # 组件展示页
    │   │   └── ComponentTest.cshtml     # 组件测试页
    │   └── Shared/
    │       ├── _Layout.cshtml           # 主布局页面
    │       ├── _Header.cshtml           # 头部组件
    │       ├── _Footer.cshtml           # 底部组件
    │       └── Components/              # 动态组件视图
    │           ├── _HeaderComponent.cshtml
    │           ├── _HeroComponent.cshtml
    │           ├── _ContentComponent.cshtml
    │           ├── _NavigationComponent.cshtml
    │           ├── _FooterComponent.cshtml
    │           ├── _FormComponent.cshtml
    │           └── _MediaComponent.cshtml
    ├── Models/                          # 视图模型
    │   ├── ErrorViewModel.cs            # 错误视图模型
    │   └── Components/                  # 组件视图模型
    │       ├── HeaderComponentViewModel.cs
    │       ├── HeroComponentViewModel.cs
    │       ├── ContentComponentViewModel.cs
    │       ├── NavigationComponentViewModel.cs
    │       ├── FooterComponentViewModel.cs
    │       ├── FormComponentViewModel.cs
    │       └── MediaComponentViewModel.cs
    ├── Services/                        # 前端服务
    │   └── ComponentRenderService.cs    # 组件渲染服务
    ├── wwwroot/                         # 静态资源
    │   ├── css/
    │   │   ├── site.css                 # 源CSS文件（包含Tailwind指令）
    │   │   ├── site.min.css             # 编译后的CSS文件
    │   │   └── components.css           # 自定义组件样式
    │   ├── js/
    │   │   ├── site.js                  # 主JavaScript文件
    │   │   └── components.js            # 组件相关JavaScript
    │   ├── images/                      # 图片资源
    │   │   ├── logo.svg                 # 网站Logo
    │   │   └── placeholder.jpg          # 占位图片
    │   ├── lib/                         # 第三方库
    │   │   └── jquery/
    │   └── favicon.ico                  # 网站图标
    └── node_modules/                    # Node.js依赖包（开发时）
```

## 技术栈

### 后端技术
- **框架**: ASP.NET Core MVC (.NET 9.0)
- **语言**: C# 9.0
- **数据库**: MongoDB
- **身份认证**: AspNetCore.Identity.Mongo
- **ORM**: MongoDB.Driver

### 前端技术
- **视图引擎**: Razor Pages
- **CSS框架**: Tailwind CSS 3.x + Flowbite 2.x
- **UI组件库**: Flowbite Components (基于Tailwind CSS)
- **JavaScript**: 原生JavaScript + Flowbite JS (可扩展)

## 核心特性

### 1. 多语言支持
- 使用 `Dictionary<string, LocaleFields>` 结构存储多语言数据
- 支持日语(ja)、英语(en)、中文(zh)
- 提供语言回退机制：ja → en → zh → 第一个可用值

### 2. MongoDB通用基类
- `MongoBaseService<T>` 提供通用的CRUD操作
- 支持分页查询、条件查询、字段更新等
- 所有业务服务都继承此基类

### 3. 实体设计
- 涵盖日本企业官网的所有核心模块
- 支持企业信息、新闻公告、产品服务、投资者关系等
- 预留动态组件系统扩展

### 4. 数据种子
- 提供示例数据生成器
- 开发环境自动初始化测试数据

### 5. Flowbite + Tailwind CSS UI框架
- **Tailwind CSS 3.x**: 实用优先的CSS框架，提供原子化样式类
- **Flowbite 2.x**: 基于Tailwind CSS的组件库，提供预制UI组件
- **响应式设计**: 支持移动端、平板、桌面端的响应式布局
- **主题定制**: 支持深色/浅色主题切换
- **组件丰富**: 包含导航、表单、模态框、轮播图等常用组件

## 配置说明

### MongoDB连接配置
```json
{
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "MlSoftSites"
  }
}
```

### 身份认证配置
- 使用AspNetCore.Identity.Mongo
- 密码策略已简化（开发环境）
- 支持用户注册和登录

## 使用示例

### 1. 创建多语言企业信息
```csharp
var company = new Company
{
    CompanyCode = "SAMPLE_CORP",
    Locale = new Dictionary<string, CompanyLocaleFields>
    {
        ["ja"] = new CompanyLocaleFields
        {
            CompanyName = "株式会社サンプル",
            CompanyDescription = "革新的なソリューションを提供"
        },
        ["en"] = new CompanyLocaleFields
        {
            CompanyName = "Sample Corporation",
            CompanyDescription = "Providing innovative solutions"
        }
    }
};
```

### 2. 获取本地化数据
```csharp
var localizedInfo = company.Locale.GetLocale("ja");
Console.WriteLine(localizedInfo.CompanyName); // 株式会社サンプル
```

### 3. 使用服务类
```csharp
public class CompanyController : Controller
{
    private readonly CompanyService _companyService;
    
    public async Task<IActionResult> Index()
    {
        var companies = await _companyService.GetActiveCompaniesAsync();
        return View(companies);
    }
}
```

## 运行项目

### 1. 环境要求
- .NET 9.0 SDK
- MongoDB 服务器

### 2. 启动步骤
```bash
# 1. 还原包
dotnet restore src/MlSoft.Sites.sln

# 2. 构建项目
dotnet build src/MlSoft.Sites.sln

# 3. 运行Web项目
dotnet run --project src/Web/MlSoft.Sites.Web.csproj
```

### 3. 访问地址
- 应用程序: https://localhost:5001
- 企业信息: https://localhost:5001/Company
- 新闻列表: https://localhost:5001/News

## 扩展说明

### 1. 添加新实体
1. 在 `Model/Entities` 下创建实体类
2. 在 `Model/Entities/LocaleFields` 下添加多语言字段定义
3. 在 `Service` 下创建对应的服务类
4. 在 `Web/Controllers` 下创建控制器

### 2. 添加新语言
1. 在实体的 `Locale` 字典中添加新语言键值对
2. 更新 `SiteSettings.SupportedLanguages` 配置
3. 在视图中添加语言切换逻辑

### 3. 自定义组件
1. 实现 `ComponentDefinition` 实体
2. 创建对应的 Razor 视图
3. 在 `PageConfiguration` 中配置组件使用

## 注意事项

1. **C# 版本**: 项目使用 C# 9.0，避免使用更高版本的语法特性
2. **命名空间**: 使用传统命名空间语法，不使用文件作用域命名空间
3. **MongoDB**: 确保MongoDB服务正在运行
4. **多语言**: 所有面向用户的文本都应支持多语言
5. **性能**: 大量数据时考虑使用分页和索引优化

## Flowbite + Tailwind CSS 配置

### 1. 安装和配置
```bash
# 安装Tailwind CSS
npm install -D tailwindcss
npx tailwindcss init

# 安装Flowbite
npm install flowbite
```

### 2. Tailwind配置文件 (tailwind.config.js)
```javascript
module.exports = {
  content: [
    "./src/Web/Views/**/*.cshtml",
    "./src/Web/wwwroot/**/*.js",
    "./node_modules/flowbite/**/*.js"
  ],
  theme: {
    extend: {
      colors: {
        primary: {"50":"#eff6ff","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8"}
      }
    },
  },
  plugins: [
    require('flowbite/plugin')
  ],
}
```

### 3. 主要组件使用
- **导航栏**: Flowbite Navbar组件，支持响应式菜单
- **轮播图**: Flowbite Carousel组件，用于Hero区域
- **卡片**: Flowbite Card组件，用于新闻、产品展示
- **表单**: Flowbite Form组件，包含验证样式
- **模态框**: Flowbite Modal组件，用于弹窗显示
- **按钮**: Flowbite Button组件，多种样式变体

## 后续开发建议

1. 添加管理后台界面（使用Flowbite Admin模板）
2. 实现动态组件的可视化编辑
3. 添加文件上传和管理功能
4. 实现SEO优化和静态页面生成
5. 添加缓存机制提升性能
6. 实现API接口支持前后端分离
7. 集成Flowbite的高级组件（数据表格、图表等）
8. 实现主题定制和品牌色彩配置