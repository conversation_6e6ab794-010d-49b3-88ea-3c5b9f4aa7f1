AI开发约束设置要点，必须遵守
- 本站是基于 Flowbite + Tailwind CSS 框架样式，在开发过程中，请尽可能使用 Flowbite组件和Tailwind CSS样式。
- 本站支持主题样式文件切换，因此，尽可能复用现有的样式，如果有新增样式，必须 将 wwwroot/css/active-theme.css，和 wwwroot/css/themes下所有主题样式都添加
- 本站支持暗黑模式，在界面开发过程中，必须同时兼顾明亮和暗黑的搭配，如有新增式，参考上一条
- wwwroot/css/input.css 这是样式文件非必要时不允许修改
- 涉及到全局或通用前端JS操作时，将JS方式放到 wwwroot/js/site.js 中
- 多语言
    - 目前支持 zh(默认),en,ja，页面开发的文本内容必须存放到对应的资源文件中，如果不存在，请创建

基于上面内容，帮我做一个 AI agent通用的md文件，以便不同的AI Agent都可以使用

把当前项目文件结构也列一下，并做个简单说明，也放刚才这个文件中