# 分页组件使用指南 (Pagination Component Usage Guide)

## 概述 (Overview)

这是一个通用的、支持多语言和主题的分页组件，适用于各种列表页面。组件支持多种配置选项，能够适应不同的使用场景。

## 基本使用 (Basic Usage)

### 1. 在控制器中设置分页数据 (Controller Setup)

```csharp
public async Task<IActionResult> Index(int page = 1, int pageSize = 10)
{
    var totalCount = await _service.GetTotalCountAsync();
    var items = await _service.GetPagedItemsAsync(page, pageSize);

    // 设置分页参数
    ViewBag.CurrentPageIndex = page;
    ViewBag.PageSize = pageSize;
    ViewBag.TotalCount = totalCount;
    ViewBag.PageUrl = new Func<int, string>(pageNum => Url.Action("Index", new { page = pageNum }));

    return View(items);
}
```

### 2. 在视图中使用组件 (View Usage)

```html
<!-- 基本使用 -->
@await Html.PartialAsync("_Pagination")

<!-- 在列表底部使用 -->
<div class="mt-6">
    @await Html.PartialAsync("_Pagination")
</div>
```

## 配置选项 (Configuration Options)

### 必需参数 (Required Parameters)

| ViewBag 属性 | 类型 | 说明 | 示例 |
|-------------|------|------|------|
| `CurrentPageIndex` | int | 当前页码 | `1` |
| `PageSize` | int | 每页数 | `10` |
| `TotalCount` | int | 总条数 | `10` |
| `PageUrl` | Func<int, string> | 页面URL生成函数 | `page => $"?page={page}"` |

### 可选参数 (Optional Parameters)

| ViewBag 属性 | 类型 | 默认值 | 说明 |
|-------------|------|-------|------|
| `TotalCount` | int | `0` | 总记录数 |
| `Info` | string | `""` | 自定义信息 |
| `DiffCount` | int | `2` | 当前页前后显示的页码数量 |
| `ShowFirstLast` | bool | `true` | 显示首页/末页按钮 |
| `ShowPrevNext` | bool | `true` | 显示上一页/下一页按钮 |
| `ShowPageNumbers` | bool | `true` | 显示页码按钮 |
| `ShowInfo` | bool | `true` | 显示页码信息 |
| `ShowTotalCount` | bool | `true` | 显示总记录数 |
| `Size` | string | `"md"` | 组件尺寸: `"sm"`, `"md"`, `"lg"` |

## 使用示例 (Usage Examples)

### 1. 完整配置示例 (Full Configuration)

```csharp
// 在控制器中
ViewBag.CurrentPageIndex = 3;
ViewBag.PageSize = 15;
ViewBag.TotalCount = 148;
ViewBag.DiffCount = 3; // 显示更多页码
ViewBag.PageUrl = new Func<int, string>(page =>
    Url.Action("Index", new { page, category = Request.Query["category"] }));
ViewBag.Size = "lg"; // 大号分页
ViewBag.Info = "数据更新时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm");
```

### 2. 简约配置示例 (Minimal Configuration)

```csharp
// 只显示基本分页功能
ViewBag.CurrentPageIndex = page;
ViewBag.TotalPages = totalPages;
ViewBag.PageUrl = new Func<int, string>(p => $"?page={p}");
ViewBag.ShowFirstLast = false;
ViewBag.ShowTotalCount = false;
ViewBag.ShowInfo = false;
ViewBag.Size = "sm";
```

### 3. 仅显示信息示例 (Info Only)

```csharp
// 当没有分页时，只显示信息
ViewBag.TotalPages = 0; // 不显示分页按钮
ViewBag.TotalCount = items.Count;
ViewBag.Info = "找到 " + items.Count + " 个结果";
```

## 主题和样式 (Theming and Styling)

### CSS 变量支持 (CSS Variables)

组件使用主题系统的 CSS 变量，会自动适应当前主题：

- `--color-primary-*`: 主色调变量
- `--text-primary`, `--text-secondary`, `--text-tertiary`: 文字颜色
- `--bg-primary`, `--bg-secondary`, `--bg-tertiary`: 背景色
- `--border-color`: 边框颜色

### 暗黑模式支持 (Dark Mode)

组件自动支持暗黑模式，通过 `dark:` 前缀和主题变量实现。

### 自定义样式 (Custom Styling)

可以通过覆盖 CSS 类来自定义样式：

```css
/* 自定义分页容器 */
.pagination-container {
    border-radius: 1rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 自定义按钮样式 */
.pagination-btn-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

## 多语言支持 (Internationalization)

组件使用 `SharedResource` 进行多语言支持，包含以下语言资源：

- 中文 (Chinese)
- 英文 (English)
- 日文 (Japanese)

### 支持的资源键 (Resource Keys)

- `Pagination_Total`: 总计
- `Pagination_Records`: 条记录 / records / 件
- `Pagination_Navigation`: 分页导航
- `Pagination_First`: 首页
- `Pagination_Prev`: 上一页
- `Pagination_Next`: 下一页
- `Pagination_Last`: 末页
- `Pagination_Page`: 第/Page/ページ
- `Pagination_Of`: 页，共/ of /
- `Pagination_Pages`: 页/pages/ページ

## 响应式设计 (Responsive Design)

组件在不同屏幕尺寸下会自动调整布局：

- **桌面端**: 水平排列，显示所有元素
- **移动端**: 垂直排列，紧凑显示

## 无障碍访问 (Accessibility)

组件遵循无障碍访问标准：

- 使用语义化 HTML (`nav`, `button`, `a`)
- 提供 `aria-label` 和 `aria-current` 属性
- 支持键盘导航
- 合理的颜色对比度

## 最佳实践 (Best Practices)

1. **合理设置页面大小**: 建议每页显示 10-50 项
2. **保持URL一致性**: 确保分页URL包含当前的筛选参数
3. **性能优化**: 对大数据集使用数据库分页而非内存分页
4. **用户体验**: 在加载新页面时提供加载指示器

## 故障排除 (Troubleshooting)

### 常见问题 (Common Issues)

1. **分页不显示**: 检查 `TotalPages` 是否大于 1
2. **样式异常**: 确认主题 CSS 文件已正确加载
3. **多语言未生效**: 检查 `SharedResource` 是否正确注册
4. **URL 不正确**: 检查 `PageUrl` 函数是否正确处理参数

### 调试提示 (Debug Tips)

```csharp
// 在视图中添加调试信息
@if (System.Diagnostics.Debugger.IsAttached)
{
    <div class="debug-info">
        <p>CurrentPageIndex: @ViewBag.CurrentPageIndex</p>
        <p>TotalPages: @ViewBag.TotalPages</p>
        <p>TotalCount: @ViewBag.TotalCount</p>
    </div>
}
```