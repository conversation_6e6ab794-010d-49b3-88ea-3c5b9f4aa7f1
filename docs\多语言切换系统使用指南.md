# 多语言切换系统使用指南

## 概述

本系统解决了多tab页面中语言切换的选择器冲突问题，提供了一个通用的多语言切换解决方案。

## 核心问题

在多tab页面中，不同tab可能使用相同的类名和ID格式，导致：
1. `document.querySelector` 选择到错误的元素
2. 语言切换影响其他tab的内容
3. 代码重复和维护困难

## 解决方案

### 1. 通用多语言切换函数

```javascript
window.switchLanguageTab(lang, context, options)
```

**参数：**
- `lang` (string): 语言代码，如 'zh', 'en', 'ja'
- `context` (string|HTMLElement, 可选): 上下文选择器或DOM元素
- `options` (Object, 可选): 配置选项

**配置选项：**
```javascript
{
    buttonClass: 'lang-tab-button',           // 语言按钮类名
    contentClass: 'lang-content',             // 语言内容类名
    activeClasses: ['active', 'border-primary-500', 'text-primary-600'],  // 激活状态类
    inactiveClasses: ['border-transparent', 'text-gray-500', 'dark:text-gray-400'],  // 非激活状态类
    contentIdPrefix: 'lang-'                  // 内容ID前缀
}
```

### 2. 自动上下文检测

系统会自动检测当前的多语言上下文，按以下优先级：
1. 模态框 (#historyModal, #companyInfoModal, 等)
2. 当前活动的标签页 (.tab-content.active)
3. 可见的容器 (.modal:not(.hidden))
4. 页面主体 (main, #main-content, body)

## 使用方法

### ⚠️ 重要：直接调用，不要封装

**后续开发中，请直接调用 `window.switchLanguageTab`，不要再进行封装！**

### 基本使用

```javascript
// 自动检测上下文
window.switchLanguageTab('zh');

// 指定上下文
window.switchLanguageTab('zh', '#myModal');

// 自定义配置
window.switchLanguageTab('zh', '#myModal', {
    buttonClass: 'custom-button',
    contentClass: 'custom-content'
});
```

### HTML结构要求

```html
<!-- 语言按钮 - 直接调用通用函数 -->
<button class="lang-tab-button" data-lang="zh" onclick="window.switchLanguageTab('zh')">
    中文
</button>
<button class="lang-tab-button" data-lang="en" onclick="window.switchLanguageTab('en')">
    English
</button>

<!-- 语言内容 -->
<div id="lang-zh" class="lang-content">
    中文内容
</div>
<div id="lang-en" class="lang-content hidden">
    English content
</div>
```

### 事件监听

系统会自动为以下元素添加点击事件：
1. 带有 `data-lang` 属性的 `.lang-tab-button` 元素
2. `onclick` 属性包含 `switchLanguageTab` 的元素

## 最佳实践

### 1. 命名规范

- 语言按钮：`{context}-lang-tab-button`
- 语言内容：`{context}-lang-content`
- 内容ID：`{context}-lang-{lang}`

例如：
- `executive-lang-tab-button`
- `executive-lang-content`
- `executive-lang-zh`

### 2. 上下文隔离

为不同的功能模块使用不同的上下文：

```javascript
// 历史记录模态框
window.switchLanguageTab('zh', '#historyModal');

// 公司信息标签页
window.switchLanguageTab('zh', '#companyInfoTab');

// 联系信息模态框
window.switchLanguageTab('zh', '#contactModal');
```

### 3. 特殊情况处理

只有在需要特殊逻辑（如TinyMCE重新初始化）时才创建专用函数：

```javascript
// 仅在有特殊需求时创建专用函数
window.switchCompanyInfoLanguageTab = function(lang) {
    const result = window.switchLanguageTab(lang, null, {
        buttonClass: 'company-info-lang-tab-button',
        contentClass: 'company-info-lang-content',
        contentIdPrefix: 'company-info-lang-'
    });
    
    if (result) {
        // 特殊逻辑：重新初始化TinyMCE
        setTimeout(() => {
            if (window.AdminTinyMCE) {
                const activeContent = document.getElementById(`company-info-lang-${lang}`);
                if (activeContent) {
                    const newTextareas = activeContent.querySelectorAll('.tinymce-editor');
                    newTextareas.forEach(textarea => {
                        if (!tinymce.get(textarea.id)) {
                            AdminTinyMCE.init(textarea, {
                                menubar: false,
                                height: 200
                            });
                        }
                    });
                }
            }
        }, 100);
    }
    
    return result;
};
```

## 工具函数

### 获取当前语言

```javascript
const currentLang = window.getCurrentLanguage('#myModal');
```

### 设置默认语言

```javascript
window.setDefaultLanguage('zh', '#myModal');
```

### 初始化所有语言切换器

```javascript
window.initLanguageSwitchers();
```

## 开发规范

### ✅ 推荐做法

1. **直接调用通用函数**：
   ```html
   <button onclick="window.switchLanguageTab('zh', '#myModal')">中文</button>
   ```

2. **使用标准配置**：
   ```javascript
   window.switchLanguageTab('zh', '#myModal', {
       buttonClass: 'my-lang-tab-button',
       contentClass: 'my-lang-content',
       contentIdPrefix: 'my-lang-'
   });
   ```

3. **利用自动上下文检测**：
   ```javascript
   // 系统会自动检测上下文
   window.switchLanguageTab('zh');
   ```

### ❌ 避免做法

1. **不要创建不必要的封装函数**：
   ```javascript
   // ❌ 不要这样做
   window.switchMyLanguageTab = function(lang) {
       return window.switchLanguageTab(lang, '#myModal', {
           buttonClass: 'my-lang-tab-button',
           contentClass: 'my-lang-content'
       });
   };
   
   // ✅ 直接调用
   window.switchLanguageTab('zh', '#myModal', {
       buttonClass: 'my-lang-tab-button',
       contentClass: 'my-lang-content'
   });
   ```

2. **不要重复实现切换逻辑**：
   ```javascript
   // ❌ 不要重新实现切换逻辑
   function mySwitchLanguage(lang) {
       // 手动切换按钮状态
       // 手动切换内容显示
   }
   ```

## 迁移指南

### 从旧系统迁移

1. **替换通用选择器**：
   ```javascript
   // 旧代码
   document.querySelectorAll('.lang-content')
   
   // 新代码
   context.querySelectorAll('.lang-content')
   ```

2. **使用通用函数**：
   ```javascript
   // 旧代码
   window.switchLanguageTab = function(lang) { /* 复杂逻辑 */ }
   
   // 新代码 - 直接调用
   window.switchLanguageTab('zh', '#myModal', {
       buttonClass: 'lang-tab-button',
       contentClass: 'lang-content'
   });
   ```

3. **更新HTML结构**：
   - 确保语言按钮有 `data-lang` 属性
   - 确保语言内容有正确的ID格式
   - 使用统一的类名规范

## 故障排除

### 常见问题

1. **语言切换不生效**
   - 检查上下文是否正确
   - 确认HTML结构符合要求
   - 查看控制台是否有错误信息

2. **选择器冲突**
   - 使用更具体的上下文选择器
   - 检查是否有重复的ID

3. **样式问题**
   - 确认激活/非激活状态的类名正确
   - 检查CSS优先级

### 调试技巧

```javascript
// 启用调试模式
window.switchLanguageTab = function(lang, context, options) {
    console.log('Switching to language:', lang);
    console.log('Context:', context);
    console.log('Options:', options);
    
    // 原有逻辑...
};
```

## 总结

这个通用多语言切换系统解决了多tab页面中的选择器冲突问题，提供了：

1. **自动上下文检测** - 无需手动指定上下文
2. **灵活的配置选项** - 支持不同的命名规范
3. **统一调用接口** - 所有多语言切换都使用 `window.switchLanguageTab`
4. **向后兼容** - 现有代码可以逐步迁移
5. **事件驱动** - 自动处理用户交互

**重要提醒：后续开发中，请直接调用 `window.switchLanguageTab`，不要再进行封装！**

通过使用这个系统，可以确保多语言切换功能在各种复杂的页面结构中都能正常工作。