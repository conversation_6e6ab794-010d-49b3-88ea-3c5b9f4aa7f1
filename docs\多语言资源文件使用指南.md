# ASP.NET Core 多语言资源文件使用指南

## 资源文件结构

```
src/Web/Resources/
├── SharedResource.cs                    # 共享资源标记类
├── SharedResource.zh-CN.resx          # 中文共享资源
├── SharedResource.en-US.resx          # 英文共享资源  
├── SharedResource.ja-JP.resx          # 日文共享资源
├── Views/
│   ├── Home.zh-CN.resx                # Home视图中文资源
│   ├── Home.en-US.resx                # Home视图英文资源
│   └── Home.ja-JP.resx                # Home视图日文资源
└── Controllers/
    └── HomeController.zh-CN.resx      # HomeController中文资源
```

## 在控制器中使用本地化

### 方法1：注入 IStringLocalizer

```csharp
using Microsoft.Extensions.Localization;
using MlSoft.Sites.Web.Resources;

public class HomeController : BaseController
{
    private readonly IStringLocalizer<SharedResource> _sharedLocalizer;
    private readonly IStringLocalizer<HomeController> _localizer;
    
    public HomeController(
        IStringLocalizer<SharedResource> sharedLocalizer,
        IStringLocalizer<HomeController> localizer,
        // 其他依赖...)
        : base(...)
    {
        _sharedLocalizer = sharedLocalizer;
        _localizer = localizer;
    }
    
    public IActionResult Index()
    {
        // 使用共享资源
        ViewBag.Welcome = _sharedLocalizer["Home"];
        
        // 使用控制器特定资源
        ViewBag.Message = _localizer["ValidationError"];
        
        return View();
    }
}
```

### 方法2：使用自定义本地化服务

```csharp
using MlSoft.Sites.Web.Services;

public class HomeController : BaseController
{
    private readonly ILocalizationService _localization;
    
    public HomeController(ILocalizationService localization, ...)
        : base(...)
    {
        _localization = localization;
    }
    
    public IActionResult Index()
    {
        ViewBag.Title = _localization.GetString("Home");
        ViewBag.Description = _localization.GetString("SiteDescription");
        
        return View();
    }
}
```

## 在视图中使用本地化

### 方法1：注入 IStringLocalizer

```html
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<MlSoft.Sites.Web.Resources.SharedResource> SharedLocalizer
@inject IViewLocalizer Localizer

<h1>@SharedLocalizer["Home"]</h1>
<p>@Localizer["Welcome"]</p>
<button>@SharedLocalizer["Save"]</button>
```

### 方法2：使用本地化服务

```html
@using MlSoft.Sites.Web.Services
@inject ILocalizationService Localization

<h1>@Localization.GetString("Home")</h1>
<p>@Localization.GetString("WelcomeMessage")</p>

<nav>
    <a href="@Html.MultilingualUrl("Index", "Home")">@Localization.GetString("Home")</a>
    <a href="@Html.MultilingualUrl("Index", "Company")">@Localization.GetString("Company")</a>
    <a href="@Html.MultilingualUrl("Index", "News")">@Localization.GetString("News")</a>
</nav>
```

## 数据注解本地化

在模型类中使用本地化的数据注解：

```csharp
using System.ComponentModel.DataAnnotations;

public class ContactModel
{
    [Display(Name = "Name")]
    [Required(ErrorMessage = "RequiredField")]
    public string Name { get; set; }
    
    [Display(Name = "Email")]
    [Required(ErrorMessage = "RequiredField")]
    [EmailAddress(ErrorMessage = "InvalidEmail")]
    public string Email { get; set; }
    
    [Display(Name = "Phone")]
    [Phone(ErrorMessage = "InvalidPhone")]
    public string? Phone { get; set; }
}
```

## 多语言 URL 生成

### 使用扩展方法

```html
<!-- 生成多语言链接 -->
<a href="@Html.MultilingualUrl("Index", "Home", "en")">English</a>
<a href="@Html.MultilingualUrl("About", "Company", "ja")">日本語版について</a>

<!-- 当前页面的其他语言版本 -->
<a href="@Html.CurrentPageMultilingualUrl("zh")">中文版</a>

<!-- 带参数的多语言链接 -->
<a href="@Html.MultilingualUrl("Details", "Product", "en", new { id = 123 })">Product Details</a>
```

### 使用语言切换器

```html
<!-- 下拉式语言选择器 -->
<language-switcher dropdown="true" show-flags="true" show-names="true" css-class="my-language-switcher"></language-switcher>

<!-- 列表式语言选择器 -->
<language-switcher dropdown="false" show-flags="true" show-names="false"></language-switcher>
```

## 常用的资源键约定

### 通用操作
- `Save` - 保存
- `Cancel` - 取消
- `Edit` - 编辑
- `Delete` - 删除
- `Create` - 创建
- `Search` - 搜索

### 导航菜单
- `Home` - 首页
- `About` - 关于我们
- `Contact` - 联系我们
- `News` - 新闻
- `Products` - 产品
- `Services` - 服务

### 状态消息
- `Success` - 成功
- `Error` - 错误
- `Loading` - 加载中
- `NoData` - 暂无数据

### 验证消息
- `RequiredField` - 必填字段
- `InvalidEmail` - 无效邮箱
- `ValidationError` - 验证错误

## 最佳实践

1. **资源键命名**：使用英文，采用 PascalCase 命名
2. **分类组织**：相关的资源键使用前缀分组（如 `Button.Save`, `Message.Success`）
3. **避免重复**：优先使用共享资源，特定场景才使用专门资源
4. **保持同步**：确保所有语言的资源文件包含相同的键
5. **使用注释**：在 .resx 文件中添加注释说明资源的用途

## 测试多语言

1. **URL测试**：
   - 默认语言：`/Home/Index` 
   - 英文：`/en/Home/Index`
   - 日语：`/ja/Home/Index`

2. **浏览器语言测试**：修改浏览器语言设置测试自动语言检测

3. **资源缺失测试**：删除某些资源键测试回退机制