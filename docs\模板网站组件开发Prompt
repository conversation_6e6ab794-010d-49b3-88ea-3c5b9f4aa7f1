

Todo List
- 共用组件提取（分页）

- 组件制作
- 页面
- 模板设计(主题，页面启用组件)


- AI 翻译



前台页面使用的 留言组件 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Message 页面和功能开发
- 基于 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/Messages/MessageInquiry.cs 实体
- /mnt/d/Codes/MlSoft.Sites/src/Web/Controllers/Admin/AdminMessageController.cs 控制器
做一个默认的组件，参考/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Hero组件
要求：
1、Default.cshtml
2、default.json
注意，按照目前目本企业官网主流的留言方式即可，基于现有实体来做，不要太复杂。


按照 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md，检查  /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Message 模块的以下几个：
1、样式硬编码，没有使用主题样式变量
2、缺少暗黑模式支持
3、资源文件硬编码，或者有资源键值，但没有加入到资源文件中。
注意   MessageType已经有资源项了，不要建重复了。


按照 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md，检查  /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/SiteSettings 模块的以下几个：
1、样式硬编码，没有使用主题样式变量
2、缺少暗黑模式支持
3、资源文件硬编码，或者有资源键值，但没有加入到资源文件中。



公共组件 数据本配置 功能开发
- 在 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/SiteSettings/Index.cshtml 公共组件设置tab页中，在当前选中的组件变体上显示一个 数据管理 按钮
- 点击 数据管理 弹出 一个对话框，显示该组件配置的表单动态渲染出来 的界面（/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/PageManage/Edit.cshtml， showComponentProperties(componentElement, componentIndex);）
- 保存数据为JSON格式，存放到 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/Settings/ComponentConfigData.cs，/mnt/d/Codes/MlSoft.Sites/src/Service/Settings/ComponentConfigDataService.cs）

遵循/mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md,帮我完成这个功能的功能。



在 form-field-renderer-extensions.js和form-field-renderer.js，如果json的对应Field有defaultValue，则在创建表单元素就将默认值填充。注意：只在创建元素时加默认值，这样后面的setForm会将保存的值覆盖，如果没有保存值，则正好显示之前的默认值。