功能设计

基于 /mnt/d/Codes/MlSoft.Sites/docs/日本企业官网特点.txt  和 /mnt/d/Codes/MlSoft.Sites/docs/动态组件_mvc_razor_架构方案.md， 我要做一个模板型展示网站，所涉及和企业相关的需要存放到数据库，有几点要注意：
- C# 9.0
- MongoDB数据库
- 多语言存储
- ASP.net Core Identify MongoDB数据库

先帮我整理文档中所涉及数据表实体，并按C#实体定义，先存为一个 md文档

多语言存储结构请按以下方式调整

Locale:{
    "en":{
        "字段1":"",
        "字段2":"",
        ....
    },
    "zh":{
        "字段1":"",
        "字段2":"",
        ....
    },
    "ja":{
        "字段1":"",
        "字段2":"",
        ....
    },
    ....
}

C# 用  Dictionary<string, 需要多语言存储的字段定义实体> Locale


## 项目搭建

- 项目根目录 src/
- 项目结构
    - 项目解决方案：src/MlSoft.Sites.sln
    - Web项目：Web前端
        - src/Web/MlSoft.Sites.Web.csproj
    - Model项目：实体定义
        - src/Model/MlSoft.Sites.Model.csproj
    - Service 项目：
        - src/Service/MlSoft.Sites.Service.csproj

如果需要其他项目，按上述结构创建

项目采用 C#9 ASP.net MVC + package AspNetCore.Identity.Mongo

需要注意的是：
mongodb操作，请封装一个通用基类，所有service中业务操作类都从该基类继承


## Web项目：Web前端
采用 Flowbite + Tailwind CSS，移除 bootstrap样式及相关库文件
移除jquery引用


Model数据库集合名称采用 首字母大写方式，现在是首字母是小写，统一调整下


## 页面组件开发

根据 D:\Codes\MlSoft.Sites\docs\动态组件_mvc_razor_架构方案.md方案和页面D:\Codes\MlSoft.Sites\src\Model\Entities\Pages\PageConfiguration.cs定义，以及 页面类型定义
public enum ComponentCategory
{
    Header, // 头部
    Hero, // 主视觉
    Content, // 内容
    Navigation, // 导航
    Footer, // 底部
    Form, // 表单
    Media, // 媒体
    Layout // 布局
}

先帮我创建上面这些组件，各一个，内容样式先不考虑，把先这个框架搭建起来。


基于Flowbite + Tailwind CSS，实现可以切换整站的颜色风格，这样开发时，各个各页面组件的样式该如何定义？
先给我一个方案

我想实现的场景是：

网站部署成功后，客户进入系统设置，选择我们内置好的几套颜色方案，确定之后，整个网站就会切换成这个风格，这个只是站点管理员操作，不是网站用户来切换。


===============================

Todo List

- 用户注册登录，修改密码


- 多语言支持及配置
- 授权机制
    - 授权机制
        - 客户Id
        - 可用语言
        - 有效期
        - 产品数量
        - ...

- 站点配置
    - 模板
    - 主题
    - 默认语言
    - 多语言设置

### 用户模块
    - 登录，修改密码
    注意：目前是演示站点，用户只有初始的管理员，所以暂时只做以下几个页面
        - 登陆
        - 后台管理(看板)
        - 设置：可以 修改密码，修改邮箱
基于 AspNetCore.Identity.Mongo       






### 多语言
- 采用asp.net core默认的国际化处理
- 可用语言从 appSettings.json 的 SupportedLanguages 读取
- 站点设置中，语言列表从上面配置中显示
- URL路径
    - 默认语言 /
    - 其他语言 /{code}/


 appSettings.json  增加了   "CurrentLanauage": "zh", 作为程序启动时的默认语言

 安照asp.net core国际化多语方式，帮我创建 zh,en,ja 三种语言的资源文件


### 管理员后台功能 需要新增的功能

- 站点设置
    - 基本信息设置
        - 站点名称
        - 域名，域名https证书
        - 默认语言

    - 模板主题设置
        - 模板选择
        - 主题选择
    
    - 全局组件设置
        - Header
        - Footer
        - Navigation

- 页面设置

- 新闻管理
- 产品管理


--------
站点设置优化：
1、移除以下几个设置，并同步清理实体和对应的service/controller方法
- 启用 HTTPS
- 站点描述
- 支持的语言


--  站点设置 - 基本信息 界面调整
1、根据配置的语言， 站点名称请在一行显示多个语言的输入框
2、appsetting.json增加了 UploadRootPath ，这是所有用户上传文件的根目录，请放到全局ViewData中
3、Logo, 网站图标 改为图片上传方式, 存放路径为 {UploadRootPath}+{EnumFolderType.Basic}, logo 在保存时转为webp格式
网站图标 ico方式，或者在 网站图标边上加个按钮，从logo中生成，调用后台方法转换
调用 /mnt/d/Codes/MlSoft.Sites/src/Utility/MlSoft.Sites.Utility/ImageHelper.cs 方法，但是 图片转ico方法，请帮我添加到这个图片类中。
4、数据存放路径为 文件名（不包括前面路径）

有几个问题：
1、站点名称，在一行显示；保存成功后，下次打开 没有显示 英文和日文的站点或名称
2、如果是首次上传logo,上传成功后，右侧应该出现 从logo转ico的按钮.
3、从logo转ico的按钮调整下样式，太长了，正常按钮大小即可。



### 暗黑模式支持
/mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/css/ 增加暗黑模式支持

帮我在 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Header 这个组件添加上暗黑切换，需要用到脚本 请放到 /mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js/site.js中。

能否做到在首次打开时，根据系统的暗黑来显示，先不改代码，给我一个方案




  1. 统一组件目录结构

  推荐结构：
  Views/Shared/Components/
  ├── Header/
  │   ├── Default.cshtml
  │   ├── Minimal.cshtml
  │   └── Corporate.cshtml
  ├── Footer/
  │   ├── Default.cshtml
  │   ├── Simple.cshtml
  │   └── Extended.cshtml
  └── Navigation/
      ├── Default.cshtml
      ├── Horizontal.cshtml
      └── Sidebar.cshtml

  2. 引入 ASP.NET Core View Components

  // 新增 ViewComponent 类
  public class HeaderViewComponent : ViewComponent
  {
      private readonly IComponentConfigService _configService;

      public async Task<IViewComponentResult> InvokeAsync(string variant = "Default")
      {
          var config = await _configService.GetHeaderConfigAsync();
          return View(variant, config);
      }
  }

  // 使用方式
  @await Component.InvokeAsync("Header", new { variant = "Corporate" })

  3. 组件版本与主题关联机制

  public class ComponentVariantSelector
  {
      public async Task<string> GetComponentVariantAsync(string componentName, string themeId)
      {
          // 根据主题选择对应的组件版本
          var themeConfig = await _themeService.GetThemeConfigAsync(themeId);
          return themeConfig.ComponentVariants.GetValueOrDefault(componentName, "Default");
      }
  }

  4. 改进配置缓存和热更新

  public class ComponentConfigService
  {
      private readonly IMemoryCache _cache;
      private readonly IConfiguration _config;

      // 支持配置热更新
      public async Task RefreshComponentConfigAsync(string componentName)
      {
          _cache.Remove($"component_config_{componentName}");
          await LoadComponentConfigAsync(componentName);

          // 发布配置变更事件
          await _eventBus.PublishAsync(new ComponentConfigUpdatedEvent(componentName));
      }
  }

  5. 组件注册和发现机制

  public class ComponentRegistry
  {
      public void RegisterComponent<TViewComponent>(string componentName, 
          List<string> availableVariants) where TViewComponent : ViewComponent
      {
          // 自动扫描和注册组件及其变体
      }

      public List<string> GetAvailableVariants(string componentName)
      {
          // 返回组件可用的所有版本
      }
  }

  6. 布局文件优化

  @* _Layout.cshtml 优化后 *@
  <!-- Header Component -->
  @await Component.InvokeAsync("Header", new {
      variant = ViewData["HeaderVariant"] ?? "Default"
  })

  <!-- Navigation Component -->
  @await Component.InvokeAsync("Navigation", new {
      variant = ViewData["NavigationVariant"] ?? "Horizontal"
  })

  @RenderBody()

  <!-- Footer Component -->
  @await Component.InvokeAsync("Footer", new {
      variant = ViewData["FooterVariant"] ?? "Default"
  })

  7. 管理界面组件配置

  // 组件配置控制器
  public class ComponentConfigController : Controller
  {
      [HttpGet]
      public async Task<IActionResult> ConfigureComponent(string componentName)
      {
          var availableVariants = _registry.GetAvailableVariants(componentName);
          var currentConfig = await _configService.GetComponentConfigAsync(componentName);

          return View(new ComponentConfigViewModel
          {
              ComponentName = componentName,
              AvailableVariants = availableVariants,
              CurrentConfig = currentConfig
          });
      }
  }

  核心改进点总结：

  1. 架构升级: 从 Partial View 升级到 View Components
  2. 统一规范: 建立一致的目录结构和命名规范
  3. 动态选择: 支持基于主题的组件版本自动选择
  4. 配置管理: 实现配置的热更新和版本管理
  5. 可扩展性: 支持组件的动态注册和发现
  6. 管理界面: 提供可视化的组件配置管理

  这个优化方案既保持了你现有设计思路的核心理念，又解决了当前实现中的问题，并且充分利用了 ASP.NET Core 框架的特性。



------

调整前请先阅读 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md，并最遵守

多语言调整
- 先将 admin 管理后台、Account 资源化，要求中文，英，日文，其中 中文作为默认语言和基本语言，不需要建 xxx.zh.resx， 直接用xxx.resx作为中文



在 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components 
- 每个组件目录下，生成一个json配置文件，格式：[{Category="", Id = "Default", Name = "默认样式", Description = "标准页头布局" }]
一个类型下有多个就有多条记录
Category
{
    Header, // 头部
    Hero, // 主视觉
    Content, // 内容
    Navigation, // 导航
    Footer, // 底部
    Form, // 表单
    Media, // 媒体
    Layout // 布局
}

 public List<ComponentVariantOption> HeaderVariants { get; set; } = new() 在程序启动时，就从这些配置读取

 Name = "默认样式", Description = "标准页头布局"
 改为 
   Name: {
    "zh": "自然绿",
    "en": "Nature Green",
    "ja": "ナチュラルグリーン"
  },
  Descriptions: {
    "zh": "清新自然的绿色主题，适合环保、健康类企业",
    "en": "Fresh and natural green theme, suitable for environmental and health companies",
    "ja": "新鮮で自然な緑のテーマ、環境や健康関連の企業に適しています"
  },
-------------



SEO相关组件
TDK
og
schema.org

标准组件，有对应参数则输出对应部分

先给我一个方案，确认后再开发、


---
动态页面思路

前端Web项目 = 模板 + 主题

模板 = 一组页面的组合
页面 = 一些组件的组合

通过Json格式配置前端所有页面，如
{
    "Id":"页面唯一Id",
    "Route":"路由地址",
    "Components":[
        {
            "Sort":"",
            "Id":"",            
            "Category":""
        }
    ]
}

程序启动时，先从这个配置文件将路由注册到路由器，由统一个控制器处理这些请求，并将组件按配置及次序输出。


按照这个方案，帮我实现它，注意
1、遵循 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md要求
2、缓存机制暂时没有Redis,但可以保留，如果不配置或不启用，就内存缓存。


现在的动态加载在处理组件和数据时和我设想的不同，看看我的想法是否正确

1、请求到达 PageController中，根据页面Id从数据库中获取该页面的组件数据，这些数据都是该页面的组件所需要的结构化数据，需要转为对应的组件数据，并存在页面级的ViewData中。
2、在cshtml view中，根据页面组件，使用 Component.InvokeAsync 方式加载组件。



  用户的想法是正确的。我需要重新实现动态组件系统：

  1. PageController: 
    - 获取页面配置数据
    - 解析每个组件的JSON参数
    - 将解析后的结构化数据转换为对应组件的ViewModel
    - 将这些ViewModel存储在ViewData中
  2. Dynamic.cshtml:
    - 使用@await Component.InvokeAsync(componentName, componentViewModel)来调用实际的ViewComponent
    - 移除当前的HTML占位符逻辑
  3. 组件数据处理:
    - 需要为每种组件类型创建对应的ViewModel
    - 需要一个组件数据转换服务来处理JSON参数到ViewModel的转换

  让我开始重新实现这个系统。

  
另外，页面配置只需要配置 该页面的Id，名称(多语言)，组件列表（组件Id，排序），即可。因为在管理后台，当设置该页面，会自动将组件需要的数据变成表单让用户填写，然后保存为该组件需要的结构化数据。
配置中不需要这些数据。


==================

网站缓存处理

SupportedLanguages: 程序启动注入，可直接注入获取

支持语言，默认语言和当前语言，Web项目中可直接使用

BaseController已加入全局ViewData中
ViewData["CurrentLanguage"] = currentLanguage?.Code;
ViewData["CurrentCulture"] = currentCulture.Name;
ViewData["DefaultLanguage"] = _defaultLanguage;
ViewData["SupportedLanguages"] = _supportedLanguages;
ViewData["IsDefaultLanguage"] = IsDefaultLanguage(currentLanguage?.Code);

 

站点配置(HeaderComponentConfig)


=============

我在做一个动态渲染的页面，使用asp.net core mvc，页面由各个组件构成，有什么好的办法，在配置时可以简单的设置下布局，比如 这2个组件放在一行，然后在渲染时按照配置的输出
给我一个方案，不用实现，我看看是否是我想要的。

====================================

系统数据结构
-----------

系统配置
    站点配置
        域名：用于SEO链接，sitemap链接等
        Logo:
        Favicon:
        站点名称
            - 用于默认一些地方没有填值时使用，比如标题
            - 需要支持多语言填写
        主题：当前应用的是哪个主题
        全局组件：如果页面中配置了，则用页面的组件
            - Header
            - Footer
            - Navigation
        
                

页面管理：（正常由开发人员配置好）
    - 页面配置：/mnt/d/Codes/MlSoft.Sites/design/pages/home-page-config.json 这个文件需要调整，配置不能和数据绑定，也就是这里只是组件的相关信息，没有内容信息。
        - 页面组件
        - 路由
        - 参数
        - 权限
        - 样式
    
- 页面数据管理：每个页面都会列出来，都可以配置，部署后会自动填充，根据需要进行修改
    - 页面数据：主要根据页面上的组件所需要的数据，有些是公用的，有些是这个组件特有的
        - 基本信息的维护，比如 会社详情中，企业的描述，图片等
        - 多语言(接入AI付费，使用，AI统一走我司接口，统一处理)
    - SEO 数据

    
        


企业信息
    基本信息


业务信息

新闻/通知

招聘

留言

自定义页面数据




====================

页面配置 功能，管理员后台使用
- 用于简单可视化编辑页面，添加组件
- 并将最终于页面数据存为指定的JSON格式，可逆加密存储到数据库 PageConfig 表中
- 页面配置原格式见 /mnt/d/Codes/MlSoft.Sites/design/pages/page-config-demo.json
- 功能包括
    - 页面列表：将所有页面都以列表方式展现出来，将重要的项显示在列表上
    - 列表操作：新增页面，编辑页面，禁用页面
    - 点击进入编辑页面的界面，推荐用什么样的可视化组件来做这个界面编辑
    - 保存时，需要采用可逆的加密，将 页面配置内容加密为文本存放到 PageConfig表中；
    - 加/解密方式 请放到 /mnt/d/Codes/MlSoft.Sites/src/Utility 项目作，新增为静态方式

给我一个详细的开发方案，形成一个md文件，先不要写代码。注意设计过程要遵循 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md 所定的规则。


好的，现在请按 /mnt/d/Codes/MlSoft.Sites/docs/页面配置管理功能开发方案.md 文档和遵循 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md 所定的规则，开发一步一步开发这个功能。



 请看下 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/PageConfiguration下的 Create.cshtml 和 Edit.cshtml 请共用一个view，保留一个，编辑和新增都用它。



帮我添加一组弹窗：用于替换 原生的 alert，confirm
- 放到site.js中
- 使用 现有主题机制，不要添加新变量
- 相关资源提取到sharedResource中
- 在  /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md 增加一个规则， 凡用需要用到 alert，confirm的，必须使用本次添加的这组方法


===============
页面编辑界面调整
- H1下方增加一个导航，返回或指向 页面列表
- 当前编辑页面已经占满整个屏幕了，导致左侧菜单被挤压变型了
- 中央区的 上方面编辑区调整：

    - 左右2档
        - 左侧
            - 页面名称
                多语言，在一行
            - 页面键，路由在一行
            - 增加 label 指示
        - 右侧
            - 预览，保存，取消 在一行或三行
 


 -----
 src/Web/Views/Admin/PageConfiguration/Edit.cshtml
 编辑页面的组件，将该组件所有的模板都列出，在 模板的 下拉列表(select id:component-template)中供选择 


网站管理(网站管理员使用)
- 网页管理：管理有效的网页页面数据及组件数据的，包括SEO数据(也是SEO组件)，页面组件数据
    - 列表
    - 点击页面进入页面数据设置页面
        - 页面分片，SEO数据，根据组件依次向下展开，需要填写数据在上面填个写，共用数据不需要

必须注意，特别强调：
- 本次要做的网页管理(PageManage)，和 页面配置（/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/PageConfiguration ）是2个不同的功能
- 网页管理(PageManage)，是对 已经配置好的页面，根据其配置的组件的数据结构，在页面形成表单，让用户来填写数据，并保存到一个页面数据数据表中。
- 这样，在动态页面解析时，根据 页面配置（PageConfiguration） + 网页管理(PageManage)的数据，就能正确输出页面内容了。


- 目前每个组件都有自己的数据结构，PageManage 只需要记录是哪个页面，哪个组件的数据（根据组件的数据实体定义），现在你的设计是一个全新的页面数据结构？
先暂定写代码，先分析下和梳理清楚

我的一个想法是
- 最终存到数据库是，是组件定义的对象的序列化字符串
- 关于组件实体中一些字段的多语言，需要 在组件的实体定义中 增加一个多语言字段列表，这样在网页管理(PageManage)编辑界面，如果是多语言字段，则根据当前支持语言展开，保存时存为 {"zh":"","en":"","ja":""}这样的形式，在页面展示时，如果是多语言，则读取对应的属性的对应语言值，组织为 组件实体的对象，赋值。（在组件的variants.json 增加该组件的多语言字段列表）


variants.json 结构发生了变化，需要修改对应的实体定义以及读取服务类


看来还是没有完全理解我的意思
- 网页管理(PageManage) 是 将 PageConfiguration 已经配置好的页面 列出；然后在这个页面列表上，点击进入进行编辑
- 如果有有效的数据，则显示，没有就相当于新增页面数据


关于内容表单构建
- 现在components: @Html.Raw(JsonSerializer.Serialize(Model.ComponentsData.Select(c => new { 
       ComponentDefinitionId = c.ComponentDefinitionId, 
       MultilingualFields = c.MultilingualFields,
       DataJson = c.DataJson
   }))) 这个肯定是错误的，因为如果组件没有值，这部分就空了。

我的思路：
- 方案1：反射每个组件对应的 实体模型定义：MlSoft.Sites.Web.ViewModels.Components，根据字段类型，以及 MultilingualFields ，构建表单
- 方案2：在variants.json定义表单项，包括输入项的类型，如果是这样的话，那 组件的实体模型定义也可以不用了。完全由json定义。
你给我一个建议的方案


现在有个问题

比如 Hero组件，HeroComponentViewModel 定义   public List<ActionButton> ActionButtons
Hero/variants.json 这个formFields定了2个按钮，主，次按钮及相关信息，所以在PageController中的ConvertContentDataToViewModel 转换中就丢了主次按钮，ActionButtons为空了。
先不改代码，这种情况推荐解决方案。


HeroComponentViewModel 需要增加一个背景色，同时 variants也要配置一个背景色的设置，注意这个 背景色控制只能用 css 变量来做变化得到的值，不能写死颜色值。
能否实现，先给个方案


===================
页面数据编辑器的 优化：现在页面编辑时控件都是平铺的，不好看，也占空间，需要优化。
我的思路如下：
- 在variants中的formFields中字段的配置增加一些属性，比如 输入框，输入一个宽度值；textarea输入 row, col；多语言字段 设置是显示在一行，还是一行一个等等；
- 需要做文件上传功能，包括视频，图片，资料文件，上传控件 可能要配置文件夹名称，类型，最大的文件大小

这也相当于一个低代码或无码平台的表单编辑器了，请你结合本项目以前当前主流的做法，给我一个相对简单的方案，能满足本系统要求，不需要过于复杂。
先给个方案


请按照 design/页面数据编辑器优化开发方案.md，并严格遵循 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md 规范进行开发。


现在 页面内容管理编辑界面有问题 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/PageManage/Edit.cshtml， /mnt/d/Codes/MlSoft.Sites/src/Web/Controllers/Admin/PageManageController.cs
- 没有应用新的表单设计，请按照 /mnt/d/Codes/MlSoft.Sites/design/页面数据编辑器优化开发方案.md，在 PageManage 这个功能上进行修改。
不要改动 PageConfiguration 任何功能！


hero组件，如果有背景图片或视频，如果图片超过显示区域，怎么样做到那种鼠标滚动时，背景图片也会有种动的感觉

对于组件中有大图片/视频，像hero,需要在 Dynamic.cshtml 做一个图片预加载的 preload 


/mnt/d/Codes/MlSoft.Sites/src/Web/Controllers/FileUploadController.cs 中，如果是图片，原尺寸转为webp

----------

- 给 const Dialog  对象增加一个 右下角 弹出的notify通知, 3秒自动消失
- 前端操作成功的，一律改为使用  这个ntofity，不要用alert。如果操作失败，保持不变还是用alert
- 将这个约定更新到 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md中


现在有2套对话框，将 showAlert，showInlineAlert，showNotification 替换为 Dialog对应的操作，成功的提示使用notify,失败用alert，请务必仔细处理，不要引入新的bug


继续优化清理代码：
- 
- 请检是 Dialog中所有窗口元素是否都是按照 主题样式变量方式写的，保证没有硬编码颜色。



按照 AI_Agent_Development_Guidelines.md， 仔细检查 
- /mnt/d/Codes/MlSoft.Sites/src/Web/Controllers/Admin/PageManageController.cs
- /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/PageManage 
这些文件是否存在：
- 硬编码的文本，请提取到资源文件中，提取前请检查是否有可以用的键值。
- 硬编码的样式，请使用主题样式变量
 
按照 AI_Agent_Development_Guidelines.md， 仔细检查 
 /mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js/form-field-renderer.js
 - 硬编码的文本，请提取到资源文件中，提取前请检查是否有可以用的键值。
- 硬编码的样式，请使用主题样式变量



/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/PageManage下的 index.cshtml 还有硬编码的文本，请提取到资源文件中，提取前请检查是否有可以用的键值。

====================================

到目前为止，针对现在的页面配置，页面数据数据管理，以及根据组件variants动态表单输出
为了更加灵活开发 组件，我有一个想法：

- 组件实体定义 转为 在 variants 中定义的JSON（现在formFields就够了），好处是足够灵活，也减少了 序列化的反序列化的开销
- 每个组件目录结构如下，以hero为例
    - Hero/
            ├── Default.cshtml
            ├── Simple.cshtml
            ├── TwoCols.cshtml
            ├── TwoCols.json (和TwoCols.cshtml同名，表示是这个专用)
            ├── variants.json (默认的配置，如果cshtml的同名json不存在，则认为是使用variants.json)
- 在PageController和Dynamic.cshtml中，直接从读取数据JSON传给组件html
- 缺点是，在组件cshtml没有定义的强类型调用，只能是JSON属性来读取值。

结合本项目，请充分评估我的这个想法可行性、性能、和现在相比的优缺点。
如果可行，针对我的这个方案，给我列一个详细的调整方案，形成md文档


小调整：
- 组件配置的json文件名使用小写字母，和variants一样，比如 TwoCols.json 改为 twocols.json
请修改相关代码，并将本次更新添加以 组件系统JSON配置重构方案.md 中



刚才方案还是有点问题，请按我新的方案调整：
1、每个变体都自己的json文件，名称和cshtml一致（json小写）
2、因json中有变体的名称和描述，所以不能共用。
3、以hero为例，现在的variants.json 要改为 default.json,以和cshtml保持一致


加载组件变体的方法，还是要从*.json中获取，请调整。
现在组件变体的cshtml和json名称一致(json文件小写)
   /mnt/d/Codes/MlSoft.Sites/src/Web/Services/Components/ComponentVariantService.cs 
   LoadComponentVariantConfig方法





请以  /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Hero 为例，帮我完成 接口 /mnt/d/Codes/MlSoft.Sites/src/Web/Services/Components/IComponentConfigService.cs的具体实现/mnt/d/Codes/MlSoft.Sites/src/Web/Services/Components/ComponentConfigService.cs
实体定义/mnt/d/Codes/MlSoft.Sites/src/Web/Services/Components/ComponentConfigModels.cs


将 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Hero/default.json 中 label,helpText 的文本提取到 /mnt/d/Codes/MlSoft.Sites/src/Web/Resources/FormResource.cs 资源文件中，三个语言，注意，不要添加重复值，键名不要包括组件名，尽量做到与组件无关的键名，以便共用

/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Hero/default.json 要用资源键名替换，然后调整下/mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js/form-field-renderer.js


关于前端JS资源问题，你有什么好的方案？


页面编辑中有动态表单生成，里面有很多资源在resx中，js无法直接读取，现在部分是通过 cshtml把需要的资源在页面中输出为js变量。
现在，我需做一个这样的功能：
1、帮我生成一个可以给vs调用的脚本，在程序发布或调试时，可以生成按语言生成 资源的js文件
2、在admin.layout中按照语言引入对应的js资源
3、这样admin管理后台中涉及js资源就可以直接用js对象，不需要像之前那样在页面写了。

给我一个方案，不用实现，我看看是否是我想要的。


刚才 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Hero/default.json以及/mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js/form-field-renderer.js 也调整下



我把/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Navigation/Default.cshtml 内容合并到 Header中，并将Navigation组件移除了。 
请帮我将/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Header/Default.cshtml 中 是属于动态可更换的内容，整理一下，放到/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Header/default.json中的表单配置中，这样就可以从后台进行数据填充。

请将 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Footer/Default.cshtml 也提取下动态表单项到default.json中


检查表单 Footer和Header的json中表单的相关是否硬编码，或者没有加入资源


我把默认语言调整 ja，但在 /mnt/d/Codes/MlSoft.Sites/src/Web/TagHelpers/LanguageSwitcherTagHelper.cs 读取当前语言还是中文，而不是我默认的ja

我需要在一个入口进行当前语言的设置：
如果路由有 有效的{culture},则将CultureInfo.CurrentCulture 设置为culture语言，否则设为默认的语言，

CultureInfo.CurrentCulture


帮我从程序启动开始，分析下现在路由处理情况，是否有多余不需要的处理程序
并检查下面几个程序的作用
/mnt/d/Codes/MlSoft.Sites/src/Web/Providers/RouteDataRequestCultureProvider.cs
/mnt/d/Codes/MlSoft.Sites/src/Web/Middleware/PageRenderingMiddleware.cs
/mnt/d/Codes/MlSoft.Sites/src/Web/Services/Localization/LanguageUrlService.cs
/mnt/d/Codes/MlSoft.Sites/src/Web/TagHelpers/SimplifiedLanguageSwitcherTagHelper.cs
/mnt/d/Codes/MlSoft.Sites/src/Web/Middleware/CultureMiddleware.cs

这些文件功能是否有重复的地方

先分析，等确认后再处理·



======================================================

内容管理功能开发

后台菜单

- 网站管理
    - 企业信息
        - 基本信息 
        - 企业历史
        - 役員紹介/組織図
        - 联系信息
        - CSR
        - IR情報        

    - 业务信息
        - 业务部门
        - 产品服务
    

    - 留言信息        
    - 招聘信息

    - 新闻资讯



我要开始做网站的内容管理部分功能，这是我列的菜单，根据 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities 所实义的对象。
菜单如下：

- 网站管理
    - 企业信息
        - 基本信息 
        - 企业历史
        - 役員紹介/組織図
        - 联系信息
        - CSR
        - IR情報        

    - 业务信息
        - 业务部门
        - 产品服务
    

    - 留言信息        
    - 招聘信息

    - 新闻资讯

我们一个功能一个功能完成。
1、 企业信息，这是一个页面，
        - 基本信息 
        - 企业历史
        - 役員紹介/組織図
        - 联系信息
        - CSR
        - IR情報    
这些都是页面上的tab页面，类似/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/SiteSettings/Index.cshtml。

请结合本项目和代码，在 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md 规则下，给我一个设计文档，包括tab页面上数据对应实体定义。
如果现在的定义需要调整，也请一并列出来。
注意，先处理 企业信息，其他几个先不处理。

============

企业代码(CompanyCode)
企业Logo(LogoUrl)


确定理解js引用多语言资源文件的方案吗？
1、/mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js/resources 已经在/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/_AdminLayout.cshtml按语言引用了，所以后面页面中都不需要引用。
2、需要添加到资源文件的文本和以前一样，该加的还要加，程序会自动生成最新的js资源文件
3、在view中的js需要引用资源，直接使用对应的js中的资源项。

4、理解后，请调整/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/CompanyInfo/Index.cshtml,并更新 开发规范文档。

请按 AI_Agent_Development_Guidelines.md ，检查 src/Web/Views/Admin/CompanyInfo/Index.cshtml，主要在 样式硬编码，文本硬编码等问题


请严格遵守 AI_Agent_Development_Guidelines.md 开发规则，按/mnt/d/Codes/MlSoft.Sites/design/enterprise-info-management-design.md，完成“企业历史”tab页的功能开发。

多语言界面请按 这种动态输出形式，而不是硬编码。请将这条也加入开发规则文档中
                        <!-- 多语言经营理念 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">@AdminRes["Philosophy"]</label>
                            <div class="space-y-4">
                                @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                                {
                                }
                            </div>
                        </div>

同时将刚才开发的/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/CompanyInfo/Index.cshtml中企业历史中涉及多语言的调整下


关于 CompanyInfo/Index.cshtml 页面，因为有很多tab页面，因此现在的数据加载方式将会比较多，所以，我有个方案：
打开这个页面，第1个tab页面像现在一样正常加载显示，其他标签不加载。
点击 后面的tab，检查是否已经加载过，如果未加载，则调用接口获取html，加入当前html中，如果已加载，则直接显示。

这个方案如何？

关于其他标签内容，我想以物理文件方式cshtml存放在company目录下，只是通过api去读取。因为物理文件容易 修改维护。你觉得呢


现在，请结合 CompanyInfo/Index.cshtml 这个多tab页面的开发方式，帮我写一个类似/mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md的开发约速手册，
要求开发类似的功能，按照这种方式来，文档存为 md格式，放在 /mnt/d/Codes/MlSoft.Sites/

CompanyInfo/Index.cshtml 这个页面的<script>单独提出来放一个js文件中吧，越来越大了


@Html.MultilingualUrl在 纯js中用  /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/_AdminLayout.cshtml中的  window.currentLanguagePrefix  来处理

请按 企业历史 这个开发作为参考， 严格遵守AI_Lazy_Loading in Multi-Tab Pages-Guidelines， 完成后面- 役員紹介/組織図 - 联系信息  - CSR - IR情報   功能开发，请一个一个开完，中间不用确认。我有事情要外出。




我要开始做网站的内容管理部分功能的第2个功能    

- 业务信息
    - 业务部门
    - 产品服务

根据 src/Model/Entities 所实义的对象
请按照 D:\Codes\MlSoft.Sites\design\enterprise-info-management-design.md的说明，完全遵守D:\Codes\MlSoft.Sites\AI_Lazy_Loading in Multi-Tab Pages-Guidelines.md和D:\Codes\MlSoft.Sites\AI_Agent_Development_Guidelines.md的开发规则。
给我一个设计文档，包括tab页面上数据对应实体定义。
如果现在的定义需要调整，也请一并列出来。
注意，只处理 业务信息相的业务部门和产品服务


请检查并完成 联系信息 页面的编辑等操作


我要开始做网站的 新闻资讯 功能，根据 src/Model/Entities 所实义的对象，结合本项目的情况，我一个设计文档，存放在 D:\Codes\MlSoft.Sites\design 目录下，md格式，如果现在的定义需要调整，也请一并列出来。

请按照  D:\Codes\MlSoft.Sites\design\news-management-design.md 和 news-entity-improvements.md , 完全遵守D:\Codes\MlSoft.Sites\AI_Lazy_Loading in Multi-Tab Pages-Guidelines.md和D:\Codes\MlSoft.Sites\AI_Agent_Development_Guidelines.md的开发规则,帮我完成 这个模块的功能开发。

我要开始做网站的 
招聘信息 的后台管理功能，根据 src/Model/Entities 所实义的对象，结合本项目的情况，我一个设计文档，存放在 D:\Codes\MlSoft.Sites\design 目录下，md格式，如果现在的定义需要调整，也请一并列出来。

请按照  /mnt/d/Codes/MlSoft.Sites/design/recruitment-management-design.md 和 news-entity-improvements.md , 完全遵守 AI_Lazy_Loading in Multi-Tab Pages-Guidelines.md和 AI_Agent_Development_Guidelines.md的开发规则,帮我完成 这个模块的功能开发。

请依据 AI_Lazy_Loading in Multi-Tab Pages-Guidelines.md和 AI_Agent_Development_Guidelines.md的开发规则，对 D:\Codes\MlSoft.Sites\src\Web\Views\Admin\BusinessInfo下的页面代码进行review，将其中 文本硬编码、样式硬编码的问题 找出来并修正。



请依据 AI_Lazy_Loading in Multi-Tab Pages-Guidelines.md和 AI_Agent_Development_Guidelines.md的开发规则，对 D:\Codes\MlSoft.Sites\src\Web\Views\Admin\AdminNews下的页面代码进行review，将其中 文本硬编码、样式硬编码的问题 找出来并修正，并将资源文件加入到 AdminResource.resx，AdminResource.ja.resx，AdminResource.en.resx 中。


企业基本信息调整：
企业简介和经营理念 多语言采用 多语标标签方式


 

我要开始做网站的 留言信息 功能，根据 src/Model/Entities 所实义的对象，结合本项目的情况，我一个设计文档，存放在 D:\Codes\MlSoft.Sites\design 目录下，md格式，如果现在的定义需要调整，也请一并列出来。



请按照  /mnt/d/Codes/MlSoft.Sites/design/simple-message-feedback-system-design.md, 完全遵守D:\Codes\MlSoft.Sites\AI_Lazy_Loading in Multi-Tab Pages-Guidelines.md和AI_Agent_Development_Guidelines.md的开发规则,帮我完成 这个模块的功能开发。





请依据 AI_Lazy_Loading in Multi-Tab Pages-Guidelines.md和 AI_Agent_Development_Guidelines.md的开发规则，对 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/CompanyInfo下的页面代码进行review，将其中 文本硬编码、样式硬编码的问题 找出来并修正，并将资源文件加入到 AdminResource.resx，AdminResource.ja.resx，AdminResource.en.resx 中。

以 /mnt/d/Codes/MlSoft.Sites/src/Web/Resources/AdminResource.resx 为基准，查找 AdminResource.en.resx,AdminResource.ja.resx 2个文件中缺失的键值，并补齐。



新闻模块调整：
- 目标：企业一个简单的新闻管理，可以发布新闻
- 目前新闻实体已经更新：完全以该实体 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/News/NewsAnnouncement.cs 为准，进行相关功能调整
- 功能：
    - 列表：现在新增调整，在列表和新建新闻中间增加一个筛选功能：按 NewsType（下拉列表），NewsStatus（下拉列表），新最标题(输入框)+搜索按钮
    - 新增/编辑 新闻，共用一个cshtml

- 清除新闻模块 除此之外的页面、脚本等

新闻模块调整：

1、参照 /mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js/admin/businessinfo.js 中表单提交机制，改写 /mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js/admin/adminnews.js 表单提交机制
2、内容改为tinymce富文本

因为news模块没有其他tab页，所以直接将/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/AdminNews/Partials/_NewsModal.cshtml 放到index.cshtml中即可


按照 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md，依次检查 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/AdminNews，/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/BusinessInfo，/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/CompanyInfo 这3个模块的以下几个差距题：
1、样式硬编码，没有使用主题样式变量
2、缺少暗黑模式支持
3、资源文件硬编码，或者有资源键值，但没有加入到资源文件中。


比较News的前端页面和adminnews.js中所用到的资源项是否在adminresource三个语言中有对应键值

帮我调整下 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/Recruitment 模块，
1、按照 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/BusinessInfo 的风格样式调整
2、样式硬编码，没有使用主题样式变量
3、缺少暗黑模式支持
4、资源文件硬编码，或者有资源键值，但没有加入到资源文件中。



将 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/AdminMessage/Details.cshtml 内容放到 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/AdminMessage/Index.cshtml中。
查看留言详细 改为 弹出窗口查看和操作。


按照 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md，依次检查  /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/AdminMessage/Index.cshtml 模块的以下几个：
1、样式硬编码，没有使用主题样式变量
2、缺少暗黑模式支持
3、资源文件硬编码，或者有资源键值，但没有加入到资源文件中。


检查该页面/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Admin/AdminMessage/Index.cshtml，所有资源项的键值是否存在 三个语言的资源中，不存在则添加。


基于 D:\Codes\MlSoft.Sites\Scripts\GenerateJsResources.ps1这个文件，我想做个改进的版本，规则如下
- 比较 src/Web/Resources中每个资源文件的修改时间和本工具上次运行时记录时间，资源文件的修改时间如果过本工具上次记录时间，则继续重新生成，否则跳过生成。

- 扫描 src/Web/Views视图文件中 <script>部分的JS代码，以及 src/Web/wwwroot/js/admin, src/Web/wwwroot/js/admin.js 这几个目录和文件
- 有使用 @AdminRes， @SharedRes @AccountRes ，或者 window.Resources.Account， window.Resources.Admin，window.Resources.Form ，window.Resources.Shared 变量的，将这些变量提取出来，用于重新生成 js资源文件