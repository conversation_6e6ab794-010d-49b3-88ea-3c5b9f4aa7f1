# 组件开发配置说明


## 自动/强制引入
- _Layout.cshtml
    - Header
    - Footer

- 页面配置必须引入
    - SEO , 不引入没有标题

## 系统可用数据: 需要定义一个实体对象，并缓存，以便视图直接使用
    - 站点设置(站点名称，Logo, Favicon)
    - 企业基本信息(企业名称，介绍)
    - 企业联系信息(电话，传真，邮箱，邮编，地址)

## 数据优先级
    - SEO
        - Title, 可以使用 变量 {SiteName}：站点名称， {CompanyName}: 企业名称
    
    - Header
        - Logo > 站点设置的logo


## 组件参数说明

### 


组件开发指南
帮我写一份 组件开发指南,用英文描述，存放到 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Component_Guidelines.md，用于后续AI生成组件。
注意：一个组件的结构如下：
- 实体：在 D:\Codes\MlSoft.Sites\src\Web\ViewModels\Components 定义
- ViewComponent : D:\Codes\MlSoft.Sites\src\Web\ViewComponents
    - 继承 BaseViewComponent
    - 实现 InvokeAsync 方法， 并调用 InvokeViewAsync<T>的 方法
- 视图：/mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components
    - 文件夹名称就是组件Id
    - 每个视图文件可能有多个组件变体，每个变体结构如下：
        - Xxx.cshtml 
        - xxx.json：变体的名称描述等多语言，以及表单定义（需要和该组件实体对应）
    - 视图文件需要遵循 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Guidelines.md 中 样式变量、暗黑模式，硬编码等问题


请参考 参考 D:\Codes\MlSoft.Sites\src\Web\Views\Shared\Components\Hero， Header, Footer 等组件的开发，完成该指南


请按照 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Component_Guidelines.md 开发规则，帮我完成 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/Carousel 组件的相关页面开发


帮我再完善下AI_Agent_Development_Component_Guidelines.md规范（用英文，如果存在下面的规则，则补充），关于文本资源问题：
- 优先从 src/Web/Resources/FormResource.resx,FormResource.en.resx,FormResource.ja.resx中查找，不存在，再从 AdminResource.resx, AdminResource.en.resx,AdminResource.ja.resx, 不存在再从 SharedResource.resx, SharedResource.ja.resx,SharedResource.en.resx中查找，如果都不存在，则在 FormResource的资源中添加

再补充一条关于 json中表单的定义规则：
- 多语言文本/文本域字段，使用 display:inline，显示在一行
- 多语言富文本，display:stacked
- 配置项，如checkbox, select等，可以一行3个或4个


请按照 /mnt/d/Codes/MlSoft.Sites/AI_Agent_Development_Component_Guidelines.md 开发规则，帮我完成 
### 1.2 面包屑导航
```html
<!-- 面包屑导航 -->
<nav class="flex px-5 py-3 text-gray-700 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700" aria-label="Breadcrumb">
  <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
    <li class="inline-flex items-center">
      <a href="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
        <svg class="w-3 h-3 me-2.5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
        </svg>
        ホーム
      </a>
    </li>
    <li>
      <div class="flex items-center">
        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" fill="none" viewBox="0 0 6 10">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 9l4-4-4-4"/>
        </svg>
        <a href="/company" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">会社情報</a>
      </div>
    </li>
    <li aria-current="page">
      <div class="flex items-center">
        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" fill="none" viewBox="0 0 6 10">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 9l4-4-4-4"/>
        </svg>
        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">会社概要</span>
      </div>
    </li>
  </ol>
</nav>
```
组件开发，存放到 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components目录下



按照 AI_Agent_Development_Component_Guidelines.md 开发规则，结合本项目各个实体的定义，帮我完成 others/components 下几个组件的转换为适合本项目的组件。



按照 AI_Agent_Development_Component_Guidelines.md 开发规则，结合本项目结构，帮我做一个CTA的组件

按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 Cookie 政策的全局组件，第一次访问网站时在底部提示Cookie相关信息，注意，不要太复杂。

按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 FAQ 组件

按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 公司概要组件，比如包括（成立时间、资本金、代表人等），请按照主流企业网站的组件进行设计

帮我增加一个 回到顶部的 功能，在_layout.cshtml中


和公司基本组件一样，按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 代表人（社長）的 组件，数据来自 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/Organization/Executive.cs中高管的条目。这个组件主要用于  社長メッセージ 使用

参考 /mnt/d/Codes/MlSoft.Sites/others/components/app/ceo-message/page.tsx，帮我在 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/PresidentMessage 新增一个变体，注意按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则

按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 企业沿革的组件，请按照主流企业网站的组件进行设计（可以参考 /mnt/d/Codes/MlSoft.Sites/others/components/app/history/page.tsx），注意，组件只需要配置样式，数据来自于 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/History/CompanyHistory.cs


按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 企业拠点案内 的组件，请按照主流企业网站的组件进行设计，注意，组件只需要配置样式，数据来自于 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/Company/CompanyLocation.cs
简单一些，一定注意资源项，严格按照AI_Agent_Development_Component_Guidelines.md处理资源项，不要添加重复，能复用尽量复用。


按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 企业组织机构 展示 的组件，请按照主流企业网站的组件进行设计，注意，组件只需要配置样式，数据来自于 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/Organization
简单一些，一定注意资源项，严格按照AI_Agent_Development_Component_Guidelines.md处理资源项，不要添加重复，能复用尽量复用。

按照 AI_Agent_Development_Component_Guidelines.md和AI_Agent_Development_Guidelines.md 开发规则，结合本项目结构，帮我做一个 客户案例 展示 的组件，请按照主流企业网站的组件进行设计，注意，一定注意资源项，严格按照AI_Agent_Development_Component_Guidelines.md处理资源项，不要添加重复，能复用尽量复用。



按照 AI_Agent_Development_Component_Guidelines.md 、 AI_Agent_Development_Guidelines.md 和 AI_Agent_Development_Resource_Guidelines.md 开发规则，结合本项目结构，帮我做一个 How-to-do的 的组件，参考/mnt/d/Codes/MlSoft.Sites/others/components/components/how-it-works.tsx ，请按照主流企业网站的组件进行设计，注意，一定注意资源项，严格按照 AI_Agent_Development_Resource_Guidelines.md处理资源项，不要添加重复，能复用尽量复用。


关于组件json配置表单说明
- 多语言字段(multilingual-text, multilingual-textarea,multilingual-richtext)
- Url 地址等 text字段, 图片文件等
  - "width": "col-span-12"
  - "layout": "inline"
  - "collapsed": true

- 其他 如 checkbox, select 
  - "width": "col-span-3"， "col-span-4"
  - "layout": "inline"
  - "collapsed": true

把上面这段规则加入到 AI_Agent_Development_Component_Guidelines.md 中

然后检查 这个 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/Components/HowToDo 组件json文件的配置


按照 AI_Agent_Development_Component_Guidelines.md 、 AI_Agent_Development_Guidelines.md 和 AI_Agent_Development_Resource_Guidelines.md 开发规则，帮我做一个分页组件，调用 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Shared/_Pagination.cshtml（/mnt/d/Codes/MlSoft.Sites/docs/分页组件使用指南.md）


当前动态页面 /mnt/d/Codes/MlSoft.Sites/src/Web/Views/Page/Dynamic.cshtml 输出组件时，我想在页面配置时，按照12栅格方式，标识这个组件所占宽宽度，各个组件按照设置进行排列，这样，有些页面就可以做到灵活布局，这是我的想法和需求。
请你根据当前项目的结构，给我一个方案，如果有更好的方案一定要给我。
形成一个md文档，存放到 design 目录下。
先不要改动代码。


扫描 /mnt/d/Codes/MlSoft.Sites/src/Web/Views和/mnt/d/Codes/MlSoft.Sites/src/Web/wwwroot/js 目录的cshtml,js,json, 这个文件 TaildWindCss.cshtml除外，将代码运态拼接的、选项中的 css样式，都写入 TaildWindCss.cshtml中，比如 <div class="动态样式的完整样式名 "></div>，可以多分一些div元素写，用于tailwindcss编译时使用


动态页面输出优化：循环输出组件时
- 如果组件的 ColumnSpan == 12
  - 检查上个组件外层 包的div是否闭合，如果没闭合，则添加 </div> 使用其闭合，然后输出该组件
  - 如果没有上个组件，或上个组件 ColumnSpan == 12，则直接输出
- 如果 ColumnSpan != 12
  - 检查上一个组件 ColumnSpan， 如果是 12，或者 有外层包的div且已经闭合 
    - 加一个外层 div  class="col-span-1 grid grid-cols-1 md:grid-cols-12" ，不闭合

全部组件输出后，如果有未闭合 外层div,刚闭合



按照 AI_Agent_Development_Component_Guidelines.md 、 AI_Agent_Development_Guidelines.md 和 AI_Agent_Development_Resource_Guidelines.md 开发规则，结合本项目结构，帮我做一个 搜索框 的组件，搜索项可以后台配置，主要是2种输入控件，下拉选择和输入框，，下拉选择的项可以自定义配置，生成一个默认变体就行了。
请注意，要使用 Flowbite 的组件
请按照主流企业网站的组件进行设计，注意，一定注意资源项，严格按照 AI_Agent_Development_Resource_Guidelines.md处理资源项，不要添加重复，能复用尽量复用。


按照 AI_Agent_Development_Component_Guidelines.md ， AI_Agent_Development_Guidelines.md 和 AI_Agent_Development_Resource_Guidelines.md 开发规则，结合本项目结构，帮我做一个 新闻列表 的展示组件，请按照主流企业网站的组件进行设计，注意：
- 组件只需要配置
  - 组件标题(多语言text)和描述(多语言textarea)
  - 每页显示数量
  - 是否显示图片
  等配置类信息
  新闻数据来自于  D:\Codes\MlSoft.Sites\src\Model\Entities\News\NewsAnnouncement.cs， 数据列表

    数据来自于 /mnt/d/Codes/MlSoft.Sites/src/Model/Entities/Organization
简单一些，一定注意资源项，严格按照AI_Agent_Development_Component_Guidelines.md处理资源项，不要添加重复，能复用尽量复用。