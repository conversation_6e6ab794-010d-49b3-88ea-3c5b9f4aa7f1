# 网页内容管理(PageManage)功能设计方案

## 1. 概述

网页内容管理(PageManage)是一个用于管理已配置页面具体内容数据的功能模块。它与页面配置(PageConfiguration)功能相互配合，实现了页面结构配置与内容数据的分离管理。

### 1.1 功能定位

- **页面配置(PageConfiguration)**：定义页面结构，包括使用哪些组件、组件模板选择、布局配置等
- **网页管理(PageManage)**：基于已配置的页面结构，为每个组件填写具体的内容数据

### 1.2 核心特性

- **JSON驱动的表单生成**：基于variants.json配置动态生成编辑表单
- **灵活的字段类型支持**：支持文本、富文本、选择框、复选框等多种输入类型
- **强大的多语言支持**：字段级别的多语言配置和管理
- **实时配置更新**：修改JSON配置即可更新表单结构，无需重新编译
- **类型安全保障**：保留实体模型作为数据传输载体
- **内容版本管理**：支持草稿、发布、归档等状态管理

## 2. 设计理念：混合方案

### 2.1 核心架构

采用**JSON配置驱动 + 实体模型保障**的混合方案：

- **主要依赖**：variants.json 定义表单结构和字段配置
- **辅助保留**：实体模型作为类型安全的数据载体和API契约
- **灵活性优先**：JSON配置驱动UI生成，支持热更新

### 2.2 方案对比

| 特性 | 纯反射方案 | 纯JSON方案 | 混合方案（采用） |
|------|------------|------------|------------------|
| **开发效率** | 低 | 高 | 高 |
| **类型安全** | 强 | 弱 | 中等 |
| **灵活性** | 低 | 很高 | 很高 |
| **维护成本** | 高 | 低 | 低 |
| **扩展性** | 困难 | 简单 | 简单 |
| **性能** | 较差 | 好 | 好 |

## 3. 数据模型设计

### 3.1 PageManage实体

```csharp
public class PageManage
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;

    // 关联页面配置
    public string PageConfigurationId { get; set; } = string.Empty;
    public string PageKey { get; set; } = string.Empty;
    
    // 组件数据 - 直接存储组件ViewModel的JSON序列化
    // 多语言字段以 {"zh":"","en":"","ja":""} 格式存储在JSON中
    public List<ComponentData> ComponentsData { get; set; } = new();
    
    // 状态管理
    public PageContentStatus Status { get; set; } = PageContentStatus.Draft;
    public DateTime? PublishDate { get; set; }
    public int Version { get; set; } = 1;
    
    // 审计字段
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
}

public class ComponentData
{
    public string ComponentDefinitionId { get; set; } = string.Empty; // Hero, SEO, Content等
    public string TemplateKey { get; set; } = string.Empty;           // Default, Corporate等
    public int DisplayOrder { get; set; }
    
    // 组件ViewModel的JSON序列化字符串
    // 例如：SEOComponentViewModel、HeroComponentViewModel等的JSON
    // 多语言字段在JSON中以 {"zh":"中文","en":"English","ja":"日本語"} 格式存储
    public string DataJson { get; set; } = string.Empty;
    
    public bool IsVisible { get; set; } = true;
    public DateTime LastModified { get; set; }
}

public enum PageContentStatus
{
    Draft = 0,      // 草稿
    Review = 1,     // 待审核
    Published = 2,  // 已发布
    Archived = 3    // 已归档
}
```

### 3.2 扩展的variants.json结构

采用新的JSON配置格式，支持完整的表单字段定义：

```json
{
  "variants": [
    {
      "id": "Default",
      "names": {
        "zh": "默认样式",
        "en": "Default Style",
        "ja": "デフォルトスタイル"
      },
      "descriptions": {
        "zh": "标准布局样式",
        "en": "Standard layout style",
        "ja": "標準レイアウトスタイル"
      }
    }
  ],
  "formFields": [
    {
      "name": "Title",
      "type": "text",
      "required": true,
      "multilingual": true,
      "validation": {
        "maxLength": 100,
        "minLength": 1
      },
      "labels": {
        "zh": "标题",
        "en": "Title", 
        "ja": "タイトル"
      },
      "placeholder": {
        "zh": "请输入标题",
        "en": "Enter title",
        "ja": "タイトルを入力"
      }
    },
    {
      "name": "Content",
      "type": "textarea",
      "multilingual": true,
      "validation": {
        "maxLength": 1000
      },
      "labels": {
        "zh": "内容",
        "en": "Content",
        "ja": "内容"
      },
      "options": {
        "rows": 4
      }
    },
    {
      "name": "Url",
      "type": "url",
      "multilingual": false,
      "required": false,
      "labels": {
        "zh": "链接地址",
        "en": "URL",
        "ja": "URL"
      },
      "validation": {
        "pattern": "^https?://.+"
      }
    },
    {
      "name": "IsEnabled",
      "type": "checkbox",
      "multilingual": false,
      "defaultValue": true,
      "labels": {
        "zh": "是否启用",
        "en": "Enabled",
        "ja": "有効"
      }
    },
    {
      "name": "Priority",
      "type": "select",
      "multilingual": false,
      "options": {
        "items": [
          { "value": "low", "labels": { "zh": "低", "en": "Low", "ja": "低" } },
          { "value": "medium", "labels": { "zh": "中", "en": "Medium", "ja": "中" } },
          { "value": "high", "labels": { "zh": "高", "en": "High", "ja": "高" } }
        ]
      },
      "defaultValue": "medium",
      "labels": {
        "zh": "优先级",
        "en": "Priority",
        "ja": "優先度"
      }
    },
    {
      "name": "PublishDate",
      "type": "datetime",
      "multilingual": false,
      "labels": {
        "zh": "发布日期",
        "en": "Publish Date",
        "ja": "公開日"
      }
    },
    {
      "name": "BackgroundColor",
      "type": "color",
      "multilingual": false,
      "defaultValue": "#ffffff",
      "labels": {
        "zh": "背景颜色",
        "en": "Background Color",
        "ja": "背景色"
      }
    }
  ]
}
```

### 3.3 支持的字段类型

| 类型 | 描述 | 配置选项 | 示例 |
|------|------|----------|------|
| `text` | 单行文本输入 | maxLength, minLength, pattern | 标题、名称 |
| `textarea` | 多行文本输入 | rows, maxLength | 内容、描述 |
| `email` | 邮箱输入 | pattern | 联系邮箱 |
| `url` | URL输入 | pattern | 链接地址 |
| `tel` | 电话输入 | pattern | 联系电话 |
| `number` | 数字输入 | min, max, step | 价格、数量 |
| `checkbox` | 复选框 | defaultValue | 开关选项 |
| `radio` | 单选按钮 | items | 单选选项 |
| `select` | 下拉选择 | items | 类别选择 |
| `date` | 日期选择 | min, max | 活动日期 |
| `datetime` | 日期时间选择 | min, max | 发布时间 |
| `time` | 时间选择 | min, max | 活动时间 |
| `color` | 颜色选择 | defaultValue | 主题颜色 |
| `file` | 文件上传 | accept, multiple | 图片、文档 |
| `richtext` | 富文本编辑器 | toolbar | HTML内容 |

### 3.4 多语言字段数据结构

多语言字段在JSON中存储格式：

```json
{
  "Title": {
    "zh": "欢迎来到我们公司",
    "en": "Welcome to Our Company",
    "ja": "私たちの会社へようこそ"
  },
  "Content": {
    "zh": "专业化工解决方案提供商",
    "en": "Professional Chemical Solutions Provider", 
    "ja": "プロの化学ソリューションプロバイダー"
  },
  "Url": "/about",
  "IsEnabled": true,
  "Priority": "high",
  "BackgroundColor": "#0066cc"
}
```

## 4. 服务层设计

### 4.1 ComponentVariantService扩展

扩展现有的ComponentVariantService以支持表单字段配置：

```csharp
public interface IComponentVariantService
{
    // 现有方法
    List<ComponentVariantOption> GetComponentVariants(string componentName);
    List<string> GetComponentMultilingualFields(string componentName);
    Dictionary<string, List<ComponentVariantOption>> GetAllComponentVariants();
    
    // 新增方法
    List<FormFieldConfig> GetComponentFormFields(string componentName);
    ComponentVariantConfig GetComponentVariantConfig(string componentName);
}

public class FormFieldConfig
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public bool Required { get; set; }
    public bool Multilingual { get; set; }
    public Dictionary<string, string> Labels { get; set; } = new();
    public Dictionary<string, string> Placeholder { get; set; } = new();
    public Dictionary<string, object> Validation { get; set; } = new();
    public Dictionary<string, object> Options { get; set; } = new();
    public object? DefaultValue { get; set; }
}

public class ComponentVariantConfig
{
    public List<ComponentVariantData> Variants { get; set; } = new();
    public List<FormFieldConfig> FormFields { get; set; } = new();
    public List<string> MultilingualFields { get; set; } = new();
}
```

### 4.2 表单生成流程

1. **加载页面配置** → 获取页面结构和组件列表
2. **读取variants.json** → 获取每个组件的表单字段配置
3. **生成动态表单** → 基于配置生成对应的HTML表单元素
4. **处理多语言字段** → 为多语言字段生成语言切换标签页
5. **数据绑定** → 将现有数据绑定到表单字段

### 4.3 数据保存流程

1. **表单数据收集** → JavaScript收集所有字段数据
2. **多语言数据整合** → 将多语言字段整合为对象格式
3. **数据验证** → 前端和后端双重验证
4. **JSON序列化** → 将数据序列化为JSON存储
5. **数据持久化** → 保存到MongoDB

## 5. 前端实现设计

### 5.1 JavaScript表单生成器

```javascript
class ComponentFormGenerator {
    constructor(supportedLanguages, componentConfig) {
        this.supportedLanguages = supportedLanguages;
        this.componentConfig = componentConfig;
    }
    
    generateForm(container, componentData, componentIndex) {
        const formFields = this.componentConfig.formFields || [];
        const existingData = this.parseComponentData(componentData.DataJson);
        
        container.innerHTML = '';
        
        formFields.forEach(fieldConfig => {
            const fieldElement = this.createFormField(
                fieldConfig, 
                existingData[fieldConfig.name],
                componentIndex
            );
            container.appendChild(fieldElement);
        });
        
        this.initializeEventHandlers(container);
    }
    
    createFormField(fieldConfig, fieldValue, componentIndex) {
        if (fieldConfig.multilingual) {
            return this.createMultilingualField(fieldConfig, fieldValue, componentIndex);
        } else {
            return this.createSimpleField(fieldConfig, fieldValue, componentIndex);
        }
    }
    
    createInputElement(fieldConfig, value, fieldId) {
        switch (fieldConfig.type) {
            case 'text':
            case 'email': 
            case 'url':
            case 'tel':
                return this.createTextInput(fieldConfig, value, fieldId);
            case 'textarea':
                return this.createTextarea(fieldConfig, value, fieldId);
            case 'number':
                return this.createNumberInput(fieldConfig, value, fieldId);
            case 'checkbox':
                return this.createCheckbox(fieldConfig, value, fieldId);
            case 'radio':
                return this.createRadioGroup(fieldConfig, value, fieldId);
            case 'select':
                return this.createSelect(fieldConfig, value, fieldId);
            case 'date':
            case 'datetime':
            case 'time':
                return this.createDateTimeInput(fieldConfig, value, fieldId);
            case 'color':
                return this.createColorInput(fieldConfig, value, fieldId);
            case 'file':
                return this.createFileInput(fieldConfig, value, fieldId);
            case 'richtext':
                return this.createRichTextEditor(fieldConfig, value, fieldId);
            default:
                return this.createTextInput(fieldConfig, value, fieldId);
        }
    }
}
```

### 5.2 多语言标签页组件

```javascript
class MultilingualTabs {
    constructor(container, fieldName, fieldConfig, fieldValue, componentIndex) {
        this.container = container;
        this.fieldName = fieldName;
        this.fieldConfig = fieldConfig;
        this.fieldValue = fieldValue || {};
        this.componentIndex = componentIndex;
        
        this.render();
    }
    
    render() {
        const tabsHTML = `
            <div class="multilingual-field" data-field="${this.fieldName}">
                <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        ${this.renderTabs()}
                    </nav>
                </div>
                <div class="language-contents">
                    ${this.renderContents()}
                </div>
            </div>
        `;
        
        this.container.innerHTML = tabsHTML;
        this.initializeTabSwitching();
    }
    
    renderTabs() {
        return pageData.supportedLanguages.map((lang, index) => `
            <button type="button" 
                    class="language-tab ${index === 0 ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} 
                           dark:text-gray-400 dark:hover:text-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-lang="${lang.Code}">
                ${lang.Name}
            </button>
        `).join('');
    }
    
    renderContents() {
        return pageData.supportedLanguages.map((lang, index) => `
            <div class="language-content ${index === 0 ? 'block' : 'hidden'}" 
                 data-lang="${lang.Code}">
                ${this.createInputForLanguage(lang.Code)}
            </div>
        `).join('');
    }
}
```

## 6. 实现步骤

### 6.1 Phase 1: 扩展ComponentVariantService

1. **更新数据模型**
   - 添加FormFieldConfig、ComponentVariantConfig类
   - 扩展IComponentVariantService接口

2. **实现配置读取**
   - 修改LoadComponentData方法支持新JSON结构
   - 实现GetComponentFormFields方法

3. **向下兼容处理**
   - 如果没有formFields配置，使用现有逻辑
   - 渐进式迁移现有组件配置

### 6.2 Phase 2: 重构前端表单生成

1. **创建ComponentFormGenerator类**
   - 支持多种输入类型的表单生成
   - 实现多语言字段的标签页界面

2. **更新PageManage/Edit.cshtml**
   - 集成新的表单生成器
   - 优化用户交互体验

3. **数据处理优化**
   - 改进数据收集和验证逻辑
   - 增强错误处理和用户反馈

### 6.3 Phase 3: 组件配置迁移

1. **更新现有variants.json文件**
   - Hero组件配置迁移
   - Content组件配置迁移
   - SEO组件配置迁移
   - 其他组件逐步迁移

2. **测试和验证**
   - 功能测试
   - 多语言测试
   - 性能测试

### 6.4 Phase 4: 高级特性

1. **条件显示字段**
   - 基于其他字段值显示/隐藏字段
   - 字段间的依赖关系处理

2. **富文本编辑器集成**
   - 集成TinyMCE或CKEditor
   - 支持图片上传和管理

3. **文件上传功能**
   - 图片上传和预览
   - 文件管理和组织

## 7. 优势总结

### 7.1 开发优势

- **零编码扩展**：新增字段只需修改JSON配置
- **实时预览**：配置修改立即生效，无需重新编译
- **多样化支持**：支持各种输入类型和验证规则
- **国际化友好**：字段标签和提示信息多语言支持

### 7.2 维护优势

- **配置集中**：所有表单配置集中在JSON文件中
- **易于调试**：清晰的配置结构便于问题定位
- **版本控制**：JSON配置文件可以版本控制
- **文档即配置**：JSON配置本身就是很好的文档

### 7.3 用户体验优势

- **直观的编辑界面**：根据字段类型生成最适合的输入控件
- **智能验证**：实时的前端验证和友好的错误提示
- **多语言切换**：便捷的多语言内容管理界面
- **响应式设计**：适配各种屏幕尺寸的设备

---

*本文档描述了基于JSON配置驱动的混合方案，既保证了开发的灵活性，又维持了必要的类型安全性。通过这种设计，可以实现零编码的表单扩展，同时提供优秀的用户体验和维护性。*
4. **数据保存**：将表单数据序列化为对应组件ViewModel的JSON格式

### 4.2 渲染流程

1. **读取内容数据**：从PageManage中获取组件的DataJson
2. **语言适配**：根据当前请求语言，从多语言字段中提取对应语言内容
3. **对象重构**：将适配后的数据反序列化为具体的组件ViewModel
4. **组件渲染**：将ViewModel传递给对应的ViewComponent进行渲染

### 4.3 数据处理示例

**存储时的多语言字段处理**：
```csharp
// 编辑界面提交的数据
var heroData = new HeroComponentViewModel
{
    Title = new Dictionary<string, string>
    {
        {"zh", "欢迎来到我们公司"},
        {"en", "Welcome to Our Company"},
        {"ja", "私たちの会社へようこそ"}
    }
};

// 序列化存储
var json = JsonSerializer.Serialize(heroData);
```

**渲染时的语言提取**：
```csharp
// 从数据库读取JSON
var storedJson = componentData.DataJson;
var heroData = JsonSerializer.Deserialize<HeroComponentViewModel>(storedJson);

// 根据当前语言提取内容
var currentLanguage = "zh";
if (heroData.Title is Dictionary<string, string> titleDict)
{
    heroData.Title = titleDict.GetValueOrDefault(currentLanguage, titleDict.Values.FirstOrDefault());
}
```

## 5. 技术实现要点

### 5.1 Service层设计

```csharp
public class PageManageService : MongoBaseService<PageManage>
{
    // 获取页面内容数据
    Task<PageManage?> GetPageContentAsync(string pageConfigurationId);
    
    // 保存页面内容数据
    Task<bool> SavePageContentAsync(string pageConfigurationId, List<ComponentData> componentsData, string updatedBy);
    
    // 发布页面内容
    Task<bool> PublishPageContentAsync(string id);
    
    // 语言适配 - 将多语言字段转换为当前语言的单值
    Task<T> AdaptComponentDataForLanguageAsync<T>(string dataJson, string language) where T : class;
}
```

### 5.2 Controller层设计

```csharp
[Route("Admin/[controller]")]
public class PageManageController : BaseController
{
    [HttpGet("Edit/{pageConfigurationId}")]
    public async Task<IActionResult> Edit(string pageConfigurationId);
    
    [HttpPost("Edit/{pageConfigurationId}")]
    public async Task<IActionResult> Edit(string pageConfigurationId, PageManageEditViewModel model);
    
    [HttpGet("GetComponentForm/{componentId}")]
    public async Task<IActionResult> GetComponentForm(string componentId, string templateKey);
}
```

### 5.3 前端编辑界面设计

- 基于页面配置动态生成组件编辑区域
- 多语言字段自动展开为Tab或手风琴形式
- 支持组件的可视化预览
- 实时保存和版本管理

## 6. 优势分析

### 6.1 开发效率

- **零额外数据结构**：直接复用现有组件ViewModel，无需创建额外的数据模型
- **类型安全**：利用现有类型系统，减少类型转换错误
- **快速开发**：编辑界面可基于反射和元数据自动生成

### 6.2 可维护性

- **结构清晰**：页面配置与内容数据分离，职责明确
- **扩展性强**：新增组件只需定义ViewModel和variants.json即可
- **版本兼容**：基于JSON存储，向后兼容性好

### 6.3 多语言支持

- **灵活配置**：通过variants.json灵活定义哪些字段支持多语言
- **存储高效**：避免为每种语言创建单独记录
- **管理简单**：统一的多语言编辑界面

## 7. 实施步骤

1. **Phase 1**：创建PageManage数据模型和基础Service
2. **Phase 2**：扩展现有组件的variants.json，定义多语言字段
3. **Phase 3**：开发PageManage编辑界面和Controller
4. **Phase 4**：修改页面渲染逻辑，集成内容数据
5. **Phase 5**：完善版本管理和发布流程

## 8. 风险评估

### 8.1 技术风险

- **JSON序列化兼容性**：需要确保组件ViewModel的序列化稳定性
- **性能考虑**：大量组件数据的序列化/反序列化性能

### 8.2 缓解方案

- 建立组件ViewModel版本管理机制
- 实施多层缓存策略，减少重复序列化操作
- 分页加载和懒加载策略

这个设计方案将现有的组件体系与内容管理功能有机结合，在保持系统架构清晰的同时，提供了强大而灵活的多语言内容管理能力。