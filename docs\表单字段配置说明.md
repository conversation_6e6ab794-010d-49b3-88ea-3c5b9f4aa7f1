# 表单字段配置说明文档

## 概述

本文档详细说明了在 `variants.json` 文件中 `formFields` 数组的配置规范，用于动态生成组件属性编辑表单。

## 配置文件结构

```json
{
  "variants": [...],
  "formFields": [
    {
      "name": "字段名称",
      "type": "字段类型",
      "label": "显示标签",
      "display": { /* 显示配置 */ },
      "validation": { /* 验证配置 */ },
      "fileConfig": { /* 文件上传配置 */ },
      "options": [ /* 选项配置 */ ],
      "defaultValue": "默认值",
      "editorConfig": { /* 编辑器配置 */ }
    }
  ]
}
```

## 基本字段配置

### `name` (必需)
- **类型**: `string`
- **说明**: 字段的唯一标识符，用于数据绑定
- **示例**: `"companyName"`, `"logo"`, `"backgroundColor"`

### `type` (必需) 
- **类型**: `string`
- **说明**: 字段的输入类型，决定渲染的表单控件
- **可选值**:
  - `"text"` - 单行文本输入
  - `"email"` - 邮箱输入
  - `"number"` - 数字输入
  - `"textarea"` - 多行文本输入
  - `"multilingual-text"` - 多语言单行文本
  - `"multilingual-textarea"` - 多语言多行文本
  - `"select"` - 下拉选择
  - `"checkbox"` - 复选框
  - `"radio"` - 单选按钮组
  - `"file"` - 文件上传
  - `"image"` - 图片上传
  - `"video"` - 视频上传
  - `"richtext"` - 富文本编辑器
  - `"color"` - 颜色选择器
  - `"date"` - 日期选择
  - `"datetime"` - 日期时间选择
  - `"time"` - 时间选择

### `label` (推荐)
- **类型**: `string`
- **说明**: 字段的显示标签，支持本地化资源键
- **示例**: `"FormFields_CompanyName"`, `"公司名称"`

## 显示配置 (`display`)

### `group`
- **类型**: `string`
- **说明**: 字段分组名称，相同分组的字段会显示在同一个手风琴组件中
- **常用值**:
  - `"FormGroups_BasicInfo"` - 基本信息
  - `"FormGroups_MediaContent"` - 媒体内容
  - `"FormGroups_LayoutSettings"` - 布局设置
  - `"FormGroups_Resources"` - 资源文件
  - `"FormGroups_SEOSettings"` - SEO设置
  - `"FormGroups_Other"` - 其他

### `width`
- **类型**: `string`
- **说明**: 字段宽度，使用Tailwind CSS grid类
- **可选值**:
  - `"col-span-12"` - 全宽 (默认)
  - `"col-span-8"` - 2/3宽度
  - `"col-span-6"` - 半宽
  - `"col-span-4"` - 1/3宽度
  - `"col-span-3"` - 1/4宽度

### `order`
- **类型**: `number`
- **说明**: 字段在分组内的排序权重，数字越小越靠前
- **示例**: `1`, `10`, `20`

### `collapsed`
- **类型**: `boolean`
- **说明**: 分组是否默认折叠
- **默认值**: `false`

### `helpText`
- **类型**: `string`
- **说明**: 字段帮助提示文本，显示在输入框下方
- **示例**: `"FormFields_CompanyNameHelpText"`

### `layout` (多语言字段专用)
- **类型**: `string`
- **说明**: 多语言字段的布局模式
- **可选值**:
  - `"default"` - 垂直堆叠布局 (默认)
  - `"inline"` - 水平内联布局

### `rows` (textarea类型专用)
- **类型**: `number`
- **说明**: 文本域的行数
- **默认值**: `4`
- **示例**: `3`, `6`, `10`

## 验证配置 (`validation`)

### `required`
- **类型**: `boolean`
- **说明**: 字段是否必填
- **默认值**: `false`

### `maxLength`
- **类型**: `number`
- **说明**: 文本最大长度限制
- **示例**: `100`, `500`

### `minLength`
- **类型**: `number`
- **说明**: 文本最小长度限制
- **示例**: `3`, `10`

### `min` / `max` (数字/日期类型)
- **类型**: `number` 或 `string`
- **说明**: 数值或日期的范围限制
- **示例**: `0`, `100`, `"2023-01-01"`

### `step` (数字类型)
- **类型**: `number`
- **说明**: 数字输入的步进值
- **示例**: `0.1`, `1`, `5`

### `pattern`
- **类型**: `string`
- **说明**: 正则表达式验证模式
- **示例**: `"^[a-zA-Z0-9]+$"`

## 文件上传配置 (`fileConfig`)

适用于 `file`、`image`、`video` 类型字段。

### `folder`
- **类型**: `string`
- **说明**: 文件上传的目标文件夹
- **示例**: `"header-logos"`, `"hero-images"`, `"documents"`

### `types`
- **类型**: `string[]`
- **说明**: 允许的文件类型列表
- **示例**: 
  ```json
  ["image/jpeg", "image/png", "image/webp"]
  ["video/mp4", "video/webm"]
  ["application/pdf", "application/msword"]
  ["image/*", "video/*"]
  ```

### `maxSize`
- **类型**: `string`
- **说明**: 最大文件大小限制
- **格式**: 数字+单位 (`KB`, `MB`, `GB`)
- **示例**: `"2MB"`, `"500KB"`, `"1GB"`

### `multiple`
- **类型**: `boolean`
- **说明**: 是否允许选择多个文件
- **默认值**: `false`

### `preview`
- **类型**: `boolean`
- **说明**: 是否显示预览功能
- **默认值**: `true`

## 选项配置 (`options`)

适用于 `select`、`radio` 类型字段。

```json
"options": [
  {
    "value": "option1",
    "label": "选项1",
    "selected": false
  },
  {
    "value": "option2", 
    "label": "选项2",
    "selected": true
  }
]
```

### `value`
- **类型**: `string`
- **说明**: 选项的实际值

### `label`
- **类型**: `string`
- **说明**: 选项的显示文本

### `selected`
- **类型**: `boolean`
- **说明**: 是否默认选中

## 默认值 (`defaultValue`)

根据字段类型设置不同的默认值：

- **文本类型**: `"默认文本"`
- **数字类型**: `0`, `100`
- **布尔类型**: `true`, `false`
- **多语言类型**: 
  ```json
  {
    "zh": "中文内容",
    "en": "English Content", 
    "ja": "日本語コンテンツ"
  }
  ```
- **颜色类型**: `"#ffffff"`, `"#333333"`

## 编辑器配置 (`editorConfig`)

适用于 `richtext` 类型字段。

```json
"editorConfig": {
  "height": 400,
  "toolbar": "undo redo | bold italic | alignleft aligncenter alignright",
  "plugins": ["link", "image", "code"],
  "menubar": false
}
```

## 完整示例

```json
{
  "variants": [...],
  "formFields": [
    {
      "name": "companyName",
      "type": "multilingual-text",
      "label": "FormFields_CompanyName",
      "display": {
        "group": "FormGroups_BasicInfo",
        "width": "col-span-8",
        "order": 1,
        "collapsed": false,
        "helpText": "FormFields_CompanyNameHelpText",
        "layout": "inline"
      },
      "validation": {
        "required": true,
        "maxLength": 100
      },
      "defaultValue": {
        "zh": "",
        "en": "",
        "ja": ""
      }
    },
    {
      "name": "logo",
      "type": "image",
      "label": "FormFields_Logo",
      "display": {
        "group": "FormGroups_MediaContent",
        "width": "col-span-6",
        "order": 10,
        "helpText": "FormFields_LogoHelpText"
      },
      "fileConfig": {
        "folder": "header-logos",
        "types": ["image/jpeg", "image/png", "image/webp"],
        "maxSize": "2MB",
        "multiple": false,
        "preview": true
      }
    },
    {
      "name": "showLanguageSelector",
      "type": "checkbox",
      "label": "FormFields_ShowLanguageSelector",
      "display": {
        "group": "FormGroups_LayoutSettings",
        "width": "col-span-6",
        "order": 20,
        "helpText": "FormFields_ShowLanguageSelectorHelpText"
      },
      "defaultValue": true
    },
    {
      "name": "themeColor",
      "type": "select",
      "label": "FormFields_ThemeColor",
      "display": {
        "group": "FormGroups_LayoutSettings",
        "width": "col-span-4",
        "order": 25
      },
      "options": [
        {
          "value": "blue",
          "label": "蓝色主题",
          "selected": true
        },
        {
          "value": "green",
          "label": "绿色主题"
        },
        {
          "value": "purple",
          "label": "紫色主题"
        }
      ]
    },
    {
      "name": "description",
      "type": "richtext",
      "label": "FormFields_Description",
      "display": {
        "group": "FormGroups_BasicInfo",
        "width": "col-span-12",
        "order": 5,
        "rows": 6
      },
      "validation": {
        "required": false,
        "maxLength": 2000
      },
      "editorConfig": {
        "height": 300,
        "toolbar": "undo redo | bold italic underline | alignleft aligncenter alignright | bullist numlist | link",
        "menubar": false
      }
    }
  ]
}
```

## 本地化资源

所有 `label`、`helpText` 和选项标签都支持本地化资源键。确保在以下文件中定义对应的资源：

- `/Resources/SharedResource.resx` - 共享资源
- `/Resources/AdminResource.resx` - 管理界面资源

示例资源定义：
```xml
<data name="FormFields_CompanyName" xml:space="preserve">
  <value>公司名称</value>
</data>
<data name="FormFields_CompanyNameHelpText" xml:space="preserve">
  <value>请输入您公司的正式名称</value>
</data>
```

## 最佳实践

1. **命名规范**: 使用驼峰命名法，如 `companyName`、`backgroundColor`
2. **分组组织**: 合理使用 `group` 将相关字段归类
3. **排序规划**: 使用 `order` 值间隔较大，便于后续插入新字段
4. **必填标识**: 对必填字段设置 `required: true` 并提供清晰的帮助文本
5. **文件限制**: 根据实际需求合理设置文件类型和大小限制
6. **响应式布局**: 使用合适的 `width` 值确保在不同屏幕尺寸下的良好显示
7. **多语言支持**: 对面向用户的内容使用多语言字段类型

这套配置系统提供了极大的灵活性，可以满足各种复杂的表单需求，同时保持良好的用户体验。