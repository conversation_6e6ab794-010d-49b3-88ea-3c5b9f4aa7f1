<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司组织架构图</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .org-chart {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .level {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            margin: 2rem 0;
            position: relative;
        }

        .level-1 {
            margin-bottom: 3rem;
        }

        .level-2 {
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .level-3 {
            gap: 2rem;
        }

        /* 连接线样式 */
        .connection-line {
            position: absolute;
            background-color: #4a5568;
            z-index: 1;
        }

        /* 从CEO到各部门的垂直线 */
        .vertical-main {
            width: 2px;
            height: 60px;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 连接各部门的水平线 */
        .horizontal-main {
            height: 2px;
            width: calc(100% - 2rem);
            top: -60px;
            left: 1rem;
        }

        /* 从研发部到子部门的连接线 */
        .rd-vertical {
            width: 2px;
            height: 40px;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
        }

        .rd-horizontal {
            height: 2px;
            width: 280px;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
        }

        .rd-vertical-down {
            width: 2px;
            height: 20px;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
        }

        .node-container {
            z-index: 2;
            position: relative;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .node-container {
            animation: fadeInUp 0.6s ease-out;
        }

        .node-container:nth-child(2) { animation-delay: 0.1s; }
        .node-container:nth-child(3) { animation-delay: 0.2s; }
        .node-container:nth-child(4) { animation-delay: 0.3s; }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .level-2, .level-3 {
                flex-direction: column;
                gap: 2rem;
            }
            
            .horizontal-main {
                display: none;
            }
            
            .rd-horizontal {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="org-chart">
        <!-- 标题 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">公司组织架构图</h1>
            <p class="text-gray-600">Company Organizational Chart</p>
        </div>

        <!-- 第一层：公司高管层 -->
        <div class="level level-1">
            <div class="node-container executive-node bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-300 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-64 text-center relative overflow-hidden">
                <!-- 层级标识角标 -->
                <div class="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-sm">
                    高管
                </div>

                <!-- 装饰性图标 -->
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-indigo-500"></div>

                <div class="p-5 pt-6">
                    <!-- 部门图标 -->
                    <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-crown text-blue-600 text-lg"></i>
                    </div>

                    <!-- 部门名称 -->
                    <h3 class="text-lg font-bold text-blue-900 mb-2 leading-tight">
                        公司
                    </h3>

                    <!-- 统计信息 -->
                    <div class="flex justify-center items-center space-x-4 text-xs text-gray-500">
                        <span class="flex items-center">
                            <i class="fas fa-sitemap mr-1"></i>
                            3 个下属部门
                        </span>
                    </div>
                </div>

                <!-- 悬停效果的光晕 -->
                <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
        </div>

        <!-- 第二层：各部门 -->
        <div class="level level-2">
            <!-- 连接线 -->
            <div class="connection-line vertical-main"></div>
            <div class="connection-line horizontal-main"></div>

            <!-- 销售部 -->
            <div class="node-container manager-node bg-gradient-to-br from-green-50 to-emerald-100 border-2 border-green-300 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-64 text-center relative overflow-hidden">
                <div class="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-sm">
                    部门
                </div>

                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-emerald-500"></div>

                <div class="p-5 pt-6">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-green-100 flex items-center justify-center">
                        <i class="fas fa-chart-line text-green-600 text-lg"></i>
                    </div>

                    <h3 class="text-lg font-bold text-green-900 mb-2 leading-tight">
                        销售部
                    </h3>

                    <div class="flex justify-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-leaf mr-1"></i>
                            执行部门
                        </span>
                    </div>
                </div>

                <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>

            <!-- 售后支持部 -->
            <div class="node-container manager-node bg-gradient-to-br from-purple-50 to-violet-100 border-2 border-purple-300 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-64 text-center relative overflow-hidden">
                <div class="absolute top-2 right-2 bg-purple-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-sm">
                    部门
                </div>

                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-violet-500"></div>

                <div class="p-5 pt-6">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-purple-100 flex items-center justify-center">
                        <i class="fas fa-headset text-purple-600 text-lg"></i>
                    </div>

                    <h3 class="text-lg font-bold text-purple-900 mb-2 leading-tight">
                        售后支持部
                    </h3>

                    <div class="flex justify-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            <i class="fas fa-leaf mr-1"></i>
                            执行部门
                        </span>
                    </div>
                </div>

                <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>

            <!-- 研发部 -->
            <div class="node-container manager-node bg-gradient-to-br from-orange-50 to-amber-100 border-2 border-orange-300 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-64 text-center relative overflow-hidden">
                <div class="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-sm">
                    部门
                </div>

                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-400 to-amber-500"></div>

                <div class="p-5 pt-6">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-orange-100 flex items-center justify-center">
                        <i class="fas fa-code text-orange-600 text-lg"></i>
                    </div>

                    <h3 class="text-lg font-bold text-orange-900 mb-2 leading-tight">
                        研发部
                    </h3>

                    <div class="flex justify-center items-center space-x-4 text-xs text-gray-500">
                        <span class="flex items-center">
                            <i class="fas fa-sitemap mr-1"></i>
                            2 个下属部门
                        </span>
                    </div>
                </div>

                <!-- 研发部连接线 -->
                <div class="connection-line rd-vertical"></div>
                <div class="connection-line rd-horizontal"></div>

                <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
        </div>

        <!-- 第三层：研发部下属部门 -->
        <div class="level level-3">
            <!-- B2B事业部 -->
            <div class="node-container employee-node bg-gradient-to-br from-teal-50 to-cyan-100 border-2 border-teal-300 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-64 text-center relative overflow-hidden">
                <div class="connection-line rd-vertical-down"></div>
                
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-teal-400 to-cyan-500"></div>

                <div class="p-5 pt-6">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-teal-100 flex items-center justify-center">
                        <i class="fas fa-building text-teal-600 text-lg"></i>
                    </div>

                    <h3 class="text-lg font-bold text-teal-900 mb-2 leading-tight">
                        B2B事业部
                    </h3>

                    <div class="flex justify-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                            <i class="fas fa-leaf mr-1"></i>
                            执行部门
                        </span>
                    </div>
                </div>

                <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>

            <!-- 网站事业部 -->
            <div class="node-container employee-node bg-gradient-to-br from-rose-50 to-pink-100 border-2 border-rose-300 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-64 text-center relative overflow-hidden">
                <div class="connection-line rd-vertical-down"></div>
                
                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-rose-400 to-pink-500"></div>

                <div class="p-5 pt-6">
                    <div class="w-12 h-12 mx-auto mb-3 rounded-full bg-rose-100 flex items-center justify-center">
                        <i class="fas fa-globe text-rose-600 text-lg"></i>
                    </div>

                    <h3 class="text-lg font-bold text-rose-900 mb-2 leading-tight">
                        网站事业部
                    </h3>

                    <div class="flex justify-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-rose-100 text-rose-800">
                            <i class="fas fa-leaf mr-1"></i>
                            执行部门
                        </span>
                    </div>
                </div>

                <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
        </div>

        <!-- 图例 -->
        <div class="mt-8 p-4 bg-white rounded-lg shadow-md">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">组织架构说明</h4>
            <div class="flex flex-wrap gap-4 text-sm">
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded bg-blue-500 mr-2"></div>
                    <span>高管层</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded bg-green-500 mr-2"></div>
                    <span>销售部门</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded bg-purple-500 mr-2"></div>
                    <span>支持部门</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded bg-orange-500 mr-2"></div>
                    <span>研发部门</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded bg-teal-500 mr-2"></div>
                    <span>B2B事业部</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded bg-rose-500 mr-2"></div>
                    <span>网站事业部</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>