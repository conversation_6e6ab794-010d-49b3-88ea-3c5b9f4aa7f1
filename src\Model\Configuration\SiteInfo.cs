﻿using MlSoft.Sites.Model.Entities.LocaleFields;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Sites.Model.Configuration
{

    /// <summary>
    /// 本实体是用于 将前端组件、页面用到 公共 的一些信息统一汇总缓存，如 站点、企业名称，联系电话等，菜单
    /// </summary>
    public class SiteInfo
    {
        /// <summary>
        /// 站点名称，来自 SiteSettings
        /// </summary>
        public Dictionary<string, string> SiteNames { get; set; } = new();

        /// <summary>
        /// Logo, 来自 SiteSettings
        /// </summary>
        public string LogoUrl { get; set; }

        /// <summary>
        /// 页底统计代码, 来自 SiteSettings
        /// </summary>
        public string? CountCode { get; set; }

        /// <summary>
        /// 域名，来自 SiteSettings的CustomSettings- Domain键值
        /// </summary>
        public string? Domain { get; set; }


        /// <summary>
        /// 企业名称，来自Company
        /// </summary>
        public Dictionary<string, string> CompanyNames { get; set; } = new();


        #region ContactInfo

        /// <summary>
        /// 电话号码 - 企业联系电话
        /// 日本企业网站"お問い合わせ"部分的核心联系方式
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// 传真号码 - 企业传真号码
        /// 日本商务环境中仍广泛使用的传统联系方式
        /// </summary>
        public string? Fax { get; set; }

        /// <summary>
        /// 电子邮箱 - 企业官方邮箱地址
        /// 用于"お問い合わせフォーム"和直接联系
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 官方网站 - 企业官网URL
        /// 通常用于集团企业或子公司的链接
        /// </summary>
        public string? Website { get; set; }

        /// <summary>
        /// 邮政编码 - 日本邮政编码格式（如123-4567）
        /// 配合地址信息使用，便于用户定位
        /// </summary>
        public string? PostalCode { get; set; }

        /// <summary>
        /// 地址信息 - 多语言的详细地址描述
        /// 支持日语和英语版本，对应"本社・支社所在地"
        /// </summary>
        public Dictionary<string, string>? Address { get; set; }


        #endregion



    }
}
