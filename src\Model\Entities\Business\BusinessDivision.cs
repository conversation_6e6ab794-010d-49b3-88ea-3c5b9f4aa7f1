using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.LocaleFields;
using System.Collections.Generic;
using System;

namespace MlSoft.Sites.Model.Entities.Business
{

public class BusinessDivision
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// 业务部门唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 多语言字段 - 包含部门名称、业务介绍、服务描述等本地化内容
    /// 对应日本企业网站"事業紹介"部分的各事业部详细介绍
    /// </summary>
    public Dictionary<string, BusinessDivisionLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 业务部门主图 - 展示业务特色的宣传图片
    /// 用于"事業紹介"页面的视觉呈现
    /// </summary>
    public string? ImageUrl { get; set; }

    /// <summary>
    /// 业务部门图标 - 小尺寸的业务标识图标
    /// 用于首页业务入口或导航菜单显示
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 显示顺序 - 控制各业务部门在页面中的排列顺序
    /// 通常按业务重要性或营收占比排序
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 记录更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 是否启用状态 - 控制该业务部门是否在前端展示
    /// </summary>
    public bool IsActive { get; set; } = true;
}
}
