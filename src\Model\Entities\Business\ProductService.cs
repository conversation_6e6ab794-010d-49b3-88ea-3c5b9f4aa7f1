﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Business
{

public class ProductService
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// 产品服务唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 所属业务部门ID - 关联产品归属的业务部门
    /// 用于按部门分类展示产品服务
    /// </summary>
    public string? BusinessDivisionId { get; set; }

    /// <summary>
    /// 多语言字段 - 包含产品名称、详细描述、特性说明等本地化内容
    /// 日本企业网站"製品・サービス"和"製品一覧"的核心数据
    /// </summary>
    public Dictionary<string, ProductServiceLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 产品图片集合 - 产品展示图、技术图片、应用场景图等
    /// 用于"製品・サービス"页面的视觉展示和产品手册
    /// </summary>
    public List<string> ImageUrls { get; set; } = new();

    /// <summary>
    /// 产品文档集合 - 技术规格书、说明书、安全数据表等文档
    /// 对应日本企业喜欢提供的"資料ダウンロード"功能，常为PDF格式
    /// </summary>
    public List<ProductDocument> Documents { get; set; } = new();

    /// <summary>
    /// 产品分类 - 按用途、行业、技术类型等进行分类
    /// 便于产品目录的分类浏览和筛选
    /// </summary>
    public ProductCategory Category { get; set; }

    /// <summary>
    /// 产品价格 - 标准零售价或参考价格
    /// 部分企业会在产品介绍中提供价格参考
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 价格货币单位 - 配合价格字段使用（如JPY、USD等）
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// 显示顺序 - 控制产品在页面中的排列顺序
    /// 通常按销量、重要性或战略地位排序
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 记录更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 是否启用状态 - 控制该产品是否在前端展示
    /// </summary>
    public bool IsActive { get; set; } = true;
}

public class ProductDocument
{
    /// <summary>
    /// 多语言字段 - 包含文档标题、描述等本地化内容
    /// 支持日语和英语版本的技术文档标题
    /// </summary>
    public Dictionary<string, DocumentLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 文档文件URL - 文档的下载链接地址
    /// 通常为PDF、Word或Excel格式的技术资料
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型 - 如PDF、DOC、XLS等文件格式标识
    /// 用于前端显示相应的文件类型图标
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小 - 文件的字节大小
    /// 提供给用户的下载参考信息
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 上传日期 - 文档的上传时间
    /// 用于版本管理和时效性标识
    /// </summary>
    public DateTime UploadDate { get; set; }
}
}

