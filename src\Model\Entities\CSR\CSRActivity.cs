﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.CSR
{

public class CSRActivity
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// CSR活动唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 多语言字段 - 包含活动名称、详细描述、成果总结等本地化内容
    /// 日本企业网站"CSR・サステナビリティ"部分的核心数据，体现企业社会责任
    /// </summary>
    public Dictionary<string, CSRActivityLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// CSR活动类型 - 如环境保护、社会贡献、员工关怀等分类
    /// 对应日本企业重视的"環境への取り組み"和"社会貢献活動"
    /// </summary>
    public CSRCategory Category { get; set; }

    /// <summary>
    /// 活动开始日期 - CSR项目的启动时间
    /// 用于展示企业持续的社会责任履行历程
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 活动结束日期 - 如为持续性项目则可为空
    /// 用于区分长期承诺和短期项目
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 活动图片集合 - 展示CSR活动现场照片、成果图片等
    /// 增强"CSR・サステナビリティ"页面的可信度和感染力
    /// </summary>
    public List<string> ImageUrls { get; set; } = new();

    /// <summary>
    /// CSR报告文件 - 详细的CSR报告PDF下载链接
    /// 日本企业喜欢提供详细资料供用户下载的传统
    /// </summary>
    public string? ReportFileUrl { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 记录更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 是否启用状态 - 控制该CSR活动是否在前端展示
    /// </summary>
    public bool IsActive { get; set; } = true;
}
}

