using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Model.Entities.Common
{
    public class ContactInfo
    {
        /// <summary>
        /// 电话号码 - 企业联系电话
        /// 日本企业网站"お問い合わせ"部分的核心联系方式
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// 传真号码 - 企业传真号码
        /// 日本商务环境中仍广泛使用的传统联系方式
        /// </summary>
        public string? Fax { get; set; }

        /// <summary>
        /// 电子邮箱 - 企业官方邮箱地址
        /// 用于"お問い合わせフォーム"和直接联系
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 官方网站 - 企业官网URL
        /// 通常用于集团企业或子公司的链接
        /// </summary>
        public string? Website { get; set; }

        /// <summary>
        /// 邮政编码 - 日本邮政编码格式（如123-4567）
        /// 配合地址信息使用，便于用户定位
        /// </summary>
        public string? PostalCode { get; set; }

        /// <summary>
        /// Google/Baidu等地图的嵌入链接
        /// </summary>
        public string? MapLink { get; set; }

        /// <summary>
        /// 保留，未用
        /// 地理位置坐标 - GPS经纬度信息
        /// 用于地图显示和导航功能，对应"アクセス"页面
        /// </summary>
        public GeoLocation? Location { get; set; }

        /// <summary>
        /// 多语言字段 - 包含地址、营业时间、交通信息等本地化内容
        /// </summary>
        public Dictionary<string, ContactInfoLocaleFields>? Locale { get; set; }
    }

    public class ContactInfoLocaleFields
    {
        /// <summary>
        /// 地址信息 - 多语言的详细地址描述
        /// 支持日语和英语版本，对应"本社・支社所在地"
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 营业时间描述 - 如"周一至周五 9:00-18:00"
        /// </summary>
        public string? BusinessHours { get; set; }

        /// <summary>
        /// 交通信息 - 如"JR山手线新宿站东口徒步5分钟"
        /// </summary>
        public string? AccessInfo { get; set; }
    }
}