using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Model.Entities.Common
{
    public class GeoLocation
    {
        /// <summary>
        /// 纬度 - 地理坐标的纬度值
        /// 用于企业据点的精确定位和地图展示
        /// </summary>
        public double Latitude { get; set; }

        /// <summary>
        /// 经度 - 地理坐标的经度值
        /// 配合纬度实现企业"拠点・アクセス"的GPS导航功能
        /// </summary>
        public double Longitude { get; set; }
    }
}