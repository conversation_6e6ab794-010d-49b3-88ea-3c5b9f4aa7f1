﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Model.Entities.Common;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Entities.Enums;

namespace MlSoft.Sites.Model.Entities.Company
{

public class Company
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// 企业实体唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 多语言字段 - 包含企业名称、简介、经营理念等本地化内容
    /// 日本企业网站必备的多语言支持（日语+英语面向海外投资者）
    /// </summary>
    public Dictionary<string, CompanyLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 设立日期 - 日本企业网站中"沿革"部分的起始时间，体现企业历史可信度
    /// </summary>
    public DateTime EstablishedDate { get; set; }

    /// <summary>
    /// 工商注册号 - 体现企业合法性和透明度，增强信赖感
    /// </summary>
    public string? RegistrationNumber { get; set; }

    /// <summary>
    /// 注册资本 - 显示企业实力，常在"会社概要"中展示
    /// </summary>
    public decimal? Capital { get; set; }

    /// <summary>
    /// 货币单位 - 配合注册资本使用（如JPY、USD等）
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// 联系信息 - 日本企业网站"お問い合わせ"部分的核心数据
    /// </summary>
    public ContactInfo? ContactInfo { get; set; }



    /// <summary>
    /// 企业规模 - 员工人数规模分类，体现企业实力
    /// </summary>
    public EmployeeScale? EmployeeScale { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 记录更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 是否启用状态
    /// </summary>
    public bool IsActive { get; set; } = true;
}
}

