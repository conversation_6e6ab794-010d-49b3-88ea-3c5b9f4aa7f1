﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Common;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Company
{

public class CompanyLocation
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;

    // 多语言字段
    public Dictionary<string, CompanyLocationLocaleFields> Locale { get; set; } = new();

    public LocationType Type { get; set; }
    public ContactInfo? ContactInfo { get; set; }

    /// <summary>
    /// 是否为主要据点 - 用于标识重要的据点信息
    /// </summary>
    public bool IsPrimary { get; set; }

    /// <summary>
    /// 显示顺序 - 用于控制据点在前端的显示顺序
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// 是否启用状态 - 控制该据点是否在前端展示
    /// </summary>
    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; }
}
}

