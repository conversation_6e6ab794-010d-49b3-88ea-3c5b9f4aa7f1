using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Model.Entities.Enums
{

    public class EnumFolderType
    {
        /// <summary>
        /// 公司Logo,Hero等图片
        /// </summary>
        public const string BasicInfo = "basic";

        /// <summary>
        /// 业务，产品
        /// </summary>
        public const string Product = "products";


        /// <summary>
        /// 新闻
        /// </summary>
        public const string News = "news";

    }

    public enum LocationType
    {
        Headquarters, // 总部
        Branch, // 分部
        Factory, // 工厂
        Office, // 办事处
        Laboratory, // 研究所
        Warehouse // 仓库
    }

    public enum EmployeeScale
    {
        Small = 1,      // 1-50人
        Medium = 2,     // 51-300人
        Large = 3,      // 301-1000人
        Enterprise = 4  // 1000人以上
    }

    public enum HistoryEventType
    {
        Establishment, // 成立
        Expansion, // 扩张
        ProductLaunch, // 产品发布
        Acquisition, // 收购
        Partnership, // 合作
        Award, // 获奖
        Milestone, // 里程碑
        Other // 其他
    }

    public enum ProductCategory
    {
        Product, // 产品
        Service, // 服务
        Solution, // 解决方案
        Technology // 技术
    }

    public enum NewsType
    {
        CompanyNews, // 公司新闻
        IndustryNews, // 行业新闻
        ProductNews, // 产品新闻
        EventNews, // 活动新闻
        Announcement // 公告
    }

    public enum ReportType
    {
        AnnualReport, // 年报
        QuarterlyReport, // 季报
        EarningsReport, // 决算短信
        SecuritiesReport // 有价证券报告书
    }

    public enum DocumentType
    {
        Notice, // 通知
        Agenda, // 议程
        Minutes, // 会议纪要
        Resolution, // 决议
        Other // 其他
    }

    public enum MeetingStatus
    {
        Scheduled, // 已安排
        InProgress, // 进行中
        Completed, // 已完成
        Cancelled // 已取消
    }

    public enum CSRCategory
    {
        Environment, // 环境保护
        SocialContribution, // 社会贡献
        Governance, // 治理
        EmployeeWelfare, // 员工福利
        CommunitySupport, // 社区支持
        Education, // 教育支持
        DisasterRelief, // 灾害救助
        Healthcare //
    }

    public enum JobType
    {
        NewGraduate, // 校园招聘
        MidCareer, // 社会招聘
        Internal // 内部招聘
    }

    public enum EmploymentType
    {
        FullTime, // 全职
        PartTime, // 兼职
        Contract, // 合同
        Temporary, // 临时
        Internship // 实习
    }

    public enum ExperienceLevel
    {
        Entry, // 入门级
        Junior, // 初级
        Mid, // 中级
        Senior, // 高级
        Executive // 管理层
    }

    public enum ComponentCategory
    {
        Header, // 头部
        Hero, // 主视觉
        Content, // 内容
        Navigation, // 导航
        Footer, // 底部
        Form, // 表单
        Media, // 媒体
        Layout // 布局
    }

    public enum PageStatus
    {
        Draft, // 草稿
        Preview, // 预览
        Published, // 已发布
        Disabled // 已归档
    }

    public enum PageContentStatus
    {
        Draft = 0,      // 草稿
        Review = 1,     // 待审核
        Published = 2,  // 已发布
        Archived = 3    // 已归档
    }

    public enum NewsStatus
    {
        Draft = 0,      // 草稿
        Review = 1,     // 待审核
        Published = 2,  // 已发布
    }

    public enum NewsSource
    {
        Internal = 0,       // 内部发布
        External = 1,       // 外部转载
        PressRelease = 2,   // 新闻稿
        MediaReport = 3,    // 媒体报道
        Partner = 4         // 合作伙伴
    }

    public enum NewsPriority
    {
        Low = 0,        // 低优先级
        Normal = 1,     // 普通优先级
        High = 2,       // 高优先级
        Urgent = 3      // 紧急优先级
    }

    public enum BatchOperationType
    {
        Publish,        // 发布
        Unpublish,      // 取消发布
        Feature,        // 设为推荐
        Unfeature,      // 取消推荐
        Delete,         // 删除
        Archive,        // 归档
        ChangeStatus,   // 更改状态
        ChangeType      // 更改类型
    }

    public enum NewsReviewStatus
    {
        Pending = 0,    // 待审核
        Approved = 1,   // 已通过
        Rejected = 2,   // 已拒绝
        NeedRevision = 3 // 需要修改
    }



    public enum InterviewType
    {
        NewEmployee = 0,    // 新员工访谈
        Veteran = 1,        // 资深员工访谈
        Management = 2,     // 管理层访谈
        Technical = 3,      // 技术人员访谈
        Sales = 4,          // 销售人员访谈
        Other = 5           // 其他
    }

    public enum PublishStatus
    {
        Draft = 0,          // 草稿
        Published = 1,      // 已发布
        Archived = 2        // 已归档
    }

    public enum MessageType
    {
        GeneralInquiry = 0,     // 一般咨询
        ProductInquiry = 1,     // 产品咨询
        ServiceInquiry = 2,     // 服务咨询
        TechnicalSupport = 3,   // 技术支持
        BusinessCooperation = 4, // 商务合作
        PartnershipInquiry = 5, // 合作伙伴咨询
        MediaInquiry = 6,       // 媒体咨询
        CareerInquiry = 7,      // 招聘咨询
        Complaint = 8,          // 投诉建议
        Other = 9               // 其他
    }

    public enum MessageStatus
    {
        New = 0,                // 新留言
        InProgress = 1,         // 处理中
        WaitingForResponse = 2, // 等待回复
        WaitingForCustomer = 3, // 等待客户反馈
        Resolved = 4,           // 已解决
        Closed = 5,             // 已关闭
        Spam = 6                // 垃圾邮件
    }
}