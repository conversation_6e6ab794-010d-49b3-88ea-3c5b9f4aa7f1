﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.History
{

public class CompanyHistory
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// 企业历史事件唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 历史事件发生日期 - 日本企业网站"沿革"年表的核心字段
    /// 用于构建详细时间轴，体现企业发展历程的可信度
    /// </summary>
    public DateTime EventDate { get; set; }

    /// <summary>
    /// 历史事件类型 - 如创立、上市、重组、新业务等里程碑事件分类
    /// </summary>
    public HistoryEventType EventType { get; set; }

    /// <summary>
    /// 多语言字段 - 包含事件标题、详细描述等本地化内容
    /// 支持日语原文和英语翻译，面向不同受众群体
    /// </summary>
    public Dictionary<string, CompanyHistoryLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 事件配图 - 历史照片、纪念图片等视觉素材，增强"沿革"页面的吸引力
    /// </summary>
    public string? ImageUrl { get; set; }

    /// <summary>
    /// 显示顺序 - 用于在"沿革"页面中按重要性或时间顺序排列事件
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 是否启用状态 - 控制该历史事件是否在前端展示
    /// </summary>
    public bool IsActive { get; set; } = true;
}
}

