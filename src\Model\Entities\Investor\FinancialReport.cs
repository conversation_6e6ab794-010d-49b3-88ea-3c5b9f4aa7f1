﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Investor
{

public class FinancialReport
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// 财务报告唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 报告类型 - 如年报、季报、半年报等
    /// 日本上市企业"IR情報"部分的标准分类
    /// </summary>
    public ReportType Type { get; set; }


    /// <summary>
    /// 报告年份 - 财务数据所属年份
    /// 用于"IR情報"页面的年度归档和检索
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// 报告季度 - 季度报告的具体季度（1-4季度）
    /// 年度报告时该字段为空
    /// </summary>
    public int? Quarter { get; set; }

    /// <summary>
    /// 多语言字段 - 包含报告标题、摘要、分析等本地化内容
    /// 支持面向日本投资者的日语版和国际投资者的英语版
    /// </summary>
    public Dictionary<string, FinancialReportLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 报告文件下载链接 - 通常为PDF格式的详细财务报告
    /// 对应"決算短信・有価証券報告書"的下载功能
    /// </summary>
    public string? ReportFileUrl { get; set; }

    /// <summary>
    /// 报告发布日期 - 财务报告的正式公布时间
    /// 用于"IR情報"页面按时间排序和检索
    /// </summary>
    public DateTime PublishDate { get; set; }

    /// <summary>
    /// 营业收入 - 关键财务指标，用于快速展示
    /// 在"株主・投資家向け情報"中的核心数据
    /// </summary>
    public decimal? Revenue { get; set; }

    /// <summary>
    /// 净利润 - 关键财务指标，体现盈利能力
    /// 投资者关注的核心业绩数据
    /// </summary>
    public decimal? NetIncome { get; set; }

    /// <summary>
    /// 总资产 - 关键财务指标，体现企业规模
    /// 用于评估企业财务健康状况
    /// </summary>
    public decimal? TotalAssets { get; set; }

    /// <summary>
    /// 货币单位 - 财务数据的计量货币（如JPY、USD等）
    /// 确保国际投资者正确理解财务数据
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 是否已发布 - 控制报告是否对外公开
    /// 遵循上市公司信息披露规定
    /// </summary>
    public bool IsPublished { get; set; } = true;
}
}

