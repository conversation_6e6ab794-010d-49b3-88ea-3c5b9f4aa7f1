﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Investor
{

public class ShareholderMeeting
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;
    
    public DateTime MeetingDate { get; set; }
    
    // 多语言字段
    public Dictionary<string, ShareholderMeetingLocaleFields> Locale { get; set; } = new();
    
    public List<MeetingDocument> Documents { get; set; } = new();
    public MeetingStatus Status { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class MeetingDocument
{
    // 多语言字段
    public Dictionary<string, DocumentLocaleFields> Locale { get; set; } = new();
    
    public string FileUrl { get; set; } = string.Empty;
    public DocumentType Type { get; set; }
    public DateTime UploadDate { get; set; }
}
}

