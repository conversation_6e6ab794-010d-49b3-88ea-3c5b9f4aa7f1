﻿using System;

namespace MlSoft.Sites.Model.Entities.LocaleFields
{

public class CompanyLocaleFields
{
    public string? CompanyName { get; set; }
    public string? CompanyDescription { get; set; }
    public string? Mission { get; set; }
    public string? Vision { get; set; }
    public string? Address { get; set; }
}

public class CompanyLocationLocaleFields
{
    public string? LocationName { get; set; }
}

public class ExecutiveLocaleFields
{
    public string? Name { get; set; }
    public string? Position { get; set; }
    public string? Biography { get; set; }
    public string? Message { get; set; }
}

public class OrganizationLocaleFields
{
    public string? DepartmentName { get; set; }
    public string? Description { get; set; }
}

public class CompanyHistoryLocaleFields
{
    public string? EventTitle { get; set; }
    public string? EventDescription { get; set; }
}

public class BusinessDivisionLocaleFields
{
    public string? DivisionName { get; set; }
    public string? Description { get; set; }
    public string? Services { get; set; }
}

public class ProductServiceLocaleFields
{
    public string? ProductName { get; set; }
    public string? Description { get; set; }
    public string? Features { get; set; }
    public string? Specifications { get; set; }
}

public class NewsAnnouncementLocaleFields
{
    public string? Title { get; set; }
    public string? Summary { get; set; }
    public string? Content { get; set; }
    public string[]? Tags { get; set; }
    
    /// <summary>
    /// 新闻副标题 - 用于更详细的标题描述
    /// </summary>
    public string? Subtitle { get; set; }

    /// <summary>
    /// 新闻作者 - 显示新闻的撰写者
    /// </summary>
    public string? Author { get; set; }

    /// <summary>
    /// 新闻来源 - 如"公司内部"、"外部媒体"等
    /// </summary>
    public string? Source { get; set; }

    /// <summary>
    /// 分类标签 - 用于更细粒度的内容分类
    /// </summary>
    public string[]? Categories { get; set; }

    /// <summary>
    /// SEO描述 - 用于搜索引擎优化
    /// </summary>
    public string? SeoDescription { get; set; }

    /// <summary>
    /// SEO关键词 - 用于搜索引擎优化
    /// </summary>
    public string? SeoKeywords { get; set; }

    /// <summary>
    /// 新闻位置 - 如"东京总部"、"大阪工厂"等
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 相关链接 - 新闻中提到的相关链接
    /// </summary>
    public string[]? RelatedLinks { get; set; }

    /// <summary>
    /// 编辑备注 - 编辑时的备注信息
    /// 用于内部沟通和版本管理
    /// </summary>
    public string? EditNotes { get; set; }

    /// <summary>
    /// 审核状态描述 - 审核状态的详细描述
    /// 用于审核流程的详细记录
    /// </summary>
    public string? ReviewStatusDescription { get; set; }
}

public class FinancialReportLocaleFields
{
    public string? Title { get; set; }
    public string? Summary { get; set; }
}

public class ShareholderMeetingLocaleFields
{
    public string? MeetingTitle { get; set; }
    public string? Agenda { get; set; }
    public string? Location { get; set; }
}

public class CSRActivityLocaleFields
{
    public string? Title { get; set; }
    public string? Description { get; set; }
    public string? Impact { get; set; }
}

public class JobPositionLocaleFields
{
    public string? JobTitle { get; set; }
    public string? JobDescription { get; set; }
    public string? Requirements { get; set; }
    public string? Benefits { get; set; }
    public string? WorkLocation { get; set; }

    /// <summary>
    /// 职位亮点 - 吸引求职者的关键特色
    /// </summary>
    public string? Highlights { get; set; }

    /// <summary>
    /// 工作环境描述 - 办公环境、团队氛围等
    /// </summary>
    public string? WorkEnvironment { get; set; }

    /// <summary>
    /// 发展机会 - 职业发展路径说明
    /// </summary>
    public string? CareerDevelopment { get; set; }

    /// <summary>
    /// 应聘方式 - 如何申请该职位
    /// </summary>
    public string? ApplicationMethod { get; set; }
}

public class EmployeeInterviewLocaleFields
{
    public string? EmployeeName { get; set; }
    public string? Position { get; set; }
    public string? InterviewContent { get; set; }
    public string? WorkDescription { get; set; }
    public string? CompanyImpression { get; set; }
}

public class ComponentDefinitionLocaleFields
{
    public string? ComponentName { get; set; }
    public string? Description { get; set; }
}

public class ComponentTemplateLocaleFields
{
    public string? TemplateName { get; set; }
    public string? Description { get; set; }
}

public class PageConfigurationLocaleFields
{
    public string? PageTitle { get; set; }
    public string? MetaDescription { get; set; }
}

public class SiteSettingsLocaleFields
{
    public string? SiteTitle { get; set; }
}

public class SEOSettingsLocaleFields
{
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public string? MetaKeywords { get; set; }
    public string? OgTitle { get; set; }
    public string? OgDescription { get; set; }
    public string? TwitterTitle { get; set; }
    public string? TwitterDescription { get; set; }
}

public class DocumentLocaleFields
{
    public string? DocumentName { get; set; }
}
}

