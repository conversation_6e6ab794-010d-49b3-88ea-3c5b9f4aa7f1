using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;

namespace MlSoft.Sites.Model.Entities.Messages
{
    public class MessageInquiry
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;

        // 基本信息
        public string ContactName { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public string? ContactPhone { get; set; }
        public string? CompanyName { get; set; }
        public string? JobTitle { get; set; }

        // 留言内容
        public string Message { get; set; } = string.Empty;

        // 分类和状态
        public MessageType Type { get; set; }
        public MessageStatus Status { get; set; } = MessageStatus.New;

        // 来源信息
        public string? SourcePage { get; set; }
        public string? ReferrerUrl { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public string? UserAgent { get; set; }

        // 时间记录
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // 处理结果
        public string DealResult { get; set; } = string.Empty;

        // 管理标识
        public bool IsRead { get; set; } = false;
        public bool IsImportant { get; set; } = false;
        public string? ProcessedByUserId { get; set; }
        public DateTime? ProcessedAt { get; set; }
    }
}