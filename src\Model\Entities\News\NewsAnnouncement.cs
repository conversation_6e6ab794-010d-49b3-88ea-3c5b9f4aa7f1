﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.News
{

    public class NewsAnnouncement
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        /// <summary>
        /// 新闻公告唯一标识符
        /// </summary>
        public string Id { get; set; } = string.Empty;

        public string Slug { get; set; } = string.Empty;

        /// <summary>
        /// 多语言字段 - 包含新闻标题、正文内容、摘要等本地化内容
        /// 日本企业网站"ニュース / プレスリリース"部分的核心数据
        /// 支持定期更新以显示企业活跃度
        /// </summary>
        public Dictionary<string, NewsAnnouncementLocaleFields> Locale { get; set; } = new();

        /// <summary>
        /// 新闻类型 - 如企业公告、产品更新、媒体报道、CSR活动等分类
        /// 对应"最新情報"、"メディア掲載"、"お知らせ"等不同栏目
        /// </summary>
        public NewsType Type { get; set; }

        /// <summary>
        /// 发布日期 - 新闻的正式发布时间
        /// 用于新闻页面按时间排序，体现信息的时效性
        /// </summary>
        public DateTime PublishDate { get; set; }

        /// <summary>
        /// 新闻图片集合 - 配套的新闻图片、现场照片等
        /// 增强新闻内容的可读性和吸引力
        /// </summary>
        public List<string> ImageUrls { get; set; } = new();

        /// <summary>
        /// 缩略图 - 用于新闻列表页面展示的预览图片
        /// 提升"ニュース"页面的视觉效果
        /// </summary>
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// 是否为推荐新闻 - 标识重要新闻，用于首页推荐展示
        /// 突出重要的企业动态和里程碑事件
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// 阅读量 - 统计新闻的访问次数
        /// 用于分析用户关注度和内容效果
        /// </summary>
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 新闻来源 - 如内部发布、外部转载等
        /// 用于区分新闻的发布渠道和可信度
        /// </summary>
        public NewsSource Source { get; set; } = NewsSource.Internal;

        /// <summary>
        /// 外部链接 - 如果是转载新闻，提供原文链接
        /// 支持外部新闻的引用和追踪
        /// </summary>
        public string? ExternalUrl { get; set; }

        /// <summary>
        /// 新闻优先级 - 用于排序和推荐算法
        /// 数值越高优先级越高，用于重要新闻的置顶显示
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 相关新闻ID列表 - 用于推荐相关新闻
        /// 提高用户粘性和内容发现率
        /// </summary>
        public List<string> RelatedNewsIds { get; set; } = new();

        /// <summary>
        /// 标签列表 - 用于新闻分类和搜索
        /// 支持更灵活的内容组织和发现
        /// </summary>
        public List<string> Tags { get; set; } = new();


        /// <summary>
        /// 创建者ID - 记录新闻的创建者
        /// 用于权限控制和操作审计
        /// </summary>
        public string? CreatedById { get; set; }

        /// <summary>
        /// 最后修改者ID - 记录最后修改新闻的管理员
        /// 用于操作审计和权限控制
        /// </summary>
        public string? LastModifiedById { get; set; }


        /// <summary>
        /// 新闻状态 - 草稿、待审核、已发布、已归档
        /// 支持完整的内容管理流程
        /// </summary>
        public NewsStatus Status { get; set; } = NewsStatus.Draft;
    }
}

