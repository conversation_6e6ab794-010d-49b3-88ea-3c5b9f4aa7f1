﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Organization
{

public class Executive
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// 高管信息唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 多语言字段 - 包含姓名、职位、简历、致辞内容等本地化信息
    /// 日本企业网站"役員紹介"和"社長メッセージ"的核心数据
    /// </summary>
    public Dictionary<string, ExecutiveLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 高管照片URL - 日本企业网站高管介绍的标准配置
    /// 体现企业的透明度和人性化形象
    /// </summary>
    public string? PhotoUrl { get; set; }

    /// <summary>
    /// 显示顺序 - 按职位层级或重要性排序显示高管信息
    /// 社长通常排在首位
    /// </summary>
    public int DisplayOrder { get; set; }

    /// <summary>
    /// 是否为社长 - 标识该高管是否为公司社长
    /// 用于"社長メッセージ"功能和特殊展示处理
    /// </summary>
    public bool IsPresident { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 记录更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 是否启用状态 - 控制该高管信息是否在前端展示
    /// </summary>
    public bool IsActive { get; set; } = true;
}
}

