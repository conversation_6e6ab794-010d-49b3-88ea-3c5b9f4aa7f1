﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Organization
{

public class OrganizationStructure
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;
    
    public string? ParentDepartmentId { get; set; }
    
    // 多语言字段
    public Dictionary<string, OrganizationLocaleFields> Locale { get; set; } = new();
    
    public int Level { get; set; }
    public int DisplayOrder { get; set; }

    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public bool IsActive { get; set; } = true;
}
}

