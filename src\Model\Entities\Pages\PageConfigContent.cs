using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Model.Entities.Pages
{
    /// <summary>
    /// 页面配置内容实体 - 用于存储完整的页面配置信息
    /// 该实体不对应数据库表，而是序列化为JSON后加密存储在PageConfiguration.Config字段中
    /// </summary>
    public class PageConfigContent
    {
        public string PageKey { get; set; } = string.Empty;
        public string Route { get; set; } = string.Empty;
        public string LayoutTemplate { get; set; } = "_Layout";
        public List<PageComponentConfig> Components { get; set; } = new();
        public string? GeneratedViewPath { get; set; }
        public PagePerformanceConfig Performance { get; set; } = new();
        public PageCacheConfiguration CacheConfig { get; set; } = new();
        public PageAccessControlConfig AccessControl { get; set; } = new();
    }

    /// <summary>
    /// 页面组件配置
    /// </summary>
    public class PageComponentConfig
    {
        public string ComponentDefinitionId { get; set; } = string.Empty;

        /// <summary>
        /// 组件在当前页面中的唯一标识，每次添加时会自动加上，保证一个页面有多个相同组件时，在页面填充数据时不会搞乱。
        /// </summary>
        public string IdNo { get; set; } = string.Empty;

        /// <summary>
        /// VariantId
        /// </summary>
        public string TemplateKey { get; set; } = string.Empty;
        public string ParametersJson { get; set; } = string.Empty;
        public int DisplayOrder { get; set; }
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// The number of columns the component should span in a 12-column grid.
        /// Defaults to 12 (full width).
        /// </summary>
        public int ColumnSpan { get; set; } = 12;
    }

    /// <summary>
    /// 页面性能配置
    /// </summary>
    public class PagePerformanceConfig
    {
        public bool EnableImageLazyLoading { get; set; } = true;
        public bool EnableComponentLazyLoading { get; set; } = false;
        public int MaxComponentsPerPage { get; set; } = 20;
        public bool EnableBundleOptimization { get; set; } = true;
        public int ImageQuality { get; set; } = 85;
        public bool EnableWebpFormat { get; set; } = true;
    }

    /// <summary>
    /// 页面缓存配置
    /// </summary>
    public class PageCacheConfiguration
    {
        public int CacheDurationMinutes { get; set; } = 60;
        public List<string> CacheVaryByParams { get; set; } = new();
        public bool EnableCDNCache { get; set; } = true;
        public List<string> CacheInvalidationTags { get; set; } = new();
        public bool EnableOutputCache { get; set; } = true;
        public string CacheProfile { get; set; } = string.Empty;
    }

    /// <summary>
    /// 页面访问控制配置
    /// </summary>
    public class PageAccessControlConfig
    {
        public bool RequireAuthentication { get; set; } = false;
        public List<string> RequiredRoles { get; set; } = new();
        public List<string> AllowedUsers { get; set; } = new();
        public DateTime? PublishDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public List<string> AllowedCountries { get; set; } = new();
        public bool EnableGeoRestriction { get; set; } = false;
    }
}