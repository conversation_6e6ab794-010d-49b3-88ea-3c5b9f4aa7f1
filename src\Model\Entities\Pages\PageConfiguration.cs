﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;

namespace MlSoft.Sites.Model.Entities.Pages
{

    public class PageConfiguration
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;

        // 多语言页面名称
        public Dictionary<string, string> Name { get; set; } = new();

        // 加密的PageConfigContent JSON字符串
        public string Config { get; set; } = string.Empty;

        public PageStatus Status { get; set; }
        public DateTime? PublishDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }


        /// <summary>
        /// 解密后的配置信息-PageConfigContent
        /// </summary>
        [BsonIgnore]
        public PageConfigContent? PageCfgContent { get; set; } = null;
    }

}