using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;

namespace MlSoft.Sites.Model.Entities.Pages
{
    /// <summary>
    /// 网页内容管理实体 - 存储页面组件的具体内容数据
    /// </summary>
    public class PageManage
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 关联的页面配置ID
        /// </summary>
        public string PageConfigurationId { get; set; } = string.Empty;

        /// <summary>
        /// 页面标识键
        /// </summary>
        public string PageKey { get; set; } = string.Empty;

        /// <summary>
        /// 组件数据列表 - 存储每个组件的具体内容
        /// </summary>
        public List<ComponentData> ComponentsData { get; set; } = new();

        /// <summary>
        /// 内容状态
        /// </summary>
        public PageContentStatus Status { get; set; } = PageContentStatus.Draft;

        /// <summary>
        /// 发布日期
        /// </summary>
        public DateTime? PublishDate { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string? UpdatedBy { get; set; }
    }

    /// <summary>
    /// 组件数据条目 - 存储单个组件的内容数据
    /// </summary>
    public class ComponentData
    {
        /// <summary>
        /// 组件定义ID (如: Hero, SEO, Content等)
        /// </summary>
        public string ComponentDefinitionId { get; set; } = string.Empty;

        /// <summary>
        /// 模板键 (如: Default, Corporate等)
        /// </summary>
        public string TemplateKey { get; set; } = string.Empty;

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 组件ViewModel的JSON序列化字符串
        /// 多语言字段以 {"zh":"中文","en":"English","ja":"日本語"} 格式存储
        /// </summary>
        public string DataJson { get; set; } = string.Empty;

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// 关联页面配置中的组件分配的IdNo,用于处理同一页面有多个相同组件时，数据区分
        /// </summary>
        public string IdNo { get; set; } = string.Empty;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
    }
}