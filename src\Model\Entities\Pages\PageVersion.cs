﻿using System;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Sites.Model.Entities.Pages
{

public class PageVersion
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;
    
    public string PageConfigurationId { get; set; } = string.Empty;
    public int VersionNumber { get; set; }
    public string ConfigurationSnapshot { get; set; } = string.Empty;
    
    public string? ChangeDescription { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public bool IsCurrentVersion { get; set; } = false;
}
}

