﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Recruitment
{

    public class EmployeeInterview
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;

        public string? DepartmentId { get; set; }
        public int YearsOfService { get; set; }

        // 多语言字段
        public Dictionary<string, EmployeeInterviewLocaleFields> Locale { get; set; } = new();

        public string? PhotoUrl { get; set; }


        public DateTime InterviewDate { get; set; }
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 访谈类型 - 如新人访谈、资深员工访谈等
        /// </summary>
        public InterviewType InterviewType { get; set; }

        /// <summary>
        /// 访谈状态 - 草稿、已发布等
        /// </summary>
        public PublishStatus Status { get; set; } = PublishStatus.Draft;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }


        /// <summary>
        /// 是否推荐到首页
        /// </summary>
        public bool IsFeatured { get; set; }

        public DateTime CreatedAt { get; set; }
    }
}