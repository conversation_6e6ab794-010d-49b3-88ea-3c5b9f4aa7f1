﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Recruitment
{

public class JobPosition
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    /// <summary>
    /// 职位信息唯一标识符
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 所属部门ID - 关联业务部门信息
    /// 用于按部门分类展示招聘职位
    /// </summary>
    public string? DepartmentId { get; set; }

    /// <summary>
    /// 多语言字段 - 包含职位名称、职责描述、任职要求等本地化内容
    /// 日本企业网站"採用情報"的核心数据，支持详细的职位说明
    /// </summary>
    public Dictionary<string, JobPositionLocaleFields> Locale { get; set; } = new();

    /// <summary>
    /// 职位类型 - 如技术类、管理类、销售类等职能分类
    /// 便于"採用情報"页面的职位筛选和分类展示
    /// </summary>
    public JobType Type { get; set; }

    /// <summary>
    /// 雇佣类型 - 如正社员、契约社员、派遣等日本特有的雇佣形式
    /// 对应日本劳动法规的不同雇佣类别
    /// </summary>
    public EmploymentType EmploymentType { get; set; }

    /// <summary>
    /// 经验要求 - 如新卒、有经验者等
    /// 日本企业区分"新卒採用"和"中途採用"的重要字段
    /// </summary>
    public ExperienceLevel ExperienceLevel { get; set; }

    /// <summary>
    /// 薪资下限 - 职位薪资范围的最低值
    /// 用于"募集要項"中的薪资待遇展示
    /// </summary>
    public decimal? SalaryMin { get; set; }

    /// <summary>
    /// 薪资上限 - 职位薪资范围的最高值
    /// 与薪资下限配合展示完整的薪资区间
    /// </summary>
    public decimal? SalaryMax { get; set; }

    /// <summary>
    /// 薪资货币单位 - 配合薪资数值使用（如JPY、USD等）
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// 职位发布日期 - 招聘信息的发布时间
    /// 用于"採用情報"页面按时间排序和展示
    /// </summary>
    public DateTime PostDate { get; set; }

    /// <summary>
    /// 申请截止日期 - 招聘的最后申请时间
    /// 重要的招聘时效信息，过期自动隐藏
    /// </summary>
    public DateTime? ApplicationDeadline { get; set; }

    /// <summary>
    /// 申请人数统计
    /// </summary>
    public int ApplicationCount { get; set; }



    /// <summary>
    /// 是否推荐职位 - 在"採用情報"页面突出显示
    /// </summary>
    public bool IsFeatured { get; set; }



    /// <summary>
    /// 工作时间描述 - 如"9:00-17:30"、"フレックスタイム制"
    /// </summary>
    public string? WorkingHours { get; set; }

    /// <summary>
    /// 试用期描述 - 日本企业常见的试用期说明
    /// </summary>
    public string? ProbationPeriod { get; set; }



    /// <summary>
    /// 是否启用状态 - 控制该职位是否在前端展示
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 记录更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
}

