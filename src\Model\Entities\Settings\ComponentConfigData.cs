﻿using Microsoft.VisualBasic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Sites.Model.Entities.Settings
{

    /// <summary>
    /// 用于存放共用组件数据，比如 Header, Footer，每个组件的每个变体只有一份数据，JSON格式
    /// </summary>
    public class ComponentConfigData
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 组件ID，如 Header, Footer 等
        /// </summary>
        public string ComponentId { get; set; } = string.Empty;

        /// <summary>
        /// 变体ID，如 Default, Corporate, Minimal 等
        /// </summary>
        public string VariantId { get; set; } = string.Empty;

        /// <summary>
        /// 组件配置数据，JSON格式
        /// </summary>
        public string JsonData { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新者
        /// </summary>
        public string UpdatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
}
