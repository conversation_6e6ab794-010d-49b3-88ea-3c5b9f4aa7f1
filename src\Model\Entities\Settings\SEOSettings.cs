﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Settings
{

public class SEOSettings
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;
    
    public string? PageConfigurationId { get; set; }
    
    // 多语言字段
    public Dictionary<string, SEOSettingsLocaleFields> Locale { get; set; } = new();
    
    public string? OgImageUrl { get; set; }
    public string? CanonicalUrl { get; set; }
    public List<string> AlternateUrls { get; set; } = new();
    
    // Twitter Card configuration
    public string? TwitterCard { get; set; } = "summary_large_image";
    public string? TwitterSite { get; set; }
    public string? TwitterCreator { get; set; }
    public string? TwitterImageUrl { get; set; }
    
    // Hreflang 支持
    public Dictionary<string, string> HreflangUrls { get; set; } = new();
    
    // Schema.org 配置
    public Dictionary<string, object> SchemaData { get; set; } = new();
    public List<string> EnabledSchemaTypes { get; set; } = new();
    public bool EnableAutoSchema { get; set; } = true;
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
}

