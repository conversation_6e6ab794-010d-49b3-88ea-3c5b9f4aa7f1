﻿using System;
using System.Collections.Generic;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Model.Entities.Settings
{

    public class SiteSettings
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;

        // 多语言字段
        public Dictionary<string, SiteSettingsLocaleFields> Locale { get; set; } = new();

        public string? LogoUrl { get; set; }
        public string? FaviconUrl { get; set; }
        
        /// <summary>
        /// 页底统计代码
        /// </summary>
        public string? CountCode { get; set; }


        public Dictionary<string, string> CustomSettings { get; set; } = new();

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}

