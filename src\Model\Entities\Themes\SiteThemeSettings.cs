using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Sites.Model.Entities.Themes
{
    /// <summary>
    /// 站点主题设置
    /// </summary>
    public class SiteThemeSettings
    {
        /// <summary>
        /// 设置ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;
        
        /// <summary>
        /// 公司/站点ID
        /// </summary>
        public string CompanyId { get; set; } = string.Empty;
        
        /// <summary>
        /// 当前激活的主题ID
        /// </summary>
        public string ActiveThemeId { get; set; } = string.Empty;
        
        /// <summary>
        /// 备份主题ID（用于回滚）
        /// </summary>
        public string? BackupThemeId { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 更新者
        /// </summary>
        public string UpdatedBy { get; set; } = string.Empty;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 是否启用主题
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// 主题应用历史记录
        /// </summary>
        public List<ThemeApplicationHistory> History { get; set; } = new();
    }
    
    /// <summary>
    /// 主题应用历史记录
    /// </summary>
    public class ThemeApplicationHistory
    {
        /// <summary>
        /// 应用的主题ID
        /// </summary>
        public string ThemeId { get; set; } = string.Empty;
        
        /// <summary>
        /// 应用时间
        /// </summary>
        public DateTime AppliedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 应用者
        /// </summary>
        public string AppliedBy { get; set; } = string.Empty;
        
        /// <summary>
        /// 应用结果
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 错误消息（如果失败）
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}