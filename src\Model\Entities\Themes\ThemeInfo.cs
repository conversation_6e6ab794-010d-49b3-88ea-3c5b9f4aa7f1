using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Model.Entities.Themes
{
    /// <summary>
    /// 主题信息
    /// </summary>
    public class ThemeInfo
    {
        /// <summary>
        /// 主题ID
        /// </summary>
        public string Id { get; set; } = string.Empty;
        
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 主题描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// 预览图片路径
        /// </summary>
        public string PreviewImage { get; set; } = string.Empty;
        
        /// <summary>
        /// CSS文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否为内置主题
        /// </summary>
        public bool IsBuiltIn { get; set; } = true;
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 颜色预览（用于管理界面显示）
        /// </summary>
        public Dictionary<string, string> ColorPreview { get; set; } = new();
        
        /// <summary>
        /// 主题版本
        /// </summary>
        public string Version { get; set; } = "1.0.0";
        
        /// <summary>
        /// 主题作者
        /// </summary>
        public string Author { get; set; } = "MlSoft";
        
        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}