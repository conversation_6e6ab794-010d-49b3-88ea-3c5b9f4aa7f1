using System;
using System.Collections.Generic;
using System.Linq;

namespace MlSoft.Sites.Model.Extensions
{
    public static class LocaleExtensions
    {
        /// <summary>
        /// 获取指定语言的值
        /// </summary>
        public static T GetLocale<T>(this Dictionary<string, T> locale, string languageCode) where T : class, new()
        {
            if (locale.TryGetValue(languageCode, out var value))
                return value;

            // 回退策略：ja -> en -> zh -> 第一个可用值
            var fallbackOrder = new[] { "ja", "en", "zh" };
            foreach (var fallback in fallbackOrder)
            {
                if (locale.TryGetValue(fallback, out var fallbackValue))
                    return fallbackValue;
            }

            return locale.Values.FirstOrDefault() ?? new T();
        }

        /// <summary>
        /// 设置指定语言的值
        /// </summary>
        public static void SetLocale<T>(this Dictionary<string, T> locale, string languageCode, T value)
        {
            locale[languageCode] = value;
        }

        /// <summary>
        /// 获取所有可用语言
        /// </summary>
        public static IEnumerable<string> GetAvailableLanguages<T>(this Dictionary<string, T> locale)
        {
            return locale.Keys;
        }

        /// <summary>
        /// 安全获取多语言对象的指定属性值，带回退机制
        /// </summary>
        /// <typeparam name="TEntity">包含Locale字典的实体类型</typeparam>
        /// <typeparam name="TLocale">语言字典中的值类型</typeparam>
        /// <typeparam name="TResult">要获取的属性类型</typeparam>
        /// <param name="entity">包含Locale的实体对象</param>
        /// <param name="languageCode">语言代码</param>
        /// <param name="propertySelector">属性选择器</param>
        /// <param name="fallbackValue">回退值（可选）</param>
        /// <returns>获取的属性值</returns>
        public static TResult? GetLocalizedProperty<TEntity, TLocale, TResult>(
            this TEntity entity, 
            string languageCode, 
            Func<TLocale, TResult> propertySelector,
            TResult? fallbackValue = default)
            where TEntity : class
            where TLocale : class, new()
        {
            if (entity == null) return fallbackValue;
            
            var localeProperty = typeof(TEntity).GetProperty("Locale");
            if (localeProperty?.GetValue(entity) is not Dictionary<string, TLocale> locale)
                return fallbackValue;

            var localeData = locale.GetLocale(languageCode);
            if (localeData == null) return fallbackValue;

            try
            {
                return propertySelector(localeData);
            }
            catch
            {
                return fallbackValue;
            }
        }
    }
}
