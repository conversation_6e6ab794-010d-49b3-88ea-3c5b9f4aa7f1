﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using MongoDB.Driver;
using MongoDB.Bson;
using System.Linq.Expressions;

namespace MlSoft.Sites.Service.Base
{

    public abstract class MongoBaseService<T> where T : class
    {
        protected readonly IMongoCollection<T> _collection;
        protected readonly IMongoDatabase _database;

        protected MongoBaseService(IMongoDatabase database, string collectionName)
        {
            _database = database;
            _collection = database.GetCollection<T>(collectionName);
        }

        public virtual async Task<T?> GetByIdAsync(string id)
        {
            var filter = Builders<T>.Filter.Eq("_id", ObjectId.Parse(id));
            return await _collection.Find(filter).FirstOrDefaultAsync();
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            return await _collection.Find(_ => true).ToListAsync();
        }

        public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> filter)
        {
            return await _collection.Find(filter).ToListAsync();
        }

        public virtual async Task<T?> FindOneAsync(Expression<Func<T, bool>> filter)
        {
            return await _collection.Find(filter).FirstOrDefaultAsync();
        }

        public virtual async Task<T> CreateAsync(T entity)
        {
            await _collection.InsertOneAsync(entity);
            return entity;
        }

        public virtual async Task<bool> UpdateAsync(string id, T entity)
        {
            var filter = Builders<T>.Filter.Eq("_id", ObjectId.Parse(id));
            var result = await _collection.ReplaceOneAsync(filter, entity);
            return result.ModifiedCount > 0;
        }

        public virtual async Task<bool> DeleteAsync(string id)
        {
            var filter = Builders<T>.Filter.Eq("_id", ObjectId.Parse(id));
            var result = await _collection.DeleteOneAsync(filter);
            return result.DeletedCount > 0;
        }

        public virtual async Task<long> CountAsync(Expression<Func<T, bool>>? filter = null)
        {
            return filter == null
                ? await _collection.CountDocumentsAsync(_ => true)
                : await _collection.CountDocumentsAsync(filter);
        }

        public virtual async Task<IEnumerable<T>> GetPagedAsync(int page, int pageSize, Expression<Func<T, bool>>? filter = null)
        {
            var query = filter == null ? _collection.Find(_ => true) : _collection.Find(filter);
            return await query.Skip((page - 1) * pageSize).Limit(pageSize).ToListAsync();
        }

        public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> filter)
        {
            var count = await _collection.CountDocumentsAsync(filter);
            return count > 0;
        }

        public virtual async Task<bool> UpdateFieldAsync<TField>(string id, Expression<Func<T, TField>> field, TField value)
        {
            var filter = Builders<T>.Filter.Eq("_id", ObjectId.Parse(id));
            var update = Builders<T>.Update.Set(field, value);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public virtual async Task<bool> IncrementAsync<TField>(string id, Expression<Func<T, TField>> field, TField value)
        {
            var filter = Builders<T>.Filter.Eq("_id", ObjectId.Parse(id));
            var update = Builders<T>.Update.Inc(field, value);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }


        public async Task<List<T>> PaginateAsync(Expression<Func<T, bool>> filter, int pageNumber, int pageSize)
        {
            return await _collection.Find(filter)
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
        }

        public async Task<List<T>> PaginateAsync(
            Expression<Func<T, bool>> filter,
            Expression<Func<T, object>> sortExpression,
            bool sortDescending,
            int pageNumber,
            int pageSize)
        {
            var query = _collection.Find(filter);

            if (sortDescending)
            {
                query = query.SortByDescending(sortExpression);
            }
            else
            {
                query = query.SortBy(sortExpression);
            }

            return await query
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
        }

        public async Task<List<TResult>> PaginateAsync<TResult>(
            Expression<Func<T, bool>> filter,
            Expression<Func<T, TResult>> projection,
            Expression<Func<T, object>> sortExpression,
            bool sortDescending,
            int pageNumber,
            int pageSize)
        {
            var query = _collection.Find(filter);

            if (sortDescending)
            {
                query = query.SortByDescending(sortExpression);
            }
            else
            {
                query = query.SortBy(sortExpression);
            }

            return await query
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .Project(projection)
                .ToListAsync();
        }
    }
}

