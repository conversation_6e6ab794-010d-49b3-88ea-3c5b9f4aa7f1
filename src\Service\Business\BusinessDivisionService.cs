﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Business;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Business
{

public class BusinessDivisionService : MongoBaseService<BusinessDivision>
{
    public BusinessDivisionService(IMongoDatabase database) : base(database, "BusinessDivisions")
    {
    }

    public async Task<IEnumerable<BusinessDivision>> GetActiveDivisionsAsync()
    {
        return await FindAsync(d => d.IsActive);
    }

    public async Task<IEnumerable<BusinessDivision>> GetDivisionsOrderedAsync()
    {
        var divisions = await FindAsync(d => d.IsActive);
        return divisions.OrderBy(d => d.DisplayOrder);
    }

    public async Task<BusinessDivision> CreateDivisionAsync(BusinessDivision division)
    {
        division.CreatedAt = DateTime.UtcNow;
        division.UpdatedAt = DateTime.UtcNow;
        return await CreateAsync(division);
    }

    public async Task<bool> UpdateDivisionAsync(string id, BusinessDivision division)
    {
        division.UpdatedAt = DateTime.UtcNow;
        return await UpdateAsync(id, division);
    }

    public async Task<bool> DeactivateDivisionAsync(string id)
    {
        return await UpdateFieldAsync(id, d => d.IsActive, false);
    }
}
}

