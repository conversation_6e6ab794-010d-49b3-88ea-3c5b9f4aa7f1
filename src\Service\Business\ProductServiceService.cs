﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Business;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Business
{

public class ProductServiceService : MongoBaseService<ProductService>
{
    public ProductServiceService(IMongoDatabase database) : base(database, "ProductServices")
    {
    }

    public async Task<IEnumerable<ProductService>> GetActiveProductsAsync()
    {
        return await FindAsync(p => p.IsActive);
    }

    public async Task<IEnumerable<ProductService>> GetProductsByDivisionAsync(string divisionId)
    {
        return await FindAsync(p => p.BusinessDivisionId == divisionId && p.IsActive);
    }

    public async Task<IEnumerable<ProductService>> GetProductsByCategoryAsync(ProductCategory category)
    {
        return await FindAsync(p => p.Category == category && p.IsActive);
    }

    public async Task<ProductService> CreateProductAsync(ProductService product)
    {
        product.CreatedAt = DateTime.UtcNow;
        product.UpdatedAt = DateTime.UtcNow;
        return await CreateAsync(product);
    }

    public async Task<bool> UpdateProductAsync(string id, ProductService product)
    {
        product.UpdatedAt = DateTime.UtcNow;
        return await UpdateAsync(id, product);
    }

    public async Task<bool> DeactivateProductAsync(string id)
    {
        return await UpdateFieldAsync(id, p => p.IsActive, false);
    }
}
}

