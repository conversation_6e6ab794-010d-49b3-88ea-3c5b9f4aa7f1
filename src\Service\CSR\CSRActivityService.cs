using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.CSR;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.CSR
{
    public class CSRActivityService : MongoBaseService<CSRActivity>
    {
        public CSRActivityService(IMongoDatabase database) : base(database, "CSRActivities")
        {
        }

        public async Task<IEnumerable<CSRActivity>> GetActiveActivitiesAsync(int Num)
        {
            return await _collection.Find(x=>x.IsActive == true).SortByDescending(x=>x.Id).Limit(Num).ToListAsync();
        }

        public async Task<IEnumerable<CSRActivity>> GetActiveActivitiesAsync()
        {
            return await FindAsync(c => c.IsActive);
        }

        public async Task<IEnumerable<CSRActivity>> GetActivitiesByCategoryAsync(CSRCategory category)
        {
            return await FindAsync(c => c.IsActive && c.Category == category);
        }

        public async Task<IEnumerable<CSRActivity>> GetOngoingActivitiesAsync()
        {
            var now = DateTime.UtcNow;
            return await FindAsync(c => c.IsActive && c.StartDate <= now && (!c.EndDate.HasValue || c.EndDate.Value >= now));
        }

        public async Task<IEnumerable<CSRActivity>> GetCompletedActivitiesAsync()
        {
            var now = DateTime.UtcNow;
            return await FindAsync(c => c.IsActive && c.EndDate.HasValue && c.EndDate.Value < now);
        }

        public async Task<IEnumerable<CSRActivity>> GetActivitiesByYearAsync(int year)
        {
            return await FindAsync(c => c.IsActive && c.StartDate.Year == year);
        }

        public async Task<CSRActivity> CreateActivityAsync(CSRActivity activity)
        {
            activity.CreatedAt = DateTime.UtcNow;
            activity.UpdatedAt = DateTime.UtcNow;
            return await CreateAsync(activity);
        }

        public async Task<bool> UpdateActivityAsync(string id, CSRActivity activity)
        {
            activity.UpdatedAt = DateTime.UtcNow;
            return await UpdateAsync(id, activity);
        }

        public async Task<bool> DeactivateActivityAsync(string id)
        {
            return await UpdateFieldAsync(id, c => c.IsActive, false);
        }
    }
}