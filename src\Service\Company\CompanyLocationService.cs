﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Company;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;
using System.Linq;

namespace MlSoft.Sites.Service.Company
{

    public class CompanyLocationService : MongoBaseService<CompanyLocation>
    {
        public CompanyLocationService(IMongoDatabase database) : base(database, "CompanyLocations")
        {
        }

        public async Task<IEnumerable<CompanyLocation>> GetLocationsByTypeAsync(LocationType type)
        {
            return await FindAsync(l => l.Type == type);
        }

        public async Task<IEnumerable<CompanyLocation>> GetLocationsOrderedAsync()
        {
            return (await FindAsync(x => x.IsActive == true)).OrderByDescending(x => x.IsPrimary).ThenBy(x => x.DisplayOrder);
        }

        public async Task<CompanyLocation> CreateLocationAsync(CompanyLocation location)
        {
            location.CreatedAt = DateTime.UtcNow;
            return await <PERSON>reate<PERSON><PERSON>(location);
        }
    }
}

