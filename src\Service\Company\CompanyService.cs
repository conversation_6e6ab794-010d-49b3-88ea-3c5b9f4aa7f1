﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Company;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Company
{

    public class CompanyService : MongoBaseService<Model.Entities.Company.Company>
    {
        public CompanyService(IMongoDatabase database) : base(database, "Companies")
        {
        }

        public async Task<Model.Entities.Company.Company?> GetCompany()
        {
            return await FindOneAsync(c => c.IsActive);
        }

        public async Task<IEnumerable<Model.Entities.Company.Company>> GetActiveCompaniesAsync()
        {
            return await FindAsync(c => c.IsActive);
        }

        public async Task<Model.Entities.Company.Company> CreateCompanyAsync(Model.Entities.Company.Company company)
        {
            company.CreatedAt = DateTime.UtcNow;
            company.UpdatedAt = DateTime.UtcNow;
            return await CreateAsync(company);
        }

        public async Task<bool> UpdateCompanyAsync(string id, Model.Entities.Company.Company company)
        {
            company.UpdatedAt = DateTime.UtcNow;
            return await UpdateAsync(id, company);
        }

        public async Task<bool> DeactivateCompanyAsync(string id)
        {
            return await UpdateFieldAsync(id, c => c.IsActive, false);
        }
    }
}

