using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.History;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.History
{
    public class CompanyHistoryService : MongoBaseService<CompanyHistory>
    {
        public CompanyHistoryService(IMongoDatabase database) : base(database, "CompanyHistories")
        {
        }

        public async Task<IEnumerable<CompanyHistory>> GetActiveHistoryAsync()
        {
            return await FindAsync(h => h.IsActive);
        }

        public async Task<IEnumerable<CompanyHistory>> GetHistoryOrderedAsync()
        {
            var history = await FindAsync(h => h.IsActive);
            return history.OrderByDescending(h => h.EventDate).ThenBy(h => h.DisplayOrder);
        }

        public async Task<IEnumerable<CompanyHistory>> GetHistoryByTypeAsync(HistoryEventType eventType)
        {
            return await FindAsync(h => h.IsActive && h.EventType == eventType);
        }

        public async Task<IEnumerable<CompanyHistory>> GetHistoryByYearAsync(int year)
        {
            return await FindAsync(h => h.IsActive && h.EventDate.Year == year);
        }

        public async Task<CompanyHistory> CreateHistoryAsync(CompanyHistory history)
        {
            history.CreatedAt = DateTime.UtcNow;
            return await CreateAsync(history);
        }

        public async Task<bool> DeactivateHistoryAsync(string id)
        {
            return await UpdateFieldAsync(id, h => h.IsActive, false);
        }
    }
}