using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Investor;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Investor
{
    public class FinancialReportService : MongoBaseService<FinancialReport>
    {
        public FinancialReportService(IMongoDatabase database) : base(database, "FinancialReports")
        {
        }

        public async Task<IEnumerable<FinancialReport>> GetPublishedReportsAsync()
        {
            return await FindAsync(r => r.IsPublished);
        }

        public async Task<IEnumerable<FinancialReport>> GetReportsByTypeAsync(ReportType type)
        {
            return await FindAsync(r => r.IsPublished && r.Type == type);
        }

        public async Task<IEnumerable<FinancialReport>> GetReportsByYearAsync(int year)
        {
            return await FindAsync(r => r.IsPublished && r.Year == year);
        }

        public async Task<FinancialReport?> GetLatestAnnualReportAsync()
        {
            var reports = await FindAsync(r => r.IsPublished && r.Type == ReportType.AnnualReport);
            return reports.OrderByDescending(r => r.Year).FirstOrDefault();
        }

        public async Task<IEnumerable<FinancialReport>> GetQuarterlyReportsAsync(int year)
        {
            return await FindAsync(r => r.IsPublished && r.Type == ReportType.QuarterlyReport && r.Year == year);
        }

        public async Task<FinancialReport> CreateReportAsync(FinancialReport report)
        {
            report.CreatedAt = DateTime.UtcNow;
            return await CreateAsync(report);
        }

        public async Task<bool> UpdateReportAsync(string id, FinancialReport report)
        {
            return await UpdateAsync(id, report);
        }

        public async Task<bool> PublishReportAsync(string id)
        {
            return await UpdateFieldAsync(id, r => r.IsPublished, true);
        }

        public async Task<bool> UnpublishReportAsync(string id)
        {
            return await UpdateFieldAsync(id, r => r.IsPublished, false);
        }
    }
}