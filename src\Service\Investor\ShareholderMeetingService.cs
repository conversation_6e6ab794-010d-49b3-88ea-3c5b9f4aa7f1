using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Investor;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Investor
{
    public class ShareholderMeetingService : MongoBaseService<ShareholderMeeting>
    {
        public ShareholderMeetingService(IMongoDatabase database) : base(database, "ShareholderMeetings")
        {
        }

        public async Task<IEnumerable<ShareholderMeeting>> GetUpcomingMeetingsAsync()
        {
            return await FindAsync(m => m.MeetingDate > DateTime.UtcNow && m.Status == MeetingStatus.Scheduled);
        }

        public async Task<IEnumerable<ShareholderMeeting>> GetCompletedMeetingsAsync()
        {
            return await FindAsync(m => m.Status == MeetingStatus.Completed);
        }

        public async Task<IEnumerable<ShareholderMeeting>> GetMeetingsByYearAsync(int year)
        {
            return await FindAsync(m => m.MeetingDate.Year == year);
        }

        public async Task<ShareholderMeeting?> GetLatestMeetingAsync()
        {
            var meetings = await GetAllAsync();
            return meetings.OrderByDescending(m => m.MeetingDate).FirstOrDefault();
        }

        public async Task<ShareholderMeeting> CreateMeetingAsync(ShareholderMeeting meeting)
        {
            meeting.CreatedAt = DateTime.UtcNow;
            meeting.UpdatedAt = DateTime.UtcNow;
            return await CreateAsync(meeting);
        }

        public async Task<bool> UpdateMeetingAsync(string id, ShareholderMeeting meeting)
        {
            meeting.UpdatedAt = DateTime.UtcNow;
            return await UpdateAsync(id, meeting);
        }

        public async Task<bool> UpdateMeetingStatusAsync(string id, MeetingStatus status)
        {
            return await UpdateFieldAsync(id, m => m.Status, status);
        }
    }
}