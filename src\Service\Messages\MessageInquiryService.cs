using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Driver;
using MongoDB.Bson;
using MlSoft.Sites.Model.Entities.Messages;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;
using MlSoft.Sites.Model.Entities.Common;

namespace MlSoft.Sites.Service.Messages
{
    public class MessageInquiryService : MongoBaseService<MessageInquiry>
    {
        public MessageInquiryService(IMongoDatabase database)
            : base(database, "MessageInquiries") { }

        // 获取分页留言列表
        public async Task<PagedResult<MessageInquiry>> GetPagedAsync(int page, int pageSize,
            MessageStatus? status = null, string? search = null)
        {
            var filterBuilder = Builders<MessageInquiry>.Filter;
            var filter = filterBuilder.Empty;

            if (status.HasValue)
            {
                filter = filterBuilder.And(filter, filterBuilder.Eq(m => m.Status, status.Value));
            }

            if (!string.IsNullOrEmpty(search))
            {
                var searchFilter = filterBuilder.Or(
                    filterBuilder.Regex(m => m.ContactName, new BsonRegularExpression(search, "i")),
                    filterBuilder.Regex(m => m.ContactEmail, new BsonRegularExpression(search, "i")),
                    filterBuilder.Regex(m => m.Message, new BsonRegularExpression(search, "i")),
                    filterBuilder.Regex(m => m.CompanyName, new BsonRegularExpression(search, "i"))
                );
                filter = filterBuilder.And(filter, searchFilter);
            }

            var total = await _collection.CountDocumentsAsync(filter);
            var messages = await _collection
                .Find(filter)
                .Sort(Builders<MessageInquiry>.Sort.Descending(m => m.CreatedAt))
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();

            return new PagedResult<MessageInquiry>
            {
                Items = messages,
                Total = (int)total,
                Page = page,
                PageSize = pageSize
            };
        }

        // 更新状态
        public async Task<bool> UpdateStatusAsync(string id, MessageStatus status, string? userId = null)
        {
            var updateBuilder = Builders<MessageInquiry>.Update
                .Set(m => m.Status, status)
                .Set(m => m.UpdatedAt, DateTime.UtcNow);

            if (status != MessageStatus.New)
            {
                updateBuilder = updateBuilder.Set(m => m.IsRead, true);
            }

            if (!string.IsNullOrEmpty(userId))
            {
                updateBuilder = updateBuilder
                    .Set(m => m.ProcessedByUserId, userId)
                    .Set(m => m.ProcessedAt, DateTime.UtcNow);
            }

            var result = await _collection.UpdateOneAsync(
                Builders<MessageInquiry>.Filter.Eq(m => m.Id, id), updateBuilder);
            return result.ModifiedCount > 0;
        }

        // 更新处理结果
        public async Task<bool> UpdateDealResultAsync(string id, string dealResult, string? userId = null)
        {
            var updateBuilder = Builders<MessageInquiry>.Update
                .Set(m => m.DealResult, dealResult)
                .Set(m => m.UpdatedAt, DateTime.UtcNow);

            if (!string.IsNullOrEmpty(userId))
            {
                updateBuilder = updateBuilder
                    .Set(m => m.ProcessedByUserId, userId)
                    .Set(m => m.ProcessedAt, DateTime.UtcNow);
            }

            var result = await _collection.UpdateOneAsync(
                Builders<MessageInquiry>.Filter.Eq(m => m.Id, id), updateBuilder);
            return result.ModifiedCount > 0;
        }

        // 标记为已读
        public async Task<bool> MarkAsReadAsync(string id, string? userId = null)
        {
            var updateBuilder = Builders<MessageInquiry>.Update
                .Set(m => m.IsRead, true)
                .Set(m => m.UpdatedAt, DateTime.UtcNow);

            if (!string.IsNullOrEmpty(userId))
            {
                updateBuilder = updateBuilder
                    .Set(m => m.ProcessedByUserId, userId)
                    .Set(m => m.ProcessedAt, DateTime.UtcNow);
            }

            var result = await _collection.UpdateOneAsync(
                Builders<MessageInquiry>.Filter.Eq(m => m.Id, id), updateBuilder);
            return result.ModifiedCount > 0;
        }

        // 标记重要
        public async Task<bool> ToggleImportantAsync(string id)
        {
            var message = await GetByIdAsync(id);
            if (message == null) return false;

            var update = Builders<MessageInquiry>.Update
                .Set(m => m.IsImportant, !message.IsImportant)
                .Set(m => m.UpdatedAt, DateTime.UtcNow);

            var result = await _collection.UpdateOneAsync(
                Builders<MessageInquiry>.Filter.Eq(m => m.Id, id), update);
            return result.ModifiedCount > 0;
        }

        // 获取统计信息
        public async Task<MessageStatistics> GetStatisticsAsync()
        {
            var total = await _collection.CountDocumentsAsync(FilterDefinition<MessageInquiry>.Empty);
            var newCount = await _collection.CountDocumentsAsync(
                Builders<MessageInquiry>.Filter.Eq(m => m.Status, MessageStatus.New));
            var todayCount = await _collection.CountDocumentsAsync(
                Builders<MessageInquiry>.Filter.Gte(m => m.CreatedAt, DateTime.Today));

            return new MessageStatistics
            {
                Total = (int)total,
                New = (int)newCount,
                Today = (int)todayCount
            };
        }
    }

    public class MessageStatistics
    {
        public int Total { get; set; }
        public int New { get; set; }
        public int Today { get; set; }
    }

    
}