﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>9.0</LangVersion>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Components\**" />
    <EmbeddedResource Remove="Components\**" />
    <None Remove="Components\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Routing\RouteUpdateExample.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.Identity.Mongo" Version="10.1.0" />
    <PackageReference Include="MongoDB.Bson" Version="3.4.3" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Model\MlSoft.Sites.Model.csproj" />
    <ProjectReference Include="..\Utility\MlSoft.Sites.Utility.csproj" />
  </ItemGroup>

</Project>
