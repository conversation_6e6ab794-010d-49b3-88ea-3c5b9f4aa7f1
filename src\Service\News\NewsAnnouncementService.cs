﻿using Microsoft.Extensions.Caching.Memory;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Entities.News;
using MlSoft.Sites.Model.Entities.Settings;
using MlSoft.Sites.Service.Base;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Service.News
{

    public class NewsAnnouncementService : MongoBaseService<NewsAnnouncement>
    {

        private readonly IMemoryCache _cache;

        private const string CACHE_NEWSFORCOMPONENT = "cache_newsforcomponent";
        // 静态内存缓存
        private static readonly object _lockNewsObject = new object();

        public NewsAnnouncementService(IMongoDatabase database, IMemoryCache cache) : base(database, "NewsAnnouncements")
        {
            _cache = cache;

        }


        /// <summary>
        /// 为组件获取新闻数据
        /// </summary>
        /// <param name="Num"></param>
        /// <returns></returns>
        public async Task<List<NewsAnnouncement>?> GetNewsForComponent(int Num)
        {
            if (_cache.TryGetValue(CACHE_NEWSFORCOMPONENT, out List<NewsAnnouncement>? news) && news != null)
            {
                return news;
            }


            var newsList = await PaginateAsync(x => x.Status == NewsStatus.Published, x => x.PublishDate, true, 1, Num);
            if (newsList != null && newsList.Count != 0)
            {

                var simpleList = new List<NewsAnnouncement>();
                foreach(var item in newsList)
                {
                    var simple = new NewsAnnouncement
                    {
                        Id = item.Id,
                        Slug = item.Slug,
                        Type = item.Type,
                        PublishDate = item.PublishDate,
                        ThumbnailUrl = item.ThumbnailUrl
                    };

                    var locale = new Dictionary<string, NewsAnnouncementLocaleFields>();
                    foreach(var loc in item.Locale)
                    {
                        locale[loc.Key] = new NewsAnnouncementLocaleFields
                        {
                            Title = loc.Value.Title,
                            Summary = loc.Value.Summary
                        };
                    }

                    simple.Locale = locale;

                    simpleList.Add(item);
                }

                // 加载组件并缓存
                lock (_lockNewsObject)
                {
                    // 缓存到IMemoryCache
                    _cache.Set(CACHE_NEWSFORCOMPONENT, simpleList, TimeSpan.FromDays(10));
                    return simpleList;
                }
            }

            return null;
        }

        public async Task<IEnumerable<NewsAnnouncement>> GetPublishedNewsAsync(int page = 1, int pageSize = 10)
        {
            return await GetPagedAsync(page, pageSize, n => n.Status == NewsStatus.Published);
        }

        public async Task<IEnumerable<NewsAnnouncement>> GetFeaturedNewsAsync()
        {
            return await FindAsync(n => n.Status == NewsStatus.Published && n.IsFeatured);
        }

        public async Task<IEnumerable<NewsAnnouncement>> GetNewsByTypeAsync(NewsType type, int page = 1, int pageSize = 10)
        {
            return await GetPagedAsync(page, pageSize, n => n.Status == NewsStatus.Published && n.Type == type);
        }

        public async Task<NewsAnnouncement> CreateNewsAsync(NewsAnnouncement news)
        {
            news.CreatedAt = DateTime.UtcNow;
            news.UpdatedAt = DateTime.UtcNow;
            var ret = await CreateAsync(news);
            _cache.Remove(CACHE_NEWSFORCOMPONENT);
            return ret;
        }

        public async Task<bool> UpdateNewsAsync(string id, NewsAnnouncement news)
        {
            news.UpdatedAt = DateTime.UtcNow;
            var ret = await UpdateAsync(id, news);

            _cache.Remove(CACHE_NEWSFORCOMPONENT);
            return ret;

        }

        public async Task<bool> IncrementViewCountAsync(string id)
        {
            return await IncrementAsync(id, n => n.ViewCount, 1);
        }

        public async Task<bool> ToggleFeaturedAsync(string id, bool isFeatured)
        {
            return await UpdateFieldAsync(id, n => n.IsFeatured, isFeatured);
        }

        public async Task<bool> TogglePublishAsync(string id, bool isPublished)
        {
            var status = isPublished ? NewsStatus.Published : NewsStatus.Draft;
            var ret = await UpdateFieldAsync(id, n => n.Status, status);
            _cache.Remove(CACHE_NEWSFORCOMPONENT);
            return ret;
        }

        /// <summary>
        /// 搜索新闻
        /// </summary>
        public async Task<IEnumerable<NewsAnnouncement>> SearchNewsAsync(string searchTerm, int page = 1, int pageSize = 10)
        {
            // 使用MongoDB的文本搜索，这里简化为标题和摘要的模糊匹配
            return await GetPagedAsync(page, pageSize, x => x.Status == NewsStatus.Published &&
                (x.Locale.Values.Any(l => l.Title != null && l.Title.Contains(searchTerm)) ||
                 x.Locale.Values.Any(l => l.Summary != null && l.Summary.Contains(searchTerm)) ||
                 x.Tags.Contains(searchTerm)));
        }

        /// <summary>
        /// 获取定时发布的新闻（暂不支持）
        /// </summary>
        public async Task<IEnumerable<NewsAnnouncement>> GetScheduledNewsAsync()
        {
            // 由于实体没有ScheduledPublishDate，返回草稿状态的新闻
            return await FindAsync(x => x.Status == NewsStatus.Draft);
        }

        /// <summary>
        /// 获取待审核新闻
        /// </summary>
        public async Task<IEnumerable<NewsAnnouncement>> GetPendingReviewNewsAsync(int page = 1, int pageSize = 10)
        {
            return await GetPagedAsync(page, pageSize, x => x.Status == NewsStatus.Review);
        }

        /// <summary>
        /// 获取草稿新闻
        /// </summary>
        public async Task<IEnumerable<NewsAnnouncement>> GetDraftNewsAsync()
        {
            return await FindAsync(x => x.Status == NewsStatus.Draft);
        }

        /// <summary>
        /// 获取指定创建者的新闻
        /// </summary>
        public async Task<IEnumerable<NewsAnnouncement>> GetNewsByCreatorAsync(string creatorId, int page = 1, int pageSize = 10)
        {
            return await GetPagedAsync(page, pageSize, x => x.CreatedById == creatorId);
        }

        /// <summary>
        /// 获取新闻统计信息
        /// </summary>
        public async Task<Dictionary<string, object>> GetNewsStatisticsAsync()
        {
            var totalCount = await CountAsync(x => x.Status == NewsStatus.Published);
            var featuredCount = await CountAsync(x => x.Status == NewsStatus.Published && x.IsFeatured);
            var draftCount = await CountAsync(x => x.Status == NewsStatus.Draft);
            var todayCount = await CountAsync(x => x.Status == NewsStatus.Published && x.PublishDate.Date == DateTime.UtcNow.Date);

            return new Dictionary<string, object>
            {
                ["TotalPublished"] = (int)totalCount,
                ["Featured"] = (int)featuredCount,
                ["Drafts"] = (int)draftCount,
                ["TodayPublished"] = (int)todayCount
            };
        }

        /// <summary>
        /// 获取新闻类型统计
        /// </summary>
        public async Task<Dictionary<NewsType, int>> GetNewsTypeStatisticsAsync()
        {
            var pipeline = new[]
            {
            new BsonDocument("$match", new BsonDocument("Status", (int)NewsStatus.Published)),
            new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$Type" },
                { "count", new BsonDocument("$sum", 1) }
            })
        };

            var results = await _collection.Aggregate<BsonDocument>(pipeline).ToListAsync();
            var statistics = new Dictionary<NewsType, int>();

            foreach (var result in results)
            {
                var type = (NewsType)result["_id"].AsInt32;
                var count = result["count"].AsInt32;
                statistics[type] = count;
            }

            return statistics;
        }

        /// <summary>
        /// 批量更新新闻状态
        /// </summary>
        public async Task<bool> BatchUpdateStatusAsync(List<string> newsIds, NewsStatus status)
        {
            var filter = Builders<NewsAnnouncement>.Filter.In(x => x.Id, newsIds);
            var update = Builders<NewsAnnouncement>.Update
                .Set(x => x.Status, status)
                .Set(x => x.UpdatedAt, DateTime.UtcNow);

            var result = await _collection.UpdateManyAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        /// <summary>
        /// 处理定时发布（简化版本）
        /// </summary>
        public async Task ProcessScheduledPublishingAsync()
        {
            // 由于实体没有ScheduledPublishDate，这里仅作为占位符方法
            await Task.CompletedTask;
        }

        /// <summary>
        /// 设置定时发布（简化版本）
        /// </summary>
        public async Task<bool> SchedulePublishAsync(string id, DateTime publishDate)
        {
            // 简化为直接设置发布日期和状态
            var news = await GetByIdAsync(id);
            if (news == null) return false;

            news.PublishDate = publishDate;
            news.Status = NewsStatus.Published;
            news.UpdatedAt = DateTime.UtcNow;

            return await UpdateAsync(id, news);
        }

        /// <summary>
        /// 审核新闻（简化版本）
        /// </summary>
        public async Task<bool> ReviewNewsAsync(string id, string reviewerId, NewsReviewStatus reviewStatus, string? reviewNotes = null)
        {
            var news = await GetByIdAsync(id);
            if (news == null) return false;

            // 简化审核流程
            news.UpdatedAt = DateTime.UtcNow;

            if (reviewStatus == NewsReviewStatus.Approved)
            {
                news.Status = NewsStatus.Published;
            }
            else if (reviewStatus == NewsReviewStatus.Rejected || reviewStatus == NewsReviewStatus.NeedRevision)
            {
                news.Status = NewsStatus.Draft;
            }

            return await UpdateAsync(id, news);
        }

        /// <summary>
        /// 获取审核统计信息
        /// </summary>
        public async Task<Dictionary<string, int>> GetReviewStatisticsAsync()
        {
            var pendingCount = await CountAsync(x => x.Status == NewsStatus.Review);
            var approvedCount = await CountAsync(x => x.Status == NewsStatus.Published);
            var rejectedCount = await CountAsync(x => x.Status == NewsStatus.Draft);

            return new Dictionary<string, int>
            {
                ["Pending"] = (int)pendingCount,
                ["Approved"] = (int)approvedCount,
                ["Rejected"] = (int)rejectedCount
            };
        }

        /// <summary>
        /// 根据过滤条件获取新闻列表
        /// </summary>
        public async Task<IEnumerable<NewsAnnouncement>> GetNewsWithFiltersAsync(NewsType? type, NewsStatus? status, string? searchTerm, int page = 1, int pageSize = 10)
        {
            var filterBuilder = Builders<NewsAnnouncement>.Filter;
            var filters = new List<FilterDefinition<NewsAnnouncement>>();

            if (type.HasValue)
            {
                filters.Add(filterBuilder.Eq(x => x.Type, type.Value));
            }

            if (status.HasValue)
            {
                filters.Add(filterBuilder.Eq(x => x.Status, status.Value));
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchFilters = new List<FilterDefinition<NewsAnnouncement>>
            {
                filterBuilder.Regex("Locale.ja.Title", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.en.Title", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.zh.Title", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.ja.Summary", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.en.Summary", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.zh.Summary", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.AnyEq(x => x.Tags, searchTerm)
            };
                filters.Add(filterBuilder.Or(searchFilters));
            }

            var combinedFilter = filters.Any() ? filterBuilder.And(filters) : filterBuilder.Empty;

            return await _collection.Find(combinedFilter)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .SortByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 根据过滤条件获取新闻总数
        /// </summary>
        public async Task<long> GetNewsCountWithFiltersAsync(NewsType? type, NewsStatus? status, string? searchTerm)
        {
            var filterBuilder = Builders<NewsAnnouncement>.Filter;
            var filters = new List<FilterDefinition<NewsAnnouncement>>();

            if (type.HasValue)
            {
                filters.Add(filterBuilder.Eq(x => x.Type, type.Value));
            }

            if (status.HasValue)
            {
                filters.Add(filterBuilder.Eq(x => x.Status, status.Value));
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchFilters = new List<FilterDefinition<NewsAnnouncement>>
            {
                filterBuilder.Regex("Locale.ja.Title", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.en.Title", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.zh.Title", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.ja.Summary", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.en.Summary", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.Regex("Locale.zh.Summary", new BsonRegularExpression(searchTerm, "i")),
                filterBuilder.AnyEq(x => x.Tags, searchTerm)
            };
                filters.Add(filterBuilder.Or(searchFilters));
            }

            var combinedFilter = filters.Any() ? filterBuilder.And(filters) : filterBuilder.Empty;

            return await _collection.CountDocumentsAsync(combinedFilter);
        }
    }
}

