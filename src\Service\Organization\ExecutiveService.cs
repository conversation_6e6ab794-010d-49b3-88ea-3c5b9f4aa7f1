using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Organization;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Organization
{
    public class ExecutiveService : MongoBaseService<Executive>
    {
        public ExecutiveService(IMongoDatabase database) : base(database, "Executives")
        {
        }

        public async Task<IEnumerable<Executive>> GetActiveExecutivesAsync()
        {
            return await FindAsync(e => e.IsActive);
        }

        public async Task<Executive?> GetPresidentAsync()
        {
            return await FindOneAsync(e => e.IsPresident && e.IsActive);
        }

        public async Task<IEnumerable<Executive>> GetExecutivesOrderedAsync()
        {
            var executives = await FindAsync(e => e.IsActive);
            return executives.OrderBy(e => e.DisplayOrder);
        }

        public async Task<Executive> CreateExecutiveAsync(Executive executive)
        {
            executive.CreatedAt = DateTime.UtcNow;
            executive.UpdatedAt = DateTime.UtcNow;
            return await <PERSON><PERSON><PERSON><PERSON>(executive);
        }

        public async Task<bool> UpdateExecutiveAsync(string id, Executive executive)
        {
            executive.UpdatedAt = DateTime.UtcNow;
            return await UpdateAsync(id, executive);
        }

        public async Task<bool> DeactivateExecutiveAsync(string id)
        {
            return await UpdateFieldAsync(id, e => e.IsActive, false);
        }
    }
}