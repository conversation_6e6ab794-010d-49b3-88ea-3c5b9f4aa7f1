using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Organization;
using MlSoft.Sites.Service.Base;
using System.Linq;

namespace MlSoft.Sites.Service.Organization
{
    public class OrganizationStructureService : MongoBaseService<OrganizationStructure>
    {
        public OrganizationStructureService(IMongoDatabase database) : base(database, "OrganizationStructures")
        {
        }

        public async Task<IEnumerable<OrganizationStructure>> GetActiveDepartmentsAsync()
        {
            return (await FindAsync(o => o.IsActive));
        }


        /// <summary>
        /// 返回 仿树状的列表 Id:Value
        /// Root
        /// |- D1
        ///     |- D11
        ///     |- D12
        /// |- D2
        ///     |- D21
        ///     |= D22
        /// </summary>
        /// <param name="language"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetTreeOfOrgan(string language)
        {
            var result = new Dictionary<string, string>();

            // Load all active departments
            var departments = await FindAsync(o => o.IsActive);
            var departmentList = new List<OrganizationStructure>(departments);

            // Helper to get localized name with fallbacks
            string GetDepartmentName(OrganizationStructure dept)
            {
                if (dept.Locale != null)
                {
                    if (dept.Locale.TryGetValue(language, out var loc) && !string.IsNullOrWhiteSpace(loc?.DepartmentName))
                    {
                        return loc!.DepartmentName!;
                    }

                    // fallback: first non-empty name
                    foreach (var kv in dept.Locale)
                    {
                        var name = kv.Value?.DepartmentName;
                        if (!string.IsNullOrWhiteSpace(name))
                        {
                            return name!;
                        }
                    }
                }
                return string.Empty;
            }

            // Build parent -> children map
            var childrenMap = new Dictionary<string, List<OrganizationStructure>>();
            var roots = new List<OrganizationStructure>();

            foreach (var dept in departmentList)
            {
                if (string.IsNullOrEmpty(dept.ParentDepartmentId))
                {
                    roots.Add(dept);
                }
                else
                {
                    var parentId = dept.ParentDepartmentId!;
                    if (!childrenMap.TryGetValue(parentId, out var list))
                    {
                        list = new List<OrganizationStructure>();
                        childrenMap[parentId] = list;
                    }
                    list.Add(dept);
                }
            }

            // Sort helper
            int Compare(OrganizationStructure a, OrganizationStructure b)
            {
                var order = a.DisplayOrder.CompareTo(b.DisplayOrder);
                if (order != 0) return order;
                return string.Compare(GetDepartmentName(a), GetDepartmentName(b), StringComparison.Ordinal);
            }

            roots.Sort(Compare);
            foreach (var key in new List<string>(childrenMap.Keys))
            {
                childrenMap[key].Sort(Compare);
            }

            // DFS to build labels
            void Traverse(OrganizationStructure node, int level)
            {
                var prefix = level > 0 ? new string(' ', level * 4) + "|- " : string.Empty;
                var label = prefix + GetDepartmentName(node);
                result[node.Id] = label;

                if (childrenMap.TryGetValue(node.Id, out var children))
                {
                    foreach (var child in children)
                    {
                        Traverse(child, level + 1);
                    }
                }
            }

            foreach (var root in roots)
            {
                Traverse(root, 0);
            }

            return result;
        }

        public async Task<IEnumerable<OrganizationStructure>> GetRootDepartmentsAsync()
        {
            return await FindAsync(o => o.IsActive && string.IsNullOrEmpty(o.ParentDepartmentId));
        }

        public async Task<IEnumerable<OrganizationStructure>> GetChildDepartmentsAsync(string parentId)
        {
            return await FindAsync(o => o.IsActive && o.ParentDepartmentId == parentId);
        }

        public async Task<IEnumerable<OrganizationStructure>> GetDepartmentsByLevelAsync(int level)
        {
            return await FindAsync(o => o.IsActive && o.Level == level);
        }

        public async Task<OrganizationStructure> CreateDepartmentAsync(OrganizationStructure department)
        {
            department.CreatedAt = DateTime.UtcNow;
            return await CreateAsync(department);
        }

        public async Task<bool> DeactivateDepartmentAsync(string id)
        {
            return await UpdateFieldAsync(id, o => o.IsActive, false);
        }
    }
}