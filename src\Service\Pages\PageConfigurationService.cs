﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Service.Base;
using MlSoft.Sites.Utility;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Service.Pages
{

    public class PageConfigurationService : MongoBaseService<PageConfiguration>
    {
        private readonly IMemoryCache _cache;
        private readonly IConfiguration _configuration;
        private readonly string _encryptionKey;

        /// <summary>
        /// 所有有效的页面配置
        /// </summary>
        protected const string CACHE_ALL_PAGECONFIG = "cache_all_pageconfig";


        // 静态内存缓存
        private static readonly object _lockPgObject = new object();


        public PageConfigurationService(IMongoDatabase database, IConfiguration configuration, IMemoryCache cache)
            : base(database, "PageConfigurations")
        {
            _configuration = configuration;
            _cache = cache;
            _encryptionKey = _configuration["PageConfiguration:EncryptionKey"] ?? throw new InvalidOperationException("PageConfiguration:EncryptionKey not configured");
        }


        private async Task ClearCache()
        {
            _cache.Remove(CACHE_ALL_PAGECONFIG);
        }

        /// <summary>
        /// 获取所有有效的页面配置，同时包括已解密的配置
        /// </summary>
        /// <returns></returns>
        private async Task<List<PageConfiguration>> GetAllPageConfiguration()
        {
            if (_cache.TryGetValue(CACHE_ALL_PAGECONFIG, out List<PageConfiguration>? pageConfigs) && pageConfigs != null)
            {
                return pageConfigs;
            }

            // 加载组件并缓存
            lock (_lockPgObject)
            {

                var pageConfigList = FindAsync(p => p.Status != PageStatus.Disabled).Result;

                foreach (var page in pageConfigList)
                {
                    page.PageCfgContent = DecryptPageConfig(page.Config);
                }

                // 缓存到IMemoryCache
                _cache.Set(CACHE_ALL_PAGECONFIG, pageConfigList, TimeSpan.FromDays(10));

                return pageConfigList.ToList();
            }
        }

        /// <summary>
        /// 根据PageKey获取页面配置
        /// </summary>
        public async Task<PageConfiguration?> GetByPageKeyAsync(string pageKey)
        {
            var allPageCfgs = await GetAllPageConfiguration();
            if (allPageCfgs != null)
            {
                var page = allPageCfgs.FirstOrDefault(x => x.PageCfgContent.PageKey == pageKey);
                return page;
            }

            return null;
        }

        /// <summary>
        /// 根据页面ID获取完整的页面配置内容
        /// </summary>
        public async Task<(PageConfiguration? page, PageConfigContent? content)> GetPageWithContentAsync(string id)
        {
            var allPageCfgs = await GetAllPageConfiguration();
            if (allPageCfgs != null)
            {
                var pageCfg = allPageCfgs.FirstOrDefault(x => x.Id == id);
                if (pageCfg != null)
                {
                    return (pageCfg, pageCfg.PageCfgContent);
                }
            }

            return (null, null);
        }

        /// <summary>
        /// 获取所有已发布的页面
        /// </summary>
        public async Task<IEnumerable<PageConfiguration>> GetPublishedPagesAsync()
        {
            var allPageCfgs = await GetAllPageConfiguration();
            return allPageCfgs.Where(p => p.Status == PageStatus.Published);
        }

        /// <summary>
        /// 获取已发布页面及其解密后的内容
        /// </summary>
        public async Task<IEnumerable<(PageConfiguration page, PageConfigContent? content)>> GetPublishedPagesWithContentAsync()
        {
            var allPageCfgs = await GetAllPageConfiguration();
            var publishedPages = allPageCfgs.Where(x => x.Status == PageStatus.Published);
            var result = new List<(PageConfiguration page, PageConfigContent? content)>();

            foreach (var page in publishedPages)
            {
                result.Add((page, page.PageCfgContent));
            }

            return result;
        }



        /// <summary>
        /// 获取页面列表（分页）
        /// </summary>
        public async Task<(List<PageConfiguration> pages, long totalCount)> GetPageListAsync(int page, int pageSize, PageStatus? status = null)
        {
            var allPageCfgs = await GetAllPageConfiguration();

            var totalCount = allPageCfgs.Count;


            if (status != null)
            {
                allPageCfgs = allPageCfgs.Where(x => x.Status == status.Value).ToList();
            }


            var list = allPageCfgs.OrderByDescending(x => x.UpdatedAt).Skip((page - 1) * pageSize).Take(pageSize).ToList();

            return (list, totalCount);
        }

        /// <summary>
        /// 创建新页面
        /// </summary>
        public async Task<PageConfiguration> CreatePageAsync(Dictionary<string, string> name, PageConfigContent content, string createdBy)
        {
            if (!ValidatePageConfigContent(content))
            {
                throw new ArgumentException("Invalid page config content");
            }

            var encryptedConfig = EncryptPageConfig(content);

            var page = new PageConfiguration
            {
                Name = name,
                Config = encryptedConfig,
                Status = PageStatus.Draft,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = createdBy,
                UpdatedBy = createdBy
            };

            var newPage = await CreateAsync(page);

            //清除缓存
            await ClearCache();

            return newPage;
        }

        /// <summary>
        /// 更新页面配置
        /// </summary>
        public async Task<bool> UpdatePageAsync(string id, Dictionary<string, string> name, PageConfigContent content, string updatedBy)
        {
            if (!ValidatePageConfigContent(content))
            {
                throw new ArgumentException("Invalid page config content");
            }

            var encryptedConfig = EncryptPageConfig(content);

            var updateDefinition = Builders<PageConfiguration>.Update
                .Set(p => p.Name, name)
                .Set(p => p.Config, encryptedConfig)
                .Set(p => p.UpdatedAt, DateTime.UtcNow)
                .Set(p => p.UpdatedBy, updatedBy);

            var filter = Builders<PageConfiguration>.Filter.Eq("_id", MongoDB.Bson.ObjectId.Parse(id));
            var result = await _collection.UpdateOneAsync(filter, updateDefinition);
            var ret = result.ModifiedCount > 0;

            if (ret)
            {
                await ClearCache();
            }

            return ret;

        }

        /// <summary>
        /// 发布页面
        /// </summary>
        public async Task<bool> PublishPageAsync(string id)
        {
            var updateDefinition = Builders<PageConfiguration>.Update
                .Set(p => p.Status, PageStatus.Published)
                .Set(p => p.PublishDate, DateTime.UtcNow)
                .Set(p => p.UpdatedAt, DateTime.UtcNow);

            var filter = Builders<PageConfiguration>.Filter.Eq("_id", MongoDB.Bson.ObjectId.Parse(id));
            var result = await _collection.UpdateOneAsync(filter, updateDefinition);
            var ret = result.ModifiedCount > 0;

            if (ret)
            {
                await ClearCache();
            }

            return ret;
        }

        /// <summary>
        /// 取消发布页面
        /// </summary>
        public async Task<bool> UnpublishPageAsync(string id)
        {
            var updateDefinition = Builders<PageConfiguration>.Update
                .Set(p => p.Status, PageStatus.Draft)
                .Set(p => p.PublishDate, (DateTime?)null)
                .Set(p => p.UpdatedAt, DateTime.UtcNow);

            var filter = Builders<PageConfiguration>.Filter.Eq("_id", MongoDB.Bson.ObjectId.Parse(id));
            var result = await _collection.UpdateOneAsync(filter, updateDefinition);
            var ret = result.ModifiedCount > 0;

            if (ret)
            {
                await ClearCache();
            }

            return ret;
        }

        /// <summary>
        /// 删除页面
        /// </summary>
        public async Task<bool> DeletePageAsync(string id)
        {
            var ret =  await DeleteAsync(id);

            if (ret)
            {
                await ClearCache();
            }

            return ret;
        }

        /// <summary>
        /// 检查页面Key是否唯一
        /// </summary>
        public async Task<bool> IsPageKeyUniqueAsync(string pageKey, string? excludeId = null)
        {
            var pages =  await GetAllPageConfiguration();

            var chk = pages.FirstOrDefault(x => x.Id != excludeId && x.PageCfgContent?.PageKey == pageKey);

            return chk == null;
        }

        /// <summary>
        /// 检查路由是否唯一
        /// </summary>
        public async Task<bool> IsRouteUniqueAsync(string route, string? excludeId = null)
        {

            var pages = await GetAllPageConfiguration();

            var chk = pages.FirstOrDefault(x => x.Id != excludeId && x.PageCfgContent?.Route == route);

            return chk == null;
        }

        /// <summary>
        /// 验证PageConfigContent对象格式是否正确
        /// </summary>
        /// <param name="configContent">要验证的配置内容</param>
        /// <returns>验证结果</returns>
        public bool ValidatePageConfigContent(PageConfigContent configContent)
        {
            if (configContent == null)
                return false;

            // 验证必填字段
            if (string.IsNullOrWhiteSpace(configContent.PageKey))
                return false;

            if (string.IsNullOrWhiteSpace(configContent.Route))
                return false;

            // 验证组件配置
            if (configContent.Components != null)
            {
                foreach (var component in configContent.Components)
                {
                    if (string.IsNullOrWhiteSpace(component.ComponentDefinitionId) ||
                        string.IsNullOrWhiteSpace(component.TemplateKey))
                        return false;

                    // 验证DisplayOrder不能为负数
                    if (component.DisplayOrder < 0)
                        return false;
                }
            }

            // 验证性能配置
            if (configContent.Performance != null)
            {
                if (configContent.Performance.ImageQuality < 1 || configContent.Performance.ImageQuality > 100)
                    return false;

                if (configContent.Performance.MaxComponentsPerPage < 1)
                    return false;
            }

            // 验证缓存配置
            if (configContent.CacheConfig != null)
            {
                if (configContent.CacheConfig.CacheDurationMinutes < 0)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 加密页面配置内容
        /// </summary>
        private string EncryptPageConfig(PageConfigContent content)
        {
            return DataCrypto.Encrypt(content, _encryptionKey, ValidatePageConfigContent);
        }

        /// <summary>
        /// 解密页面配置内容
        /// </summary>
        public PageConfigContent? DecryptPageConfig(string encryptedConfig)
        {
            return DataCrypto.Decrypt<PageConfigContent>(encryptedConfig, _encryptionKey);
        }
    }
}

