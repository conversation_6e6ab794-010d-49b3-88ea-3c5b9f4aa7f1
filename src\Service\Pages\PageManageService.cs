using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Service.Base;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace MlSoft.Sites.Service.Pages
{
    /// <summary>
    /// 网页内容管理服务
    /// </summary>
    public class PageManageService : MongoBaseService<PageManage>
    {
        private readonly PageConfigurationService _pageConfigService;
        private readonly IMemoryCache _cache;

        /// <summary>
        /// 所有有效的页面配置
        /// </summary>
        protected const string CACHE_ALL_PAGEDATA = "cache_all_pagedata";


        // 静态内存缓存
        private static readonly object _lockPdObject = new object();

        public PageManageService(IMongoDatabase database, IConfiguration configuration, PageConfigurationService pageConfigService, IMemoryCache cache)
            : base(database, "PageData")
        {
            _pageConfigService = pageConfigService;
            _cache = cache;
        }



        private async Task ClearCache()
        {
            _cache.Remove(CACHE_ALL_PAGEDATA);
        }

        /// <summary>
        /// 获取所有有效的页面配置，同时包括已解密的配置
        /// </summary>
        /// <returns></returns>
        private async Task<List<PageManage>> GetAllPageData()
        {
            if (_cache.TryGetValue(CACHE_ALL_PAGEDATA, out List<PageManage>? pageData) && pageData != null)
            {
                return pageData;
            }

            // 加载组件并缓存
            lock (_lockPdObject)
            {

                var pageDataList = FindAsync(x => true).Result;

                // 缓存到IMemoryCache
                _cache.Set(CACHE_ALL_PAGEDATA, pageDataList, TimeSpan.FromDays(10));

                return pageDataList.ToList();
            }
        }



        /// <summary>
        /// 根据页面配置ID获取页面内容数据
        /// </summary>
        /// <param name="pageConfigurationId">页面配置ID</param>
        /// <returns>页面内容数据</returns>
        public async Task<PageManage?> GetPageContentAsync(string pageConfigurationId)
        {
            if (string.IsNullOrEmpty(pageConfigurationId))
                return null;

            var allData = await GetAllPageData();

            return allData.FirstOrDefault(x => x.PageConfigurationId == pageConfigurationId);
        }

        /// <summary>
        /// 根据页面Key获取页面内容数据
        /// </summary>
        /// <param name="pageKey">页面Key</param>
        /// <returns>页面内容数据</returns>
        public async Task<PageManage?> GetPageContentByKeyAsync(string pageKey)
        {
            if (string.IsNullOrEmpty(pageKey))
                return null;

            var allData = await GetAllPageData();

            return allData.FirstOrDefault(x => x.PageKey == pageKey);
        }

        /// <summary>
        /// 创建或更新页面内容数据
        /// </summary>
        /// <param name="pageConfigurationId">页面配置ID</param>
        /// <param name="pageKey">页面Key</param>
        /// <param name="componentsData">组件数据列表</param>
        /// <param name="updatedBy">操作者</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> SavePageContentAsync(string pageConfigurationId, string pageKey, List<ComponentData> componentsData, string updatedBy)
        {
            try
            {
                // 检查页面配置是否存在
                var (pageConfig, _) = await _pageConfigService.GetPageWithContentAsync(pageConfigurationId);
                if (pageConfig == null)
                    return false;

                var existingContent = await GetPageContentAsync(pageConfigurationId);
                var now = DateTime.UtcNow;

                if (existingContent == null)
                {
                    // 创建新的内容记录
                    var newContent = new PageManage
                    {
                        PageConfigurationId = pageConfigurationId,
                        PageKey = pageKey,
                        ComponentsData = componentsData,
                        Status = PageContentStatus.Draft,
                        Version = 1,
                        CreatedAt = now,
                        UpdatedAt = now,
                        CreatedBy = updatedBy,
                        UpdatedBy = updatedBy
                    };

                    // 设置组件的最后修改时间
                    foreach (var component in newContent.ComponentsData)
                    {
                        component.LastModified = now;
                    }

                    await _collection.InsertOneAsync(newContent);
                }
                else
                {
                    // 更新现有内容记录
                    var updateBuilder = Builders<PageManage>.Update
                        .Set(x => x.ComponentsData, componentsData)
                        .Set(x => x.UpdatedAt, now)
                        .Set(x => x.UpdatedBy, updatedBy);

                    // 设置组件的最后修改时间
                    foreach (var component in componentsData)
                    {
                        component.LastModified = now;
                    }

                    var filter = Builders<PageManage>.Filter.Eq(x => x.Id, existingContent.Id);
                    await _collection.UpdateOneAsync(filter, updateBuilder);
                }

                await ClearCache();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 发布页面内容
        /// </summary>
        /// <param name="id">页面内容ID</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> PublishPageContentAsync(string id)
        {
            try
            {
                var filter = Builders<PageManage>.Filter.Eq(x => x.Id, id);
                var update = Builders<PageManage>.Update
                    .Set(x => x.Status, PageContentStatus.Published)
                    .Set(x => x.PublishDate, DateTime.UtcNow)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow);

                var result = await _collection.UpdateOneAsync(filter, update);
                var ret= result.ModifiedCount > 0;
                if (ret)
                {
                    await ClearCache();
                }
                return ret;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 取消发布页面内容
        /// </summary>
        /// <param name="id">页面内容ID</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> UnpublishPageContentAsync(string id)
        {
            try
            {
                var filter = Builders<PageManage>.Filter.Eq(x => x.Id, id);
                var update = Builders<PageManage>.Update
                    .Set(x => x.Status, PageContentStatus.Draft)
                    .Set(x => x.PublishDate, (DateTime?)null)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow);

                var result = await _collection.UpdateOneAsync(filter, update);
                var ret = result.ModifiedCount > 0;
                if (ret)
                {
                    await ClearCache();
                }
                return ret;
            }
            catch (Exception)
            {
                return false;
            }
        }

       

        /// <summary>
        /// 删除页面内容数据
        /// </summary>
        /// <param name="id">内容ID</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> DeletePageContentAsync(string id)
        {
            try
            {
                var filter = Builders<PageManage>.Filter.Eq(x => x.Id, id);
                var result = await _collection.DeleteOneAsync(filter);
                var ret = result.DeletedCount > 0;
                if (ret)
                {
                    await ClearCache();
                }
                return ret;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取页面内容列表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="status">状态筛选</param>
        /// <returns>页面内容列表和总数</returns>
        public async Task<(List<PageManage> contents, long totalCount)> GetPageContentListAsync(int page = 1, int pageSize = 20, PageContentStatus? status = null)
        {

            var allList = await GetAllPageData();

            var totalCount = allList.Count;


            if (status != null)
            {
                allList = allList.Where(x => x.Status == status.Value).ToList();
            }


            var list = allList.OrderByDescending(x => x.UpdatedAt).Skip((page - 1) * pageSize).Take(pageSize).ToList();

            return (list, totalCount);
        }
    }
}