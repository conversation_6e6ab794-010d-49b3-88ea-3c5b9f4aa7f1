using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Pages
{
    public class PageVersionService : MongoBaseService<PageVersion>
    {
        public PageVersionService(IMongoDatabase database) : base(database, "PageVersions")
        {
        }

        public async Task<IEnumerable<PageVersion>> GetVersionsByPageAsync(string pageConfigurationId)
        {
            var versions = await FindAsync(v => v.PageConfigurationId == pageConfigurationId);
            return versions.OrderByDescending(v => v.VersionNumber);
        }

        public async Task<PageVersion?> GetCurrentVersionAsync(string pageConfigurationId)
        {
            return await FindOneAsync(v => v.PageConfigurationId == pageConfigurationId && v.IsCurrentVersion);
        }

        public async Task<PageVersion?> GetVersionByNumberAsync(string pageConfigurationId, int versionNumber)
        {
            return await FindOneAsync(v => v.PageConfigurationId == pageConfigurationId && v.VersionNumber == versionNumber);
        }

        public async Task<int> GetNextVersionNumberAsync(string pageConfigurationId)
        {
            var versions = await FindAsync(v => v.PageConfigurationId == pageConfigurationId);
            return versions.Any() ? versions.Max(v => v.VersionNumber) + 1 : 1;
        }

        public async Task<PageVersion> CreateVersionAsync(string pageConfigurationId, string configurationSnapshot, string changeDescription, string createdBy)
        {
            var versionNumber = await GetNextVersionNumberAsync(pageConfigurationId);

            // Set all previous versions as not current
            var filter = Builders<PageVersion>.Filter.And(
                Builders<PageVersion>.Filter.Eq(v => v.PageConfigurationId, pageConfigurationId),
                Builders<PageVersion>.Filter.Eq(v => v.IsCurrentVersion, true)
            );
            var update = Builders<PageVersion>.Update.Set(v => v.IsCurrentVersion, false);
            await _collection.UpdateManyAsync(filter, update);

            var version = new PageVersion
            {
                PageConfigurationId = pageConfigurationId,
                VersionNumber = versionNumber,
                ConfigurationSnapshot = configurationSnapshot,
                ChangeDescription = changeDescription,
                CreatedBy = createdBy,
                CreatedAt = DateTime.UtcNow,
                IsCurrentVersion = true
            };

            return await CreateAsync(version);
        }

        public async Task<bool> RestoreVersionAsync(string pageConfigurationId, int versionNumber)
        {
            // Set all versions as not current
            var filter = Builders<PageVersion>.Filter.Eq(v => v.PageConfigurationId, pageConfigurationId);
            var update = Builders<PageVersion>.Update.Set(v => v.IsCurrentVersion, false);
            await _collection.UpdateManyAsync(filter, update);

            // Set the specified version as current
            var targetFilter = Builders<PageVersion>.Filter.And(
                Builders<PageVersion>.Filter.Eq(v => v.PageConfigurationId, pageConfigurationId),
                Builders<PageVersion>.Filter.Eq(v => v.VersionNumber, versionNumber)
            );
            var targetUpdate = Builders<PageVersion>.Update.Set(v => v.IsCurrentVersion, true);
            var result = await _collection.UpdateOneAsync(targetFilter, targetUpdate);

            return result.ModifiedCount > 0;
        }

        public async Task<bool> DeleteVersionAsync(string pageConfigurationId, int versionNumber)
        {
            // Don't allow deletion of current version
            var version = await GetVersionByNumberAsync(pageConfigurationId, versionNumber);
            if (version?.IsCurrentVersion == true)
            {
                return false;
            }

            var filter = Builders<PageVersion>.Filter.And(
                Builders<PageVersion>.Filter.Eq(v => v.PageConfigurationId, pageConfigurationId),
                Builders<PageVersion>.Filter.Eq(v => v.VersionNumber, versionNumber)
            );
            var result = await _collection.DeleteOneAsync(filter);
            return result.DeletedCount > 0;
        }
    }
}