using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Recruitment;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Recruitment
{
    public class EmployeeInterviewService : MongoBaseService<EmployeeInterview>
    {
        public EmployeeInterviewService(IMongoDatabase database) : base(database, "EmployeeInterviews")
        {
            // 创建索引以优化查询性能
            CreateIndexes();
        }

        private void CreateIndexes()
        {
            // 发布状态索引
            var publishedIndexKeys = Builders<EmployeeInterview>.IndexKeys
                .Ascending(x => x.DisplayOrder);
            var publishedIndexModel = new CreateIndexModel<EmployeeInterview>(publishedIndexKeys);
            _collection.Indexes.CreateOne(publishedIndexModel);

            // 类型索引
            var typeIndexKeys = Builders<EmployeeInterview>.IndexKeys
                .Ascending(x => x.InterviewType);
            var typeIndexModel = new CreateIndexModel<EmployeeInterview>(typeIndexKeys);
            _collection.Indexes.CreateOne(typeIndexModel);

            // 状态索引
            var statusIndexKeys = Builders<EmployeeInterview>.IndexKeys
                .Ascending(x => x.Status)
                .Descending(x => x.CreatedAt);
            var statusIndexModel = new CreateIndexModel<EmployeeInterview>(statusIndexKeys);
            _collection.Indexes.CreateOne(statusIndexModel);
        }

        public async Task<IEnumerable<EmployeeInterview>> GetPublishedInterviewsAsync()
        {
            return await FindAsync(i => i.Status == PublishStatus.Published);
        }

        public async Task<IEnumerable<EmployeeInterview>> GetInterviewsByDepartmentAsync(string departmentId)
        {
            return await FindAsync(i => i.Status == PublishStatus.Published && i.DepartmentId == departmentId);
        }

        public async Task<IEnumerable<EmployeeInterview>> GetInterviewsOrderedAsync()
        {
            var interviews = await FindAsync(i => i.Status == PublishStatus.Published);
            return interviews.OrderBy(i => i.DisplayOrder).ThenByDescending(i => i.InterviewDate);
        }

        public async Task<IEnumerable<EmployeeInterview>> GetInterviewsByExperienceAsync(int minYears, int maxYears)
        {
            return await FindAsync(i => i.Status == PublishStatus.Published && i.YearsOfService >= minYears && i.YearsOfService <= maxYears);
        }

        /// <summary>
        /// 获取推荐访谈
        /// </summary>
        public async Task<IEnumerable<EmployeeInterview>> GetFeaturedInterviewsAsync(int limit = 5)
        {
            var filter = Builders<EmployeeInterview>.Filter.And(
                Builders<EmployeeInterview>.Filter.Eq(x => x.Status, PublishStatus.Published),
                Builders<EmployeeInterview>.Filter.Eq(x => x.IsFeatured, true)
            );
            var sort = Builders<EmployeeInterview>.Sort
                .Ascending(x => x.DisplayOrder)
                .Descending(x => x.InterviewDate);

            return await _collection.Find(filter).Sort(sort).Limit(limit).ToListAsync();
        }

        /// <summary>
        /// 按访谈类型获取访谈
        /// </summary>
        public async Task<IEnumerable<EmployeeInterview>> GetInterviewsByTypeAsync(InterviewType type, int page = 1, int pageSize = 10)
        {
            var filter = Builders<EmployeeInterview>.Filter.And(
                Builders<EmployeeInterview>.Filter.Eq(x => x.Status, PublishStatus.Published),
                Builders<EmployeeInterview>.Filter.Eq(x => x.InterviewType, type)
            );
            var interviews = await _collection.Find(filter)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
            return interviews;
        }

        /// <summary>
        /// 按状态获取访谈
        /// </summary>
        public async Task<IEnumerable<EmployeeInterview>> GetInterviewsByStatusAsync(PublishStatus status, int page = 1, int pageSize = 10)
        {
            var filter = Builders<EmployeeInterview>.Filter.Eq(x => x.Status, status);
            var interviews = await _collection.Find(filter)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
            return interviews;
        }

        /// <summary>
        /// 搜索访谈
        /// </summary>
        public async Task<IEnumerable<EmployeeInterview>> SearchInterviewsAsync(string searchTerm, int page = 1, int pageSize = 10)
        {
            var filter = Builders<EmployeeInterview>.Filter.And(
                Builders<EmployeeInterview>.Filter.Eq(x => x.Status, PublishStatus.Published),
                Builders<EmployeeInterview>.Filter.Or(
                    Builders<EmployeeInterview>.Filter.Regex("Locale.ja.EmployeeName", new MongoDB.Bson.BsonRegularExpression(searchTerm, "i")),
                    Builders<EmployeeInterview>.Filter.Regex("Locale.ja.Position", new MongoDB.Bson.BsonRegularExpression(searchTerm, "i")),
                    Builders<EmployeeInterview>.Filter.Regex("Locale.ja.InterviewContent", new MongoDB.Bson.BsonRegularExpression(searchTerm, "i"))
                )
            );

            var interviews = await _collection.Find(filter)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
            return interviews;
        }

       
      
        /// <summary>
        /// 更新显示顺序
        /// </summary>
        public async Task<bool> UpdateDisplayOrderAsync(string id, int displayOrder)
        {
            var update = Builders<EmployeeInterview>.Update
                .Set(x => x.DisplayOrder, displayOrder)
                .Set(x => x.UpdatedAt, DateTime.UtcNow);

            var result = await _collection.UpdateOneAsync(
                Builders<EmployeeInterview>.Filter.Eq(x => x.Id, id),
                update
            );
            return result.ModifiedCount > 0;
        }

        public async Task<EmployeeInterview> CreateInterviewAsync(EmployeeInterview interview)
        {
            interview.CreatedAt = DateTime.UtcNow;
            interview.UpdatedAt = DateTime.UtcNow;
            return await CreateAsync(interview);
        }

        public async Task<bool> UpdateInterviewAsync(string id, EmployeeInterview interview)
        {
            interview.UpdatedAt = DateTime.UtcNow;
            return await UpdateAsync(id, interview);
        }

        public async Task<bool> PublishInterviewAsync(string id)
        {
            var update = Builders<EmployeeInterview>.Update
                .Set(x => x.Status, PublishStatus.Published)
                .Set(x => x.UpdatedAt, DateTime.UtcNow);

            var result = await _collection.UpdateOneAsync(
                Builders<EmployeeInterview>.Filter.Eq(x => x.Id, id),
                update
            );
            return result.ModifiedCount > 0;
        }

        public async Task<bool> UnpublishInterviewAsync(string id)
        {
            var update = Builders<EmployeeInterview>.Update
                .Set(x => x.Status, PublishStatus.Draft)
                .Set(x => x.UpdatedAt, DateTime.UtcNow);

            var result = await _collection.UpdateOneAsync(
                Builders<EmployeeInterview>.Filter.Eq(x => x.Id, id),
                update
            );
            return result.ModifiedCount > 0;
        }
    }
}