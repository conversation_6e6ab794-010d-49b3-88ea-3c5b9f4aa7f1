using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Recruitment;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Recruitment
{
    public class JobPositionService : MongoBaseService<JobPosition>
    {
        public JobPositionService(IMongoDatabase database) : base(database, "JobPositions")
        {
            // 创建索引以优化查询性能
            CreateIndexes();
        }

        private void CreateIndexes()
        {
            // 主要查询索引：活跃状态 + 发布日期
            var activeIndexKeys = Builders<JobPosition>.IndexKeys
                .Ascending(x => x.IsActive)
                .Descending(x => x.PostDate);
            var activeIndexModel = new CreateIndexModel<JobPosition>(activeIndexKeys);
            _collection.Indexes.CreateOne(activeIndexModel);

            // 职位类型索引
            var typeIndexKeys = Builders<JobPosition>.IndexKeys
                .Ascending(x => x.Type)
                .Ascending(x => x.IsActive);
            var typeIndexModel = new CreateIndexModel<JobPosition>(typeIndexKeys);
            _collection.Indexes.CreateOne(typeIndexModel);

            // 状态索引
            var statusIndexKeys = Builders<JobPosition>.IndexKeys
                .Descending(x => x.CreatedAt);
            var statusIndexModel = new CreateIndexModel<JobPosition>(statusIndexKeys);
            _collection.Indexes.CreateOne(statusIndexModel);

            // 推荐职位索引
            var featuredIndexKeys = Builders<JobPosition>.IndexKeys
                .Ascending(x => x.IsFeatured);
            var featuredIndexModel = new CreateIndexModel<JobPosition>(featuredIndexKeys);
            _collection.Indexes.CreateOne(featuredIndexModel);
        }

        public async Task<IEnumerable<JobPosition>> GetActiveJobsAsync()
        {
            return await FindAsync(j => j.IsActive);
        }

        public async Task<IEnumerable<JobPosition>> GetJobsByTypeAsync(JobType jobType)
        {
            return await FindAsync(j => j.IsActive && j.Type == jobType);
        }

        public async Task<IEnumerable<JobPosition>> GetJobsByDepartmentAsync(string departmentId)
        {
            return await FindAsync(j => j.IsActive && j.DepartmentId == departmentId);
        }

        public async Task<IEnumerable<JobPosition>> GetJobsByExperienceLevelAsync(ExperienceLevel level)
        {
            return await FindAsync(j => j.IsActive && j.ExperienceLevel == level);
        }

        public async Task<IEnumerable<JobPosition>> GetJobsWithDeadlineAsync()
        {
            var now = DateTime.UtcNow;
            return await FindAsync(j => j.IsActive && j.ApplicationDeadline.HasValue && j.ApplicationDeadline.Value > now);
        }

        public async Task<IEnumerable<JobPosition>> GetExpiredJobsAsync()
        {
            var now = DateTime.UtcNow;
            return await FindAsync(j => j.IsActive && j.ApplicationDeadline.HasValue && j.ApplicationDeadline.Value <= now);
        }

        public async Task<JobPosition> CreateJobAsync(JobPosition job)
        {
            job.CreatedAt = DateTime.UtcNow;
            job.UpdatedAt = DateTime.UtcNow;
            return await CreateAsync(job);
        }

        public async Task<bool> UpdateJobAsync(string id, JobPosition job)
        {
            job.UpdatedAt = DateTime.UtcNow;
            return await UpdateAsync(id, job);
        }

        /// <summary>
        /// 获取推荐职位
        /// </summary>
        public async Task<IEnumerable<JobPosition>> GetFeaturedJobsAsync(int limit = 5)
        {
            var filter = Builders<JobPosition>.Filter.And(
                Builders<JobPosition>.Filter.Eq(x => x.IsActive, true),
                Builders<JobPosition>.Filter.Eq(x => x.IsFeatured, true)
            );
            var sort = Builders<JobPosition>.Sort
                .Descending(x => x.PostDate);

            return await _collection.Find(filter).Sort(sort).Limit(limit).ToListAsync();
        }

        /// <summary>
        /// 获取指定状态的职位
        /// </summary>
        public async Task<IEnumerable<JobPosition>> GetJobsList(int page = 1, int pageSize = 10)
        {
            var jobs = await _collection.Find(x=>true)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
            return jobs;
        }

        /// <summary>
        /// 获取职位总数
        /// </summary>
        public async Task<long> GetJobsCountAsync()
        {
            return await _collection.CountDocumentsAsync(x => true);
        }


        /// <summary>
        /// 搜索职位
        /// </summary>
        public async Task<IEnumerable<JobPosition>> SearchJobsAsync(string searchTerm, int page = 1, int pageSize = 10)
        {
            var filter = Builders<JobPosition>.Filter.And(
                Builders<JobPosition>.Filter.Eq(x => x.IsActive, true),
                Builders<JobPosition>.Filter.Or(
                    Builders<JobPosition>.Filter.Regex("Locale.ja.JobTitle", new MongoDB.Bson.BsonRegularExpression(searchTerm, "i")),
                    Builders<JobPosition>.Filter.Regex("Locale.ja.JobDescription", new MongoDB.Bson.BsonRegularExpression(searchTerm, "i")),
                    Builders<JobPosition>.Filter.In("Tags", new[] { searchTerm })
                )
            );

            var jobs = await _collection.Find(filter)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
            return jobs;
        }


    }
}