using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Service.Pages;

namespace MlSoft.Sites.Service.Routing
{
    public interface IDynamicPageRouteService
    {
        Task RegisterPageRoutesAsync(IEndpointRouteBuilder endpoints, string defaultLanguage);
        Task RefreshRoutesAsync(IEndpointRouteBuilder endpoints, string defaultLanguage);
        Task RefreshSinglePageRouteAsync(string pageId);
        Task RemovePageRouteAsync(string pageId);
        string GenerateSEOUrl(PageConfiguration page, SupportedLanguage language, string defaultLanguage);
        Task<PageRouteInfo?> ResolveRouteAsync(string path, string culture);
    }

    public class DynamicPageRouteService : IDynamicPageRouteService
    {
        private readonly PageConfigurationService _pageConfigService;
        private readonly SupportedLanguage[] _supportedLanguages;
        private readonly ILogger<DynamicPageRouteService> _logger;
        private readonly IRouteChangeNotificationService _notificationService;
        private readonly Dictionary<string, PageRouteInfo> _routeCache = new();
        private IEndpointRouteBuilder? _endpoints;
        private string? _defaultLanguage;

        public DynamicPageRouteService(
            PageConfigurationService pageConfigService,
            SupportedLanguage[] supportedLanguages,
            ILogger<DynamicPageRouteService> logger,
            IRouteChangeNotificationService notificationService)
        {
            _pageConfigService = pageConfigService;
            _supportedLanguages = supportedLanguages;
            _logger = logger;
            _notificationService = notificationService;
        }

        public async Task RegisterPageRoutesAsync(IEndpointRouteBuilder endpoints, string defaultLanguage)
        {
            _endpoints = endpoints;
            _defaultLanguage = defaultLanguage;

            try
            {
                var pagesWithContent = await _pageConfigService.GetPublishedPagesWithContentAsync();
                var routeCount = 0;

                foreach (var (page, content) in pagesWithContent)
                {
                    // 跳过解密失败的页面
                    if (content == null)
                    {
                        _logger.LogWarning("Skipping page {PageId} due to decryption failure", page.Id);
                        continue;
                    }

                    foreach (var lang in _supportedLanguages)
                    {
                        var routeInfo = BuildPageRoute(page, content, lang, defaultLanguage);
                        if (routeInfo != null)
                        {
                            RegisterSingleRoute(endpoints, routeInfo);

                            // 缓存路由信息
                            var cacheKey = GenerateRouteCacheKey(routeInfo.Pattern, lang.Code);
                            _routeCache[cacheKey] = routeInfo;

                            routeCount++;
                        }
                    }
                }

                _logger.LogInformation("Registered {RouteCount} dynamic page routes", routeCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering page routes");
            }
        }

        public async Task RefreshRoutesAsync(IEndpointRouteBuilder endpoints, string defaultLanguage)
        {
            try
            {
                _logger.LogInformation("Refreshing all dynamic page routes");
                
                // 清除现有缓存
                _routeCache.Clear();
                
                // 重新注册所有已发布页面的路由
                await RegisterPageRoutesAsync(endpoints, defaultLanguage);
                
                _logger.LogInformation("Successfully refreshed all dynamic page routes");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing dynamic page routes");
            }
        }

        public async Task RefreshSinglePageRouteAsync(string pageId)
        {
            if (_endpoints == null || _defaultLanguage == null)
            {
                _logger.LogWarning("Cannot refresh route for page {PageId}: endpoints or default language not initialized", pageId);
                return;
            }

            try
            {
                _logger.LogInformation("Refreshing route for page {PageId}", pageId);

                // 获取旧的路由信息用于通知
                var oldRoutes = _routeCache
                    .Where(kvp => kvp.Value.PageId == pageId)
                    .ToList();

                // 先移除该页面的现有路由
                await RemovePageRouteAsync(pageId);

                // 获取页面配置
                var (page, content) = await _pageConfigService.GetPageWithContentAsync(pageId);
                
                // 只有已发布的页面才重新添加路由
                if (page != null && content != null && page.Status == PageStatus.Published)
                {
                    foreach (var lang in _supportedLanguages)
                    {
                        var routeInfo = BuildPageRoute(page, content, lang, _defaultLanguage);
                        if (routeInfo != null)
                        {
                            RegisterSingleRoute(_endpoints, routeInfo);

                            // 更新缓存
                            var cacheKey = GenerateRouteCacheKey(routeInfo.Pattern, lang.Code);
                            _routeCache[cacheKey] = routeInfo;

                            // 发送通知
                            var oldRoute = oldRoutes.FirstOrDefault(r => r.Value.Culture == lang.Code).Value;
                            if (oldRoute != null && oldRoute.Pattern != routeInfo.Pattern)
                            {
                                await _notificationService.NotifyRouteUpdatedAsync(
                                    pageId, content.PageKey, lang.Code, oldRoute.Pattern, routeInfo.Pattern);
                            }
                            else if (oldRoute == null)
                            {
                                await _notificationService.NotifyRouteAddedAsync(
                                    pageId, content.PageKey, lang.Code, routeInfo.Pattern);
                            }
                        }
                    }

                    _logger.LogInformation("Successfully refreshed route for published page {PageId}", pageId);
                }
                else
                {
                    _logger.LogInformation("Page {PageId} is not published, route removed", pageId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing route for page {PageId}", pageId);
            }
        }

        public async Task RemovePageRouteAsync(string pageId)
        {
            try
            {
                _logger.LogInformation("Removing routes for page {PageId}", pageId);

                // 从缓存中移除该页面的所有路由并收集信息用于通知
                var routesToRemove = _routeCache
                    .Where(kvp => kvp.Value.PageId == pageId)
                    .ToList();

                foreach (var (key, routeInfo) in routesToRemove)
                {
                    _routeCache.Remove(key);
                    
                    // 发送移除通知
                    await _notificationService.NotifyRouteRemovedAsync(
                        pageId, routeInfo.PageKey, routeInfo.Culture, routeInfo.Pattern);
                }

                // 注意：ASP.NET Core 的路由系统不支持运行时移除已注册的路由
                // 这里只能从缓存中移除，实际的路由仍然存在但不会被匹配到
                // 如果需要完全移除，需要重启应用或使用自定义路由处理机制

                _logger.LogInformation("Removed {RouteCount} cached routes for page {PageId}", routesToRemove.Count, pageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing routes for page {PageId}", pageId);
            }
        }

        public string GenerateSEOUrl(PageConfiguration page, SupportedLanguage language, string defaultLanguage)
        {
            try
            {
                var content = _pageConfigService.DecryptPageConfig(page.Config);
                if (content == null)
                {
                    // 如果解密失败，使用页面名称作为fallback
                    var fallbackPageName = page.Name.GetValueOrDefault(language.Code) ?? page.Name.Values.FirstOrDefault();
                    return BuildFullUrl(language, string.IsNullOrEmpty(fallbackPageName) ? "page" : GenerateSlug(fallbackPageName));
                }

                return GenerateSEOUrl(page, content, language, defaultLanguage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SEO URL for page {PageId}", page.Id);
                return BuildFullUrl(language, "page");
            }
        }

        /// <summary>
        /// 生成SEO友好的URL（使用已解密的内容）
        /// </summary>
        public string GenerateSEOUrl(PageConfiguration page, PageConfigContent content, SupportedLanguage language, string defaultLanguage)
        {
            try
            {
                // 如果页面有自定义路由，使用自定义路由
                if (!string.IsNullOrEmpty(content.Route))
                {
                    var customRoute = ProcessRouteTemplate(content.Route, language, defaultLanguage);
                    return BuildFullUrl(language, customRoute);
                }

                // 使用页面名称生成SEO友好URL
                var pageName = page.Name.GetValueOrDefault(language.Code) ?? page.Name.Values.FirstOrDefault();
                if (!string.IsNullOrEmpty(pageName))
                {
                    var slug = GenerateSlug(pageName);
                    return BuildFullUrl(language, slug);
                }

                // 回退到PageKey
                return BuildFullUrl(language, content.PageKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SEO URL for page in language {Language}",
                    language.Code);
                var fallbackPageName = page.Name.GetValueOrDefault(language.Code) ?? page.Name.Values.FirstOrDefault();
                return BuildFullUrl(language, string.IsNullOrEmpty(fallbackPageName) ? "page" : GenerateSlug(fallbackPageName));
            }
        }

        public async Task<PageRouteInfo?> ResolveRouteAsync(string path, string culture)
        {
            try
            {
                var cacheKey = GenerateRouteCacheKey(path, culture);
                if (_routeCache.TryGetValue(cacheKey, out var cachedRoute))
                {
                    return cachedRoute;
                }

                // 如果缓存中没有，尝试匹配现有路由
                foreach (var route in _routeCache.Values)
                {
                    if (route.Culture == culture && MatchesRoute(path, route.Pattern))
                    {
                        return route;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving route for path {Path} and culture {Culture}", path, culture);
                return null;
            }
        }

        private PageRouteInfo? BuildPageRoute(PageConfiguration page, PageConfigContent content, SupportedLanguage language, string defaultLanguage)
        {
            try
            {
                var pattern = GenerateRoutePattern(page, content, language, defaultLanguage);
                var routeName = $"page_{content.PageKey}_{language.Code}";

                return new PageRouteInfo
                {
                    Name = routeName,
                    Pattern = pattern,
                    PageKey = content.PageKey,
                    PageId = page.Id,
                    Culture = language.Code,
                    Controller = "Page",
                    Action = "Render",
                    LayoutTemplate = content.LayoutTemplate,
                    RequireAuth = content.AccessControl?.RequireAuthentication ?? false,
                    CacheDuration = content.CacheConfig?.CacheDurationMinutes ?? 30
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building route for page {PageId} in language {Language}",
                    page.Id, language.Code);
                return null;
            }
        }

        private string GenerateRoutePattern(PageConfiguration page, PageConfigContent content, SupportedLanguage language, string defaultLanguage)
        {
            // 如果有自定义路由，使用自定义路由
            if (!string.IsNullOrEmpty(content.Route))
            {
                return ProcessRouteTemplate(content.Route, language, defaultLanguage);
            }

            var langCode = language.Code;
            if (langCode == defaultLanguage)
            {
                langCode = "";
            }

            // 使用页面名称生成SEO友好的URL模式
            var pageName = page.Name.GetValueOrDefault(language.Code) ?? page.Name.Values.FirstOrDefault();
            if (!string.IsNullOrEmpty(pageName))
            {
                var slug = GenerateSlug(pageName);
                return string.IsNullOrEmpty(langCode) ? slug : $"{langCode}/{slug}";
            }

            // 回退到PageKey
            return string.IsNullOrEmpty(langCode) ? content.PageKey : $"{langCode}/{content.PageKey}";
        }

        private string ProcessRouteTemplate(string routeTemplate, SupportedLanguage language, string defaultLanguage)
        {
            var langCode = language.Code;
            if (langCode == defaultLanguage)
            {
                langCode = "";
            }

            // 替换路由模板中的占位符
            var processed = routeTemplate
                .Replace("{culture}", langCode)
                .Replace("{language}", langCode)
                .Replace("{baseUrl}", language.BaseUrl.Trim('/'));

            return processed.TrimStart('/');
        }

        private void RegisterSingleRoute(IEndpointRouteBuilder endpoints, PageRouteInfo routeInfo)
        {
            try
            {
                endpoints.MapControllerRoute(
                    name: routeInfo.Name,
                    pattern: routeInfo.Pattern,
                    defaults: new
                    {
                        controller = routeInfo.Controller,
                        action = routeInfo.Action,
                        pageKey = routeInfo.PageKey,
                        culture = routeInfo.Culture
                    },
                    constraints: new
                    {
                        culture = $"^({string.Join("|", _supportedLanguages.Select(x => x.Code))})$"
                    }
                );

                _logger.LogDebug("Registered route: {RouteName} -> {Pattern}", routeInfo.Name, routeInfo.Pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering single route: {RouteName}", routeInfo.Name);
            }
        }

        private string GenerateSlug(string title)
        {
            if (string.IsNullOrEmpty(title))
                return string.Empty;

            // 转换为小写
            var slug = title.ToLowerInvariant();

            // 移除特殊字符，保留字母、数字和连字符
            slug = Regex.Replace(slug, @"[^a-z0-9\s-]", "");

            // 将空格替换为连字符
            slug = Regex.Replace(slug, @"\s+", "-");

            // 移除多个连字符
            slug = Regex.Replace(slug, @"-+", "-");

            // 移除首尾连字符
            slug = slug.Trim('-');

            // 限制长度
            if (slug.Length > 50)
            {
                slug = slug.Substring(0, 50).TrimEnd('-');
            }

            return slug;
        }

        private string BuildFullUrl(SupportedLanguage language, string path)
        {
            var baseUrl = language.BaseUrl.TrimEnd('/');

            // 如果BaseUrl是绝对URL，直接拼接
            if (baseUrl.StartsWith("http://") || baseUrl.StartsWith("https://"))
            {
                return $"{baseUrl}/{path.TrimStart('/')}";
            }

            // 相对路径处理
            if (string.IsNullOrEmpty(baseUrl) || baseUrl == "/")
            {
                return $"/{path.TrimStart('/')}";
            }

            return $"{baseUrl}/{path.TrimStart('/')}";
        }

        private string GenerateRouteCacheKey(string pattern, string culture)
        {
            return $"{culture}:{pattern}";
        }

        private bool MatchesRoute(string path, string pattern)
        {
            // 简单的路由匹配逻辑
            // 这里可以扩展为更复杂的路由匹配算法
            var normalizedPath = path.Trim('/').ToLowerInvariant();
            var normalizedPattern = pattern.Trim('/').ToLowerInvariant();

            // 处理参数路由（简单版本）
            var patternRegex = normalizedPattern
                .Replace("{pageKey}", @"[^/]+")
                .Replace("{id}", @"\d+")
                .Replace("{culture}", @"[a-z]{2}");

            return Regex.IsMatch(normalizedPath, $"^{patternRegex}$");
        }
    }

    public class PageRouteInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Pattern { get; set; } = string.Empty;
        public string PageKey { get; set; } = string.Empty;
        public string PageId { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;
        public string Controller { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string? LayoutTemplate { get; set; }
        public bool RequireAuth { get; set; }
        public int CacheDuration { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }
}