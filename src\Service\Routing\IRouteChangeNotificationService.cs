using System;
using System.Threading.Tasks;

namespace MlSoft.Sites.Service.Routing
{
    public interface IRouteChangeNotificationService
    {
        event Func<RouteChangeEventArgs, Task> RouteChanged;
        
        Task NotifyRouteAddedAsync(string pageId, string pageKey, string culture, string pattern);
        Task NotifyRouteRemovedAsync(string pageId, string pageKey, string culture, string pattern);
        Task NotifyRouteUpdatedAsync(string pageId, string pageKey, string culture, string oldPattern, string newPattern);
    }

    public class RouteChangeEventArgs
    {
        public string PageId { get; set; } = string.Empty;
        public string PageKey { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;
        public string Pattern { get; set; } = string.Empty;
        public string? OldPattern { get; set; }
        public RouteChangeType ChangeType { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public enum RouteChangeType
    {
        Added,
        Removed,
        Updated
    }
}