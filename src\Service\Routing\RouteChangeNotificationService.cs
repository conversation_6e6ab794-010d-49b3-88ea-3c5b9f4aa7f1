using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace MlSoft.Sites.Service.Routing
{
    public class RouteChangeNotificationService : IRouteChangeNotificationService
    {
        private readonly ILogger<RouteChangeNotificationService> _logger;

        public RouteChangeNotificationService(ILogger<RouteChangeNotificationService> logger)
        {
            _logger = logger;
        }

        public event Func<RouteChangeEventArgs, Task>? RouteChanged;

        public async Task NotifyRouteAddedAsync(string pageId, string pageKey, string culture, string pattern)
        {
            var args = new RouteChangeEventArgs
            {
                PageId = pageId,
                PageKey = pageKey,
                Culture = culture,
                Pattern = pattern,
                ChangeType = RouteChangeType.Added
            };

            await NotifyAsync(args);
        }

        public async Task NotifyRouteRemovedAsync(string pageId, string pageKey, string culture, string pattern)
        {
            var args = new RouteChangeEventArgs
            {
                PageId = pageId,
                PageKey = pageKey,
                Culture = culture,
                Pattern = pattern,
                ChangeType = RouteChangeType.Removed
            };

            await NotifyAsync(args);
        }

        public async Task NotifyRouteUpdatedAsync(string pageId, string pageKey, string culture, string oldPattern, string newPattern)
        {
            var args = new RouteChangeEventArgs
            {
                PageId = pageId,
                PageKey = pageKey,
                Culture = culture,
                Pattern = newPattern,
                OldPattern = oldPattern,
                ChangeType = RouteChangeType.Updated
            };

            await NotifyAsync(args);
        }

        private async Task NotifyAsync(RouteChangeEventArgs args)
        {
            try
            {
                _logger.LogInformation("Route {ChangeType}: Page {PageId} ({PageKey}) - {Culture} - {Pattern}",
                    args.ChangeType, args.PageId, args.PageKey, args.Culture, args.Pattern);

                if (RouteChanged != null)
                {
                    await RouteChanged.Invoke(args);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error notifying route change for page {PageId}", args.PageId);
            }
        }
    }
}