﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

using MongoDB.Driver;
using Microsoft.AspNetCore.Identity;
using AspNetCore.Identity.Mongo.Model;
using MlSoft.Sites.Model.Entities.Company;
using MlSoft.Sites.Model.Entities.News;
using MlSoft.Sites.Model.Entities.Business;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Model.Entities.Messages;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.News;
using MlSoft.Sites.Service.Business;
using MlSoft.Sites.Service.Pages;
using MlSoft.Sites.Service.Messages;

namespace MlSoft.Sites.Service.Seeders
{

public class DataSeeder
{
    private readonly CompanyService _companyService;
    private readonly NewsAnnouncementService _newsService;
    private readonly BusinessDivisionService _divisionService;
    private readonly PageConfigurationService _pageConfigService;
    private readonly MessageInquiryService _messageService;
    private readonly UserManager<MongoUser> _userManager;

    public DataSeeder(
        CompanyService companyService,
        NewsAnnouncementService newsService,
        BusinessDivisionService divisionService,
        PageConfigurationService pageConfigService,
        MessageInquiryService messageService,
        UserManager<MongoUser> userManager)
    {
        _companyService = companyService;
        _newsService = newsService;
        _divisionService = divisionService;
        _pageConfigService = pageConfigService;
        _messageService = messageService;
        _userManager = userManager;
    }

    public async Task SeedAsync()
    {
        await SeedAdminUserAsync();
        await SeedCompanyDataAsync();
        await SeedNewsDataAsync();
        await SeedBusinessDivisionDataAsync();
        await SeedMessageInquiryDataAsync();
        await SeedPageConfigurationsAsync();
    }

    private async Task SeedAdminUserAsync()
    {
        var adminEmail = "<EMAIL>";
        var existingUser = await _userManager.FindByEmailAsync(adminEmail);
        
        if (existingUser == null)
        {
            var adminUser = new MongoUser
            {
                UserName = adminEmail,
                Email = adminEmail,
                EmailConfirmed = true
            };

            var result = await _userManager.CreateAsync(adminUser, "Admin123!");
            if (!result.Succeeded)
            {
                throw new Exception($"Failed to create admin user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }
        }
    }

    private async Task SeedCompanyDataAsync()
    {
        //var existingCompany = await _companyService.GetByCompanyCodeAsync("SAMPLE_CORP");
        //if (existingCompany != null) return;

        //var company = new Model.Entities.Company.Company
        //{
        //    CompanyCode = "SAMPLE_CORP",
        //    EstablishedDate = new DateTime(1990, 4, 1),
        //    RegistrationNumber = "1234567890",
        //    Capital = *********,
        //    Currency = "JPY",
        //    Locale = new Dictionary<string, CompanyLocaleFields>
        //    {
        //        ["ja"] = new CompanyLocaleFields
        //        {
        //            CompanyName = "株式会社サンプル",
        //            CompanyDescription = "私たちは革新的なソリューションを提供する会社です。お客様のビジネス成長をサポートし、持続可能な未来の創造に貢献します。",
        //            Mission = "技術で世界を変える",
        //            Vision = "持続可能な未来の創造",
        //            Address = "東京都渋谷区渋谷1-1-1 サンプルビル10F"
        //        },
        //        ["en"] = new CompanyLocaleFields
        //        {
        //            CompanyName = "Sample Corporation",
        //            CompanyDescription = "We are a company that provides innovative solutions. We support our customers' business growth and contribute to creating a sustainable future.",
        //            Mission = "Changing the world through technology",
        //            Vision = "Creating a sustainable future",
        //            Address = "Sample Building 10F, 1-1-1 Shibuya, Shibuya-ku, Tokyo"
        //        },
        //        ["zh"] = new CompanyLocaleFields
        //        {
        //            CompanyName = "样本公司",
        //            CompanyDescription = "我们是一家提供创新解决方案的公司。我们支持客户的业务增长，为创造可持续的未来做出贡献。",
        //            Mission = "通过技术改变世界",
        //            Vision = "创造可持续的未来",
        //            Address = "东京都涩谷区涩谷1-1-1 样本大厦10F"
        //        }
        //    }
        //};

        //await _companyService.CreateCompanyAsync(company);
    }

    private async Task SeedNewsDataAsync()
    {
        var existingNews = await _newsService.GetAllAsync();
        if (existingNews.Any()) return;

        var newsItems = new[]
        {
            new NewsAnnouncement
            {
                Type = NewsType.CompanyNews,
                PublishDate = DateTime.UtcNow.AddDays(-7),
                Status = NewsStatus.Published,
                IsFeatured = true,
                Locale = new Dictionary<string, NewsAnnouncementLocaleFields>
                {
                    ["ja"] = new NewsAnnouncementLocaleFields
                    {
                        Title = "新製品発表のお知らせ",
                        Summary = "弊社では、革新的な新製品を発表いたします。この製品は業界初の技術を採用し、お客様の業務効率を大幅に向上させます。",
                        Content = "詳細な製品情報については、後日発表予定です。ご期待ください。",
                        Tags = new[] { "新製品", "技術革新", "業務効率" }
                    },
                    ["en"] = new NewsAnnouncementLocaleFields
                    {
                        Title = "New Product Announcement",
                        Summary = "We are pleased to announce our innovative new product. This product adopts industry-first technology and significantly improves customer work efficiency.",
                        Content = "Detailed product information will be announced at a later date. Please stay tuned.",
                        Tags = new[] { "new product", "innovation", "efficiency" }
                    }
                }
            },
            new NewsAnnouncement
            {
                Type = NewsType.Announcement,
                PublishDate = DateTime.UtcNow.AddDays(-14),
                Status = NewsStatus.Published,
                IsFeatured = false,
                Locale = new Dictionary<string, NewsAnnouncementLocaleFields>
                {
                    ["ja"] = new NewsAnnouncementLocaleFields
                    {
                        Title = "業務提携に関するお知らせ",
                        Summary = "A社との戦略的業務提携を締結いたしました。この提携により、両社の強みを活かした新たなサービスを提供してまいります。",
                        Content = "詳細については、プレスリリースをご確認ください。",
                        Tags = new[] { "業務提携", "戦略", "サービス" }
                    },
                    ["en"] = new NewsAnnouncementLocaleFields
                    {
                        Title = "Business Partnership Announcement",
                        Summary = "We have concluded a strategic business partnership with Company A. Through this partnership, we will provide new services that leverage the strengths of both companies.",
                        Content = "For details, please check our press release.",
                        Tags = new[] { "partnership", "strategy", "service" }
                    }
                }
            }
        };

        foreach (var news in newsItems)
        {
            await _newsService.CreateNewsAsync(news);
        }
    }

    private async Task SeedBusinessDivisionDataAsync()
    {
        var existingDivisions = await _divisionService.GetAllAsync();
        if (existingDivisions.Any()) return;

        var divisions = new[]
        {
            new BusinessDivision
            {
                DisplayOrder = 1,
                Locale = new Dictionary<string, BusinessDivisionLocaleFields>
                {
                    ["ja"] = new BusinessDivisionLocaleFields
                    {
                        DivisionName = "IT事業部",
                        Description = "最新のIT技術を活用したソリューションを提供します。",
                        Services = "システム開発、クラウドサービス、AI・機械学習"
                    },
                    ["en"] = new BusinessDivisionLocaleFields
                    {
                        DivisionName = "IT Division",
                        Description = "We provide solutions utilizing the latest IT technologies.",
                        Services = "System Development, Cloud Services, AI & Machine Learning"
                    }
                }
            },
            new BusinessDivision
            {
                DisplayOrder = 2,
                Locale = new Dictionary<string, BusinessDivisionLocaleFields>
                {
                    ["ja"] = new BusinessDivisionLocaleFields
                    {
                        DivisionName = "コンサルティング事業部",
                        Description = "企業の課題解決をサポートする専門的なコンサルティングサービスを提供します。",
                        Services = "経営コンサルティング、業務改善、デジタル変革支援"
                    },
                    ["en"] = new BusinessDivisionLocaleFields
                    {
                        DivisionName = "Consulting Division",
                        Description = "We provide professional consulting services to support corporate problem-solving.",
                        Services = "Management Consulting, Process Improvement, Digital Transformation Support"
                    }
                }
            }
        };

        foreach (var division in divisions)
        {
            await _divisionService.CreateDivisionAsync(division);
        }
    }

    private async Task SeedMessageInquiryDataAsync()
    {
        var existingMessages = await _messageService.GetAllAsync();
        if (existingMessages.Any()) return;

        var messages = new[]
        {
            new MessageInquiry
            {
                ContactName = "田中 太郎",
                ContactEmail = "<EMAIL>",
                ContactPhone = "03-1234-5678",
                CompanyName = "株式会社サンプル化学",
                JobTitle = "購買部長",
                Message = "貴社の精密化学品について詳細な仕様書とサンプルの提供をお願いいたします。当社の新製品開発プロジェクトにて使用を検討しております。特に純度95%以上の製品をお探ししています。",
                Type = MessageType.ProductInquiry,
                Status = MessageStatus.New,
                SourcePage = "/products/precision-chemicals",
                ReferrerUrl = "https://google.com/search?q=精密化学品",
                IpAddress = "*************",
                UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                UpdatedAt = DateTime.UtcNow.AddDays(-5),
                IsRead = false,
                IsImportant = true
            },
            new MessageInquiry
            {
                ContactName = "山田 花子",
                ContactEmail = "<EMAIL>",
                ContactPhone = "06-9876-5432",
                CompanyName = "テクノロジー株式会社",
                JobTitle = "技術開発主任",
                Message = "御社の技術サポートサービスについてお伺いしたく連絡いたしました。現在、化学分析装置の導入を検討しており、技術指導やメンテナンスサポートの詳細について教えていただけますでしょうか。",
                Type = MessageType.TechnicalSupport,
                Status = MessageStatus.InProgress,
                SourcePage = "/services/technical-support",
                ReferrerUrl = "https://bing.com/search?q=化学分析装置+サポート",
                IpAddress = "*************",
                UserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15",
                CreatedAt = DateTime.UtcNow.AddDays(-3),
                UpdatedAt = DateTime.UtcNow.AddDays(-2),
                IsRead = true,
                IsImportant = false,
                ProcessedByUserId = "admin",
                ProcessedAt = DateTime.UtcNow.AddDays(-2),
                DealResult = "技術部門へ転送済み。詳細資料を準備中。"
            },
            new MessageInquiry
            {
                ContactName = "佐藤 一郎",
                ContactEmail = "<EMAIL>",
                ContactPhone = "************",
                CompanyName = "グリーンテック工業株式会社",
                JobTitle = "営業部課長",
                Message = "持続可能な化学製品の共同開発について、パートナーシップの可能性をご相談したくご連絡いたします。弊社は環境配慮型の製品開発に力を入れており、貴社の技術力と連携できればと考えております。",
                Type = MessageType.BusinessCooperation,
                Status = MessageStatus.Resolved,
                SourcePage = "/company/partnership",
                ReferrerUrl = "https://linkedin.com/company/mlsoft-sites",
                IpAddress = "***********",
                UserAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                UpdatedAt = DateTime.UtcNow.AddDays(-1),
                IsRead = true,
                IsImportant = true,
                ProcessedByUserId = "admin",
                ProcessedAt = DateTime.UtcNow.AddDays(-8),
                DealResult = "営業部で検討後、正式な提携協議を開始することが決定。契約書草案を準備中。"
            },
            new MessageInquiry
            {
                ContactName = "鈴木 美香",
                ContactEmail = "<EMAIL>",
                ContactPhone = "090-1234-5678",
                CompanyName = "東京理科大学",
                JobTitle = "化学科 4年生",
                Message = "来年の新卒採用についてお伺いします。化学工学を専攻しており、特に環境化学分野に興味があります。貴社での研究開発職の募集状況と求める人材像について教えていただけますでしょうか。",
                Type = MessageType.CareerInquiry,
                Status = MessageStatus.WaitingForResponse,
                SourcePage = "/careers/new-graduate",
                ReferrerUrl = "https://job.mynavi.jp/25/pc/search/corp12345",
                IpAddress = "************",
                UserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15",
                CreatedAt = DateTime.UtcNow.AddDays(-2),
                UpdatedAt = DateTime.UtcNow.AddDays(-1),
                IsRead = true,
                IsImportant = false,
                ProcessedByUserId = "admin",
                ProcessedAt = DateTime.UtcNow.AddDays(-1),
                DealResult = "人事部に転送済み。採用担当者からの回答待ち。"
            },
            new MessageInquiry
            {
                ContactName = "高橋 健司",
                ContactEmail = "<EMAIL>",
                ContactPhone = "03-9999-8888",
                CompanyName = "化学業界新聞社",
                JobTitle = "記者",
                Message = "貴社の新しい環境配慮型製品について取材をさせていただきたく、ご連絡いたします。業界向けの記事作成のため、開発背景や今後の展開予定についてお聞かせください。",
                Type = MessageType.MediaInquiry,
                Status = MessageStatus.New,
                SourcePage = "/news/new-eco-product-2024",
                ReferrerUrl = "https://chemical-news.jp/search/eco-products",
                IpAddress = "***************",
                UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
                CreatedAt = DateTime.UtcNow.AddHours(-6),
                UpdatedAt = DateTime.UtcNow.AddHours(-6),
                IsRead = false,
                IsImportant = true
            }
        };

        foreach (var message in messages)
        {
            await _messageService.CreateAsync(message);
        }
    }

    private async Task SeedPageConfigurationsAsync()
    {
        await SeedHomePageConfigAsync();
    }

    private async Task SeedHomePageConfigAsync()
    {
        //var existingHomePage = await _pageConfigService.GetByPageKeyAsync("home");
        //if (existingHomePage != null) return;

        //var homePageConfig = new PageConfiguration
        //{
        //    PageKey = "home",
        //    Locale = new Dictionary<string, PageConfigurationLocaleFields>
        //    {
        //        ["zh"] = new PageConfigurationLocaleFields
        //        {
        //            PageTitle = "欢迎访问 MlSoft Sites - 领先的化工解决方案提供商",
        //            MetaDescription = "MlSoft Sites是一家专业的化工解决方案提供商，致力于为客户提供高品质的化工产品和技术服务。我们拥有先进的生产设备和专业的技术团队。"
        //        },
        //        ["en"] = new PageConfigurationLocaleFields
        //        {
        //            PageTitle = "Welcome to MlSoft Sites - Leading Chemical Solutions Provider",
        //            MetaDescription = "MlSoft Sites is a professional chemical solutions provider, committed to providing high-quality chemical products and technical services to customers. We have advanced production equipment and professional technical team."
        //        },
        //        ["ja"] = new PageConfigurationLocaleFields
        //        {
        //            PageTitle = "MlSoft Sitesへようこそ - 化学ソリューションのリーディングプロバイダー",
        //            MetaDescription = "MlSoft Sitesは専門的な化学ソリューションプロバイダーであり、お客様に高品質な化学製品と技術サービスを提供することに専念しています。先進的な生産設備と専門的な技術チームを有しています。"
        //        }
        //    },
        //    Route = "{culture}/test-dynamic-page",
        //    LayoutTemplate = "_Layout",
        //    Components = new List<PageComponent>
        //    {
        //        new PageComponent
        //        {
        //            ComponentDefinitionId = "hero_component",
        //            TemplateKey = "Default",
        //            ParametersJson = "{\"title\":{\"zh\":\"领先的化工解决方案\",\"en\":\"Leading Chemical Solutions\",\"ja\":\"先進的な化学ソリューション\"},\"subtitle\":{\"zh\":\"专业、安全、可持续的化工产品与服务\",\"en\":\"Professional, Safe, and Sustainable Chemical Products & Services\",\"ja\":\"専門的で安全で持続可能な化学製品とサービス\"},\"backgroundImage\":\"/images/hero/chemical-factory.jpg\",\"ctaText\":{\"zh\":\"了解更多\",\"en\":\"Learn More\",\"ja\":\"詳細を見る\"},\"ctaLink\":\"/company\",\"showVideo\":false,\"height\":\"600px\"}",
        //            DisplayOrder = 1,
        //            IsVisible = true
        //        },
        //        new PageComponent
        //        {
        //            ComponentDefinitionId = "content_component",
        //            TemplateKey = "Default",
        //            ParametersJson = "{\"title\":{\"zh\":\"企业简介\",\"en\":\"Company Overview\",\"ja\":\"企業概要\"},\"content\":{\"zh\":\"<p>MlSoft Sites成立于2020年，是一家专注于化工行业的技术解决方案提供商。我们致力于为客户提供安全、环保、高效的化工产品和服务，助力企业实现可持续发展。</p><p>公司拥有完善的质量管理体系，通过了ISO 9001、ISO 14001等国际认证，确保产品质量和环境安全。</p>\",\"en\":\"<p>MlSoft Sites was established in 2020, is a technology solution provider focused on the chemical industry. We are committed to providing safe, environmentally friendly, and efficient chemical products and services to help enterprises achieve sustainable development.</p><p>The company has a complete quality management system and has passed ISO 9001, ISO 14001 and other international certifications to ensure product quality and environmental safety.</p>\",\"ja\":\"<p>MlSoft Sitesは2020年に設立され、化学工業に焦点を当てた技術ソリューションプロバイダーです。私たちは、企業の持続可能な発展を支援するため、安全で環境に優しく効率的な化学製品とサービスの提供に取り組んでいます。</p><p>当社は完全な品質管理システムを有し、ISO 9001、ISO 14001などの国際認証を取得し、製品品質と環境安全を確保しています。</p>\"},\"layout\":\"text-image\",\"imageUrl\":\"/images/about/company-overview.jpg\",\"imagePosition\":\"right\"}",
        //            DisplayOrder = 2,
        //            IsVisible = true
        //        },
        //        new PageComponent
        //        {
        //            ComponentDefinitionId = "content_component", 
        //            TemplateKey = "Default",
        //            ParametersJson = "{\"title\":{\"zh\":\"核心业务\",\"en\":\"Core Business\",\"ja\":\"コアビジネス\"},\"content\":{\"zh\":\"<div class='grid grid-cols-1 md:grid-cols-3 gap-6'><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>基础化工</h3><p>提供高品质的基础化工原料，包括有机化学品、无机化学品等。</p></div><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>精细化工</h3><p>专业生产各类精细化工产品，满足不同行业的特殊需求。</p></div><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>技术服务</h3><p>提供专业的技术咨询和解决方案服务，助力客户优化生产工艺。</p></div></div>\",\"en\":\"<div class='grid grid-cols-1 md:grid-cols-3 gap-6'><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>Basic Chemicals</h3><p>Providing high-quality basic chemical raw materials, including organic chemicals, inorganic chemicals, etc.</p></div><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>Fine Chemicals</h3><p>Professional production of various fine chemical products to meet the special needs of different industries.</p></div><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>Technical Services</h3><p>Providing professional technical consulting and solution services to help customers optimize production processes.</p></div></div>\",\"ja\":\"<div class='grid grid-cols-1 md:grid-cols-3 gap-6'><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>基礎化学</h3><p>有機化学品、無機化学品などの高品質な基礎化学原料を提供しています。</p></div><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>ファインケミカル</h3><p>様々な業界の特殊なニーズに応えるため、各種ファインケミカル製品を専門的に生産しています。</p></div><div class='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md'><h3 class='text-xl font-semibold mb-3 text-primary-600'>技術サービス</h3><p>お客様の生産プロセス最適化を支援する専門的な技術コンサルティングとソリューションサービスを提供しています。</p></div></div>\"},\"layout\":\"full-width\"}",
        //            DisplayOrder = 3,
        //            IsVisible = true
        //        }
        //    },
        //    Status = PageStatus.Published,
        //    PublishDate = DateTime.UtcNow.AddDays(-30),
        //    SEO = new Dictionary<string, BasicSEOConfiguration>
        //    {
        //        ["zh"] = new BasicSEOConfiguration
        //        {
        //            MetaTitle = "MlSoft Sites - 专业化工解决方案提供商 | 安全环保可持续",
        //            MetaDescription = "MlSoft Sites是一家专业的化工解决方案提供商，致力于为客户提供高品质的化工产品和技术服务。通过ISO认证，确保产品质量和环境安全。",
        //            MetaKeywords = "化工解决方案,化工产品,技术服务,ISO认证,环保化工,精细化工,基础化工",
        //            OgImage = "/images/og/home-zh.jpg",
        //            NoIndex = false
        //        },
        //        ["en"] = new BasicSEOConfiguration
        //        {
        //            MetaTitle = "MlSoft Sites - Professional Chemical Solutions Provider | Safe, Eco-friendly, Sustainable",
        //            MetaDescription = "MlSoft Sites is a professional chemical solutions provider, committed to providing high-quality chemical products and technical services. ISO certified for quality and environmental safety.",
        //            MetaKeywords = "chemical solutions,chemical products,technical services,ISO certification,eco-friendly chemicals,fine chemicals,basic chemicals",
        //            OgImage = "/images/og/home-en.jpg",
        //            NoIndex = false
        //        },
        //        ["ja"] = new BasicSEOConfiguration
        //        {
        //            MetaTitle = "MlSoft Sites - 専門的な化学ソリューションプロバイダー | 安全・環境に配慮・持続可能",
        //            MetaDescription = "MlSoft Sitesは専門的な化学ソリューションプロバイダーであり、高品質な化学製品と技術サービスを提供しています。ISO認証により品質と環境安全を保証。",
        //            MetaKeywords = "化学ソリューション,化学製品,技術サービス,ISO認証,環境配慮化学品,ファインケミカル,基礎化学",
        //            OgImage = "/images/og/home-ja.jpg",
        //            NoIndex = false
        //        }
        //    },
        //    Performance = new BasicPerformanceConfig
        //    {
        //        EnableImageLazyLoading = true,
        //        EnableComponentLazyLoading = false,
        //        ImageQuality = 85
        //    },
        //    CreatedAt = DateTime.UtcNow.AddDays(-30),
        //    UpdatedAt = DateTime.UtcNow.AddDays(-1),
        //    CreatedBy = "admin",
        //    UpdatedBy = "admin"
        //};

        //await _pageConfigService.CreatePageAsync(homePageConfig, "admin");
    }
}
}

