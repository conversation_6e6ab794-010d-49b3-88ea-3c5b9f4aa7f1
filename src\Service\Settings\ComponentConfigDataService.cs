using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Model.Entities.Settings;
using MlSoft.Sites.Service.Base;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Service.Settings
{
    public class ComponentConfigDataService : MongoBaseService<ComponentConfigData>
    {
        private readonly IMemoryCache _cache;
        /// <summary>
        /// 全局组组件信息
        /// </summary>
        public const string CACHE_GLOBAL_COMPONENTDATA = "cache_global_componentdata";
        // 静态内存缓存
        private static readonly object _lockCcdObject = new object();

        public ComponentConfigDataService(IMongoDatabase database, IMemoryCache cache)
            : base(database, "ComponentConfigData")
        {
            _cache = cache;
        }


        public async Task<List<ComponentConfigData>?> GetAllData()
        {
            if (_cache.TryGetValue(CACHE_GLOBAL_COMPONENTDATA, out List<ComponentConfigData>? info) && info != null)
            {
                return info;
            }

            info = (await FindAsync(x => true)).ToList();
            if (info != null && info.Count != 0)
            {
                lock (_lockCcdObject)
                {
                    // 缓存到IMemoryCache
                    _cache.Set(CACHE_GLOBAL_COMPONENTDATA, info, TimeSpan.FromDays(10));
                }
            }

            return info;
        }

        /// <summary>
        /// 根据组件ID和变体ID获取配置数据
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="variantId">变体ID</param>
        /// <returns>配置数据，如果不存在返回null</returns>
        public async Task<ComponentConfigData?> GetByComponentAndVariantAsync(string componentId, string variantId)
        {
            if (string.IsNullOrEmpty(componentId) || string.IsNullOrEmpty(variantId))
                return null;

            var list = await GetAllData();

            return list?.Where(x => x.VariantId == variantId && x.ComponentId == componentId).FirstOrDefault();
        }

        /// <summary>
        /// 创建或更新组件配置数据
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="variantId">变体ID</param>
        /// <param name="jsonData">JSON数据</param>
        /// <param name="updatedBy">更新者</param>
        /// <returns>是否成功</returns>
        public async Task<bool> CreateOrUpdateAsync(string componentId, string variantId, string jsonData, string updatedBy = "")
        {
            if (string.IsNullOrEmpty(componentId) || string.IsNullOrEmpty(variantId))
                return false;

            var existing = await GetByComponentAndVariantAsync(componentId, variantId);

            if (existing != null)
            {
                // 更新现有记录
                var update = Builders<ComponentConfigData>.Update
                    .Set(x => x.JsonData, jsonData)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow)
                    .Set(x => x.UpdatedBy, updatedBy);

                var result = await _collection.UpdateOneAsync(
                    Builders<ComponentConfigData>.Filter.Eq(x => x.Id, existing.Id),
                    update
                );

                _cache.Remove(CACHE_GLOBAL_COMPONENTDATA);

                return result.ModifiedCount > 0;
            }
            else
            {
                // 创建新记录
                var newRecord = new ComponentConfigData
                {
                    ComponentId = componentId,
                    VariantId = variantId,
                    JsonData = jsonData,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedBy = updatedBy,
                    UpdatedBy = updatedBy
                };

                await _collection.InsertOneAsync(newRecord);

                _cache.Remove(CACHE_GLOBAL_COMPONENTDATA);

                return true;
            }
        }

        /// <summary>
        /// 获取指定组件的所有变体配置
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <returns>配置数据列表</returns>
        public async Task<List<ComponentConfigData>> GetByComponentIdAsync(string componentId)
        {
            if (string.IsNullOrEmpty(componentId))
                return new List<ComponentConfigData>();

            var list = await GetAllData();
            return list?.Where(x=>x.ComponentId == componentId).ToList();
        }

        /// <summary>
        /// 删除指定组件和变体的配置数据
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="variantId">变体ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteByComponentAndVariantAsync(string componentId, string variantId)
        {
            if (string.IsNullOrEmpty(componentId) || string.IsNullOrEmpty(variantId))
                return false;

            var filter = Builders<ComponentConfigData>.Filter.And(
                Builders<ComponentConfigData>.Filter.Eq(x => x.ComponentId, componentId),
                Builders<ComponentConfigData>.Filter.Eq(x => x.VariantId, variantId)
            );

            var result = await _collection.DeleteOneAsync(filter);
            _cache.Remove(CACHE_GLOBAL_COMPONENTDATA);
            return result.DeletedCount > 0;
        }

        /// <summary>
        /// 检查配置数据是否存在
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="variantId">变体ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(string componentId, string variantId)
        {
            if (string.IsNullOrEmpty(componentId) || string.IsNullOrEmpty(variantId))
                return false;

            var filter = Builders<ComponentConfigData>.Filter.And(
                Builders<ComponentConfigData>.Filter.Eq(x => x.ComponentId, componentId),
                Builders<ComponentConfigData>.Filter.Eq(x => x.VariantId, variantId)
            );

            var count = await _collection.CountDocumentsAsync(filter);
            return count > 0;
        }
    }
}