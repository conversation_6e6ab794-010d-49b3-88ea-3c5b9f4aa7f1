using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Driver;
using MlSoft.Sites.Model.Entities.Settings;
using MlSoft.Sites.Service.Base;

namespace MlSoft.Sites.Service.Settings
{
    public class SEOSettingsService : MongoBaseService<SEOSettings>
    {
        public SEOSettingsService(IMongoDatabase database) : base(database, "SEOSettings")
        {
        }

        public async Task<SEOSettings?> GetSEOSettingsByPageAsync(string pageConfigurationId)
        {
            return await FindOneAsync(s => s.PageConfigurationId == pageConfigurationId);
        }

        public async Task<IEnumerable<SEOSettings>> GetAllSEOSettingsAsync()
        {
            return await GetAllAsync();
        }

        public async Task<SEOSettings> CreateOrUpdateSEOSettingsAsync(SEOSettings seoSettings)
        {
            var existingSettings = await GetSEOSettingsByPageAsync(seoSettings.PageConfigurationId!);
            
            if (existingSettings != null)
            {
                seoSettings.Id = existingSettings.Id;
                seoSettings.CreatedAt = existingSettings.CreatedAt;
                seoSettings.UpdatedAt = DateTime.UtcNow;
                await UpdateAsync(existingSettings.Id, seoSettings);
                return seoSettings;
            }
            else
            {
                seoSettings.CreatedAt = DateTime.UtcNow;
                seoSettings.UpdatedAt = DateTime.UtcNow;
                return await CreateAsync(seoSettings);
            }
        }

        public async Task<bool> UpdateCanonicalUrlAsync(string pageConfigurationId, string canonicalUrl)
        {
            var settings = await GetSEOSettingsByPageAsync(pageConfigurationId);
            if (settings == null) return false;

            settings.CanonicalUrl = canonicalUrl;
            settings.UpdatedAt = DateTime.UtcNow;

            return await UpdateAsync(settings.Id, settings);
        }

        public async Task<bool> UpdateAlternateUrlsAsync(string pageConfigurationId, List<string> alternateUrls)
        {
            var settings = await GetSEOSettingsByPageAsync(pageConfigurationId);
            if (settings == null) return false;

            settings.AlternateUrls = alternateUrls;
            settings.UpdatedAt = DateTime.UtcNow;

            return await UpdateAsync(settings.Id, settings);
        }

        public async Task<bool> DeleteSEOSettingsAsync(string pageConfigurationId)
        {
            var settings = await GetSEOSettingsByPageAsync(pageConfigurationId);
            if (settings == null) return false;

            return await DeleteAsync(settings.Id);
        }
    }
}