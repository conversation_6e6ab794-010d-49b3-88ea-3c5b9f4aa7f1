using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Model.Entities.Settings;
using MlSoft.Sites.Service.Base;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Service.Settings
{
    public class SiteSettingsService : MongoBaseService<SiteSettings>
    {
        private readonly SupportedLanguage[] _supportedLanguages;
        private readonly IMemoryCache _cache;

        private const string CACHE_SITESETTINGS = "cache_sitesettings";
        // 静态内存缓存
        private static readonly object _lockSsObject = new object();

        public SiteSettingsService(IMongoDatabase database, SupportedLanguage[] supportedLanguages, IMemoryCache cache)
            : base(database, "SiteSettings")
        {
            _supportedLanguages = supportedLanguages;
            _cache = cache;
        }

        public async Task<SiteSettings?> GetCurrentSettingsAsync()
        {
            if (_cache.TryGetValue(CACHE_SITESETTINGS, out SiteSettings? siteSettings) && siteSettings != null)
            {
                return siteSettings;
            }
            var settings = await GetAllAsync();
            if (settings != null)
            {
                var curSettings = settings.FirstOrDefault();
                // 加载组件并缓存
                lock (_lockSsObject)
                {

                    // 缓存到IMemoryCache
                    _cache.Set(CACHE_SITESETTINGS, curSettings, TimeSpan.FromDays(10));

                    return curSettings;
                }

            }

            return null;
        }

        public SupportedLanguage[] GetSupportedLanguagesFromConfig()
        {
            return _supportedLanguages;
        }

        public SupportedLanguage? GetLanguageByCode(string code)
        {
            return _supportedLanguages.FirstOrDefault(x => x.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
        }

        public async Task<SiteSettings> CreateOrUpdateSettingsAsync(SiteSettings settings)
        {
            var existingSettings = await GetCurrentSettingsAsync();

            if (existingSettings != null)
            {
                settings.Id = existingSettings.Id;
                settings.CreatedAt = existingSettings.CreatedAt;
                settings.UpdatedAt = DateTime.UtcNow;
                await UpdateAsync(existingSettings.Id, settings);
                _cache.Remove(CACHE_SITESETTINGS);
                return settings;
            }
            else
            {
                settings.CreatedAt = DateTime.UtcNow;
                settings.UpdatedAt = DateTime.UtcNow;
                _cache.Remove(CACHE_SITESETTINGS);
                return await CreateAsync(settings);
            }
        }


        public async Task<bool> UpdateCustomSettingAsync(string key, string value)
        {
            var settings = await GetCurrentSettingsAsync();
            if (settings == null) return false;

            settings.CustomSettings[key] = value;
            settings.UpdatedAt = DateTime.UtcNow;

            var result = await UpdateAsync(settings.Id, settings);

            _cache.Remove(CACHE_SITESETTINGS);

            return result;
        }


    }
}