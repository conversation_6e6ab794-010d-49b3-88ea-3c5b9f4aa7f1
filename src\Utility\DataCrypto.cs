using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace MlSoft.Sites.Utility;

/// <summary>
/// 通用配置加密工具类
/// 使用 AES-256-GCM 算法提供认证加密功能
/// </summary>
public static class DataCrypto
{
    private const int KeySize = 32; // 256 bits
    private const int NonceSize = 12; // 96 bits for GCM
    private const int TagSize = 16; // 128 bits

    /// <summary>
    /// 将对象加密为字符串
    /// </summary>
    /// <typeparam name="T">要加密的对象类型</typeparam>
    /// <param name="data">要加密的对象</param>
    /// <param name="encryptionKey">加密密钥</param>
    /// <param name="validator">可选的对象验证器</param>
    /// <returns>Base64编码的加密字符串</returns>
    public static string Encrypt<T>(T data, string encryptionKey, Func<T, bool>? validator = null)
    {
        if (data == null)
            throw new ArgumentNullException(nameof(data));

        if (string.IsNullOrEmpty(encryptionKey))
            throw new ArgumentException("Encryption key cannot be null or empty", nameof(encryptionKey));

        // 如果提供了验证器，则验证对象格式
        if (validator != null && !validator(data))
            throw new ArgumentException("Invalid data content", nameof(data));

        var jsonString = SerializeToJson(data);
        var plainTextBytes = Encoding.UTF8.GetBytes(jsonString);
        var keyBytes = DeriveKey(encryptionKey);

        using var aesGcm = new AesGcm(keyBytes, TagSize);

        var nonce = new byte[NonceSize];
        RandomNumberGenerator.Fill(nonce);

        var ciphertext = new byte[plainTextBytes.Length];
        var tag = new byte[TagSize];

        aesGcm.Encrypt(nonce, plainTextBytes, ciphertext, tag);

        // 组合 nonce + ciphertext + tag
        var result = new byte[NonceSize + ciphertext.Length + TagSize];
        Array.Copy(nonce, 0, result, 0, NonceSize);
        Array.Copy(ciphertext, 0, result, NonceSize, ciphertext.Length);
        Array.Copy(tag, 0, result, NonceSize + ciphertext.Length, TagSize);

        return Convert.ToBase64String(result);
    }

    /// <summary>
    /// 将加密字符串解密为指定类型的对象
    /// </summary>
    /// <typeparam name="T">要解密到的对象类型</typeparam>
    /// <param name="encryptedData">Base64编码的加密字符串</param>
    /// <param name="encryptionKey">解密密钥</param>
    /// <returns>解密后的对象，解密失败返回default(T)</returns>
    public static T? Decrypt<T>(string encryptedData, string encryptionKey)
    {
        if (string.IsNullOrEmpty(encryptedData))
            return default(T);

        if (string.IsNullOrEmpty(encryptionKey))
            throw new ArgumentException("Encryption key cannot be null or empty", nameof(encryptionKey));

        try
        {
            var encryptedBytes = Convert.FromBase64String(encryptedData);
            
            if (encryptedBytes.Length < NonceSize + TagSize)
                return default(T);

            var keyBytes = DeriveKey(encryptionKey);

            var nonce = new byte[NonceSize];
            var ciphertext = new byte[encryptedBytes.Length - NonceSize - TagSize];
            var tag = new byte[TagSize];

            Array.Copy(encryptedBytes, 0, nonce, 0, NonceSize);
            Array.Copy(encryptedBytes, NonceSize, ciphertext, 0, ciphertext.Length);
            Array.Copy(encryptedBytes, NonceSize + ciphertext.Length, tag, 0, TagSize);

            using var aesGcm = new AesGcm(keyBytes, TagSize);
            var plaintext = new byte[ciphertext.Length];

            aesGcm.Decrypt(nonce, ciphertext, tag, plaintext);

            var jsonString = Encoding.UTF8.GetString(plaintext);
            return DeserializeFromJson<T>(jsonString);
        }
        catch (Exception)
        {
            // 解密失败返回default，不暴露具体错误信息
            return default(T);
        }
    }

    /// <summary>
    /// 内部方法：序列化对象为JSON字符串
    /// </summary>
    private static string SerializeToJson<T>(T data)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = false,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        return JsonSerializer.Serialize(data, options);
    }

    /// <summary>
    /// 内部方法：从JSON字符串反序列化对象
    /// </summary>
    private static T? DeserializeFromJson<T>(string jsonString)
    {
        if (string.IsNullOrWhiteSpace(jsonString))
            return default(T);

        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };

        try
        {
            return JsonSerializer.Deserialize<T>(jsonString, options);
        }
        catch
        {
            return default(T);
        }
    }

    /// <summary>
    /// 从字符串密钥派生固定长度的加密密钥
    /// </summary>
    private static byte[] DeriveKey(string password)
    {
        // 使用PBKDF2派生密钥
        var salt = Encoding.UTF8.GetBytes("MlSoft.Sites.Config.Salt.2025");
        using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
        return pbkdf2.GetBytes(KeySize);
    }
}