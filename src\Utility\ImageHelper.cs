using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.Processing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SkiaSharp;
using SkiaSharp.Extended.Svg;
using SKSvg = SkiaSharp.Extended.Svg.SKSvg;

namespace MlSoft.Sites.Utility
{
    public class ImageHelper
    {

        /// <summary>
        /// 
        /// </summary>
        /// <param name="srcFilePath"></param>
        /// <param name="webpFilePath"></param>
        /// <param name="Width"></param>
        /// <param name="Height"></param>
        /// <param name="scaleCut">按比例裁剪</param>
        /// <returns></returns>
        public static bool ConvertImage2WebP(string srcFilePath, string webpFilePath, int Width = 800, int Height = 600, bool scaleCut = false)
        {
            string? tempPngFilePath = null;
            try
            {
                var ext = Path.GetExtension(srcFilePath).ToLower();

                if (ext == ".svg")
                {
                    //先转为文件png
                    tempPngFilePath = Path.GetTempPath() + Guid.NewGuid().ToString("N") + ".png";
                    
                    try
                    {
                        // 使用 SkiaSharp.Extended.Svg 处理 SVG
                        var svg = new SKSvg();
                        
                        using var svgStream = File.OpenRead(srcFilePath);
                        var svgDocument = svg.Load(svgStream);
                        
                        if (svgDocument == null)
                        {
                            return false;
                        }
                        
                        // 获取SVG尺寸
                        var bounds = svgDocument.CullRect;
                        float originalWidth = bounds.Width > 0 ? bounds.Width : 1024;
                        float originalHeight = bounds.Height > 0 ? bounds.Height : 768;
                        
                        // 计算目标尺寸
                        float targetWidth = Width > 0 ? Width : Math.Min(originalWidth, 1024);
                        float targetHeight = Height > 0 ? Height : Math.Min(originalHeight, 768);
                        
                        // 计算缩放比例，保持宽高比
                        float scaleX = targetWidth / originalWidth;
                        float scaleY = targetHeight / originalHeight;
                        float scale = Math.Min(scaleX, scaleY);
                        
                        // 计算实际渲染尺寸
                        int renderWidth = (int)(originalWidth * scale);
                        int renderHeight = (int)(originalHeight * scale);
                        
                        // 创建位图并渲染
                        var info = new SKImageInfo(renderWidth, renderHeight);
                        using var surface = SKSurface.Create(info);
                        using var canvas = surface.Canvas;
                        
                        canvas.Clear(SKColors.Transparent);
                        
                        // 使用简单的缩放矩阵
                        canvas.Scale(scale);
                        canvas.DrawPicture(svgDocument);
                        
                        // 保存为PNG
                        using var image = surface.Snapshot();
                        using var data = image.Encode(SKEncodedImageFormat.Png, 100);
                        using var stream = File.OpenWrite(tempPngFilePath);
                        data.SaveTo(stream);
                        
                        // 更新srcFilePath为临时PNG文件路径，继续后续处理
                        srcFilePath = tempPngFilePath;
                    }
                    catch (Exception)
                    {
                        // SVG转换失败，清理临时文件
                        if (File.Exists(tempPngFilePath))
                        {
                            File.Delete(tempPngFilePath);
                        }
                        return false;
                    }
                }


                using (var image = Image.Load(srcFilePath))
                {
                    // 配置 WebP 编码器
                    var encoder = new WebpEncoder
                    {
                        Quality = 80, // 降低质量以减小文件大小
                        Method = WebpEncodingMethod.BestQuality, // 使用最佳压缩方法
                        FileFormat = WebpFileFormatType.Lossy, // 使用有损压缩
                        TransparentColorMode = WebpTransparentColorMode.Clear, // 优化透明处理
                        UseAlphaCompression = true, // 启用 alpha 通道压缩
                        EntropyPasses = 2, // 增加熵编码通道
                        SpatialNoiseShaping = 50, // 添加空间噪声整形
                        FilterStrength = 20, // 设置过滤强度
                    };

                    if (Width != 0 && Height != 0)
                    {
                        // 计算等比例缩放后的尺寸
                        var size = CalculateResizedDimensions(image.Width, image.Height, Width, Height);

                        if (scaleCut)
                        {
                            // 先将图片缩放到足够大的尺寸以覆盖目标区域
                            double widthRatio = (double)Width / image.Width;
                            double heightRatio = (double)Height / image.Height;
                            double ratio = Math.Max(widthRatio, heightRatio);

                            int resizeWidth = (int)(image.Width * ratio);
                            int resizeHeight = (int)(image.Height * ratio);

                            image.Mutate(x => x
                                .Resize(resizeWidth, resizeHeight)
                                .Crop(Width, Height)); // 居中裁剪到目标尺寸
                        }
                        else
                        {
                            // 保持原有的等比例缩放逻辑
                            image.Mutate(x => x.Resize(size.Width, size.Height));
                        }
                    }

                    // 保存为 WebP 格式
                    image.Save(webpFilePath, encoder);

                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
            finally
            {
                // 清理临时PNG文件
                if (!string.IsNullOrEmpty(tempPngFilePath) && File.Exists(tempPngFilePath))
                {
                    try
                    {
                        File.Delete(tempPngFilePath);
                    }
                    catch
                    {
                        // 忽略删除临时文件的异常
                    }
                }
            }
        }

        /// <summary>
        /// 计算等比例缩放后的尺寸
        /// </summary>
        /// <param name="originalWidth">原始宽度</param>
        /// <param name="originalHeight">原始高度</param>
        /// <param name="targetWidth">目标宽度</param>
        /// <param name="targetHeight">目标高度</param>
        /// <returns>缩放后的尺寸</returns>
        static Size CalculateResizedDimensions(int originalWidth, int originalHeight, int targetWidth, int targetHeight)
        {
            double widthRatio = (double)targetWidth / originalWidth;
            double heightRatio = (double)targetHeight / originalHeight;

            // 使用较小的比例，确保图片完全适应目标尺寸
            double ratio = Math.Min(widthRatio, heightRatio);

            return new Size(
                (int)(originalWidth * ratio),
                (int)(originalHeight * ratio)
            );
        }


        /// <summary>
        /// 将base64存为webp,宽，高不超过限定制
        /// </summary>
        /// <param name="base64">base64编码的图片数据</param>
        /// <param name="filePath">保存的文件路径</param>
        /// <param name="maxWidth">最大宽度</param>
        /// <param name="maxHeight">最大高度</param>
        /// <returns>成功返回保存的文件路径，失败返回空字符串</returns>
        public static bool SaveBase64asWebp(string base64, string filePath, int maxWidth, int maxHeight, bool dontConvert = false)
        {
            try
            {
                // 验证输入参数
                if (string.IsNullOrEmpty(base64) || string.IsNullOrEmpty(filePath))
                {
                    return false;
                }

                // 移除base64前缀（如果存在）
                string base64Data = base64;
                if (base64.Contains(","))
                {
                    base64Data = base64.Split(',')[1];
                }

                // 将base64转换为字节数组
                byte[] imageBytes = Convert.FromBase64String(base64Data);

                // 确保目录存在
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }


                if (!dontConvert)
                {

                    // 加载图片
                    using (var image = Image.Load(imageBytes))
                    {
                        // 配置 WebP 编码器
                        var encoder = new WebpEncoder
                        {
                            Quality = 80, // 设置质量
                            Method = WebpEncodingMethod.BestQuality,
                            FileFormat = WebpFileFormatType.Lossy,
                            TransparentColorMode = WebpTransparentColorMode.Clear,
                            UseAlphaCompression = true,
                            EntropyPasses = 2,
                            SpatialNoiseShaping = 50,
                            FilterStrength = 20,
                        };

                        // 如果指定了最大尺寸，则进行缩放
                        if (maxWidth > 0 && maxHeight > 0)
                        {
                            // 计算等比例缩放后的尺寸，确保不超过最大限制
                            var size = CalculateResizedDimensions(image.Width, image.Height, maxWidth, maxHeight);

                            // 只有在需要缩小时才进行缩放
                            if (size.Width < image.Width || size.Height < image.Height)
                            {
                                image.Mutate(x => x.Resize(size.Width, size.Height));
                            }
                        }

                        // 确保文件路径以.webp结尾
                        string webpFilePath = filePath;
                        if (!webpFilePath.ToLower().EndsWith(".webp"))
                        {
                            webpFilePath = Path.ChangeExtension(filePath, ".webp");
                        }

                        // 保存为 WebP 格式
                        image.Save(webpFilePath, encoder);

                        return true;
                    }
                }
                else
                {
                    // 直接保存
                    File.WriteAllBytes(filePath, imageBytes);
                    return true;
                }
            }
            catch (Exception ex)
            {
                // 可以在这里记录日志
                return false;
            }
        }

        /// <summary>
        /// 将图片转换为ICO格式
        /// </summary>
        /// <param name="sourceImagePath">源图片路径</param>
        /// <param name="icoFilePath">保存的ICO文件路径</param>
        /// <param name="iconSizes">图标尺寸数组，默认包含16x16, 24x24, 32x32, 48x48, 256x256</param>
        /// <returns>转换是否成功</returns>
        public static bool ConvertImageToIco(string sourceImagePath, string icoFilePath, int[]? iconSizes = null)
        {
            try
            {
                // 默认图标尺寸
                iconSizes ??= new int[] { 16, 24, 32, 48, 256 };
                
                // 确保目录存在
                string directory = Path.GetDirectoryName(icoFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using (var originalImage = Image.Load(sourceImagePath))
                {
                    // 创建ICO格式的多尺寸图像
                    using (var icoStream = new MemoryStream())
                    {
                        // ICO文件头部
                        var writer = new BinaryWriter(icoStream);
                        
                        // ICO头：保留字段(2) + 类型(2) + 图像数量(2)
                        writer.Write((short)0);  // 保留字段
                        writer.Write((short)1);  // 图标类型
                        writer.Write((short)iconSizes.Length);  // 图像数量
                        
                        var imageDataList = new List<byte[]>();
                        var offsetPosition = 6 + (iconSizes.Length * 16); // 头部 + 目录项
                        
                        // 为每个尺寸生成图像数据
                        foreach (var size in iconSizes)
                        {
                            // 创建新的图像副本并调整尺寸
                            var resizedImage = originalImage.Clone(x => x.Resize(size, size));
                            
                            // 转换为PNG格式的字节数据
                            using (var pngStream = new MemoryStream())
                            {
                                resizedImage.SaveAsPng(pngStream);
                                var imageData = pngStream.ToArray();
                                imageDataList.Add(imageData);
                                
                                // 写入目录项
                                writer.Write((byte)(size == 256 ? 0 : size));  // 宽度 (256用0表示)
                                writer.Write((byte)(size == 256 ? 0 : size));  // 高度 (256用0表示)
                                writer.Write((byte)0);   // 调色板颜色数
                                writer.Write((byte)0);   // 保留字段
                                writer.Write((short)1); // 颜色平面数
                                writer.Write((short)32); // 位深度
                                writer.Write(imageData.Length); // 图像数据大小
                                writer.Write(offsetPosition); // 图像数据偏移
                                
                                offsetPosition += imageData.Length;
                            }
                            
                            resizedImage.Dispose(); // 释放克隆的图像
                        }
                        
                        // 写入所有图像数据
                        foreach (var imageData in imageDataList)
                        {
                            writer.Write(imageData);
                        }
                        
                        // 保存ICO文件
                        File.WriteAllBytes(icoFilePath, icoStream.ToArray());
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                // 可以在这里记录日志
                return false;
            }
        }

        /// <summary>
        /// 将Base64图片数据转换为ICO格式
        /// </summary>
        /// <param name="base64">Base64编码的图片数据</param>
        /// <param name="icoFilePath">保存的ICO文件路径</param>
        /// <param name="iconSizes">图标尺寸数组</param>
        /// <returns>转换是否成功</returns>
        public static bool ConvertBase64ToIco(string base64, string icoFilePath, int[]? iconSizes = null)
        {
            try
            {
                // 验证输入参数
                if (string.IsNullOrEmpty(base64) || string.IsNullOrEmpty(icoFilePath))
                {
                    return false;
                }

                // 移除base64前缀（如果存在）
                string base64Data = base64;
                if (base64.Contains(","))
                {
                    base64Data = base64.Split(',')[1];
                }

                // 将base64转换为字节数组
                byte[] imageBytes = Convert.FromBase64String(base64Data);
                
                // 创建临时文件
                string tempFilePath = Path.GetTempPath() + Guid.NewGuid().ToString("N") + ".tmp";
                
                try
                {
                    File.WriteAllBytes(tempFilePath, imageBytes);
                    return ConvertImageToIco(tempFilePath, icoFilePath, iconSizes);
                }
                finally
                {
                    // 清理临时文件
                    if (File.Exists(tempFilePath))
                    {
                        try
                        {
                            File.Delete(tempFilePath);
                        }
                        catch
                        {
                            // 忽略删除临时文件的异常
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }

    }
}
