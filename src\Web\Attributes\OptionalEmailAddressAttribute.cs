using System.ComponentModel.DataAnnotations;

namespace MlSoft.Sites.Web.Attributes
{
    /// <summary>
    /// 可选的邮箱地址验证特性
    /// 允许空值，但如果有值则验证邮箱格式
    /// </summary>
    public class OptionalEmailAddressAttribute : ValidationAttribute
    {
        private readonly EmailAddressAttribute _emailValidator = new();

        public override bool IsValid(object? value)
        {
            // 如果值为空或空字符串，则验证通过
            if (value == null || string.IsNullOrEmpty(value.ToString()))
            {
                return true;
            }

            // 如果有值，则验证邮箱格式
            return _emailValidator.IsValid(value);
        }

        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (IsValid(value))
            {
                return ValidationResult.Success;
            }

            return new ValidationResult(ErrorMessage ?? "邮箱格式无效");
        }
    }
}
