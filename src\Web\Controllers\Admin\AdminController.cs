using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using AspNetCore.Identity.Mongo.Model;
using MlSoft.Sites.Web.ViewModels.Admin;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.News;
using MlSoft.Sites.Service.Pages;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    [Authorize]
    public class AdminController : Controller
    {
        private readonly UserManager<MongoUser> _userManager;
        private readonly CompanyService _companyService;
        private readonly NewsAnnouncementService _newsService;
        private readonly PageConfigurationService _pageService;

        public AdminController(
            UserManager<MongoUser> userManager,
            CompanyService companyService,
            NewsAnnouncementService newsService,
            PageConfigurationService pageService)
        {
            _userManager = userManager;
            _companyService = companyService;
            _newsService = newsService;
            _pageService = pageService;
        }

        public async Task<IActionResult> Dashboard()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Challenge();
            }

            var model = new DashboardViewModel
            {
                UserName = user.UserName ?? "",
                Email = user.Email ?? "",
                LastLoginTime = DateTime.UtcNow              
            };

            return View(model);
        }

        public IActionResult Settings()
        {
            return View();
        }
    }
}