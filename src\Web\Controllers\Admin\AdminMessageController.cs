using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Messages;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Messages;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using MlSoft.Sites.Web.ViewModels;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    /// <summary>
    /// 留言管理控制器
    /// </summary>
    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class AdminMessageController : BaseController
    {
        private readonly ILogger<AdminMessageController> _logger;
        private readonly MessageInquiryService _messageService;
        private readonly AdminResource _adminResource;

        public AdminMessageController(
            ILogger<AdminMessageController> logger,
            MessageInquiryService messageService,
            AdminResource adminResource,
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _logger = logger;
            _messageService = messageService;
            _adminResource = adminResource;
        }

        // 获取当前用户ID的辅助方法
        private string GetCurrentUserId()
        {
            return User?.Identity?.Name ?? "system";
        }

        [HttpGet]
        public async Task<IActionResult> Index(int page = 1, MessageStatus? status = null, string? search = null)
        {
            try
            {
                const int pageSize = 10;
                var messages = await _messageService.GetPagedAsync(page, pageSize, status, search);
                var statistics = await _messageService.GetStatisticsAsync();

                var viewModel = new AdminMessageIndexViewModel
                {
                    Messages = messages,
                    Statistics = statistics,
                    CurrentStatus = status,
                    SearchQuery = search
                };

                return View("~/Views/Admin/AdminMessage/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading message index");
                TempData["Error"] = _adminResource["LoadDataError"];
                return View(new AdminMessageIndexViewModel());
            }
        }

        [HttpPost("MarkAsRead/{id}")]
        public async Task<IActionResult> MarkAsRead(string id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _messageService.MarkAsReadAsync(id, userId);

                if (success)
                {
                    return Json(new { success = true });
                }
                else
                {
                    return Json(new { success = false, error = _adminResource["UpdateError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking message as read for ID: {MessageId}", id);
                return Json(new { success = false, error = _adminResource["UpdateError"] });
            }
        }

        [HttpPost("UpdateStatus/{id}")]
        public async Task<IActionResult> UpdateStatus(string id, MessageStatus status)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _messageService.UpdateStatusAsync(id, status, userId);

                if (success)
                {
                    return Json(new { success = true, message = _adminResource["StatusUpdateSuccess"] });
                }
                else
                {
                    return Json(new { success = false, error = _adminResource["StatusUpdateError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating message status for ID: {MessageId}", id);
                return Json(new { success = false, error = _adminResource["UpdateError"] });
            }
        }

        [HttpPost("UpdateDealResult/{id}")]
        public async Task<IActionResult> UpdateDealResult(string id, string dealResult)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(dealResult))
                {
                    return Json(new { success = false, error = _adminResource["DealResultRequired"] });
                }

                var userId = GetCurrentUserId();
                var success = await _messageService.UpdateDealResultAsync(id, dealResult, userId);

                if (success)
                {
                    return Json(new { success = true, message = _adminResource["DealResultSaveSuccess"] });
                }
                else
                {
                    return Json(new { success = false, error = _adminResource["SaveError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating deal result for message ID: {MessageId}", id);
                return Json(new { success = false, error = _adminResource["SaveError"] });
            }
        }

        [HttpPost("ToggleImportant/{id}")]
        public async Task<IActionResult> ToggleImportant(string id)
        {
            try
            {
                var success = await _messageService.ToggleImportantAsync(id);

                if (success)
                {
                    return Json(new { success = true, message = _adminResource["ImportantToggleSuccess"] });
                }
                else
                {
                    return Json(new { success = false, error = _adminResource["UpdateError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling important flag for message ID: {MessageId}", id);
                return Json(new { success = false, error = _adminResource["UpdateError"] });
            }
        }

        /// <summary>
        /// 前台联系表单提交 - 公开端点
        /// </summary>
        [AllowAnonymous]
        [HttpPost("/Contact/Submit")]
        [HttpPost("/{culture}/Contact/Submit")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Submit([FromForm] ContactFormViewModel model)
        {
            try
            {
                // 验证模型
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return Json(new {
                        success = false,
                        message = string.Join(";", errors)
                    });
                }

                // 创建留言实体
                var messageInquiry = new MessageInquiry
                {
                    ContactName = model.ContactName?.Trim() ?? string.Empty,
                    ContactEmail = model.ContactEmail?.Trim() ?? string.Empty,
                    ContactPhone = model.ContactPhone?.Trim(),
                    CompanyName = model.CompanyName?.Trim(),
                    JobTitle = model.JobTitle?.Trim(),
                    Message = model.Message?.Trim() ?? string.Empty,
                    Type = model.Type,
                    Status = MessageStatus.New,
                    SourcePage = model.SourcePage ?? Request.Path,
                    ReferrerUrl = model.ReferrerUrl ?? Request.Headers.Referer,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = Request.Headers.UserAgent,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    IsRead = false,
                    IsImportant = false,
                    DealResult = string.Empty
                };

                // 保存到数据库
                var createdMessage = await _messageService.CreateAsync(messageInquiry);

                if (createdMessage != null)
                {
                    _logger.LogInformation("New contact form submission from {Email}, Type: {Type}, ID: {Id}",
                        messageInquiry.ContactEmail, messageInquiry.Type, createdMessage.Id);

                    return Json(new {
                        success = true,
                        message = ""
                    });
                }
                else
                {
                    _logger.LogError("Failed to save contact form submission from {Email}", model.ContactEmail);
                    return Json(new {
                        success = false,
                        message = ""
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing contact form submission from {Email}", model?.ContactEmail);
                return Json(new {
                    success = false,
                    message = ""
                });
            }
        }

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        private string GetClientIpAddress()
        {
            // 检查是否通过代理
            var xForwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xForwardedFor))
            {
                return xForwardedFor.Split(',')[0].Trim();
            }

            var xRealIp = Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(xRealIp))
            {
                return xRealIp;
            }

            return Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }
    }
}