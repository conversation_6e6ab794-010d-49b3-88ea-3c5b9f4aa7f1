using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.News;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Service.News;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class AdminNewsController : BaseController
    {
        private readonly ILogger<AdminNewsController> _logger;
        private readonly IViewRenderService _viewRenderService;
        private readonly NewsAnnouncementService _newsService;
        private readonly AdminResource _adminResource;

        public AdminNewsController(
            ILogger<AdminNewsController> logger,
            IViewRenderService viewRenderService,
            NewsAnnouncementService newsService,
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration,
            AdminResource adminResource)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _logger = logger;
            _viewRenderService = viewRenderService;
            _newsService = newsService;
            _adminResource = adminResource;
        }

        // GET: /admin/news
        [HttpGet]
        public async Task<IActionResult> Index(NewsType? type = null, NewsStatus? status = null, string search = "", int page = 1)
        {
            try
            {
                var newsList = await _newsService.GetNewsWithFiltersAsync(type, status, search, page, 10);
                var totalCount = await _newsService.GetNewsCountWithFiltersAsync(type, status, search);

                var viewModel = new AdminNewsIndexViewModel
                {
                    News = new Model.Entities.Common.PagedResult<NewsAnnouncement>()
                    {
                        Items = newsList.ToList(),
                        Page = page,
                        Total = (int)totalCount,
                        PageSize = 10
                    },
                   
                    SelectedType = type,
                    SelectedStatus = status,
                    SearchTerm = search ?? string.Empty,
                    AvailableTypes = Enum.GetValues<NewsType>()
                };

                return View("~/Views/Admin/AdminNews/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading news index page");
                return View("Error");
            }
        }

        // GET: /admin/news/statistics (simplified)
        [HttpGet("statistics")]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                var statistics = await _newsService.GetNewsStatisticsAsync();
                return Json(new { success = true, data = statistics });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading news statistics");
                return Json(new { success = false, message = _adminResource["LoadStatisticsError"] });
            }
        }

        // GET: /admin/news/create
        [HttpGet("create")]
        public IActionResult Create()
        {
            var viewModel = new NewsAnnouncementViewModel
            {
                Type = NewsType.CompanyNews,
                PublishDate = DateTime.UtcNow,
                IsFeatured = false,
                Status = NewsStatus.Draft,
                Priority = (int)NewsPriority.Normal,
                Source = NewsSource.Internal,
                Locale = new Dictionary<string, NewsAnnouncementLocaleFields>(),
                ImageUrls = new List<string>(),
                Tags = new List<string>(),
                RelatedNewsIds = new List<string>()
            };

            // 初始化多语言字段
            foreach (var lang in _supportedLanguages)
            {
                viewModel.Locale[lang.Code] = new NewsAnnouncementLocaleFields();
            }

            return PartialView("~/Views/Admin/AdminNews/Partials/_NewsModal.cshtml", viewModel);
        }

        // POST: /admin/news/create
        [HttpPost("create")]
        public async Task<IActionResult> Create([FromBody] NewsAnnouncementViewModel model)
        {
            try
            {
                // 调试输出
                _logger.LogInformation("Create news called. Model is null: {IsNull}", model == null);
                if (model != null)
                {
                    _logger.LogInformation("Model received: Id={Id}, Type={Type}, Title={Title}",
                        model.Id, model.Type, model.Locale?.FirstOrDefault().Value?.Title);
                }

                if (model == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = "Model is null"
                    });
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                    _logger.LogWarning("Model validation failed: {Errors}", string.Join(", ", errors));

                    return Json(new
                    {
                        success = false,
                        message = _adminResource["ValidationFailed"],
                        errors = errors
                    });
                }

                var news = new NewsAnnouncement
                {
                    Type = model.Type,
                    PublishDate = model.PublishDate,
                    ImageUrls = model.ImageUrls ?? new List<string>(),
                    ThumbnailUrl = model.ThumbnailUrl,
                    IsFeatured = model.IsFeatured,
                    Locale = model.Locale,
                    Status = model.Status,
                    Priority = model.Priority,
                    Source = model.Source,
                    ExternalUrl = model.ExternalUrl,
                    Tags = model.Tags ?? new List<string>(),
                    RelatedNewsIds = model.RelatedNewsIds ?? new List<string>(),
                    CreatedById = User.Identity?.Name,
                    LastModifiedById = User.Identity?.Name,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _newsService.CreateNewsAsync(news);

                return Json(new
                {
                    success = true,
                    message = _adminResource["CreateNewsSuccess"],
                    redirectUrl = Url.Action("Index")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating news");
                return Json(new
                {
                    success = false,
                    message = _adminResource["CreateNewsError"]
                });
            }
        }

        // GET: /admin/news/edit/{id}
        [HttpGet("edit/{id}")]
        public async Task<IActionResult> Edit(string id)
        {
            try
            {
                var news = await _newsService.GetByIdAsync(id);
                if (news == null)
                {
                    return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                }

                var viewModel = new NewsAnnouncementViewModel
                {
                    Id = news.Id,
                    Type = news.Type,
                    PublishDate = news.PublishDate,
                    ImageUrls = news.ImageUrls ?? new List<string>(),
                    ThumbnailUrl = news.ThumbnailUrl,
                    IsFeatured = news.IsFeatured,
                    Locale = news.Locale,
                    Status = news.Status,
                    Priority = news.Priority,
                    Source = news.Source,
                    ExternalUrl = news.ExternalUrl,
                    Tags = news.Tags ?? new List<string>(),
                    RelatedNewsIds = news.RelatedNewsIds ?? new List<string>(),
                    ViewCount = news.ViewCount,
                    CreatedAt = news.CreatedAt,
                    UpdatedAt = news.UpdatedAt,
                    CreatedById = news.CreatedById,
                    LastModifiedById = news.LastModifiedById
                };

                return Json(new { success = true, data = viewModel });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading news for edit: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadRecordError"] });
            }
        }

        // POST: /admin/news/edit/{id}
        [HttpPost("edit/{id}")]
        public async Task<IActionResult> Edit(string id, [FromBody] NewsAnnouncementViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new
                    {
                        success = false,
                        message = _adminResource["ValidationFailed"],
                        errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)
                    });
                }

                var news = await _newsService.GetByIdAsync(id);
                if (news == null)
                {
                    return NotFound();
                }

                news.Type = model.Type;
                news.PublishDate = model.PublishDate;
                news.ImageUrls = model.ImageUrls ?? new List<string>();
                news.ThumbnailUrl = model.ThumbnailUrl;
                news.IsFeatured = model.IsFeatured;
                news.Locale = model.Locale;
                news.Status = model.Status;
                news.Priority = model.Priority;
                news.Source = model.Source;
                news.ExternalUrl = model.ExternalUrl;
                news.Tags = model.Tags ?? new List<string>();
                news.RelatedNewsIds = model.RelatedNewsIds ?? new List<string>();
                news.LastModifiedById = User.Identity?.Name;
                news.UpdatedAt = DateTime.UtcNow;

                await _newsService.UpdateNewsAsync(id, news);

                return Json(new
                {
                    success = true,
                    message = _adminResource["UpdateNewsSuccess"],
                    redirectUrl = Url.Action("Index")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating news: {Id}", id);
                return Json(new
                {
                    success = false,
                    message = _adminResource["UpdateNewsError"]
                });
            }
        }

        // POST: /admin/news/delete/{id}
        [HttpPost("delete/{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var result = await _newsService.DeleteAsync(id);
                if (result)
                {
                    return Json(new
                    {
                        success = true,
                        message = _adminResource["DeleteNewsSuccess"]
                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = _adminResource["DeleteNewsError"]
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting news: {Id}", id);
                return Json(new
                {
                    success = false,
                    message = _adminResource["DeleteNewsError"]
                });
            }
        }

        // POST: /admin/news/toggle-featured/{id}
        [HttpPost("toggle-featured/{id}")]
        public async Task<IActionResult> ToggleFeatured(string id)
        {
            try
            {
                var news = await _newsService.GetByIdAsync(id);
                if (news == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = _adminResource["NewsNotFound"]
                    });
                }

                var newFeaturedStatus = !news.IsFeatured;
                await _newsService.ToggleFeaturedAsync(id, newFeaturedStatus);

                return Json(new
                {
                    success = true,
                    message = newFeaturedStatus ? _adminResource["SetFeaturedSuccess"] : _adminResource["UnsetFeaturedSuccess"],
                    isFeatured = newFeaturedStatus
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling featured status: {Id}", id);
                return Json(new
                {
                    success = false,
                    message = _adminResource["ToggleFeaturedError"]
                });
            }
        }

        // POST: /admin/news/toggle-publish/{id}
        [HttpPost("toggle-publish/{id}")]
        public async Task<IActionResult> TogglePublish(string id)
        {
            try
            {
                var news = await _newsService.GetByIdAsync(id);
                if (news == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = _adminResource["NewsNotFound"]
                    });
                }

                var newPublishStatus = news.Status != NewsStatus.Published;
                await _newsService.TogglePublishAsync(id, newPublishStatus);

                return Json(new
                {
                    success = true,
                    message = newPublishStatus ? _adminResource["PublishSuccess"] : _adminResource["UnpublishSuccess"],
                    isPublished = newPublishStatus
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling publish status: {Id}", id);
                return Json(new
                {
                    success = false,
                    message = _adminResource["TogglePublishError"]
                });
            }
        }

        // POST: /admin/news/schedule-publish/{id}
        [HttpPost("schedule-publish/{id}")]
        public async Task<IActionResult> SchedulePublish(string id, DateTime publishDate)
        {
            try
            {
                var result = await _newsService.SchedulePublishAsync(id, publishDate);
                if (result)
                {
                    return Json(new
                    {
                        success = true,
                        message = _adminResource["SchedulePublishSuccess"]
                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = _adminResource["SchedulePublishError"]
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling publish: {Id}", id);
                return Json(new
                {
                    success = false,
                    message = _adminResource["SchedulePublishError"]
                });
            }
        }
    }
}
