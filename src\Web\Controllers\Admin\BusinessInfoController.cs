using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Business;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Service.Business;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class BusinessInfoController : BaseController
    {
        private readonly ILogger<BusinessInfoController> _logger;
        private readonly IViewRenderService _viewRenderService;
        private readonly BusinessDivisionService _businessDivisionService;
        private readonly ProductServiceService _productServiceService;
        private readonly AdminResource _adminResource;

        public BusinessInfoController(
              ILogger<BusinessInfoController> logger,
            IViewRenderService viewRenderService,
            BusinessDivisionService businessDivisionService,
            ProductServiceService productServiceService,
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration,
            AdminResource adminResource)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _viewRenderService = viewRenderService;
            _businessDivisionService = businessDivisionService;
            _productServiceService = productServiceService;
            _adminResource = adminResource;
            _logger = logger;
        }

        // Tab页面主视图 - 只加载业务部门数据
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var businessDivisions = await _businessDivisionService.GetAllAsync();
                
                var viewModel = new BusinessInfoViewModel
                {
                    BusinessDivisions = businessDivisions.Select(bd => new BusinessDivisionViewModel
                    {
                        Id = bd.Id,
                        Locale = bd.Locale,
                        ImageUrl = bd.ImageUrl,
                        IconUrl = bd.IconUrl,
                        DisplayOrder = bd.DisplayOrder,
                        IsActive = bd.IsActive,
                        CreatedAt = bd.CreatedAt,
                        UpdatedAt = bd.UpdatedAt
                    }).ToList(),
                    // 产品服务数据为空，懒加载
                    ProductServices = new List<ProductServiceViewModel>()
                };

               
                return View("~/Views/Admin/BusinessInfo/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading business info index page");
                return View("Error");
            }
        }

        // 懒加载 API 端点
        [HttpGet("tab/{tabName}")]
        public async Task<IActionResult> GetTabContent(string tabName)
        {
            try
            {
                var partialViewName = GetPartialViewName(tabName);
                var model = await GetTabDataAsync(tabName);

               

                var html = await _viewRenderService.RenderPartialViewAsync(
                    partialViewName, model, ViewData);

                return Json(new { success = true, html = html });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading tab content: {TabName}", tabName);
                return Json(new {
                    success = false,
                    message = _adminResource["LoadTabError"]
                });
            }
        }

        // 业务部门 CRUD API
        [HttpPost("business-division")]
        public async Task<IActionResult> SaveBusinessDivision([FromBody] BusinessDivisionViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                var entity = new BusinessDivision
                {
                    Id = string.IsNullOrEmpty(model.Id) ? string.Empty : model.Id,
                    Locale = model.Locale,
                    ImageUrl = model.ImageUrl,
                    IconUrl = model.IconUrl,
                    DisplayOrder = model.DisplayOrder,
                    IsActive = model.IsActive,
                    CreatedAt = model.CreatedAt == default ? DateTime.UtcNow : model.CreatedAt,
                    UpdatedAt = DateTime.UtcNow
                };

                BusinessDivision result;
                if (string.IsNullOrEmpty(model.Id))
                {
                    result = await _businessDivisionService.CreateAsync(entity);
                }
                else
                {
                    await _businessDivisionService.UpdateAsync(model.Id, entity);
                    result = entity;
                }

                return Json(new { 
                    success = true, 
                    message = _adminResource["SaveSuccess"],
                    data = new BusinessDivisionViewModel
                    {
                        Id = result.Id,
                        Locale = result.Locale,
                        ImageUrl = result.ImageUrl,
                        IconUrl = result.IconUrl,
                        DisplayOrder = result.DisplayOrder,
                        IsActive = result.IsActive,
                        CreatedAt = result.CreatedAt,
                        UpdatedAt = result.UpdatedAt
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving business division");
                return Json(new { success = false, message = _adminResource["SaveError"] });
            }
        }

        [HttpGet("business-division/{id}")]
        public async Task<IActionResult> GetBusinessDivision(string id)
        {
            try
            {
                var entity = await _businessDivisionService.GetByIdAsync(id);
                if (entity == null)
                {
                    return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                }

                var model = new BusinessDivisionViewModel
                {
                    Id = entity.Id,
                    Locale = entity.Locale,
                    ImageUrl = entity.ImageUrl,
                    IconUrl = entity.IconUrl,
                    DisplayOrder = entity.DisplayOrder,
                    IsActive = entity.IsActive,
                    CreatedAt = entity.CreatedAt,
                    UpdatedAt = entity.UpdatedAt
                };

                return Json(new { success = true, data = model });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting business division: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadRecordError"] });
            }
        }

        [HttpDelete("business-division/{id}")]
        public async Task<IActionResult> DeleteBusinessDivision(string id)
        {
            try
            {
                var result = await _businessDivisionService.DeleteAsync(id);
                if (result)
                {
                    return Json(new { success = true, message = _adminResource["DeleteSuccess"] });
                }
                else
                {
                    return Json(new { success = false, message = _adminResource["DeleteError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting business division: {Id}", id);
                return Json(new { success = false, message = _adminResource["DeleteError"] });
            }
        }

        // 产品服务 CRUD API
        [HttpPost("product-service")]
        public async Task<IActionResult> SaveProductService([FromBody] ProductServiceViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                var entity = new ProductService
                {
                    Id = string.IsNullOrEmpty(model.Id) ? string.Empty : model.Id,
                    BusinessDivisionId = model.BusinessDivisionId,
                    Locale = model.Locale,
                    ImageUrls = model.ImageUrls,
                    Documents = model.Documents.Select(d => new ProductDocument
                    {
                        Locale = d.Locale,
                        FileUrl = d.FileUrl,
                        FileType = d.FileType,
                        FileSize = d.FileSize,
                        UploadDate = d.UploadDate
                    }).ToList(),
                    Category = model.Category,
                    Price = model.Price,
                    Currency = model.Currency,
                    DisplayOrder = model.DisplayOrder,
                    IsActive = model.IsActive,
                    CreatedAt = model.CreatedAt == default ? DateTime.UtcNow : model.CreatedAt,
                    UpdatedAt = DateTime.UtcNow
                };

                ProductService result;
                if (string.IsNullOrEmpty(model.Id))
                {
                    result = await _productServiceService.CreateAsync(entity);
                }
                else
                {
                    await _productServiceService.UpdateAsync(model.Id, entity);
                    result = entity;
                }

                return Json(new { 
                    success = true, 
                    message = _adminResource["SaveSuccess"],
                    data = new ProductServiceViewModel
                    {
                        Id = result.Id,
                        BusinessDivisionId = result.BusinessDivisionId,
                        Locale = result.Locale,
                        ImageUrls = result.ImageUrls,
                        Documents = result.Documents.Select(d => new ProductDocumentViewModel
                        {
                            Locale = d.Locale,
                            FileUrl = d.FileUrl,
                            FileType = d.FileType,
                            FileSize = d.FileSize,
                            UploadDate = d.UploadDate
                        }).ToList(),
                        Category = result.Category,
                        Price = result.Price,
                        Currency = result.Currency,
                        DisplayOrder = result.DisplayOrder,
                        IsActive = result.IsActive,
                        CreatedAt = result.CreatedAt,
                        UpdatedAt = result.UpdatedAt
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving product service");
                return Json(new { success = false, message = _adminResource["SaveError"] });
            }
        }

        [HttpGet("product-service/{id}")]
        public async Task<IActionResult> GetProductService(string id)
        {
            try
            {
                var entity = await _productServiceService.GetByIdAsync(id);
                if (entity == null)
                {
                    return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                }

                var model = new ProductServiceViewModel
                {
                    Id = entity.Id,
                    BusinessDivisionId = entity.BusinessDivisionId,
                    Locale = entity.Locale,
                    ImageUrls = entity.ImageUrls,
                    Documents = entity.Documents.Select(d => new ProductDocumentViewModel
                    {
                        Locale = d.Locale,
                        FileUrl = d.FileUrl,
                        FileType = d.FileType,
                        FileSize = d.FileSize,
                        UploadDate = d.UploadDate
                    }).ToList(),
                    Category = entity.Category,
                    Price = entity.Price,
                    Currency = entity.Currency,
                    DisplayOrder = entity.DisplayOrder,
                    IsActive = entity.IsActive,
                    CreatedAt = entity.CreatedAt,
                    UpdatedAt = entity.UpdatedAt
                };

                return Json(new { success = true, data = model });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product service: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadRecordError"] });
            }
        }

        [HttpDelete("product-service/{id}")]
        public async Task<IActionResult> DeleteProductService(string id)
        {
            try
            {
                var result = await _productServiceService.DeleteAsync(id);
                if (result)
                {
                    return Json(new { success = true, message = _adminResource["DeleteSuccess"] });
                }
                else
                {
                    return Json(new { success = false, message = _adminResource["DeleteError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product service: {Id}", id);
                return Json(new { success = false, message = _adminResource["DeleteError"] });
            }
        }

        // 获取业务部门列表（用于产品服务的下拉选择）
        [HttpGet("business-divisions")]
        public async Task<IActionResult> GetBusinessDivisions()
        {
            try
            {
                var divisions = await _businessDivisionService.GetAllAsync();
                var result = divisions.Select(d => new
                {
                    id = d.Id,
                    name = d.Locale.ContainsKey(_currentLanguage) ? d.Locale[_currentLanguage].DivisionName :
                           d.Locale.ContainsKey(ViewData["DefaultLanguage"]?.ToString()) ? d.Locale[ViewData["DefaultLanguage"]?.ToString()].DivisionName : 
                           d.Locale.Values.FirstOrDefault()?.DivisionName ?? "Unknown"
                }).ToList();

                return Json(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting business divisions");
                return Json(new { success = false, message = _adminResource["LoadRecordError"] });
            }
        }

        // 标签页名称映射
        private string GetPartialViewName(string tabName)
        {
            return tabName switch
            {
                "business-divisions" => "Admin/BusinessInfo/Partials/_BusinessDivisionsTab",
                "product-services" => "Admin/BusinessInfo/Partials/_ProductServicesTab",
                _ => throw new ArgumentException($"Invalid tab name: {tabName}")
            };
        }

        // 标签页数据获取
        private async Task<object> GetTabDataAsync(string tabName)
        {
            return tabName switch
            {
                "business-divisions" => await GetBusinessDivisionsDataAsync(),
                "product-services" => await GetProductServicesDataAsync(),
                _ => null
            };
        }

        private async Task<List<BusinessDivisionViewModel>> GetBusinessDivisionsDataAsync()
        {
            var entities = await _businessDivisionService.GetAllAsync();
            return entities.Select(bd => new BusinessDivisionViewModel
            {
                Id = bd.Id,
                Locale = bd.Locale,
                ImageUrl = bd.ImageUrl,
                IconUrl = bd.IconUrl,
                DisplayOrder = bd.DisplayOrder,
                IsActive = bd.IsActive,
                CreatedAt = bd.CreatedAt,
                UpdatedAt = bd.UpdatedAt
            }).ToList();
        }

        private async Task<List<ProductServiceViewModel>> GetProductServicesDataAsync()
        {
            var entities = await _productServiceService.GetAllAsync();
            var businessDivisions = await _businessDivisionService.GetAllAsync();
            var divisionDict = businessDivisions.ToDictionary(d => d.Id, d => d);

            return entities.Select(ps => new ProductServiceViewModel
            {
                Id = ps.Id,
                BusinessDivisionId = ps.BusinessDivisionId,
                    BusinessDivisionName = ps.BusinessDivisionId != null && divisionDict.ContainsKey(ps.BusinessDivisionId) 
                        ? (divisionDict[ps.BusinessDivisionId].Locale.ContainsKey(_currentLanguage) 
                            ? divisionDict[ps.BusinessDivisionId].Locale[_currentLanguage].DivisionName
                            : divisionDict[ps.BusinessDivisionId].Locale.Values.FirstOrDefault()?.DivisionName ?? "Unknown")
                        : null,
                Locale = ps.Locale,
                ImageUrls = ps.ImageUrls,
                Documents = ps.Documents.Select(d => new ProductDocumentViewModel
                {
                    Locale = d.Locale,
                    FileUrl = d.FileUrl,
                    FileType = d.FileType,
                    FileSize = d.FileSize,
                    UploadDate = d.UploadDate
                }).ToList(),
                Category = ps.Category,
                Price = ps.Price,
                Currency = ps.Currency,
                DisplayOrder = ps.DisplayOrder,
                IsActive = ps.IsActive,
                CreatedAt = ps.CreatedAt,
                UpdatedAt = ps.UpdatedAt
            }).ToList();
        }
    }
}
