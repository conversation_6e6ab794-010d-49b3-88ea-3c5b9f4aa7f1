using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Company;
using MlSoft.Sites.Model.Entities.CSR;
using MlSoft.Sites.Model.Entities.History;
using MlSoft.Sites.Model.Entities.Investor;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Entities.Organization;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.CSR;
using MlSoft.Sites.Service.History;
using MlSoft.Sites.Service.Investor;
using MlSoft.Sites.Service.Organization;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    /// <summary>
    /// 企业信息管理控制器
    /// </summary>
    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class CompanyInfoController : BaseController
    {
        private readonly ILogger<CompanyInfoController> _logger;
        private readonly CompanyService _companyService;
        private readonly CompanyHistoryService _companyHistoryService;
        private readonly ExecutiveService _executiveService;
        private readonly OrganizationStructureService _organizationService;
        private readonly CompanyLocationService _companyLocationService;
        private readonly CSRActivityService _csrActivityService;
        private readonly FinancialReportService _financialReportService;
        private readonly ShareholderMeetingService _shareholderMeetingService;
        private readonly AdminResource _adminResource;
        private readonly IWebHostEnvironment _env;
        private readonly IViewRenderService _viewRenderService;
        private readonly IMemoryCache _cache;

        public CompanyInfoController(
            ILogger<CompanyInfoController> logger,
            CompanyService companyService,
            CompanyHistoryService companyHistoryService,
            ExecutiveService executiveService,
            OrganizationStructureService organizationService,
            CompanyLocationService companyLocationService,
            CSRActivityService csrActivityService,
            FinancialReportService financialReportService,
            ShareholderMeetingService shareholderMeetingService,
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration,
            IWebHostEnvironment env,
              IMemoryCache cache,
            AdminResource adminResource,
            IViewRenderService viewRenderService)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _logger = logger;
            _companyService = companyService;
            _companyHistoryService = companyHistoryService;
            _executiveService = executiveService;
            _organizationService = organizationService;
            _companyLocationService = companyLocationService;
            _csrActivityService = csrActivityService;
            _financialReportService = financialReportService;
            _shareholderMeetingService = shareholderMeetingService;
            _adminResource = adminResource;
            _env = env;
            _viewRenderService = viewRenderService;
            _cache = cache;
        }

        protected SupportedLanguage[] SupportedLanguages => _supportedLanguages;

        /// <summary>
        /// 企业信息管理主页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = new CompanyInfoViewModel();

                // 获取企业基本信息
                var companies = await _companyService.GetAllAsync();
                var company = companies.FirstOrDefault();

                if (company != null)
                {
                    viewModel.BasicInfo = MapToBasicInfoViewModel(company);

                    // 懒加载策略：只加载基本信息，其他标签页按需加载
                    // 企业历史、高管信息、CSR活动、投资者关系等将通过API懒加载
                }

                return View("~/Views/Admin/CompanyInfo/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取企业信息时发生错误");
                TempData["Error"] = _adminResource["ErrorLoadingCompanyInfo"];
                return View(new CompanyInfoViewModel());
            }
        }

        /// <summary>
        /// 懒加载标签页内容
        /// </summary>
        /// <param name="tabName">标签页名称</param>
        /// <returns></returns>
        [HttpGet("tab/{tabName}")]
        public async Task<IActionResult> GetTabContent(string tabName)
        {
            try
            {
                var partialViewName = GetPartialViewName(tabName);
                var model = await GetTabDataAsync(tabName);


                // 渲染部分视图为HTML字符串
                var html = await _viewRenderService.RenderPartialViewAsync(
                    partialViewName,
                    model,
                    ViewData
                );

                return Json(new { success = true, html = html });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading tab content: {TabName}", tabName);
                return Json(new
                {
                    success = false,
                    message = _adminResource["LoadTabError"] ?? "Failed to load tab content"
                });
            }
        }

        /// <summary>
        /// 获取部分视图名称
        /// </summary>
        /// <param name="tabName"></param>
        /// <returns></returns>
        private string GetPartialViewName(string tabName)
        {
            return tabName switch
            {
                "company-history" => "Admin/CompanyInfo/Partials/_CompanyHistoryTab",
                "executive-organization" => "Admin/CompanyInfo/Partials/_ExecutiveOrganizationTab",
                "contact-info" => "Admin/CompanyInfo/Partials/_ContactInfoTab",
                "csr-activities" => "Admin/CompanyInfo/Partials/_CSRActivitiesTab",
                "investor-relations" => "Admin/CompanyInfo/Partials/_InvestorRelationsTab",
                _ => throw new ArgumentException($"Invalid tab name: {tabName}")
            };
        }

        /// <summary>
        /// 获取标签页数据
        /// </summary>
        /// <param name="tabName"></param>
        /// <returns></returns>
        private async Task<object> GetTabDataAsync(string tabName)
        {
            return tabName switch
            {
                "company-history" => await GetCompanyHistoryDataAsync(),
                "executive-organization" => await GetExecutiveOrganizationDataAsync(),
                "contact-info" => await GetContactInfoDataAsync(),
                "csr-activities" => await GetCSRActivitiesDataAsync(),
                "investor-relations" => await GetInvestorRelationsDataAsync(),
                _ => new { }
            };
        }

        /// <summary>
        /// 获取企业历史数据
        /// </summary>
        /// <returns></returns>
        private async Task<List<CompanyHistoryItemViewModel>> GetCompanyHistoryDataAsync()
        {
            var histories = await _companyHistoryService.GetAllAsync();
            return histories.Select(MapToHistoryViewModel).ToList();
        }

        /// <summary>
        /// 获取高管组织数据
        /// </summary>
        /// <returns></returns>
        private async Task<ExecutiveOrganizationViewModel> GetExecutiveOrganizationDataAsync()
        {
            var executives = await _executiveService.GetExecutivesOrderedAsync();
            var organizations = await _organizationService.GetActiveDepartmentsAsync();

            var executiveViewModels = executives.Select(MapToExecutiveViewModel).ToList();
            var organizationViewModels = organizations.Select(MapToOrganizationViewModel).ToList();

            return new ExecutiveOrganizationViewModel
            {
                Executives = executiveViewModels,
                OrganizationStructures = organizationViewModels.OrderBy(x => x.Id).ThenBy(x => x.DisplayOrder).ToList()
            };
        }

        /// <summary>
        /// 获取联系信息数据
        /// </summary>
        /// <returns></returns>
        private async Task<ContactInfoViewModel> GetContactInfoDataAsync()
        {
            var company = await _companyService.FindOneAsync(x => true);
            if (company == null)
            {
                return new ContactInfoViewModel();
            }

            return await MapToContactInfoViewModel(company);
        }

        /// <summary>
        /// 获取CSR活动数据
        /// </summary>
        /// <returns></returns>
        private async Task<List<CSRActivityItemViewModel>> GetCSRActivitiesDataAsync()
        {
            var activities = await _csrActivityService.GetActiveActivitiesAsync();
            return activities.Select(MapToCSRActivityViewModel).ToList();
        }

        /// <summary>
        /// 获取投资者关系数据
        /// </summary>
        /// <returns></returns>
        private async Task<InvestorRelationsViewModel> GetInvestorRelationsDataAsync()
        {
            var financialReports = await _financialReportService.FindAsync(x => true);
            var shareholderMeetings = await _shareholderMeetingService.GetMeetingsByYearAsync(DateTime.Now.Year);

            var financialReportViewModels = financialReports.Select(MapToFinancialReportViewModel).ToList();
            var shareholderMeetingViewModels = shareholderMeetings.Select(MapToShareholderMeetingViewModel).ToList();

            return new InvestorRelationsViewModel
            {
                FinancialReports = financialReportViewModels,
                ShareholderMeetings = shareholderMeetingViewModels
            };
        }

        /// <summary>
        /// 保存企业基本信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("basic-info")]
        public async Task<IActionResult> SaveBasicInfo([FromBody] CompanyBasicInfoViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                var companies = await _companyService.GetAllAsync();
                var company = companies.FirstOrDefault();

                if (company == null)
                {
                    // 创建新企业
                    company = new Company
                    {
                        EstablishedDate = model.EstablishedDate,
                        RegistrationNumber = model.RegistrationNumber,
                        Capital = model.Capital,
                        Currency = model.Currency,
                        EmployeeScale = model.EmployeeScale,
                        IsActive = model.IsActive,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    // 设置多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        company.Locale[lang.Code] = new Model.Entities.LocaleFields.CompanyLocaleFields
                        {
                            CompanyName = model.CompanyNames.GetValueOrDefault(lang.Code, ""),
                            CompanyDescription = model.CompanyDescriptions.GetValueOrDefault(lang.Code, ""),
                            Mission = model.Philosophy.GetValueOrDefault(lang.Code, "")
                        };
                    }

                    await _companyService.CreateAsync(company);
                }
                else
                {
                    // 更新现有企业
                    company.EstablishedDate = model.EstablishedDate;
                    company.RegistrationNumber = model.RegistrationNumber;
                    company.Capital = model.Capital;
                    company.Currency = model.Currency;
                    company.EmployeeScale = model.EmployeeScale;
                    company.IsActive = model.IsActive;
                    company.UpdatedAt = DateTime.UtcNow;

                    // 更新多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        if (!company.Locale.ContainsKey(lang.Code))
                        {
                            company.Locale[lang.Code] = new Model.Entities.LocaleFields.CompanyLocaleFields();
                        }

                        company.Locale[lang.Code].CompanyName = model.CompanyNames.GetValueOrDefault(lang.Code, "");
                        company.Locale[lang.Code].CompanyDescription = model.CompanyDescriptions.GetValueOrDefault(lang.Code, "");
                        company.Locale[lang.Code].Mission = model.Philosophy.GetValueOrDefault(lang.Code, "");
                    }

                    await _companyService.UpdateAsync(company.Id, company);
                }

                // 清理缓存
                _cache.Remove(ComponentConfigService.CACHE_GLOBAL_SITEINFO);

                return Json(new { success = true, message = _adminResource["SaveBasicInfoSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存企业基本信息时发生错误");
                return Json(new { success = false, message = _adminResource["SaveBasicInfoError"] });
            }
        }

        /// <summary>
        /// 保存企业历史
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("company-history")]
        public async Task<IActionResult> SaveCompanyHistory([FromBody] CompanyHistoryItemViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                CompanyHistory history;

                if (string.IsNullOrEmpty(model.Id))
                {
                    // 创建新历史记录
                    history = new CompanyHistory
                    {
                        EventDate = model.EventDate,
                        EventType = model.EventType,
                        ImageUrl = model.ImageUrl,
                        DisplayOrder = model.DisplayOrder,
                        IsActive = model.IsActive,
                        CreatedAt = DateTime.UtcNow
                    };

                    // 设置多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        history.Locale[lang.Code] = new Model.Entities.LocaleFields.CompanyHistoryLocaleFields
                        {
                            EventTitle = model.EventTitles.GetValueOrDefault(lang.Code, ""),
                            EventDescription = model.EventDescriptions.GetValueOrDefault(lang.Code, "")
                        };
                    }

                    await _companyHistoryService.CreateAsync(history);
                }
                else
                {
                    // 更新现有历史记录
                    history = await _companyHistoryService.GetByIdAsync(model.Id);
                    if (history == null)
                    {
                        return Json(new { success = false, message = _adminResource["HistoryNotFound"] });
                    }

                    if (!string.IsNullOrEmpty(history.ImageUrl) && history.ImageUrl != model.ImageUrl)
                    {
                        //删除老的图片
                    }

                    history.EventDate = model.EventDate;
                    history.EventType = model.EventType;
                    history.ImageUrl = model.ImageUrl;
                    history.DisplayOrder = model.DisplayOrder;
                    history.IsActive = model.IsActive;

                    // 更新多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        if (!history.Locale.ContainsKey(lang.Code))
                        {
                            history.Locale[lang.Code] = new Model.Entities.LocaleFields.CompanyHistoryLocaleFields();
                        }

                        history.Locale[lang.Code].EventTitle = model.EventTitles.GetValueOrDefault(lang.Code, "");
                        history.Locale[lang.Code].EventDescription = model.EventDescriptions.GetValueOrDefault(lang.Code, "");
                    }

                    await _companyHistoryService.UpdateAsync(history.Id, history);
                }

                return Json(new { success = true, message = _adminResource["SaveHistorySuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存企业历史时发生错误");
                return Json(new { success = false, message = _adminResource["SaveHistoryError"] });
            }
        }

        /// <summary>
        /// 获取单个企业历史记录
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("company-history/{id}")]
        public async Task<IActionResult> GetCompanyHistory(string id)
        {
            try
            {
                var history = await _companyHistoryService.GetByIdAsync(id);
                if (history == null)
                {
                    return Json(new { success = false, message = _adminResource["HistoryNotFound"] });
                }

                var viewModel = new CompanyHistoryItemViewModel
                {
                    Id = history.Id,
                    EventDate = history.EventDate,
                    EventType = history.EventType,
                    DisplayOrder = history.DisplayOrder,
                    ImageUrl = history.ImageUrl,
                    IsActive = history.IsActive,
                    EventTitles = new Dictionary<string, string>(),
                    EventDescriptions = new Dictionary<string, string>()
                };

                // 映射多语言字段
                foreach (var lang in SupportedLanguages)
                {
                    if (history.Locale.ContainsKey(lang.Code))
                    {
                        var localeData = history.Locale[lang.Code];
                        viewModel.EventTitles[lang.Code] = localeData.EventTitle ?? "";
                        viewModel.EventDescriptions[lang.Code] = localeData.EventDescription ?? "";
                    }
                    else
                    {
                        viewModel.EventTitles[lang.Code] = "";
                        viewModel.EventDescriptions[lang.Code] = "";
                    }
                }

                return Json(new { success = true, data = viewModel });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取企业历史记录时发生错误");
                return Json(new { success = false, message = _adminResource["LoadHistoryError"] });
            }
        }

        /// <summary>
        /// 删除企业历史
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("company-history/{id}")]
        public async Task<IActionResult> DeleteCompanyHistory(string id)
        {
            try
            {
                var history = await _companyHistoryService.GetByIdAsync(id);
                if (history == null)
                {
                    return Json(new { success = false, message = _adminResource["HistoryNotFound"] });
                }

                await _companyHistoryService.DeleteAsync(id);
                return Json(new { success = true, message = _adminResource["DeleteHistorySuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除企业历史时发生错误");
                return Json(new { success = false, message = _adminResource["DeleteHistoryError"] });
            }
        }

        #region Executive CRUD API Endpoints

        /// <summary>
        /// 保存高管信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("executive")]
        public async Task<IActionResult> SaveExecutive([FromBody] ExecutiveItemViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                Executive executive;
                if (string.IsNullOrEmpty(model.Id))
                {
                    // 新增
                    executive = new Executive
                    {
                        PhotoUrl = model.PhotoUrl,
                        IsPresident = model.IsPresident,
                        DisplayOrder = model.DisplayOrder,
                        IsActive = model.IsActive,
                        Locale = new Dictionary<string, ExecutiveLocaleFields>(),
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    // 添加多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        executive.Locale[lang.Code] = new ExecutiveLocaleFields
                        {
                            Name = model.Names.GetValueOrDefault(lang.Code, ""),
                            Position = model.Positions.GetValueOrDefault(lang.Code, ""),
                            Biography = model.Biographies.GetValueOrDefault(lang.Code, ""),
                            Message = model.Messages.GetValueOrDefault(lang.Code, "")
                        };
                    }

                    await _executiveService.CreateAsync(executive);
                }
                else
                {
                    // 更新
                    executive = await _executiveService.GetByIdAsync(model.Id);
                    if (executive == null)
                    {
                        return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                    }

                    executive.PhotoUrl = model.PhotoUrl;
                    executive.IsPresident = model.IsPresident;
                    executive.DisplayOrder = model.DisplayOrder;
                    executive.IsActive = model.IsActive;
                    executive.UpdatedAt = DateTime.UtcNow;

                    // 更新多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        if (!executive.Locale.ContainsKey(lang.Code))
                        {
                            executive.Locale[lang.Code] = new ExecutiveLocaleFields();
                        }

                        executive.Locale[lang.Code].Name = model.Names.GetValueOrDefault(lang.Code, "");
                        executive.Locale[lang.Code].Position = model.Positions.GetValueOrDefault(lang.Code, "");
                        executive.Locale[lang.Code].Biography = model.Biographies.GetValueOrDefault(lang.Code, "");
                        executive.Locale[lang.Code].Message = model.Messages.GetValueOrDefault(lang.Code, "");
                    }

                    await _executiveService.UpdateAsync(executive.Id, executive);
                }

                return Json(new { success = true, message = _adminResource["SaveExecutiveSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存高管信息时发生错误");
                return Json(new { success = false, message = _adminResource["SaveExecutiveError"] });
            }
        }

        /// <summary>
        /// 获取高管信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("executive/{id}")]
        public async Task<IActionResult> GetExecutive(string id)
        {
            try
            {
                var executive = await _executiveService.GetByIdAsync(id);
                if (executive == null)
                {
                    return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                }

                var viewModel = new ExecutiveItemViewModel
                {
                    Id = executive.Id,
                    PhotoUrl = executive.PhotoUrl,
                    IsPresident = executive.IsPresident,
                    DisplayOrder = executive.DisplayOrder,
                    IsActive = executive.IsActive,
                    Names = new Dictionary<string, string>(),
                    Positions = new Dictionary<string, string>(),
                    Biographies = new Dictionary<string, string>(),
                    Messages = new Dictionary<string, string>()
                };

                // 映射多语言字段
                foreach (var locale in executive.Locale)
                {
                    viewModel.Names[locale.Key] = locale.Value.Name ?? "";
                    viewModel.Positions[locale.Key] = locale.Value.Position ?? "";
                    viewModel.Biographies[locale.Key] = locale.Value.Biography ?? "";
                    viewModel.Messages[locale.Key] = locale.Value.Message ?? "";
                }

                return Json(new { success = true, data = viewModel });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取高管信息时发生错误: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadRecordError"] });
            }
        }

        /// <summary>
        /// 删除高管信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("executive/{id}")]
        public async Task<IActionResult> DeleteExecutive(string id)
        {
            try
            {
                var executive = await _executiveService.GetByIdAsync(id);
                if (executive == null)
                {
                    return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                }

                await _executiveService.DeleteAsync(id);
                return Json(new { success = true, message = _adminResource["DeleteExecutiveSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除高管信息时发生错误");
                return Json(new { success = false, message = _adminResource["DeleteExecutiveError"] });
            }
        }

        #endregion

        #region Organization Structure CRUD API Endpoints

        /// <summary>
        /// 保存组织架构信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("organization")]
        public async Task<IActionResult> SaveOrganization([FromBody] OrganizationItemViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                OrganizationStructure organization;
                if (string.IsNullOrEmpty(model.Id))
                {
                    // 新增
                    organization = new OrganizationStructure
                    {
                        ParentDepartmentId = model.ParentDepartmentId,
                        Level = model.Level,
                        DisplayOrder = model.DisplayOrder,
                        IsActive = model.IsActive,
                        Locale = new Dictionary<string, OrganizationLocaleFields>(),
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    // 添加多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        organization.Locale[lang.Code] = new OrganizationLocaleFields
                        {
                            DepartmentName = model.DepartmentNames.GetValueOrDefault(lang.Code, ""),
                            Description = model.DepartmentDescriptions.GetValueOrDefault(lang.Code, "")
                        };
                    }

                    await _organizationService.CreateAsync(organization);
                }
                else
                {
                    // 更新
                    organization = await _organizationService.GetByIdAsync(model.Id);
                    if (organization == null)
                    {
                        return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                    }

                    organization.ParentDepartmentId = model.ParentDepartmentId;
                    organization.Level = model.Level;
                    organization.DisplayOrder = model.DisplayOrder;
                    organization.IsActive = model.IsActive;
                    organization.UpdatedAt = DateTime.UtcNow;

                    // 更新多语言字段
                    foreach (var lang in SupportedLanguages)
                    {
                        if (!organization.Locale.ContainsKey(lang.Code))
                        {
                            organization.Locale[lang.Code] = new OrganizationLocaleFields();
                        }

                        organization.Locale[lang.Code].DepartmentName = model.DepartmentNames.GetValueOrDefault(lang.Code, "");
                        organization.Locale[lang.Code].Description = model.DepartmentDescriptions.GetValueOrDefault(lang.Code, "");
                    }

                    await _organizationService.UpdateAsync(organization.Id, organization);
                }

                return Json(new { success = true, message = _adminResource["SaveOrganizationSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存组织架构信息时发生错误");
                return Json(new { success = false, message = _adminResource["SaveOrganizationError"] });
            }
        }

        /// <summary>
        /// 获取组织架构信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("organization/{id}")]
        public async Task<IActionResult> GetOrganization(string id)
        {
            try
            {
                var organization = await _organizationService.GetByIdAsync(id);
                if (organization == null)
                {
                    return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                }

                var viewModel = new OrganizationItemViewModel
                {
                    Id = organization.Id,
                    ParentDepartmentId = organization.ParentDepartmentId,
                    Level = organization.Level,
                    DisplayOrder = organization.DisplayOrder,
                    IsActive = organization.IsActive,
                    DepartmentNames = new Dictionary<string, string>(),
                    DepartmentDescriptions = new Dictionary<string, string>()
                };

                // 映射多语言字段
                foreach (var locale in organization.Locale)
                {
                    viewModel.DepartmentNames[locale.Key] = locale.Value.DepartmentName ?? "";
                    viewModel.DepartmentDescriptions[locale.Key] = locale.Value.Description ?? "";
                }

                return Json(new { success = true, data = viewModel });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取组织架构信息时发生错误: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadRecordError"] });
            }
        }

        /// <summary>
        /// 删除组织架构信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("organization/{id}")]
        public async Task<IActionResult> DeleteOrganization(string id)
        {
            try
            {
                var organization = await _organizationService.GetByIdAsync(id);
                if (organization == null)
                {
                    return Json(new { success = false, message = _adminResource["RecordNotFound"] });
                }

                await _organizationService.DeleteAsync(id);
                return Json(new { success = true, message = _adminResource["DeleteOrganizationSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除组织架构信息时发生错误");
                return Json(new { success = false, message = _adminResource["DeleteOrganizationError"] });
            }
        }

        #endregion

        #region Contact Info API Endpoints

        /// <summary>
        /// 获取联系信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("contact-info")]
        public async Task<IActionResult> GetContactInfo()
        {
            try
            {
                var contactInfo = await GetContactInfoDataAsync();
                return Json(new { success = true, data = contactInfo });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取联系信息时发生错误");
                return Json(new { success = false, message = _adminResource["LoadContactInfoError"] });
            }
        }

        /// <summary>
        /// 保存联系信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("contact-info")]
        public async Task<IActionResult> SaveContactInfo([FromBody] ContactInfoDetailViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                var companies = await _companyService.GetAllAsync();
                var company = companies.FirstOrDefault();

                if (company == null)
                {
                    return Json(new { success = false, message = _adminResource["CompanyNotFound"] });
                }

                // Update contact info
                if (company.ContactInfo == null)
                {
                    company.ContactInfo = new Model.Entities.Common.ContactInfo();
                }

                company.ContactInfo.Phone = model.Phone;
                company.ContactInfo.Fax = model.Fax;
                company.ContactInfo.Email = model.Email;
                company.ContactInfo.Website = model.Website;
                company.ContactInfo.PostalCode = model.PostalCode;
                company.ContactInfo.MapLink = model.MapLink;

                //// Update location coordinates
                //if (model.Latitude.HasValue && model.Longitude.HasValue)
                //{
                //    if (company.ContactInfo.Location == null)
                //    {
                //        company.ContactInfo.Location = new Model.Entities.Common.GeoLocation();
                //    }
                //    company.ContactInfo.Location.Latitude = model.Latitude.Value;
                //    company.ContactInfo.Location.Longitude = model.Longitude.Value;
                //}

                // Update multilingual fields
                if (company.ContactInfo.Locale == null)
                {
                    company.ContactInfo.Locale = new Dictionary<string, Model.Entities.Common.ContactInfoLocaleFields>();
                }

                foreach (var lang in SupportedLanguages)
                {
                    if (!company.ContactInfo.Locale.ContainsKey(lang.Code))
                    {
                        company.ContactInfo.Locale[lang.Code] = new Model.Entities.Common.ContactInfoLocaleFields();
                    }

                    company.ContactInfo.Locale[lang.Code].Address = model.Addresses.GetValueOrDefault(lang.Code, "");
                    company.ContactInfo.Locale[lang.Code].BusinessHours = model.BusinessHours.GetValueOrDefault(lang.Code, "");
                    company.ContactInfo.Locale[lang.Code].AccessInfo = model.AccessInfo.GetValueOrDefault(lang.Code, "");
                }

                company.UpdatedAt = DateTime.UtcNow;
                await _companyService.UpdateAsync(company.Id, company);

                return Json(new { success = true, message = _adminResource["SaveContactInfoSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存联系信息时发生错误");
                return Json(new { success = false, message = _adminResource["SaveContactInfoError"] });
            }
        }

        /// <summary>
        /// 保存公司据点信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("location")]
        public async Task<IActionResult> SaveLocation([FromBody] CompanyLocationViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["ValidationError"] });
                }

                CompanyLocation location;
                if (string.IsNullOrEmpty(model.Id))
                {
                    // 创建新据点
                    location = new CompanyLocation
                    {
                        Type = model.Type,
                        IsPrimary = model.IsPrimary,
                        DisplayOrder = model.DisplayOrder,
                        IsActive = model.IsActive,
                        Locale = new Dictionary<string, Model.Entities.LocaleFields.CompanyLocationLocaleFields>(),
                        ContactInfo = new Model.Entities.Common.ContactInfo(),
                        CreatedAt = DateTime.UtcNow
                    };

                    // 设置多语言据点名称
                    foreach (var lang in SupportedLanguages)
                    {
                        location.Locale[lang.Code] = new Model.Entities.LocaleFields.CompanyLocationLocaleFields
                        {
                            LocationName = model.Names.GetValueOrDefault(lang.Code, "")
                        };
                    }

                    // 设置联系信息
                    if (model.ContactInfo != null)
                    {
                        location.ContactInfo.Phone = model.ContactInfo.Phone;
                        location.ContactInfo.Fax = model.ContactInfo.Fax;
                        location.ContactInfo.Email = model.ContactInfo.Email;
                        location.ContactInfo.Website = model.ContactInfo.Website;
                        location.ContactInfo.PostalCode = model.ContactInfo.PostalCode;
                        location.ContactInfo.MapLink = model.ContactInfo.MapLink;

                        //if (model.ContactInfo.Latitude.HasValue && model.ContactInfo.Longitude.HasValue)
                        //{
                        //    location.ContactInfo.Location = new Model.Entities.Common.GeoLocation
                        //    {
                        //        Latitude = model.ContactInfo.Latitude.Value,
                        //        Longitude = model.ContactInfo.Longitude.Value
                        //    };
                        //}

                        // 设置多语言联系信息
                        location.ContactInfo.Locale = new Dictionary<string, Model.Entities.Common.ContactInfoLocaleFields>();
                        foreach (var lang in SupportedLanguages)
                        {
                            location.ContactInfo.Locale[lang.Code] = new Model.Entities.Common.ContactInfoLocaleFields
                            {
                                Address = model.ContactInfo.Addresses.GetValueOrDefault(lang.Code, ""),
                                BusinessHours = model.ContactInfo.BusinessHours.GetValueOrDefault(lang.Code, ""),
                                AccessInfo = model.ContactInfo.AccessInfo.GetValueOrDefault(lang.Code, "")
                            };
                        }
                    }

                    await _companyLocationService.CreateAsync(location);
                }
                else
                {
                    // 更新现有据点
                    location = await _companyLocationService.GetByIdAsync(model.Id);
                    if (location == null)
                    {
                        return Json(new { success = false, message = _adminResource["LocationNotFound"] });
                    }

                    location.Type = model.Type;
                    location.IsPrimary = model.IsPrimary;
                    location.DisplayOrder = model.DisplayOrder;
                    location.IsActive = model.IsActive;

                    // 更新多语言据点名称
                    foreach (var lang in SupportedLanguages)
                    {
                        if (!location.Locale.ContainsKey(lang.Code))
                        {
                            location.Locale[lang.Code] = new Model.Entities.LocaleFields.CompanyLocationLocaleFields();
                        }
                        location.Locale[lang.Code].LocationName = model.Names.GetValueOrDefault(lang.Code, "");
                    }

                    // 更新联系信息
                    if (model.ContactInfo != null)
                    {
                        if (location.ContactInfo == null)
                        {
                            location.ContactInfo = new Model.Entities.Common.ContactInfo();
                        }

                        location.ContactInfo.Phone = model.ContactInfo.Phone;
                        location.ContactInfo.Fax = model.ContactInfo.Fax;
                        location.ContactInfo.Email = model.ContactInfo.Email;
                        location.ContactInfo.Website = model.ContactInfo.Website;
                        location.ContactInfo.PostalCode = model.ContactInfo.PostalCode;
                        location.ContactInfo.MapLink = model.ContactInfo.MapLink;
                        //if (model.ContactInfo.Latitude.HasValue && model.ContactInfo.Longitude.HasValue)
                        //{
                        //    if (location.ContactInfo.Location == null)
                        //    {
                        //        location.ContactInfo.Location = new Model.Entities.Common.GeoLocation();
                        //    }
                        //    location.ContactInfo.Location.Latitude = model.ContactInfo.Latitude.Value;
                        //    location.ContactInfo.Location.Longitude = model.ContactInfo.Longitude.Value;
                        //}

                        // 更新多语言联系信息
                        if (location.ContactInfo.Locale == null)
                        {
                            location.ContactInfo.Locale = new Dictionary<string, Model.Entities.Common.ContactInfoLocaleFields>();
                        }

                        foreach (var lang in SupportedLanguages)
                        {
                            if (!location.ContactInfo.Locale.ContainsKey(lang.Code))
                            {
                                location.ContactInfo.Locale[lang.Code] = new Model.Entities.Common.ContactInfoLocaleFields();
                            }

                            location.ContactInfo.Locale[lang.Code].Address = model.ContactInfo.Addresses.GetValueOrDefault(lang.Code, "");
                            location.ContactInfo.Locale[lang.Code].BusinessHours = model.ContactInfo.BusinessHours.GetValueOrDefault(lang.Code, "");
                            location.ContactInfo.Locale[lang.Code].AccessInfo = model.ContactInfo.AccessInfo.GetValueOrDefault(lang.Code, "");
                        }
                    }

                    await _companyLocationService.UpdateAsync(location.Id, location);
                }

                return Json(new { success = true, message = _adminResource["SaveLocationSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存公司据点信息时发生错误");
                return Json(new { success = false, message = _adminResource["SaveLocationError"] });
            }
        }

        /// <summary>
        /// 获取公司据点信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("location/{id}")]
        public async Task<IActionResult> GetLocation(string id)
        {
            try
            {
                var location = await _companyLocationService.GetByIdAsync(id);
                if (location == null)
                {
                    return Json(new { success = false, message = _adminResource["LocationNotFound"] });
                }

                var viewModel = new CompanyLocationViewModel
                {
                    Id = location.Id,
                    Type = location.Type,
                    IsPrimary = location.IsPrimary,
                    DisplayOrder = location.DisplayOrder,
                    IsActive = location.IsActive,
                    Names = new Dictionary<string, string>(),
                    ContactInfo = new ContactInfoDetailViewModel()
                };

                // 映射多语言据点名称
                foreach (var locale in location.Locale)
                {
                    viewModel.Names[locale.Key] = locale.Value.LocationName ?? "";
                }

                // 映射联系信息
                if (location.ContactInfo != null)
                {
                    viewModel.ContactInfo.Phone = location.ContactInfo.Phone;
                    viewModel.ContactInfo.Fax = location.ContactInfo.Fax;
                    viewModel.ContactInfo.Email = location.ContactInfo.Email;
                    viewModel.ContactInfo.Website = location.ContactInfo.Website;
                    viewModel.ContactInfo.PostalCode = location.ContactInfo.PostalCode;
                    viewModel.ContactInfo.MapLink = location.ContactInfo.MapLink;

                    //viewModel.ContactInfo.Latitude = location.ContactInfo.Location?.Latitude;
                    //viewModel.ContactInfo.Longitude = location.ContactInfo.Location?.Longitude;

                    if (location.ContactInfo.Locale != null)
                    {
                        foreach (var locale in location.ContactInfo.Locale)
                        {
                            viewModel.ContactInfo.Addresses[locale.Key] = locale.Value.Address ?? "";
                            viewModel.ContactInfo.BusinessHours[locale.Key] = locale.Value.BusinessHours ?? "";
                            viewModel.ContactInfo.AccessInfo[locale.Key] = locale.Value.AccessInfo ?? "";
                        }
                    }
                }

                return Json(new { success = true, data = viewModel });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取公司据点信息时发生错误: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadRecordError"] });
            }
        }

        /// <summary>
        /// 删除公司据点信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("location/{id}")]
        public async Task<IActionResult> DeleteLocation(string id)
        {
            try
            {
                var location = await _companyLocationService.GetByIdAsync(id);
                if (location == null)
                {
                    return Json(new { success = false, message = _adminResource["LocationNotFound"] });
                }

                await _companyLocationService.DeleteAsync(id);
                return Json(new { success = true, message = _adminResource["DeleteLocationSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除公司据点信息时发生错误");
                return Json(new { success = false, message = _adminResource["DeleteLocationError"] });
            }
        }

        #endregion

        // 映射方法
        private CompanyBasicInfoViewModel MapToBasicInfoViewModel(Company company)
        {
            var model = new CompanyBasicInfoViewModel
            {
                EstablishedDate = company.EstablishedDate,
                RegistrationNumber = company.RegistrationNumber,
                Capital = company.Capital,
                Currency = company.Currency,
                EmployeeScale = company.EmployeeScale,
                IsActive = company.IsActive
            };

            // 映射多语言字段
            foreach (var locale in company.Locale)
            {
                model.CompanyNames[locale.Key] = locale.Value.CompanyName ?? "";
                model.CompanyDescriptions[locale.Key] = locale.Value.CompanyDescription ?? "";
                model.Philosophy[locale.Key] = locale.Value.Mission ?? "";
            }

            return model;
        }

        private CompanyHistoryItemViewModel MapToHistoryViewModel(CompanyHistory history)
        {
            var model = new CompanyHistoryItemViewModel
            {
                Id = history.Id,
                EventDate = history.EventDate,
                EventType = history.EventType,
                ImageUrl = history.ImageUrl,
                DisplayOrder = history.DisplayOrder,
                IsActive = history.IsActive
            };

            // 映射多语言字段
            foreach (var locale in history.Locale)
            {
                model.EventTitles[locale.Key] = locale.Value.EventTitle ?? "";
                model.EventDescriptions[locale.Key] = locale.Value.EventDescription ?? "";
            }

            return model;
        }

        private ExecutiveItemViewModel MapToExecutiveViewModel(Executive executive)
        {
            var model = new ExecutiveItemViewModel
            {
                Id = executive.Id,
                PhotoUrl = executive.PhotoUrl,
                IsPresident = executive.IsPresident,
                DisplayOrder = executive.DisplayOrder,
                IsActive = executive.IsActive
            };

            // 映射多语言字段
            foreach (var locale in executive.Locale)
            {
                model.Names[locale.Key] = locale.Value.Name ?? "";
                model.Positions[locale.Key] = locale.Value.Position ?? "";
                model.Biographies[locale.Key] = locale.Value.Biography ?? "";
                model.Messages[locale.Key] = locale.Value.Message ?? "";
            }

            return model;
        }

        private OrganizationItemViewModel MapToOrganizationViewModel(OrganizationStructure organization)
        {
            var model = new OrganizationItemViewModel
            {
                Id = organization.Id,
                ParentDepartmentId = organization.ParentDepartmentId,
                Level = organization.Level,
                DisplayOrder = organization.DisplayOrder,
                IsActive = organization.IsActive
            };

            // 映射多语言字段
            foreach (var locale in organization.Locale)
            {
                model.DepartmentNames[locale.Key] = locale.Value.DepartmentName ?? "";
                model.DepartmentDescriptions[locale.Key] = locale.Value.Description ?? "";
            }

            return model;
        }

        private async Task<ContactInfoViewModel> MapToContactInfoViewModel(Company company)
        {
            var model = new ContactInfoViewModel();

            if (company.ContactInfo != null)
            {
                model.BasicContactInfo = new ContactInfoDetailViewModel
                {
                    Phone = company.ContactInfo.Phone,
                    Fax = company.ContactInfo.Fax,
                    Email = company.ContactInfo.Email,
                    Website = company.ContactInfo.Website,
                    PostalCode = company.ContactInfo.PostalCode,
                    MapLink = company.ContactInfo.MapLink
                    //Latitude = company.ContactInfo.Location?.Latitude,
                    //Longitude = company.ContactInfo.Location?.Longitude
                };

                // 映射多语言字段
                if (company.ContactInfo.Locale != null)
                {
                    foreach (var locale in company.ContactInfo.Locale)
                    {
                        model.BasicContactInfo.Addresses[locale.Key] = locale.Value.Address ?? "";
                        model.BasicContactInfo.BusinessHours[locale.Key] = locale.Value.BusinessHours ?? "";
                        model.BasicContactInfo.AccessInfo[locale.Key] = locale.Value.AccessInfo ?? "";
                    }
                }
            }

            //
            var locations = await _companyLocationService.FindAsync(x => true);

            // 映射据点信息
            foreach (var location in locations)
            {
                var locationModel = new CompanyLocationViewModel
                {
                    Id = location.Id,
                    Type = location.Type,
                    IsPrimary = location.IsPrimary,
                    DisplayOrder = location.DisplayOrder,
                    IsActive = location.IsActive
                };

                // 映射据点多语言字段
                foreach (var locale in location.Locale)
                {
                    locationModel.Names[locale.Key] = locale.Value.LocationName ?? "";
                }

                // 映射据点联系信息
                if (location.ContactInfo != null)
                {
                    locationModel.ContactInfo = new ContactInfoDetailViewModel
                    {
                        Phone = location.ContactInfo.Phone,
                        Fax = location.ContactInfo.Fax,
                        Email = location.ContactInfo.Email,
                        Website = location.ContactInfo.Website,
                        PostalCode = location.ContactInfo.PostalCode,
                        MapLink = location.ContactInfo.MapLink,
                        //Latitude = location.ContactInfo.Location?.Latitude,
                        //Longitude = location.ContactInfo.Location?.Longitude
                    };

                    if (location.ContactInfo.Locale != null)
                    {
                        foreach (var locale in location.ContactInfo.Locale)
                        {
                            locationModel.ContactInfo.Addresses[locale.Key] = locale.Value.Address ?? "";
                            locationModel.ContactInfo.BusinessHours[locale.Key] = locale.Value.BusinessHours ?? "";
                            locationModel.ContactInfo.AccessInfo[locale.Key] = locale.Value.AccessInfo ?? "";
                        }
                    }
                }

                model.Locations.Add(locationModel);
            }

            return model;
        }

        private CSRActivityItemViewModel MapToCSRActivityViewModel(CSRActivity csr)
        {
            var model = new CSRActivityItemViewModel
            {
                Id = csr.Id,
                Category = csr.Category,
                StartDate = csr.StartDate,
                EndDate = csr.EndDate,
                ImageUrls = csr.ImageUrls.ToList(),
                ReportFileUrl = csr.ReportFileUrl,
                IsActive = csr.IsActive
            };

            // 映射多语言字段
            foreach (var locale in csr.Locale)
            {
                model.Titles[locale.Key] = locale.Value.Title ?? "";
                model.Descriptions[locale.Key] = locale.Value.Description ?? "";
                model.Summaries[locale.Key] = locale.Value.Impact ?? "";
            }

            return model;
        }

        private FinancialReportItemViewModel MapToFinancialReportViewModel(FinancialReport report)
        {
            var model = new FinancialReportItemViewModel
            {
                Id = report.Id,
                Type = report.Type,
                Year = report.Year,
                Quarter = report.Quarter,
                ReportFileUrl = report.ReportFileUrl,
                PublishDate = report.PublishDate,
                Revenue = report.Revenue,
                NetIncome = report.NetIncome,
                TotalAssets = report.TotalAssets,
                Currency = report.Currency,
                IsPublished = report.IsPublished
            };

            // 映射多语言字段
            foreach (var locale in report.Locale)
            {
                model.Titles[locale.Key] = locale.Value.Title ?? "";
                model.Summaries[locale.Key] = locale.Value.Summary ?? "";
            }

            return model;
        }

        private ShareholderMeetingItemViewModel MapToShareholderMeetingViewModel(ShareholderMeeting meeting)
        {
            var model = new ShareholderMeetingItemViewModel
            {
                Id = meeting.Id,
                MeetingDate = meeting.MeetingDate,
                Status = meeting.Status
            };

            // 映射多语言字段
            foreach (var locale in meeting.Locale)
            {
                model.Titles[locale.Key] = locale.Value.MeetingTitle ?? "";
                model.Descriptions[locale.Key] = locale.Value.Agenda ?? "";
            }

            // 映射文档
            foreach (var document in meeting.Documents)
            {
                var docModel = new MeetingDocumentViewModel
                {
                    Type = document.Type,
                    FileUrl = document.FileUrl,
                    UploadDate = document.UploadDate
                };

                foreach (var locale in document.Locale)
                {
                    docModel.Titles[locale.Key] = locale.Value.DocumentName ?? "";
                    docModel.Descriptions[locale.Key] = "";
                }

                model.Documents.Add(docModel);
            }

            return model;
        }

        #region CSR Activities CRUD

        // GET: /admin/companyinfo/csr-activity/{id}
        [HttpGet("csr-activity/{id}")]
        public async Task<IActionResult> GetCSRActivity(string id)
        {
            try
            {
                var activity = await _csrActivityService.GetByIdAsync(id);
                if (activity == null)
                {
                    return Json(new { success = false, message = "CSR activity not found" });
                }

                var model = MapToCSRActivityViewModel(activity);
                return Json(new { success = true, data = model });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CSR activity: {Id}", id);
                return Json(new { success = false, message = "Failed to load CSR activity" });
            }
        }

        // POST: /admin/companyinfo/csr-activity
        [HttpPost("csr-activity")]
        public async Task<IActionResult> SaveCSRActivity([FromBody] CSRActivityViewModel model)
        {
            try
            {
                CSRActivity activity;

                if (string.IsNullOrEmpty(model.Id))
                {
                    // Create new activity
                    activity = new CSRActivity
                    {
                        Category = model.Category,
                        StartDate = model.StartDate,
                        EndDate = model.EndDate,
                        ImageUrls = model.ImageUrls ?? new List<string>(),
                        ReportFileUrl = model.ReportFileUrl,
                        IsActive = model.IsActive,
                        Locale = new Dictionary<string, CSRActivityLocaleFields>()
                    };

                    foreach (var lang in SupportedLanguages)
                    {
                        activity.Locale[lang.Code] = new CSRActivityLocaleFields
                        {
                            Title = model.Titles?.GetValueOrDefault(lang.Code, "") ?? "",
                            Description = model.Descriptions?.GetValueOrDefault(lang.Code, "") ?? "",
                            Impact = model.Impacts?.GetValueOrDefault(lang.Code, "") ?? ""
                        };
                    }

                    await _csrActivityService.CreateActivityAsync(activity);
                }
                else
                {
                    // Update existing activity
                    activity = await _csrActivityService.GetByIdAsync(model.Id);
                    if (activity == null)
                    {
                        return Json(new { success = false, message = "CSR activity not found" });
                    }

                    activity.Category = model.Category;
                    activity.StartDate = model.StartDate;
                    activity.EndDate = model.EndDate;
                    activity.ImageUrls = model.ImageUrls ?? new List<string>();
                    activity.ReportFileUrl = model.ReportFileUrl;
                    activity.IsActive = model.IsActive;

                    foreach (var lang in SupportedLanguages)
                    {
                        if (!activity.Locale.ContainsKey(lang.Code))
                        {
                            activity.Locale[lang.Code] = new CSRActivityLocaleFields();
                        }

                        activity.Locale[lang.Code].Title = model.Titles?.GetValueOrDefault(lang.Code, "") ?? "";
                        activity.Locale[lang.Code].Description = model.Descriptions?.GetValueOrDefault(lang.Code, "") ?? "";
                        activity.Locale[lang.Code].Impact = model.Impacts?.GetValueOrDefault(lang.Code, "") ?? "";
                    }

                    await _csrActivityService.UpdateActivityAsync(model.Id, activity);
                }

                return Json(new { success = true, message = "CSR activity saved successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving CSR activity");
                return Json(new { success = false, message = "Failed to save CSR activity" });
            }
        }

        // DELETE: /admin/companyinfo/csr-activity/{id}
        [HttpDelete("csr-activity/{id}")]
        public async Task<IActionResult> DeleteCSRActivity(string id)
        {
            try
            {
                var result = await _csrActivityService.DeleteAsync(id);
                if (result)
                {
                    return Json(new { success = true, message = "CSR activity deleted successfully" });
                }
                else
                {
                    return Json(new { success = false, message = "Failed to delete CSR activity" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting CSR activity: {Id}", id);
                return Json(new { success = false, message = "Failed to delete CSR activity" });
            }
        }

        #endregion

        #region IR Information CRUD

        // GET: /admin/companyinfo/financial-report/{id}
        [HttpGet("financial-report/{id}")]
        public async Task<IActionResult> GetFinancialReport(string id)
        {
            try
            {
                var report = await _financialReportService.GetByIdAsync(id);
                if (report == null)
                {
                    return Json(new { success = false, message = "Financial report not found" });
                }

                var model = MapToFinancialReportViewModel(report);
                return Json(new { success = true, data = model });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting financial report: {Id}", id);
                return Json(new { success = false, message = "Failed to load financial report" });
            }
        }

        // POST: /admin/companyinfo/financial-report
        [HttpPost("financial-report")]
        public async Task<IActionResult> SaveFinancialReport([FromBody] FinancialReportViewModel model)
        {
            try
            {
                FinancialReport report;

                if (string.IsNullOrEmpty(model.Id))
                {
                    // Create new report
                    report = new FinancialReport
                    {
                        Type = model.Type,
                        Year = model.Year,
                        Quarter = model.Quarter,
                        ReportFileUrl = model.ReportFileUrl,
                        PublishDate = model.PublishDate,
                        Revenue = model.Revenue,
                        NetIncome = model.NetIncome,
                        TotalAssets = model.TotalAssets,
                        Currency = model.Currency,
                        IsPublished = model.IsPublished,
                        Locale = new Dictionary<string, FinancialReportLocaleFields>()
                    };

                    foreach (var lang in SupportedLanguages)
                    {
                        report.Locale[lang.Code] = new FinancialReportLocaleFields
                        {
                            Title = model.Titles?.GetValueOrDefault(lang.Code, "") ?? "",
                            Summary = model.Summaries?.GetValueOrDefault(lang.Code, "") ?? ""
                        };
                    }

                    await _financialReportService.CreateReportAsync(report);
                }
                else
                {
                    // Update existing report
                    report = await _financialReportService.GetByIdAsync(model.Id);
                    if (report == null)
                    {
                        return Json(new { success = false, message = "Financial report not found" });
                    }

                    report.Type = model.Type;
                    report.Year = model.Year;
                    report.Quarter = model.Quarter;
                    report.ReportFileUrl = model.ReportFileUrl;
                    report.PublishDate = model.PublishDate;
                    report.Revenue = model.Revenue;
                    report.NetIncome = model.NetIncome;
                    report.TotalAssets = model.TotalAssets;
                    report.Currency = model.Currency;
                    report.IsPublished = model.IsPublished;

                    foreach (var lang in SupportedLanguages)
                    {
                        if (!report.Locale.ContainsKey(lang.Code))
                        {
                            report.Locale[lang.Code] = new FinancialReportLocaleFields();
                        }

                        report.Locale[lang.Code].Title = model.Titles?.GetValueOrDefault(lang.Code, "") ?? "";
                        report.Locale[lang.Code].Summary = model.Summaries?.GetValueOrDefault(lang.Code, "") ?? "";
                    }

                    await _financialReportService.UpdateReportAsync(model.Id, report);
                }

                return Json(new { success = true, message = "Financial report saved successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving financial report");
                return Json(new { success = false, message = "Failed to save financial report" });
            }
        }

        // DELETE: /admin/companyinfo/financial-report/{id}
        [HttpDelete("financial-report/{id}")]
        public async Task<IActionResult> DeleteFinancialReport(string id)
        {
            try
            {
                var result = await _financialReportService.DeleteAsync(id);
                if (result)
                {
                    return Json(new { success = true, message = "Financial report deleted successfully" });
                }
                else
                {
                    return Json(new { success = false, message = "Failed to delete financial report" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting financial report: {Id}", id);
                return Json(new { success = false, message = "Failed to delete financial report" });
            }
        }

        // GET: /admin/companyinfo/shareholder-meeting/{id}
        [HttpGet("shareholder-meeting/{id}")]
        public async Task<IActionResult> GetShareholderMeeting(string id)
        {
            try
            {
                var meeting = await _shareholderMeetingService.GetByIdAsync(id);
                if (meeting == null)
                {
                    return Json(new { success = false, message = "Shareholder meeting not found" });
                }

                var model = MapToShareholderMeetingViewModel(meeting);
                return Json(new { success = true, data = model });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting shareholder meeting: {Id}", id);
                return Json(new { success = false, message = "Failed to load shareholder meeting" });
            }
        }

        // POST: /admin/companyinfo/shareholder-meeting
        [HttpPost("shareholder-meeting")]
        public async Task<IActionResult> SaveShareholderMeeting([FromBody] ShareholderMeetingViewModel model)
        {
            try
            {
                ShareholderMeeting meeting;

                if (string.IsNullOrEmpty(model.Id))
                {
                    // Create new meeting
                    meeting = new ShareholderMeeting
                    {
                        MeetingDate = model.MeetingDate,
                        Status = model.Status,
                        Locale = new Dictionary<string, ShareholderMeetingLocaleFields>(),
                        Documents = new List<MeetingDocument>()
                    };

                    foreach (var lang in SupportedLanguages)
                    {
                        meeting.Locale[lang.Code] = new ShareholderMeetingLocaleFields
                        {
                            MeetingTitle = model.Titles?.GetValueOrDefault(lang.Code, "") ?? "",
                            Agenda = model.Agendas?.GetValueOrDefault(lang.Code, "") ?? "",
                            Location = model.Locations?.GetValueOrDefault(lang.Code, "") ?? ""
                        };
                    }

                    await _shareholderMeetingService.CreateMeetingAsync(meeting);
                }
                else
                {
                    // Update existing meeting
                    meeting = await _shareholderMeetingService.GetByIdAsync(model.Id);
                    if (meeting == null)
                    {
                        return Json(new { success = false, message = "Shareholder meeting not found" });
                    }

                    meeting.MeetingDate = model.MeetingDate;
                    meeting.Status = model.Status;

                    foreach (var lang in SupportedLanguages)
                    {
                        if (!meeting.Locale.ContainsKey(lang.Code))
                        {
                            meeting.Locale[lang.Code] = new ShareholderMeetingLocaleFields();
                        }

                        meeting.Locale[lang.Code].MeetingTitle = model.Titles?.GetValueOrDefault(lang.Code, "") ?? "";
                        meeting.Locale[lang.Code].Agenda = model.Agendas?.GetValueOrDefault(lang.Code, "") ?? "";
                        meeting.Locale[lang.Code].Location = model.Locations?.GetValueOrDefault(lang.Code, "") ?? "";
                    }

                    await _shareholderMeetingService.UpdateMeetingAsync(model.Id, meeting);
                }

                return Json(new { success = true, message = "Shareholder meeting saved successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving shareholder meeting");
                return Json(new { success = false, message = "Failed to save shareholder meeting" });
            }
        }

        // DELETE: /admin/companyinfo/shareholder-meeting/{id}
        [HttpDelete("shareholder-meeting/{id}")]
        public async Task<IActionResult> DeleteShareholderMeeting(string id)
        {
            try
            {
                var result = await _shareholderMeetingService.DeleteAsync(id);
                if (result)
                {
                    return Json(new { success = true, message = "Shareholder meeting deleted successfully" });
                }
                else
                {
                    return Json(new { success = false, message = "Failed to delete shareholder meeting" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting shareholder meeting: {Id}", id);
                return Json(new { success = false, message = "Failed to delete shareholder meeting" });
            }
        }

        #endregion
    }
}