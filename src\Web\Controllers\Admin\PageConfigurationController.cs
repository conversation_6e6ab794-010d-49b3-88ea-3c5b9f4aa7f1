using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Service.Pages;
using MlSoft.Sites.Service.Routing;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Admin
{

    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class PageConfigurationController : BaseController
    {
        private readonly PageConfigurationService _pageConfigService;
        private readonly IDynamicPageRouteService _routeService;
        private readonly IStringLocalizer<AdminResource> _localizer;

        private readonly IComponentConfigService _componentConfigService;

        public PageConfigurationController(
            PageConfigurationService pageConfigService,
            IDynamicPageRouteService routeService,
            IStringLocalizer<AdminResource> localizer,
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _pageConfigService = pageConfigService;
            _routeService = routeService;
            _localizer = localizer;
            _componentConfigService = componentConfigService;

        }

        /// <summary>
        /// 页面配置列表
        /// </summary>
        [HttpGet("")]
        public async Task<IActionResult> Index(int page = 1, int pageSize = 20, PageStatus? status = null, string? search = null)
        {
            try
            {
                var (pages, totalCount) = await _pageConfigService.GetPageListAsync(page, pageSize, status);

                var viewModel = new PageConfigurationListViewModel
                {
                    Pages = new List<PageConfigurationListItemViewModel>(),
                    TotalCount = totalCount,
                    CurrentPage = page,
                    PageSize = pageSize,
                    FilterStatus = status,
                    SearchKeyword = search
                };

                // 转换为视图模型并获取解密内容
                foreach (var pageConfig in pages)
                {
                    var (_, content) = await _pageConfigService.GetPageWithContentAsync(pageConfig.Id);

                    var listItem = new PageConfigurationListItemViewModel
                    {
                        Id = pageConfig.Id,
                        Name = pageConfig.Name,
                        PageKey = content?.PageKey ?? "",
                        Route = content?.Route ?? "",
                        Status = pageConfig.Status,
                        PublishDate = pageConfig.PublishDate,
                        UpdatedAt = pageConfig.UpdatedAt,
                        UpdatedBy = pageConfig.UpdatedBy,
                        ComponentCount = content?.Components.Count ?? 0
                    };

                    viewModel.Pages.Add(listItem);
                }

                return View("~/Views/Admin/PageConfiguration/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = _localizer["LoadPageListError"].ToString();
                return View(new PageConfigurationListViewModel());
            }
        }

        /// <summary>
        /// 新增页面表单
        /// </summary>
        [HttpGet("Create")]
        public async Task<IActionResult> Create()
        {
            var viewModel = new PageConfigurationEditViewModel();
            await LoadEditViewModelData(viewModel);

            // 设置默认值
            viewModel.Name = _supportedLanguages.ToDictionary(x => x.Code, x => "");

            return ReturnEditView(viewModel);
        }

        /// <summary>
        /// 提交新增页面
        /// </summary>
        [HttpPost("Create")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PageConfigurationEditViewModel model)
        {
            if (!ModelState.IsValid)
            {
                await LoadEditViewModelData(model);
                return ReturnEditView(model);
            }

            try
            {
                // 检查PageKey唯一性
                if (!await _pageConfigService.IsPageKeyUniqueAsync(model.PageKey))
                {
                    ModelState.AddModelError(nameof(model.PageKey), _localizer["PageKeyAlreadyExists"].ToString());
                    await LoadEditViewModelData(model);
                    return ReturnEditView(model);
                }

                // 检查路由唯一性
                if (!await _pageConfigService.IsRouteUniqueAsync(model.Route))
                {
                    ModelState.AddModelError(nameof(model.Route), _localizer["RouteAlreadyExists"].ToString());
                    await LoadEditViewModelData(model);
                    return ReturnEditView(model);
                }

                var content = model.ToPageConfigContent();
                var createdBy = User.Identity?.Name ?? "System";

                await _pageConfigService.CreatePageAsync(model.Name, content, createdBy);

                TempData["SuccessMessage"] = _localizer["PageCreateSuccess"].ToString();
                return RedirectToIndexWithLanguage();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", _localizer["PageCreateError"].ToString());
                await LoadEditViewModelData(model);
                return ReturnEditView(model);
            }
        }

        /// <summary>
        /// 编辑页面表单
        /// </summary>
        [HttpGet("Edit/{id}")]
        public async Task<IActionResult> Edit(string id)
        {
            if (string.IsNullOrEmpty(id))
                return NotFound();

            try
            {
                var (page, content) = await _pageConfigService.GetPageWithContentAsync(id);

                if (page == null || content == null)
                    return NotFound();

                var viewModel = new PageConfigurationEditViewModel
                {
                    Id = page.Id,
                    Name = page.Name,
                    Status = page.Status,
                    PublishDate = page.PublishDate
                };

                viewModel.LoadFromPageConfigContent(content);
                await LoadEditViewModelData(viewModel);

                return ReturnEditView(viewModel);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = _localizer["LoadPageError"].ToString();
                return RedirectToIndexWithLanguage();
            }
        }

        /// <summary>
        /// 提交编辑页面
        /// </summary>
        [HttpPost("Edit/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string id, PageConfigurationEditViewModel model)
        {
            if (string.IsNullOrEmpty(id) || model.Id != id)
                return NotFound();

            if (!ModelState.IsValid)
            {
                await LoadEditViewModelData(model);
                return ReturnEditView(model);
            }

            try
            {
                // 检查PageKey唯一性（排除当前页面）
                if (!await _pageConfigService.IsPageKeyUniqueAsync(model.PageKey, id))
                {
                    ModelState.AddModelError(nameof(model.PageKey), _localizer["PageKeyAlreadyExists"].ToString());
                    await LoadEditViewModelData(model);
                    return ReturnEditView(model);
                }

                // 检查路由唯一性（排除当前页面）
                if (!await _pageConfigService.IsRouteUniqueAsync(model.Route, id))
                {
                    ModelState.AddModelError(nameof(model.Route), _localizer["RouteAlreadyExists"].ToString());
                    await LoadEditViewModelData(model);
                    return ReturnEditView(model);
                }

                var content = model.ToPageConfigContent();
                var updatedBy = User.Identity?.Name ?? "System";

                

                var success = await _pageConfigService.UpdatePageAsync(id, model.Name, content, updatedBy);

                if (success)
                {
                    // 如果页面已发布，刷新路由
                    var (page, _) = await _pageConfigService.GetPageWithContentAsync(id);
                    if (page?.Status == PageStatus.Published)
                    {
                        await _routeService.RefreshSinglePageRouteAsync(id);
                    }

                    TempData["SuccessMessage"] = _localizer["PageUpdateSuccess"].ToString();
                    return RedirectToIndexWithLanguage();
                }
                else
                {
                    ModelState.AddModelError("", _localizer["PageUpdateError"].ToString());
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", _localizer["PageUpdateError"]);
            }

            await LoadEditViewModelData(model);
            return View("~/Views/Admin/PageConfiguration/Edit.cshtml", model);
        }

        /// <summary>
        /// 删除页面 (Ajax)
        /// </summary>
        [HttpPost("Delete/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(string id)
        {
            if (string.IsNullOrEmpty(id))
                return Json(new { success = false, message = "Invalid page ID" });

            try
            {
                var success = await _pageConfigService.DeletePageAsync(id);

                if (success)
                {
                    // 移除该页面的路由
                    await _routeService.RemovePageRouteAsync(id);

                    return Json(new
                    {
                        success = true,
                        message = _localizer["PageDeleteSuccess"].ToString()
                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = _localizer["PageDeleteError"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = _localizer["PageDeleteError"].ToString()
                });
            }
        }

        /// <summary>
        /// 发布页面 (Ajax)
        /// </summary>
        [HttpPost("Publish/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Publish(string id)
        {
            if (string.IsNullOrEmpty(id))
                return Json(new { success = false, message = "Invalid page ID" });

            try
            {
                var success = await _pageConfigService.PublishPageAsync(id);

                if (success)
                {
                    // 刷新该页面的路由
                    await _routeService.RefreshSinglePageRouteAsync(id);

                    return Json(new
                    {
                        success = true,
                        message = _localizer["PagePublishSuccess"].ToString()
                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = _localizer["PagePublishError"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = _localizer["PagePublishError"].ToString()
                });
            }
        }

        /// <summary>
        /// 取消发布页面 (Ajax)
        /// </summary>
        [HttpPost("Unpublish/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Unpublish(string id)
        {
            if (string.IsNullOrEmpty(id))
                return Json(new { success = false, message = "Invalid page ID" });

            try
            {
                var success = await _pageConfigService.UnpublishPageAsync(id);

                if (success)
                {
                    // 移除该页面的路由
                    await _routeService.RemovePageRouteAsync(id);

                    return Json(new
                    {
                        success = true,
                        message = _localizer["PageUnpublishSuccess"].ToString()
                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = _localizer["PageUnpublishError"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = _localizer["PageUnpublishError"].ToString()
                });
            }
        }

        /// <summary>
        /// 获取组件配置数据（AJAX）
        /// </summary>
        [HttpGet("GetComponentData/{componentId}")]
        public async Task<IActionResult> GetComponentData(string componentId)
        {
            try
            {
                var component = await _componentConfigService.GetComponent(componentId);


                if (component == null)
                    return NotFound();

                var result = new
                {
                    id = componentId,
                    name = component.Names[_currentLanguage],
                    description = component.Descriptions[_currentLanguage],
                    parameterSchema = "{}",
                    availableTemplates = component.Variants.Select(v => v.Id).ToList(),
                    defaultTemplate = component.Variants.FirstOrDefault()?.Id ?? "Default"
                };

                return Json(result);
            }
            catch (Exception ex)
            {
                return BadRequest(_localizer["LoadComponentDataError"].ToString());
            }
        }

        /// <summary>
        /// 获取指定组件的所有模板（AJAX）
        /// </summary>
        [HttpGet("GetComponentTemplates/{componentId}")]
        public async Task<IActionResult> GetComponentTemplates(string componentId)
        {
            try
            {
                var component = await _componentConfigService.GetComponent(componentId);


                if (component == null)
                    return NotFound();


                var templates = component.Variants.Select(v => new
                {
                    id = v.Id,
                    name = v.Names[_currentLanguage],
                    description = v.Names[_currentLanguage]
                }).ToList();

                return Json(new { success = true, templates = templates });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = _localizer["LoadComponentDataError"].ToString() });
            }
        }

        /// <summary>
        /// 加载编辑视图模型的辅助数据
        /// </summary>
        private async Task LoadEditViewModelData(PageConfigurationEditViewModel model)
        {
            try
            {
                // 加载可用组件
                var allComponentVariants = await _componentConfigService.GetAllComponents();

                var excludeComponents = new List<string>();// { "Header", "Footer", "SEO" };

                model.AvailableComponents = allComponentVariants.Where(x => !excludeComponents.Contains(x.Id)).Select(kvp => new ComponentDefinitionViewModel
                {
                    Id = kvp.Id,
                    Name = kvp.Names[_currentLanguage],
                    Description = kvp.Descriptions[_currentLanguage],
                    Category = _localizer["GeneralCategory"],
                    AvailableTemplates = kvp.Variants.Select(v => v.Id).ToList(),
                    DefaultTemplate = kvp.Variants.FirstOrDefault()?.Id ?? "Default",
                    ParameterSchema = "{}",
                    IconCssClass = "fas fa-cube"
                }).ToList();

                // 加载可用布局模板
                model.AvailableLayouts = new List<string> { "_Layout" };
            }
            catch (Exception ex)
            {
                // 如果加载失败，使用默认值
                model.AvailableComponents = new List<ComponentDefinitionViewModel>();
                model.AvailableLayouts = new List<string> { "_Layout" };
            }
        }

        /// <summary>
        /// 返回编辑视图，统一处理多语言
        /// </summary>
        private IActionResult ReturnEditView(PageConfigurationEditViewModel model)
        {
            return View("~/Views/Admin/PageConfiguration/Edit.cshtml", model);
        }

        /// <summary>
        /// 重定向到Index，支持多语言
        /// </summary>
        private IActionResult RedirectToIndexWithLanguage()
        {
            if (_currentLanguage != _defaultLanguage)
            {
                return Redirect($"/{_currentLanguage}/Admin/PageConfiguration");
            }
            return RedirectToAction(nameof(Index));
        }
    }

}