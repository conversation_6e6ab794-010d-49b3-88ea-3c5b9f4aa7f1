using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Service.Pages;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class PageManageController : BaseController
    {
        private readonly PageManageService _pageManageService;
        private readonly PageConfigurationService _pageConfigService;
        private readonly IComponentConfigService _componentConfigService;
        private readonly IStringLocalizer<AdminResource> _localizer;

        public PageManageController(
            PageManageService pageManageService,
            PageConfigurationService pageConfigService,
            IComponentConfigService componentConfigService,
            IStringLocalizer<AdminResource> localizer,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _pageManageService = pageManageService;
            _pageConfigService = pageConfigService;
            _componentConfigService = componentConfigService;
            _localizer = localizer;
        }

        /// <summary>
        /// 网页内容管理列表 - 显示所有已配置的页面
        /// </summary>
        [HttpGet("")]
        public async Task<IActionResult> Index(int page = 1, int pageSize = 20, PageContentStatus? status = null, string? search = null)
        {
            try
            {
                // 获取所有已配置的页面
                var (pageConfigurations, totalCount) = await _pageConfigService.GetPageListAsync(page, pageSize);

                var viewModel = new PageManageListViewModel
                {
                    Contents = new List<PageManageListItemViewModel>(),
                    TotalCount = totalCount,
                    CurrentPage = page,
                    PageSize = pageSize,
                    FilterStatus = status,
                    SearchKeyword = search
                };

                // 转换为视图模型
                foreach (var pageConfig in pageConfigurations)
                {
                    // 获取页面配置的详细内容
                    var (_, pageConfigContent) = await _pageConfigService.GetPageWithContentAsync(pageConfig.Id);
                    if (pageConfigContent == null)
                        continue; // 跳过没有内容配置的页面

                    // 检查是否已有对应的内容数据
                    var existingContent = await _pageManageService.GetPageContentAsync(pageConfig.Id);

                    var listItem = new PageManageListItemViewModel
                    {
                        Id = existingContent?.Id ?? string.Empty,
                        PageConfigurationId = pageConfig.Id,
                        PageKey = pageConfigContent.PageKey,
                        PageName = pageConfig.Name,
                        Status = existingContent?.Status ?? PageContentStatus.Draft,
                        PublishDate = existingContent?.PublishDate,
                        Version = existingContent?.Version ?? 1,
                        ComponentCount = pageConfigContent.Components?.Count ?? 0,
                        UpdatedAt = existingContent?.UpdatedAt ?? pageConfig.UpdatedAt,
                        UpdatedBy = existingContent?.UpdatedBy ?? pageConfig.UpdatedBy,
                        HasContent = existingContent != null // 新增字段，标识是否已有内容
                    };

                    // 如果有状态筛选条件，只显示匹配的项
                    if (status == null || listItem.Status == status)
                    {
                        viewModel.Contents.Add(listItem);
                    }
                }

                // 如果有状态筛选，重新计算总数
                if (status != null)
                {
                    viewModel.TotalCount = viewModel.Contents.Count;
                }

                return View("~/Views/Admin/PageManage/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = _localizer["LoadPageContentListError"].ToString();
                return View(new PageManageListViewModel());
            }
        }

        /// <summary>
        /// 编辑页面内容表单
        /// </summary>
        [HttpGet("Edit/{pageConfigurationId}")]
        public async Task<IActionResult> Edit(string pageConfigurationId)
        {
            if (string.IsNullOrEmpty(pageConfigurationId))
                return NotFound();

            try
            {
                // 获取页面配置
                var (pageConfig, pageConfigContent) = await _pageConfigService.GetPageWithContentAsync(pageConfigurationId);
                if (pageConfig == null || pageConfigContent == null)
                    return NotFound();

                // 获取或创建页面内容
                var pageContent = await _pageManageService.GetPageContentAsync(pageConfigurationId);

                var viewModel = new PageManageEditViewModel
                {
                    PageConfigurationId = pageConfigurationId,
                    PageKey = pageConfigContent.PageKey,
                    PageName = pageConfig.Name,
                    Status = pageContent?.Status ?? PageContentStatus.Draft,
                    PublishDate = pageContent?.PublishDate,
                    Version = pageContent?.Version ?? 1,
                    CreatedAt = pageContent?.CreatedAt ?? DateTime.UtcNow,
                    UpdatedAt = pageContent?.UpdatedAt ?? DateTime.UtcNow,
                    CreatedBy = pageContent?.CreatedBy,
                    UpdatedBy = pageContent?.UpdatedBy,
                    SupportedLanguages = _supportedLanguages.Select(l => l.Code).ToList()
                };

                if (pageContent != null)
                {
                    viewModel.Id = pageContent.Id;
                }

                // 加载组件数据
                await LoadComponentDataAsync(viewModel, pageConfigContent, pageContent?.ComponentsData ?? new List<ComponentData>());

                return View("~/Views/Admin/PageManage/Edit.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = _localizer["LoadPageContentError"].ToString();
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// 提交编辑页面内容
        /// </summary>
        [HttpPost("Edit/{pageConfigurationId}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string pageConfigurationId, PageManageEditViewModel model)
        {
            if (string.IsNullOrEmpty(pageConfigurationId) || model.PageConfigurationId != pageConfigurationId)
                return NotFound();

            try
            {
                var componentsData = new List<ComponentData>();
                var now = DateTime.UtcNow;

                foreach (var componentViewModel in model.ComponentsData)
                {
                    var componentData = new ComponentData
                    {
                        ComponentDefinitionId = componentViewModel.ComponentDefinitionId,
                        TemplateKey = componentViewModel.TemplateKey,
                        DisplayOrder = componentViewModel.DisplayOrder,
                        DataJson = componentViewModel.DataJson,
                        IsVisible = componentViewModel.IsVisible,
                        IdNo = componentViewModel.IdNo,
                        LastModified = now
                    };

                    componentsData.Add(componentData);
                }

                var updatedBy = User.Identity?.Name ?? "System";
                var success = await _pageManageService.SavePageContentAsync(
                    pageConfigurationId, model.PageKey, componentsData, updatedBy);

                if (success)
                {
                    // 检查是否是AJAX请求
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        return Json(new
                        {
                            success = true,
                            message = _localizer["PageContentSaveSuccess"].ToString()
                        });
                    }
                    else
                    {
                        TempData["SuccessMessage"] = _localizer["PageContentSaveSuccess"].ToString();
                        return RedirectToAction(nameof(Index));
                    }
                }
                else
                {
                    // AJAX请求返回错误JSON
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        return Json(new
                        {
                            success = false,
                            message = _localizer["PageContentSaveError"].ToString()
                        });
                    }
                    else
                    {
                        ModelState.AddModelError("", _localizer["PageContentSaveError"].ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常
                Console.WriteLine($"Error saving page content: {ex.Message}");

                // AJAX请求返回错误JSON
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new
                    {
                        success = false,
                        message = _localizer["PageContentSaveError"].ToString()
                    });
                }
                else
                {
                    ModelState.AddModelError("", _localizer["PageContentSaveError"].ToString());
                }
            }

            // 非AJAX请求的错误处理：重新加载数据并返回视图
            var (pageConfigReload, pageConfigContentReload) = await _pageConfigService.GetPageWithContentAsync(pageConfigurationId);
            if (pageConfigReload != null && pageConfigContentReload != null)
            {
                await LoadComponentDataAsync(model, pageConfigContentReload, new List<ComponentData>());
            }

            return View("~/Views/Admin/PageManage/Edit.cshtml", model);
        }

        /// <summary>
        /// 发布页面内容 (Ajax)
        /// </summary>
        [HttpPost("Publish/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Publish(string id)
        {
            if (string.IsNullOrEmpty(id))
                return Json(new { success = false, message = _localizer["InvalidContentId"].ToString() });

            try
            {
                var success = await _pageManageService.PublishPageContentAsync(id);

                if (success)
                {
                    return Json(new
                    {
                        success = true,
                        message = _localizer["PageContentPublishSuccess"].ToString()
                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = _localizer["PageContentPublishError"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = _localizer["PageContentPublishError"].ToString()
                });
            }
        }

        /// <summary>
        /// 取消发布页面内容 (Ajax)
        /// </summary>
        [HttpPost("Unpublish/{id}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Unpublish(string id)
        {
            if (string.IsNullOrEmpty(id))
                return Json(new { success = false, message = _localizer["InvalidContentId"].ToString() });

            try
            {
                var success = await _pageManageService.UnpublishPageContentAsync(id);

                if (success)
                {
                    return Json(new
                    {
                        success = true,
                        message = _localizer["PageContentUnpublishSuccess"].ToString()
                    });
                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = _localizer["PageContentUnpublishError"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = _localizer["PageContentUnpublishError"].ToString()
                });
            }
        }

        /// <summary>
        /// 获取组件的表单字段配置 (Ajax)
        /// </summary>
        [HttpGet("GetComponentFormFields/{componentId}/variants/{variantId}")]
        public async Task<IActionResult> GetComponentFormFields(string componentId, string variantId)
        {
            try
            {
                ComponentVariant? variant = null;
                var component = await _componentConfigService.GetComponent(componentId);
                if (component != null && component.Variants != null)
                {
                    variant = component.Variants.FirstOrDefault(x => x.Id == variantId);
                }
                if (variant != null)
                {
                    return Json(new { success = true, formFields = variant.FormFields });
                }

            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = _localizer["FailedToLoadFormFields"].ToString() });
            }

            return Json(new { success = false, message = _localizer["FailedToLoadFormFields"].ToString() });
        }

        ///// <summary>
        ///// 获取组件的多语言字段配置 (Ajax)
        ///// </summary>
        //[HttpGet("GetComponentMultilingualFields/{componentId}/variants/{variantId}")]
        //public IActionResult GetComponentMultilingualFields(string componentId)
        //{
        //    try
        //    {
        //        ComponentVariant? variant = null;
        //        var component = await _componentConfigService.GetComponent(componentId);
        //        if (component != null && component.Variants != null)
        //        {
        //            variant = component.Variants.FirstOrDefault(x => x.Id == variantId);
        //        }

        //        var multilingualFields = _componentVariantService.GetComponentMultilingualFields(componentId);
        //        return Json(new { success = true, multilingualFields = multilingualFields });
        //    }
        //    catch (Exception ex)
        //    {
        //        return Json(new { success = false, message = _localizer["FailedToLoadMultilingualFields"].ToString() });
        //    }
        //}

        /// <summary>
        /// 加载组件数据到视图模型
        /// </summary>
        private async Task LoadComponentDataAsync(PageManageEditViewModel viewModel, PageConfigContent pageConfigContent, List<ComponentData> existingComponentsData)
        {
            viewModel.ComponentsData.Clear();

            foreach (var configComponent in pageConfigContent.Components.OrderBy(c => c.DisplayOrder))
            {
                // 查找对应的内容数据
                var existingData = existingComponentsData.FirstOrDefault(d =>
                    d.ComponentDefinitionId == configComponent.ComponentDefinitionId  && d.IdNo == configComponent.IdNo);

                var componentDataViewModel = new ComponentDataViewModel
                {
                    ComponentDefinitionId = configComponent.ComponentDefinitionId,
                    TemplateKey = configComponent.TemplateKey,
                    DisplayOrder = configComponent.DisplayOrder,
                    IsVisible = existingData?.IsVisible ?? true,
                    IdNo = !string.IsNullOrEmpty(existingData?.IdNo) ? existingData.IdNo : configComponent.IdNo,
                    DataJson = existingData?.DataJson ?? "{}",
                    MultilingualFields = await _componentConfigService.GetComponentMultilingualFields(configComponent.ComponentDefinitionId, configComponent.TemplateKey),
                    LastModified = existingData?.LastModified ?? DateTime.UtcNow
                };

                // 获取组件的可用模板
                var component = await _componentConfigService.GetComponent(configComponent.ComponentDefinitionId);
                componentDataViewModel.AvailableTemplates = component.Variants.Select(v => new ComponentVariantInfo
                {
                    Id = v.Id,
                    Names = v.Names,
                    Descriptions = v.Descriptions
                }).ToList();

                viewModel.ComponentsData.Add(componentDataViewModel);
            }
        }
    }
}