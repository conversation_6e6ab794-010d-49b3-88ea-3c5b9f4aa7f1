using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Recruitment;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Entities.Common;
using MlSoft.Sites.Service.Recruitment;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MlSoft.Sites.Service.Organization;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class RecruitmentController : BaseController
    {
        private readonly ILogger<RecruitmentController> _logger;
        private readonly IViewRenderService _viewRenderService;
        private readonly JobPositionService _jobPositionService;
        private readonly EmployeeInterviewService _employeeInterviewService;
        private readonly OrganizationStructureService _organizationStructureService;
        private readonly AdminResource _adminResource;

        public RecruitmentController(
            ILogger<RecruitmentController> logger,
            IViewRenderService viewRenderService,
            JobPositionService jobPositionService,
            EmployeeInterviewService employeeInterviewService,
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            OrganizationStructureService organizationStructureService,
        IConfiguration configuration,
            AdminResource adminResource)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _logger = logger;
            _viewRenderService = viewRenderService;
            _jobPositionService = jobPositionService;
            _employeeInterviewService = employeeInterviewService;
            _adminResource = adminResource;
            _organizationStructureService = organizationStructureService;
        }

        // 主页面 - 只加载基本信息和第一个标签页数据
        public async Task<IActionResult> Index(int page = 1, int pageSize = 10)
        {
            try
            {
                // 获取分页的职位数据
                var jobs = await _jobPositionService.GetJobsList(page, pageSize);
                var totalCount = await _jobPositionService.GetJobsCountAsync();

                var jobViewModels = jobs.Select(j => new JobPositionViewModel
                {
                    Id = j.Id,
                    JobTitle = j.Locale.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString())?.JobTitle ?? "",
                    Department = j.DepartmentId ?? "",
                    Type = j.Type.ToString(),
                    EmploymentType = j.EmploymentType.ToString(),
                    ExperienceLevel = j.ExperienceLevel.ToString(),
                    PostDate = j.PostDate,
                    ApplicationDeadline = j.ApplicationDeadline,
                    IsActive = j.IsActive,
                    IsFeatured = j.IsFeatured,
                }).ToList();

                var viewModel = new RecruitmentIndexViewModel
                {
                    Jobs = new PagedResult<JobPositionViewModel>()
                    {
                        Items = jobViewModels,
                        Page = page,
                        Total = (int)totalCount,
                        PageSize = pageSize
                    },
                    AvailableJobTypes = Enum.GetValues<JobType>().ToList(),
                    AvailableEmploymentTypes = Enum.GetValues<EmploymentType>().ToList(),
                    AvailableExperienceLevels = Enum.GetValues<ExperienceLevel>().ToList(),
                    AvailableInterviewTypes = Enum.GetValues<InterviewType>().ToList()
                };

                return View("~/Views/Admin/Recruitment/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading recruitment management page");
                TempData["ErrorMessage"] = _adminResource["LoadPageError"];
                return RedirectToAction("Index", "Dashboard");
            }
        }

        // 懒加载 API 端点
        [HttpGet("tab/{tabName}")]
        public async Task<IActionResult> GetTabContent(string tabName)
        {
            try
            {
                var partialViewName = GetPartialViewName(tabName);
                var model = await GetTabDataAsync(tabName);


                var html = await _viewRenderService.RenderPartialViewAsync(
                    partialViewName, model, ViewData);

                return Json(new { success = true, html = html });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading tab content: {TabName}", tabName);
                return Json(new
                {
                    success = false,
                    message = _adminResource["LoadTabError"]
                });
            }
        }

        // 标签页名称映射
        private string GetPartialViewName(string tabName)
        {
            return tabName switch
            {
                "job-positions" => "Admin/Recruitment/Partials/_JobPositionsTab",
                "employee-interviews" => "Admin/Recruitment/Partials/_EmployeeInterviewsTab",
                "statistics" => "Admin/Recruitment/Partials/_StatisticsTab",
                _ => throw new ArgumentException($"Invalid tab name: {tabName}")
            };
        }

        // 标签页数据获取
        private async Task<object> GetTabDataAsync(string tabName)
        {
            return tabName switch
            {
                "job-positions" => await GetJobPositionsTabDataAsync(),
                "employee-interviews" => await GetEmployeeInterviewsTabDataAsync(),
                _ => null
            };
        }

        private async Task<JobPositionsTabViewModel> GetJobPositionsTabDataAsync()
        {
            var jobs = await _jobPositionService.GetAllAsync();

            return new JobPositionsTabViewModel
            {
                Jobs = jobs.Select(j => new JobPositionViewModel
                {
                    Id = j.Id,
                    JobTitle = j.Locale.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString())?.JobTitle ?? "",
                    Department = j.DepartmentId ?? "",
                    Type = j.Type.ToString(),
                    EmploymentType = j.EmploymentType.ToString(),
                    ExperienceLevel = j.ExperienceLevel.ToString(),
                    PostDate = j.PostDate,
                    ApplicationDeadline = j.ApplicationDeadline,
                    IsActive = j.IsActive,
                    IsFeatured = j.IsFeatured,
                    SalaryRange = j.SalaryMin.HasValue && j.SalaryMax.HasValue
                        ? $"{j.SalaryMin} - {j.SalaryMax} {j.Currency}"
                        : "",
                    ApplicationCount = j.ApplicationCount,
                    WorkingHours = j.WorkingHours
                }).ToList()
            };
        }

        private async Task<EmployeeInterviewsTabViewModel> GetEmployeeInterviewsTabDataAsync()
        {
            var interviews = await _employeeInterviewService.GetAllAsync();

            var organs = await _organizationStructureService.GetTreeOfOrgan(_currentLanguage);

            return new EmployeeInterviewsTabViewModel
            {
                Interviews = interviews.Select(i => new EmployeeInterviewViewModel
                {
                    Id = i.Id,
                    EmployeeName = i.Locale.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString())?.EmployeeName ?? "",
                    Position = i.Locale.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString())?.Position ?? "",
                    YearsOfService = i.YearsOfService,
                    InterviewDate = i.InterviewDate,
                    PhotoUrl = i.PhotoUrl,
                    DisplayOrder = i.DisplayOrder,
                    IsFeatured = i.IsFeatured,
                    Status = i.Status.ToString(),
                    DepartmentId = i.DepartmentId,
                    InterviewType = i.InterviewType.ToString()
                }).ToList(),
                Organs = organs
            };
        }

        // CRUD操作 APIs

        [HttpPost("job-position")]
        public async Task<IActionResult> CreateJobPosition([FromBody] JobPositionCreateRequest request)
        {
            try
            {
                var job = new JobPosition
                {
                    Type = request.Type,
                    EmploymentType = request.EmploymentType,
                    ExperienceLevel = request.ExperienceLevel,
                    SalaryMin = request.SalaryMin,
                    SalaryMax = request.SalaryMax,
                    Currency = request.Currency,
                    PostDate = request.PostDate,
                    ApplicationDeadline = request.ApplicationDeadline,
                    IsActive = request.IsActive,
                    IsFeatured = request.IsFeatured,
                    DepartmentId = request.DepartmentId,
                    WorkingHours = request.WorkingHours,
                    ProbationPeriod = request.ProbationPeriod,
                    Locale = request.Locale ?? new Dictionary<string, JobPositionLocaleFields>()
                };

                var createdJob = await _jobPositionService.CreateJobAsync(job);
                return Json(new { success = true, data = createdJob });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating job position");
                return Json(new { success = false, message = _adminResource["CreateJobError"] });
            }
        }

        [HttpGet("job-position/{id}")]
        public async Task<IActionResult> GetJobPosition(string id)
        {
            try
            {
                var job = await _jobPositionService.GetByIdAsync(id);
                if (job == null)
                {
                    return Json(new { success = false, message = _adminResource["JobNotFound"] });
                }

                return Json(new { success = true, data = job });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting job position: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadJobError"] });
            }
        }

        [HttpPut("job-position/{id}")]
        public async Task<IActionResult> UpdateJobPosition(string id, [FromBody] JobPositionUpdateRequest request)
        {
            try
            {
                var job = await _jobPositionService.GetByIdAsync(id);
                if (job == null)
                {
                    return Json(new { success = false, message = _adminResource["JobNotFound"] });
                }

                // 更新字段
                job.Type = request.Type;
                job.EmploymentType = request.EmploymentType;
                job.ExperienceLevel = request.ExperienceLevel;
                job.SalaryMin = request.SalaryMin;
                job.SalaryMax = request.SalaryMax;
                job.Currency = request.Currency;
                job.PostDate = request.PostDate;
                job.ApplicationDeadline = request.ApplicationDeadline;
                job.IsActive = request.IsActive;
                job.IsFeatured = request.IsFeatured;
                job.DepartmentId = request.DepartmentId;
                job.WorkingHours = request.WorkingHours;
                job.ProbationPeriod = request.ProbationPeriod;
                job.Locale = request.Locale ?? new Dictionary<string, JobPositionLocaleFields>();

                var success = await _jobPositionService.UpdateJobAsync(id, job);
                if (success)
                {
                    return Json(new { success = true, message = _adminResource["UpdateJobSuccess"] });
                }
                else
                {
                    return Json(new { success = false, message = _adminResource["UpdateJobError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating job position: {Id}", id);
                return Json(new { success = false, message = _adminResource["UpdateJobError"] });
            }
        }

        [HttpDelete("job-position/{id}")]
        public async Task<IActionResult> DeleteJobPosition(string id)
        {
            try
            {
                var success = await _jobPositionService.DeleteAsync(id);
                if (success)
                {
                    return Json(new { success = true, message = _adminResource["DeleteJobSuccess"] });
                }
                else
                {
                    return Json(new { success = false, message = _adminResource["DeleteJobError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting job position: {Id}", id);
                return Json(new { success = false, message = _adminResource["DeleteJobError"] });
            }
        }

        // 员工访谈相关APIs
        [HttpPost("employee-interview")]
        public async Task<IActionResult> CreateEmployeeInterview([FromBody] EmployeeInterviewCreateRequest request)
        {
            try
            {
                var interview = new EmployeeInterview
                {
                    DepartmentId = request.DepartmentId,
                    YearsOfService = request.YearsOfService,
                    PhotoUrl = request.PhotoUrl,
                    InterviewDate = request.InterviewDate,
                    DisplayOrder = request.DisplayOrder,
                    IsFeatured = request.IsFeatured,
                    Status = request.Status,
                    InterviewType = request.InterviewType,
                    Locale = request.Locale ?? new Dictionary<string, EmployeeInterviewLocaleFields>()
                };

                var createdInterview = await _employeeInterviewService.CreateInterviewAsync(interview);
                return Json(new { success = true, data = createdInterview });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating employee interview");
                return Json(new { success = false, message = _adminResource["CreateInterviewError"] });
            }
        }

        [HttpGet("employee-interview/{id}")]
        public async Task<IActionResult> GetEmployeeInterview(string id)
        {
            try
            {
                var interview = await _employeeInterviewService.GetByIdAsync(id);
                if (interview == null)
                {
                    return Json(new { success = false, message = _adminResource["InterviewNotFound"] });
                }

                return Json(new { success = true, data = interview });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting employee interview: {Id}", id);
                return Json(new { success = false, message = _adminResource["LoadInterviewError"] });
            }
        }

        [HttpPut("employee-interview/{id}")]
        public async Task<IActionResult> UpdateEmployeeInterview(string id, [FromBody] EmployeeInterviewUpdateRequest request)
        {
            try
            {
                var interview = await _employeeInterviewService.GetByIdAsync(id);
                if (interview == null)
                {
                    return Json(new { success = false, message = _adminResource["InterviewNotFound"] });
                }

                // 更新字段
                interview.DepartmentId = request.DepartmentId;
                interview.YearsOfService = request.YearsOfService;
                interview.PhotoUrl = request.PhotoUrl;
                interview.InterviewDate = request.InterviewDate;
                interview.DisplayOrder = request.DisplayOrder;
                interview.IsFeatured = request.IsFeatured;
                interview.Status = request.Status;
                interview.InterviewType = request.InterviewType;
                interview.Locale = request.Locale ?? new Dictionary<string, EmployeeInterviewLocaleFields>();

                var success = await _employeeInterviewService.UpdateInterviewAsync(id, interview);
                if (success)
                {
                    return Json(new { success = true, message = _adminResource["UpdateInterviewSuccess"] });
                }
                else
                {
                    return Json(new { success = false, message = _adminResource["UpdateInterviewError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating employee interview: {Id}", id);
                return Json(new { success = false, message = _adminResource["UpdateInterviewError"] });
            }
        }

        [HttpDelete("employee-interview/{id}")]
        public async Task<IActionResult> DeleteEmployeeInterview(string id)
        {
            try
            {
                var success = await _employeeInterviewService.DeleteAsync(id);
                if (success)
                {
                    return Json(new { success = true, message = _adminResource["DeleteInterviewSuccess"] });
                }
                else
                {
                    return Json(new { success = false, message = _adminResource["DeleteInterviewError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting employee interview: {Id}", id);
                return Json(new { success = false, message = _adminResource["DeleteInterviewError"] });
            }
        }


    }
}