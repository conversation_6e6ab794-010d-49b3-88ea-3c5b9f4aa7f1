using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Settings;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Utility;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Admin
{
    /// <summary>
    /// 站点设置控制器
    /// </summary>
    [Authorize]
    [Route("Admin/[controller]")]
    [Route("{culture}/Admin/[controller]")]
    public class SiteSettingsController : BaseController
    {
        private readonly ILogger<SiteSettingsController> _logger;
        private readonly IThemeFileService _themeFileService;
        private readonly IComponentConfigService _componentConfigService;
        private readonly ComponentConfigDataService _componentConfigDataService;
        private readonly AdminResource _adminResource;
        private readonly IWebHostEnvironment _env;
        private readonly IMemoryCache _cache;
        public SiteSettingsController(
            ILogger<SiteSettingsController> logger,
            SiteSettingsService siteSettingsService,
            IThemeFileService themeFileService,
            IComponentConfigService componentConfigService,
            ComponentConfigDataService componentConfigDataService,
            IThemeSettingsService themeSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration,
            IWebHostEnvironment env,
             IMemoryCache cache,
        AdminResource adminResource)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _logger = logger;
            _themeFileService = themeFileService;
            _componentConfigService = componentConfigService;
            _componentConfigDataService = componentConfigDataService;
            _adminResource = adminResource;
            _env = env;
            _cache = cache;
        }

        /// <summary>
        /// 站点设置主页面
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                var currentSettings = await _siteSettingsService.GetCurrentSettingsAsync();
                var availableThemes = await _themeFileService.GetAvailableThemesAsync();
                var currentThemeId = await _themeFileService.GetCurrentThemeIdAsync();

                var currentLanuage = _currentLanguage;

                var viewModel = new SiteSettingsViewModel
                {
                    BasicInfo = new BasicInfoSettings
                    {
                        SiteNames = GetMultiLanguageSiteNames(currentSettings),
                        CountCode = currentSettings?.CountCode ?? "",
                        Domain = GetCustomSettingAsync(currentSettings, "Domain") ?? "",
                        LogoFileName = ExtractFileName(currentSettings?.LogoUrl),
                        FaviconFileName = ExtractFileName(currentSettings?.FaviconUrl)
                    },
                    ThemeSettings = new ThemeSettings
                    {
                        AvailableThemes = availableThemes.Select(t => new ThemeOption
                        {
                            Id = t.Id,
                            Name = t.Name,
                            Description = t.Description,
                            PreviewImage = t.PreviewImage,
                            IsActive = t.Id == currentThemeId,
                            ColorPreview = new Dictionary<string, string>(),
                            Version = t.Version ?? "1.0.0",
                            Author = t.Author ?? "MlSoft"
                        }).ToList(),
                        CurrentThemeId = currentThemeId
                    },
                    ComponentSettings = new ComponentSettings
                    {
                        HeaderVariant = GetCustomSettingAsync(currentSettings, "HeaderVariant") ?? "Default",
                        FooterVariant = GetCustomSettingAsync(currentSettings, "FooterVariant") ?? "Default",
                        CookieVariant = GetCustomSettingAsync(currentSettings, "CookieVariant") ?? "Default",
                        HeaderVariants = await _componentConfigService.GetComponentVariants("Header", currentLanuage),
                        CookieVariants = await _componentConfigService.GetComponentVariants("CookiePolicy", currentLanuage),
                        FooterVariants = await _componentConfigService.GetComponentVariants("Footer", currentLanuage),
                    }
                };



                return View("~/Views/Admin/SiteSettings/Index.cshtml", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading site settings");
                TempData["Error"] = _adminResource["LoadSiteSettingsError"];
                return RedirectToAction("Dashboard", "Admin");
            }
        }

        private string GetCustomSettingAsync(SiteSettings? settings, string key)
        {
            if (settings == null || settings.CustomSettings == null)
            {
                return string.Empty;
            }
            return settings?.CustomSettings.TryGetValue(key, out var value) == true ? value : null;
        }

        /// <summary>
        /// 更新基本信息设置
        /// </summary>
        [HttpPost]
        [Route("basic-info")]
        public async Task<IActionResult> UpdateBasicInfo([FromBody] BasicInfoSettings basicInfo)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["InputDataError"] });
                }

                var currentSettings = await _siteSettingsService.GetCurrentSettingsAsync();
                if (currentSettings == null)
                {
                    currentSettings = new MlSoft.Sites.Model.Entities.Settings.SiteSettings();
                }

                // 更新基本信息

                // 默认语言不支持用户修改
                //currentSettings.DefaultLanguage = basicInfo.DefaultLanguage;

                currentSettings.LogoUrl = !string.IsNullOrEmpty(basicInfo.LogoFileName) ? $"{basicInfo.LogoFileName}" : null;

                if (!string.IsNullOrEmpty(basicInfo.FaviconFileName) && basicInfo.FaviconFileName != "favicon.ico")
                {
                    var uploadRootPath = _configuration.GetValue<string>("UploadRootPath");
                    var newIcon = Path.Combine(uploadRootPath, EnumFolderType.BasicInfo, basicInfo.FaviconFileName);
                    if (System.IO.File.Exists(newIcon))
                    {
                        var faviconPath = Path.Combine(_env.WebRootPath, "favicon.ico");
                        System.IO.File.Move(newIcon, faviconPath, true);
                        currentSettings.FaviconUrl = "favicon.ico";
                    }
                }

                currentSettings.CountCode = basicInfo.CountCode;

                currentSettings.UpdatedAt = DateTime.UtcNow;

                // 更新多语言字段
                if (currentSettings.Locale == null)
                    currentSettings.Locale = new Dictionary<string, MlSoft.Sites.Model.Entities.LocaleFields.SiteSettingsLocaleFields>();

                // 更新所有语言的站点名称
                foreach (var siteNameEntry in basicInfo.SiteNames)
                {
                    var localeData = currentSettings.Locale.GetValueOrDefault(siteNameEntry.Key,
                        new MlSoft.Sites.Model.Entities.LocaleFields.SiteSettingsLocaleFields());

                    localeData.SiteTitle = siteNameEntry.Value;
                    currentSettings.Locale[siteNameEntry.Key] = localeData;
                }

                await _siteSettingsService.CreateOrUpdateSettingsAsync(currentSettings);

                // 更新自定义设置
                await _siteSettingsService.UpdateCustomSettingAsync("Domain", basicInfo.Domain);

                _logger.LogInformation("Basic site settings updated by {User}", User.Identity?.Name);

                // 清理缓存
                _cache.Remove(ComponentConfigService.CACHE_GLOBAL_SITEINFO);

                return Json(new { success = true, message = _adminResource["BasicInfoSaved"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating basic info settings");
                return Json(new { success = false, message = _adminResource["SaveSettingsError"] + "：" + ex.Message });
            }
        }

        /// <summary>
        /// 应用主题
        /// </summary>
        [HttpPost]
        [Route("apply-theme")]
        public async Task<IActionResult> ApplyTheme([FromBody] ApplyThemeRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ThemeId))
                {
                    return Json(new { success = false, message = _adminResource["SelectTheme"] });
                }

                var companyId = "default-company";
                var currentUser = User.Identity?.Name ?? "Unknown";

                // 验证主题是否存在
                if (!await _themeFileService.ValidateThemeAsync(request.ThemeId))
                {
                    return Json(new { success = false, message = _adminResource["InvalidTheme"] });
                }

                // 应用主题文件
                var fileSuccess = await _themeFileService.ApplyThemeAsync(request.ThemeId);

                if (fileSuccess)
                {
                    // 更新数据库记录
                    var dbSuccess = await _themeSettingsService.UpdateSiteThemeAsync(companyId, request.ThemeId, currentUser);

                    _logger.LogInformation("Theme {ThemeId} applied successfully by {User}",
                        request.ThemeId, currentUser);

                    return Json(new
                    {
                        success = true,
                        message = _adminResource["ThemeAppliedSuccess"]
                    });
                }

                return Json(new { success = false, message = _adminResource["ThemeApplyFailed"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying theme: {ThemeId}", request.ThemeId);
                return Json(new { success = false, message = _adminResource["SystemError"] + "：" + ex.Message });
            }
        }

        /// <summary>
        /// 更新组件设置
        /// </summary>
        [HttpPost]
        [Route("component-settings")]
        public async Task<IActionResult> UpdateComponentSettings([FromBody] ComponentSettings componentSettings)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new { success = false, message = _adminResource["InputDataError"] });
                }

                // 保存组件设置到自定义设置
                await _siteSettingsService.UpdateCustomSettingAsync("HeaderVariant", componentSettings.HeaderVariant);
                await _siteSettingsService.UpdateCustomSettingAsync("FooterVariant", componentSettings.FooterVariant);

                // 刷新组件配置缓存
                //await _componentConfigService.RefreshAllComponentConfigAsync();

                _logger.LogInformation("Component settings updated by {User}", User.Identity?.Name);

                return Json(new { success = true, message = _adminResource["ComponentSettingsSaved"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating component settings");
                return Json(new { success = false, message = _adminResource["SaveSettingsError"] + "：" + ex.Message });
            }
        }

        /// <summary>
        /// 上传图片
        /// </summary>
        [HttpPost]
        [Route("upload-image")]
        public async Task<IActionResult> UploadImage(IFormFile file, string type)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return Json(new { success = false, message = _adminResource["SelectFile"] });
                }

                // 验证文件类型
                var allowedTypes = new[] { "image/x-icon", "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/svg+xml" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                {
                    return Json(new { success = false, message = _adminResource["UnsupportedFileType"] });
                }

                // 验证文件大小 (5MB)
                if (file.Length > 5 * 1024 * 1024)
                {
                    return Json(new { success = false, message = _adminResource["FileSizeLimit"] });
                }

                // 获取上传根目录
                var uploadRootPath = _configuration.GetValue<string>("UploadRootPath");
                if (string.IsNullOrEmpty(uploadRootPath))
                {
                    return Json(new { success = false, message = _adminResource["UploadConfigError"] });
                }

                // 创建目标目录
                var targetDir = Path.Combine(uploadRootPath, EnumFolderType.BasicInfo);
                if (!Directory.Exists(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                // 生成文件名
                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                var fileName = $"{type}_{DateTime.Now.Ticks}";

                // Logo转换为webp格式
                if (type == "logo")
                {
                    fileName += ".webp";
                    var tempFilePath = $"{Path.GetTempFileName()}{fileExtension}";

                    try
                    {
                        // 先保存原始文件
                        using (var stream = new FileStream(tempFilePath, FileMode.Create))
                        {
                            await file.CopyToAsync(stream);
                        }

                        // 转换为webp格式
                        var finalPath = Path.Combine(targetDir, fileName);
                        var success = ImageHelper.ConvertImage2WebP(tempFilePath, finalPath, 800, 600, false);

                        if (!success)
                        {
                            return Json(new { success = false, message = _adminResource["ImageProcessFailed"] });
                        }
                    }
                    finally
                    {
                        // 清理临时文件
                        if (System.IO.File.Exists(tempFilePath))
                        {
                            System.IO.File.Delete(tempFilePath);
                        }
                    }
                }
                else
                {
                    // Favicon或其他图片直接保存
                    fileName += fileExtension;
                    var finalPath = Path.Combine(targetDir, fileName);

                    using (var stream = new FileStream(finalPath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    if (fileExtension != ".ico")
                    {
                        //转换

                        // 生成favicon文件名
                        var faviconFileName = $"favicon_{DateTime.UtcNow.Ticks}.ico";
                        var faviconPath = Path.Combine(targetDir, faviconFileName);

                        // 调用ImageHelper转换为ICO格式
                        if (ImageHelper.ConvertImageToIco(finalPath, faviconPath))
                        {
                            System.IO.File.Delete(finalPath);
                            fileName = faviconFileName;
                        }

                    }

                }

                _logger.LogInformation("Image uploaded: {FileName} by {User}", fileName, User.Identity?.Name);

                return Json(new { success = true, fileName = fileName, message = _adminResource["UploadSuccess"] });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image");
                return Json(new { success = false, message = _adminResource["UploadFailed"] + "：" + ex.Message });
            }
        }

        /// <summary>
        /// 从Logo生成Favicon
        /// </summary>
        [HttpPost]
        [Route("generate-favicon")]
        public async Task<IActionResult> GenerateFavicon([FromBody] GenerateFaviconRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.LogoFileName))
                {
                    return Json(new { success = false, message = _adminResource["LogoFileNameEmpty"] });
                }

                var uploadRootPath = _configuration.GetValue<string>("UploadRootPath");
                if (string.IsNullOrEmpty(uploadRootPath))
                {
                    return Json(new { success = false, message = _adminResource["UploadConfigError"] });
                }

                var sourceDir = Path.Combine(uploadRootPath, EnumFolderType.BasicInfo);
                var logoPath = Path.Combine(sourceDir, request.LogoFileName);

                if (!System.IO.File.Exists(logoPath))
                {
                    return Json(new { success = false, message = _adminResource["LogoFileNotExists"] });
                }

                // 生成favicon文件名
                var faviconFileName = $"favicon_{DateTime.UtcNow.Ticks}.ico";
                var faviconPath = Path.Combine(sourceDir, faviconFileName);

                // 调用ImageHelper转换为ICO格式
                var success = ImageHelper.ConvertImageToIco(logoPath, faviconPath);

                if (!success)
                {
                    return Json(new { success = false, message = _adminResource["GenerateFaviconFailed"] });
                }

                _logger.LogInformation("Favicon generated from logo: {LogoFileName} -> {FaviconFileName} by {User}",
                    request.LogoFileName, faviconFileName, User.Identity?.Name);

                return Json(new
                {
                    success = true,
                    faviconFileName = faviconFileName,
                    message = _adminResource["FaviconGenerateSuccess"]
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating favicon from logo: {LogoFileName}", request.LogoFileName);
                return Json(new { success = false, message = _adminResource["GenerateFaviconFailed"] + "：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取多语言站点名称
        /// </summary>
        private Dictionary<string, string> GetMultiLanguageSiteNames(MlSoft.Sites.Model.Entities.Settings.SiteSettings? settings)
        {
            var siteNames = new Dictionary<string, string>();

            if (settings?.Locale != null)
            {
                foreach (var lang in _supportedLanguages)
                {
                    var localeData = settings.Locale.GetValueOrDefault(lang.Code);
                    siteNames[lang.Code] = localeData?.SiteTitle ?? "";
                }
            }
            else
            {
                // 如果没有设置数据，初始化空值
                foreach (var lang in _supportedLanguages)
                {
                    siteNames[lang.Code] = "";
                }
            }

            return siteNames;
        }

        /// <summary>
        /// 从URL中提取文件名
        /// </summary>
        private string? ExtractFileName(string? urlOrFileName)
        {
            if (string.IsNullOrEmpty(urlOrFileName))
                return null;

            // 如果已经是文件名（不包含路径分隔符），直接返回
            if (!urlOrFileName.Contains('/') && !urlOrFileName.Contains('\\'))
                return urlOrFileName;

            // 如果是完整URL或路径，提取最后的文件名部分
            return Path.GetFileName(urlOrFileName);
        }

        /// <summary>
        /// 获取组件配置数据
        /// </summary>
        [HttpGet]
        [Route("get-component-data/{componentId}/{variantId}")]
        public async Task<IActionResult> GetComponentData(string componentId, string variantId)
        {
            try
            {
                if (string.IsNullOrEmpty(componentId) || string.IsNullOrEmpty(variantId))
                {
                    return Json(new { success = false, message = _adminResource["InvalidParameters"] });
                }

                var componentData = await _componentConfigDataService.GetByComponentAndVariantAsync(componentId, variantId);

                if (componentData == null)
                {
                    return Json(new { success = true, data = new { } }); // 返回空对象而不是null
                }

                // 解析JSON数据
                object? data = null;
                if (!string.IsNullOrEmpty(componentData.JsonData))
                {
                    try
                    {
                        data = JsonSerializer.Deserialize<object>(componentData.JsonData);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, "Failed to parse component data JSON for {ComponentId}/{VariantId}", componentId, variantId);
                        data = new { };
                    }
                }

                return Json(new { success = true, data = data ?? new { } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting component data for {ComponentId}/{VariantId}", componentId, variantId);
                return Json(new { success = false, message = _adminResource["LoadComponentDataError"] });
            }
        }

        /// <summary>
        /// 保存组件配置数据
        /// </summary>
        [HttpPost]
        [Route("save-component-data")]
        public async Task<IActionResult> SaveComponentData([FromBody] SaveComponentDataRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.ComponentId) || string.IsNullOrEmpty(request.VariantId))
                {
                    return Json(new { success = false, message = _adminResource["InvalidParameters"] });
                }

                // 将数据序列化为JSON
                string jsonData = "{}";
                if (request.Data != null)
                {
                    try
                    {
                        jsonData = JsonSerializer.Serialize(request.Data, new JsonSerializerOptions
                        {
                            WriteIndented = true
                        });
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, "Failed to serialize component data for {ComponentId}/{VariantId}", request.ComponentId, request.VariantId);
                        return Json(new { success = false, message = _adminResource["SerializeDataError"] });
                    }
                }

                var currentUser = User.Identity?.Name ?? "Unknown";
                var success = await _componentConfigDataService.CreateOrUpdateAsync(
                    request.ComponentId,
                    request.VariantId,
                    jsonData,
                    currentUser
                );

                if (success)
                {
                    _logger.LogInformation("Component data saved for {ComponentId}/{VariantId} by {User}",
                        request.ComponentId, request.VariantId, currentUser);

                    return Json(new { success = true, message = _adminResource["ComponentDataSaved"] });
                }
                else
                {
                    return Json(new { success = false, message = _adminResource["SaveComponentDataError"] });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving component data for {ComponentId}/{VariantId}",
                    request.ComponentId, request.VariantId);
                return Json(new { success = false, message = _adminResource["SaveComponentDataError"] + "：" + ex.Message });
            }
        }


    }

    /// <summary>
    /// 应用主题请求
    /// </summary>
    public class ApplyThemeRequest
    {
        public string ThemeId { get; set; } = string.Empty;
        public bool ForceRecompile { get; set; } = true;
    }

    /// <summary>
    /// 生成Favicon请求
    /// </summary>
    public class GenerateFaviconRequest
    {
        public string LogoFileName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 保存组件数据请求
    /// </summary>
    public class SaveComponentDataRequest
    {
        public string ComponentId { get; set; } = string.Empty;
        public string VariantId { get; set; } = string.Empty;
        public object? Data { get; set; }
    }
}