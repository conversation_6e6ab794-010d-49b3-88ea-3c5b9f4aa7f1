using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.Utilities;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers.Base
{
    /// <summary>
    /// Controller基类，处理通用组件数据
    /// </summary>
    public abstract class BaseController : Controller
    {
        protected readonly IComponentConfigService _componentConfigService;
        protected readonly IThemeSettingsService _themeSettingsService;
        protected readonly SiteSettingsService _siteSettingsService;
        protected readonly IConfiguration _configuration;
        protected readonly SupportedLanguage[] _supportedLanguages;

        protected string _defaultLanguage { get; set; }

        protected BaseController(
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration)
        {
            _componentConfigService = componentConfigService;
            _themeSettingsService = themeSettingsService;
            _siteSettingsService = siteSettingsService;
            _configuration = configuration;
            _supportedLanguages = supportedLanguages;


        }

        protected string _currentLanguage
        {
            get
            {
                if (ViewData.ContainsKey("CurrentLanguage"))
                {
                    return ViewData["CurrentLanguage"].ToString();
                }
                else
                {

                    var culture = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
                    ViewData["CurrentLanguage"] = culture;

                    return culture;

                }


            }
            set { }
        }

        /// <summary>
        /// 在Action执行后设置通用组件ViewData
        /// </summary>
        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // 执行Action
            var result = await next();

            // 如果Action执行成功，设置组件ViewData
            if (result.Result == null || result.Result is ViewResult)
            {
                _currentLanguage = ViewData["CurrentLanguage"].ToString();
                _defaultLanguage = ViewData["DefaultLanguage"].ToString();
            }
        }


    }
}