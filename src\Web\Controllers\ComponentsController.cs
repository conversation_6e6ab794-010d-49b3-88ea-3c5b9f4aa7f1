using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [Route("{culture}/api/[controller]")]
    public class ComponentsController : ControllerBase
    {
        private readonly IComponentConfigService _componentConfigService;
        private readonly ILogger<ComponentsController> _logger;

        public ComponentsController(
            IComponentConfigService componentConfigService,
            ILogger<ComponentsController> logger)
        {
            _componentConfigService = componentConfigService;
            _logger = logger;
        }

        /// <summary>
        /// 获取组件的特定变体信息
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <param name="variantId">变体ID</param>
        /// <returns>变体信息</returns>
        [HttpGet("{componentId}/variants/{variantId}")]
        public async Task<IActionResult> GetComponentVariant(string componentId, string variantId = "Default")
        {
            try
            {
                ComponentVariant? variant = null;
                var component = await _componentConfigService.GetComponent(componentId);
                if (component != null && component.Variants != null)
                {
                    variant = component.Variants.FirstOrDefault(x => x.Id == variantId);
                }

                if (variant == null)
                {
                    return NotFound(new { error = $"Variant {variantId} not found for component {componentId}" });
                }

                // 处理本地化
                var localizedVariant = await _componentConfigService.ProcessVariantLocalization(variant);

                return Ok(localizedVariant);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting variant {VariantId} for component {ComponentId}", variantId, componentId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// 获取组件的所有变体
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <returns>变体列表</returns>
        [HttpGet("{componentId}/variants")]
        public async Task<IActionResult> GetComponentVariants(string componentId)
        {
            try
            {
                List<ComponentVariant>? variants = null;
                var component = await _componentConfigService.GetComponent(componentId);
                if (component != null && component.Variants != null)
                {
                    variants = component.Variants;
                }


                var localizedVariants = variants.Select(async v => await _componentConfigService.ProcessVariantLocalization(v)).ToList();

                return Ok(localizedVariants);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting variants for component {ComponentId}", componentId);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }
    }
}