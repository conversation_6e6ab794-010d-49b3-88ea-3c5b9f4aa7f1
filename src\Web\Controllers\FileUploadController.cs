using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Utility;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class FileUploadController : ControllerBase
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ILogger<FileUploadController> _logger;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public FileUploadController(
            IWebHostEnvironment webHostEnvironment,
            ILogger<FileUploadController> logger,
            IStringLocalizer<SharedResource> localizer)
        {
            _webHostEnvironment = webHostEnvironment;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(
            IFormFile file, 
            [FromForm] string folder, 
            [FromForm] string allowedTypes, 
            [FromForm] string maxSize,
            [FromForm] int maxWidth = 0,
            [FromForm] int maxHeight = 0
            )
        {
            try
            {
                // 验证文件
                var validationResult = ValidateFile(file, allowedTypes, maxSize);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new { error = validationResult.ErrorMessage });
                }

                // 生成文件名和路径
                var fileName = GenerateUniqueFileName(file.FileName);
                var uploadsFolder = Path.Combine(_webHostEnvironment.WebRootPath, "upload", folder);
                Directory.CreateDirectory(uploadsFolder);

                var filePath = Path.Combine(uploadsFolder, fileName);
                var finalFilePath = filePath;
                var finalFileName = fileName;
                var finalContentType = file.ContentType;

                // 检查是否为图片文件
                var isImage = IsImageFile(file.ContentType, file.FileName);
                
                if (isImage)
                {
                    // 先保存原始文件到临时位置
                    var tempFilePath = Path.Combine(uploadsFolder, $"temp_{fileName}");
                    using (var stream = new FileStream(tempFilePath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    try
                    {
                        // 生成WebP文件名
                        var webpFileName = Path.ChangeExtension(fileName, ".webp");
                        var webpFilePath = Path.Combine(uploadsFolder, webpFileName);

                        // 转换为WebP格式，保持原尺寸（设置为0表示不调整尺寸）
                        var conversionSuccess = ImageHelper.ConvertImage2WebP(tempFilePath, webpFilePath, maxWidth, maxHeight);

                        if (conversionSuccess)
                        {
                            // 转换成功，使用WebP文件
                            finalFilePath = webpFilePath;
                            finalFileName = webpFileName;
                            finalContentType = "image/webp";
                        }
                        else
                        {
                            //// 转换失败，使用原始文件
                            //System.IO.File.Move(tempFilePath, filePath);
                            //_logger.LogWarning("Failed to convert image to WebP: {FileName}", file.FileName);
                            _logger.LogError("Failed to convert image to WebP: {FileName}", file.FileName);
                            return StatusCode(500, new { error = _localizer["FileUpload_UploadError"].Value });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error uploading file");
                        return StatusCode(500, new { error = _localizer["FileUpload_UploadError"].Value });
                    }
                    finally
                    {
                        // 删除临时文件
                        if (System.IO.File.Exists(tempFilePath))
                        {
                            System.IO.File.Delete(tempFilePath);
                        }
                    }
                }
                else
                {
                    // 非图片文件，直接保存
                    using var stream = new FileStream(filePath, FileMode.Create);
                    await file.CopyToAsync(stream);
                }

                

                // 获取最终文件信息
                var fileInfo = new FileInfo(finalFilePath);
                var relativePath = $"/upload/{folder}/{finalFileName}";
                
                return Ok(new 
                {
                    success = true,
                    fileName = finalFileName,
                    originalName = file.FileName,
                    filePath = relativePath,
                    fileSize = fileInfo.Length,
                    contentType = finalContentType,
                    converted = isImage && finalContentType == "image/webp"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");
                return StatusCode(500, new { error = _localizer["FileUpload_UploadError"].Value });
            }
        }

        [HttpDelete("delete")]
        public IActionResult DeleteFile([FromQuery] string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    return BadRequest(new { error = "File path is required" });
                }

                // 确保路径安全
                var safePath = filePath.TrimStart('/');
                var fullPath = Path.Combine(_webHostEnvironment.WebRootPath, safePath);

                // 安全检查：确保路径在wwwroot/upload目录内
                var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "upload");
                var normalizedFullPath = Path.GetFullPath(fullPath);
                var normalizedUploadsPath = Path.GetFullPath(uploadsPath);

                if (!normalizedFullPath.StartsWith(normalizedUploadsPath))
                {
                    return BadRequest(new { error = "Invalid file path" });
                }

                if (System.IO.File.Exists(fullPath))
                {
                    System.IO.File.Delete(fullPath);
                    return Ok(new { success = true });
                }

                return Ok(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
                return StatusCode(500, new { error = _localizer["FileUpload_DeleteError"].Value });
            }
        }

        private (bool IsValid, string ErrorMessage) ValidateFile(IFormFile file, string allowedTypes, string maxSize)
        {
            if (file == null || file.Length == 0)
            {
                return (false, _localizer["FileUpload_NoFileSelected"].Value);
            }

            // 验证文件类型
            if (!string.IsNullOrEmpty(allowedTypes))
            {
                var types = allowedTypes.Split(',').Select(t => t.Trim()).ToList();
                var isValidType = types.Any(t =>
                {
                    if (t.EndsWith("/*"))
                    {
                        var prefix = t.Substring(0, t.Length - 2);
                        return file.ContentType.StartsWith(prefix);
                    }
                    return file.ContentType.Equals(t, StringComparison.OrdinalIgnoreCase) ||
                           file.FileName.EndsWith(t.Replace("application/", "."), StringComparison.OrdinalIgnoreCase);
                });

                if (!isValidType)
                {
                    return (false, _localizer["FileUpload_InvalidFileType"].Value);
                }
            }

            // 验证文件大小
            if (!string.IsNullOrEmpty(maxSize))
            {
                var maxSizeBytes = ParseFileSize(maxSize);
                if (file.Length > maxSizeBytes)
                {
                    return (false, _localizer["FileUpload_FileTooLarge"].Value);
                }
            }

            return (true, string.Empty);
        }

        private long ParseFileSize(string sizeString)
        {
            var size = sizeString.ToUpper();
            var numberPart = System.Text.RegularExpressions.Regex.Match(size, @"\d+").Value;
            
            if (string.IsNullOrEmpty(numberPart))
                return 0;

            var number = long.Parse(numberPart);

            if (size.Contains("KB")) return number * 1024;
            if (size.Contains("MB")) return number * 1024 * 1024;
            if (size.Contains("GB")) return number * 1024 * 1024 * 1024;

            return number;
        }

        private string GenerateUniqueFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var randomString = Guid.NewGuid().ToString("N")[..8];

            // 清理文件名，移除特殊字符
            var cleanName = System.Text.RegularExpressions.Regex.Replace(
                nameWithoutExtension, @"[^a-zA-Z0-9_\-\u4e00-\u9fff]", "_");

            return $"{cleanName}_{timestamp}_{randomString}{extension}";
        }

        private bool IsImageFile(string contentType, string fileName)
        {
            if (string.IsNullOrEmpty(contentType) && string.IsNullOrEmpty(fileName))
                return false;

            // 通过Content-Type判断
            var imageContentTypes = new[]
            {
                "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp",
                "image/webp", "image/svg+xml", "image/tiff", "image/tif"
            };

            if (!string.IsNullOrEmpty(contentType) && 
                imageContentTypes.Any(type => contentType.Equals(type, StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }

            // 通过文件扩展名判断
            if (!string.IsNullOrEmpty(fileName))
            {
                var extension = Path.GetExtension(fileName).ToLowerInvariant();
                var imageExtensions = new[]
                {
                    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".tiff", ".tif"
                };

                return imageExtensions.Contains(extension);
            }

            return false;
        }
    }
}