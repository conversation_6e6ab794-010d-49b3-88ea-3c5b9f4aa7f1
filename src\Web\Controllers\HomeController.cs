using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Web.ViewModels;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Themes;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers
{

    public class HomeController : BaseController
    {
        private readonly ILogger<HomeController> _logger;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public HomeController(
        ILogger<HomeController> logger,
        IComponentConfigService componentConfigService,
        IThemeSettingsService themeSettingsService,
        SiteSettingsService siteSettingsService,
        SupportedLanguage[] supportedLanguages,
        IStringLocalizer<SharedResource> localizer,
        IConfiguration configuration)
        : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _logger = logger;
            _localizer = localizer;
        }

        public IActionResult Index()
        {

            var test = _localizer["Welcome"];
            if (!string.IsNullOrEmpty(test))
            {

            }

            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        public IActionResult Components()
        {
            return View();
        }

        public IActionResult ComponentTest()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult NotFound()
        {
            Response.StatusCode = 404;
            return View();
        }
    }
}

