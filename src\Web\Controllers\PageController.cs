using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Service.Pages;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Controllers.Base;
using MlSoft.Sites.Web.Extensions;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.Services.Themes;
using MlSoft.Sites.Web.ViewModels;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Controllers
{
    public class PageController : BaseController
    {
        private readonly PageConfigurationService _pageConfigService;
        private readonly PageManageService _pageManageService;
        private readonly IComponentConfigService _componentConfigService;
        private readonly IThemeFileService _themeFileService;
        private readonly ILogger<PageController> _logger;

        public PageController(
            PageConfigurationService pageConfigService,
            PageManageService pageManageService,
            IThemeFileService themeFileService,
            ILogger<PageController> logger,
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
            SiteSettingsService siteSettingsService,
            SupportedLanguage[] supportedLanguages,
            IConfiguration configuration)
            : base(componentConfigService, themeSettingsService, siteSettingsService, supportedLanguages, configuration)
        {
            _pageConfigService = pageConfigService;
            _pageManageService = pageManageService;
            _componentConfigService = componentConfigService;
            _themeFileService = themeFileService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Render(string pageKey, string? culture = null)
        {
            try
            {
               

                // 获取当前文化和主题
                culture ??= _currentLanguage;


                // 获取页面内容数据
                var pageContent = await _pageManageService.GetPageContentByKeyAsync(pageKey);
                if (pageContent == null)
                {
                    _logger.LogWarning("Page content not found for key: {PageKey}", pageKey);
                    return NotFound();
                }

                // 检查页面状态
                if (pageContent.Status != Model.Entities.Enums.PageContentStatus.Published)
                {
                    _logger.LogWarning("Page {PageKey} is not published", pageKey);
                    return NotFound();
                }

                // 获取页面配置
                var pageConfig = await _pageConfigService.GetByPageKeyAsync(pageKey);
                if (pageConfig == null)
                {
                    _logger.LogWarning("Page configuration not found for key: {PageKey}", pageKey);
                    return NotFound();
                }

                // 检查页面状态
                if (pageConfig.Status != Model.Entities.Enums.PageStatus.Published)
                {
                    _logger.LogWarning("Page {PageKey} is not published", pageKey);
                    return NotFound();
                }

                // 解密页面配置内容
                var configContent = pageConfig.PageCfgContent;
                if (configContent == null)
                {
                    _logger.LogError("Failed to decrypt page config for key: {PageKey}", pageKey);
                    return NotFound();
                }

                // 检查访问权限
                if (!await HasAccessToPage(pageConfig, configContent))
                {
                    return Forbid();
                }


                // 组件间共享数据
                var ctx = new PageComponentContext();
                ViewData[PageComponentContext.ViewDataKey] = ctx;


                // 设置ViewData
                ViewData["PageKey"] = configContent.PageKey;


                // 设置布局模板
                if (!string.IsNullOrEmpty(configContent.LayoutTemplate))
                {
                    ViewData["Layout"] = configContent.LayoutTemplate;
                }

                ViewData["PageName"] = pageConfig.Name.ContainsKey(_currentLanguage) ? pageConfig.Name[_currentLanguage] : "";


                // 准备组件数据 - 使用新的JSON模式
                var visibleComponents = configContent.Components
                    .Where(c => c.IsVisible)
                    .OrderBy(c => c.DisplayOrder)
                    .ToList();



                var componentJsonData = new Dictionary<string, JObject>();

                foreach (var component in visibleComponents)
                {
                    JObject jsonData = null;

                    var contentData = pageContent.ComponentsData.FirstOrDefault(d =>
                            d.ComponentDefinitionId == component.ComponentDefinitionId && d.IdNo == component.IdNo);

                    if (contentData != null)
                    {
                        if (!contentData.IsVisible)
                        {
                            component.IsVisible = false;
                            continue;
                        }


                        if (!string.IsNullOrEmpty(contentData.DataJson))
                        {
                            try
                            {
                                jsonData = JObject.Parse(contentData.DataJson);
                            }
                            catch (JsonException ex)
                            {
                                _logger.LogWarning(ex, "Failed to parse component data JSON for {ComponentId}", component.ComponentDefinitionId);
                            }
                        }

                        // 如果没有内容数据，使用配置中的默认参数
                        if (jsonData == null)
                        {
                            try
                            {
                                jsonData = JObject.Parse(component.ParametersJson);
                            }
                            catch (JsonException ex)
                            {
                                _logger.LogWarning(ex, "Failed to parse component parameters JSON for {ComponentId}", component.ComponentDefinitionId);
                            }
                        }

                        // 如果还是没有数据，使用组件的默认配置
                        if (jsonData == null)
                        {
                            var componentConfig = await _componentConfigService.GetComponent(component.ComponentDefinitionId);
                            if (componentConfig != null)
                            {
                                var variant = componentConfig.Variants.FirstOrDefault(x => x.Id == component.TemplateKey);
                                if (variant != null && variant.DefaultData != null)
                                {
                                    try
                                    {
                                        jsonData = JObject.FromObject(variant.DefaultData);
                                    }
                                    catch (JsonException ex)
                                    {
                                        _logger.LogWarning(ex, "Failed to load default data {ComponentId},{variantId}", component.ComponentDefinitionId, variant.Id);
                                    }
                                }
                            }
                        }


                        // 适配多语言数据
                        if (jsonData != null)
                        {
                            try
                            {
                                var adaptedData = await _componentConfigService.GetAdaptedComponentDataAsync(
                                    component.ComponentDefinitionId, component.TemplateKey, culture, jsonData);


                                var componentKey = component.ComponentDefinitionId + "_" + component.IdNo;
                                componentJsonData[componentKey] = adaptedData;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error adapting component data for {ComponentId}", component.ComponentDefinitionId);

                                // 使用原始数据作为回退
                                var componentKey = component.ComponentDefinitionId + "_" + component.IdNo;
                                componentJsonData[componentKey] = jsonData;
                            }
                        }
                    }
                }

                ViewData["PageComponents"] = visibleComponents;
                ViewData["ComponentJsonData"] = componentJsonData;


                // 渲染动态视图
                return View("Dynamic", pageConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rendering page {PageKey}", pageKey);
                return StatusCode(500);
            }
        }


        private async Task<bool> HasAccessToPage(Model.Entities.Pages.PageConfiguration pageConfig, Model.Entities.Pages.PageConfigContent configContent)
        {
            try
            {
                var accessConfig = configContent.AccessControl;
                if (accessConfig == null)
                {
                    return true;
                }

                // 检查身份验证
                if (accessConfig.RequireAuthentication && !User.Identity?.IsAuthenticated == true)
                {
                    return false;
                }

                // 检查用户角色
                if (accessConfig.RequiredRoles.Any() && !accessConfig.RequiredRoles.Any(role => User.IsInRole(role)))
                {
                    return false;
                }



                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking page access for {PageKey}", configContent.PageKey);
                return false;
            }
        }


        /// <summary>
        /// 从JsonElement提取值
        /// </summary>
        private object? GetJsonElementValue(JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Null:
                    return null;
                case JsonValueKind.String:
                    return element.GetString();
                case JsonValueKind.Number:
                    if (element.TryGetInt32(out var intValue))
                        return intValue;
                    if (element.TryGetInt64(out var longValue))
                        return longValue;
                    if (element.TryGetDouble(out var doubleValue))
                        return doubleValue;
                    if (element.TryGetDecimal(out var decimalValue))
                        return decimalValue;
                    return 0;
                case JsonValueKind.True:
                    return true;
                case JsonValueKind.False:
                    return false;
                case JsonValueKind.Array:
                    // 处理数组，如文件上传的多个文件或多选项
                    var arrayItems = new List<object?>();
                    foreach (var item in element.EnumerateArray())
                    {
                        arrayItems.Add(GetJsonElementValue(item));
                    }
                    return arrayItems;
                case JsonValueKind.Object:
                    // 处理对象，如文件信息或复杂结构
                    var obj = new Dictionary<string, object?>();
                    foreach (var prop in element.EnumerateObject())
                    {
                        obj[prop.Name] = GetJsonElementValue(prop.Value);
                    }
                    return obj;
                default:
                    return element.ToString();
            }
        }


    }
}