using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using MlSoft.Sites.Model.Configuration;
using System.Globalization;
using System.Linq;

namespace MlSoft.Sites.Web.Extensions
{
    /// <summary>
    /// String扩展方法
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// 将字符串转换为标题格式
        /// </summary>
        public static string ToTitleCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input.ToLower());
        }
    }
}