using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using MlSoft.Sites.Model.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MlSoft.Sites.Web.Extensions
{
    /// <summary>
    /// 多语言 URL 生成扩展方法，供 Razor 视图使用
    /// </summary>
    public static class MultilingualUrlExtensions
    {
        /// <summary>
        /// 生成多语言 Action 链接
        /// </summary>
        /// <param name="htmlHelper">HTML Helper</param>
        /// <param name="linkText">链接文本</param>
        /// <param name="actionName">Action 名称</param>
        /// <param name="controllerName">Controller 名称</param>
        /// <param name="languageCode">语言代码，为空时使用当前语言</param>
        /// <param name="routeValues">路由参数</param>
        /// <param name="htmlAttributes">HTML 属性</param>
        /// <returns>HTML 链接</returns>
        public static IHtmlContent MultilingualActionLink(
            this IHtmlHelper htmlHelper,
            string linkText,
            string actionName,
            string controllerName,
            string? languageCode = null,
            object? routeValues = null,
            object? htmlAttributes = null, string area = "Admin")
        {
            var url = GenerateMultilingualUrl(htmlHelper, actionName, controllerName, languageCode, routeValues, area);

            // 创建一个 TagBuilder 来生成 <a> 标签
            var tagBuilder = new Microsoft.AspNetCore.Mvc.Rendering.TagBuilder("a");
            tagBuilder.InnerHtml.SetContent(linkText);
            tagBuilder.Attributes["href"] = url;

            // 添加 HTML 属性
            if (htmlAttributes != null)
            {
                var attributes = Microsoft.AspNetCore.Mvc.ViewFeatures.HtmlHelper.AnonymousObjectToHtmlAttributes(htmlAttributes);
                tagBuilder.MergeAttributes(attributes);
            }

            return tagBuilder;
        }

        /// <summary>
        /// 生成多语言 URL
        /// </summary>
        /// <param name="htmlHelper">HTML Helper</param>
        /// <param name="actionName">Action 名称</param>
        /// <param name="controllerName">Controller 名称</param>
        /// <param name="languageCode">语言代码，为空时使用当前语言</param>
        /// <param name="routeValues">路由参数</param>
        /// <returns>多语言 URL</returns>
        public static string MultilingualUrl(
            this IHtmlHelper htmlHelper,
            string actionName,
            string controllerName,
            string? languageCode = null,
            object? routeValues = null,
            string area = "Admin"
            )
        {
            return GenerateMultilingualUrl(htmlHelper, actionName, controllerName, languageCode, routeValues, area);
        }

        /// <summary>
        /// 生成多语言 URL（字典参数版本）
        /// </summary>
        /// <param name="htmlHelper">HTML Helper</param>
        /// <param name="actionName">Action 名称</param>
        /// <param name="controllerName">Controller 名称</param>
        /// <param name="languageCode">语言代码</param>
        /// <param name="routeValues">路由参数字典</param>
        /// <returns>多语言 URL</returns>
        public static string MultilingualUrl(
            this IHtmlHelper htmlHelper,
            string actionName,
            string controllerName,
            string languageCode,
            IDictionary<string, object?> routeValues,
            string area = "Admin")
        {
            return GenerateMultilingualUrl(htmlHelper, actionName, controllerName, languageCode, routeValues, area);
        }

        /// <summary>
        /// 获取当前页面的多语言版本URL
        /// </summary>
        /// <param name="htmlHelper">HTML Helper</param>
        /// <param name="languageCode">目标语言代码</param>
        /// <returns>当前页面的多语言版本URL</returns>
        public static string CurrentPageMultilingualUrl(this IHtmlHelper htmlHelper, string languageCode, string area = "Admin")
        {
            var routeData = htmlHelper.ViewContext.RouteData;
            var actionName = routeData.Values["action"]?.ToString() ?? "Index";
            var controllerName = routeData.Values["controller"]?.ToString() ?? "Home";

            // 复制当前路由参数，但排除 culture 参数
            var routeValues = new Dictionary<string, object?>();
            foreach (var kvp in routeData.Values)
            {
                if (kvp.Key.ToLower() != "culture" && kvp.Key.ToLower() != "action" && kvp.Key.ToLower() != "controller")
                {
                    routeValues[kvp.Key] = kvp.Value;
                }
            }

            return GenerateMultilingualUrl(htmlHelper, actionName, controllerName, languageCode, routeValues, area);
        }

        /// <summary>
        /// 内部方法：生成多语言URL的核心逻辑
        /// </summary>
        private static string GenerateMultilingualUrl(
            IHtmlHelper htmlHelper,
            string actionName,
            string controllerName,
            string? languageCode,
            object? routeValues, string area = "Admin")
        {
            var viewData = htmlHelper.ViewData;

            // 获取支持的语言列表
            var supportedLanguages = viewData["SupportedLanguages"] as SupportedLanguage[];
            if (supportedLanguages == null || supportedLanguages.Length == 0)
            {
                // 回退到普通URL生成
                var urlHelper = htmlHelper.ViewContext.HttpContext.RequestServices.GetService(typeof(IUrlHelper)) as IUrlHelper;
                return urlHelper?.Action(actionName, controllerName, routeValues) ?? "/";
            }

            // 确定目标语言
            languageCode ??= GetCurrentLanguageCode(viewData, supportedLanguages);
            var targetLanguage = supportedLanguages.FirstOrDefault(x => x.Code == languageCode);

            if (targetLanguage == null)
            {
                // 找不到指定语言，回退到默认语言
                var defaultLanguageCode = viewData["DefaultLanguage"] as string ?? supportedLanguages.First().Code;
                targetLanguage = supportedLanguages.FirstOrDefault(x => x.Code == defaultLanguageCode) ?? supportedLanguages.First();
            }

            // 生成基础 URL（确保不包含语言前缀）
            var urlHelper2 = htmlHelper.ViewContext.HttpContext.RequestServices.GetService(typeof(IUrlHelper)) as IUrlHelper;
            if (urlHelper2 == null)
            {
                // 如果IUrlHelper为null，直接构建URL
                var routeString = routeValues != null ? $"/{routeValues.GetType().GetProperty("id")?.GetValue(routeValues)}" : "";
                var actionUrl = "";

                if (!string.IsNullOrEmpty(area))
                {
                    actionUrl = $"/{area}";
                }

                if (!string.IsNullOrEmpty(controllerName))
                {
                    actionUrl += $"/{controllerName}";
                }

                if (!string.IsNullOrEmpty(actionName))
                {
                    actionUrl += $"/{actionName}";
                }

                if (!string.IsNullOrEmpty(routeString))
                {
                    actionUrl += $"{routeString}";
                }

                return BuildUrlWithBaseUrl(targetLanguage, actionUrl);
            }
            var actionUrlFromHelper = urlHelper2.Action(actionName, controllerName, routeValues) ?? "/";

            // 使用 BaseUrl 构建完整URL
            return BuildUrlWithBaseUrl(targetLanguage, actionUrlFromHelper);
        }

        /// <summary>
        /// 内部方法：生成多语言URL的核心逻辑（字典参数版本）
        /// </summary>
        private static string GenerateMultilingualUrl(
            IHtmlHelper htmlHelper,
            string actionName,
            string controllerName,
            string? languageCode,
            IDictionary<string, object?> routeValues)
        {
            var viewData = htmlHelper.ViewData;

            var supportedLanguages = viewData["SupportedLanguages"] as SupportedLanguage[];
            if (supportedLanguages == null || supportedLanguages.Length == 0)
            {
                var urlHelper = htmlHelper.ViewContext.HttpContext.RequestServices.GetService(typeof(IUrlHelper)) as IUrlHelper;
                return urlHelper?.Action(actionName, controllerName, routeValues) ?? "/";
            }

            languageCode ??= GetCurrentLanguageCode(viewData, supportedLanguages);
            var targetLanguage = supportedLanguages.FirstOrDefault(x => x.Code == languageCode);

            if (targetLanguage == null)
            {
                var defaultLanguageCode = viewData["DefaultLanguage"] as string ?? supportedLanguages.First().Code;
                targetLanguage = supportedLanguages.FirstOrDefault(x => x.Code == defaultLanguageCode) ?? supportedLanguages.First();
            }

            var urlHelper2 = htmlHelper.ViewContext.HttpContext.RequestServices.GetService(typeof(IUrlHelper)) as IUrlHelper;
            if (urlHelper2 == null)
            {
                // 如果IUrlHelper为null，直接构建URL
                var routeString = routeValues.ContainsKey("id") ? $"/{routeValues["id"]}" : "";
                var actionUrl = $"/Admin/{controllerName}/{actionName}{routeString}";
                return BuildUrlWithBaseUrl(targetLanguage, actionUrl);
            }
            var actionUrlFromHelper = urlHelper2.Action(actionName, controllerName, routeValues) ?? "/";

            return BuildUrlWithBaseUrl(targetLanguage, actionUrlFromHelper);
        }

        /// <summary>
        /// 获取当前语言代码
        /// </summary>
        private static string GetCurrentLanguageCode(ViewDataDictionary viewData, SupportedLanguage[] supportedLanguages)
        {
            var currentLanguage = viewData["CurrentLanguage"]?.ToString();
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                return currentLanguage;
            }

            var defaultLanguageCode = viewData["DefaultLanguage"] as string;
            return defaultLanguageCode ?? supportedLanguages.First().Code;
        }

        /// <summary>
        /// 使用 BaseUrl 构建完整的 URL
        /// </summary>
        private static string BuildUrlWithBaseUrl(SupportedLanguage targetLanguage, string actionUrl)
        {
            var baseUrl = targetLanguage.BaseUrl.TrimEnd('/');

            // 如果 BaseUrl 是绝对 URL (包含协议)，直接拼接
            if (targetLanguage.BaseUrl.StartsWith("http://") || targetLanguage.BaseUrl.StartsWith("https://"))
            {
                return $"{baseUrl}{actionUrl}";
            }

            // 否则是相对路径，需要处理路径合并
            if (baseUrl == "" || baseUrl == "/")
            {
                return actionUrl; // 默认语言，直接返回原始路径
            }

            // 确保 actionUrl 以 / 开头
            if (!actionUrl.StartsWith("/"))
            {
                actionUrl = "/" + actionUrl;
            }

            return $"{baseUrl}{actionUrl}";
        }
    }
}