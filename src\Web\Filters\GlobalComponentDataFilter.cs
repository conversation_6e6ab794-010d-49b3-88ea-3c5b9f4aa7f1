using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Themes;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Filters
{
    /// <summary>
    /// 全局组件数据Action Filter
    /// 为所有Controller自动设置组件ViewData
    /// </summary>
    public class GlobalComponentDataFilter : IAsyncActionFilter
    {
        private readonly IComponentConfigService _componentConfigService;
        private readonly IThemeSettingsService _themeSettingsService;
        private readonly SupportedLanguage[] _supportedLanguages;
        protected readonly IConfiguration _configuration;

        protected readonly string _defaultLanguage;
        protected string _currentLanguage { get; set; }

        /// <summary>
        /// 如果是默认语言，此值为空，主要是为了一些链接不要加 默认语言的 前缀
        /// </summary>
        protected string _currentLanguagePrefix;



        public GlobalComponentDataFilter(
            IComponentConfigService componentConfigService,
            IThemeSettingsService themeSettingsService,
           IConfiguration configuration,
        SupportedLanguage[] supportedLanguages)
        {
            _componentConfigService = componentConfigService;
            _themeSettingsService = themeSettingsService;
            _supportedLanguages = supportedLanguages;
            _configuration = configuration;
            
            _defaultLanguage = configuration.GetValue<string>("DefaultLanguage") ?? "zh";
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // 在执行Action之前设置ViewData，使其在Controller中可用
            if (context.Controller is Controller preController)
            {
                await SetGlobalComponentDataAsync(preController);
                // 需要在设置全局组件数据后追加的逻辑，可写在这里
            }

            // 执行Action
            var result = await next();
        }

        private async Task SetGlobalComponentDataAsync(Controller controller)
        {

            var culture = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;

            if (_supportedLanguages.Any(x => x.Code == culture))
            {
                controller.ViewData["CurrentLanguage"] = culture;
            }
            else
            {
                controller.ViewData["CurrentLanguage"] = _defaultLanguage;
            }


            controller.ViewData["SupportedLanguages"] = _supportedLanguages;
            controller.ViewData["DefaultLanguage"] = _defaultLanguage;
        }
    }
}