using Newtonsoft.Json.Linq;
using System;
using System.Linq;

namespace MlSoft.Sites.Web.Helpers
{
    /// <summary>
    /// JObject数据获取辅助类，用于组件视图中的JSON数据处理
    /// </summary>
    public static class JObjectHelper
    {
        /// <summary>
        /// 获取多语言字符串值
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="currentLanguage">当前语言代码</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>本地化的字符串值</returns>
        public static string GetLocaleStringValue(JObject jObject, string propertyName, string currentLanguage = "en", string defaultValue = "")
        {
            var returnText = string.Empty;
            if (jObject[propertyName] != null)
            {
                var property = jObject[propertyName];

                if (property is JObject localeObject && localeObject[currentLanguage] != null)
                {
                    returnText = localeObject[currentLanguage].ToString();
                }
                else if (property.Type == JTokenType.String)
                {
                    returnText = property.ToString();
                }
            }

            if (string.IsNullOrEmpty(returnText))
            {
                returnText = defaultValue;
            }

            return returnText;
        }

        /// <summary>
        /// 获取媒体文件URL值（从数组中获取第一个文件的路径）
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>媒体文件URL</returns>
        public static string GetMediaUrlValue(JObject jObject, string propertyName, string defaultValue = "")
        {
            var returnText = string.Empty;

            if (jObject[propertyName] != null)
            {
                var property = jObject[propertyName];
                if (property is JArray mediaArray && mediaArray.Count > 0)
                {
                    var firstMedia = mediaArray[0];
                    if (firstMedia is JObject mediaObject && mediaObject["path"] != null)
                    {
                        returnText = mediaObject["path"].ToString();
                    }
                    else if (firstMedia.Type == JTokenType.String)
                    {
                        returnText = firstMedia.ToString();
                    }
                }
                else if (property.Type == JTokenType.String)
                {
                    returnText = property.ToString();
                }
            }

            if (string.IsNullOrEmpty(returnText))
            {
                returnText = defaultValue;
            }

            return returnText;
        }

        /// <summary>
        /// 获取字符串值
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>字符串值</returns>
        public static string GetStringValue(JObject jObject, string propertyName, string defaultValue = "")
        {
            var returnText = string.Empty;

            if (jObject[propertyName] != null)
            {
                returnText= jObject[propertyName].ToString();
            }
            if (string.IsNullOrEmpty(returnText))
            {
                returnText = defaultValue;
            }

            return returnText;
        }

        /// <summary>
        /// 获取布尔值
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>布尔值</returns>
        public static bool GetBoolValue(JObject jObject, string propertyName, bool defaultValue = false)
        {

            if (jObject[propertyName] != null)
            {
                var value = jObject[propertyName];
                if (value.Type == JTokenType.Boolean)
                {
                    return value.ToObject<bool>();
                }
                else if (value.Type == JTokenType.String)
                {
                    return bool.TryParse(value.ToString(), out var result) ? result : defaultValue;
                }
                else if (value.Type == JTokenType.Integer)
                {
                    return value.ToObject<int>() != 0;
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取整数值
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>整数值</returns>
        public static int GetIntValue(JObject jObject, string propertyName, int defaultValue = 0)
        {
            if (jObject[propertyName] != null)
            {
                var value = jObject[propertyName];
                if (value.Type == JTokenType.Integer)
                {
                    return value.ToObject<int>();
                }
                else if (value.Type == JTokenType.String)
                {
                    return int.TryParse(value.ToString(), out var result) ? result : defaultValue;
                }
                else if (value.Type == JTokenType.Float)
                {
                    return (int)value.ToObject<double>();
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取双精度浮点数值
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>双精度浮点数值</returns>
        public static double GetDoubleValue(JObject jObject, string propertyName, double defaultValue = 0.0)
        {
            if (jObject[propertyName] != null)
            {
                var value = jObject[propertyName];
                if (value.Type == JTokenType.Float || value.Type == JTokenType.Integer)
                {
                    return value.ToObject<double>();
                }
                else if (value.Type == JTokenType.String)
                {
                    return double.TryParse(value.ToString(), out var result) ? result : defaultValue;
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取JArray值
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>JArray或null</returns>
        public static JArray? GetArrayValue(JObject jObject, string propertyName)
        {
            if (jObject[propertyName] != null && jObject[propertyName] is JArray array)
            {
                return array;
            }
            return null;
        }

        /// <summary>
        /// 获取JObject值
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>JObject或null</returns>
        public static JObject? GetObjectValue(JObject jObject, string propertyName)
        {
            if (jObject[propertyName] != null && jObject[propertyName] is JObject obj)
            {
                return obj;
            }
            return null;
        }

        /// <summary>
        /// 处理文件路径，确保正确的URL格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>处理后的文件路径</returns>
        public static string ProcessFilePath(string? filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return "";
            if (filePath.StartsWith("http://") || filePath.StartsWith("https://")) return filePath;
            if (!filePath.StartsWith("/")) filePath = "/" + filePath;
            return filePath;
        }

        /// <summary>
        /// 生成唯一ID
        /// </summary>
        /// <param name="prefix">前缀</param>
        /// <returns>唯一ID</returns>
        public static string GenerateId(string prefix = "component")
        {
            return $"{prefix}-{Guid.NewGuid().ToString("N")[..8]}";
        }

        /// <summary>
        /// 获取高度CSS类
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>高度CSS类</returns>
        public static string GetHeightClass(string height)
        {
            return height switch
            {
                "screen" => "h-screen",
                "large" => "h-[400px] lg:h-[600px]",
                "medium" => "h-[360px] lg:h-[400px]",
                "small" => "h-48 lg:h-64",
                "auto" => "h-auto min-h-96",
                _ => "h-auto min-h-96"
            };
        }

        /// <summary>
        /// 获取文本对齐CSS类
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>文本对齐CSS类</returns>
        public static string GetTextAlignmentClass(string alignment)
        {
            return alignment switch
            {
                "left" => "text-left",
                "center" => "text-center",
                "right" => "text-right",
                "justify" => "text-justify",
                _ => "text-center"
            };
        }

        /// <summary>
        /// 获取按钮CSS类
        /// </summary>
        /// <param name="variant">按钮变体</param>
        /// <returns>按钮CSS类</returns>
        public static string GetButtonClass(string variant = "primary")
        {
            return variant switch
            {
                "primary" => "bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200",
                "secondary" => "bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 px-6 py-3 rounded-lg font-medium transition-colors duration-200",
                "outline" => "border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200",
                "ghost" => "text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 px-6 py-3 rounded-lg font-medium transition-colors duration-200",
                "link" => "text-primary-600 hover:text-primary-700 underline font-medium",
                _ => "bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
            };
        }

        /// <summary>
        /// 获取容器大小CSS类
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>容器大小CSS类</returns>
        public static string GetContainerSizeClass(string size)
        {
            return size switch
            {
                "sm" => "max-w-screen-sm",
                "md" => "max-w-screen-md",
                "lg" => "max-w-screen-lg",
                "xl" => "max-w-screen-xl",
                "2xl" => "max-w-screen-2xl",
                "full" => "max-w-full",
                "default" => "max-w-7xl",
                _ => "max-w-7xl"
            };
        }

        /// <summary>
        /// 获取间距CSS类
        /// </summary>
        /// <param name="jObject">JObject数据源</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="direction">方向 (padding, margin, py, px, my, mx)</param>
        /// <returns>间距CSS类</returns>
        public static string GetSpacingClass(string spacing, string direction = "py")
        {
            var spaceValue = spacing switch
            {
                "none" => "0",
                "xs" => "2",
                "sm" => "4",
                "medium" => "8",
                "lg" => "12",
                "xl" => "16",
                "2xl" => "24",
                _ => "8"
            };
            return $"{direction}-{spaceValue}";
        }
    }
}