using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Web.Utilities;
using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Middleware
{
    /// <summary>
    /// Culture middleware that sets the current culture based on route or default configuration
    /// </summary>
    public class CultureMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        private readonly SupportedLanguage[] _supportedLanguages;
        private readonly string _defaultLanguageCode;

        public CultureMiddleware(RequestDelegate next, IConfiguration configuration, SupportedLanguage[] supportedLanguages)
        {
            _next = next;
            _configuration = configuration;
            _supportedLanguages = supportedLanguages;
            _defaultLanguageCode = configuration.GetValue<string>("DefaultLanguage") ?? "en";
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var path = context.Request.Path.Value ?? "";

            // Skip culture processing for static resources and special paths
            if (CulturePathHelper.ShouldSkipCultureProcessing(path))
            {
                await _next(context);
                return;
            }

            var cultureCode = GetCultureFromRoute(context) ?? _defaultLanguageCode;

            // Find the corresponding language configuration
            var targetLanguage = _supportedLanguages.FirstOrDefault(x => x.Code == cultureCode);
            if (targetLanguage == null)
            {
                // Fallback to default language if the route culture is invalid
                cultureCode = _defaultLanguageCode;
                targetLanguage = _supportedLanguages.FirstOrDefault(x => x.Code == cultureCode);
            }

            // Set the culture for the current request
            if (targetLanguage != null)
            {
                var culture = new CultureInfo(targetLanguage.Culture);
                CultureInfo.CurrentCulture = culture;
                CultureInfo.CurrentUICulture = culture;

                // Also set in HttpContext for consistency
                context.Features.Set<IRequestCultureFeature>(new RequestCultureFeature(new RequestCulture(culture), null));
            }

            await _next(context);
        }

        private string? GetCultureFromRoute(HttpContext context)
        {
            // Try to get culture from route data
            var routeValues = context.GetRouteData()?.Values;
            if (routeValues != null && routeValues.ContainsKey("culture"))
            {
                var cultureCode = routeValues["culture"]?.ToString();
                if (!string.IsNullOrEmpty(cultureCode) && _supportedLanguages.Any(x => x.Code == cultureCode))
                {
                    return cultureCode;
                }
            }

            // Try to extract from URL path manually if route data is not available
            return CulturePathHelper.ExtractCultureFromPath(context.Request.Path.Value, _supportedLanguages);
        }
    }
}