<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>9.0</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Views\Shared\Components\Form\**" />
    <Compile Remove="Views\Shared\Components\Media\**" />
    <Compile Remove="Views\Shared\Components\Navigation\**" />
    <Content Remove="Views\Shared\Components\Form\**" />
    <Content Remove="Views\Shared\Components\Media\**" />
    <Content Remove="Views\Shared\Components\Navigation\**" />
    <EmbeddedResource Remove="Views\Shared\Components\Form\**" />
    <EmbeddedResource Remove="Views\Shared\Components\Media\**" />
    <EmbeddedResource Remove="Views\Shared\Components\Navigation\**" />
    <None Remove="Views\Shared\Components\Form\**" />
    <None Remove="Views\Shared\Components\Media\**" />
    <None Remove="Views\Shared\Components\Navigation\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Services\Components\ComponentConfigService - Copy.cs" />
    <Compile Remove="Services\Components\ComponentDataConversionService.cs" />
    <Compile Remove="Services\Components\ComponentVariantService.cs" />
    <Compile Remove="Services\Components\IJsonComponentConfigService.cs" />
    <Compile Remove="Services\Components\JsonComponentConfigService.cs" />
    <Compile Remove="ViewComponents\FormViewComponent.cs" />
    <Compile Remove="ViewComponents\MediaViewComponent.cs" />
    <Compile Remove="ViewComponents\NavigationViewComponent.cs" />
    <Compile Remove="ViewModels\Components\FormComponentViewModel.cs" />
    <Compile Remove="ViewModels\Components\MediaComponentViewModel.cs" />
    <Compile Remove="ViewModels\Components\NavigationComponentViewModel.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Views\Home\Components.cshtml" />
    <Content Remove="Views\Home\ComponentTest.cshtml" />
    <Content Remove="Views\Shared\Components\Header\Corporate.cshtml" />
    <Content Remove="Views\Shared\Components\Header\Minimal.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.Identity.Mongo" Version="10.1.0" />
    <PackageReference Include="MongoDB.Bson" Version="3.4.3" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="WebMarkupMin.AspNetCoreLatest" Version="2.19.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Model\MlSoft.Sites.Model.csproj" />
    <ProjectReference Include="..\Service\MlSoft.Sites.Service.csproj" />
    <ProjectReference Include="..\Utility\MlSoft.Sites.Utility.csproj" />
  </ItemGroup>

<!-- 自动生成JavaScript资源文件 -->
<Target Name="GenerateJsResources" BeforeTargets="Build" Condition="'$(OS)' == 'Windows_NT'">
  <Message Text="Generating JavaScript resource files (improved version)..." Importance="high" />
  <Exec Command="powershell -ExecutionPolicy Bypass -File &quot;$(MSBuildProjectDirectory)/../../Scripts/GenerateJsResourcesImproved.ps1&quot; -ProjectPath &quot;$(MSBuildProjectDirectory)/../..&quot;" ContinueOnError="false" WorkingDirectory="$(MSBuildProjectDirectory)" IgnoreExitCode="false" />
</Target>

<!-- <Target Name="GenerateJsResourcesLinux" BeforeTargets="Build" Condition="'$(OS)' != 'Windows_NT'">
  <Exec Command="bash $(MSBuildProjectDirectory)/../../Scripts/GenerateJsResources.sh" />
</Target> -->

  <!-- 监听资源文件变化 -->
  <ItemGroup>
    <Watch Include="Resources/**/*.resx" />
  </ItemGroup>

</Project>
