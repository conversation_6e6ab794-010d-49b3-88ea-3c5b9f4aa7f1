using AspNetCore.Identity.Mongo;
using AspNetCore.Identity.Mongo.Model;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Service.Business;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.News;
using MlSoft.Sites.Service.Pages;
using MlSoft.Sites.Service.Routing;
using MongoDB.Driver;
using System;
using System.Globalization;
using System.Linq;
using WebMarkupMin.AspNetCoreLatest;

var builder = WebApplication.CreateBuilder(args);

// MongoDB Configuration
var connectionString = builder.Configuration.GetConnectionString("MongoDB") ?? "mongodb://localhost:27017";
var databaseName = builder.Configuration["MongoDB:DatabaseName"] ?? "MlSoftSites";

// Localization Configuration
var supportedLanguages = builder.Configuration.GetSection("SupportedLanguages").Get<SupportedLanguage[]>();

var supportedCultures = supportedLanguages.Select(lang => new CultureInfo(lang.Culture)).ToList();

// 获取默认语言配置
var defaultLanguageCode = builder.Configuration.GetValue<string>("DefaultLanguage") ?? "en";
var defaultLanguage = supportedLanguages.FirstOrDefault(x => x.Code == defaultLanguageCode);
var defaultCulture = defaultLanguage != null
    ? new CultureInfo(defaultLanguage.Culture)
    : new CultureInfo("en-US");

builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    options.DefaultRequestCulture = new RequestCulture(defaultCulture, defaultCulture);
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;

    // Clear all providers - we will handle culture setting in our middleware
    options.RequestCultureProviders.Clear();
});

// Register supported languages configuration
builder.Services.Configure<SupportedLanguage[]>(builder.Configuration.GetSection("SupportedLanguages"));
builder.Services.AddSingleton(supportedLanguages);

builder.Services.AddSingleton<IMongoClient>(sp =>
{
    return new MongoClient(connectionString);
});

builder.Services.AddSingleton<IMongoDatabase>(sp =>
{
    var client = sp.GetRequiredService<IMongoClient>();
    return client.GetDatabase(databaseName);
});

// Identity with MongoDB
builder.Services.AddIdentityMongoDbProvider<MongoUser, MongoRole>(identity =>
{
    identity.Password.RequiredLength = 6;
    identity.Password.RequireDigit = false;
    identity.Password.RequireLowercase = false;
    identity.Password.RequireNonAlphanumeric = false;
    identity.Password.RequireUppercase = false;
    identity.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(10); // 锁定持续时间
    identity.Lockout.MaxFailedAccessAttempts = 3; // 失败次数阈值

}, mongo =>
{
    mongo.ConnectionString = connectionString + "/" + databaseName;
});

// Caching Services
builder.Services.AddMemoryCache();


// Register Services
// Company Services
builder.Services.AddScoped<CompanyService>();
builder.Services.AddScoped<CompanyLocationService>();

// News Services
builder.Services.AddScoped<NewsAnnouncementService>();

// Business Services
builder.Services.AddScoped<ProductServiceService>();
builder.Services.AddScoped<BusinessDivisionService>();

// Organization Services
builder.Services.AddScoped<MlSoft.Sites.Service.Organization.ExecutiveService>();
builder.Services.AddScoped<MlSoft.Sites.Service.Organization.OrganizationStructureService>();

// History Services
builder.Services.AddScoped<MlSoft.Sites.Service.History.CompanyHistoryService>();

// Investor Services
builder.Services.AddScoped<MlSoft.Sites.Service.Investor.FinancialReportService>();
builder.Services.AddScoped<MlSoft.Sites.Service.Investor.ShareholderMeetingService>();

// CSR Services
builder.Services.AddScoped<MlSoft.Sites.Service.CSR.CSRActivityService>();

// Recruitment Services
builder.Services.AddScoped<MlSoft.Sites.Service.Recruitment.JobPositionService>();
builder.Services.AddScoped<MlSoft.Sites.Service.Recruitment.EmployeeInterviewService>();

// Message Services
builder.Services.AddScoped<MlSoft.Sites.Service.Messages.MessageInquiryService>();

// Component Config Service  
builder.Services.AddScoped<MlSoft.Sites.Web.Services.IComponentConfigService, MlSoft.Sites.Web.Services.Components.ComponentConfigService>();


// Page Services
builder.Services.AddScoped<PageConfigurationService>();
builder.Services.AddScoped<PageManageService>();
builder.Services.AddScoped<MlSoft.Sites.Service.Pages.PageVersionService>();

// Routing Services
builder.Services.AddScoped<IDynamicPageRouteService, DynamicPageRouteService>();
builder.Services.AddSingleton<IRouteChangeNotificationService, RouteChangeNotificationService>();

// Settings Services
builder.Services.AddScoped<MlSoft.Sites.Service.Settings.SiteSettingsService>();
builder.Services.AddScoped<MlSoft.Sites.Service.Settings.SEOSettingsService>();
builder.Services.AddScoped<MlSoft.Sites.Service.Settings.ComponentConfigDataService>();

// Theme Services
builder.Services.AddScoped<MlSoft.Sites.Web.Services.Themes.IThemeFileService, MlSoft.Sites.Web.Services.Themes.ThemeFileService>();
builder.Services.AddScoped<MlSoft.Sites.Web.Services.Themes.IThemeSettingsService, MlSoft.Sites.Web.Services.Themes.ThemeSettingsService>();

// Resource Services
builder.Services.AddScoped<MlSoft.Sites.Web.Resources.SharedResource>();
builder.Services.AddScoped<MlSoft.Sites.Web.Resources.AccountResource>();
builder.Services.AddScoped<MlSoft.Sites.Web.Resources.AdminResource>();
builder.Services.AddScoped<MlSoft.Sites.Web.Resources.FormResource>();

// Localization Services
builder.Services.AddScoped<MlSoft.Sites.Web.Services.Localization.ILanguageUrlService, MlSoft.Sites.Web.Services.Localization.LanguageUrlService>();

// Data Seeder
builder.Services.AddScoped<MlSoft.Sites.Service.Seeders.DataSeeder>();
// 注册全局组件数据Filter
builder.Services.AddScoped<MlSoft.Sites.Web.Filters.GlobalComponentDataFilter>();

// ViewRenderService for lazy loading
builder.Services.AddScoped<MlSoft.Sites.Web.Services.IViewRenderService, MlSoft.Sites.Web.Services.ViewRenderService>();
// Add MVC
builder.Services.AddLocalization();
builder.Services.AddControllersWithViews(options =>
{
    // 注册全局组件数据Filter
    options.Filters.Add(typeof(MlSoft.Sites.Web.Filters.GlobalComponentDataFilter));
})
.AddViewLocalization(Microsoft.AspNetCore.Mvc.Razor.LanguageViewLocationExpanderFormat.Suffix)
.AddDataAnnotationsLocalization(options =>
{
    // 使用共享资源文件进行数据注解本地化
    options.DataAnnotationLocalizerProvider = (type, factory) =>
        factory.Create(typeof(MlSoft.Sites.Web.Resources.SharedResource));
});
builder.Services.AddRazorPages();


builder.Services.AddWebMarkupMin()
    .AddHtmlMinification()
    .AddXmlMinification()
    .AddHttpCompression();

// The MVC services automatically register ICompositeViewEngine and ITempDataProvider

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

// Handle 404 errors
app.UseStatusCodePagesWithReExecute("/Home/NotFound");

app.UseHttpsRedirection();
app.UseRouting();

// Use our custom culture middleware after routing so we can access route data
app.UseMiddleware<MlSoft.Sites.Web.Middleware.CultureMiddleware>();

// REMOVE UseRequestLocalization completely since we handle culture in our middleware
// app.UseRequestLocalization();

app.UseAuthentication();
app.UseAuthorization();

app.UseStaticFiles();

// Register dynamic page routes
using (var scope = app.Services.CreateScope())
{
    var dynamicRouteService = scope.ServiceProvider.GetRequiredService<IDynamicPageRouteService>();
    await dynamicRouteService.RegisterPageRoutesAsync(app, defaultLanguageCode);
}

// Multilingual routing
app.MapControllerRoute(
    name: "localized",
    pattern: "{culture}/{controller=Home}/{action=Index}/{id?}/",
    defaults: new { },
    constraints: new { culture = $"^({string.Join("|", supportedLanguages.Select(x => x.Code))})$" });

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}/");

app.MapRazorPages();

// Seed data in development
if (app.Environment.IsDevelopment())
{
    using var scope = app.Services.CreateScope();
    var seeder = scope.ServiceProvider.GetRequiredService<MlSoft.Sites.Service.Seeders.DataSeeder>();
    await seeder.SeedAsync();
}

// Initialize static resources for Data Annotations localization
using (var scope = app.Services.CreateScope())
{
    var adminLocalizer = scope.ServiceProvider.GetRequiredService<Microsoft.Extensions.Localization.IStringLocalizer<MlSoft.Sites.Web.Resources.AdminResource>>();
    MlSoft.Sites.Web.Resources.AdminResource.InitializeStatic(adminLocalizer);
}

app.Run();

