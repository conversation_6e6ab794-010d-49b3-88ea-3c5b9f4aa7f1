using Microsoft.Extensions.Localization;

namespace MlSoft.Sites.Web.Resources
{
    /// <summary>
    /// Admin资源服务 - 同时支持依赖注入和静态访问
    /// </summary>
    public class AdminResource
    {
        private readonly IStringLocalizer<AdminResource> _localizer;
        private static IStringLocalizer<AdminResource>? _staticLocalizer;

        public AdminResource(IStringLocalizer<AdminResource> localizer)
        {
            _localizer = localizer;
            // 每次创建实例都更新静态本地化器，确保语言切换时能正确更新
            _staticLocalizer = localizer;
        }

        /// <summary>
        /// 初始化静态本地化器（用于Data Annotations）
        /// </summary>
        public static void InitializeStatic(IStringLocalizer<AdminResource> localizer)
        {
            _staticLocalizer = localizer;
        }

        // 统一索引器访问（实例方法）
        public string this[string key] => _localizer[key];


        // ===========================================
        // 静态属性（用于Data Annotations）
        // ===========================================

        // ViewModel字段名称
        public static string UserName => _staticLocalizer?["UserName"] ?? "用户名";
        public static string Password => _staticLocalizer?["Password"] ?? "密码";
        public static string RememberMe => _staticLocalizer?["RememberMe"] ?? "记住我";
        public static string CurrentPassword => _staticLocalizer?["CurrentPassword"] ?? "当前密码";
        public static string NewPassword => _staticLocalizer?["NewPassword"] ?? "新密码";
        public static string ConfirmNewPassword => _staticLocalizer?["ConfirmNewPassword"] ?? "确认新密码";
        public static string NewEmailAddress => _staticLocalizer?["NewEmailAddress"] ?? "新邮箱地址";
        public static string ConfirmPassword => _staticLocalizer?["ConfirmPassword"] ?? "确认密码";
        public static string HeaderStyle => _staticLocalizer?["HeaderStyle"] ?? "头部样式";
        public static string FooterStyle => _staticLocalizer?["FooterStyle"] ?? "页脚样式";
        public static string NavigationStyle => _staticLocalizer?["NavigationStyle"] ?? "导航样式";
        
        // SiteSettingsViewModel 需要的静态属性
        public static string SiteName => _staticLocalizer?["SiteName"] ?? "网站名称";
        public static string CompanyName => _staticLocalizer?["CompanyName"] ?? "公司名称";
        public static string CompanyDescription => _staticLocalizer?["CompanyDescription"] ?? "公司简介";
        public static string Philosophy => _staticLocalizer?["Philosophy"] ?? "经营理念";
        public static string EstablishedDate => _staticLocalizer?["EstablishedDate"] ?? "设立日期";
        public static string RegistrationNumber => _staticLocalizer?["RegistrationNumber"] ?? "工商注册号";
        public static string Capital => _staticLocalizer?["Capital"] ?? "注册资本";
        public static string Currency => _staticLocalizer?["Currency"] ?? "货币单位";
        public static string EmployeeScale => _staticLocalizer?["EmployeeScale"] ?? "企业规模";
        public static string EventDate => _staticLocalizer?["EventDate"] ?? "事件日期";
        public static string EventType => _staticLocalizer?["EventType"] ?? "事件类型";
        public static string EventTitle => _staticLocalizer?["EventTitle"] ?? "事件标题";
        public static string EventDescription => _staticLocalizer?["EventDescription"] ?? "事件描述";
        public static string EventImage => _staticLocalizer?["EventImage"] ?? "事件图片";
        public static string DisplayOrder => _staticLocalizer?["DisplayOrder"] ?? "显示顺序";
        public static string ExecutiveName => _staticLocalizer?["ExecutiveName"] ?? "姓名";
        public static string Position => _staticLocalizer?["Position"] ?? "职位";
        public static string Biography => _staticLocalizer?["Biography"] ?? "简历";
        public static string Message => _staticLocalizer?["Message"] ?? "致辞";
        public static string PhotoUrl => _staticLocalizer?["PhotoUrl"] ?? "照片";
        public static string IsPresident => _staticLocalizer?["IsPresident"] ?? "是否为社长";
        public static string DepartmentName => _staticLocalizer?["DepartmentName"] ?? "部门名称";
        public static string DepartmentDescription => _staticLocalizer?["DepartmentDescription"] ?? "部门描述";
        public static string Level => _staticLocalizer?["Level"] ?? "层级";
        public static string Phone => _staticLocalizer?["Phone"] ?? "电话";
        public static string Fax => _staticLocalizer?["Fax"] ?? "传真";
        public static string Email => _staticLocalizer?["Email"] ?? "邮箱";
        public static string Website => _staticLocalizer?["Website"] ?? "网站";
        public static string PostalCode => _staticLocalizer?["PostalCode"] ?? "邮政编码";
        public static string Address => _staticLocalizer?["Address"] ?? "地址";
        public static string BusinessHours => _staticLocalizer?["BusinessHours"] ?? "营业时间";
        public static string AccessInfo => _staticLocalizer?["AccessInfo"] ?? "交通信息";
        public static string Latitude => _staticLocalizer?["Latitude"] ?? "纬度";
        public static string Longitude => _staticLocalizer?["Longitude"] ?? "经度";
        public static string LocationName => _staticLocalizer?["LocationName"] ?? "据点名称";
        public static string LocationType => _staticLocalizer?["LocationType"] ?? "据点类型";
        public static string IsPrimary => _staticLocalizer?["IsPrimary"] ?? "是否为主要据点";
        public static string ActivityTitle => _staticLocalizer?["ActivityTitle"] ?? "活动标题";
        public static string ActivityDescription => _staticLocalizer?["ActivityDescription"] ?? "活动描述";
        public static string ActivitySummary => _staticLocalizer?["ActivitySummary"] ?? "成果总结";
        public static string ActivityImpact => _staticLocalizer?["ActivityImpact"] ?? "活动影响";
        public static string CSRCategory => _staticLocalizer?["CSRCategory"] ?? "CSR分类";
        public static string StartDate => _staticLocalizer?["StartDate"] ?? "开始日期";
        public static string EndDate => _staticLocalizer?["EndDate"] ?? "结束日期";
        public static string ActivityImages => _staticLocalizer?["ActivityImages"] ?? "活动图片";
        public static string ReportFile => _staticLocalizer?["ReportFile"] ?? "报告文件";
        public static string ReportType => _staticLocalizer?["ReportType"] ?? "报告类型";
        public static string ReportPeriod => _staticLocalizer?["ReportPeriod"] ?? "报告期间";
        public static string ReportYear => _staticLocalizer?["ReportYear"] ?? "报告年份";
        public static string Year => _staticLocalizer?["Year"] ?? "年份";
        public static string Quarter => _staticLocalizer?["Quarter"] ?? "季度";
        public static string ReportTitle => _staticLocalizer?["ReportTitle"] ?? "报告标题";
        public static string ReportSummary => _staticLocalizer?["ReportSummary"] ?? "报告摘要";
        public static string PublishDate => _staticLocalizer?["PublishDate"] ?? "发布日期";
        public static string Revenue => _staticLocalizer?["Revenue"] ?? "营业收入";
        public static string NetIncome => _staticLocalizer?["NetIncome"] ?? "净利润";
        public static string TotalAssets => _staticLocalizer?["TotalAssets"] ?? "总资产";
        public static string IsPublished => _staticLocalizer?["IsPublished"] ?? "是否已发布";
        public static string MeetingDate => _staticLocalizer?["MeetingDate"] ?? "会议日期";
        public static string MeetingTitle => _staticLocalizer?["MeetingTitle"] ?? "会议标题";
        public static string MeetingDescription => _staticLocalizer?["MeetingDescription"] ?? "会议描述";
        public static string MeetingAgenda => _staticLocalizer?["MeetingAgenda"] ?? "会议议程";
        public static string MeetingLocation => _staticLocalizer?["MeetingLocation"] ?? "会议地点";
        public static string MeetingStatus => _staticLocalizer?["MeetingStatus"] ?? "会议状态";
        public static string MeetingDocuments => _staticLocalizer?["MeetingDocuments"] ?? "会议文档";
        public static string DocumentTitle => _staticLocalizer?["DocumentTitle"] ?? "文档标题";
        public static string DocumentDescription => _staticLocalizer?["DocumentDescription"] ?? "文档描述";
        public static string DocumentType => _staticLocalizer?["DocumentType"] ?? "文档类型";
        public static string DocumentFile => _staticLocalizer?["DocumentFile"] ?? "文档文件";

        public static string Domain => _staticLocalizer?["Domain"] ?? "域名";
        public static string DefaultLanguage => _staticLocalizer?["DefaultLanguage"] ?? "默认语言";
        public static string Logo => _staticLocalizer?["Logo"] ?? "Logo";
        public static string Favicon => _staticLocalizer?["Favicon"] ?? "网站图标";

        // ViewModel验证消息
        public static string DomainRequired => _staticLocalizer?["DomainRequired"] ?? "域名不能为空";
        public static string SiteNameRequired => _staticLocalizer?["SiteNameRequired"] ?? "网站名称不能为空";
        public static string UserNameRequired => _staticLocalizer?["UserNameRequired"] ?? "用户名不能为空";
        public static string PasswordRequired => _staticLocalizer?["PasswordRequired"] ?? "密码不能为空";
        public static string CurrentPasswordRequired => _staticLocalizer?["CurrentPasswordRequired"] ?? "当前密码不能为空";
        public static string NewPasswordRequired => _staticLocalizer?["NewPasswordRequired"] ?? "新密码不能为空";
        public static string PasswordLengthError => _staticLocalizer?["PasswordLengthError"] ?? "密码长度至少6位";
        public static string PasswordMismatch => _staticLocalizer?["PasswordMismatch"] ?? "密码确认不匹配";
        public static string NewEmailRequired => _staticLocalizer?["NewEmailRequired"] ?? "新邮箱地址不能为空";
        public static string InvalidEmailFormat => _staticLocalizer?["InvalidEmailFormat"] ?? "邮箱格式无效";
        public static string DomainMaxLength => _staticLocalizer?["DomainMaxLength"] ?? "域名长度不能超过200字符";
        public static string DefaultLanguageRequired => _staticLocalizer?["DefaultLanguageRequired"] ?? "必须选择默认语言";
        public static string EstablishedDateRequired => _staticLocalizer?["EstablishedDateRequired"] ?? "设立日期不能为空";
        public static string EventDateRequired => _staticLocalizer?["EventDateRequired"] ?? "事件日期不能为空";
        public static string EventTypeRequired => _staticLocalizer?["EventTypeRequired"] ?? "事件类型不能为空";
        public static string EmailInvalid => _staticLocalizer?["EmailInvalid"] ?? "邮箱格式无效";
        public static string WebsiteInvalid => _staticLocalizer?["WebsiteInvalid"] ?? "网站地址格式无效";
        public static string ConfirmPasswordRequired => _staticLocalizer?["ConfirmPasswordRequired"] ?? "确认密码不能为空";
    }
}