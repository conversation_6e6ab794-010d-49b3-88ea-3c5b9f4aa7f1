<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="SiteSettingsTitle" xml:space="preserve">
    <value>Site Settings</value>
  </data>
  <data name="SiteNameRequired" xml:space="preserve">
    <value>Site name is required</value>
  </data>
  <data name="DomainRequired" xml:space="preserve">
    <value>Domain is required</value>
  </data>
  <data name="LoadSiteSettingsError" xml:space="preserve">
    <value>Error loading site settings, please try again later.</value>
  </data>
  <data name="InputDataError" xml:space="preserve">
    <value>Input data is invalid, please check and try again.</value>
  </data>
  <data name="BasicInfoSaved" xml:space="preserve">
    <value>Basic information settings saved successfully!</value>
  </data>
  <data name="SaveSettingsError" xml:space="preserve">
    <value>Error occurred while saving settings</value>
  </data>
  <data name="SelectTheme" xml:space="preserve">
    <value>Please select a theme to apply</value>
  </data>
  <data name="InvalidTheme" xml:space="preserve">
    <value>Selected theme is invalid or does not exist</value>
  </data>
  <data name="ThemeAppliedSuccess" xml:space="preserve">
    <value>Theme applied successfully! The page will refresh automatically to show the new theme effect.</value>
  </data>
  <data name="ThemeApplyFailed" xml:space="preserve">
    <value>Failed to apply theme, please try again later.</value>
  </data>
  <data name="SystemError" xml:space="preserve">
    <value>System Error</value>
  </data>
  <data name="ComponentSettingsSaved" xml:space="preserve">
    <value>Component settings saved successfully!</value>
  </data>
  <data name="SelectFile" xml:space="preserve">
    <value>Please select a file to upload</value>
  </data>
  <data name="UnsupportedFileType" xml:space="preserve">
    <value>Unsupported file type, please upload an image file</value>
  </data>
  <data name="FileSizeLimit" xml:space="preserve">
    <value>File size cannot exceed 5MB</value>
  </data>
  <data name="UploadConfigError" xml:space="preserve">
    <value>Upload configuration error</value>
  </data>
  <data name="ImageProcessFailed" xml:space="preserve">
    <value>Image processing failed</value>
  </data>
  <data name="UploadSuccess" xml:space="preserve">
    <value>Upload successful</value>
  </data>
  <data name="UploadFailed" xml:space="preserve">
    <value>Upload failed</value>
  </data>
  <data name="LogoFileNameEmpty" xml:space="preserve">
    <value>Logo filename cannot be empty</value>
  </data>
  <data name="LogoFileNotExists" xml:space="preserve">
    <value>Logo file does not exist</value>
  </data>
  <data name="GenerateFaviconFailed" xml:space="preserve">
    <value>Failed to generate website icon</value>
  </data>
  <data name="FaviconGenerateSuccess" xml:space="preserve">
    <value>Website icon generated successfully</value>
  </data>
  <data name="BasicInfo" xml:space="preserve">
    <value>Basic Info</value>
  </data>
  <data name="ThemeSettings" xml:space="preserve">
    <value>Theme Settings</value>
  </data>
  <data name="ComponentSettings" xml:space="preserve">
    <value>Component Settings</value>
  </data>
  <data name="BasicInfoSettingsTitle" xml:space="preserve">
    <value>Basic Information Settings</value>
  </data>
  <data name="SiteName" xml:space="preserve">
    <value>Site Name</value>
  </data>
  <data name="Domain" xml:space="preserve">
    <value>Domain</value>
  </data>
  <data name="DefaultLanguage" xml:space="preserve">
    <value>Default Language</value>
  </data>
  <data name="Logo" xml:space="preserve">
    <value>Logo</value>
  </data>
  <data name="Favicon" xml:space="preserve">
    <value>Favicon</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save Settings</value>
  </data>
  <data name="GenerateFavicon" xml:space="preserve">
    <value>Generate from Logo</value>
  </data>
  <data name="SelectLogo" xml:space="preserve">
    <value>Select Logo</value>
  </data>
  <data name="SelectFavicon" xml:space="preserve">
    <value>Select Favicon</value>
  </data>
  <data name="ClickUploadLogo" xml:space="preserve">
    <value>Click to upload File</value>
  </data>
  <data name="ClickUploadFavicon" xml:space="preserve">
    <value>Click to upload icon</value>
  </data>
  <data name="SaveBasicInfo" xml:space="preserve">
    <value>Save Basic Info</value>
  </data>
  <data name="ThemeSettingsTitle" xml:space="preserve">
    <value>Theme Settings</value>
  </data>
  <data name="PleaseWaitProcessing" xml:space="preserve">
    <value>Please wait, processing...</value>
  </data>
  <data name="ApplyingTheme" xml:space="preserve">
    <value>Applying theme...</value>
  </data>
  <data name="SavingBasicInfo" xml:space="preserve">
    <value>Saving basic information...</value>
  </data>
  <data name="SavingComponentSettings" xml:space="preserve">
    <value>Saving component settings...</value>
  </data>
  <data name="GeneratingFaviconFromLogo" xml:space="preserve">
    <value>Generating favicon from logo...</value>
  </data>
  <data name="UploadingLogo" xml:space="preserve">
    <value>Uploading logo...</value>
  </data>
  <data name="UploadingFavicon" xml:space="preserve">
    <value>Uploading favicon...</value>
  </data>
  <data name="ApplyThemeError" xml:space="preserve">
    <value>Error applying theme, please try again later</value>
  </data>
  <data name="SaveBasicInfoError" xml:space="preserve">
    <value>Error saving basic information, please try again later</value>
  </data>
  <data name="SaveComponentSettingsError" xml:space="preserve">
    <value>Error saving component settings, please try again later</value>
  </data>
  <data name="GenerateFaviconError" xml:space="preserve">
    <value>Error generating favicon</value>
  </data>
  <data name="PleaseUploadLogoFirst" xml:space="preserve">
    <value>Please upload logo first</value>
  </data>
  <data name="FaviconGeneratedSuccess" xml:space="preserve">
    <value>Favicon generated successfully!</value>
  </data>
  <data name="UploadSuccessful" xml:space="preserve">
    <value>Upload successful!</value>
  </data>
  <data name="BackendManagement" xml:space="preserve">
    <value>Admin Panel</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>System Settings</value>
  </data>
  <data name="SiteSettings" xml:space="preserve">
    <value>Site Settings</value>
  </data>
  <data name="AccountSettings" xml:space="preserve">
    <value>Account Settings</value>
  </data>
  <data name="FrontendPage" xml:space="preserve">
    <value>Frontend</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="WelcomeAdmin" xml:space="preserve">
    <value>Welcome, Administrator</value>
  </data>
  <data name="AccountSettingsTitle" xml:space="preserve">
    <value>Account Settings</value>
  </data>
  <data name="ChangePasswordTitle" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="UpdateYourAccountPassword" xml:space="preserve">
    <value>Update your account password</value>
  </data>
  <data name="ChangeEmailTitle" xml:space="preserve">
    <value>Change Email</value>
  </data>
  <data name="UpdateYourEmailAddress" xml:space="preserve">
    <value>Update your email address</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>System Information</value>
  </data>
  <data name="SystemVersion" xml:space="preserve">
    <value>System Version</value>
  </data>
  <data name="RuntimeEnvironment" xml:space="preserve">
    <value>Runtime Environment</value>
  </data>
  <data name="Database" xml:space="preserve">
    <value>Database</value>
  </data>
  <data name="Authentication" xml:space="preserve">
    <value>Authentication</value>
  </data>
  <data name="DashboardTitle" xml:space="preserve">
    <value>Admin Dashboard</value>
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value>Welcome Back</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Remember Me</value>
  </data>
  <data name="CurrentPassword" xml:space="preserve">
    <value>Current Password</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="ConfirmNewPassword" xml:space="preserve">
    <value>Confirm New Password</value>
  </data>
  <data name="NewEmailAddress" xml:space="preserve">
    <value>New Email Address</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="HeaderStyle" xml:space="preserve">
    <value>Header Style</value>
  </data>
  <data name="FooterStyle" xml:space="preserve">
    <value>Footer Style</value>
  </data>
  <data name="NavigationStyle" xml:space="preserve">
    <value>Navigation Style</value>
  </data>
  <data name="UserNameRequired" xml:space="preserve">
    <value>Username is required</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>Password is required</value>
  </data>
  <data name="CurrentPasswordRequired" xml:space="preserve">
    <value>Current password is required</value>
  </data>
  <data name="NewPasswordRequired" xml:space="preserve">
    <value>New password is required</value>
  </data>
  <data name="PasswordLengthError" xml:space="preserve">
    <value>Password must be at least 6 characters long</value>
  </data>
  <data name="PasswordMismatch" xml:space="preserve">
    <value>Password confirmation does not match</value>
  </data>
  <data name="NewEmailRequired" xml:space="preserve">
    <value>New email address is required</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>Invalid email address format</value>
  </data>
  <data name="DomainMaxLength" xml:space="preserve">
    <value>Domain cannot exceed 200 characters</value>
  </data>
  <data name="DefaultLanguageRequired" xml:space="preserve">
    <value>Default language must be selected</value>
  </data>
  <data name="ConfirmPasswordRequired" xml:space="preserve">
    <value>Password confirmation is required</value>
  </data>
  <data name="CurrentTheme" xml:space="preserve">
    <value>Current Theme</value>
  </data>
  <data name="ApplyTheme" xml:space="preserve">
    <value>Apply Theme</value>
  </data>
  <data name="CurrentInUse" xml:space="preserve">
    <value>Currently In Use</value>
  </data>
  <data name="CountCode" xml:space="preserve">
    <value>Count Code</value>
  </data>
  <data name="GlobalComponentSettings" xml:space="preserve">
    <value>Global Component Settings</value>
  </data>
  <data name="HeaderComponent" xml:space="preserve">
    <value>Header Component</value>
  </data>
  <data name="FooterComponent" xml:space="preserve">
    <value>Footer Component</value>
  </data>
  <data name="CookieComponent" xml:space="preserve">
    <value>Cookie Component</value>
  </data>
  <data name="NavigationComponent" xml:space="preserve">
    <value>Navigation Component</value>
  </data>
  <data name="SaveComponentSettings" xml:space="preserve">
    <value>Save Component Settings</value>
  </data>
  <data name="Processing" xml:space="preserve">
    <value>Processing...</value>
  </data>
  <data name="PleaseWaitSavingSettings" xml:space="preserve">
    <value>Please wait, saving settings</value>
  </data>
  <data name="PageConfigurationTitle" xml:space="preserve">
    <value>Page Configuration</value>
  </data>
  <data name="PageConfigurationList" xml:space="preserve">
    <value>Page Configuration List</value>
  </data>
  <data name="PageConfigurationListDescription" xml:space="preserve">
    <value>Manage and configure website page structure and components</value>
  </data>
  <data name="CreateNewPage" xml:space="preserve">
    <value>Create New Page</value>
  </data>
  <data name="EditPage" xml:space="preserve">
    <value>Edit Page</value>
  </data>
  <data name="PageName" xml:space="preserve">
    <value>Page Name</value>
  </data>
  <data name="PageKey" xml:space="preserve">
    <value>Page Key</value>
  </data>
  <data name="Route" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="ComponentCount" xml:space="preserve">
    <value>Component Count</value>
  </data>
  <data name="LastUpdated" xml:space="preserve">
    <value>Last Updated</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Publish" xml:space="preserve">
    <value>Publish</value>
  </data>
  <data name="Unpublish" xml:space="preserve">
    <value>Unpublish</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Published</value>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>Draft</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PageEditor" xml:space="preserve">
    <value>Page Editor</value>
  </data>
  <data name="ComponentLibrary" xml:space="preserve">
    <value>Component Library</value>
  </data>
  <data name="PropertiesPanel" xml:space="preserve">
    <value>Properties Panel</value>
  </data>
  <data name="AddComponent" xml:space="preserve">
    <value>Add Component</value>
  </data>
  <data name="SavePage" xml:space="preserve">
    <value>Save Page</value>
  </data>
  <data name="PreviewPage" xml:space="preserve">
    <value>Preview Page</value>
  </data>
  <data name="DeleteConfirm" xml:space="preserve">
    <value>Are you sure you want to delete this page? This action cannot be undone.</value>
  </data>
  <data name="ComponentConfiguration" xml:space="preserve">
    <value>Component Configuration</value>
  </data>
  <data name="MoveUp" xml:space="preserve">
    <value>Move Up</value>
  </data>
  <data name="MoveDown" xml:space="preserve">
    <value>Move Down</value>
  </data>
  <data name="RemoveComponent" xml:space="preserve">
    <value>Remove Component</value>
  </data>
  <data name="ColumnSpan" xml:space="preserve">
    <value>Column Span</value>
  </data>
  <data name="AllStatus" xml:space="preserve">
    <value>All Status</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Components" xml:space="preserve">
    <value>Components</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="ConfirmUnpublish" xml:space="preserve">
    <value>Are you sure you want to unpublish this page?</value>
  </data>
  <data name="ConfirmPublish" xml:space="preserve">
    <value>Are you sure you want to publish this page?</value>
  </data>
  <data name="ShowingResults" xml:space="preserve">
    <value>Showing {0} - {1} of {2} results</value>
  </data>
  <data name="NoPages" xml:space="preserve">
    <value>No Pages</value>
  </data>
  <data name="NoPagesDescription" xml:space="preserve">
    <value>No pages have been created yet. Click the button below to create your first page.</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="PageName_Zh" xml:space="preserve">
    <value>Chinese Name</value>
  </data>
  <data name="PageName_En" xml:space="preserve">
    <value>English Name</value>
  </data>
  <data name="PageName_Ja" xml:space="preserve">
    <value>Japanese Name</value>
  </data>
  <data name="EnterPageName_Zh" xml:space="preserve">
    <value>Enter Chinese page name</value>
  </data>
  <data name="EnterPageName_En" xml:space="preserve">
    <value>Enter English page name</value>
  </data>
  <data name="EnterPageName_Ja" xml:space="preserve">
    <value>Enter Japanese page name</value>
  </data>
  <data name="EnterPageKey" xml:space="preserve">
    <value>Enter page key</value>
  </data>
  <data name="PageKeyHelp" xml:space="preserve">
    <value>Page key is used for internal identification, only letters, numbers, underscores and hyphens allowed</value>
  </data>
  <data name="EnterRoute" xml:space="preserve">
    <value>Enter route</value>
  </data>
  <data name="RouteHelp" xml:space="preserve">
    <value>Page access path, must start with /, e.g.: /about</value>
  </data>
  <data name="LayoutTemplate" xml:space="preserve">
    <value>Layout Template</value>
  </data>
  <data name="PublishStatus" xml:space="preserve">
    <value>Publish Status</value>
  </data>
  <data name="PerformanceSettings" xml:space="preserve">
    <value>Performance Settings</value>
  </data>
  <data name="CacheSettings" xml:space="preserve">
    <value>Cache Settings</value>
  </data>
  <data name="EnableImageLazyLoading" xml:space="preserve">
    <value>Enable Image Lazy Loading</value>
  </data>
  <data name="EnableComponentLazyLoading" xml:space="preserve">
    <value>Enable Component Lazy Loading</value>
  </data>
  <data name="ImageQuality" xml:space="preserve">
    <value>Image Quality</value>
  </data>
  <data name="CacheDuration" xml:space="preserve">
    <value>Cache Duration</value>
  </data>
  <data name="Minutes" xml:space="preserve">
    <value>Minutes</value>
  </data>
  <data name="EnableOutputCache" xml:space="preserve">
    <value>Enable Output Cache</value>
  </data>
  <data name="CreatePage" xml:space="preserve">
    <value>Create Page</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>Save Changes</value>
  </data>
  <data name="NoComponentsYet" xml:space="preserve">
    <value>No components added yet</value>
  </data>
  <data name="ClickAddComponentToStart" xml:space="preserve">
    <value>Click "Add Component" button to start building the page</value>
  </data>
  <data name="PublishedOn" xml:space="preserve">
    <value>Published on</value>
  </data>
  <data name="QuickActions" xml:space="preserve">
    <value>Quick Actions</value>
  </data>
  <data name="LoadPageListError" xml:space="preserve">
    <value>Error loading page list</value>
  </data>
  <data name="PageKeyAlreadyExists" xml:space="preserve">
    <value>Page key already exists, please use a different key</value>
  </data>
  <data name="RouteAlreadyExists" xml:space="preserve">
    <value>Route already exists, please use a different route path</value>
  </data>
  <data name="PageCreateSuccess" xml:space="preserve">
    <value>Page created successfully!</value>
  </data>
  <data name="PageCreateError" xml:space="preserve">
    <value>Failed to create page, please check input and try again</value>
  </data>
  <data name="LoadPageError" xml:space="preserve">
    <value>Error loading page information</value>
  </data>
  <data name="PageUpdateSuccess" xml:space="preserve">
    <value>Page updated successfully!</value>
  </data>
  <data name="PageUpdateError" xml:space="preserve">
    <value>Failed to update page, please try again later</value>
  </data>
  <data name="PageDeleteSuccess" xml:space="preserve">
    <value>Page deleted successfully!</value>
  </data>
  <data name="PageDeleteError" xml:space="preserve">
    <value>Failed to delete page, please try again later</value>
  </data>
  <data name="PagePublishSuccess" xml:space="preserve">
    <value>Page published successfully!</value>
  </data>
  <data name="PagePublishError" xml:space="preserve">
    <value>Failed to publish page, please try again later</value>
  </data>
  <data name="PageUnpublishSuccess" xml:space="preserve">
    <value>Page unpublished successfully!</value>
  </data>
  <data name="PageUnpublishError" xml:space="preserve">
    <value>Failed to unpublish page, please try again later</value>
  </data>
  <data name="LoadComponentDataError" xml:space="preserve">
    <value>Error loading component data</value>
  </data>
  <data name="ComponentDescription" xml:space="preserve">
    <value>{0} Component</value>
  </data>
  <data name="GeneralCategory" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="DropComponentHere" xml:space="preserve">
    <value>Drop component here</value>
  </data>
  <data name="MoveComponentHere" xml:space="preserve">
    <value>Drop component here</value>
  </data>
  <data name="SelectComponentToEdit" xml:space="preserve">
    <value>Select component to edit</value>
  </data>
  <data name="ClickComponentToShowProperties" xml:space="preserve">
    <value>Click Component To Show Properties</value>
  </data>
  <data name="DropHere" xml:space="preserve">
    <value>Drop Here</value>
  </data>
  <data name="LoadingTemplates" xml:space="preserve">
    <value>Loading Templates...</value>
  </data>
  <data name="ComponentInfo" xml:space="preserve">
    <value>Component Info</value>
  </data>
  <data name="ComponentType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Template" xml:space="preserve">
    <value>Template</value>
  </data>
  <data name="DisplayOrder" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="ComponentVisible" xml:space="preserve">
    <value>Component Visible</value>
  </data>
  <data name="ComponentParameters" xml:space="preserve">
    <value>Component Parameters</value>
  </data>
  <data name="EditComponent" xml:space="preserve">
    <value>Edit Component</value>
  </data>
  <data name="DeleteComponent" xml:space="preserve">
    <value>Delete Component</value>
  </data>
  <data name="DeleteComponentConfirm" xml:space="preserve">
    <value>Are you sure you want to delete this component?</value>
  </data>
  <data name="ComponentParametersJson" xml:space="preserve">
    <value>Component Parameters (JSON)</value>
  </data>
  <data name="VisibilitySettings" xml:space="preserve">
    <value>Visibility Settings</value>
  </data>
  <data name="Visible" xml:space="preserve">
    <value>Visible</value>
  </data>
  <data name="Hidden" xml:space="preserve">
    <value>Hidden</value>
  </data>
  <data name="DragComponentsToPage" xml:space="preserve">
    <value>Drag components to page area</value>
  </data>
  <data name="SearchComponents" xml:space="preserve">
    <value>Search components</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="EmptyPageCanvas" xml:space="preserve">
    <value>Empty Canvas</value>
  </data>
  <data name="DragComponentsHere" xml:space="preserve">
    <value>Drag components here to start building your page</value>
  </data>
  <data name="ToggleVisibility" xml:space="preserve">
    <value>Toggle Visibility</value>
  </data>
  <data name="DragToReorder" xml:space="preserve">
    <value>Drag to Reorder</value>
  </data>
  <data name="ComponentProperties" xml:space="preserve">
    <value>Component Properties</value>
  </data>
  <data name="BackToPageList" xml:space="preserve">
    <value>Back to Page List</value>
  </data>
  <data name="WebsiteManage" xml:space="preserve">
    <value>Website Manage</value>
  </data>
  <data name="PageContentManage" xml:space="preserve">
    <value>Content Management</value>
  </data>
  <data name="LoadPageContentListError" xml:space="preserve">
    <value>Error loading page content list. Please try again later.</value>
  </data>
  <data name="LoadPageContentError" xml:space="preserve">
    <value>Error loading page content. Please try again later.</value>
  </data>
  <data name="PageContentSaveSuccess" xml:space="preserve">
    <value>Page content saved successfully!</value>
  </data>
  <data name="PageContentSaveError" xml:space="preserve">
    <value>Error saving page content. Please try again later.</value>
  </data>
  <data name="PageContentPublishSuccess" xml:space="preserve">
    <value>Page content published successfully!</value>
  </data>
  <data name="PageContentPublishError" xml:space="preserve">
    <value>Error publishing page content. Please try again later.</value>
  </data>
  <data name="PageContentUnpublishSuccess" xml:space="preserve">
    <value>Content unpublished successfully!</value>
  </data>
  <data name="PageContentUnpublishError" xml:space="preserve">
    <value>Error unpublishing content. Please try again later.</value>
  </data>
  <data name="EditPageContent" xml:space="preserve">
    <value>Edit Page Content</value>
  </data>
  <data name="SavePageContent" xml:space="preserve">
    <value>Save Content</value>
  </data>
  <data name="PublishPageContent" xml:space="preserve">
    <value>Publish Content</value>
  </data>
  <data name="UnpublishPageContent" xml:space="preserve">
    <value>Unpublish</value>
  </data>
  <data name="PreviewPageContent" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="ComponentContent" xml:space="preserve">
    <value>Component Content</value>
  </data>
  <data name="MultilingualContent" xml:space="preserve">
    <value>Multilingual Content</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>Chinese</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>Japanese</value>
  </data>
  <data name="ContentRequired" xml:space="preserve">
    <value>Content cannot be empty</value>
  </data>
  <data name="NoComponents" xml:space="preserve">
    <value>This page has no component configuration</value>
  </data>
  <data name="PageContentStatus" xml:space="preserve">
    <value>Content Status</value>
  </data>
  <data name="Review" xml:space="preserve">
    <value>Under Review</value>
  </data>
  <data name="Archived" xml:space="preserve">
    <value>Archived</value>
  </data>
  <data name="InvalidContentId" xml:space="preserve">
    <value>Invalid content ID</value>
  </data>
  <data name="FailedToLoadFormFields" xml:space="preserve">
    <value>Failed to load form fields configuration</value>
  </data>
  <data name="FailedToLoadMultilingualFields" xml:space="preserve">
    <value>Failed to load multilingual fields</value>
  </data>
  <data name="ManagePageContentData" xml:space="preserve">
    <value>Manage specific content data for configured pages</value>
  </data>
  <data name="SearchPageKeywords" xml:space="preserve">
    <value>Search page keywords...</value>
  </data>
  <data name="NoContentCreated" xml:space="preserve">
    <value>No content created</value>
  </data>
  <data name="ComponentsCount" xml:space="preserve">
    <value> components</value>
  </data>
  <data name="CreateContent" xml:space="preserve">
    <value>Create Content</value>
  </data>
  <data name="NoComponentsConfigured" xml:space="preserve">
    <value>This page has no components configured yet, please add components in page configuration first.</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>Is Active</value>
  </data>
  <data name="SelectEventType" xml:space="preserve">
    <value>Please select event type</value>
  </data>
  <data name="Establishment" xml:space="preserve">
    <value>Establishment</value>
  </data>
  <data name="Expansion" xml:space="preserve">
    <value>Expansion</value>
  </data>
  <data name="ProductLaunch" xml:space="preserve">
    <value>Product Launch</value>
  </data>
  <data name="Acquisition" xml:space="preserve">
    <value>Acquisition</value>
  </data>
  <data name="Partnership" xml:space="preserve">
    <value>Partnership</value>
  </data>
  <data name="Award" xml:space="preserve">
    <value>Award</value>
  </data>
  <data name="Milestone" xml:space="preserve">
    <value>Milestone</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="EditHistoryEvent" xml:space="preserve">
    <value>Edit History Event</value>
  </data>
  <data name="EventTitleZhRequired" xml:space="preserve">
    <value>Chinese event title is required</value>
  </data>
  <data name="LoadTabError" xml:space="preserve">
    <value>Failed to load tab content</value>
  </data>
  <data name="LoadHistoryError" xml:space="preserve">
    <value>Failed to load history record</value>
  </data>
  <data name="HistoryTabNotLoaded" xml:space="preserve">
    <value>Please load the company history tab first</value>
  </data>
  <data name="LoadingHistoryData" xml:space="preserve">
    <value>Loading history data...</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>Retry</value>
  </data>
  <data name="ExecutiveInformation" xml:space="preserve">
    <value>Executive Information</value>
  </data>
  <data name="AddExecutive" xml:space="preserve">
    <value>Add Executive</value>
  </data>
  <data name="IsPresident" xml:space="preserve">
    <value>Is President</value>
  </data>
  <data name="President" xml:space="preserve">
    <value>President</value>
  </data>
  <data name="Executive" xml:space="preserve">
    <value>Executive</value>
  </data>
  <data name="NoExecutiveRecords" xml:space="preserve">
    <value>No executive records</value>
  </data>
  <data name="OrganizationStructure" xml:space="preserve">
    <value>Organization Structure</value>
  </data>
  <data name="AddDepartment" xml:space="preserve">
    <value>Add Department</value>
  </data>
  <data name="RootDepartment" xml:space="preserve">
    <value>Root Department</value>
  </data>
  <data name="NoOrganizationRecords" xml:space="preserve">
    <value>No organization records</value>
  </data>
  <data name="BasicContactInfo" xml:space="preserve">
    <value>Basic Contact Information</value>
  </data>
  <data name="EditContactInfo" xml:space="preserve">
    <value>Edit Contact Info</value>
  </data>
  <data name="NoContactInfo" xml:space="preserve">
    <value>No contact information configured</value>
  </data>
  <data name="CompanyLocations" xml:space="preserve">
    <value>Company Locations</value>
  </data>
  <data name="AddLocation" xml:space="preserve">
    <value>Add Location</value>
  </data>
  <data name="LocationCoordinates" xml:space="preserve">
    <value>Location Coordinates</value>
  </data>
  <data name="Headquarters" xml:space="preserve">
    <value>Headquarters</value>
  </data>
  <data name="PrimaryLocation" xml:space="preserve">
    <value>Primary Location</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="Factory" xml:space="preserve">
    <value>Factory</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>Office</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="ResearchCenter" xml:space="preserve">
    <value>Research Center</value>
  </data>
  <data name="NoLocationRecords" xml:space="preserve">
    <value>No location records</value>
  </data>
  <data name="Laboratory" xml:space="preserve">
    <value>Laboratory</value>
  </data>
  <data name="ViewOnMap" xml:space="preserve">
    <value>View on Map</value>
  </data>
  <data name="CSRActivities" xml:space="preserve">
    <value>CSR Activities</value>
  </data>
  <data name="AddCSRActivity" xml:space="preserve">
    <value>Add CSR Activity</value>
  </data>
  <data name="Environment" xml:space="preserve">
    <value>Environment Protection</value>
  </data>
  <data name="SocialContribution" xml:space="preserve">
    <value>Social Contribution</value>
  </data>
  <data name="Governance" xml:space="preserve">
    <value>Governance</value>
  </data>
  <data name="CommunitySupport" xml:space="preserve">
    <value>Community Support</value>
  </data>
  <data name="EmployeeWelfare" xml:space="preserve">
    <value>Employee Welfare</value>
  </data>
  <data name="DisasterRelief" xml:space="preserve">
    <value>Disaster Relief</value>
  </data>
  <data name="Education" xml:space="preserve">
    <value>Education</value>
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value>Healthcare</value>
  </data>
  <data name="Ongoing" xml:space="preserve">
    <value>Ongoing</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="NoCSRActivityRecords" xml:space="preserve">
    <value>No CSR activity records</value>
  </data>
  <data name="TotalActivities" xml:space="preserve">
    <value>Total Activities</value>
  </data>
  <data name="OngoingActivities" xml:space="preserve">
    <value>Ongoing Activities</value>
  </data>
  <data name="ActivitiesWithReports" xml:space="preserve">
    <value>Activities with Reports</value>
  </data>
  <data name="FinancialReports" xml:space="preserve">
    <value>Financial Reports</value>
  </data>
  <data name="ManageFinancialReportsDescription" xml:space="preserve">
    <value>Manage corporate financial reports, annual reports, quarterly reports and other documents</value>
  </data>
  <data name="AddFinancialReport" xml:space="preserve">
    <value>Add Financial Report</value>
  </data>
  <data name="AnnualReport" xml:space="preserve">
    <value>Annual Report</value>
  </data>
  <data name="QuarterlyReport" xml:space="preserve">
    <value>Quarterly Report</value>
  </data>
  <data name="EarningsRelease" xml:space="preserve">
    <value>Earnings Release</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>Period</value>
  </data>
  <data name="NoFinancialReportRecords" xml:space="preserve">
    <value>No financial report records</value>
  </data>
  <data name="ShareholderMeetings" xml:space="preserve">
    <value>Shareholder Meetings</value>
  </data>
  <data name="ManageShareholderMeetingsDescription" xml:space="preserve">
    <value>Manage shareholder meetings, meeting documents and related information</value>
  </data>
  <data name="AddShareholderMeeting" xml:space="preserve">
    <value>Add Shareholder Meeting</value>
  </data>
  <data name="MeetingTitle" xml:space="preserve">
    <value>Meeting Title</value>
  </data>
  <data name="Scheduled" xml:space="preserve">
    <value>Scheduled</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>In Progress</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="Documents" xml:space="preserve">
    <value>Documents</value>
  </data>
  <data name="NoDocuments" xml:space="preserve">
    <value>No Documents</value>
  </data>
  <data name="NoShareholderMeetingRecords" xml:space="preserve">
    <value>No shareholder meeting records</value>
  </data>
  <data name="TotalFinancialReports" xml:space="preserve">
    <value>Total Reports</value>
  </data>
  <data name="PublishedReports" xml:space="preserve">
    <value>Published Reports</value>
  </data>
  <data name="TotalMeetings" xml:space="preserve">
    <value>Total Meetings</value>
  </data>
  <data name="UpcomingMeetings" xml:space="preserve">
    <value>Upcoming Meetings</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>Record not found</value>
  </data>
  <data name="LoadRecordError" xml:space="preserve">
    <value>Error loading record</value>
  </data>
  <data name="SaveExecutiveSuccess" xml:space="preserve">
    <value>Executive information saved successfully!</value>
  </data>
  <data name="SaveExecutiveError" xml:space="preserve">
    <value>Error saving executive information</value>
  </data>
  <data name="DeleteExecutiveSuccess" xml:space="preserve">
    <value>Executive information deleted successfully!</value>
  </data>
  <data name="DeleteExecutiveError" xml:space="preserve">
    <value>Error deleting executive information</value>
  </data>
  <data name="SaveOrganizationSuccess" xml:space="preserve">
    <value>Organization structure saved successfully!</value>
  </data>
  <data name="SaveOrganizationError" xml:space="preserve">
    <value>Error saving organization structure</value>
  </data>
  <data name="DeleteOrganizationSuccess" xml:space="preserve">
    <value>Organization structure deleted successfully!</value>
  </data>
  <data name="DeleteOrganizationError" xml:space="preserve">
    <value>Error deleting organization structure</value>
  </data>
  <data name="ConfirmDeleteExecutive" xml:space="preserve">
    <value>Are you sure you want to delete this executive?</value>
  </data>
  <data name="ConfirmDeleteOrganization" xml:space="preserve">
    <value>Are you sure you want to delete this department?</value>
  </data>
  <data name="SelectPhoto" xml:space="preserve">
    <value>Select Photo</value>
  </data>
  <data name="ExecutiveNameRequired" xml:space="preserve">
    <value>Executive name is required</value>
  </data>
  <data name="DepartmentNameRequired" xml:space="preserve">
    <value>Department name is required</value>
  </data>
  <data name="BusinessInfoTitle" xml:space="preserve">
    <value>Business Information</value>
  </data>
  <data name="BusinessDivisions" xml:space="preserve">
    <value>Business Divisions</value>
  </data>
  <data name="ProductServices" xml:space="preserve">
    <value>Product Services</value>
  </data>
  <data name="DivisionName" xml:space="preserve">
    <value>Division Name</value>
  </data>
  <data name="DivisionDescription" xml:space="preserve">
    <value>Division Description</value>
  </data>
  <data name="DivisionServices" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="DivisionImage" xml:space="preserve">
    <value>Division Image</value>
  </data>
  <data name="DivisionIcon" xml:space="preserve">
    <value>Division Icon</value>
  </data>
  <data name="AddBusinessDivision" xml:space="preserve">
    <value>Add Business Division</value>
  </data>
  <data name="EditBusinessDivision" xml:space="preserve">
    <value>Edit Business Division</value>
  </data>
  <data name="DeleteBusinessDivision" xml:space="preserve">
    <value>Delete Business Division</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="ProductDescription" xml:space="preserve">
    <value>Product Description</value>
  </data>
  <data name="ProductFeatures" xml:space="preserve">
    <value>Product Features</value>
  </data>
  <data name="ProductSpecifications" xml:space="preserve">
    <value>Technical Specifications</value>
  </data>
  <data name="BusinessDivision" xml:space="preserve">
    <value>Business Division</value>
  </data>
  <data name="ProductCategory" xml:space="preserve">
    <value>Product Category</value>
  </data>
  <data name="ProductImages" xml:space="preserve">
    <value>Product Images</value>
  </data>
  <data name="ProductDocuments" xml:space="preserve">
    <value>Product Documents</value>
  </data>
  <data name="ProductPrice" xml:space="preserve">
    <value>Product Price</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="AddProductService" xml:space="preserve">
    <value>Add Product Service</value>
  </data>
  <data name="EditProductService" xml:space="preserve">
    <value>Edit Product Service</value>
  </data>
  <data name="DeleteProductService" xml:space="preserve">
    <value>Delete Product Service</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="Solution" xml:space="preserve">
    <value>Solution</value>
  </data>
  <data name="Technology" xml:space="preserve">
    <value>Technology</value>
  </data>
  <data name="DocumentName" xml:space="preserve">
    <value>Document Name</value>
  </data>
  <data name="DocumentType" xml:space="preserve">
    <value>Document Type</value>
  </data>
  <data name="FileSize" xml:space="preserve">
    <value>File Size</value>
  </data>
  <data name="UploadDate" xml:space="preserve">
    <value>Upload Date</value>
  </data>
  <data name="AddDocument" xml:space="preserve">
    <value>Add Document</value>
  </data>
  <data name="RemoveDocument" xml:space="preserve">
    <value>Remove Document</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="NoData" xml:space="preserve">
    <value>No Data</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="SaveSuccess" xml:space="preserve">
    <value>Saved successfully</value>
  </data>
  <data name="DeleteSuccess" xml:space="preserve">
    <value>Deleted successfully</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>Save failed</value>
  </data>
  <data name="DeleteError" xml:space="preserve">
    <value>Delete failed</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Confirm delete?</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>Validation failed</value>
  </data>
  <data name="NewsManagement" xml:space="preserve">
    <value>News Management</value>
  </data>
  <data name="NewsManagementDescription" xml:space="preserve">
    <value>Manage corporate news content including publishing, editing, and review functions</value>
  </data>
  <data name="NewsList" xml:space="preserve">
    <value>News List</value>
  </data>
  <data name="DraftNews" xml:space="preserve">
    <value>Draft News</value>
  </data>
  <data name="ScheduledNews" xml:space="preserve">
    <value>Scheduled News</value>
  </data>
  <data name="ReviewNews" xml:space="preserve">
    <value>Pending Review</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>Statistics</value>
  </data>
  <data name="CreateNews" xml:space="preserve">
    <value>Create News</value>
  </data>
  <data name="EditNews" xml:space="preserve">
    <value>Edit News</value>
  </data>
  <data name="DeleteNews" xml:space="preserve">
    <value>Delete News</value>
  </data>
  <data name="BatchPublish" xml:space="preserve">
    <value>Batch Publish</value>
  </data>
  <data name="BatchArchive" xml:space="preserve">
    <value>Batch Archive</value>
  </data>
  <data name="BatchApprove" xml:space="preserve">
    <value>Batch Approve</value>
  </data>
  <data name="BatchReject" xml:space="preserve">
    <value>Batch Reject</value>
  </data>
  <data name="SearchNews" xml:space="preserve">
    <value>Search News</value>
  </data>
  <data name="SearchDraftNews" xml:space="preserve">
    <value>Search Drafts</value>
  </data>
  <data name="SearchScheduledNews" xml:space="preserve">
    <value>Search Scheduled</value>
  </data>
  <data name="SearchReviewNews" xml:space="preserve">
    <value>Search Pending Review</value>
  </data>
  <data name="AllTypes" xml:space="preserve">
    <value>All Types</value>
  </data>
  <data name="Featured" xml:space="preserve">
    <value>Featured</value>
  </data>
  <data name="PendingReview" xml:space="preserve">
    <value>Pending Review</value>
  </data>
  <data name="TotalPublished" xml:space="preserve">
    <value>Total Published</value>
  </data>
  <data name="TodayPublished" xml:space="preserve">
    <value>Today Published</value>
  </data>
  <data name="FeaturedNews" xml:space="preserve">
    <value>Featured News</value>
  </data>
  <data name="NewsTypeDistribution" xml:space="preserve">
    <value>News Type Distribution</value>
  </data>
  <data name="PublishTrend" xml:space="preserve">
    <value>Publish Trend</value>
  </data>
  <data name="TopViewedNews" xml:space="preserve">
    <value>Top Viewed News</value>
  </data>
  <data name="ExportStatistics" xml:space="preserve">
    <value>Export Statistics</value>
  </data>
  <data name="RefreshStatistics" xml:space="preserve">
    <value>Refresh Statistics</value>
  </data>
  <data name="NoDraftNews" xml:space="preserve">
    <value>No Draft News</value>
  </data>
  <data name="NoDraftNewsDescription" xml:space="preserve">
    <value>You haven't created any draft news yet</value>
  </data>
  <data name="NoScheduledNews" xml:space="preserve">
    <value>No Scheduled News</value>
  </data>
  <data name="NoScheduledNewsDescription" xml:space="preserve">
    <value>You haven't scheduled any news for publishing</value>
  </data>
  <data name="NoReviewNews" xml:space="preserve">
    <value>No Pending Review News</value>
  </data>
  <data name="NoReviewNewsDescription" xml:space="preserve">
    <value>There are no news items pending review</value>
  </data>
  <data name="CreateFirstNews" xml:space="preserve">
    <value>Create First News</value>
  </data>
  <data name="UntitledNews" xml:space="preserve">
    <value>Untitled News</value>
  </data>
  <data name="NoSummary" xml:space="preserve">
    <value>No Summary</value>
  </data>
  <data name="ChartComingSoon" xml:space="preserve">
    <value>Chart feature coming soon</value>
  </data>
  <data name="NoDataAvailable" xml:space="preserve">
    <value>No data available</value>
  </data>
  <data name="ProcessScheduledPublishing" xml:space="preserve">
    <value>Process Scheduled Publishing</value>
  </data>
  <data name="ConfirmDeleteNews" xml:space="preserve">
    <value>Are you sure you want to delete this news?</value>
  </data>
  <data name="ConfirmBatchPublish" xml:space="preserve">
    <value>Are you sure you want to batch publish selected news?</value>
  </data>
  <data name="ConfirmBatchArchive" xml:space="preserve">
    <value>Are you sure you want to batch archive selected news?</value>
  </data>
  <data name="ConfirmApproveNews" xml:space="preserve">
    <value>Are you sure you want to approve this news?</value>
  </data>
  <data name="ConfirmRejectNews" xml:space="preserve">
    <value>Are you sure you want to reject this news?</value>
  </data>
  <data name="SelectNewsFirst" xml:space="preserve">
    <value>Please select news first</value>
  </data>
  <data name="CreateNewsSuccess" xml:space="preserve">
    <value>News created successfully</value>
  </data>
  <data name="CreateNewsError" xml:space="preserve">
    <value>Failed to create news</value>
  </data>
  <data name="UpdateNewsSuccess" xml:space="preserve">
    <value>News updated successfully</value>
  </data>
  <data name="UpdateNewsError" xml:space="preserve">
    <value>Failed to update news</value>
  </data>
  <data name="DeleteNewsSuccess" xml:space="preserve">
    <value>News deleted successfully</value>
  </data>
  <data name="DeleteNewsError" xml:space="preserve">
    <value>Failed to delete news</value>
  </data>
  <data name="SetFeaturedSuccess" xml:space="preserve">
    <value>Set as featured successfully</value>
  </data>
  <data name="UnsetFeaturedSuccess" xml:space="preserve">
    <value>Removed from featured successfully</value>
  </data>
  <data name="ToggleFeaturedError" xml:space="preserve">
    <value>Failed to update featured status</value>
  </data>
  <data name="BatchOperationSuccess" xml:space="preserve">
    <value>Batch operation completed successfully</value>
  </data>
  <data name="BatchOperationError" xml:space="preserve">
    <value>Batch operation failed</value>
  </data>
  <data name="BatchPublishSuccess" xml:space="preserve">
    <value>Batch publish completed successfully</value>
  </data>
  <data name="BatchUnpublishSuccess" xml:space="preserve">
    <value>Batch unpublish completed successfully</value>
  </data>
  <data name="BatchArchiveSuccess" xml:space="preserve">
    <value>Batch archive completed successfully</value>
  </data>
  <data name="BatchDeleteSuccess" xml:space="preserve">
    <value>Batch delete completed successfully</value>
  </data>
  <data name="InvalidBatchOperation" xml:space="preserve">
    <value>Invalid batch operation</value>
  </data>
  <data name="SchedulePublishSuccess" xml:space="preserve">
    <value>Scheduled publishing set successfully</value>
  </data>
  <data name="SchedulePublishError" xml:space="preserve">
    <value>Failed to set scheduled publishing</value>
  </data>
  <data name="ReviewActionSuccess" xml:space="preserve">
    <value>Review action completed successfully</value>
  </data>
  <data name="ReviewActionError" xml:space="preserve">
    <value>Review action failed</value>
  </data>
  <data name="LoadFormError" xml:space="preserve">
    <value>Failed to load form</value>
  </data>
  <data name="ExportFeatureComingSoon" xml:space="preserve">
    <value>Export feature coming soon</value>
  </data>
  <data name="NewsNotFound" xml:space="preserve">
    <value>News not found</value>
  </data>
  <data name="NewsType_CompanyNews" xml:space="preserve">
    <value>Company News</value>
  </data>
  <data name="NewsType_PressRelease" xml:space="preserve">
    <value>Press Release</value>
  </data>
  <data name="NewsType_ProductUpdate" xml:space="preserve">
    <value>Product Update</value>
  </data>
  <data name="NewsType_Event" xml:space="preserve">
    <value>Event</value>
  </data>
  <data name="NewsType_MediaCoverage" xml:space="preserve">
    <value>Media Coverage</value>
  </data>
  <data name="NewsType_Announcement" xml:space="preserve">
    <value>Announcement</value>
  </data>
  <data name="BusinessInfoDescription" xml:space="preserve">
    <value>Manage the company's business divisions and product/service information.</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="NoBusinessDivisionsYet" xml:space="preserve">
    <value>No business divisions have been added yet.</value>
  </data>
  <data name="SelectBusinessDivision" xml:space="preserve">
    <value>Select a business division</value>
  </data>
  <data name="Currency_JPY" xml:space="preserve">
    <value>JPY (Japanese Yen)</value>
  </data>
  <data name="Currency_USD" xml:space="preserve">
    <value>USD (US Dollar)</value>
  </data>
  <data name="Currency_EUR" xml:space="preserve">
    <value>EUR (Euro)</value>
  </data>
  <data name="Currency_CNY" xml:space="preserve">
    <value>CNY (Chinese Yuan)</value>
  </data>
  <data name="FeatureInProgress" xml:space="preserve">
    <value>Feature in Progress</value>
  </data>
  <data name="ImageAndDocManagementComingSoon" xml:space="preserve">
    <value>Image and document management features will be implemented in a future version.</value>
  </data>
  <data name="AllCategories" xml:space="preserve">
    <value>All Categories</value>
  </data>
  <data name="AllDivisions" xml:space="preserve">
    <value>All Divisions</value>
  </data>
  <data name="SearchProductsPlaceholder" xml:space="preserve">
    <value>Search products...</value>
  </data>
  <data name="ProductImage" xml:space="preserve">
    <value>Product Image</value>
  </data>
  <data name="NoProductServicesYet" xml:space="preserve">
    <value>No products or services have been added yet.</value>
  </data>
  <data name="NewsThumbnail" xml:space="preserve">
    <value>News Thumbnail</value>
  </data>
  <data name="AdminNewsStatisticsTitle" xml:space="preserve">
    <value>News Statistics</value>
  </data>
  <data name="AdminNewsStatisticsDescription" xml:space="preserve">
    <value>View news publication statistics and analysis data</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Back to List</value>
  </data>
  <data name="Drafts" xml:space="preserve">
    <value>Drafts</value>
  </data>
  <data name="ReviewStatistics" xml:space="preserve">
    <value>Review Statistics</value>
  </data>
  <data name="MonthlyPublishTrend" xml:space="preserve">
    <value>Monthly Publish Trend</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Views" xml:space="preserve">
    <value>Views</value>
  </data>
  <data name="Untitled" xml:space="preserve">
    <value>Untitled</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>Rejected</value>
  </data>
  <data name="PublishedCount" xml:space="preserve">
    <value>Published Count</value>
  </data>
  <data name="NewsType" xml:space="preserve">
    <value>News Type</value>
  </data>
  <data name="NewsType_IndustryNews" xml:space="preserve">
    <value>Industry News</value>
  </data>
  <data name="NewsType_ProductNews" xml:space="preserve">
    <value>Product News</value>
  </data>
  <data name="NewsType_EventNews" xml:space="preserve">
    <value>Event News</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="Priority_Low" xml:space="preserve">
    <value>Low</value>
  </data>
  <data name="Priority_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="Priority_High" xml:space="preserve">
    <value>High</value>
  </data>
  <data name="Priority_Urgent" xml:space="preserve">
    <value>Urgent</value>
  </data>
  <data name="PublishImmediately" xml:space="preserve">
    <value>Publish Immediately</value>
  </data>
  <data name="AllowComments" xml:space="preserve">
    <value>Allow Comments</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="Content" xml:space="preserve">
    <value>Content</value>
  </data>
  <data name="SeoKeywords" xml:space="preserve">
    <value>SEO Keywords</value>
  </data>
  <data name="GlobalSeoKeywords" xml:space="preserve">
    <value>Global SEO Keywords</value>
  </data>
  <data name="Tags" xml:space="preserve">
    <value>Tags</value>
  </data>
  <data name="EnterTagsPlaceholder" xml:space="preserve">
    <value>Enter tags, separated by commas</value>
  </data>
  <data name="CompanyDescriptionAndPhilosophy" xml:space="preserve">
    <value>Company Description and Philosophy</value>
  </data>
  <data name="CompanyDescriptionPlaceholder" xml:space="preserve">
    <value>Enter company description...</value>
  </data>
  <data name="PhilosophyPlaceholder" xml:space="preserve">
    <value>Enter business philosophy...</value>
  </data>
  <data name="YearSuffix" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="AccessInfo" xml:space="preserve">
    <value>Access Information</value>
  </data>
  <data name="MapLink" xml:space="preserve">
    <value>Map Link</value>
  </data>
  <data name="ActivityDescription" xml:space="preserve">
    <value>Activity Description</value>
  </data>
  <data name="ActivityImages" xml:space="preserve">
    <value>Activity Images</value>
  </data>
  <data name="ActivitySummary" xml:space="preserve">
    <value>Activity Summary</value>
  </data>
  <data name="ActivityImpact" xml:space="preserve">
    <value>Activity Impact</value>
  </data>
  <data name="ActivityTitle" xml:space="preserve">
    <value>Activity Title</value>
  </data>
  <data name="AddHistoryEvent" xml:space="preserve">
    <value>Add History Event</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="BackToMessageList" xml:space="preserve">
    <value>Back to Message List</value>
  </data>
  <data name="Biography" xml:space="preserve">
    <value>Biography</value>
  </data>
  <data name="BusinessCooperation" xml:space="preserve">
    <value>Business Cooperation</value>
  </data>
  <data name="BusinessHours" xml:space="preserve">
    <value>Business Hours</value>
  </data>
  <data name="CNY" xml:space="preserve">
    <value>Chinese Yuan</value>
  </data>
  <data name="CSRActivitiesDeveloping" xml:space="preserve">
    <value>CSR Activities management is under development...</value>
  </data>
  <data name="CSRCategory" xml:space="preserve">
    <value>CSR Category</value>
  </data>
  <data name="Capital" xml:space="preserve">
    <value>Capital</value>
  </data>
  <data name="Complaint" xml:space="preserve">
    <value>Complaint</value>
  </data>
  <data name="CompanyDescription" xml:space="preserve">
    <value>Company Description</value>
  </data>
  <data name="CompanyHistory" xml:space="preserve">
    <value>Company History</value>
  </data>
  <data name="CompanyHistoryEditDeveloping" xml:space="preserve">
    <value>Company history editing is under development</value>
  </data>
  <data name="CompanyInfoTitle" xml:space="preserve">
    <value>Company Information</value>
  </data>
  <data name="ConfirmDeleteHistoryRecord" xml:space="preserve">
    <value>Are you sure you want to delete this history record?</value>
  </data>
  <data name="ContactInfo" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="ContactInfoDeveloping" xml:space="preserve">
    <value>Contact information management is under development...</value>
  </data>
  <data name="CreatedAt" xml:space="preserve">
    <value>Created At</value>
  </data>
  <data name="DealResult" xml:space="preserve">
    <value>Deal Result</value>
  </data>
  <data name="DealResultPlaceholder" xml:space="preserve">
    <value>Enter deal result...</value>
  </data>
  <data name="DeleteFunctionDeveloping" xml:space="preserve">
    <value>Delete function is under development</value>
  </data>
  <data name="DeleteHistoryError" xml:space="preserve">
    <value>Failed to delete company history</value>
  </data>
  <data name="DeleteHistorySuccess" xml:space="preserve">
    <value>Company history deleted successfully!</value>
  </data>
  <data name="DepartmentDescription" xml:space="preserve">
    <value>Department Description</value>
  </data>
  <data name="DepartmentName" xml:space="preserve">
    <value>Department Name</value>
  </data>
  <data name="DocumentDescription" xml:space="preserve">
    <value>Document Description</value>
  </data>
  <data name="DocumentFile" xml:space="preserve">
    <value>Document File</value>
  </data>
  <data name="DocumentTitle" xml:space="preserve">
    <value>Document Title</value>
  </data>
  <data name="EUR" xml:space="preserve">
    <value>Euro</value>
  </data>
  <data name="EditHistoryDeveloping" xml:space="preserve">
    <value>Edit history function is under development</value>
  </data>
  <data name="EmailInvalid" xml:space="preserve">
    <value>Invalid email format</value>
  </data>
  <data name="EmployeeScale" xml:space="preserve">
    <value>Employee Scale</value>
  </data>
  <data name="EmployeeScale_Enterprise" xml:space="preserve">
    <value>Enterprise (1000+ employees)</value>
  </data>
  <data name="EmployeeScale_Large" xml:space="preserve">
    <value>Large (301-1000 employees)</value>
  </data>
  <data name="EmployeeScale_Medium" xml:space="preserve">
    <value>Medium (51-300 employees)</value>
  </data>
  <data name="EmployeeScale_Small" xml:space="preserve">
    <value>Small (1-50 employees)</value>
  </data>
  <data name="EnableCompanyInfo" xml:space="preserve">
    <value>Enable Company Information</value>
  </data>
  <data name="ErrorLoadingCompanyInfo" xml:space="preserve">
    <value>Failed to load company information, please try again later.</value>
  </data>
  <data name="EstablishedDate" xml:space="preserve">
    <value>Established Date</value>
  </data>
  <data name="EstablishedDateRequired" xml:space="preserve">
    <value>Established date is required</value>
  </data>
  <data name="EventDate" xml:space="preserve">
    <value>Event Date</value>
  </data>
  <data name="EventDateRequired" xml:space="preserve">
    <value>Event date is required</value>
  </data>
  <data name="EventDescription" xml:space="preserve">
    <value>Event Description</value>
  </data>
  <data name="EventImage" xml:space="preserve">
    <value>Event Image</value>
  </data>
  <data name="EventTitle" xml:space="preserve">
    <value>Event Title</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>Event Type</value>
  </data>
  <data name="EventTypeRequired" xml:space="preserve">
    <value>Event type is required</value>
  </data>
  <data name="ExecutiveName" xml:space="preserve">
    <value>Executive Name</value>
  </data>
  <data name="ExecutiveOrganization" xml:space="preserve">
    <value>Executive &amp; Organization</value>
  </data>
  <data name="ExecutiveOrganizationDeveloping" xml:space="preserve">
    <value>Executive and organization management is under development...</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="GeneralInquiry" xml:space="preserve">
    <value>General Inquiry</value>
  </data>
  <data name="HistoryNotFound" xml:space="preserve">
    <value>History record not found</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>Important</value>
  </data>
  <data name="InvestorRelations" xml:space="preserve">
    <value>Investor Relations</value>
  </data>
  <data name="InvestorRelationsDeveloping" xml:space="preserve">
    <value>Investor relations management is under development...</value>
  </data>
  <data name="IsPrimary" xml:space="preserve">
    <value>Is Primary</value>
  </data>
  <data name="IsPublished" xml:space="preserve">
    <value>Is Published</value>
  </data>
  <data name="JPY" xml:space="preserve">
    <value>Japanese Yen</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>Latitude</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="LocationName" xml:space="preserve">
    <value>Location Name</value>
  </data>
  <data name="LocationType" xml:space="preserve">
    <value>Location Type</value>
  </data>
  <data name="LogoUrl" xml:space="preserve">
    <value>Logo URL</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>Longitude</value>
  </data>
  <data name="MarkAsImportant" xml:space="preserve">
    <value>Mark as Important</value>
  </data>
  <data name="MarkAsInProgress" xml:space="preserve">
    <value>Mark as In Progress</value>
  </data>
  <data name="MeetingDate" xml:space="preserve">
    <value>Meeting Date</value>
  </data>
  <data name="MeetingDescription" xml:space="preserve">
    <value>Meeting Description</value>
  </data>
  <data name="MeetingAgenda" xml:space="preserve">
    <value>Meeting Agenda</value>
  </data>
  <data name="MeetingLocation" xml:space="preserve">
    <value>Meeting Location</value>
  </data>
  <data name="MeetingDocuments" xml:space="preserve">
    <value>Meeting Documents</value>
  </data>
  <data name="MeetingStatus" xml:space="preserve">
    <value>Meeting Status</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="MessageContent" xml:space="preserve">
    <value>Message Content</value>
  </data>
  <data name="MessageDetails" xml:space="preserve">
    <value>Message Details</value>
  </data>
  <data name="MessageInfo" xml:space="preserve">
    <value>Message Info</value>
  </data>
  <data name="MessageManagement" xml:space="preserve">
    <value>Messages</value>
  </data>
  <data name="MessageManagementDescription" xml:space="preserve">
    <value>View and process customer messages</value>
  </data>
  <data name="NetIncome" xml:space="preserve">
    <value>Net Income</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="NewMessage" xml:space="preserve">
    <value>New Message</value>
  </data>
  <data name="NewMessages" xml:space="preserve">
    <value>New Messages</value>
  </data>
  <data name="NoCompanyHistoryRecords" xml:space="preserve">
    <value>No company history records</value>
  </data>
  <data name="NoMessages" xml:space="preserve">
    <value>No messages</value>
  </data>
  <data name="NoTitle" xml:space="preserve">
    <value>No Title</value>
  </data>
  <data name="ParentDepartment" xml:space="preserve">
    <value>Parent Department</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="PhotoUrl" xml:space="preserve">
    <value>Photo</value>
  </data>
  <data name="Philosophy" xml:space="preserve">
    <value>Philosophy</value>
  </data>
  <data name="PleaseSelect" xml:space="preserve">
    <value>Please Select</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Postal Code</value>
  </data>
  <data name="ProcessedAt" xml:space="preserve">
    <value>Processed At</value>
  </data>
  <data name="PublishDate" xml:space="preserve">
    <value>Publish Date</value>
  </data>
  <data name="Quarter" xml:space="preserve">
    <value>Quarter</value>
  </data>
  <data name="RegistrationNumber" xml:space="preserve">
    <value>Registration Number</value>
  </data>
  <data name="ReportFile" xml:space="preserve">
    <value>Report File</value>
  </data>
  <data name="ReportPeriod" xml:space="preserve">
    <value>Report Period</value>
  </data>
  <data name="ReportSummary" xml:space="preserve">
    <value>Report Summary</value>
  </data>
  <data name="ReportTitle" xml:space="preserve">
    <value>Report Title</value>
  </data>
  <data name="ReportType" xml:space="preserve">
    <value>Report Type</value>
  </data>
  <data name="ReportYear" xml:space="preserve">
    <value>Report Year</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="Revenue" xml:space="preserve">
    <value>Revenue</value>
  </data>
  <data name="SaveBasicInfoSuccess" xml:space="preserve">
    <value>Basic information saved successfully!</value>
  </data>
  <data name="SaveDealResult" xml:space="preserve">
    <value>Save Deal Result</value>
  </data>
  <data name="SaveHistoryError" xml:space="preserve">
    <value>Failed to save company history</value>
  </data>
  <data name="SaveHistorySuccess" xml:space="preserve">
    <value>Company history saved successfully!</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>Search name, email, content...</value>
  </data>
  <data name="SourcePage" xml:space="preserve">
    <value>Source Page</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="StatusFilter" xml:space="preserve">
    <value>Status Filter</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="TotalAssets" xml:space="preserve">
    <value>Total Assets</value>
  </data>
  <data name="USD" xml:space="preserve">
    <value>US Dollar</value>
  </data>
  <data name="UpdatedAt" xml:space="preserve">
    <value>Updated At</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="WebsiteInvalid" xml:space="preserve">
    <value>Invalid website format</value>
  </data>
  <data name="EnterText" xml:space="preserve">
    <value>Enter text</value>
  </data>
  <data name="FieldRenderError" xml:space="preserve">
    <value>Field render failed</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="FormGroups_Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="FileUpload_ClickToUpload" xml:space="preserve">
    <value>Click to upload</value>
  </data>
  <data name="FileUpload_OrDragDrop" xml:space="preserve">
    <value>or drag and drop files here</value>
  </data>
  <data name="FileUpload_AllFiles" xml:space="preserve">
    <value>All file types</value>
  </data>
  <data name="FileUpload_MaxSize" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="FileUpload_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="FileUpload_DeleteFile" xml:space="preserve">
    <value>Delete file</value>
  </data>
  <data name="FileUpload_DeleteConfirm" xml:space="preserve">
    <value>Are you sure you want to delete this file?</value>
  </data>
  <data name="FileUpload_DeleteSuccess" xml:space="preserve">
    <value>File deleted successfully</value>
  </data>
  <data name="FileUpload_DeleteError" xml:space="preserve">
    <value>Delete failed</value>
  </data>
  <data name="FileUpload_UploadError" xml:space="preserve">
    <value>Upload failed</value>
  </data>
  <data name="FileType_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="FileType_Video" xml:space="preserve">
    <value>Video</value>
  </data>
  <data name="FileType_PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="FileType_Word" xml:space="preserve">
    <value>Word Document</value>
  </data>
  <data name="AllStatuses" xml:space="preserve">
    <value>All Statuses</value>
  </data>
  <data name="ExternalUrl" xml:space="preserve">
    <value>External URL</value>
  </data>
  <data name="LoadingData" xml:space="preserve">
    <value>Loading data...</value>
  </data>
  <data name="NewsSource" xml:space="preserve">
    <value>News Source</value>
  </data>
  <data name="NewsSource_External" xml:space="preserve">
    <value>External</value>
  </data>
  <data name="NewsSource_Internal" xml:space="preserve">
    <value>Internal</value>
  </data>
  <data name="NewsSource_MediaReport" xml:space="preserve">
    <value>Media Report</value>
  </data>
  <data name="NewsSource_Partner" xml:space="preserve">
    <value>Partner</value>
  </data>
  <data name="NewsSource_PressRelease" xml:space="preserve">
    <value>Press Release</value>
  </data>
  <data name="NewsStatus" xml:space="preserve">
    <value>News Status</value>
  </data>
  <data name="NewsStatus_Draft" xml:space="preserve">
    <value>Draft</value>
  </data>
  <data name="NewsStatus_Published" xml:space="preserve">
    <value>Published</value>
  </data>
  <data name="NewsStatus_Review" xml:space="preserve">
    <value>Under Review</value>
  </data>
  <data name="SearchNewsPlaceholder" xml:space="preserve">
    <value>Enter news title...</value>
  </data>
  <data name="SearchTitle" xml:space="preserve">
    <value>Search Title</value>
  </data>
  <data name="Searching" xml:space="preserve">
    <value>Searching...</value>
  </data>
  <data name="ThumbnailUrl" xml:space="preserve">
    <value>Thumbnail</value>
  </data>
  <data name="TitleRequired" xml:space="preserve">
    <value>Title is required</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>Update failed</value>
  </data>
  <data name="UpdateSuccess" xml:space="preserve">
    <value>Updated successfully</value>
  </data>
  <data name="ActiveJobs" xml:space="preserve">
    <value>Active Jobs</value>
  </data>
  <data name="AddNewJob" xml:space="preserve">
    <value>Add New Job</value>
  </data>
  <data name="AllInterviewTypes" xml:space="preserve">
    <value>All Interview Types</value>
  </data>
  <data name="AllJobTypes" xml:space="preserve">
    <value>All Job Types</value>
  </data>
  <data name="AllLooksGood" xml:space="preserve">
    <value>All looks good</value>
  </data>
  <data name="ApplicationDeadline" xml:space="preserve">
    <value>Application Deadline</value>
  </data>
  <data name="AverageTranslationRate" xml:space="preserve">
    <value>Average Translation Rate</value>
  </data>
  <data name="BatchActivate" xml:space="preserve">
    <value>Batch Activate</value>
  </data>
  <data name="BatchDeactivate" xml:space="preserve">
    <value>Batch Deactivate</value>
  </data>
  <data name="BatchUnpublish" xml:space="preserve">
    <value>Batch Unpublish</value>
  </data>
  <data name="Benefits" xml:space="preserve">
    <value>Benefits</value>
  </data>
  <data name="CompanyImpression" xml:space="preserve">
    <value>Company Impression</value>
  </data>
  <data name="CompletedFields" xml:space="preserve">
    <value>Completed Fields</value>
  </data>
  <data name="ContentQualityAnalysis" xml:space="preserve">
    <value>Content Quality Analysis</value>
  </data>
  <data name="ContentRichness" xml:space="preserve">
    <value>Content Richness</value>
  </data>
  <data name="Coverage" xml:space="preserve">
    <value>Coverage</value>
  </data>
  <data name="CreateEmployeeInterview" xml:space="preserve">
    <value>Create Employee Interview</value>
  </data>
  <data name="CreateFirstInterview" xml:space="preserve">
    <value>Create First Interview</value>
  </data>
  <data name="CreateJobPosition" xml:space="preserve">
    <value>Create Job Position</value>
  </data>
  <data name="EditJobPosition" xml:space="preserve">
    <value>Edit Job Position</value>
  </data>  
  <data name="Deadline" xml:space="preserve">
    <value>Deadline</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="DraftInterviews" xml:space="preserve">
    <value>Draft Interviews</value>
  </data>
  <data name="DraftJobs" xml:space="preserve">
    <value>Draft Jobs</value>
  </data>
  <data name="EmployeeInterviews" xml:space="preserve">
    <value>Employee Interviews</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>Employee Name</value>
  </data>
  <data name="EmploymentType" xml:space="preserve">
    <value>Employment Type</value>
  </data>
  <data name="ExperienceLevel" xml:space="preserve">
    <value>Experience Level</value>
  </data>
  <data name="Expired" xml:space="preserve">
    <value>Expired</value>
  </data>
  <data name="ExpiredJobs" xml:space="preserve">
    <value>Expired Jobs</value>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>Export Data</value>
  </data>
  <data name="FeaturedInterviews" xml:space="preserve">
    <value>Featured Interviews</value>
  </data>
  <data name="FeaturedJobs" xml:space="preserve">
    <value>Featured Jobs</value>
  </data>
  <data name="InterviewContent" xml:space="preserve">
    <value>Interview Content</value>
  </data>
  <data name="InterviewDate" xml:space="preserve">
    <value>Interview Date</value>
  </data>
  <data name="InterviewType" xml:space="preserve">
    <value>Interview Type</value>
  </data>
  <data name="InterviewTypeDistribution" xml:space="preserve">
    <value>Interview Type Distribution</value>
  </data>
  <data name="InterviewType_Management" xml:space="preserve">
    <value>Management</value>
  </data>
  <data name="InterviewType_NewEmployee" xml:space="preserve">
    <value>New Employee</value>
  </data>
  <data name="InterviewType_Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="InterviewType_Sales" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="InterviewType_Technical" xml:space="preserve">
    <value>Technical</value>
  </data>
  <data name="InterviewType_Veteran" xml:space="preserve">
    <value>Veteran</value>
  </data>
  <data name="IsFeatured" xml:space="preserve">
    <value>Is Featured</value>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>Job Description</value>
  </data>
  <data name="JobPositions" xml:space="preserve">
    <value>Job Positions</value>
  </data>
    <data name="JobTitle" xml:space="preserve">
    <value>Job Title</value>
  </data>
  <data name="JobType" xml:space="preserve">
    <value>Job Type</value>
  </data>
  <data name="JobTypeDistribution" xml:space="preserve">
    <value>Job Type Distribution</value>
  </data>
  <data name="ManageInterviews" xml:space="preserve">
    <value>Manage Interviews</value>
  </data>
  <data name="MultilingualCompleteness" xml:space="preserve">
    <value>Multilingual Completeness</value>
  </data>
  <data name="NoInterviewDataAvailable" xml:space="preserve">
    <value>No interview data available</value>
  </data>
  <data name="NoInterviewsFound" xml:space="preserve">
    <value>No interviews found</value>
  </data>
  <data name="NoJobDataAvailable" xml:space="preserve">
    <value>No job data available</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="OverviewStatistics" xml:space="preserve">
    <value>Overview Statistics</value>
  </data>
  <data name="PostDate" xml:space="preserve">
    <value>Post Date</value>
  </data>
  <data name="ProbationPeriod" xml:space="preserve">
    <value>Probation Period</value>
  </data>
  <data name="ProbationPeriodPlaceholder" xml:space="preserve">
    <value>e.g. 3 months</value>
  </data>
  <data name="PublishedInterviews" xml:space="preserve">
    <value>Published Interviews</value>
  </data>
  <data name="RecruitmentContentOptimal" xml:space="preserve">
    <value>Recruitment content is optimal</value>
  </data>
  <data name="RefreshData" xml:space="preserve">
    <value>Refresh Data</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>Requirements</value>
  </data>
  <data name="SalaryMax" xml:space="preserve">
    <value>Maximum Salary</value>
  </data>
  <data name="SalaryMin" xml:space="preserve">
    <value>Minimum Salary</value>
  </data>
  <data name="SearchInterviews" xml:space="preserve">
    <value>Search Interviews</value>
  </data>
  <data name="SearchJobs" xml:space="preserve">
    <value>Search Jobs</value>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>Sort Order</value>
  </data>
  <data name="Status_Archived" xml:space="preserve">
    <value>Archived</value>
  </data>
  <data name="Status_Draft" xml:space="preserve">
    <value>Draft</value>
  </data>
  <data name="Status_Published" xml:space="preserve">
    <value>Published</value>
  </data>
  <data name="SuggestionsAndReminders" xml:space="preserve">
    <value>Suggestions and Reminders</value>
  </data>
  <data name="TagsPlaceholder" xml:space="preserve">
    <value>Separate multiple tags with commas</value>
  </data>
  <data name="TotalActiveJobs" xml:space="preserve">
    <value>Total Active Jobs</value>
  </data>
  <data name="UpdateFrequency" xml:space="preserve">
    <value>Update Frequency</value>
  </data>
  <data name="UpdatesPerWeek" xml:space="preserve">
    <value>Updates per Week</value>
  </data>
  <data name="WorkDescription" xml:space="preserve">
    <value>Work Description</value>
  </data>
  <data name="WorkLocation" xml:space="preserve">
    <value>Work Location</value>
  </data>
  <data name="WorkingHours" xml:space="preserve">
    <value>Working Hours</value>
  </data>
  <data name="WorkingHoursPlaceholder" xml:space="preserve">
    <value>e.g. 9:00-18:00</value>
  </data>
  <data name="Years" xml:space="preserve">
    <value>Years</value>
  </data>
  <data name="YearsOfService" xml:space="preserve">
    <value>Years of Service</value>
  </data>
  <data name="RecruitmentManagement" xml:space="preserve">
    <value>Recruitment</value>
  </data>
  <data name="JobType_NewGraduate" xml:space="preserve">
    <value>Campus Recruitment</value>
  </data>
  <data name="JobType_MidCareer" xml:space="preserve">
    <value>Experienced Hire</value>
  </data>
  <data name="JobType_Internal" xml:space="preserve">
    <value>Internal Recruitment</value>
  </data>
  <data name="EmploymentType_FullTime" xml:space="preserve">
    <value>Full-Time</value>
  </data>
  <data name="EmploymentType_PartTime" xml:space="preserve">
    <value>Part-Time</value>
  </data>
  <data name="EmploymentType_Contract" xml:space="preserve">
    <value>Contract</value>
  </data>
  <data name="EmploymentType_Temporary" xml:space="preserve">
    <value>Temporary</value>
  </data>
  <data name="EmploymentType_Internship" xml:space="preserve">
    <value>Internship</value>
  </data>
  <data name="ExperienceLevel_Entry" xml:space="preserve">
    <value>Entry Level</value>
  </data>
  <data name="ExperienceLevel_Junior" xml:space="preserve">
    <value>Junior</value>
  </data>
  <data name="ExperienceLevel_Mid" xml:space="preserve">
    <value>Mid-Level</value>
  </data>
  <data name="ExperienceLevel_Senior" xml:space="preserve">
    <value>Senior</value>
  </data>
  <data name="ExperienceLevel_Executive" xml:space="preserve">
    <value>Executive</value>
  </data>
  <!-- Missing Message Management Keys -->
  <data name="WaitingForCustomer" xml:space="preserve">
    <value>Waiting for Customer</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="DealResultSaved" xml:space="preserve">
    <value>Deal result saved</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="CareerInquiry" xml:space="preserve">
    <value>Career Inquiry</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="MarkAsClosed" xml:space="preserve">
    <value>Mark as Closed</value>
  </data>
  <data name="MarkAsResolved" xml:space="preserve">
    <value>Mark as Resolved</value>
  </data>
  <data name="MarkAsWaitingForResponse" xml:space="preserve">
    <value>Mark as Waiting for Response</value>
  </data>
  <data name="MediaInquiry" xml:space="preserve">
    <value>Media Inquiry</value>
  </data>
  <data name="PartnershipInquiry" xml:space="preserve">
    <value>Partnership Inquiry</value>
  </data>
  <data name="ProductInquiry" xml:space="preserve">
    <value>Product Inquiry</value>
  </data>
  <data name="RemoveImportant" xml:space="preserve">
    <value>Remove Important</value>
  </data>
  <data name="Resolved" xml:space="preserve">
    <value>Resolved</value>
  </data>
  <data name="ServiceInquiry" xml:space="preserve">
    <value>Service Inquiry</value>
  </data>
  <data name="Spam" xml:space="preserve">
    <value>Spam</value>
  </data>
  <data name="TechnicalSupport" xml:space="preserve">
    <value>Technical Support</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="WaitingForResponse" xml:space="preserve">
    <value>Waiting for Response</value>
  </data>
  <data name="DataManagement" xml:space="preserve">
    <value>Data Management</value>
  </data>
  <data name="ComponentDataManagement" xml:space="preserve">
    <value>Component Data Management</value>
  </data>
  <data name="LoadingComponentData" xml:space="preserve">
    <value>Loading component data...</value>
  </data>
  <data name="LoadComponentDataError" xml:space="preserve">
    <value>Failed to load component data</value>
  </data>
  <data name="NoFormFieldsConfigured" xml:space="preserve">
    <value>No form fields configured</value>
  </data>
  <data name="ComponentFormFieldsNotConfigured" xml:space="preserve">
    <value>This component has no form fields configured in its variants.json file</value>
  </data>
  <data name="SavingComponentData" xml:space="preserve">
    <value>Saving component data...</value>
  </data>
  <data name="ComponentDataSaved" xml:space="preserve">
    <value>Component data saved successfully</value>
  </data>
  <data name="SaveComponentDataError" xml:space="preserve">
    <value>Failed to save component data</value>
  </data>
  <data name="NoFormDataToSave" xml:space="preserve">
    <value>No form data to save</value>
  </data>
  <data name="InvalidParameters" xml:space="preserve">
    <value>Invalid parameters</value>
  </data>
  <data name="SerializeDataError" xml:space="preserve">
    <value>Failed to serialize data</value>
  </data>
  <data name="DealResultRequired" xml:space="preserve">
    <value>Deal result is required</value>
  </data>
  <data name="DealResultSaveSuccess" xml:space="preserve">
    <value>Deal result saved successfully</value>
  </data>

</root>