<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="SiteSettingsTitle" xml:space="preserve">
    <value>サイト設定</value>
  </data>
  <data name="SiteNameRequired" xml:space="preserve">
    <value>サイト名は必須です</value>
  </data>
  <data name="DomainRequired" xml:space="preserve">
    <value>ドメインは必須です</value>
  </data>
  <data name="LoadSiteSettingsError" xml:space="preserve">
    <value>サイト設定の読み込み中にエラーが発生しました。しばらくしてから再試行してください。</value>
  </data>
  <data name="InputDataError" xml:space="preserve">
    <value>入力データに誤りがあります。確認後、再試行してください。</value>
  </data>
  <data name="BasicInfoSaved" xml:space="preserve">
    <value>基本情報設定が正常に保存されました！</value>
  </data>
  <data name="SaveSettingsError" xml:space="preserve">
    <value>設定の保存中にエラーが発生しました</value>
  </data>
  <data name="SelectTheme" xml:space="preserve">
    <value>適用するテーマを選択してください</value>
  </data>
  <data name="InvalidTheme" xml:space="preserve">
    <value>選択されたテーマが無効または存在しません</value>
  </data>
  <data name="ThemeAppliedSuccess" xml:space="preserve">
    <value>テーマが正常に適用されました！新しいテーマ効果を表示するため、ページが自動的にリフレッシュされます。</value>
  </data>
  <data name="ThemeApplyFailed" xml:space="preserve">
    <value>テーマの適用に失敗しました。しばらくしてから再試行してください。</value>
  </data>
  <data name="SystemError" xml:space="preserve">
    <value>システムエラー</value>
  </data>
  <data name="ComponentSettingsSaved" xml:space="preserve">
    <value>コンポーネント設定が正常に保存されました！</value>
  </data>
  <data name="SelectFile" xml:space="preserve">
    <value>アップロードするファイルを選択してください</value>
  </data>
  <data name="UnsupportedFileType" xml:space="preserve">
    <value>サポートされていないファイル形式です。画像ファイルをアップロードしてください</value>
  </data>
  <data name="FileSizeLimit" xml:space="preserve">
    <value>ファイルサイズは5MBを超えることはできません</value>
  </data>
  <data name="UploadConfigError" xml:space="preserve">
    <value>アップロード設定エラー</value>
  </data>
  <data name="ImageProcessFailed" xml:space="preserve">
    <value>画像処理に失敗しました</value>
  </data>
  <data name="UploadSuccess" xml:space="preserve">
    <value>アップロード成功</value>
  </data>
  <data name="UploadFailed" xml:space="preserve">
    <value>アップロードに失敗しました</value>
  </data>
  <data name="LogoFileNameEmpty" xml:space="preserve">
    <value>ロゴファイル名を空にすることはできません</value>
  </data>
  <data name="LogoFileNotExists" xml:space="preserve">
    <value>ロゴファイルが存在しません</value>
  </data>
  <data name="GenerateFaviconFailed" xml:space="preserve">
    <value>ウェブサイトアイコンの生成に失敗しました</value>
  </data>
  <data name="FaviconGenerateSuccess" xml:space="preserve">
    <value>ウェブサイトアイコンが正常に生成されました</value>
  </data>
  <data name="BasicInfo" xml:space="preserve">
    <value>基本情報</value>
  </data>
  <data name="ThemeSettings" xml:space="preserve">
    <value>テーマ設定</value>
  </data>
  <data name="ComponentSettings" xml:space="preserve">
    <value>コンポーネント設定</value>
  </data>
  <data name="BasicInfoSettingsTitle" xml:space="preserve">
    <value>基本情報設定</value>
  </data>
  <data name="SiteName" xml:space="preserve">
    <value>サイト名</value>
  </data>
  <data name="Domain" xml:space="preserve">
    <value>ドメイン</value>
  </data>
  <data name="DefaultLanguage" xml:space="preserve">
    <value>デフォルト言語</value>
  </data>
  <data name="Logo" xml:space="preserve">
    <value>ロゴ</value>
  </data>
  <data name="Favicon" xml:space="preserve">
    <value>ファビコン</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>設定を保存</value>
  </data>
  <data name="GenerateFavicon" xml:space="preserve">
    <value>ロゴから生成</value>
  </data>
  <data name="SelectLogo" xml:space="preserve">
    <value>ロゴを選択</value>
  </data>
  <data name="SelectFavicon" xml:space="preserve">
    <value>ファビコンを選択</value>
  </data>
  <data name="ClickUploadLogo" xml:space="preserve">
    <value>クリックしてファイルをアップロード</value>
  </data>
  <data name="ClickUploadFavicon" xml:space="preserve">
    <value>クリックしてアイコンをアップロード</value>
  </data>
  <data name="SaveBasicInfo" xml:space="preserve">
    <value>基本情報を保存</value>
  </data>
  <data name="ThemeSettingsTitle" xml:space="preserve">
    <value>テーマ設定</value>
  </data>
  <data name="PleaseWaitProcessing" xml:space="preserve">
    <value>しばらくお待ちください、処理中...</value>
  </data>
  <data name="ApplyingTheme" xml:space="preserve">
    <value>テーマを適用中...</value>
  </data>
  <data name="SavingBasicInfo" xml:space="preserve">
    <value>基本情報を保存中...</value>
  </data>
  <data name="SavingComponentSettings" xml:space="preserve">
    <value>コンポーネント設定を保存中...</value>
  </data>
  <data name="GeneratingFaviconFromLogo" xml:space="preserve">
    <value>ロゴからファビコンを生成中...</value>
  </data>
  <data name="UploadingLogo" xml:space="preserve">
    <value>ロゴをアップロード中...</value>
  </data>
  <data name="UploadingFavicon" xml:space="preserve">
    <value>ファビコンをアップロード中...</value>
  </data>
  <data name="ApplyThemeError" xml:space="preserve">
    <value>テーマ適用時にエラーが発生しました。しばらくしてから再試行してください</value>
  </data>
  <data name="SaveBasicInfoError" xml:space="preserve">
    <value>基本情報保存時にエラーが発生しました。しばらくしてから再試行してください</value>
  </data>
  <data name="SaveComponentSettingsError" xml:space="preserve">
    <value>コンポーネント設定保存時にエラーが発生しました。しばらくしてから再試行してください</value>
  </data>
  <data name="GenerateFaviconError" xml:space="preserve">
    <value>ファビコン生成時にエラーが発生しました</value>
  </data>
  <data name="PleaseUploadLogoFirst" xml:space="preserve">
    <value>まずロゴをアップロードしてください</value>
  </data>
  <data name="FaviconGeneratedSuccess" xml:space="preserve">
    <value>ファビコンが正常に生成されました！</value>
  </data>
  <data name="UploadSuccessful" xml:space="preserve">
    <value>アップロード成功！</value>
  </data>
  <data name="BackendManagement" xml:space="preserve">
    <value>管理画面</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>ダッシュボード</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>システム設定</value>
  </data>
  <data name="SiteSettings" xml:space="preserve">
    <value>サイト設定</value>
  </data>
  <data name="AccountSettings" xml:space="preserve">
    <value>アカウント設定</value>
  </data>
  <data name="FrontendPage" xml:space="preserve">
    <value>フロント画面</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>ログアウト</value>
  </data>
  <data name="WelcomeAdmin" xml:space="preserve">
    <value>ようこそ、管理者</value>
  </data>
  <data name="AccountSettingsTitle" xml:space="preserve">
    <value>アカウント設定</value>
  </data>
  <data name="ChangePasswordTitle" xml:space="preserve">
    <value>パスワード変更</value>
  </data>
  <data name="UpdateYourAccountPassword" xml:space="preserve">
    <value>アカウントパスワードを更新</value>
  </data>
  <data name="ChangeEmailTitle" xml:space="preserve">
    <value>メールアドレス変更</value>
  </data>
  <data name="UpdateYourEmailAddress" xml:space="preserve">
    <value>メールアドレスを更新</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>システム情報</value>
  </data>
  <data name="SystemVersion" xml:space="preserve">
    <value>システムバージョン</value>
  </data>
  <data name="RuntimeEnvironment" xml:space="preserve">
    <value>実行環境</value>
  </data>
  <data name="Database" xml:space="preserve">
    <value>データベース</value>
  </data>
  <data name="Authentication" xml:space="preserve">
    <value>認証</value>
  </data>
  <data name="DashboardTitle" xml:space="preserve">
    <value>管理ダッシュボード</value>
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value>おかえりなさい</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>メールアドレス</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>ユーザー名</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>ログイン状態を保持</value>
  </data>
  <data name="CurrentPassword" xml:space="preserve">
    <value>現在のパスワード</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>新しいパスワード</value>
  </data>
  <data name="ConfirmNewPassword" xml:space="preserve">
    <value>新しいパスワードの確認</value>
  </data>
  <data name="NewEmailAddress" xml:space="preserve">
    <value>新しいメールアドレス</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>パスワード確認</value>
  </data>
  <data name="HeaderStyle" xml:space="preserve">
    <value>ヘッダースタイル</value>
  </data>
  <data name="FooterStyle" xml:space="preserve">
    <value>フッタースタイル</value>
  </data>
  <data name="NavigationStyle" xml:space="preserve">
    <value>ナビゲーションスタイル</value>
  </data>
  <data name="UserNameRequired" xml:space="preserve">
    <value>ユーザー名は必須です</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>パスワードは必須です</value>
  </data>
  <data name="CurrentPasswordRequired" xml:space="preserve">
    <value>現在のパスワードは必須です</value>
  </data>
  <data name="NewPasswordRequired" xml:space="preserve">
    <value>新しいパスワードは必須です</value>
  </data>
  <data name="PasswordLengthError" xml:space="preserve">
    <value>パスワードは6文字以上である必要があります</value>
  </data>
  <data name="PasswordMismatch" xml:space="preserve">
    <value>パスワードの確認が一致しません</value>
  </data>
  <data name="NewEmailRequired" xml:space="preserve">
    <value>新しいメールアドレスは必須です</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>メールアドレスの形式が無効です</value>
  </data>
  <data name="DomainMaxLength" xml:space="preserve">
    <value>ドメインは200文字以内である必要があります</value>
  </data>
  <data name="DefaultLanguageRequired" xml:space="preserve">
    <value>デフォルト言語を選択する必要があります</value>
  </data>
  <data name="ConfirmPasswordRequired" xml:space="preserve">
    <value>パスワードの確認は必須です</value>
  </data>
  <data name="CurrentTheme" xml:space="preserve">
    <value>現在使用中のテーマ</value>
  </data>
  <data name="ApplyTheme" xml:space="preserve">
    <value>テーマを適用</value>
  </data>
  <data name="CurrentInUse" xml:space="preserve">
    <value>使用中</value>
  </data>
  <data name="CountCode" xml:space="preserve">
    <value>統計コード</value>
  </data>
  <data name="GlobalComponentSettings" xml:space="preserve">
    <value>グローバルコンポーネント設定</value>
  </data>
  <data name="HeaderComponent" xml:space="preserve">
    <value>ヘッダーコンポーネント</value>
  </data>
  <data name="FooterComponent" xml:space="preserve">
    <value>フッターコンポーネント</value>
  </data>
  <data name="CookieComponent" xml:space="preserve">
    <value>Cookieコンポーネント</value>
  </data>
  <data name="NavigationComponent" xml:space="preserve">
    <value>ナビゲーションコンポーネント</value>
  </data>
  <data name="SaveComponentSettings" xml:space="preserve">
    <value>コンポーネント設定を保存</value>
  </data>
  <data name="Processing" xml:space="preserve">
    <value>処理中...</value>
  </data>
  <data name="PageConfigurationTitle" xml:space="preserve">
    <value>ページ設定</value>
  </data>
  <data name="PageConfigurationList" xml:space="preserve">
    <value>ページ設定一覧</value>
  </data>
  <data name="PageConfigurationListDescription" xml:space="preserve">
    <value>ウェブサイトページの構造とコンポーネントを管理・設定</value>
  </data>
  <data name="CreateNewPage" xml:space="preserve">
    <value>新規ページ作成</value>
  </data>
  <data name="EditPage" xml:space="preserve">
    <value>ページ編集</value>
  </data>
  <data name="PageName" xml:space="preserve">
    <value>ページ名</value>
  </data>
  <data name="PageName_Zh" xml:space="preserve">
    <value>ページ名（中国語）</value>
  </data>
  <data name="PageName_En" xml:space="preserve">
    <value>ページ名（英語）</value>
  </data>
  <data name="PageName_Ja" xml:space="preserve">
    <value>ページ名（日本語）</value>
  </data>
  <data name="PageKey" xml:space="preserve">
    <value>ページキー</value>
  </data>
  <data name="Route" xml:space="preserve">
    <value>ルート</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>ステータス</value>
  </data>
  <data name="ComponentCount" xml:space="preserve">
    <value>コンポーネント数</value>
  </data>
  <data name="LastUpdated" xml:space="preserve">
    <value>最終更新</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="ColumnSpan" xml:space="preserve">
    <value>列幅</value>
  </data>
  <data name="Publish" xml:space="preserve">
    <value>公開</value>
  </data>
  <data name="Unpublish" xml:space="preserve">
    <value>非公開</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>公開済み</value>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>下書き</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>無効</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>不明</value>
  </data>
  <data name="ComponentLibrary" xml:space="preserve">
    <value>コンポーネントライブラリ</value>
  </data>
  <data name="DragComponentsToPage" xml:space="preserve">
    <value>コンポーネントをページにドラッグ</value>
  </data>
  <data name="SearchComponents" xml:space="preserve">
    <value>コンポーネントを検索</value>
  </data>
  <data name="ComponentProperties" xml:space="preserve">
    <value>コンポーネントプロパティ</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>プレビュー</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>作成</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="EmptyPageCanvas" xml:space="preserve">
    <value>空のページキャンバス</value>
  </data>
  <data name="DragComponentsHere" xml:space="preserve">
    <value>ここにコンポーネントをドラッグ</value>
  </data>
  <data name="EditComponent" xml:space="preserve">
    <value>コンポーネント編集</value>
  </data>
  <data name="ToggleVisibility" xml:space="preserve">
    <value>表示切替</value>
  </data>
  <data name="DeleteComponent" xml:space="preserve">
    <value>コンポーネント削除</value>
  </data>
  <data name="DragToReorder" xml:space="preserve">
    <value>ドラッグして並び替え</value>
  </data>
  <data name="ComponentParameters" xml:space="preserve">
    <value>コンポーネントパラメータ</value>
  </data>
  <data name="SelectComponentToEdit" xml:space="preserve">
    <value>編集するコンポーネントを選択</value>
  </data>
  <data name="ClickComponentToShowProperties" xml:space="preserve">
    <value>コンポーネントをクリックしてプロパティを表示</value>
  </data>
  <data name="DropHere" xml:space="preserve">
    <value>ここにドロップ</value>
  </data>
  <data name="LoadingTemplates" xml:space="preserve">
    <value>テンプレートを読み込み中...</value>
  </data>
  <data name="LoadPageListError" xml:space="preserve">
    <value>ページ一覧の読み込み中にエラーが発生しました</value>
  </data>
  <data name="PageKeyAlreadyExists" xml:space="preserve">
    <value>ページキーが既に存在します。別のキー値を使用してください</value>
  </data>
  <data name="RouteAlreadyExists" xml:space="preserve">
    <value>ルートが既に存在します。別のルートパスを使用してください</value>
  </data>
  <data name="PageCreateSuccess" xml:space="preserve">
    <value>ページが正常に作成されました</value>
  </data>
  <data name="PageUpdateSuccess" xml:space="preserve">
    <value>ページが正常に更新されました</value>
  </data>
  <data name="PageUpdateError" xml:space="preserve">
    <value>ページの更新に失敗しました。後でもう一度お試しください</value>
  </data>
  <data name="PageDeleteSuccess" xml:space="preserve">
    <value>ページが正常に削除されました</value>
  </data>
  <data name="PageDeleteError" xml:space="preserve">
    <value>ページの削除に失敗しました。後でもう一度お試しください</value>
  </data>
  <data name="PagePublishSuccess" xml:space="preserve">
    <value>ページが正常に公開されました</value>
  </data>
  <data name="PagePublishError" xml:space="preserve">
    <value>ページの公開に失敗しました。後でもう一度お試しください</value>
  </data>
  <data name="PageUnpublishSuccess" xml:space="preserve">
    <value>ページが正常に非公開に設定されました</value>
  </data>
  <data name="PageUnpublishError" xml:space="preserve">
    <value>ページの非公開設定に失敗しました。後でもう一度お試しください</value>
  </data>
  <data name="PleaseWaitSavingSettings" xml:space="preserve">
    <value>しばらくお待ちください、設定を保存中</value>
  </data>
  <data name="ComponentDescription" xml:space="preserve">
    <value>{0}コンポーネント</value>
  </data>
  <data name="GeneralCategory" xml:space="preserve">
    <value>一般</value>
  </data>
  <data name="DropComponentHere" xml:space="preserve">
    <value>ここにコンポーネントをドロップ</value>
  </data>
  <data name="MoveComponentHere" xml:space="preserve">
    <value>ここにコンポーネントをドロップ</value>
  </data>
  <data name="ComponentInfo" xml:space="preserve">
    <value>コンポーネント情報</value>
  </data>
  <data name="ComponentType" xml:space="preserve">
    <value>タイプ</value>
  </data>
  <data name="Template" xml:space="preserve">
    <value>テンプレート</value>
  </data>
  <data name="DisplayOrder" xml:space="preserve">
    <value>順序</value>
  </data>
  <data name="ComponentVisible" xml:space="preserve">
    <value>コンポーネント表示</value>
  </data>
  <data name="DeleteComponentConfirm" xml:space="preserve">
    <value>このコンポーネントを削除してもよろしいですか？</value>
  </data>
  <data name="ComponentParametersJson" xml:space="preserve">
    <value>コンポーネントパラメータ (JSON)</value>
  </data>
  <data name="VisibilitySettings" xml:space="preserve">
    <value>表示設定</value>
  </data>
  <data name="Visible" xml:space="preserve">
    <value>表示</value>
  </data>
  <data name="Hidden" xml:space="preserve">
    <value>非表示</value>
  </data>
  <data name="QuickActions" xml:space="preserve">
    <value>クイックアクション</value>
  </data>
  <data name="BackToPageList" xml:space="preserve">
    <value>ページリストに戻る</value>
  </data>
  <data name="WebsiteManage" xml:space="preserve">
    <value>サイト管理</value>
  </data>
  <data name="PageContentManage" xml:space="preserve">
    <value>コンテンツ管理</value>
  </data>
  <data name="LoadPageContentListError" xml:space="preserve">
    <value>コンテンツリストの読み込み中にエラーが発生しました。後でもう一度お試しください。</value>
  </data>
  <data name="LoadPageContentError" xml:space="preserve">
    <value>ページコンテンツの読み込み中にエラーが発生しました。後でもう一度お試しください。</value>
  </data>
  <data name="PageContentSaveSuccess" xml:space="preserve">
    <value>ページコンテンツが正常に保存されました！</value>
  </data>
  <data name="PageContentSaveError" xml:space="preserve">
    <value>ページコンテンツの保存中にエラーが発生しました。後でもう一度お試しください。</value>
  </data>
  <data name="PageContentPublishSuccess" xml:space="preserve">
    <value>ページコンテンツが正常に公開されました！</value>
  </data>
  <data name="PageContentPublishError" xml:space="preserve">
    <value>ページコンテンツの公開中にエラーが発生しました。後でもう一度お試しください。</value>
  </data>
  <data name="PageContentUnpublishSuccess" xml:space="preserve">
    <value>コンテンツの公開が正常に取り消されました！</value>
  </data>
  <data name="PageContentUnpublishError" xml:space="preserve">
    <value>コンテンツの公開取り消し中にエラーが発生しました。後でもう一度お試しください。</value>
  </data>
  <data name="EditPageContent" xml:space="preserve">
    <value>ページコンテンツの編集</value>
  </data>
  <data name="SavePageContent" xml:space="preserve">
    <value>コンテンツを保存</value>
  </data>
  <data name="PublishPageContent" xml:space="preserve">
    <value>コンテンツを公開</value>
  </data>
  <data name="UnpublishPageContent" xml:space="preserve">
    <value>公開を取り消す</value>
  </data>
  <data name="PreviewPageContent" xml:space="preserve">
    <value>プレビュー</value>
  </data>
  <data name="ComponentContent" xml:space="preserve">
    <value>コンポーネントコンテンツ</value>
  </data>
  <data name="MultilingualContent" xml:space="preserve">
    <value>多言語コンテンツ</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>中国語</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>英語</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>日本語</value>
  </data>
  <data name="ContentRequired" xml:space="preserve">
    <value>コンテンツは空にできません</value>
  </data>
  <data name="NoComponents" xml:space="preserve">
    <value>このページにはコンポーネント設定がありません</value>
  </data>
  <data name="PageContentStatus" xml:space="preserve">
    <value>コンテンツステータス</value>
  </data>
  <data name="Review" xml:space="preserve">
    <value>レビュー中</value>
  </data>
  <data name="Archived" xml:space="preserve">
    <value>アーカイブ済み</value>
  </data>
  <data name="InvalidContentId" xml:space="preserve">
    <value>無効なコンテンツID</value>
  </data>
  <data name="FailedToLoadFormFields" xml:space="preserve">
    <value>フォームフィールド設定の読み込みに失敗しました</value>
  </data>
  <data name="FailedToLoadMultilingualFields" xml:space="preserve">
    <value>多言語フィールドの読み込みに失敗しました</value>
  </data>
  <data name="ManagePageContentData" xml:space="preserve">
    <value>設定済みページの具体的なコンテンツデータを管理</value>
  </data>
  <data name="SearchPageKeywords" xml:space="preserve">
    <value>ページキーワードを検索...</value>
  </data>
  <data name="NoContentCreated" xml:space="preserve">
    <value>コンテンツ未作成</value>
  </data>
  <data name="ComponentsCount" xml:space="preserve">
    <value>個のコンポーネント</value>
  </data>
  <data name="CreateContent" xml:space="preserve">
    <value>コンテンツを作成</value>
  </data>
  <data name="NoComponentsConfigured" xml:space="preserve">
    <value>このページにはまだコンポーネントが設定されていません。まずページ設定でコンポーネントを追加してください。</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>by</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>有効にする</value>
  </data>
  <data name="SelectEventType" xml:space="preserve">
    <value>イベントタイプを選択してください</value>
  </data>
  <data name="Establishment" xml:space="preserve">
    <value>設立</value>
  </data>
  <data name="Expansion" xml:space="preserve">
    <value>拡張</value>
  </data>
  <data name="ProductLaunch" xml:space="preserve">
    <value>製品発売</value>
  </data>
  <data name="Acquisition" xml:space="preserve">
    <value>買収</value>
  </data>
  <data name="Partnership" xml:space="preserve">
    <value>提携</value>
  </data>
  <data name="Award" xml:space="preserve">
    <value>受賞</value>
  </data>
  <data name="Milestone" xml:space="preserve">
    <value>マイルストーン</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>その他</value>
  </data>
  <data name="EditHistoryEvent" xml:space="preserve">
    <value>歴史イベント編集</value>
  </data>
  <data name="EventTitleZhRequired" xml:space="preserve">
    <value>中国語のイベントタイトルは必須です</value>
  </data>
  <data name="LoadTabError" xml:space="preserve">
    <value>タブコンテンツの読み込みに失敗しました</value>
  </data>
  <data name="LoadHistoryError" xml:space="preserve">
    <value>履歴記録の読み込みに失敗しました</value>
  </data>
  <data name="HistoryTabNotLoaded" xml:space="preserve">
    <value>まず企業歴史タブを読み込んでください</value>
  </data>
  <data name="LoadingHistoryData" xml:space="preserve">
    <value>履歴データを読み込み中...</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>再試行</value>
  </data>
  <data name="ExecutiveInformation" xml:space="preserve">
    <value>役員情報</value>
  </data>
    <data name="IsPresident" xml:space="preserve">
    <value>社長</value>
  </data>
  <data name="AddExecutive" xml:space="preserve">
    <value>役員追加</value>
  </data>
  <data name="President" xml:space="preserve">
    <value>社長</value>
  </data>
  <data name="Executive" xml:space="preserve">
    <value>役員</value>
  </data>
  <data name="NoExecutiveRecords" xml:space="preserve">
    <value>役員記録がありません</value>
  </data>
  <data name="OrganizationStructure" xml:space="preserve">
    <value>組織図</value>
  </data>
  <data name="AddDepartment" xml:space="preserve">
    <value>部門追加</value>
  </data>
  <data name="RootDepartment" xml:space="preserve">
    <value>ルート部門</value>
  </data>
  <data name="NoOrganizationRecords" xml:space="preserve">
    <value>組織記録がありません</value>
  </data>
  <data name="BasicContactInfo" xml:space="preserve">
    <value>基本連絡先情報</value>
  </data>
  <data name="EditContactInfo" xml:space="preserve">
    <value>連絡先情報編集</value>
  </data>
  <data name="NoContactInfo" xml:space="preserve">
    <value>連絡先情報が設定されていません</value>
  </data>
  <data name="CompanyLocations" xml:space="preserve">
    <value>拠点情報</value>
  </data>
  <data name="AddLocation" xml:space="preserve">
    <value>拠点追加</value>
  </data>
  <data name="LocationCoordinates" xml:space="preserve">
    <value>位置座標</value>
  </data>
  <data name="Headquarters" xml:space="preserve">
    <value>本社</value>
  </data>
  <data name="PrimaryLocation" xml:space="preserve">
    <value>主要拠点</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>支社</value>
  </data>
  <data name="Factory" xml:space="preserve">
    <value>工場</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>オフィス</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>倉庫</value>
  </data>
  <data name="ResearchCenter" xml:space="preserve">
    <value>研究所</value>
  </data>
  <data name="NoLocationRecords" xml:space="preserve">
    <value>拠点記録がありません</value>
  </data>
  <data name="Laboratory" xml:space="preserve">
    <value>研究所</value>
  </data>
  <data name="ViewOnMap" xml:space="preserve">
    <value>地図で表示</value>
  </data>
  <data name="CSRActivities" xml:space="preserve">
    <value>CSR活動</value>
  </data>
  <data name="AddCSRActivity" xml:space="preserve">
    <value>CSR活動追加</value>
  </data>
  <data name="Environment" xml:space="preserve">
    <value>環境保護</value>
  </data>
  <data name="SocialContribution" xml:space="preserve">
    <value>社会貢献</value>
  </data>
  <data name="Governance" xml:space="preserve">
    <value>ガバナンス</value>
  </data>
  <data name="CommunitySupport" xml:space="preserve">
    <value>地域社会</value>
  </data>
  <data name="EmployeeWelfare" xml:space="preserve">
    <value>従業員福利</value>
  </data>
  <data name="DisasterRelief" xml:space="preserve">
    <value>災害救助</value>
  </data>
  <data name="Education" xml:space="preserve">
    <value>教育</value>
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value>医療</value>
  </data>
  <data name="Ongoing" xml:space="preserve">
    <value>実施中</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="NoCSRActivityRecords" xml:space="preserve">
    <value>CSR活動記録がありません</value>
  </data>
  <data name="TotalActivities" xml:space="preserve">
    <value>合計活動数</value>
  </data>
  <data name="OngoingActivities" xml:space="preserve">
    <value>実施中活動</value>
  </data>
  <data name="ActivitiesWithReports" xml:space="preserve">
    <value>報告書ありの活動</value>
  </data>
  <data name="FinancialReports" xml:space="preserve">
    <value>財務報告書</value>
  </data>
  <data name="ManageFinancialReportsDescription" xml:space="preserve">
    <value>企業の財務報告書、年次報告書、四半期報告書などの文書を管理</value>
  </data>
  <data name="AddFinancialReport" xml:space="preserve">
    <value>財務報告書追加</value>
  </data>
  <data name="AnnualReport" xml:space="preserve">
    <value>年次報告書</value>
  </data>
  <data name="QuarterlyReport" xml:space="preserve">
    <value>四半期報告書</value>
  </data>
  <data name="EarningsRelease" xml:space="preserve">
    <value>決算発表</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>期間</value>
  </data>
  <data name="NoFinancialReportRecords" xml:space="preserve">
    <value>財務報告書記録がありません</value>
  </data>
  <data name="ShareholderMeetings" xml:space="preserve">
    <value>株主総会</value>
  </data>
  <data name="ManageShareholderMeetingsDescription" xml:space="preserve">
    <value>株主総会、会議文書、関連情報を管理</value>
  </data>
  <data name="AddShareholderMeeting" xml:space="preserve">
    <value>株主総会追加</value>
  </data>
  <data name="MeetingTitle" xml:space="preserve">
    <value>会議タイトル</value>
  </data>
  <data name="Scheduled" xml:space="preserve">
    <value>予定済み</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>実施中</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>完了</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>中止</value>
  </data>
  <data name="Documents" xml:space="preserve">
    <value>個の文書</value>
  </data>
  <data name="NoDocuments" xml:space="preserve">
    <value>文書なし</value>
  </data>
  <data name="NoShareholderMeetingRecords" xml:space="preserve">
    <value>株主総会記録がありません</value>
  </data>
  <data name="TotalFinancialReports" xml:space="preserve">
    <value>報告書総数</value>
  </data>
  <data name="PublishedReports" xml:space="preserve">
    <value>公開済み報告書</value>
  </data>
  <data name="TotalMeetings" xml:space="preserve">
    <value>会議総数</value>
  </data>
  <data name="UpcomingMeetings" xml:space="preserve">
    <value>開催予定</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>レコードが見つかりません</value>
  </data>
  <data name="LoadRecordError" xml:space="preserve">
    <value>レコードの読み込みエラー</value>
  </data>
  <data name="SaveExecutiveSuccess" xml:space="preserve">
    <value>役員情報が正常に保存されました！</value>
  </data>
  <data name="SaveExecutiveError" xml:space="preserve">
    <value>役員情報の保存エラー</value>
  </data>
  <data name="DeleteExecutiveSuccess" xml:space="preserve">
    <value>役員情報が正常に削除されました！</value>
  </data>
  <data name="DeleteExecutiveError" xml:space="preserve">
    <value>役員情報の削除エラー</value>
  </data>
  <data name="SaveOrganizationSuccess" xml:space="preserve">
    <value>組織構造が正常に保存されました！</value>
  </data>
  <data name="SaveOrganizationError" xml:space="preserve">
    <value>組織構造の保存エラー</value>
  </data>
  <data name="DeleteOrganizationSuccess" xml:space="preserve">
    <value>組織構造が正常に削除されました！</value>
  </data>
  <data name="DeleteOrganizationError" xml:space="preserve">
    <value>組織構造の削除エラー</value>
  </data>
  <data name="ConfirmDeleteExecutive" xml:space="preserve">
    <value>この役員を削除してもよろしいですか？</value>
  </data>
  <data name="ConfirmDeleteOrganization" xml:space="preserve">
    <value>この部門を削除してもよろしいですか？</value>
  </data>
  <data name="SelectPhoto" xml:space="preserve">
    <value>写真を選択</value>
  </data>
  <data name="ExecutiveNameRequired" xml:space="preserve">
    <value>役員名は必須です</value>
  </data>
  <data name="DepartmentNameRequired" xml:space="preserve">
    <value>部門名は必須です</value>
  </data>
  <data name="BusinessInfoTitle" xml:space="preserve">
    <value>事業情報</value>
  </data>
  <data name="BusinessDivisions" xml:space="preserve">
    <value>事業部門</value>
  </data>
  <data name="ProductServices" xml:space="preserve">
    <value>製品・サービス</value>
  </data>
  <data name="DivisionName" xml:space="preserve">
    <value>部門名</value>
  </data>
  <data name="DivisionDescription" xml:space="preserve">
    <value>部門説明</value>
  </data>
  <data name="DivisionServices" xml:space="preserve">
    <value>サービス内容</value>
  </data>
  <data name="DivisionImage" xml:space="preserve">
    <value>部門メイン画像</value>
  </data>
  <data name="DivisionIcon" xml:space="preserve">
    <value>部門アイコン</value>
  </data>
  <data name="AddBusinessDivision" xml:space="preserve">
    <value>事業部門を追加</value>
  </data>
  <data name="EditBusinessDivision" xml:space="preserve">
    <value>事業部門を編集</value>
  </data>
  <data name="DeleteBusinessDivision" xml:space="preserve">
    <value>事業部門を削除</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>製品名</value>
  </data>
  <data name="ProductDescription" xml:space="preserve">
    <value>製品説明</value>
  </data>
  <data name="ProductFeatures" xml:space="preserve">
    <value>製品特徴</value>
  </data>
  <data name="ProductSpecifications" xml:space="preserve">
    <value>技術仕様</value>
  </data>
  <data name="BusinessDivision" xml:space="preserve">
    <value>所属部門</value>
  </data>
  <data name="ProductCategory" xml:space="preserve">
    <value>製品カテゴリ</value>
  </data>
  <data name="ProductImages" xml:space="preserve">
    <value>製品画像</value>
  </data>
  <data name="ProductDocuments" xml:space="preserve">
    <value>製品資料</value>
  </data>
  <data name="ProductPrice" xml:space="preserve">
    <value>製品価格</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>通貨単位</value>
  </data>
  <data name="AddProductService" xml:space="preserve">
    <value>製品・サービスを追加</value>
  </data>
  <data name="EditProductService" xml:space="preserve">
    <value>製品・サービスを編集</value>
  </data>
  <data name="DeleteProductService" xml:space="preserve">
    <value>製品・サービスを削除</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>製品</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>サービス</value>
  </data>
  <data name="Solution" xml:space="preserve">
    <value>ソリューション</value>
  </data>
  <data name="Technology" xml:space="preserve">
    <value>技術</value>
  </data>
  <data name="DocumentName" xml:space="preserve">
    <value>資料名</value>
  </data>
  <data name="DocumentType" xml:space="preserve">
    <value>資料タイプ</value>
  </data>
  <data name="FileSize" xml:space="preserve">
    <value>ファイルサイズ</value>
  </data>
  <data name="UploadDate" xml:space="preserve">
    <value>アップロード日</value>
  </data>
  <data name="AddDocument" xml:space="preserve">
    <value>資料を追加</value>
  </data>
  <data name="RemoveDocument" xml:space="preserve">
    <value>資料を削除</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>表示</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>フィルター</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>検索</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>有効</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>無効</value>
  </data>
  <data name="NoData" xml:space="preserve">
    <value>データなし</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>読み込み中</value>
  </data>
  <data name="SaveSuccess" xml:space="preserve">
    <value>保存しました</value>
  </data>
  <data name="DeleteSuccess" xml:space="preserve">
    <value>削除しました</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>保存に失敗しました</value>
  </data>
  <data name="DeleteError" xml:space="preserve">
    <value>削除に失敗しました</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>削除してもよろしいですか？</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>検証に失敗しました</value>
  </data>
  <data name="NewsManagement" xml:space="preserve">
    <value>ニュース管理</value>
  </data>
  <data name="NewsManagementDescription" xml:space="preserve">
    <value>企業ニュースコンテンツの管理、公開、編集、審査機能を含む</value>
  </data>
  <data name="NewsList" xml:space="preserve">
    <value>ニュース一覧</value>
  </data>
  <data name="DraftNews" xml:space="preserve">
    <value>下書きニュース</value>
  </data>
  <data name="ScheduledNews" xml:space="preserve">
    <value>予約公開</value>
  </data>
  <data name="ReviewNews" xml:space="preserve">
    <value>審査待ち</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>統計分析</value>
  </data>
  <data name="CreateNews" xml:space="preserve">
    <value>ニュース作成</value>
  </data>
  <data name="EditNews" xml:space="preserve">
    <value>ニュース編集</value>
  </data>
  <data name="DeleteNews" xml:space="preserve">
    <value>ニュース削除</value>
  </data>
  <data name="BatchPublish" xml:space="preserve">
    <value>一括公開</value>
  </data>
  <data name="BatchArchive" xml:space="preserve">
    <value>一括アーカイブ</value>
  </data>
  <data name="BatchApprove" xml:space="preserve">
    <value>一括承認</value>
  </data>
  <data name="BatchReject" xml:space="preserve">
    <value>一括却下</value>
  </data>
  <data name="SearchNews" xml:space="preserve">
    <value>ニュース検索</value>
  </data>
  <data name="SearchDraftNews" xml:space="preserve">
    <value>下書き検索</value>
  </data>
  <data name="SearchScheduledNews" xml:space="preserve">
    <value>予約公開検索</value>
  </data>
  <data name="SearchReviewNews" xml:space="preserve">
    <value>審査待ち検索</value>
  </data>
  <data name="AllTypes" xml:space="preserve">
    <value>すべてのタイプ</value>
  </data>
  <data name="Featured" xml:space="preserve">
    <value>おすすめ</value>
  </data>
  <data name="PendingReview" xml:space="preserve">
    <value>審査待ち</value>
  </data>
  <data name="TotalPublished" xml:space="preserve">
    <value>公開済み総数</value>
  </data>
  <data name="TodayPublished" xml:space="preserve">
    <value>本日公開</value>
  </data>
  <data name="FeaturedNews" xml:space="preserve">
    <value>おすすめニュース</value>
  </data>
  <data name="NewsTypeDistribution" xml:space="preserve">
    <value>ニュースタイプ分布</value>
  </data>
  <data name="PublishTrend" xml:space="preserve">
    <value>公開トレンド</value>
  </data>
  <data name="TopViewedNews" xml:space="preserve">
    <value>人気ニュース</value>
  </data>
  <data name="ExportStatistics" xml:space="preserve">
    <value>統計エクスポート</value>
  </data>
  <data name="RefreshStatistics" xml:space="preserve">
    <value>統計更新</value>
  </data>
  <data name="NoDraftNews" xml:space="preserve">
    <value>下書きニュースがありません</value>
  </data>
  <data name="NoDraftNewsDescription" xml:space="preserve">
    <value>まだ下書きニュースを作成していません</value>
  </data>
  <data name="NoScheduledNews" xml:space="preserve">
    <value>予約公開ニュースがありません</value>
  </data>
  <data name="NoScheduledNewsDescription" xml:space="preserve">
    <value>まだ予約公開のニュースを設定していません</value>
  </data>
  <data name="NoReviewNews" xml:space="preserve">
    <value>審査待ちニュースがありません</value>
  </data>
  <data name="NoReviewNewsDescription" xml:space="preserve">
    <value>現在審査待ちのニュースはありません</value>
  </data>
  <data name="CreateFirstNews" xml:space="preserve">
    <value>最初のニュースを作成</value>
  </data>
  <data name="UntitledNews" xml:space="preserve">
    <value>無題のニュース</value>
  </data>
  <data name="NoSummary" xml:space="preserve">
    <value>要約なし</value>
  </data>
  <data name="ChartComingSoon" xml:space="preserve">
    <value>チャート機能は近日公開予定</value>
  </data>
  <data name="NoDataAvailable" xml:space="preserve">
    <value>データがありません</value>
  </data>
  <data name="ProcessScheduledPublishing" xml:space="preserve">
    <value>予約公開処理</value>
  </data>
  <data name="ConfirmDeleteNews" xml:space="preserve">
    <value>このニュースを削除してもよろしいですか？</value>
  </data>
  <data name="ConfirmBatchPublish" xml:space="preserve">
    <value>選択したニュースを一括公開してもよろしいですか？</value>
  </data>
  <data name="ConfirmBatchArchive" xml:space="preserve">
    <value>選択したニュースを一括アーカイブしてもよろしいですか？</value>
  </data>
  <data name="ConfirmApproveNews" xml:space="preserve">
    <value>このニュースを承認してもよろしいですか？</value>
  </data>
  <data name="ConfirmRejectNews" xml:space="preserve">
    <value>このニュースを却下してもよろしいですか？</value>
  </data>
  <data name="SelectNewsFirst" xml:space="preserve">
    <value>まずニュースを選択してください</value>
  </data>
  <data name="CreateNewsSuccess" xml:space="preserve">
    <value>ニュースが正常に作成されました</value>
  </data>
  <data name="CreateNewsError" xml:space="preserve">
    <value>ニュースの作成に失敗しました</value>
  </data>
  <data name="UpdateNewsSuccess" xml:space="preserve">
    <value>ニュースが正常に更新されました</value>
  </data>
  <data name="UpdateNewsError" xml:space="preserve">
    <value>ニュースの更新に失敗しました</value>
  </data>
  <data name="DeleteNewsSuccess" xml:space="preserve">
    <value>ニュースが正常に削除されました</value>
  </data>
  <data name="DeleteNewsError" xml:space="preserve">
    <value>ニュースの削除に失敗しました</value>
  </data>
  <data name="SetFeaturedSuccess" xml:space="preserve">
    <value>おすすめに設定されました</value>
  </data>
  <data name="UnsetFeaturedSuccess" xml:space="preserve">
    <value>おすすめから削除されました</value>
  </data>
  <data name="ToggleFeaturedError" xml:space="preserve">
    <value>おすすめ状態の更新に失敗しました</value>
  </data>
  <data name="BatchOperationSuccess" xml:space="preserve">
    <value>一括操作が正常に完了しました</value>
  </data>
  <data name="BatchOperationError" xml:space="preserve">
    <value>一括操作に失敗しました</value>
  </data>
  <data name="BatchPublishSuccess" xml:space="preserve">
    <value>一括公開が正常に完了しました</value>
  </data>
  <data name="BatchUnpublishSuccess" xml:space="preserve">
    <value>一括非公開が正常に完了しました</value>
  </data>
  <data name="BatchArchiveSuccess" xml:space="preserve">
    <value>一括アーカイブが正常に完了しました</value>
  </data>
  <data name="BatchDeleteSuccess" xml:space="preserve">
    <value>一括削除が正常に完了しました</value>
  </data>
  <data name="InvalidBatchOperation" xml:space="preserve">
    <value>無効な一括操作です</value>
  </data>
  <data name="SchedulePublishSuccess" xml:space="preserve">
    <value>予約公開が正常に設定されました</value>
  </data>
  <data name="SchedulePublishError" xml:space="preserve">
    <value>予約公開の設定に失敗しました</value>
  </data>
  <data name="ReviewActionSuccess" xml:space="preserve">
    <value>審査操作が正常に完了しました</value>
  </data>
  <data name="ReviewActionError" xml:space="preserve">
    <value>審査操作に失敗しました</value>
  </data>
  <data name="LoadFormError" xml:space="preserve">
    <value>フォームの読み込みに失敗しました</value>
  </data>
  <data name="ExportFeatureComingSoon" xml:space="preserve">
    <value>エクスポート機能は近日公開予定</value>
  </data>
  <data name="NewsNotFound" xml:space="preserve">
    <value>ニュースが見つかりません</value>
  </data>
  <data name="NewsType_CompanyNews" xml:space="preserve">
    <value>企業ニュース</value>
  </data>
  <data name="NewsType_PressRelease" xml:space="preserve">
    <value>プレスリリース</value>
  </data>
  <data name="NewsType_ProductUpdate" xml:space="preserve">
    <value>製品更新</value>
  </data>
  <data name="NewsType_Event" xml:space="preserve">
    <value>イベント</value>
  </data>
  <data name="NewsType_MediaCoverage" xml:space="preserve">
    <value>メディア掲載</value>
  </data>
  <data name="NewsType_Announcement" xml:space="preserve">
    <value>お知らせ</value>
  </data>
  <data name="BusinessInfoDescription" xml:space="preserve">
    <value>企業の事業部門と製品・サービス情報を管理します。</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>画像</value>
  </data>
  <data name="NoBusinessDivisionsYet" xml:space="preserve">
    <value>まだ事業部門が追加されていません。</value>
  </data>
  <data name="SelectBusinessDivision" xml:space="preserve">
    <value>事業部門を選択</value>
  </data>
  <data name="Currency_JPY" xml:space="preserve">
    <value>JPY (日本円)</value>
  </data>
  <data name="Currency_USD" xml:space="preserve">
    <value>USD (米ドル)</value>
  </data>
  <data name="Currency_EUR" xml:space="preserve">
    <value>EUR (ユーロ)</value>
  </data>
  <data name="Currency_CNY" xml:space="preserve">
    <value>CNY (中国人民元)</value>
  </data>
  <data name="FeatureInProgress" xml:space="preserve">
    <value>機能開発中</value>
  </data>
  <data name="ImageAndDocManagementComingSoon" xml:space="preserve">
    <value>画像アップロードとドキュメント管理機能は将来のバージョンで実装されます。</value>
  </data>
  <data name="AllCategories" xml:space="preserve">
    <value>すべてのカテゴリ</value>
  </data>
  <data name="AllDivisions" xml:space="preserve">
    <value>すべての部門</value>
  </data>
  <data name="SearchProductsPlaceholder" xml:space="preserve">
    <value>製品を検索...</value>
  </data>
  <data name="ProductImage" xml:space="preserve">
    <value>製品画像</value>
  </data>
  <data name="NoProductServicesYet" xml:space="preserve">
  <value>まだ製品やサービスが追加されていません。</value>
  </data>
  <data name="NewsThumbnail" xml:space="preserve">
    <value>ニュースサムネイル</value>
  </data>
  <data name="AdminNewsStatisticsTitle" xml:space="preserve">
    <value>ニュース統計</value>
  </data>
  <data name="AdminNewsStatisticsDescription" xml:space="preserve">
    <value>ニュース公開統計と分析データを表示</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>一覧に戻る</value>
  </data>
  <data name="Drafts" xml:space="preserve">
    <value>下書き</value>
  </data>
  <data name="ReviewStatistics" xml:space="preserve">
    <value>審査統計</value>
  </data>
  <data name="MonthlyPublishTrend" xml:space="preserve">
    <value>月次公開トレンド</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>タイトル</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>タイプ</value>
  </data>
  <data name="Views" xml:space="preserve">
    <value>閲覧数</value>
  </data>
  <data name="Untitled" xml:space="preserve">
    <value>無題</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>審査待ち</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>承認済み</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>拒否済み</value>
  </data>
  <data name="PublishedCount" xml:space="preserve">
    <value>公開数</value>
  </data>
  <data name="NewsType" xml:space="preserve">
    <value>ニュースタイプ</value>
  </data>
  <data name="NewsType_IndustryNews" xml:space="preserve">
    <value>業界ニュース</value>
  </data>
  <data name="NewsType_ProductNews" xml:space="preserve">
    <value>製品ニュース</value>
  </data>
  <data name="NewsType_EventNews" xml:space="preserve">
    <value>イベントニュース</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>優先度</value>
  </data>
  <data name="Priority_Low" xml:space="preserve">
    <value>低</value>
  </data>
  <data name="Priority_Normal" xml:space="preserve">
    <value>普通</value>
  </data>
  <data name="Priority_High" xml:space="preserve">
    <value>高</value>
  </data>
  <data name="Priority_Urgent" xml:space="preserve">
    <value>緊急</value>
  </data>
  <data name="PublishImmediately" xml:space="preserve">
    <value>すぐに公開</value>
  </data>
  <data name="AllowComments" xml:space="preserve">
    <value>コメントを許可</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>概要</value>
  </data>
  <data name="Content" xml:space="preserve">
    <value>内容</value>
  </data>
  <data name="SeoKeywords" xml:space="preserve">
    <value>SEOキーワード</value>
  </data>
  <data name="GlobalSeoKeywords" xml:space="preserve">
    <value>グローバルSEOキーワード</value>
  </data>
  <data name="Tags" xml:space="preserve">
    <value>タグ</value>
  </data>
  <data name="EnterTagsPlaceholder" xml:space="preserve">
    <value>タグを入力（カンマ区切り）</value>
  </data>
  <data name="CompanyDescriptionAndPhilosophy" xml:space="preserve">
    <value>会社概要と経営理念</value>
  </data>
  <data name="CompanyDescriptionPlaceholder" xml:space="preserve">
    <value>会社概要を入力してください...</value>
  </data>
  <data name="PhilosophyPlaceholder" xml:space="preserve">
    <value>経営理念を入力してください...</value>
  </data>
  <data name="YearSuffix" xml:space="preserve">
    <value>年</value>
  </data>
  <data name="AccessInfo" xml:space="preserve">
    <value>アクセス情報</value>
  </data>
  <data name="MapLink" xml:space="preserve">
    <value>地図リンク</value>
  </data>
  <data name="ActivityDescription" xml:space="preserve">
    <value>活動説明</value>
  </data>
  <data name="ActivityImages" xml:space="preserve">
    <value>活動画像</value>
  </data>
  <data name="ActivitySummary" xml:space="preserve">
    <value>活動概要</value>
  </data>
  <data name="ActivityImpact" xml:space="preserve">
    <value>活動の影響</value>
  </data>
  <data name="ActivityTitle" xml:space="preserve">
    <value>活動タイトル</value>
  </data>
  <data name="AddComponent" xml:space="preserve">
    <value>コンポーネント追加</value>
  </data>
  <data name="AddHistoryEvent" xml:space="preserve">
    <value>沿革追加</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>住所</value>
  </data>
  <data name="AllStatus" xml:space="preserve">
    <value>全ステータス</value>
  </data>
  <data name="BackToMessageList" xml:space="preserve">
    <value>メッセージ一覧に戻る</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>基本情報</value>
  </data>
  <data name="Biography" xml:space="preserve">
    <value>略歴</value>
  </data>
  <data name="BusinessCooperation" xml:space="preserve">
    <value>業務提携</value>
  </data>
  <data name="BusinessHours" xml:space="preserve">
    <value>営業時間</value>
  </data>
  <data name="CNY" xml:space="preserve">
    <value>人民元</value>
  </data>
  <data name="CSRActivitiesDeveloping" xml:space="preserve">
    <value>CSR活動管理機能は開発中です...</value>
  </data>
  <data name="CSRCategory" xml:space="preserve">
    <value>CSRカテゴリ</value>
  </data>
  <data name="Capital" xml:space="preserve">
    <value>資本金</value>
  </data>
  <data name="Complaint" xml:space="preserve">
    <value>苦情</value>
  </data>
  <data name="CompanyDescription" xml:space="preserve">
    <value>企業概要</value>
  </data>
  <data name="CompanyHistory" xml:space="preserve">
    <value>企業沿革</value>
  </data>
  <data name="CompanyHistoryEditDeveloping" xml:space="preserve">
    <value>企業沿革編集機能は開発中です</value>
  </data>
  <data name="CompanyInfoTitle" xml:space="preserve">
    <value>企業情報</value>
  </data>
  <data name="ConfirmDeleteHistoryRecord" xml:space="preserve">
    <value>この沿革記録を削除してもよろしいですか？</value>
  </data>
  <data name="ContactInfo" xml:space="preserve">
    <value>連絡先情報</value>
  </data>
  <data name="ContactInfoDeveloping" xml:space="preserve">
    <value>連絡先情報管理機能は開発中です...</value>
  </data>
  <data name="CreatedAt" xml:space="preserve">
    <value>作成日時</value>
  </data>
  <data name="DealResult" xml:space="preserve">
    <value>処理結果</value>
  </data>
  <data name="DealResultPlaceholder" xml:space="preserve">
    <value>処理結果を入力...</value>
  </data>
  <data name="DeleteFunctionDeveloping" xml:space="preserve">
    <value>削除機能は開発中です</value>
  </data>
  <data name="DeleteHistoryError" xml:space="preserve">
    <value>企業沿革の削除に失敗しました</value>
  </data>
  <data name="DeleteHistorySuccess" xml:space="preserve">
    <value>企業沿革が正常に削除されました！</value>
  </data>
  <data name="DepartmentDescription" xml:space="preserve">
    <value>部門説明</value>
  </data>
  <data name="DepartmentName" xml:space="preserve">
    <value>部門名</value>
  </data>
  <data name="DocumentDescription" xml:space="preserve">
    <value>文書説明</value>
  </data>
  <data name="DocumentFile" xml:space="preserve">
    <value>文書ファイル</value>
  </data>
  <data name="DocumentTitle" xml:space="preserve">
    <value>文書タイトル</value>
  </data>
  <data name="EUR" xml:space="preserve">
    <value>ユーロ</value>
  </data>
  <data name="EditHistoryDeveloping" xml:space="preserve">
    <value>沿革編集機能は開発中です</value>
  </data>
  <data name="EmailInvalid" xml:space="preserve">
    <value>メールアドレスの形式が正しくありません</value>
  </data>
  <data name="EmployeeScale" xml:space="preserve">
    <value>企業規模</value>
  </data>
  <data name="EmployeeScale_Enterprise" xml:space="preserve">
    <value>大企業 (1000人以上)</value>
  </data>
  <data name="EmployeeScale_Large" xml:space="preserve">
    <value>大規模 (301-1000人)</value>
  </data>
  <data name="EmployeeScale_Medium" xml:space="preserve">
    <value>中規模 (51-300人)</value>
  </data>
  <data name="EmployeeScale_Small" xml:space="preserve">
    <value>小規模 (1-50人)</value>
  </data>
  <data name="EnableCompanyInfo" xml:space="preserve">
    <value>企業情報を有効にする</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>終了日</value>
  </data>
  <data name="ErrorLoadingCompanyInfo" xml:space="preserve">
    <value>企業情報の読み込みに失敗しました。後でもう一度お試しください。</value>
  </data>
  <data name="EstablishedDate" xml:space="preserve">
    <value>設立年月日</value>
  </data>
  <data name="EstablishedDateRequired" xml:space="preserve">
    <value>設立年月日は必須です</value>
  </data>
  <data name="EventDate" xml:space="preserve">
    <value>事件日付</value>
  </data>
  <data name="EventDateRequired" xml:space="preserve">
    <value>事件日付は必須です</value>
  </data>
  <data name="EventDescription" xml:space="preserve">
    <value>事件説明</value>
  </data>
  <data name="EventImage" xml:space="preserve">
    <value>事件画像</value>
  </data>
  <data name="EventTitle" xml:space="preserve">
    <value>事件タイトル</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>事件種別</value>
  </data>
  <data name="EventTypeRequired" xml:space="preserve">
    <value>事件種別は必須です</value>
  </data>
  <data name="ExecutiveName" xml:space="preserve">
    <value>役員名</value>
  </data>
  <data name="ExecutiveOrganization" xml:space="preserve">
    <value>役員紹介/組織図</value>
  </data>
  <data name="ExecutiveOrganizationDeveloping" xml:space="preserve">
    <value>役員情報と組織構成管理機能は開発中です...</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>FAX</value>
  </data>
  <data name="GeneralInquiry" xml:space="preserve">
    <value>一般問い合わせ</value>
  </data>
  <data name="HistoryNotFound" xml:space="preserve">
    <value>沿革記録が見つかりません</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>重要</value>
  </data>
  <data name="InvestorRelations" xml:space="preserve">
    <value>投資家向け情報</value>
  </data>
  <data name="InvestorRelationsDeveloping" xml:space="preserve">
    <value>投資家向け情報管理機能は開発中です...</value>
  </data>
  <data name="IsPrimary" xml:space="preserve">
    <value>主要拠点かどうか</value>
  </data>
  <data name="IsPublished" xml:space="preserve">
    <value>公開済みかどうか</value>
  </data>
  <data name="JPY" xml:space="preserve">
    <value>日本円</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>緯度</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>レベル</value>
  </data>
  <data name="LocationName" xml:space="preserve">
    <value>拠点名</value>
  </data>
  <data name="LocationType" xml:space="preserve">
    <value>拠点種別</value>
  </data>
  <data name="LogoUrl" xml:space="preserve">
    <value>ロゴURL</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>経度</value>
  </data>
  <data name="MarkAsImportant" xml:space="preserve">
    <value>重要としてマーク</value>
  </data>
  <data name="MarkAsInProgress" xml:space="preserve">
    <value>進行中としてマーク</value>
  </data>
  <data name="MeetingDate" xml:space="preserve">
    <value>会議日程</value>
  </data>
  <data name="MeetingDescription" xml:space="preserve">
    <value>会議説明</value>
  </data>
  <data name="MeetingAgenda" xml:space="preserve">
    <value>会議議題</value>
  </data>
  <data name="MeetingLocation" xml:space="preserve">
    <value>会議場所</value>
  </data>
  <data name="MeetingDocuments" xml:space="preserve">
    <value>会議文書</value>
  </data>
  <data name="MeetingStatus" xml:space="preserve">
    <value>会議ステータス</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>メッセージ</value>
  </data>
  <data name="MessageContent" xml:space="preserve">
    <value>メッセージ内容</value>
  </data>
  <data name="MessageDetails" xml:space="preserve">
    <value>メッセージ詳細</value>
  </data>
  <data name="MessageInfo" xml:space="preserve">
    <value>メッセージ情報</value>
  </data>
  <data name="MessageManagement" xml:space="preserve">
    <value>メッセージ管理</value>
  </data>
  <data name="MessageManagementDescription" xml:space="preserve">
    <value>顧客メッセージの表示と処理</value>
  </data>
  <data name="NetIncome" xml:space="preserve">
    <value>当期純利益</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>新規</value>
  </data>
  <data name="NewMessage" xml:space="preserve">
    <value>新しいメッセージ</value>
  </data>
  <data name="NewMessages" xml:space="preserve">
    <value>新しいメッセージ</value>
  </data>
  <data name="NoCompanyHistoryRecords" xml:space="preserve">
    <value>企業沿革記録がありません</value>
  </data>
  <data name="NoMessages" xml:space="preserve">
    <value>メッセージがありません</value>
  </data>
  <data name="NoTitle" xml:space="preserve">
    <value>タイトルなし</value>
  </data>
  <data name="ParentDepartment" xml:space="preserve">
    <value>上位部門</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>電話</value>
  </data>
  <data name="PhotoUrl" xml:space="preserve">
    <value>写真L</value>
  </data>
  <data name="Philosophy" xml:space="preserve">
    <value>経営理念</value>
  </data>
  <data name="PleaseSelect" xml:space="preserve">
    <value>選択してください</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>役職</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>郵便番号</value>
  </data>
  <data name="ProcessedAt" xml:space="preserve">
    <value>処理日時</value>
  </data>
  <data name="PublishDate" xml:space="preserve">
    <value>公開日</value>
  </data>
  <data name="Quarter" xml:space="preserve">
    <value>四半期</value>
  </data>
  <data name="RegistrationNumber" xml:space="preserve">
    <value>登記番号</value>
  </data>
  <data name="ReportFile" xml:space="preserve">
    <value>レポートファイル</value>
  </data>
  <data name="ReportPeriod" xml:space="preserve">
    <value>レポート期間</value>
  </data>
  <data name="ReportSummary" xml:space="preserve">
    <value>レポート概要</value>
  </data>
  <data name="ReportTitle" xml:space="preserve">
    <value>レポートタイトル</value>
  </data>
  <data name="ReportType" xml:space="preserve">
    <value>レポート種別</value>
  </data>
  <data name="ReportYear" xml:space="preserve">
    <value>レポート年</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>年</value>
  </data>
  <data name="Revenue" xml:space="preserve">
    <value>売上高</value>
  </data>
  <data name="SaveBasicInfoSuccess" xml:space="preserve">
    <value>基本情報が正常に保存されました！</value>
  </data>
  <data name="SaveDealResult" xml:space="preserve">
    <value>処理結果保存</value>
  </data>
  <data name="SaveHistoryError" xml:space="preserve">
    <value>企業沿革の保存に失敗しました</value>
  </data>
  <data name="SaveHistorySuccess" xml:space="preserve">
    <value>企業沿革が正常に保存されました！</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>名前、メール、内容を検索...</value>
  </data>
  <data name="SourcePage" xml:space="preserve">
    <value>ソースページ</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>開始日</value>
  </data>
  <data name="StatusFilter" xml:space="preserve">
    <value>ステータスフィルタ</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>今日</value>
  </data>
  <data name="TotalAssets" xml:space="preserve">
    <value>総資産</value>
  </data>
  <data name="USD" xml:space="preserve">
    <value>米ドル</value>
  </data>
  <data name="UpdatedAt" xml:space="preserve">
    <value>更新日時</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>ウェブサイト</value>
  </data>
  <data name="WebsiteInvalid" xml:space="preserve">
    <value>ウェブサイトの形式が正しくありません</value>
  </data>
  <data name="EnterText" xml:space="preserve">
    <value>テキストを入力</value>
  </data>
  <data name="FieldRenderError" xml:space="preserve">
    <value>フィールドのレンダリングに失敗</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>確認</value>
  </data>
  <data name="FormGroups_Other" xml:space="preserve">
    <value>その他</value>
  </data>
  <data name="FileUpload_ClickToUpload" xml:space="preserve">
    <value>クリックしてアップロード</value>
  </data>
  <data name="FileUpload_OrDragDrop" xml:space="preserve">
    <value>またはファイルをここにドラッグ&amp;ドロップ</value>
  </data>
  <data name="FileUpload_AllFiles" xml:space="preserve">
    <value>全てのファイルタイプ</value>
  </data>
  <data name="FileUpload_MaxSize" xml:space="preserve">
    <value>最大</value>
  </data>
  <data name="FileUpload_Preview" xml:space="preserve">
    <value>プレビュー</value>
  </data>
  <data name="FileUpload_DeleteFile" xml:space="preserve">
    <value>ファイルを削除</value>
  </data>
  <data name="FileUpload_DeleteConfirm" xml:space="preserve">
    <value>このファイルを削除してもよろしいですか？</value>
  </data>
  <data name="FileUpload_DeleteSuccess" xml:space="preserve">
    <value>ファイルが正常に削除されました</value>
  </data>
  <data name="FileUpload_DeleteError" xml:space="preserve">
    <value>削除に失敗しました</value>
  </data>
  <data name="FileUpload_UploadError" xml:space="preserve">
    <value>アップロードに失敗しました</value>
  </data>
  <data name="FileType_Image" xml:space="preserve">
    <value>画像</value>
  </data>
  <data name="FileType_Video" xml:space="preserve">
    <value>ビデオ</value>
  </data>
  <data name="FileType_PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="FileType_Word" xml:space="preserve">
    <value>Word文書</value>
  </data>
  <data name="AllStatuses" xml:space="preserve">
    <value>すべてのステータス</value>
  </data>
  <data name="ExternalUrl" xml:space="preserve">
    <value>外部リンク</value>
  </data>
  <data name="LoadingData" xml:space="preserve">
    <value>データを読み込み中...</value>
  </data>
  <data name="NewsSource" xml:space="preserve">
    <value>ニュースソース</value>
  </data>
  <data name="NewsSource_External" xml:space="preserve">
    <value>外部</value>
  </data>
  <data name="NewsSource_Internal" xml:space="preserve">
    <value>内部</value>
  </data>
  <data name="NewsSource_MediaReport" xml:space="preserve">
    <value>メディア報道</value>
  </data>
  <data name="NewsSource_Partner" xml:space="preserve">
    <value>パートナー</value>
  </data>
  <data name="NewsSource_PressRelease" xml:space="preserve">
    <value>プレスリリース</value>
  </data>
  <data name="NewsStatus" xml:space="preserve">
    <value>ニュースステータス</value>
  </data>
  <data name="NewsStatus_Draft" xml:space="preserve">
    <value>下書き</value>
  </data>
  <data name="NewsStatus_Published" xml:space="preserve">
    <value>公開済み</value>
  </data>
  <data name="NewsStatus_Review" xml:space="preserve">
    <value>審査中</value>
  </data>
  <data name="SearchNewsPlaceholder" xml:space="preserve">
    <value>ニュースタイトルを入力...</value>
  </data>
  <data name="SearchTitle" xml:space="preserve">
    <value>検索タイトル</value>
  </data>
  <data name="Searching" xml:space="preserve">
    <value>検索中...</value>
  </data>
  <data name="ThumbnailUrl" xml:space="preserve">
    <value>サムネイル</value>
  </data>
  <data name="TitleRequired" xml:space="preserve">
    <value>タイトルは必須です</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>更新に失敗しました</value>
  </data>
  <data name="UpdateSuccess" xml:space="preserve">
    <value>更新が完了しました</value>
  </data>
  <data name="ActiveJobs" xml:space="preserve">
    <value>募集中の求人</value>
  </data>
  <data name="AddNewJob" xml:space="preserve">
    <value>新しい求人を追加</value>
  </data>
  <data name="AllInterviewTypes" xml:space="preserve">
    <value>すべてのインタビュータイプ</value>
  </data>
  <data name="AllJobTypes" xml:space="preserve">
    <value>すべての職種</value>
  </data>
  <data name="AllLooksGood" xml:space="preserve">
    <value>すべて正常です</value>
  </data>
  <data name="ApplicationDeadline" xml:space="preserve">
    <value>応募締切</value>
  </data>
  <data name="AverageTranslationRate" xml:space="preserve">
    <value>平均翻訳率</value>
  </data>
  <data name="BatchActivate" xml:space="preserve">
    <value>一括有効化</value>
  </data>
  <data name="BatchDeactivate" xml:space="preserve">
    <value>一括無効化</value>
  </data>
  <data name="BatchUnpublish" xml:space="preserve">
    <value>一括非公開</value>
  </data>
  <data name="Benefits" xml:space="preserve">
    <value>福利厚生</value>
  </data>
  <data name="CompanyImpression" xml:space="preserve">
    <value>会社の印象</value>
  </data>
  <data name="CompletedFields" xml:space="preserve">
    <value>完了したフィールド</value>
  </data>
  <data name="ContentQualityAnalysis" xml:space="preserve">
    <value>コンテンツ品質分析</value>
  </data>
  <data name="ContentRichness" xml:space="preserve">
    <value>コンテンツ豊富性</value>
  </data>
  <data name="Coverage" xml:space="preserve">
    <value>カバレッジ</value>
  </data>
  <data name="CreateEmployeeInterview" xml:space="preserve">
    <value>社員インタビュー作成</value>
  </data>
  <data name="CreateFirstInterview" xml:space="preserve">
    <value>最初のインタビューを作成</value>
  </data>
  <data name="CreateJobPosition" xml:space="preserve">
    <value>求人ポジション作成</value>
  </data>
  <data name="EditJobPosition" xml:space="preserve">
    <value>求人ポジション編集</value>
  </data>  
  <data name="Deadline" xml:space="preserve">
    <value>締切</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>部署</value>
  </data>
  <data name="DraftInterviews" xml:space="preserve">
    <value>下書きインタビュー</value>
  </data>
  <data name="DraftJobs" xml:space="preserve">
    <value>下書き求人</value>
  </data>
  <data name="EmployeeInterviews" xml:space="preserve">
    <value>社員インタビュー</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>社員名</value>
  </data>
  <data name="EmploymentType" xml:space="preserve">
    <value>雇用形態</value>
  </data>
  <data name="ExperienceLevel" xml:space="preserve">
    <value>経験レベル</value>
  </data>
  <data name="Expired" xml:space="preserve">
    <value>期限切れ</value>
  </data>
  <data name="ExpiredJobs" xml:space="preserve">
    <value>期限切れ求人</value>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>データエクスポート</value>
  </data>
  <data name="FeaturedInterviews" xml:space="preserve">
    <value>注目インタビュー</value>
  </data>
  <data name="FeaturedJobs" xml:space="preserve">
    <value>注目求人</value>
  </data>
  <data name="InterviewContent" xml:space="preserve">
    <value>インタビュー内容</value>
  </data>
  <data name="InterviewDate" xml:space="preserve">
    <value>インタビュー日</value>
  </data>
  <data name="InterviewType" xml:space="preserve">
    <value>インタビュータイプ</value>
  </data>
  <data name="InterviewTypeDistribution" xml:space="preserve">
    <value>インタビュータイプ分布</value>
  </data>
  <data name="InterviewType_Management" xml:space="preserve">
    <value>管理職</value>
  </data>
  <data name="InterviewType_NewEmployee" xml:space="preserve">
    <value>新入社員</value>
  </data>
  <data name="InterviewType_Other" xml:space="preserve">
    <value>その他</value>
  </data>
  <data name="InterviewType_Sales" xml:space="preserve">
    <value>営業</value>
  </data>
  <data name="InterviewType_Technical" xml:space="preserve">
    <value>技術職</value>
  </data>
  <data name="InterviewType_Veteran" xml:space="preserve">
    <value>ベテラン社員</value>
  </data>
  <data name="IsFeatured" xml:space="preserve">
    <value>注目表示</value>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>職務内容</value>
  </data>
  <data name="JobPositions" xml:space="preserve">
    <value>求人ポジション</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>職種名</value>
  </data>
  <data name="JobType" xml:space="preserve">
    <value>職種</value>
  </data>
  <data name="JobTypeDistribution" xml:space="preserve">
    <value>職種分布</value>
  </data>
  <data name="ManageInterviews" xml:space="preserve">
    <value>インタビュー管理</value>
  </data>
  <data name="MultilingualCompleteness" xml:space="preserve">
    <value>多言語完成度</value>
  </data>
  <data name="NoInterviewDataAvailable" xml:space="preserve">
    <value>インタビューデータがありません</value>
  </data>
  <data name="NoInterviewsFound" xml:space="preserve">
    <value>インタビューが見つかりません</value>
  </data>
  <data name="NoJobDataAvailable" xml:space="preserve">
    <value>求人データがありません</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>順序</value>
  </data>
  <data name="OverviewStatistics" xml:space="preserve">
    <value>概要統計</value>
  </data>
  <data name="PostDate" xml:space="preserve">
    <value>投稿日</value>
  </data>
  <data name="ProbationPeriod" xml:space="preserve">
    <value>試用期間</value>
  </data>
  <data name="ProbationPeriodPlaceholder" xml:space="preserve">
    <value>例: 3ヶ月</value>
  </data>
  <data name="PublishedInterviews" xml:space="preserve">
    <value>公開済みインタビュー</value>
  </data>
  <data name="RecruitmentContentOptimal" xml:space="preserve">
    <value>採用コンテンツは最適です</value>
  </data>
  <data name="RefreshData" xml:space="preserve">
    <value>データ更新</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>応募要件</value>
  </data>
  <data name="SalaryMax" xml:space="preserve">
    <value>最高給与</value>
  </data>
  <data name="SalaryMin" xml:space="preserve">
    <value>最低給与</value>
  </data>
  <data name="SearchInterviews" xml:space="preserve">
    <value>インタビュー検索</value>
  </data>
  <data name="SearchJobs" xml:space="preserve">
    <value>求人検索</value>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>並び順</value>
  </data>
  <data name="Status_Archived" xml:space="preserve">
    <value>アーカイブ済み</value>
  </data>
  <data name="Status_Draft" xml:space="preserve">
    <value>下書き</value>
  </data>
  <data name="Status_Published" xml:space="preserve">
    <value>公開済み</value>
  </data>
  <data name="SuggestionsAndReminders" xml:space="preserve">
    <value>提案とリマインダー</value>
  </data>
  <data name="TagsPlaceholder" xml:space="preserve">
    <value>複数のタグはカンマで区切ってください</value>
  </data>
  <data name="TotalActiveJobs" xml:space="preserve">
    <value>総募集中求人数</value>
  </data>
  <data name="UpdateFrequency" xml:space="preserve">
    <value>更新頻度</value>
  </data>
  <data name="UpdatesPerWeek" xml:space="preserve">
    <value>週間更新回数</value>
  </data>
  <data name="WorkDescription" xml:space="preserve">
    <value>業務内容</value>
  </data>
  <data name="WorkLocation" xml:space="preserve">
    <value>勤務地</value>
  </data>
  <data name="WorkingHours" xml:space="preserve">
    <value>勤務時間</value>
  </data>
  <data name="WorkingHoursPlaceholder" xml:space="preserve">
    <value>例: 9:00-18:00</value>
  </data>
  <data name="Years" xml:space="preserve">
    <value>年</value>
  </data>
  <data name="YearsOfService" xml:space="preserve">
    <value>勤続年数</value>
  </data>
  <data name="RecruitmentManagement" xml:space="preserve">
    <value>採用管理</value>
  </data>
  <data name="JobType_NewGraduate" xml:space="preserve">
    <value>新卒採用</value>
  </data>
  <data name="JobType_MidCareer" xml:space="preserve">
    <value>中途採用</value>
  </data>
  <data name="JobType_Internal" xml:space="preserve">
    <value>内部採用</value>
  </data>
  <data name="EmploymentType_FullTime" xml:space="preserve">
    <value>正社員</value>
  </data>
  <data name="EmploymentType_PartTime" xml:space="preserve">
    <value>パートタイム</value>
  </data>
  <data name="EmploymentType_Contract" xml:space="preserve">
    <value>契約社員</value>
  </data>
  <data name="EmploymentType_Temporary" xml:space="preserve">
    <value>派遣社員</value>
  </data>
  <data name="EmploymentType_Internship" xml:space="preserve">
    <value>インターンシップ</value>
  </data>
  <data name="ExperienceLevel_Entry" xml:space="preserve">
    <value>新人・未経験</value>
  </data>
  <data name="ExperienceLevel_Junior" xml:space="preserve">
    <value>初級 / ジュニア</value>
  </data>
  <data name="ExperienceLevel_Mid" xml:space="preserve">
    <value>中級</value>
  </data>
  <data name="ExperienceLevel_Senior" xml:space="preserve">
    <value>上級 / シニア</value>
  </data>
  <data name="ExperienceLevel_Executive" xml:space="preserve">
    <value>管理職</value>
  </data>
  <!-- Missing Message Management Keys -->
  <data name="WaitingForCustomer" xml:space="preserve">
    <value>お客様からの返答待ち</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>会社</value>
  </data>
  <data name="DealResultSaved" xml:space="preserve">
    <value>対応結果を保存しました</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>名前</value>
  </data>
  <data name="CareerInquiry" xml:space="preserve">
    <value>採用に関するお問い合わせ</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>クローズ済み</value>
  </data>
  <data name="MarkAsClosed" xml:space="preserve">
    <value>クローズとしてマーク</value>
  </data>
  <data name="MarkAsResolved" xml:space="preserve">
    <value>解決済みとしてマーク</value>
  </data>
  <data name="MarkAsWaitingForResponse" xml:space="preserve">
    <value>返答待ちとしてマーク</value>
  </data>
  <data name="MediaInquiry" xml:space="preserve">
    <value>メディアからのお問い合わせ</value>
  </data>
  <data name="PartnershipInquiry" xml:space="preserve">
    <value>提携に関するお問い合わせ</value>
  </data>
  <data name="ProductInquiry" xml:space="preserve">
    <value>製品に関するお問い合わせ</value>
  </data>
  <data name="RemoveImportant" xml:space="preserve">
    <value>重要マークを削除</value>
  </data>
  <data name="Resolved" xml:space="preserve">
    <value>解決済み</value>
  </data>
  <data name="ServiceInquiry" xml:space="preserve">
    <value>サービスに関するお問い合わせ</value>
  </data>
  <data name="Spam" xml:space="preserve">
    <value>スパム</value>
  </data>
  <data name="TechnicalSupport" xml:space="preserve">
    <value>技術サポート</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>合計</value>
  </data>
  <data name="WaitingForResponse" xml:space="preserve">
    <value>返答待ち</value>
  </data>
  <data name="DataManagement" xml:space="preserve">
    <value>データ管理</value>
  </data>
  <data name="ComponentDataManagement" xml:space="preserve">
    <value>コンポーネントデータ管理</value>
  </data>
  <data name="LoadingComponentData" xml:space="preserve">
    <value>コンポーネントデータを読み込み中...</value>
  </data>
  <data name="LoadComponentDataError" xml:space="preserve">
    <value>コンポーネントデータの読み込みに失敗しました</value>
  </data>
  <data name="NoFormFieldsConfigured" xml:space="preserve">
    <value>フォームフィールドが設定されていません</value>
  </data>
  <data name="ComponentFormFieldsNotConfigured" xml:space="preserve">
    <value>このコンポーネントのvariants.jsonファイルにformFieldsが設定されていません</value>
  </data>
  <data name="SavingComponentData" xml:space="preserve">
    <value>コンポーネントデータを保存中...</value>
  </data>
  <data name="ComponentDataSaved" xml:space="preserve">
    <value>コンポーネントデータが正常に保存されました</value>
  </data>
  <data name="SaveComponentDataError" xml:space="preserve">
    <value>コンポーネントデータの保存に失敗しました</value>
  </data>
  <data name="NoFormDataToSave" xml:space="preserve">
    <value>保存するフォームデータがありません</value>
  </data>
  <data name="InvalidParameters" xml:space="preserve">
    <value>無効なパラメータです</value>
  </data>
  <data name="SerializeDataError" xml:space="preserve">
    <value>データのシリアライズに失敗しました</value>
  </data>
  <data name="DealResultRequired" xml:space="preserve">
    <value>処理結果は必須です</value>
  </data>
  <data name="DealResultSaveSuccess" xml:space="preserve">
    <value>処理結果の保存に成功しました</value>
  </data>

</root>