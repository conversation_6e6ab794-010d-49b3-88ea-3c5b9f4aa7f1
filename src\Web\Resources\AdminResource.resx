<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="SiteSettingsTitle" xml:space="preserve">
    <value>站点设置</value>
  </data>
  <data name="LoadSiteSettingsError" xml:space="preserve">
    <value>加载站点设置时发生错误，请稍后重试。</value>
  </data>
  <data name="SiteNameRequired" xml:space="preserve">
    <value>站点名称不能为空</value>
  </data>
  <data name="DomainRequired" xml:space="preserve">
    <value>域名不能为空</value>
  </data>
  <data name="InputDataError" xml:space="preserve">
    <value>输入数据有误，请检查后重试。</value>
  </data>
  <data name="BasicInfoSaved" xml:space="preserve">
    <value>基本信息设置已保存成功！</value>
  </data>
  <data name="SaveSettingsError" xml:space="preserve">
    <value>保存设置时发生错误</value>
  </data>
  <data name="SelectTheme" xml:space="preserve">
    <value>请选择要应用的主题</value>
  </data>
  <data name="InvalidTheme" xml:space="preserve">
    <value>所选主题无效或不存在</value>
  </data>
  <data name="ThemeAppliedSuccess" xml:space="preserve">
    <value>主题应用成功！页面将自动刷新以显示新主题效果。</value>
  </data>
  <data name="ThemeApplyFailed" xml:space="preserve">
    <value>主题应用失败，请稍后重试。</value>
  </data>
  <data name="SystemError" xml:space="preserve">
    <value>系统错误</value>
  </data>
  <data name="ComponentSettingsSaved" xml:space="preserve">
    <value>组件设置已保存成功！</value>
  </data>
  <data name="SelectFile" xml:space="preserve">
    <value>请选择要上传的文件</value>
  </data>
  <data name="UnsupportedFileType" xml:space="preserve">
    <value>不支持的文件类型，请上传图片文件</value>
  </data>
  <data name="FileSizeLimit" xml:space="preserve">
    <value>文件大小不能超过5MB</value>
  </data>
  <data name="UploadConfigError" xml:space="preserve">
    <value>上传配置错误</value>
  </data>
  <data name="ImageProcessFailed" xml:space="preserve">
    <value>图片处理失败</value>
  </data>
  <data name="UploadSuccess" xml:space="preserve">
    <value>上传成功</value>
  </data>
  <data name="UploadFailed" xml:space="preserve">
    <value>上传失败</value>
  </data>
  <data name="LogoFileNameEmpty" xml:space="preserve">
    <value>Logo文件名不能为空</value>
  </data>
  <data name="LogoFileNotExists" xml:space="preserve">
    <value>Logo文件不存在</value>
  </data>
  <data name="GenerateFaviconFailed" xml:space="preserve">
    <value>生成网站图标失败</value>
  </data>
  <data name="FaviconGenerateSuccess" xml:space="preserve">
    <value>网站图标生成成功</value>
  </data>
  <data name="BasicInfo" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="ThemeSettings" xml:space="preserve">
    <value>主题设置</value>
  </data>
  <data name="ComponentSettings" xml:space="preserve">
    <value>组件设置</value>
  </data>
  <data name="BasicInfoSettingsTitle" xml:space="preserve">
    <value>基本信息设置</value>
  </data>
  <data name="SiteName" xml:space="preserve">
    <value>站点名称</value>
  </data>
  <data name="Domain" xml:space="preserve">
    <value>域名</value>
  </data>
  <data name="DefaultLanguage" xml:space="preserve">
    <value>默认语言</value>
  </data>
  <data name="Logo" xml:space="preserve">
    <value>Logo</value>
  </data>
  <data name="Favicon" xml:space="preserve">
    <value>网站图标</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存设置</value>
  </data>
  <data name="GenerateFavicon" xml:space="preserve">
    <value>从Logo生成</value>
  </data>
  <data name="SelectLogo" xml:space="preserve">
    <value>选择Logo</value>
  </data>
  <data name="SelectFavicon" xml:space="preserve">
    <value>选择网站图标</value>
  </data>
  <data name="ClickUploadLogo" xml:space="preserve">
    <value>点击上传</value>
  </data>
  <data name="ClickUploadFavicon" xml:space="preserve">
    <value>点击上传图标</value>
  </data>
  <data name="SaveBasicInfo" xml:space="preserve">
    <value>保存基本信息</value>
  </data>
  <data name="ThemeSettingsTitle" xml:space="preserve">
    <value>主题设置</value>
  </data>
  <data name="PleaseWaitProcessing" xml:space="preserve">
    <value>请稍候，正在处理...</value>
  </data>
  <data name="ApplyingTheme" xml:space="preserve">
    <value>正在应用主题...</value>
  </data>
  <data name="SavingBasicInfo" xml:space="preserve">
    <value>正在保存基本信息...</value>
  </data>
  <data name="SavingComponentSettings" xml:space="preserve">
    <value>正在保存组件设置...</value>
  </data>
  <data name="GeneratingFaviconFromLogo" xml:space="preserve">
    <value>正在从Logo生成网站图标...</value>
  </data>
  <data name="UploadingLogo" xml:space="preserve">
    <value>正在上传Logo...</value>
  </data>
  <data name="UploadingFavicon" xml:space="preserve">
    <value>正在上传网站图标...</value>
  </data>
  <data name="ApplyThemeError" xml:space="preserve">
    <value>应用主题时发生错误，请稍后重试</value>
  </data>
  <data name="SaveBasicInfoError" xml:space="preserve">
    <value>保存基本信息时发生错误，请稍后重试</value>
  </data>
  <data name="SaveComponentSettingsError" xml:space="preserve">
    <value>保存组件设置时发生错误，请稍后重试</value>
  </data>
  <data name="GenerateFaviconError" xml:space="preserve">
    <value>生成网站图标时发生错误</value>
  </data>
  <data name="PleaseUploadLogoFirst" xml:space="preserve">
    <value>请先上传Logo</value>
  </data>
  <data name="FaviconGeneratedSuccess" xml:space="preserve">
    <value>网站图标生成成功！</value>
  </data>
  <data name="UploadSuccessful" xml:space="preserve">
    <value>上传成功！</value>
  </data>
  <data name="BackendManagement" xml:space="preserve">
    <value>后台管理</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>看板</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>系统设置</value>
  </data>
  <data name="SiteSettings" xml:space="preserve">
    <value>站点设置</value>
  </data>
  <data name="AccountSettings" xml:space="preserve">
    <value>账户设置</value>
  </data>
  <data name="FrontendPage" xml:space="preserve">
    <value>前台页面</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>退出登录</value>
  </data>
  <data name="WelcomeAdmin" xml:space="preserve">
    <value>欢迎，管理员</value>
  </data>
  <data name="AccountSettingsTitle" xml:space="preserve">
    <value>账户设置</value>
  </data>
  <data name="ChangePasswordTitle" xml:space="preserve">
    <value>修改密码</value>
  </data>
  <data name="UpdateYourAccountPassword" xml:space="preserve">
    <value>更新您的账户密码</value>
  </data>
  <data name="ChangeEmailTitle" xml:space="preserve">
    <value>修改邮箱</value>
  </data>
  <data name="UpdateYourEmailAddress" xml:space="preserve">
    <value>更新您的邮箱地址</value>
  </data>
  <data name="SystemInformation" xml:space="preserve">
    <value>系统信息</value>
  </data>
  <data name="SystemVersion" xml:space="preserve">
    <value>系统版本</value>
  </data>
  <data name="RuntimeEnvironment" xml:space="preserve">
    <value>运行环境</value>
  </data>
  <data name="Database" xml:space="preserve">
    <value>数据库</value>
  </data>
  <data name="Authentication" xml:space="preserve">
    <value>身份认证</value>
  </data>
  <data name="DashboardTitle" xml:space="preserve">
    <value>管理看板</value>
  </data>
  <data name="WelcomeBack" xml:space="preserve">
    <value>欢迎回来</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="UserNameRequired" xml:space="preserve">
    <value>用户名不能为空</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>密码不能为空</value>
  </data>
  <data name="CurrentPasswordRequired" xml:space="preserve">
    <value>当前密码不能为空</value>
  </data>
  <data name="NewPasswordRequired" xml:space="preserve">
    <value>新密码不能为空</value>
  </data>
  <data name="PasswordLengthError" xml:space="preserve">
    <value>密码长度至少为{2}个字符</value>
  </data>
  <data name="PasswordMismatch" xml:space="preserve">
    <value>新密码和确认密码不匹配</value>
  </data>
  <data name="NewEmailRequired" xml:space="preserve">
    <value>新邮箱地址不能为空</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>邮箱格式不正确</value>
  </data>
  <data name="DomainMaxLength" xml:space="preserve">
    <value>域名最多200个字符</value>
  </data>
  <data name="DefaultLanguageRequired" xml:space="preserve">
    <value>请选择默认语言</value>
  </data>
  <data name="ConfirmPasswordRequired" xml:space="preserve">
    <value>确认密码不能为空</value>
  </data>
  <data name="CurrentTheme" xml:space="preserve">
    <value>当前使用主题</value>
  </data>
  <data name="ApplyTheme" xml:space="preserve">
    <value>应用主题</value>
  </data>
  <data name="CurrentInUse" xml:space="preserve">
    <value>当前使用中</value>
  </data>
  <data name="CountCode" xml:space="preserve">
    <value>统计代码</value>
  </data>
  <data name="GlobalComponentSettings" xml:space="preserve">
    <value>全局组件设置</value>
  </data>
  <data name="HeaderComponent" xml:space="preserve">
    <value>页头组件</value>
  </data>
  <data name="FooterComponent" xml:space="preserve">
    <value>页脚组件</value>
  </data>
  <data name="CookieComponent" xml:space="preserve">
    <value>Cookie组件</value>
  </data>
  <data name="NavigationComponent" xml:space="preserve">
    <value>导航组件</value>
  </data>
  <data name="SaveComponentSettings" xml:space="preserve">
    <value>保存组件设置</value>
  </data>
  <data name="Processing" xml:space="preserve">
    <value>处理中...</value>
  </data>
  <data name="PleaseWaitSavingSettings" xml:space="preserve">
    <value>请稍候，正在保存设置</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>记住我</value>
  </data>
  <data name="CurrentPassword" xml:space="preserve">
    <value>当前密码</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>新密码</value>
  </data>
  <data name="ConfirmNewPassword" xml:space="preserve">
    <value>确认新密码</value>
  </data>
  <data name="NewEmailAddress" xml:space="preserve">
    <value>新邮箱地址</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>确认密码</value>
  </data>
  <data name="HeaderStyle" xml:space="preserve">
    <value>页头样式</value>
  </data>
  <data name="FooterStyle" xml:space="preserve">
    <value>页脚样式</value>
  </data>
  <data name="NavigationStyle" xml:space="preserve">
    <value>导航样式</value>
  </data>
  <data name="PageConfigurationTitle" xml:space="preserve">
    <value>页面配置</value>
  </data>
  <data name="PageConfigurationList" xml:space="preserve">
    <value>页面配置列表</value>
  </data>
  <data name="PageConfigurationListDescription" xml:space="preserve">
    <value>管理和配置网站页面的结构和组件</value>
  </data>
  <data name="CreateNewPage" xml:space="preserve">
    <value>新增页面</value>
  </data>
  <data name="EditPage" xml:space="preserve">
    <value>编辑页面</value>
  </data>
  <data name="PageName" xml:space="preserve">
    <value>页面名称</value>
  </data>
  <data name="PageKey" xml:space="preserve">
    <value>页面键</value>
  </data>
  <data name="Route" xml:space="preserve">
    <value>路由</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="ComponentCount" xml:space="preserve">
    <value>组件数量</value>
  </data>
  <data name="LastUpdated" xml:space="preserve">
    <value>最后更新</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="Publish" xml:space="preserve">
    <value>发布</value>
  </data>
  <data name="Unpublish" xml:space="preserve">
    <value>取消发布</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>已发布</value>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>草稿</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>已禁用</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="PageEditor" xml:space="preserve">
    <value>页面编辑器</value>
  </data>
  <data name="ComponentLibrary" xml:space="preserve">
    <value>组件库</value>
  </data>
  <data name="PropertiesPanel" xml:space="preserve">
    <value>属性面板</value>
  </data>
  <data name="AddComponent" xml:space="preserve">
    <value>添加组件</value>
  </data>
  <data name="SavePage" xml:space="preserve">
    <value>保存页面</value>
  </data>
  <data name="PreviewPage" xml:space="preserve">
    <value>预览页面</value>
  </data>
  <data name="DeleteConfirm" xml:space="preserve">
    <value>确定要删除此页面吗？删除后无法恢复。</value>
  </data>
  <data name="ComponentConfiguration" xml:space="preserve">
    <value>组件配置</value>
  </data>
  <data name="MoveUp" xml:space="preserve">
    <value>上移</value>
  </data>
  <data name="MoveDown" xml:space="preserve">
    <value>下移</value>
  </data>
  <data name="RemoveComponent" xml:space="preserve">
    <value>移除组件</value>
  </data>

  <data name="ColumnSpan" xml:space="preserve">
    <value>列跨度</value>
  </data>

  <data name="AllStatus" xml:space="preserve">
    <value>全部状态</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>筛选</value>
  </data>
  <data name="Components" xml:space="preserve">
    <value>个组件</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>由</value>
  </data>
  <data name="ConfirmUnpublish" xml:space="preserve">
    <value>确定要取消发布此页面吗？</value>
  </data>
  <data name="ConfirmPublish" xml:space="preserve">
    <value>确定要发布此页面吗？</value>
  </data>
  <data name="ShowingResults" xml:space="preserve">
    <value>显示第 {0} - {1} 条，共 {2} 条记录</value>
  </data>
  <data name="NoPages" xml:space="preserve">
    <value>暂无页面</value>
  </data>
  <data name="NoPagesDescription" xml:space="preserve">
    <value>还没有创建任何页面，点击下方按钮开始创建第一个页面。</value>
  </data>
  <data name="BasicInformation" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="PageName_Zh" xml:space="preserve">
    <value>中文名称</value>
  </data>
  <data name="PageName_En" xml:space="preserve">
    <value>英文名称</value>
  </data>
  <data name="PageName_Ja" xml:space="preserve">
    <value>日文名称</value>
  </data>
  <data name="EnterPageName_Zh" xml:space="preserve">
    <value>请输入中文页面名称</value>
  </data>
  <data name="EnterPageName_En" xml:space="preserve">
    <value>请输入英文页面名称</value>
  </data>
  <data name="EnterPageName_Ja" xml:space="preserve">
    <value>请输入日文页面名称</value>
  </data>
  <data name="EnterPageKey" xml:space="preserve">
    <value>请输入页面键</value>
  </data>
  <data name="PageKeyHelp" xml:space="preserve">
    <value>页面键用于系统内部识别，只能包含字母、数字、下划线和连字符</value>
  </data>
  <data name="EnterRoute" xml:space="preserve">
    <value>请输入路由</value>
  </data>
  <data name="RouteHelp" xml:space="preserve">
    <value>页面访问路径，必须以/开头，如：/about</value>
  </data>
  <data name="LayoutTemplate" xml:space="preserve">
    <value>布局模板</value>
  </data>
  <data name="PublishStatus" xml:space="preserve">
    <value>发布状态</value>
  </data>
  <data name="PerformanceSettings" xml:space="preserve">
    <value>性能设置</value>
  </data>
  <data name="CacheSettings" xml:space="preserve">
    <value>缓存设置</value>
  </data>
  <data name="EnableImageLazyLoading" xml:space="preserve">
    <value>启用图片懒加载</value>
  </data>
  <data name="EnableComponentLazyLoading" xml:space="preserve">
    <value>启用组件懒加载</value>
  </data>
  <data name="ImageQuality" xml:space="preserve">
    <value>图片质量</value>
  </data>
  <data name="CacheDuration" xml:space="preserve">
    <value>缓存时长</value>
  </data>
  <data name="Minutes" xml:space="preserve">
    <value>分钟</value>
  </data>
  <data name="EnableOutputCache" xml:space="preserve">
    <value>启用输出缓存</value>
  </data>
  <data name="CreatePage" xml:space="preserve">
    <value>创建页面</value>
  </data>
  <data name="SaveChanges" xml:space="preserve">
    <value>保存更改</value>
  </data>
  <data name="NoComponentsYet" xml:space="preserve">
    <value>还没有添加任何组件</value>
  </data>
  <data name="ClickAddComponentToStart" xml:space="preserve">
    <value>点击"添加组件"按钮开始构建页面</value>
  </data>
  <data name="PublishedOn" xml:space="preserve">
    <value>发布于</value>
  </data>
  <data name="QuickActions" xml:space="preserve">
    <value>快速操作</value>
  </data>
  <data name="LoadPageListError" xml:space="preserve">
    <value>加载页面列表时发生错误</value>
  </data>
  <data name="PageKeyAlreadyExists" xml:space="preserve">
    <value>页面键已存在，请使用其他键值</value>
  </data>
  <data name="RouteAlreadyExists" xml:space="preserve">
    <value>路由已存在，请使用其他路由路径</value>
  </data>
  <data name="PageCreateSuccess" xml:space="preserve">
    <value>页面创建成功！</value>
  </data>
  <data name="PageCreateError" xml:space="preserve">
    <value>页面创建失败，请检查输入信息后重试</value>
  </data>
  <data name="LoadPageError" xml:space="preserve">
    <value>加载页面信息时发生错误</value>
  </data>
  <data name="PageUpdateSuccess" xml:space="preserve">
    <value>页面更新成功！</value>
  </data>
  <data name="PageUpdateError" xml:space="preserve">
    <value>页面更新失败，请稍后重试</value>
  </data>
  <data name="PageDeleteSuccess" xml:space="preserve">
    <value>页面删除成功！</value>
  </data>
  <data name="PageDeleteError" xml:space="preserve">
    <value>页面删除失败，请稍后重试</value>
  </data>
  <data name="PagePublishSuccess" xml:space="preserve">
    <value>页面发布成功！</value>
  </data>
  <data name="PagePublishError" xml:space="preserve">
    <value>页面发布失败，请稍后重试</value>
  </data>
  <data name="PageUnpublishSuccess" xml:space="preserve">
    <value>页面已取消发布！</value>
  </data>
  <data name="PageUnpublishError" xml:space="preserve">
    <value>取消发布失败，请稍后重试</value>
  </data>
  <data name="LoadComponentDataError" xml:space="preserve">
    <value>加载组件数据时发生错误</value>
  </data>
  <data name="ComponentDescription" xml:space="preserve">
    <value>{0}组件</value>
  </data>
  <data name="GeneralCategory" xml:space="preserve">
    <value>通用</value>
  </data>
  <data name="DropComponentHere" xml:space="preserve">
    <value>在此放置组件</value>
  </data>
  <data name="MoveComponentHere" xml:space="preserve">
    <value>在此放置组件</value>
  </data>
  <data name="SelectComponentToEdit" xml:space="preserve">
    <value>选择组件进行编辑</value>
  </data>
  <data name="ClickComponentToShowProperties" xml:space="preserve">
    <value>点击左侧组件显示属性编辑面板</value>
  </data>
  <data name="DropHere" xml:space="preserve">
    <value>放置在这里</value>
  </data>
  <data name="LoadingTemplates" xml:space="preserve">
    <value>正在加载模板...</value>
  </data>
  <data name="ComponentInfo" xml:space="preserve">
    <value>组件信息</value>
  </data>
  <data name="ComponentType" xml:space="preserve">
    <value>类型</value>
  </data>
  <data name="Template" xml:space="preserve">
    <value>模板</value>
  </data>
  <data name="DisplayOrder" xml:space="preserve">
    <value>顺序</value>
  </data>
  <data name="ComponentVisible" xml:space="preserve">
    <value>组件可见</value>
  </data>
  <data name="ComponentParameters" xml:space="preserve">
    <value>组件参数</value>
  </data>
  <data name="EditComponent" xml:space="preserve">
    <value>编辑组件</value>
  </data>
  <data name="DeleteComponent" xml:space="preserve">
    <value>删除组件</value>
  </data>
  <data name="DeleteComponentConfirm" xml:space="preserve">
    <value>确定要删除这个组件吗？</value>
  </data>
  <data name="ComponentParametersJson" xml:space="preserve">
    <value>组件参数 (JSON)</value>
  </data>
  <data name="VisibilitySettings" xml:space="preserve">
    <value>可见性设置</value>
  </data>
  <data name="Visible" xml:space="preserve">
    <value>可见</value>
  </data>
  <data name="Hidden" xml:space="preserve">
    <value>隐藏</value>
  </data>
  <data name="DragComponentsToPage" xml:space="preserve">
    <value>拖拽组件到页面区域</value>
  </data>
  <data name="SearchComponents" xml:space="preserve">
    <value>搜索组件</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>预览</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>创建</value>
  </data>
  <data name="EmptyPageCanvas" xml:space="preserve">
    <value>空白画布</value>
  </data>
  <data name="DragComponentsHere" xml:space="preserve">
    <value>拖拽组件到这里开始构建页面</value>
  </data>
  <data name="ToggleVisibility" xml:space="preserve">
    <value>切换可见性</value>
  </data>
  <data name="DragToReorder" xml:space="preserve">
    <value>拖拽排序</value>
  </data>
  <data name="ComponentProperties" xml:space="preserve">
    <value>组件属性</value>
  </data>
  <data name="BackToPageList" xml:space="preserve">
    <value>返回页面列表</value>
  </data>
  <data name="WebsiteManage" xml:space="preserve">
    <value>网站管理</value>
  </data>
  <data name="PageContentManage" xml:space="preserve">
    <value>内容管理</value>
  </data>
  <data name="LoadPageContentListError" xml:space="preserve">
    <value>加载网页内容列表时发生错误，请稍后重试。</value>
  </data>
  <data name="LoadPageContentError" xml:space="preserve">
    <value>加载网页内容时发生错误，请稍后重试。</value>
  </data>
  <data name="PageContentSaveSuccess" xml:space="preserve">
    <value>网页内容保存成功！</value>
  </data>
  <data name="PageContentSaveError" xml:space="preserve">
    <value>保存网页内容时发生错误，请稍后重试。</value>
  </data>
  <data name="PageContentPublishSuccess" xml:space="preserve">
    <value>网页内容发布成功！</value>
  </data>
  <data name="PageContentPublishError" xml:space="preserve">
    <value>发布网页内容时发生错误，请稍后重试。</value>
  </data>
  <data name="PageContentUnpublishSuccess" xml:space="preserve">
    <value>取消发布成功！</value>
  </data>
  <data name="PageContentUnpublishError" xml:space="preserve">
    <value>取消发布时发生错误，请稍后重试。</value>
  </data>
  <data name="EditPageContent" xml:space="preserve">
    <value>编辑网页内容</value>
  </data>
  <data name="SavePageContent" xml:space="preserve">
    <value>保存内容</value>
  </data>
  <data name="PublishPageContent" xml:space="preserve">
    <value>发布内容</value>
  </data>
  <data name="UnpublishPageContent" xml:space="preserve">
    <value>取消发布</value>
  </data>
  <data name="PreviewPageContent" xml:space="preserve">
    <value>预览</value>
  </data>
  <data name="ComponentContent" xml:space="preserve">
    <value>组件内容</value>
  </data>
  <data name="MultilingualContent" xml:space="preserve">
    <value>多语言内容</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>英文</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>日文</value>
  </data>
  <data name="ContentRequired" xml:space="preserve">
    <value>内容不能为空</value>
  </data>
  <data name="NoComponents" xml:space="preserve">
    <value>该页面暂无组件配置</value>
  </data>
  <data name="PageContentStatus" xml:space="preserve">
    <value>内容状态</value>
  </data>
  <data name="Review" xml:space="preserve">
    <value>待审核</value>
  </data>
  <data name="Archived" xml:space="preserve">
    <value>已归档</value>
  </data>
  <data name="InvalidContentId" xml:space="preserve">
    <value>无效的内容ID</value>
  </data>
  <data name="FailedToLoadFormFields" xml:space="preserve">
    <value>加载表单字段配置失败</value>
  </data>
  <data name="FailedToLoadMultilingualFields" xml:space="preserve">
    <value>加载多语言字段失败</value>
  </data>
  <data name="ManagePageContentData" xml:space="preserve">
    <value>管理已配置页面的具体内容数据</value>
  </data>
  <data name="SearchPageKeywords" xml:space="preserve">
    <value>搜索页面关键词...</value>
  </data>
  <data name="NoContentCreated" xml:space="preserve">
    <value>未创建内容</value>
  </data>
  <data name="ComponentsCount" xml:space="preserve">
    <value>个组件</value>
  </data>
  <data name="CreateContent" xml:space="preserve">
    <value>创建内容</value>
  </data>
  <data name="NoComponentsConfigured" xml:space="preserve">
    <value>该页面尚未配置任何组件，请先在页面配置中添加组件。</value>
  </data>
  <data name="CompanyInfoTitle" xml:space="preserve">
    <value>企业信息</value>
  </data>
  <data name="CompanyHistory" xml:space="preserve">
    <value>企业历史</value>
  </data>
  <data name="ExecutiveOrganization" xml:space="preserve">
    <value>高管介绍/组织架构图</value>
  </data>
  <data name="ContactInfo" xml:space="preserve">
    <value>联系信息</value>
  </data>
  <data name="CSRActivities" xml:space="preserve">
    <value>企业社会责任</value>
  </data>
  <data name="InvestorRelations" xml:space="preserve">
    <value>投资者关系</value>
  </data>
  <data name="CompanyCode" xml:space="preserve">
    <value>企业代码</value>
  </data>
  <data name="CompanyCodeRequired" xml:space="preserve">
    <value>企业代码不能为空</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>企业名称</value>
  </data>
  <data name="CompanyDescription" xml:space="preserve">
    <value>企业简介</value>
  </data>
  <data name="CompanyDescriptionAndPhilosophy" xml:space="preserve">
    <value>企业简介和经营理念</value>
  </data>
  <data name="CompanyDescriptionPlaceholder" xml:space="preserve">
    <value>请输入企业简介...</value>
  </data>
  <data name="Philosophy" xml:space="preserve">
    <value>经营理念</value>
  </data>
  <data name="PhilosophyPlaceholder" xml:space="preserve">
    <value>请输入经营理念...</value>
  </data>
  <data name="EstablishedDate" xml:space="preserve">
    <value>设立日期</value>
  </data>
  <data name="EstablishedDateRequired" xml:space="preserve">
    <value>设立日期不能为空</value>
  </data>
  <data name="RegistrationNumber" xml:space="preserve">
    <value>工商注册号</value>
  </data>
  <data name="Capital" xml:space="preserve">
    <value>注册资本</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>货币单位</value>
  </data>
  <data name="LogoUrl" xml:space="preserve">
    <value>企业LOGO</value>
  </data>
  <data name="EmployeeScale" xml:space="preserve">
    <value>企业规模</value>
  </data>
  <data name="EventDate" xml:space="preserve">
    <value>事件日期</value>
  </data>
  <data name="EventDateRequired" xml:space="preserve">
    <value>事件日期不能为空</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>事件类型</value>
  </data>
  <data name="EventTypeRequired" xml:space="preserve">
    <value>事件类型不能为空</value>
  </data>
  <data name="EventTitle" xml:space="preserve">
    <value>事件标题</value>
  </data>
  <data name="EventDescription" xml:space="preserve">
    <value>事件描述</value>
  </data>
  <data name="EventImage" xml:space="preserve">
    <value>事件配图</value>
  </data>
  <data name="ExecutiveName" xml:space="preserve">
    <value>姓名</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>职位</value>
  </data>
  <data name="Biography" xml:space="preserve">
    <value>简历</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>致辞</value>
  </data>
  <data name="IsPresident" xml:space="preserve">
    <value>是否为总裁</value>
  </data>
  <data name="PhotoUrl" xml:space="preserve">
    <value>照片</value>
  </data>
  <data name="DepartmentName" xml:space="preserve">
    <value>部门名称</value>
  </data>
  <data name="DepartmentDescription" xml:space="preserve">
    <value>部门描述</value>
  </data>
  <data name="ParentDepartment" xml:space="preserve">
    <value>上级部门</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>层级</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>电话号码</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>传真号码</value>
  </data>
  <data name="EmailInvalid" xml:space="preserve">
    <value>电子邮箱格式不正确</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>官方网站</value>
  </data>
  <data name="WebsiteInvalid" xml:space="preserve">
    <value>官方网站地址格式不正确</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>邮政编码</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>地址</value>
  </data>
  <data name="BusinessHours" xml:space="preserve">
    <value>营业时间</value>
  </data>
  <data name="AccessInfo" xml:space="preserve">
    <value>交通信息</value>
  </data>
  <data name="MapLink" xml:space="preserve">
    <value>地图链接</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>纬度</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>经度</value>
  </data>
  <data name="LocationName" xml:space="preserve">
    <value>地点名称</value>
  </data>
  <data name="LocationType" xml:space="preserve">
    <value>地点类型</value>
  </data>
  <data name="IsPrimary" xml:space="preserve">
    <value>主要地点</value>
  </data>
  <data name="ActivityTitle" xml:space="preserve">
    <value>活动名称</value>
  </data>
  <data name="ActivityDescription" xml:space="preserve">
    <value>活动描述</value>
  </data>
  <data name="ActivitySummary" xml:space="preserve">
    <value>成果总结</value>
  </data>
  <data name="ActivityImpact" xml:space="preserve">
    <value>活动影响</value>
  </data>
  <data name="CSRCategory" xml:space="preserve">
    <value>活动类型</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>开始日期</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>结束日期</value>
  </data>
  <data name="ActivityImages" xml:space="preserve">
    <value>活动图片</value>
  </data>
  <data name="ReportFile" xml:space="preserve">
    <value>报告文件</value>
  </data>
  <data name="ReportType" xml:space="preserve">
    <value>报告类型</value>
  </data>
  <data name="ReportPeriod" xml:space="preserve">
    <value>报告期间</value>
  </data>
  <data name="ReportYear" xml:space="preserve">
    <value>报告年份</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>年份</value>
  </data>
  <data name="Quarter" xml:space="preserve">
    <value>报告季度</value>
  </data>
  <data name="ReportTitle" xml:space="preserve">
    <value>报告标题</value>
  </data>
  <data name="ReportSummary" xml:space="preserve">
    <value>报告摘要</value>
  </data>
  <data name="PublishDate" xml:space="preserve">
    <value>发布日期</value>
  </data>
  <data name="Revenue" xml:space="preserve">
    <value>营业收入</value>
  </data>
  <data name="NetIncome" xml:space="preserve">
    <value>净利润</value>
  </data>
  <data name="TotalAssets" xml:space="preserve">
    <value>总资产</value>
  </data>
  <data name="IsPublished" xml:space="preserve">
    <value>已发布</value>
  </data>
  <data name="MeetingDate" xml:space="preserve">
    <value>会议日期</value>
  </data>
  <data name="MeetingTitle" xml:space="preserve">
    <value>会议标题</value>
  </data>
  <data name="MeetingDescription" xml:space="preserve">
    <value>会议描述</value>
  </data>
  <data name="MeetingAgenda" xml:space="preserve">
    <value>会议议程</value>
  </data>
  <data name="MeetingLocation" xml:space="preserve">
    <value>会议地点</value>
  </data>
  <data name="MeetingStatus" xml:space="preserve">
    <value>会议状态</value>
  </data>
  <data name="MeetingDocuments" xml:space="preserve">
    <value>会议文档</value>
  </data>
  <data name="DocumentTitle" xml:space="preserve">
    <value>文档标题</value>
  </data>
  <data name="DocumentDescription" xml:space="preserve">
    <value>文档描述</value>
  </data>
  <data name="DocumentType" xml:space="preserve">
    <value>文档类型</value>
  </data>
  <data name="DocumentFile" xml:space="preserve">
    <value>文档文件</value>
  </data>
  <data name="ErrorLoadingCompanyInfo" xml:space="preserve">
    <value>加载企业信息时发生错误，请稍后重试。</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>输入数据验证失败，请检查后重试。</value>
  </data>
  <data name="SaveBasicInfoSuccess" xml:space="preserve">
    <value>企业基本信息保存成功！</value>
  </data>
  <data name="SaveHistorySuccess" xml:space="preserve">
    <value>企业历史保存成功！</value>
  </data>
  <data name="SaveHistoryError" xml:space="preserve">
    <value>保存企业历史时发生错误</value>
  </data>
  <data name="HistoryNotFound" xml:space="preserve">
    <value>未找到指定的历史记录</value>
  </data>
  <data name="DeleteHistorySuccess" xml:space="preserve">
    <value>企业历史删除成功！</value>
  </data>
  <data name="DeleteHistoryError" xml:space="preserve">
    <value>删除企业历史时发生错误</value>
  </data>
  <data name="PleaseSelect" xml:space="preserve">
    <value>请选择</value>
  </data>
  <data name="JPY" xml:space="preserve">
    <value>日元</value>
  </data>
  <data name="USD" xml:space="preserve">
    <value>美元</value>
  </data>
  <data name="CNY" xml:space="preserve">
    <value>人民币</value>
  </data>
  <data name="EUR" xml:space="preserve">
    <value>欧元</value>
  </data>
  <data name="EmployeeScale_Small" xml:space="preserve">
    <value>小型 (1-50人)</value>
  </data>
  <data name="EmployeeScale_Medium" xml:space="preserve">
    <value>中型 (51-300人)</value>
  </data>
  <data name="EmployeeScale_Large" xml:space="preserve">
    <value>大型 (301-1000人)</value>
  </data>
  <data name="EmployeeScale_Enterprise" xml:space="preserve">
    <value>企业级 (1000人以上)</value>
  </data>
  <data name="EnableCompanyInfo" xml:space="preserve">
    <value>启用企业信息</value>
  </data>
  <data name="AddHistoryEvent" xml:space="preserve">
    <value>添加历史事件</value>
  </data>
  <data name="NoCompanyHistoryRecords" xml:space="preserve">
    <value>暂无企业历史记录</value>
  </data>
  <data name="ExecutiveOrganizationDeveloping" xml:space="preserve">
    <value>高管信息和组织架构管理功能正在开发中...</value>
  </data>
  <data name="ContactInfoDeveloping" xml:space="preserve">
    <value>联系信息管理功能正在开发中...</value>
  </data>
  <data name="CSRActivitiesDeveloping" xml:space="preserve">
    <value>CSR活动管理功能正在开发中...</value>
  </data>
  <data name="InvestorRelationsDeveloping" xml:space="preserve">
    <value>投资者关系管理功能正在开发中...</value>
  </data>
  <data name="CompanyHistoryEditDeveloping" xml:space="preserve">
    <value>企业历史编辑功能正在开发中</value>
  </data>
  <data name="EditHistoryDeveloping" xml:space="preserve">
    <value>编辑历史功能正在开发中</value>
  </data>
  <data name="DeleteFunctionDeveloping" xml:space="preserve">
    <value>删除功能正在开发中</value>
  </data>
  <data name="ConfirmDeleteHistoryRecord" xml:space="preserve">
    <value>确定要删除这条企业历史记录吗？</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>是否启用</value>
  </data>
  <data name="SelectEventType" xml:space="preserve">
    <value>请选择事件类型</value>
  </data>
  <data name="Establishment" xml:space="preserve">
    <value>成立</value>
  </data>
  <data name="Expansion" xml:space="preserve">
    <value>扩张</value>
  </data>
  <data name="ProductLaunch" xml:space="preserve">
    <value>产品发布</value>
  </data>
  <data name="Acquisition" xml:space="preserve">
    <value>收购</value>
  </data>
  <data name="Partnership" xml:space="preserve">
    <value>合作</value>
  </data>
  <data name="Award" xml:space="preserve">
    <value>获奖</value>
  </data>
  <data name="Milestone" xml:space="preserve">
    <value>里程碑</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>其他</value>
  </data>
  <data name="EditHistoryEvent" xml:space="preserve">
    <value>编辑历史事件</value>
  </data>
  <data name="EventTitleZhRequired" xml:space="preserve">
    <value>中文事件标题为必填项</value>
  </data>
  <data name="LoadTabError" xml:space="preserve">
    <value>加载标签页内容失败</value>
  </data>
  <data name="LoadHistoryError" xml:space="preserve">
    <value>加载历史记录时发生错误</value>
  </data>
  <data name="HistoryTabNotLoaded" xml:space="preserve">
    <value>请先加载企业历史标签页</value>
  </data>
  <data name="LoadingHistoryData" xml:space="preserve">
    <value>正在加载历史记录数据...</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>重试</value>
  </data>
  <data name="ExecutiveInformation" xml:space="preserve">
    <value>高管信息</value>
  </data>
  <data name="AddExecutive" xml:space="preserve">
    <value>添加高管</value>
  </data>
  <data name="President" xml:space="preserve">
    <value>社长</value>
  </data>
  <data name="Executive" xml:space="preserve">
    <value>高管</value>
  </data>
  <data name="NoExecutiveRecords" xml:space="preserve">
    <value>暂无高管记录</value>
  </data>
  <data name="OrganizationStructure" xml:space="preserve">
    <value>组织架构</value>
  </data>
  <data name="AddDepartment" xml:space="preserve">
    <value>添加部门</value>
  </data>
  <data name="RootDepartment" xml:space="preserve">
    <value>根部门</value>
  </data>
  <data name="NoOrganizationRecords" xml:space="preserve">
    <value>暂无组织架构记录</value>
  </data>
  <data name="BasicContactInfo" xml:space="preserve">
    <value>基本联系信息</value>
  </data>
  <data name="EditContactInfo" xml:space="preserve">
    <value>编辑联系信息</value>
  </data>
  <data name="NoContactInfo" xml:space="preserve">
    <value>尚未设置联系信息</value>
  </data>
  <data name="CompanyLocations" xml:space="preserve">
    <value>分支机构/据点</value>
  </data>
  <data name="AddLocation" xml:space="preserve">
    <value>添加据点</value>
  </data>
  <data name="LocationCoordinates" xml:space="preserve">
    <value>地理坐标</value>
  </data>
  <data name="Headquarters" xml:space="preserve">
    <value>总部</value>
  </data>
  <data name="PrimaryLocation" xml:space="preserve">
    <value>主要据点</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>分公司</value>
  </data>
  <data name="Factory" xml:space="preserve">
    <value>工厂</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>办公室</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>仓库</value>
  </data>
  <data name="ResearchCenter" xml:space="preserve">
    <value>研发中心</value>
  </data>
  <data name="NoLocationRecords" xml:space="preserve">
    <value>暂无据点记录</value>
  </data>
  <data name="Laboratory" xml:space="preserve">
    <value>研究所</value>
  </data>
  <data name="ViewOnMap" xml:space="preserve">
    <value>在地图上查看</value>
  </data>
  <data name="AddCSRActivity" xml:space="preserve">
    <value>添加CSR活动</value>
  </data>
  <data name="Environment" xml:space="preserve">
    <value>环境保护</value>
  </data>
  <data name="SocialContribution" xml:space="preserve">
    <value>社会贡献</value>
  </data>
  <data name="Governance" xml:space="preserve">
    <value>治理</value>
  </data>
  <data name="CommunitySupport" xml:space="preserve">
    <value>社区支持</value>
  </data>
  <data name="EmployeeWelfare" xml:space="preserve">
    <value>员工福利</value>
  </data>
  <data name="DisasterRelief" xml:space="preserve">
    <value>灾害救助</value>
  </data>
  <data name="Education" xml:space="preserve">
    <value>教育</value>
  </data>
  <data name="Healthcare" xml:space="preserve">
    <value>医疗</value>
  </data>
  <data name="Ongoing" xml:space="preserve">
    <value>进行中</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="NoCSRActivityRecords" xml:space="preserve">
    <value>暂无CSR活动记录</value>
  </data>
  <data name="TotalActivities" xml:space="preserve">
    <value>活动总数</value>
  </data>
  <data name="OngoingActivities" xml:space="preserve">
    <value>进行中活动</value>
  </data>
  <data name="ActivitiesWithReports" xml:space="preserve">
    <value>有报告的活动</value>
  </data>
  <data name="FinancialReports" xml:space="preserve">
    <value>财务报告</value>
  </data>
  <data name="ManageFinancialReportsDescription" xml:space="preserve">
    <value>管理企业财务报告、年报、季报等文件</value>
  </data>
  <data name="AddFinancialReport" xml:space="preserve">
    <value>添加财务报告</value>
  </data>
  <data name="AnnualReport" xml:space="preserve">
    <value>年度报告</value>
  </data>
  <data name="QuarterlyReport" xml:space="preserve">
    <value>季度报告</value>
  </data>
  <data name="EarningsRelease" xml:space="preserve">
    <value>业绩发布</value>
  </data>
  <data name="Period" xml:space="preserve">
    <value>期间</value>
  </data>
  <data name="NoFinancialReportRecords" xml:space="preserve">
    <value>暂无财务报告记录</value>
  </data>
  <data name="ShareholderMeetings" xml:space="preserve">
    <value>股东大会</value>
  </data>
  <data name="ManageShareholderMeetingsDescription" xml:space="preserve">
    <value>管理股东大会、会议文档和相关信息</value>
  </data>
  <data name="AddShareholderMeeting" xml:space="preserve">
    <value>添加股东大会</value>
  </data>
  <data name="Scheduled" xml:space="preserve">
    <value>已安排</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>进行中</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>已完成</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>已取消</value>
  </data>
  <data name="Documents" xml:space="preserve">
    <value>份文档</value>
  </data>
  <data name="NoDocuments" xml:space="preserve">
    <value>无文档</value>
  </data>
  <data name="NoShareholderMeetingRecords" xml:space="preserve">
    <value>暂无股东大会记录</value>
  </data>
  <data name="TotalFinancialReports" xml:space="preserve">
    <value>报告总数</value>
  </data>
  <data name="PublishedReports" xml:space="preserve">
    <value>已发布报告</value>
  </data>
  <data name="TotalMeetings" xml:space="preserve">
    <value>会议总数</value>
  </data>
  <data name="UpcomingMeetings" xml:space="preserve">
    <value>即将举行</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>记录未找到</value>
  </data>
  <data name="LoadRecordError" xml:space="preserve">
    <value>加载记录时发生错误</value>
  </data>
  <data name="SaveExecutiveSuccess" xml:space="preserve">
    <value>高管信息保存成功！</value>
  </data>
  <data name="SaveExecutiveError" xml:space="preserve">
    <value>保存高管信息时发生错误</value>
  </data>
  <data name="DeleteExecutiveSuccess" xml:space="preserve">
    <value>高管信息删除成功！</value>
  </data>
  <data name="DeleteExecutiveError" xml:space="preserve">
    <value>删除高管信息时发生错误</value>
  </data>
  <data name="SaveOrganizationSuccess" xml:space="preserve">
    <value>组织架构保存成功！</value>
  </data>
  <data name="SaveOrganizationError" xml:space="preserve">
    <value>保存组织架构时发生错误</value>
  </data>
  <data name="DeleteOrganizationSuccess" xml:space="preserve">
    <value>组织架构删除成功！</value>
  </data>
  <data name="DeleteOrganizationError" xml:space="preserve">
    <value>删除组织架构时发生错误</value>
  </data>
  <data name="ConfirmDeleteExecutive" xml:space="preserve">
    <value>确定要删除这位高管吗？</value>
  </data>
  <data name="ConfirmDeleteOrganization" xml:space="preserve">
    <value>确定要删除这个部门吗？</value>
  </data>
  <data name="SelectPhoto" xml:space="preserve">
    <value>选择照片</value>
  </data>
  <data name="ExecutiveNameRequired" xml:space="preserve">
    <value>高管姓名不能为空</value>
  </data>
  <data name="DepartmentNameRequired" xml:space="preserve">
    <value>部门名称不能为空</value>
  </data>
  <data name="BusinessInfoTitle" xml:space="preserve">
    <value>业务信息</value>
  </data>
  <data name="BusinessDivisions" xml:space="preserve">
    <value>业务部门</value>
  </data>
  <data name="ProductServices" xml:space="preserve">
    <value>产品服务</value>
  </data>
  <data name="DivisionName" xml:space="preserve">
    <value>部门名称</value>
  </data>
  <data name="DivisionDescription" xml:space="preserve">
    <value>部门描述</value>
  </data>
  <data name="DivisionServices" xml:space="preserve">
    <value>服务内容</value>
  </data>
  <data name="DivisionImage" xml:space="preserve">
    <value>部门主图</value>
  </data>
  <data name="DivisionIcon" xml:space="preserve">
    <value>部门图标</value>
  </data>
  <data name="AddBusinessDivision" xml:space="preserve">
    <value>新增业务部门</value>
  </data>
  <data name="EditBusinessDivision" xml:space="preserve">
    <value>编辑业务部门</value>
  </data>
  <data name="DeleteBusinessDivision" xml:space="preserve">
    <value>删除业务部门</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>产品名称</value>
  </data>
  <data name="ProductDescription" xml:space="preserve">
    <value>产品描述</value>
  </data>
  <data name="ProductFeatures" xml:space="preserve">
    <value>产品特性</value>
  </data>
  <data name="ProductSpecifications" xml:space="preserve">
    <value>技术规格</value>
  </data>
  <data name="BusinessDivision" xml:space="preserve">
    <value>所属部门</value>
  </data>
  <data name="ProductCategory" xml:space="preserve">
    <value>产品分类</value>
  </data>
  <data name="ProductImages" xml:space="preserve">
    <value>产品图片</value>
  </data>
  <data name="ProductDocuments" xml:space="preserve">
    <value>产品文档</value>
  </data>
  <data name="ProductPrice" xml:space="preserve">
    <value>产品价格</value>
  </data>
  <data name="AddProductService" xml:space="preserve">
    <value>新增产品服务</value>
  </data>
  <data name="EditProductService" xml:space="preserve">
    <value>编辑产品服务</value>
  </data>
  <data name="DeleteProductService" xml:space="preserve">
    <value>删除产品服务</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>产品</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>服务</value>
  </data>
  <data name="Solution" xml:space="preserve">
    <value>解决方案</value>
  </data>
  <data name="Technology" xml:space="preserve">
    <value>技术</value>
  </data>
  <data name="DocumentName" xml:space="preserve">
    <value>文档名称</value>
  </data>
  <data name="FileSize" xml:space="preserve">
    <value>文件大小</value>
  </data>
  <data name="UploadDate" xml:space="preserve">
    <value>上传日期</value>
  </data>
  <data name="AddDocument" xml:space="preserve">
    <value>添加文档</value>
  </data>
  <data name="RemoveDocument" xml:space="preserve">
    <value>移除文档</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>查看</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>搜索</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>启用</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>禁用</value>
  </data>
  <data name="NoData" xml:space="preserve">
    <value>暂无数据</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>加载中</value>
  </data>
  <data name="SaveSuccess" xml:space="preserve">
    <value>保存成功</value>
  </data>
  <data name="DeleteSuccess" xml:space="preserve">
    <value>删除成功</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>保存失败</value>
  </data>
  <data name="DeleteError" xml:space="preserve">
    <value>删除失败</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>确认删除？</value>
  </data>
  <data name="NewsManagement" xml:space="preserve">
    <value>新闻管理</value>
  </data>
  <data name="NewsManagementDescription" xml:space="preserve">
    <value>管理企业新闻资讯内容，包括发布、编辑、审核等功能</value>
  </data>
  <data name="NewsList" xml:space="preserve">
    <value>新闻列表</value>
  </data>
  <data name="DraftNews" xml:space="preserve">
    <value>草稿新闻</value>
  </data>
  <data name="ScheduledNews" xml:space="preserve">
    <value>定时发布</value>
  </data>
  <data name="ReviewNews" xml:space="preserve">
    <value>待审核</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>统计分析</value>
  </data>
  <data name="CreateNews" xml:space="preserve">
    <value>创建新闻</value>
  </data>
  <data name="EditNews" xml:space="preserve">
    <value>编辑新闻</value>
  </data>
  <data name="DeleteNews" xml:space="preserve">
    <value>删除新闻</value>
  </data>
  <data name="BatchPublish" xml:space="preserve">
    <value>批量发布</value>
  </data>
  <data name="BatchArchive" xml:space="preserve">
    <value>批量归档</value>
  </data>
  <data name="BatchApprove" xml:space="preserve">
    <value>批量审核</value>
  </data>
  <data name="BatchReject" xml:space="preserve">
    <value>批量拒绝</value>
  </data>
  <data name="SearchNews" xml:space="preserve">
    <value>搜索新闻</value>
  </data>
  <data name="SearchDraftNews" xml:space="preserve">
    <value>搜索草稿</value>
  </data>
  <data name="SearchScheduledNews" xml:space="preserve">
    <value>搜索定时发布</value>
  </data>
  <data name="SearchReviewNews" xml:space="preserve">
    <value>搜索待审核</value>
  </data>
  <data name="AllTypes" xml:space="preserve">
    <value>所有类型</value>
  </data>
  <data name="Featured" xml:space="preserve">
    <value>推荐</value>
  </data>
  <data name="PendingReview" xml:space="preserve">
    <value>待审核</value>
  </data>
  <data name="TotalPublished" xml:space="preserve">
    <value>已发布总数</value>
  </data>
  <data name="TodayPublished" xml:space="preserve">
    <value>今日发布</value>
  </data>
  <data name="FeaturedNews" xml:space="preserve">
    <value>推荐新闻</value>
  </data>
  <data name="NewsTypeDistribution" xml:space="preserve">
    <value>新闻类型分布</value>
  </data>
  <data name="PublishTrend" xml:space="preserve">
    <value>发布趋势</value>
  </data>
  <data name="TopViewedNews" xml:space="preserve">
    <value>热门新闻</value>
  </data>
  <data name="ExportStatistics" xml:space="preserve">
    <value>导出统计</value>
  </data>
  <data name="RefreshStatistics" xml:space="preserve">
    <value>刷新统计</value>
  </data>
  <data name="NoDraftNews" xml:space="preserve">
    <value>暂无草稿新闻</value>
  </data>
  <data name="NoDraftNewsDescription" xml:space="preserve">
    <value>您还没有创建任何草稿新闻</value>
  </data>
  <data name="NoScheduledNews" xml:space="preserve">
    <value>暂无定时发布新闻</value>
  </data>
  <data name="NoScheduledNewsDescription" xml:space="preserve">
    <value>您还没有设置任何定时发布的新闻</value>
  </data>
  <data name="NoReviewNews" xml:space="preserve">
    <value>暂无待审核新闻</value>
  </data>
  <data name="NoReviewNewsDescription" xml:space="preserve">
    <value>当前没有需要审核的新闻</value>
  </data>
  <data name="CreateFirstNews" xml:space="preserve">
    <value>创建第一篇新闻</value>
  </data>
  <data name="UntitledNews" xml:space="preserve">
    <value>未命名新闻</value>
  </data>
  <data name="NoSummary" xml:space="preserve">
    <value>暂无摘要</value>
  </data>
  <data name="ChartComingSoon" xml:space="preserve">
    <value>图表功能即将推出</value>
  </data>
  <data name="NoDataAvailable" xml:space="preserve">
    <value>暂无数据</value>
  </data>
  <data name="ProcessScheduledPublishing" xml:space="preserve">
    <value>处理定时发布</value>
  </data>
  <data name="ConfirmDeleteNews" xml:space="preserve">
    <value>确定要删除这条新闻吗？</value>
  </data>
  <data name="ConfirmBatchPublish" xml:space="preserve">
    <value>确定要批量发布选中的新闻吗？</value>
  </data>
  <data name="ConfirmBatchArchive" xml:space="preserve">
    <value>确定要批量归档选中的新闻吗？</value>
  </data>
  <data name="ConfirmApproveNews" xml:space="preserve">
    <value>确定要审核通过这条新闻吗？</value>
  </data>
  <data name="ConfirmRejectNews" xml:space="preserve">
    <value>确定要拒绝这条新闻吗？</value>
  </data>
  <data name="SelectNewsFirst" xml:space="preserve">
    <value>请先选择新闻</value>
  </data>
  <data name="CreateNewsSuccess" xml:space="preserve">
    <value>新闻创建成功</value>
  </data>
  <data name="CreateNewsError" xml:space="preserve">
    <value>新闻创建失败</value>
  </data>
  <data name="UpdateNewsSuccess" xml:space="preserve">
    <value>新闻更新成功</value>
  </data>
  <data name="UpdateNewsError" xml:space="preserve">
    <value>新闻更新失败</value>
  </data>
  <data name="DeleteNewsSuccess" xml:space="preserve">
    <value>新闻删除成功</value>
  </data>
  <data name="DeleteNewsError" xml:space="preserve">
    <value>新闻删除失败</value>
  </data>
  <data name="SetFeaturedSuccess" xml:space="preserve">
    <value>设为推荐成功</value>
  </data>
  <data name="UnsetFeaturedSuccess" xml:space="preserve">
    <value>取消推荐成功</value>
  </data>
  <data name="ToggleFeaturedError" xml:space="preserve">
    <value>推荐状态更新失败</value>
  </data>
  <data name="BatchOperationSuccess" xml:space="preserve">
    <value>批量操作成功</value>
  </data>
  <data name="BatchOperationError" xml:space="preserve">
    <value>批量操作失败</value>
  </data>
  <data name="BatchPublishSuccess" xml:space="preserve">
    <value>批量发布成功</value>
  </data>
  <data name="BatchUnpublishSuccess" xml:space="preserve">
    <value>批量取消发布成功</value>
  </data>
  <data name="BatchArchiveSuccess" xml:space="preserve">
    <value>批量归档成功</value>
  </data>
  <data name="BatchDeleteSuccess" xml:space="preserve">
    <value>批量删除成功</value>
  </data>
  <data name="InvalidBatchOperation" xml:space="preserve">
    <value>无效的批量操作</value>
  </data>
  <data name="SchedulePublishSuccess" xml:space="preserve">
    <value>定时发布设置成功</value>
  </data>
  <data name="SchedulePublishError" xml:space="preserve">
    <value>定时发布设置失败</value>
  </data>
  <data name="ReviewActionSuccess" xml:space="preserve">
    <value>审核操作成功</value>
  </data>
  <data name="ReviewActionError" xml:space="preserve">
    <value>审核操作失败</value>
  </data>
  <data name="LoadFormError" xml:space="preserve">
    <value>表单加载失败</value>
  </data>
  <data name="ExportFeatureComingSoon" xml:space="preserve">
    <value>导出功能即将推出</value>
  </data>
  <data name="NewsNotFound" xml:space="preserve">
    <value>新闻不存在</value>
  </data>
  <data name="NewsType_CompanyNews" xml:space="preserve">
    <value>企业新闻</value>
  </data>
  <data name="NewsType_PressRelease" xml:space="preserve">
    <value>新闻发布</value>
  </data>
  <data name="NewsType_ProductUpdate" xml:space="preserve">
    <value>产品更新</value>
  </data>
  <data name="NewsType_Event" xml:space="preserve">
    <value>活动</value>
  </data>
  <data name="NewsType_MediaCoverage" xml:space="preserve">
    <value>媒体报道</value>
  </data>
  <data name="NewsType_Announcement" xml:space="preserve">
    <value>公告</value>
  </data>
  <data name="BusinessInfoDescription" xml:space="preserve">
    <value>管理企业的业务部门和产品服务信息</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>图片</value>
  </data>
  <data name="NoBusinessDivisionsYet" xml:space="preserve">
    <value>还没有添加任何业务部门</value>
  </data>
  <data name="SelectBusinessDivision" xml:space="preserve">
    <value>选择业务部门</value>
  </data>
  <data name="Currency_JPY" xml:space="preserve">
    <value>JPY (日元)</value>
  </data>
  <data name="Currency_USD" xml:space="preserve">
    <value>USD (美元)</value>
  </data>
  <data name="Currency_EUR" xml:space="preserve">
    <value>EUR (欧元)</value>
  </data>
  <data name="Currency_CNY" xml:space="preserve">
    <value>CNY (人民币)</value>
  </data>
  <data name="FeatureInProgress" xml:space="preserve">
    <value>功能开发中</value>
  </data>
  <data name="ImageAndDocManagementComingSoon" xml:space="preserve">
    <value>图片上传和文档管理功能将在后续版本中实现。</value>
  </data>
  <data name="AllCategories" xml:space="preserve">
    <value>所有分类</value>
  </data>
  <data name="AllDivisions" xml:space="preserve">
    <value>所有部门</value>
  </data>
  <data name="SearchProductsPlaceholder" xml:space="preserve">
    <value>搜索产品...</value>
  </data>
  <data name="ProductImage" xml:space="preserve">
    <value>产品图片</value>
  </data>
  <data name="NoProductServicesYet" xml:space="preserve">
    <value>还没有添加任何产品服务</value>
  </data>
  <data name="NewsThumbnail" xml:space="preserve">
    <value>新闻缩略图</value>
  </data>
  <data name="AdminNewsStatisticsTitle" xml:space="preserve">
    <value>新闻统计</value>
  </data>
  <data name="AdminNewsStatisticsDescription" xml:space="preserve">
    <value>查看新闻发布统计和分析数据</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>返回列表</value>
  </data>
  <data name="Drafts" xml:space="preserve">
    <value>草稿</value>
  </data>
  <data name="ReviewStatistics" xml:space="preserve">
    <value>审核统计</value>
  </data>
  <data name="MonthlyPublishTrend" xml:space="preserve">
    <value>月度发布趋势</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>标题</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>类型</value>
  </data>
  <data name="Views" xml:space="preserve">
    <value>浏览量</value>
  </data>
  <data name="Untitled" xml:space="preserve">
    <value>无标题</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>待审核</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>已通过</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>已拒绝</value>
  </data>
  <data name="PublishedCount" xml:space="preserve">
    <value>发布数量</value>
  </data>
  <data name="NewsType" xml:space="preserve">
    <value>新闻类型</value>
  </data>
  <data name="NewsType_IndustryNews" xml:space="preserve">
    <value>行业新闻</value>
  </data>
  <data name="NewsType_ProductNews" xml:space="preserve">
    <value>产品新闻</value>
  </data>
  <data name="NewsType_EventNews" xml:space="preserve">
    <value>活动新闻</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>优先级</value>
  </data>
  <data name="Priority_Low" xml:space="preserve">
    <value>低</value>
  </data>
  <data name="Priority_Normal" xml:space="preserve">
    <value>普通</value>
  </data>
  <data name="Priority_High" xml:space="preserve">
    <value>高</value>
  </data>
  <data name="Priority_Urgent" xml:space="preserve">
    <value>紧急</value>
  </data>
  <data name="PublishImmediately" xml:space="preserve">
    <value>立即发布</value>
  </data>
  <data name="AllowComments" xml:space="preserve">
    <value>允许评论</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>摘要</value>
  </data>
  <data name="Content" xml:space="preserve">
    <value>内容</value>
  </data>
  <data name="SeoKeywords" xml:space="preserve">
    <value>SEO关键词</value>
  </data>
  <data name="GlobalSeoKeywords" xml:space="preserve">
    <value>全局SEO关键词</value>
  </data>
  <data name="Tags" xml:space="preserve">
    <value>标签</value>
  </data>
  <data name="EnterTagsPlaceholder" xml:space="preserve">
    <value>输入标签，用逗号分隔</value>
  </data>
  <data name="MessageManagement" xml:space="preserve">
    <value>留言管理</value>
  </data>
  <data name="MessageManagementDescription" xml:space="preserve">
    <value>查看和处理客户留言信息</value>
  </data>
  <data name="MessageDetails" xml:space="preserve">
    <value>留言详情</value>
  </data>
  <data name="BackToMessageList" xml:space="preserve">
    <value>返回留言列表</value>
  </data>
  <data name="MessageNotFound" xml:space="preserve">
    <value>留言未找到</value>
  </data>
  <data name="LoadDataError" xml:space="preserve">
    <value>加载数据失败</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>总计</value>
  </data>
  <data name="NewMessages" xml:space="preserve">
    <value>新留言</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>今日</value>
  </data>
  <data name="StatusFilter" xml:space="preserve">
    <value>状态筛选</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>搜索姓名、邮箱、内容...</value>
  </data>
  <data name="NoTitle" xml:space="preserve">
    <value>无标题</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>新</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>重要</value>
  </data>
  <data name="CreatedAt" xml:space="preserve">
    <value>创建时间</value>
  </data>
  <data name="UpdatedAt" xml:space="preserve">
    <value>更新时间</value>
  </data>
  <data name="ProcessedAt" xml:space="preserve">
    <value>处理时间</value>
  </data>
  <data name="DealResult" xml:space="preserve">
    <value>处理结果</value>
  </data>
  <data name="NoMessages" xml:space="preserve">
    <value>暂无留言数据</value>
  </data>
  <data name="NewMessage" xml:space="preserve">
    <value>新留言</value>
  </data>
  <data name="WaitingForResponse" xml:space="preserve">
    <value>等待回复</value>
  </data>
  <data name="Resolved" xml:space="preserve">
    <value>已解决</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>已关闭</value>
  </data>
  <data name="Spam" xml:space="preserve">
    <value>垃圾邮件</value>
  </data>
  <data name="MessageInfo" xml:space="preserve">
    <value>留言信息</value>
  </data>
  <data name="SourcePage" xml:space="preserve">
    <value>来源页面</value>
  </data>
  <data name="MessageContent" xml:space="preserve">
    <value>留言内容</value>
  </data>
  <data name="DealResultPlaceholder" xml:space="preserve">
    <value>输入处理结果...</value>
  </data>
  <data name="SaveDealResult" xml:space="preserve">
    <value>保存处理结果</value>
  </data>
  <data name="MarkAsInProgress" xml:space="preserve">
    <value>标记为处理中</value>
  </data>
  <data name="MarkAsWaitingForResponse" xml:space="preserve">
    <value>等待回复</value>
  </data>
  <data name="MarkAsResolved" xml:space="preserve">
    <value>标记为已解决</value>
  </data>
  <data name="MarkAsClosed" xml:space="preserve">
    <value>关闭</value>
  </data>
  <data name="RemoveImportant" xml:space="preserve">
    <value>取消重要</value>
  </data>
  <data name="MarkAsImportant" xml:space="preserve">
    <value>标记重要</value>
  </data>
  <data name="GeneralInquiry" xml:space="preserve">
    <value>一般咨询</value>
  </data>
  <data name="ProductInquiry" xml:space="preserve">
    <value>产品咨询</value>
  </data>
  <data name="ServiceInquiry" xml:space="preserve">
    <value>服务咨询</value>
  </data>
  <data name="TechnicalSupport" xml:space="preserve">
    <value>技术支持</value>
  </data>
  <data name="BusinessCooperation" xml:space="preserve">
    <value>商务合作</value>
  </data>
  <data name="PartnershipInquiry" xml:space="preserve">
    <value>合作伙伴咨询</value>
  </data>
  <data name="MediaInquiry" xml:space="preserve">
    <value>媒体咨询</value>
  </data>
  <data name="CareerInquiry" xml:space="preserve">
    <value>招聘咨询</value>
  </data>
  <data name="Complaint" xml:space="preserve">
    <value>投诉建议</value>
  </data>
  <data name="StatusUpdateSuccess" xml:space="preserve">
    <value>状态更新成功</value>
  </data>
  <data name="StatusUpdateError" xml:space="preserve">
    <value>状态更新失败</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>更新失败</value>
  </data>
  <data name="DealResultRequired" xml:space="preserve">
    <value>处理结果不能为空</value>
  </data>
  <data name="DealResultSaveSuccess" xml:space="preserve">
    <value>处理结果保存成功</value>
  </data>
  <data name="ImportantToggleSuccess" xml:space="preserve">
    <value>重要标记已更新</value>
  </data>
  <data name="YearSuffix" xml:space="preserve">
    <value>年</value>
  </data>
  <data name="EnterText" xml:space="preserve">
    <value>请输入文本</value>
  </data>
  <data name="FieldRenderError" xml:space="preserve">
    <value>字段渲染失败</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>确认</value>
  </data>
  <data name="FormGroups_Other" xml:space="preserve">
    <value>其他</value>
  </data>
  <data name="FileUpload_ClickToUpload" xml:space="preserve">
    <value>点击上传</value>
  </data>
  <data name="FileUpload_OrDragDrop" xml:space="preserve">
    <value>或拖拽文件到此处</value>
  </data>
  <data name="FileUpload_AllFiles" xml:space="preserve">
    <value>所有文件类型</value>
  </data>
  <data name="FileUpload_MaxSize" xml:space="preserve">
    <value>最大</value>
  </data>
  <data name="FileUpload_Preview" xml:space="preserve">
    <value>预览</value>
  </data>
  <data name="FileUpload_DeleteFile" xml:space="preserve">
    <value>删除文件</value>
  </data>
  <data name="FileUpload_DeleteConfirm" xml:space="preserve">
    <value>确定要删除此文件吗？</value>
  </data>
  <data name="FileUpload_DeleteSuccess" xml:space="preserve">
    <value>文件删除成功</value>
  </data>
  <data name="FileUpload_DeleteError" xml:space="preserve">
    <value>删除失败</value>
  </data>
  <data name="FileUpload_UploadError" xml:space="preserve">
    <value>上传失败</value>
  </data>
  <data name="FileType_Image" xml:space="preserve">
    <value>图片</value>
  </data>
  <data name="FileType_Video" xml:space="preserve">
    <value>视频</value>
  </data>
  <data name="FileType_PDF" xml:space="preserve">
    <value>PDF文档</value>
  </data>
  <data name="FileType_Word" xml:space="preserve">
    <value>Word文档</value>
  </data>
  <data name="NewsTypeRequired" xml:space="preserve">
    <value>新闻类型不能为空</value>
  </data>
  <data name="PublishDateRequired" xml:space="preserve">
    <value>发布日期不能为空</value>
  </data>
  <data name="NewsStatusRequired" xml:space="preserve">
    <value>新闻状态不能为空</value>
  </data>
  <data name="PriorityRangeError" xml:space="preserve">
    <value>优先级必须在0-3之间</value>
  </data>
  <data name="TitleRequired" xml:space="preserve">
    <value>标题不能为空</value>
  </data>
  <data name="AllStatuses" xml:space="preserve">
    <value>所有状态</value>
  </data>
  <data name="ExternalUrl" xml:space="preserve">
    <value>外部链接</value>
  </data>
  <data name="LoadingData" xml:space="preserve">
    <value>正在加载数据...</value>
  </data>
  <data name="NewsSource" xml:space="preserve">
    <value>新闻来源</value>
  </data>
  <data name="NewsSource_External" xml:space="preserve">
    <value>外部来源</value>
  </data>
  <data name="NewsSource_Internal" xml:space="preserve">
    <value>内部来源</value>
  </data>
  <data name="NewsSource_MediaReport" xml:space="preserve">
    <value>媒体报道</value>
  </data>
  <data name="NewsSource_Partner" xml:space="preserve">
    <value>合作伙伴</value>
  </data>
  <data name="NewsSource_PressRelease" xml:space="preserve">
    <value>新闻稿</value>
  </data>
  <data name="NewsStatus" xml:space="preserve">
    <value>新闻状态</value>
  </data>
  <data name="NewsStatus_Draft" xml:space="preserve">
    <value>草稿</value>
  </data>
  <data name="NewsStatus_Published" xml:space="preserve">
    <value>已发布</value>
  </data>
  <data name="NewsStatus_Review" xml:space="preserve">
    <value>审核中</value>
  </data>
  <data name="SearchNewsPlaceholder" xml:space="preserve">
    <value>请输入新闻标题...</value>
  </data>
  <data name="SearchTitle" xml:space="preserve">
    <value>搜索标题</value>
  </data>
  <data name="Searching" xml:space="preserve">
    <value>搜索中...</value>
  </data>
  <data name="ThumbnailUrl" xml:space="preserve">
    <value>缩略图</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>更新失败</value>
  </data>
  <data name="UpdateSuccess" xml:space="preserve">
    <value>更新成功</value>
  </data>
  <data name="ActiveJobs" xml:space="preserve">
    <value>活跃职位</value>
  </data>
  <data name="AddNewJob" xml:space="preserve">
    <value>添加新职位</value>
  </data>
  <data name="AllInterviewTypes" xml:space="preserve">
    <value>所有访谈类型</value>
  </data>
  <data name="AllJobTypes" xml:space="preserve">
    <value>所有职位类型</value>
  </data>
  <data name="AllLooksGood" xml:space="preserve">
    <value>一切正常</value>
  </data>
  <data name="ApplicationDeadline" xml:space="preserve">
    <value>申请截止日期</value>
  </data>
  <data name="AverageTranslationRate" xml:space="preserve">
    <value>平均翻译率</value>
  </data>
  <data name="BatchActivate" xml:space="preserve">
    <value>批量激活</value>
  </data>
  <data name="BatchDeactivate" xml:space="preserve">
    <value>批量停用</value>
  </data>
  <data name="BatchUnpublish" xml:space="preserve">
    <value>批量取消发布</value>
  </data>
  <data name="Benefits" xml:space="preserve">
    <value>福利待遇</value>
  </data>
  <data name="CompanyImpression" xml:space="preserve">
    <value>公司印象</value>
  </data>
  <data name="CompletedFields" xml:space="preserve">
    <value>已完成字段</value>
  </data>
  <data name="ContentQualityAnalysis" xml:space="preserve">
    <value>内容质量分析</value>
  </data>
  <data name="ContentRichness" xml:space="preserve">
    <value>内容丰富度</value>
  </data>
  <data name="Coverage" xml:space="preserve">
    <value>覆盖率</value>
  </data>
  <data name="CreateEmployeeInterview" xml:space="preserve">
    <value>创建员工访谈</value>
  </data>
  <data name="CreateFirstInterview" xml:space="preserve">
    <value>创建第一个访谈</value>
  </data>
  <data name="CreateJobPosition" xml:space="preserve">
    <value>创建职位</value>
  </data>
  <data name="EditJobPosition" xml:space="preserve">
    <value>编辑职位</value>
  </data>  
  <data name="Deadline" xml:space="preserve">
    <value>截止日期</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>部门</value>
  </data>
  <data name="DraftInterviews" xml:space="preserve">
    <value>草稿访谈</value>
  </data>
  <data name="DraftJobs" xml:space="preserve">
    <value>草稿职位</value>
  </data>
  <data name="EmployeeInterviews" xml:space="preserve">
    <value>员工访谈</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>员工姓名</value>
  </data>
  <data name="EmploymentType" xml:space="preserve">
    <value>雇佣类型</value>
  </data>
  <data name="ExperienceLevel" xml:space="preserve">
    <value>经验要求</value>
  </data>
  <data name="Expired" xml:space="preserve">
    <value>已过期</value>
  </data>
  <data name="ExpiredJobs" xml:space="preserve">
    <value>过期职位</value>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>导出数据</value>
  </data>
  <data name="FeaturedInterviews" xml:space="preserve">
    <value>推荐访谈</value>
  </data>
  <data name="FeaturedJobs" xml:space="preserve">
    <value>推荐职位</value>
  </data>
  <data name="InterviewContent" xml:space="preserve">
    <value>访谈内容</value>
  </data>
  <data name="InterviewDate" xml:space="preserve">
    <value>访谈日期</value>
  </data>
  <data name="InterviewType" xml:space="preserve">
    <value>访谈类型</value>
  </data>
  <data name="InterviewTypeDistribution" xml:space="preserve">
    <value>访谈类型分布</value>
  </data>
  <data name="InterviewType_Management" xml:space="preserve">
    <value>管理层</value>
  </data>
  <data name="InterviewType_NewEmployee" xml:space="preserve">
    <value>新员工</value>
  </data>
  <data name="InterviewType_Other" xml:space="preserve">
    <value>其他</value>
  </data>
  <data name="InterviewType_Sales" xml:space="preserve">
    <value>销售</value>
  </data>
  <data name="InterviewType_Technical" xml:space="preserve">
    <value>技术</value>
  </data>
  <data name="InterviewType_Veteran" xml:space="preserve">
    <value>资深员工</value>
  </data>
  <data name="IsFeatured" xml:space="preserve">
    <value>是否推荐</value>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>职位描述</value>
  </data>
  <data name="JobPositions" xml:space="preserve">
    <value>职位管理</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>职位名称</value>
  </data>
  <data name="JobType" xml:space="preserve">
    <value>职位类型</value>
  </data>
  <data name="JobTypeDistribution" xml:space="preserve">
    <value>职位类型分布</value>
  </data>
  <data name="ManageInterviews" xml:space="preserve">
    <value>管理访谈</value>
  </data>
  <data name="MultilingualCompleteness" xml:space="preserve">
    <value>多语言完整性</value>
  </data>
  <data name="NoInterviewDataAvailable" xml:space="preserve">
    <value>无访谈数据</value>
  </data>
  <data name="NoInterviewsFound" xml:space="preserve">
    <value>未找到访谈</value>
  </data>
  <data name="NoJobDataAvailable" xml:space="preserve">
    <value>无职位数据</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>排序</value>
  </data>
  <data name="OverviewStatistics" xml:space="preserve">
    <value>概览统计</value>
  </data>
  <data name="PostDate" xml:space="preserve">
    <value>发布日期</value>
  </data>
  <data name="ProbationPeriod" xml:space="preserve">
    <value>试用期</value>
  </data>
  <data name="ProbationPeriodPlaceholder" xml:space="preserve">
    <value>例如：3个月</value>
  </data>
  <data name="PublishedInterviews" xml:space="preserve">
    <value>已发布访谈</value>
  </data>
  <data name="RecruitmentContentOptimal" xml:space="preserve">
    <value>招聘内容已优化</value>
  </data>
  <data name="RefreshData" xml:space="preserve">
    <value>刷新数据</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>任职要求</value>
  </data>
  <data name="SalaryMax" xml:space="preserve">
    <value>最高薪资</value>
  </data>
  <data name="SalaryMin" xml:space="preserve">
    <value>最低薪资</value>
  </data>
  <data name="SearchInterviews" xml:space="preserve">
    <value>搜索访谈</value>
  </data>
  <data name="SearchJobs" xml:space="preserve">
    <value>搜索职位</value>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>排序号</value>
  </data>
  <data name="Status_Archived" xml:space="preserve">
    <value>已归档</value>
  </data>
  <data name="Status_Draft" xml:space="preserve">
    <value>草稿</value>
  </data>
  <data name="Status_Published" xml:space="preserve">
    <value>已发布</value>
  </data>
  <data name="SuggestionsAndReminders" xml:space="preserve">
    <value>建议和提醒</value>
  </data>
  <data name="TagsPlaceholder" xml:space="preserve">
    <value>用逗号分隔多个标签</value>
  </data>
  <data name="TotalActiveJobs" xml:space="preserve">
    <value>活跃职位总数</value>
  </data>
  <data name="UpdateFrequency" xml:space="preserve">
    <value>更新频率</value>
  </data>
  <data name="UpdatesPerWeek" xml:space="preserve">
    <value>每周更新次数</value>
  </data>
  <data name="WorkDescription" xml:space="preserve">
    <value>工作描述</value>
  </data>
  <data name="WorkLocation" xml:space="preserve">
    <value>工作地点</value>
  </data>
  <data name="WorkingHours" xml:space="preserve">
    <value>工作时间</value>
  </data>
  <data name="WorkingHoursPlaceholder" xml:space="preserve">
    <value>例如：9:00-18:00</value>
  </data>
  <data name="Years" xml:space="preserve">
    <value>年</value>
  </data>
  <data name="YearsOfService" xml:space="preserve">
    <value>工作年限</value>
  </data>
  <data name="RecruitmentManagement" xml:space="preserve">
    <value>招聘管理</value>
  </data>
  <data name="JobType_NewGraduate" xml:space="preserve">
    <value>校园招聘</value>
  </data>
  <data name="JobType_MidCareer" xml:space="preserve">
    <value>社会招聘</value>
  </data>
  <data name="JobType_Internal" xml:space="preserve">
    <value>内部招聘</value>
  </data>
  <data name="EmploymentType_FullTime" xml:space="preserve">
    <value>全职</value>
  </data>
  <data name="EmploymentType_PartTime" xml:space="preserve">
    <value>兼职</value>
  </data>
  <data name="EmploymentType_Contract" xml:space="preserve">
    <value>合同工</value>
  </data>
  <data name="EmploymentType_Temporary" xml:space="preserve">
    <value>临时工</value>
  </data>
  <data name="EmploymentType_Internship" xml:space="preserve">
    <value>实习生</value>
  </data>
  <data name="ExperienceLevel_Entry" xml:space="preserve">
    <value>入门级</value>
  </data>
  <data name="ExperienceLevel_Junior" xml:space="preserve">
    <value>初级</value>
  </data>
  <data name="ExperienceLevel_Mid" xml:space="preserve">
    <value>中级</value>
  </data>
  <data name="ExperienceLevel_Senior" xml:space="preserve">
    <value>高级</value>
  </data>
  <data name="ExperienceLevel_Executive" xml:space="preserve">
    <value>管理层</value>
  </data>
  <!-- Missing Message Management Keys -->
  <data name="WaitingForCustomer" xml:space="preserve">
    <value>等待客户反馈</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>公司</value>
  </data>
  <data name="DealResultSaved" xml:space="preserve">
    <value>处理结果已保存</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>姓名</value>
  </data>
  <data name="DataManagement" xml:space="preserve">
    <value>数据管理</value>
  </data>
  <data name="ComponentDataManagement" xml:space="preserve">
    <value>组件数据管理</value>
  </data>
  <data name="LoadingComponentData" xml:space="preserve">
    <value>正在加载组件数据...</value>
  </data>
  <data name="LoadComponentDataError" xml:space="preserve">
    <value>加载组件数据失败</value>
  </data>
  <data name="NoFormFieldsConfigured" xml:space="preserve">
    <value>未配置表单字段</value>
  </data>
  <data name="ComponentFormFieldsNotConfigured" xml:space="preserve">
    <value>该组件的variants.json文件中未配置formFields</value>
  </data>
  <data name="SavingComponentData" xml:space="preserve">
    <value>正在保存组件数据...</value>
  </data>
  <data name="ComponentDataSaved" xml:space="preserve">
    <value>组件数据保存成功</value>
  </data>
  <data name="SaveComponentDataError" xml:space="preserve">
    <value>保存组件数据失败</value>
  </data>
  <data name="NoFormDataToSave" xml:space="preserve">
    <value>没有可保存的表单数据</value>
  </data>
  <data name="InvalidParameters" xml:space="preserve">
    <value>无效的参数</value>
  </data>
  <data name="SerializeDataError" xml:space="preserve">
    <value>数据序列化失败</value>
  </data>

</root>