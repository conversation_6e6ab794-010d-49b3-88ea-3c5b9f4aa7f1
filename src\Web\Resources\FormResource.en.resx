﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Button related fields -->
  <data name="PrimaryButtonText" xml:space="preserve">
    <value>Primary Button Text</value>
  </data>
  <data name="PrimaryButtonLink" xml:space="preserve">
    <value>Primary Button Link</value>
  </data>
  <data name="SecondaryButtonText" xml:space="preserve">
    <value>Secondary Button Text</value>
  </data>
  <data name="SecondaryButtonLink" xml:space="preserve">
    <value>Secondary Button Link</value>
  </data>

  <!-- Layout settings fields -->
  <data name="TextAlignment" xml:space="preserve">
    <value>Text Alignment</value>
  </data>
  <data name="SectionHeight" xml:space="preserve">
    <value>Section Height</value>
  </data>
  <data name="OverlayOpacity" xml:space="preserve">
    <value>Overlay Opacity</value>
  </data>
  <data name="ShowOverlay" xml:space="preserve">
    <value>Show Overlay</value>
  </data>
  <data name="EnableAnimation" xml:space="preserve">
    <value>Enable Animation</value>
  </data>
  <data name="EnableParallax" xml:space="preserve">
    <value>Enable Parallax</value>
  </data>
  <data name="ShowScrollIndicator" xml:space="preserve">
    <value>Show Scroll Indicator</value>
  </data>

  <!-- Alignment options -->
  <data name="AlignLeft" xml:space="preserve">
    <value>Left Align</value>
  </data>
  <data name="AlignCenter" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="AlignRight" xml:space="preserve">
    <value>Right Align</value>
  </data>

  <data name="SectionSize" xml:space="preserve">
    <value>Section Size</value>
  </data>
  <!-- Height options -->
  <data name="AutoHeight" xml:space="preserve">
    <value>Auto Height</value>
  </data>
  <data name="FullScreenHeight" xml:space="preserve">
    <value>Full Screen Height</value>
  </data>
  <data name="LargeSize" xml:space="preserve">
    <value>Large Size</value>
  </data>
    <data name="SmallSize" xml:space="preserve">
    <value>Small Size</value>
  </data>
  <data name="MediumSize" xml:space="preserve">
    <value>Medium Size</value>
  </data>

  <!-- Help texts -->
  <data name="PrimaryButtonTextHelpText" xml:space="preserve">
    <value>Display text for the primary action button</value>
  </data>
  <data name="PrimaryButtonLinkHelpText" xml:space="preserve">
    <value>URL to navigate when primary button is clicked</value>
  </data>
  <data name="SecondaryButtonTextHelpText" xml:space="preserve">
    <value>Display text for the secondary action button</value>
  </data>
  <data name="SecondaryButtonLinkHelpText" xml:space="preserve">
    <value>URL to navigate when secondary button is clicked</value>
  </data>
  <data name="TextAlignmentHelpText" xml:space="preserve">
    <value>Text content alignment style</value>
  </data>
  <data name="SectionHeightHelpText" xml:space="preserve">
    <value>Height setting for the hero section</value>
  </data>
  <data name="OverlayOpacityHelpText" xml:space="preserve">
    <value>Overlay layer opacity (0-100)</value>
  </data>
  <data name="ShowOverlayHelpText" xml:space="preserve">
    <value>Show semi-transparent overlay on background image/video</value>
  </data>
  <data name="EnableAnimationHelpText" xml:space="preserve">
    <value>Enable text fade-in and button hover animations</value>
  </data>
  <data name="EnableParallaxHelpText" xml:space="preserve">
    <value>Enable parallax scrolling effect</value>
  </data>
  <data name="ShowScrollIndicatorHelpText" xml:space="preserve">
    <value>Show down-arrow scroll indicator at bottom</value>
  </data>
  <data name="BackgroundVideoHelpText" xml:space="preserve">
    <value>Optional background video that will overlay the background image</value>
  </data>

  <!-- Header Component Fields -->
  <data name="FormFields_CompanyName" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="FormFields_CompanyNameHelpText" xml:space="preserve">
    <value>Company name displayed in the header</value>
  </data>
  <data name="FormFields_Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="FormFields_AddressHelpText" xml:space="preserve">
    <value>Company address displayed in the page</value>
  </data>
  <data name="FormFields_Sort" xml:space="preserve">
    <value>Sort Number</value>
  </data>
  <data name="FormFields_SortHelpText" xml:space="preserve">
    <value>Sort number for menu items</value>
  </data>
  <data name="FormFields_ContactInfo" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="FormFields_ContactInfoHelpText" xml:space="preserve">
    <value>Contact information displayed in the header or footer</value>
  </data>
  <data name="FormFields_Phone" xml:space="preserve">
    <value>Phone Number</value>
  </data>
  <data name="FormFields_PhoneHelpText" xml:space="preserve">
    <value>Company contact phone number</value>
  </data>
  <data name="FormFields_Email" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="FormFields_EmailHelpText" xml:space="preserve">
    <value>Company contact email address</value>
  </data>
  <data name="FormFields_MenuItems" xml:space="preserve">
    <value>Navigation Menu Items</value>
  </data>
  <data name="FormFields_MenuItemsHelpText" xml:space="preserve">
    <value>Header navigation menu items</value>
  </data>
  <data name="FormFields_MenuItemText" xml:space="preserve">
    <value>Menu Item Text</value>
  </data>
  <data name="FormFields_MenuItemTextHelpText" xml:space="preserve">
    <value>Text displayed for navigation menu item</value>
  </data>
  <data name="FormFields_MenuItemUrl" xml:space="preserve">
    <value>Menu Item Link</value>
  </data>
  <data name="FormFields_MenuItemUrlHelpText" xml:space="preserve">
    <value>URL address to navigate when menu item is clicked</value>
  </data>
  <data name="FormFields_IsActive" xml:space="preserve">
    <value>Is Active</value>
  </data>
  <data name="FormFields_IsActiveHelpText" xml:space="preserve">
    <value>Whether this menu item is in active state</value>
  </data>
  <data name="FormFields_SubMenuItems" xml:space="preserve">
    <value>Sub Menu Items</value>
  </data>
  <data name="FormFields_SubMenuItemsHelpText" xml:space="preserve">
    <value>Dropdown submenu items for this menu item</value>
  </data>
  <data name="FormFields_SubMenuItemText" xml:space="preserve">
    <value>Sub Menu Item Text</value>
  </data>
  <data name="FormFields_SubMenuItemTextHelpText" xml:space="preserve">
    <value>Text displayed for sub menu item</value>
  </data>
  <data name="FormFields_SubMenuItemUrl" xml:space="preserve">
    <value>Sub Menu Item Link</value>
  </data>
  <data name="FormFields_SubMenuItemUrlHelpText" xml:space="preserve">
    <value>URL address to navigate when sub menu item is clicked</value>
  </data>
  <data name="FormFields_MobileMenuButtonText" xml:space="preserve">
    <value>Mobile Menu Button Text</value>
  </data>
  <data name="FormFields_MobileMenuButtonTextHelpText" xml:space="preserve">
    <value>Text displayed on mobile menu button</value>
  </data>
  <data name="FormFields_ShowCompanyName" xml:space="preserve">
    <value>Show Company Name</value>
  </data>
  <data name="FormFields_ShowCompanyNameHelpText" xml:space="preserve">
    <value>Display company name in header</value>
  </data>
  <data name="FormFields_ShowLanguageSelector" xml:space="preserve">
    <value>Show Language Selector</value>
  </data>
  <data name="FormFields_ShowLanguageSelectorHelpText" xml:space="preserve">
    <value>Display multi-language selector in header</value>
  </data>
  <data name="FormFields_ShowDarkModeToggle" xml:space="preserve">
    <value>Show Dark Mode Toggle</value>
  </data>
  <data name="FormFields_ShowDarkModeToggleHelpText" xml:space="preserve">
    <value>Display dark/light theme toggle in header</value>
  </data>
  <data name="FormFields_ShowSearchBox" xml:space="preserve">
    <value>Show Search Box</value>
  </data>
  <data name="FormFields_ShowSearchBoxHelpText" xml:space="preserve">
    <value>Display search input box in header</value>
  </data>
  <data name="FormFields_BackgroundColor" xml:space="preserve">
    <value>Background Color</value>
  </data>
  <data name="FormFields_BackgroundColorHelpText" xml:space="preserve">
    <value>Background color setting</value>
  </data>
  <data name="FormFields_TextColor" xml:space="preserve">
    <value>Text Color</value>
  </data>
  <data name="FormFields_TextColorHelpText" xml:space="preserve">
    <value>Text color setting</value>
  </data>

  <data name="FormFields_BackgroundColor_White" xml:space="preserve">
    <value>White</value>
  </data>
  <data name="FormFields_BackgroundColor_Muted" xml:space="preserve">
    <value>Muted</value>
  </data>
  <data name="FormFields_ColumnsDesktop" xml:space="preserve">
    <value>Desktop Columns</value>
  </data>
  <data name="FormFields_ColumnsTablet" xml:space="preserve">
    <value>Tablet Columns</value>
  </data>

  <data name="FormFields_MaxItems" xml:space="preserve">
    <value>Max Items</value>
  </data>
  <data name="FormFields_ShowViewAllButton" xml:space="preserve">
    <value>Show "View All" Button</value>
  </data>
  <data name="FormFields_ShowCategories" xml:space="preserve">
    <value>Show Categories</value>
  </data>
  <data name="FormFields_ShowDates" xml:space="preserve">
    <value>Show Dates</value>
  </data>
  <data name="FormFields_ShowExcerpts" xml:space="preserve">
    <value>Show Excerpts</value>
  </data>
  <data name="FormFields_ViewAllButtonText" xml:space="preserve">
    <value>View All Button Text</value>
  </data>
  <data name="FormFields_ViewAllButtonUrl" xml:space="preserve">
    <value>View All Button URL</value>
  </data>

  <data name="FormFields_RecruitmentSection_Categories" xml:space="preserve">
    <value>Recruitment Section Categories</value>
  </data>

  <!-- Footer Component Fields -->
  <data name="FormFields_CompanyDescription" xml:space="preserve">
    <value>Company Slogan</value>
  </data>
  <data name="FormFields_CompanyDescriptionHelpText" xml:space="preserve">
    <value>Company slogan text displayed in footer</value>
  </data>
  <data name="FormFields_Copyright" xml:space="preserve">
    <value>Copyright Information</value>
  </data>
  <data name="FormFields_CopyrightHelpText" xml:space="preserve">
    <value>Copyright notice text displayed in footer</value>
  </data>
  <data name="FormFields_FooterSections" xml:space="preserve">
    <value>Footer Sections</value>
  </data>
  <data name="FormFields_FooterSectionsHelpText" xml:space="preserve">
    <value>Footer column sections</value>
  </data>
  <data name="FormFields_SectionTitle" xml:space="preserve">
    <value>Section Title</value>
  </data>
  <data name="FormFields_SectionTitleHelpText" xml:space="preserve">
    <value>Footer section title text</value>
  </data>
  <data name="FormFields_SectionLinks" xml:space="preserve">
    <value>Section Links</value>
  </data>
  <data name="FormFields_SectionLinksHelpText" xml:space="preserve">
    <value>Link items under this section</value>
  </data>
  <data name="FormFields_LinkText" xml:space="preserve">
    <value>Link Text</value>
  </data>
  <data name="FormFields_LinkTextHelpText" xml:space="preserve">
    <value>Text displayed for the link</value>
  </data>
  <data name="FormFields_LinkUrl" xml:space="preserve">
    <value>Link URL</value>
  </data>
  <data name="FormFields_LinkUrlHelpText" xml:space="preserve">
    <value>URL address to navigate when link is clicked</value>
  </data>
  <data name="FormFields_OpenInNewTab" xml:space="preserve">
    <value>Open In New Tab</value>
  </data>
  <data name="FormFields_OpenInNewTabHelpText" xml:space="preserve">
    <value>Whether to open link in new browser tab</value>
  </data>
  <data name="FormFields_SocialLinks" xml:space="preserve">
    <value>Social Media Links</value>
  </data>
  <data name="FormFields_SocialLinksHelpText" xml:space="preserve">
    <value>Social media platform links displayed in footer</value>
  </data>
  <data name="FormFields_SocialPlatform" xml:space="preserve">
    <value>Social Platform</value>
  </data>
  <data name="FormFields_SocialPlatformHelpText" xml:space="preserve">
    <value>Select social media platform type</value>
  </data>
  <data name="FormFields_SocialUrl" xml:space="preserve">
    <value>Social Media URL</value>
  </data>
  <data name="FormFields_SocialUrlHelpText" xml:space="preserve">
    <value>Social media platform URL address</value>
  </data>
  <data name="FormFields_SocialIcon" xml:space="preserve">
    <value>Social Media Icon</value>
  </data>
  <data name="FormFields_SocialIconHelpText" xml:space="preserve">
    <value>Social media platform icon class name or image</value>
  </data>
  <data name="FormFields_ShowAdminLinks" xml:space="preserve">
    <value>Show Admin Links</value>
  </data>
  <data name="FormFields_ShowAdminLinksHelpText" xml:space="preserve">
    <value>Display administrator-related links in footer</value>
  </data>
  <data name="FormFields_BorderColor" xml:space="preserve">
    <value>Border Color</value>
  </data>
  <data name="FormFields_BorderColorHelpText" xml:space="preserve">
    <value>Border color setting</value>
  </data>
  <data name="FormFields_LinkHoverColor" xml:space="preserve">
    <value>Link Hover Color</value>
  </data>
  <data name="FormFields_LinkHoverColorHelpText" xml:space="preserve">
    <value>Text color when hovering over links</value>
  </data>
  <data name="FormFields_PleaseSelect" xml:space="preserve">
    <value>Please Select</value>
  </data>
  <data name="FormFields_Other" xml:space="preserve">
    <value>Other</value>
  </data>

  <!-- Content Component Fields -->
  <data name="FormFields_Content" xml:space="preserve">
    <value>Content</value>
  </data>
  <data name="FormFields_ContentHelpText" xml:space="preserve">
    <value>Enter main content with multilingual rich text support</value>
  </data>
  <data name="FormFields_ContentBlocks" xml:space="preserve">
    <value>Content Blocks</value>
  </data>
  <data name="FormFields_ContentBlocksHelpText" xml:space="preserve">
    <value>Add multiple content blocks with images, icons and links</value>
  </data>
  <data name="FormFields_BlockTitle" xml:space="preserve">
    <value>Block Title</value>
  </data>
  <data name="FormFields_BlockContent" xml:space="preserve">
    <value>Block Content</value>
  </data>
  <data name="FormFields_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="FormFields_Icon" xml:space="preserve">
    <value>Icon</value>
  </data>
  <data name="FormFields_Link" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="FormFields_Layout" xml:space="preserve">
    <value>Layout</value>
  </data>
  <data name="FormFields_LayoutHelpText" xml:space="preserve">
    <value>Select display layout for content blocks</value>
  </data>
  <data name="FormFields_GridLayout" xml:space="preserve">
    <value>Grid Layout</value>
  </data>
  <data name="FormFields_ListLayout" xml:space="preserve">
    <value>List Layout</value>
  </data>
  <data name="FormFields_ShowDivider" xml:space="preserve">
    <value>Show Divider</value>
  </data>
  <data name="FormFields_ShowDividerHelpText" xml:space="preserve">
    <value>Show divider lines between content blocks</value>
  </data>
  <data name="FormFields_AnimationEnabled" xml:space="preserve">
    <value>Enable Animation</value>
  </data>
  <data name="FormFields_AnimationEnabledHelpText" xml:space="preserve">
    <value>Enable fade-in animation effects on scroll</value>
  </data>
  <data name="FormFields_AnimationType" xml:space="preserve">
    <value>Animation Type</value>
  </data>
  <data name="FormFields_AnimationTypeHelpText" xml:space="preserve">
    <value>Select animation type for content blocks</value>
  </data>
  <!-- Content Component Groups -->
  <data name="FormGroups_ContentBlocks" xml:space="preserve">
    <value>Content Blocks</value>
  </data>

  <!-- Form Groups -->
  <data name="FormGroups_ContactInfo" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="FormGroups_Navigation" xml:space="preserve">
    <value>Navigation Settings</value>
  </data>
  <data name="FormGroups_MobileSettings" xml:space="preserve">
    <value>Mobile Settings</value>
  </data>
  <data name="FormGroups_CompanyInfo" xml:space="preserve">
    <value>Company Information</value>
  </data>
  <data name="FormGroups_SocialMedia" xml:space="preserve">
    <value>Social Media</value>
  </data>

  <!-- Message Component Related -->
  <data name="ShowTitle" xml:space="preserve">
    <value>Show Title</value>
  </data>
  <data name="ShowTitleHelpText" xml:space="preserve">
    <value>Whether to display the title at the top of the component</value>
  </data>
  <data name="ShowSubtitle" xml:space="preserve">
    <value>Show Subtitle</value>
  </data>
  <data name="ShowSubtitleHelpText" xml:space="preserve">
    <value>Whether to display the subtitle below the title</value>
  </data>
  <data name="ShowDescription" xml:space="preserve">
    <value>Show Description</value>
  </data>
  <data name="ShowDescriptionHelpText" xml:space="preserve">
    <value>Whether to display the form description text</value>
  </data>
  <data name="FormWidth" xml:space="preserve">
    <value>Form Width</value>
  </data>
  <data name="FormWidthHelpText" xml:space="preserve">
    <value>Select the width of the form container</value>
  </data>
  <data name="SmallWidth" xml:space="preserve">
    <value>Small</value>
  </data>
  <data name="MediumWidth" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="LargeWidth" xml:space="preserve">
    <value>Large</value>
  </data>
  <data name="FullWidth" xml:space="preserve">
    <value>Full Width</value>
  </data>
  <data name="SubmitButtonText" xml:space="preserve">
    <value>Submit Button Text</value>
  </data>
  <data name="SubmitButtonTextHelpText" xml:space="preserve">
    <value>Text displayed on the form submit button</value>
  </data>
  <data name="RequiredFieldsNote" xml:space="preserve">
    <value>Required Fields Note</value>
  </data>
  <data name="RequiredFieldsNoteHelpText" xml:space="preserve">
    <value>Note text for required fields in the form</value>
  </data>
  <data name="PrivacyNotice" xml:space="preserve">
    <value>Privacy Notice</value>
  </data>
  <data name="PrivacyNoticeHelpText" xml:space="preserve">
    <value>Privacy policy notice text at the bottom of the form</value>
  </data>

  <!-- Carousel Component Resources -->
  <data name="FormFields_CarouselItems" xml:space="preserve">
    <value>Carousel Items</value>
  </data>
  <data name="FormFields_CarouselItemsHelpText" xml:space="preserve">
    <value>Add carousel images and content items</value>
  </data>
  <data name="FormFields_GalleryItems" xml:space="preserve">
    <value>Gallery Items</value>
  </data>
  <data name="FormFields_GalleryItemsHelpText" xml:space="preserve">
    <value>Add gallery display items</value>
  </data>
  <data name="FormFields_SliderItems" xml:space="preserve">
    <value>Slider Items</value>
  </data>
  <data name="FormFields_SliderItemsHelpText" xml:space="preserve">
    <value>Add compact slider items</value>
  </data>
  <data name="FormFields_AutoPlay" xml:space="preserve">
    <value>Auto Play</value>
  </data>
  <data name="FormFields_AutoPlayHelpText" xml:space="preserve">
    <value>Enable automatic carousel rotation</value>
  </data>
  <data name="FormFields_AutoPlayInterval" xml:space="preserve">
    <value>Auto Play Interval</value>
  </data>
  <data name="FormFields_AutoPlayIntervalHelpText" xml:space="preserve">
    <value>Time interval for auto play (milliseconds)</value>
  </data>
  <data name="FormFields_ShowIndicators" xml:space="preserve">
    <value>Show Indicators</value>
  </data>
  <data name="FormFields_ShowNavigation" xml:space="preserve">
    <value>Show Navigation</value>
  </data>
  <data name="FormFields_PauseOnHover" xml:space="preserve">
    <value>Pause On Hover</value>
  </data>
  <data name="FormFields_InfiniteLoop" xml:space="preserve">
    <value>Infinite Loop</value>
  </data>
  <data name="FormFields_TransitionEffect" xml:space="preserve">
    <value>Transition Effect</value>
  </data>
  <data name="FormFields_TransitionDuration" xml:space="preserve">
    <value>Transition Duration</value>
  </data>
  <data name="FormFields_TransitionDurationHelpText" xml:space="preserve">
    <value>Duration of transition animation (milliseconds)</value>
  </data>
  <data name="FormFields_Height" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="FormFields_CustomHeight" xml:space="preserve">
    <value>Custom Height</value>
  </data>
  <data name="FormFields_CustomHeightHelpText" xml:space="preserve">
    <value>Enter custom height value, e.g.: 400px, 50vh</value>
  </data>
  <data name="FormFields_ShowCaptions" xml:space="preserve">
    <value>Show Captions</value>
  </data>
  <data name="FormFields_CaptionPosition" xml:space="preserve">
    <value>Caption Position</value>
  </data>
  <data name="FormFields_CarouselType" xml:space="preserve">
    <value>Carousel Type</value>
  </data>
  <data name="FormFields_ActionButtons" xml:space="preserve">
    <value>Action Buttons</value>
  </data>
  <data name="FormFields_ButtonText" xml:space="preserve">
    <value>Button Text</value>
  </data>
  <data name="FormFields_ButtonUrl" xml:space="preserve">
    <value>Button URL</value>
  </data>
  <data name="FormFields_ButtonStyle" xml:space="preserve">
    <value>Button Style</value>
  </data>
  <data name="FormFields_AltText" xml:space="preserve">
    <value>Alt Text</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>Subtitle</value>
  </data>

  <!-- Form Groups -->
  <data name="FormGroups_Content" xml:space="preserve">
    <value>Content Settings</value>
  </data>
  <data name="FormGroups_Settings" xml:space="preserve">
    <value>Feature Settings</value>
  </data>
  <data name="FormGroups_Appearance" xml:space="preserve">
    <value>Appearance Settings</value>
  </data>
  <data name="FormGroups_Statistics" xml:space="preserve">
    <value>Statistics Settings</value>
  </data>
  <data name="CompanyOverview_Stats" xml:space="preserve">
    <value>Statistics Settings</value>
  </data>
  <data name="StatItem_Label" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="StatItem_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="StatItem_Icon" xml:space="preserve">
    <value>Icon</value>
  </data>

  <!-- Option Values -->
  <data name="TransitionEffect_Slide" xml:space="preserve">
    <value>Slide</value>
  </data>
  <data name="TransitionEffect_Fade" xml:space="preserve">
    <value>Fade</value>
  </data>
  <data name="Height_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="Height_Small" xml:space="preserve">
    <value>Small</value>
  </data>
  <data name="Height_Medium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="Height_Large" xml:space="preserve">
    <value>Large</value>
  </data>
  <data name="Height_Full" xml:space="preserve">
    <value>Full Screen</value>
  </data>
  <data name="Height_Custom" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="CaptionPosition_Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="CaptionPosition_Top" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="CaptionPosition_Center" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="CaptionPosition_Overlay" xml:space="preserve">
    <value>Overlay</value>
  </data>
  <data name="CarouselType_Banner" xml:space="preserve">
    <value>Banner Carousel</value>
  </data>
  <data name="CarouselType_Gallery" xml:space="preserve">
    <value>Gallery Display</value>
  </data>
  <data name="CarouselType_Slider" xml:space="preserve">
    <value>Compact Slider</value>
  </data>
  <data name="ButtonStyle_Primary" xml:space="preserve">
    <value>Primary</value>
  </data>
  <data name="ButtonStyle_Secondary" xml:space="preserve">
    <value>Secondary</value>
  </data>
  <data name="ButtonStyle_Outline" xml:space="preserve">
    <value>Outline</value>
  </data>
  <data name="ButtonStyle_Ghost" xml:space="preserve">
    <value>Ghost</value>
  </data>
  <!-- Breadcrumb Component Resources -->
  <data name="BreadcrumbHomeLinkText" xml:space="preserve">
    <value>Home Link Text</value>
  </data>
  <data name="BreadcrumbHomeLinkTextHelpText" xml:space="preserve">
    <value>Text displayed for the home link in breadcrumb navigation</value>
  </data>
  <data name="BreadcrumbHomeUrl" xml:space="preserve">
    <value>Home URL</value>
  </data>
  <data name="BreadcrumbHomeUrlHelpText" xml:space="preserve">
    <value>URL address to navigate to when clicking the home link</value>
  </data>
  <data name="BreadcrumbAriaLabel" xml:space="preserve">
    <value>Aria Label</value>
  </data>
  <data name="BreadcrumbAriaLabelHelpText" xml:space="preserve">
    <value>Accessibility label for the breadcrumb navigation</value>
  </data>
  <data name="BreadcrumbShowHomeIcon" xml:space="preserve">
    <value>Show Home Icon</value>
  </data>
  <data name="BreadcrumbShowHomeIconHelpText" xml:space="preserve">
    <value>Display a house icon before the home link</value>
  </data>
  <data name="BreadcrumbRoundedCorners" xml:space="preserve">
    <value>Rounded Corners</value>
  </data>
  <data name="BreadcrumbRoundedCornersHelpText" xml:space="preserve">
    <value>Add rounded corners to the breadcrumb navigation container</value>
  </data>
  <data name="BreadcrumbBackgroundStyle" xml:space="preserve">
    <value>Background Style</value>
  </data>
  <data name="BreadcrumbBackgroundStyleHelpText" xml:space="preserve">
    <value>Select the background style for breadcrumb navigation</value>
  </data>
  <data name="BreadcrumbBackgroundStyleLight" xml:space="preserve">
    <value>Light Background</value>
  </data>
  <data name="BreadcrumbBackgroundStyleDark" xml:space="preserve">
    <value>Dark Background</value>
  </data>
  <data name="BreadcrumbBackgroundStyleTransparent" xml:space="preserve">
    <value>Transparent Background</value>
  </data>
  <data name="BreadcrumbItems" xml:space="preserve">
    <value>Breadcrumb Items</value>
  </data>
  <data name="BreadcrumbItemsHelpText" xml:space="preserve">
    <value>Add intermediate level items for breadcrumb navigation (home and current page do not need to be added)</value>
  </data>
  <data name="BreadcrumbItemText" xml:space="preserve">
    <value>Item Text</value>
  </data>
  <data name="BreadcrumbItemUrl" xml:space="preserve">
    <value>Item URL</value>
  </data>

  <!-- Cookie Policy Form Fields -->
  <data name="FormFields_CookiePolicyTitleHelpText" xml:space="preserve">
    <value>Title for the cookie policy banner, optional</value>
  </data>
  <data name="FormFields_CookiePolicyMessageHelpText" xml:space="preserve">
    <value>Cookie policy description text, supports multiple languages</value>
  </data>
  <data name="FormFields_AcceptButtonText" xml:space="preserve">
    <value>Accept Button Text</value>
  </data>
  <data name="FormFields_AcceptButtonTextHelpText" xml:space="preserve">
    <value>Button text displayed when user accepts cookie policy</value>
  </data>
  <data name="FormFields_DeclineButtonText" xml:space="preserve">
    <value>Decline Button Text</value>
  </data>
  <data name="FormFields_DeclineButtonTextHelpText" xml:space="preserve">
    <value>Button text displayed when user declines cookie policy</value>
  </data>
  <data name="FormFields_LearnMoreText" xml:space="preserve">
    <value>Learn More Link Text</value>
  </data>
  <data name="FormFields_LearnMoreTextHelpText" xml:space="preserve">
    <value>Link text to detailed cookie policy page</value>
  </data>
  <data name="FormFields_LearnMoreUrl" xml:space="preserve">
    <value>Learn More URL</value>
  </data>
  <data name="FormFields_LearnMoreUrlHelpText" xml:space="preserve">
    <value>URL address of the detailed cookie policy page</value>
  </data>
  <data name="FormFields_Position" xml:space="preserve">
    <value>Display Position</value>
  </data>
  <data name="FormFields_PositionHelpText" xml:space="preserve">
    <value>Position of the cookie banner on the page</value>
  </data>
  <data name="FormFields_PositionBottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="FormFields_PositionTop" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="FormFields_BackgroundColor" xml:space="preserve">
    <value>Background Color</value>
  </data>
  <data name="FormFields_BackgroundColorHelpText" xml:space="preserve">
    <value>Background color theme of the cookie banner</value>
  </data>
  <data name="FormFields_BackgroundColorDark" xml:space="preserve">
    <value>Dark</value>
  </data>
  <data name="FormFields_BackgroundColorLight" xml:space="preserve">
    <value>Light</value>
  </data>
  <data name="FormFields_ShowDeclineButton" xml:space="preserve">
    <value>Show Decline Button</value>
  </data>
  <data name="FormFields_ShowDeclineButtonHelpText" xml:space="preserve">
    <value>Whether to show the decline cookie button</value>
  </data>
  <data name="FormFields_ShowLearnMoreLink" xml:space="preserve">
    <value>Show Learn More Link</value>
  </data>
  <data name="FormFields_ShowLearnMoreLinkHelpText" xml:space="preserve">
    <value>Whether to show the link to detailed cookie policy</value>
  </data>
  <data name="FormFields_AutoHide" xml:space="preserve">
    <value>Auto Hide</value>
  </data>
  <data name="FormFields_AutoHideHelpText" xml:space="preserve">
    <value>Whether to automatically accept and hide banner after specified time</value>
  </data>
  <data name="FormFields_AutoHideDelay" xml:space="preserve">
    <value>Auto Hide Delay (ms)</value>
  </data>
  <data name="FormFields_AutoHideDelayHelpText" xml:space="preserve">
    <value>Wait time before auto hide in milliseconds</value>
  </data>

  <!-- Form Groups -->
  <data name="FormGroups_AdvancedSettings" xml:space="preserve">
    <value>Advanced Settings</value>
  </data>

  <!-- CTA Component Resources -->
  <!-- Background Color Options -->
  <data name="FormFields_BackgroundColor_Primary" xml:space="preserve">
    <value>Primary</value>
  </data>
  <data name="FormFields_BackgroundColor_Secondary" xml:space="preserve">
    <value>Secondary</value>
  </data>
  <data name="FormFields_BackgroundColor_Accent" xml:space="preserve">
    <value>Accent</value>
  </data>
  <data name="FormFields_BackgroundColor_Gray" xml:space="preserve">
    <value>Gray</value>
  </data>
  <data name="FormFields_BackgroundColor_Transparent" xml:space="preserve">
    <value>Transparent</value>
  </data>

  <!-- Text Color Options -->
  <data name="WhiteText" xml:space="preserve">
    <value>White Text</value>
  </data>
  <data name="BlackText" xml:space="preserve">
    <value>Black Text</value>
  </data>
  <data name="PrimaryText" xml:space="preserve">
    <value>Primary Text</value>
  </data>
  <data name="GrayText" xml:space="preserve">
    <value>Gray Text</value>
  </data>

  <!-- Spacing Options -->
  <data name="TightSpacing" xml:space="preserve">
    <value>Tight Spacing</value>
  </data>
  <data name="NormalSpacing" xml:space="preserve">
    <value>Normal Spacing</value>
  </data>
  <data name="LooseSpacing" xml:space="preserve">
    <value>Loose Spacing</value>
  </data>

  <!-- Border Radius Options -->
  <data name="NoBorderRadius" xml:space="preserve">
    <value>No Radius</value>
  </data>
  <data name="SmallBorderRadius" xml:space="preserve">
    <value>Small Radius</value>
  </data>
  <data name="NormalBorderRadius" xml:space="preserve">
    <value>Normal Radius</value>
  </data>
  <data name="LargeBorderRadius" xml:space="preserve">
    <value>Large Radius</value>
  </data>
  <data name="FullBorderRadius" xml:space="preserve">
    <value>Full Radius</value>
  </data>

  <!-- Animation Options -->
  <data name="FadeInAnimation" xml:space="preserve">
    <value>Fade In</value>
  </data>
  <data name="SlideInLeftAnimation" xml:space="preserve">
    <value>Slide In Left</value>
  </data>
  <data name="SlideInRightAnimation" xml:space="preserve">
    <value>Slide In Right</value>
  </data>
  <data name="ScaleInAnimation" xml:space="preserve">
    <value>Scale In</value>
  </data>

  <!-- FAQ Component Fields -->
  <data name="FormFields_FAQItems" xml:space="preserve">
    <value>FAQ Items</value>
  </data>
  <data name="FormFields_FAQItemsHelpText" xml:space="preserve">
    <value>Add and manage frequently asked questions and their answers</value>
  </data>
  <data name="FormFields_Question" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="FormFields_Answer" xml:space="preserve">
    <value>Answer</value>
  </data>
  <data name="FormFields_Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="FormFields_Order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="FormFields_IsPopular" xml:space="preserve">
    <value>Popular Question</value>
  </data>
  <data name="FormFields_ShowSearch" xml:space="preserve">
    <value>Show Search Box</value>
  </data>
  <data name="FormFields_ShowSearchHelpText" xml:space="preserve">
    <value>Allow users to search through FAQ items</value>
  </data>
  <data name="FormFields_ShowCategoriesHelpText" xml:space="preserve">
    <value>Display category filter buttons</value>
  </data>
  <data name="FormFields_FAQLayoutHelpText" xml:space="preserve">
    <value>Choose the display layout for FAQ items</value>
  </data>
  <data name="FormFields_AccordionLayout" xml:space="preserve">
    <value>Accordion Layout</value>
  </data>
  <data name="FormFields_ListLayout" xml:space="preserve">
    <value>List Layout</value>
  </data>
  <data name="FormFields_AllowMultipleOpen" xml:space="preserve">
    <value>Allow Multiple Open</value>
  </data>
  <data name="FormFields_AllowMultipleOpenHelpText" xml:space="preserve">
    <value>Allow multiple questions to be expanded simultaneously in accordion mode</value>
  </data>
  <data name="FormFields_AccentColor" xml:space="preserve">
    <value>Accent Color</value>
  </data>
  <data name="FormFields_AccentColorHelpText" xml:space="preserve">
    <value>Choose the primary accent color for the component</value>
  </data>
  <data name="FormFields_PrimaryColor" xml:space="preserve">
    <value>Primary</value>
  </data>
  <data name="FormFields_SecondaryColor" xml:space="preserve">
    <value>Secondary</value>
  </data>
  <data name="FormFields_SuccessColor" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="FormFields_InfoColor" xml:space="preserve">
    <value>Info</value>
  </data>

  <!-- FAQ Component Groups -->
  <data name="FormGroups_FAQContent" xml:space="preserve">
    <value>FAQ Content</value>
  </data>

  <!-- Basic Form Fields -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="FormFields_TitleHelpText" xml:space="preserve">
    <value>Main title of the component</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>Subtitle</value>
  </data>
  <data name="FormFields_SubtitleHelpText" xml:space="preserve">
    <value>Subtitle or description text of the component</value>
  </data>
  <data name="FormFields_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="FormFields_DescriptionHelpText" xml:space="preserve">
    <value>Detailed description of the component</value>
  </data>
  <data name="FormFields_Content" xml:space="preserve">
    <value>Content</value>
  </data>
  <data name="FormFields_ContentHelpText" xml:space="preserve">
    <value>Main content of the component</value>
  </data>
  <data name="FormFields_AnimationEnabled" xml:space="preserve">
    <value>Enable Animation</value>
  </data>
  <data name="FormFields_AnimationEnabledHelpText" xml:space="preserve">
    <value>Enable animation effects for the component</value>
  </data>
  <data name="FormFields_Layout" xml:space="preserve">
    <value>Layout</value>
  </data>

  <!-- CompanyBasicInfo Component Resources -->
  <data name="CompanyBasicInfo_Title" xml:space="preserve">
    <value>Company Basic Information</value>
  </data>
  <data name="CompanyBasicInfo_EstablishedDate" xml:space="preserve">
    <value>Established Date</value>
  </data>
  <data name="CompanyBasicInfo_Capital" xml:space="preserve">
    <value>Capital</value>
  </data>
  <data name="CompanyBasicInfo_Representative" xml:space="preserve">
    <value>Representative</value>
  </data>
  <data name="CompanyBasicInfo_EmployeeCount" xml:space="preserve">
    <value>Employee Count</value>
  </data>
  <data name="CompanyBasicInfo_BusinessType" xml:space="preserve">
    <value>Business Type</value>
  </data>
  <data name="CompanyBasicInfo_HeadOffice" xml:space="preserve">
    <value>Head Office</value>
  </data>
  <data name="CompanyBasicInfo_Website" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="CompanyBasicInfo_CustomFields" xml:space="preserve">
    <value>Custom Fields</value>
  </data>
  <data name="CompanyBasicInfo_Layout" xml:space="preserve">
    <value>Layout Style</value>
  </data>
  <data name="CompanyBasicInfo_ShowTitle" xml:space="preserve">
    <value>Show Title</value>
  </data>
  <data name="CompanyBasicInfo_ShowBorder" xml:space="preserve">
    <value>Show Border</value>
  </data>
  <data name="CompanyBasicInfo_TitleText" xml:space="preserve">
    <value>Custom Title</value>
  </data>
  <data name="CompanyBasicInfo_BackgroundStyle" xml:space="preserve">
    <value>Background Style</value>
  </data>

  <!-- Custom Field Related -->
  <data name="CustomField_Label" xml:space="preserve">
    <value>Field Label</value>
  </data>
  <data name="CustomField_Value" xml:space="preserve">
    <value>Field Value</value>
  </data>
  <data name="CustomField_Icon" xml:space="preserve">
    <value>Icon</value>
  </data>
  <data name="CustomField_IsVisible" xml:space="preserve">
    <value>Visible</value>
  </data>

  <!-- Layout Options -->
  <data name="Layout_Grid" xml:space="preserve">
    <value>Grid Layout</value>
  </data>
  <data name="Layout_List" xml:space="preserve">
    <value>List Layout</value>
  </data>
  <data name="Layout_Card" xml:space="preserve">
    <value>Card Layout</value>
  </data>

  <!-- Background Style Options -->
  <data name="Background_White" xml:space="preserve">
    <value>White Background</value>
  </data>
  <data name="Background_Gray" xml:space="preserve">
    <value>Gray Background</value>
  </data>
  <data name="Background_Transparent" xml:space="preserve">
    <value>Transparent Background</value>
  </data>

  <!-- Field Display Controls -->
  <data name="CompanyBasicInfo_ShowEstablishedDate" xml:space="preserve">
    <value>Show Established Date</value>
  </data>
  <data name="CompanyBasicInfo_ShowCapital" xml:space="preserve">
    <value>Show Capital</value>
  </data>
  <data name="CompanyBasicInfo_ShowEmployeeScale" xml:space="preserve">
    <value>Show Employee Scale</value>
  </data>
  <data name="CompanyBasicInfo_ShowAddress" xml:space="preserve">
    <value>Show Address</value>
  </data>
  <data name="CompanyBasicInfo_ShowPhone" xml:space="preserve">
    <value>Show Phone</value>
  </data>
  <data name="CompanyBasicInfo_ShowEmail" xml:space="preserve">
    <value>Show Email</value>
  </data>
  <data name="CompanyBasicInfo_ShowWebsite" xml:space="preserve">
    <value>Show Website</value>
  </data>
  <data name="CompanyBasicInfo_ShowPresident" xml:space="preserve">
    <value>Show President</value>
  </data>
  <data name="CompanyBasicInfo_ShowPostalCode" xml:space="preserve">
    <value>Show Postal Code</value>
  </data>
  <data name="CompanyBasicInfo_ShowRegistrationNumber" xml:space="preserve">
    <value>Show Registration Number</value>
  </data>
  <data name="CompanyBasicInfo_President" xml:space="preserve">
    <value>President</value>
  </data>
  <data name="CompanyBasicInfo_PostalCode" xml:space="preserve">
    <value>Postal Code</value>
  </data>
  <data name="CompanyBasicInfo_RegistrationNumber" xml:space="preserve">
    <value>Registration Number</value>
  </data>
  <data name="CompanyBasicInfo_BasicInfo" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="CompanyBasicInfo_ContactInfo" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="CompanyBasicInfo_Fax" xml:space="preserve">
    <value>Fax</value>
  </data>

  <!-- PresidentMessage Component Resources -->
  <data name="PresidentMessage_Title" xml:space="preserve">
    <value>President's Message</value>
  </data>
  <data name="PresidentMessage_Biography" xml:space="preserve">
    <value>Biography</value>
  </data>
  <data name="PresidentMessage_NoData" xml:space="preserve">
    <value>No president information available</value>
  </data>
  <data name="PresidentMessage_MessageFrom" xml:space="preserve">
    <value>Message from President</value>
  </data>
  <data name="PresidentMessage_ShowPhoto" xml:space="preserve">
    <value>Show Photo</value>
  </data>
  <data name="PresidentMessage_ShowPosition" xml:space="preserve">
    <value>Show Position</value>
  </data>
  <data name="PresidentMessage_ShowBiography" xml:space="preserve">
    <value>Show Biography</value>
  </data>
  <data name="PresidentMessage_ShowTitle" xml:space="preserve">
    <value>Show Title</value>
  </data>
  <data name="PresidentMessage_ShowBorder" xml:space="preserve">
    <value>Show Border</value>
  </data>
  <data name="PresidentMessage_TitleText" xml:space="preserve">
    <value>Custom Title</value>
  </data>
  <data name="PresidentMessage_BackgroundStyle" xml:space="preserve">
    <value>Background Style</value>
  </data>
  <data name="PresidentMessage_PhotoPosition" xml:space="preserve">
    <value>Photo Position</value>
  </data>
  <data name="PresidentMessage_PhotoSize" xml:space="preserve">
    <value>Photo Size</value>
  </data>

  <!-- Photo Position Options -->
  <data name="PhotoPosition_Left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="PhotoPosition_Right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="PhotoPosition_Top" xml:space="preserve">
    <value>Top</value>
  </data>

  <!-- Photo Size Options -->
  <data name="PhotoSize_Small" xml:space="preserve">
    <value>Small</value>
  </data>
  <data name="PhotoSize_Medium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="PhotoSize_Large" xml:space="preserve">
    <value>Large</value>
  </data>

  <!-- Complete Variant Resources -->
  <data name="PresidentMessage_ToStakeholders" xml:space="preserve">
    <value>To Our Stakeholders</value>
  </data>
   <data name="FormFields_ShowMap" xml:space="preserve">
    <value>Show Map</value>
  </data>
  <data name="FormFields_ShowContactInfo" xml:space="preserve">
    <value>Show Contact Info</value>
  </data>
  <data name="FormFields_ItemsPerRow" xml:space="preserve">
    <value>Items Per Row</value>
  </data>

  <!-- Form Groups -->  
  <data name="FormGroups_CustomFields" xml:space="preserve">
    <value>Custom Fields</value>
  </data>
  <data name="FormGroups_FieldDisplay" xml:space="preserve">
    <value>Field Display</value>
  </data>

  <data name="FormFields_ShowImages" xml:space="preserve">
    <value>Show Images</value>
  </data>
  <data name="FormFields_BorderRadius" xml:space="preserve">
    <value>Border Radius</value>
  </data>
  <data name="FormFields_SpacingSettings" xml:space="preserve">
    <value>Spacing Settings</value>
  </data>

  <!-- Customer Cases specific fields -->
  <data name="CustomerCases" xml:space="preserve">
    <value>Customer Cases</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Company Logo</value>
  </data>
  <data name="Industry" xml:space="preserve">
    <value>Industry</value>
  </data>
  <data name="ProjectTitle" xml:space="preserve">
    <value>Project Title</value>
  </data>
  <data name="ProjectDescription" xml:space="preserve">
    <value>Project Description</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>Featured Image</value>
  </data>
  <data name="TestimonialText" xml:space="preserve">
    <value>Customer Testimonial</value>
  </data>
  <data name="TestimonialAuthor" xml:space="preserve">
    <value>Testimonial Author</value>
  </data>
  <data name="TestimonialPosition" xml:space="preserve">
    <value>Author Position</value>
  </data>
  <data name="CaseStudyUrl" xml:space="preserve">
    <value>Case Study URL</value>
  </data>
  <data name="IsFeatured" xml:space="preserve">
    <value>Featured Case</value>
  </data>
  <data name="CustomerCases_ShowIndustryFilter" xml:space="preserve">
    <value>Show Industry Filter</value>
  </data>
  <data name="CustomerCases_ShowTestimonials" xml:space="preserve">
    <value>Show Testimonials</value>
  </data>
  <data name="CustomerCases_ShowCompanyLogos" xml:space="preserve">
    <value>Show Company Logos</value>
  </data>
  <data name="FormGroups_CTA" xml:space="preserve">
    <value>Call to Action</value>
  </data>
  <data name="ShowCtaButton" xml:space="preserve">
    <value>Show CTA Button</value>
  </data>
  <data name="FormFields_ButtonUrl" xml:space="preserve">
    <value>Button URL</value>
  </data>
  <data name="FormGroups_Content" xml:space="preserve">
    <value>Content</value>
  </data>

  <!-- Process Steps fields (neutral naming, reusable) -->
  <data name="ProcessSteps" xml:space="preserve">
    <value>Process Steps</value>
  </data>
  <data name="StepNumber" xml:space="preserve">
    <value>Step Number</value>
  </data>
  <data name="FormFields_ShowStepNumbers" xml:space="preserve">
    <value>Show Step Numbers</value>
  </data>
  <data name="FormFields_ShowStepIcons" xml:space="preserve">
    <value>Show Step Icons</value>
  </data>
  <data name="FormFields_ShowConnectors" xml:space="preserve">
    <value>Show Connectors</value>
  </data>
  <data name="DetailText" xml:space="preserve">
    <value>Detail Text</value>
  </data>
  <data name="ActionText" xml:space="preserve">
    <value>Action Text</value>
  </data>
  <data name="ActionUrl" xml:space="preserve">
    <value>Action URL</value>
  </data>
  <data name="IsHighlighted" xml:space="preserve">
    <value>Is Highlighted</value>
  </data>
  <data name="NumberStyle" xml:space="preserve">
    <value>Number Style</value>
  </data>

  <!-- Pagination Component -->
  <data name="PageNumber" xml:space="preserve">
    <value>Current Page</value>
  </data>
  <data name="ItemsPerPage" xml:space="preserve">
    <value>Items Per Page</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="DisplayRange" xml:space="preserve">
    <value>Display Range</value>
  </data>
  <data name="Url" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>Parameters</value>
  </data>
  <data name="ShowFirstLast" xml:space="preserve">
    <value>Show First/Last</value>
  </data>
  <data name="ShowPrevNext" xml:space="preserve">
    <value>Show Prev/Next</value>
  </data>
  <data name="ShowPageNumbers" xml:space="preserve">
    <value>Show Page Numbers</value>
  </data>
  <data name="ShowTotal" xml:space="preserve">
    <value>Show Total</value>
  </data>

  <!-- SearchBox Component Fields (neutral naming for reuse) -->
  <data name="FormFields_SearchFields" xml:space="preserve">
    <value>Search Fields</value>
  </data>
  <data name="FormFields_SearchFieldsHelpText" xml:space="preserve">
    <value>Configure input fields for the search form</value>
  </data>
  <data name="FormFields_FieldName" xml:space="preserve">
    <value>Field Name</value>
  </data>
  <data name="FormFields_FieldType" xml:space="preserve">
    <value>Field Type</value>
  </data>
  <data name="FormFields_TextInput" xml:space="preserve">
    <value>Text Input</value>
  </data>
  <data name="FormFields_SelectDropdown" xml:space="preserve">
    <value>Select Dropdown</value>
  </data>
  <data name="FormFields_Placeholder" xml:space="preserve">
    <value>Placeholder</value>
  </data>
  <data name="FormFields_FieldWidth" xml:space="preserve">
    <value>Field Width</value>
  </data>
  <data name="FormFields_WidthAuto" xml:space="preserve">
    <value>Auto Width</value>
  </data>
  <data name="FormFields_WidthHalf" xml:space="preserve">
    <value>Half Width</value>
  </data>
  <data name="FormFields_WidthThird" xml:space="preserve">
    <value>Third Width</value>
  </data>
  <data name="FormFields_Required" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="FormFields_SelectOptions" xml:space="preserve">
    <value>Select Options</value>
  </data>
  <data name="FormFields_SelectOptionsHelpText" xml:space="preserve">
    <value>Configure options for dropdown select field</value>
  </data>
  <data name="FormFields_OptionValue" xml:space="preserve">
    <value>Option Value</value>
  </data>
  <data name="FormFields_OptionLabel" xml:space="preserve">
    <value>Option Label</value>
  </data>
  <data name="FormFields_ShowSearchButton" xml:space="preserve">
    <value>Show Search Button</value>
  </data>
  <data name="FormFields_ShowResetButton" xml:space="preserve">
    <value>Show Reset Button</value>
  </data>
  <data name="FormFields_SearchButtonText" xml:space="preserve">
    <value>Search Button Text</value>
  </data>
  <data name="FormFields_ResetButtonText" xml:space="preserve">
    <value>Reset Button Text</value>
  </data>
  <data name="FormFields_SearchAction" xml:space="preserve">
    <value>Search Action</value>
  </data>
  <data name="FormFields_SearchActionHelpText" xml:space="preserve">
    <value>Target URL for search form submission</value>
  </data>
  <data name="FormFields_SearchMethod" xml:space="preserve">
    <value>Search Method</value>
  </data>

  <!-- NewsList Component Fields -->
  <data name="FormFields_DateFormat" xml:space="preserve">
    <value>Date Format</value>
  </data>

</root>