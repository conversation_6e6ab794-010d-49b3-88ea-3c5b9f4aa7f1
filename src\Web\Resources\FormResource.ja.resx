﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- ボタン関連フィールド -->
  <data name="PrimaryButtonText" xml:space="preserve">
    <value>メインボタンテキスト</value>
  </data>
  <data name="PrimaryButtonLink" xml:space="preserve">
    <value>メインボタンリンク</value>
  </data>
  <data name="SecondaryButtonText" xml:space="preserve">
    <value>サブボタンテキスト</value>
  </data>
  <data name="SecondaryButtonLink" xml:space="preserve">
    <value>サブボタンリンク</value>
  </data>

  <!-- レイアウト設定フィールド -->
  <data name="TextAlignment" xml:space="preserve">
    <value>テキスト配置</value>
  </data>
  <data name="SectionHeight" xml:space="preserve">
    <value>セクション高さ</value>
  </data>
  <data name="OverlayOpacity" xml:space="preserve">
    <value>オーバーレイ透明度</value>
  </data>
  <data name="ShowOverlay" xml:space="preserve">
    <value>オーバーレイ表示</value>
  </data>
  <data name="EnableAnimation" xml:space="preserve">
    <value>アニメーション有効</value>
  </data>
  <data name="EnableParallax" xml:space="preserve">
    <value>パララックス有効</value>
  </data>
  <data name="ShowScrollIndicator" xml:space="preserve">
    <value>スクロールインジケーター表示</value>
  </data>

  <!-- 配置オプション -->
  <data name="AlignLeft" xml:space="preserve">
    <value>左寄せ</value>
  </data>
  <data name="AlignCenter" xml:space="preserve">
    <value>中央</value>
  </data>
  <data name="AlignRight" xml:space="preserve">
    <value>右寄せ</value>
  </data>

  <data name="SectionSize" xml:space="preserve">
    <value>セクションサイズ</value>
  </data>
  <!-- 高さオプション -->
  <data name="AutoHeight" xml:space="preserve">
    <value>自動高さ</value>
  </data>
  <data name="FullScreenHeight" xml:space="preserve">
    <value>全画面高さ</value>
  </data>
  <data name="LargeSize" xml:space="preserve">
    <value>大サイズ</value>
  </data>
  <data name="SmallSize" xml:space="preserve">
    <value>小サイズ</value>
  </data>
  <data name="MediumSize" xml:space="preserve">
    <value>中サイズ</value>
  </data>

  <!-- ヘルプテキスト -->
  <data name="PrimaryButtonTextHelpText" xml:space="preserve">
    <value>メインアクションボタンの表示テキスト</value>
  </data>
  <data name="PrimaryButtonLinkHelpText" xml:space="preserve">
    <value>メインボタンクリック時の遷移先URL</value>
  </data>
  <data name="SecondaryButtonTextHelpText" xml:space="preserve">
    <value>サブアクションボタンの表示テキスト</value>
  </data>
  <data name="SecondaryButtonLinkHelpText" xml:space="preserve">
    <value>サブボタンクリック時の遷移先URL</value>
  </data>
  <data name="TextAlignmentHelpText" xml:space="preserve">
    <value>テキストコンテンツの配置方法</value>
  </data>
  <data name="SectionHeightHelpText" xml:space="preserve">
    <value>ヒーローセクションの高さ設定</value>
  </data>
  <data name="OverlayOpacityHelpText" xml:space="preserve">
    <value>オーバーレイレイヤーの透明度 (0-100)</value>
  </data>
  <data name="ShowOverlayHelpText" xml:space="preserve">
    <value>背景画像/動画に半透明オーバーレイを表示</value>
  </data>
  <data name="EnableAnimationHelpText" xml:space="preserve">
    <value>テキストフェードインとボタンホバーアニメーションを有効</value>
  </data>
  <data name="EnableParallaxHelpText" xml:space="preserve">
    <value>パララックススクロール効果を有効</value>
  </data>
  <data name="ShowScrollIndicatorHelpText" xml:space="preserve">
    <value>下部にスクロール矢印インジケーターを表示</value>
  </data>
  <data name="BackgroundVideoHelpText" xml:space="preserve">
    <value>背景画像の上に表示するオプションの背景動画</value>
  </data>

  <!-- Header コンポーネントフィールド -->
  <data name="FormFields_CompanyName" xml:space="preserve">
    <value>会社名</value>
  </data>
  <data name="FormFields_CompanyNameHelpText" xml:space="preserve">
    <value>ヘッダーに表示される会社名</value>
  </data>
  <data name="FormFields_Address" xml:space="preserve">
    <value>住所</value>
  </data>
  <data name="FormFields_Sort" xml:space="preserve">
    <value>ソート番号</value>
  </data>
  <data name="FormFields_SortHelpText" xml:space="preserve">
    <value>メニュー項目のソート番号</value>
  </data>
  <data name="FormFields_AddressHelpText" xml:space="preserve">
    <value>ページに表示される会社住所</value>
  </data>
  <data name="FormFields_ContactInfo" xml:space="preserve">
    <value>連絡先情報</value>
  </data>
  <data name="FormFields_ContactInfoHelpText" xml:space="preserve">
    <value>ヘッダーまたはフッターに表示される連絡先情報</value>
  </data>
  <data name="FormFields_Phone" xml:space="preserve">
    <value>電話番号</value>
  </data>
  <data name="FormFields_PhoneHelpText" xml:space="preserve">
    <value>会社の連絡先電話番号</value>
  </data>
  <data name="FormFields_Email" xml:space="preserve">
    <value>メールアドレス</value>
  </data>
  <data name="FormFields_EmailHelpText" xml:space="preserve">
    <value>会社の連絡先メールアドレス</value>
  </data>
  <data name="FormFields_MenuItems" xml:space="preserve">
    <value>ナビゲーションメニュー項目</value>
  </data>
  <data name="FormFields_MenuItemsHelpText" xml:space="preserve">
    <value>ヘッダーナビゲーションメニューの項目</value>
  </data>
  <data name="FormFields_MenuItemText" xml:space="preserve">
    <value>メニュー項目テキスト</value>
  </data>
  <data name="FormFields_MenuItemTextHelpText" xml:space="preserve">
    <value>ナビゲーションメニュー項目に表示されるテキスト</value>
  </data>
  <data name="FormFields_MenuItemUrl" xml:space="preserve">
    <value>メニュー項目リンク</value>
  </data>
  <data name="FormFields_MenuItemUrlHelpText" xml:space="preserve">
    <value>メニュー項目をクリックした時の遷移先URL</value>
  </data>
  <data name="FormFields_IsActive" xml:space="preserve">
    <value>アクティブ</value>
  </data>
  <data name="FormFields_IsActiveHelpText" xml:space="preserve">
    <value>このメニュー項目がアクティブ状態かどうか</value>
  </data>
  <data name="FormFields_SubMenuItems" xml:space="preserve">
    <value>サブメニュー項目</value>
  </data>
  <data name="FormFields_SubMenuItemsHelpText" xml:space="preserve">
    <value>このメニュー項目のドロップダウンサブメニュー</value>
  </data>
  <data name="FormFields_SubMenuItemText" xml:space="preserve">
    <value>サブメニュー項目テキスト</value>
  </data>
  <data name="FormFields_SubMenuItemTextHelpText" xml:space="preserve">
    <value>サブメニュー項目に表示されるテキスト</value>
  </data>
  <data name="FormFields_SubMenuItemUrl" xml:space="preserve">
    <value>サブメニュー項目リンク</value>
  </data>
  <data name="FormFields_SubMenuItemUrlHelpText" xml:space="preserve">
    <value>サブメニュー項目をクリックした時の遷移先URL</value>
  </data>
  <data name="FormFields_MobileMenuButtonText" xml:space="preserve">
    <value>モバイルメニューボタンテキスト</value>
  </data>
  <data name="FormFields_MobileMenuButtonTextHelpText" xml:space="preserve">
    <value>モバイルメニューボタンに表示されるテキスト</value>
  </data>
  <data name="FormFields_ShowCompanyName" xml:space="preserve">
    <value>会社名表示</value>
  </data>
  <data name="FormFields_ShowCompanyNameHelpText" xml:space="preserve">
    <value>ヘッダーに会社名を表示</value>
  </data>
  <data name="FormFields_ShowLanguageSelector" xml:space="preserve">
    <value>言語選択表示</value>
  </data>
  <data name="FormFields_ShowLanguageSelectorHelpText" xml:space="preserve">
    <value>ヘッダーに多言語選択機能を表示</value>
  </data>
  <data name="FormFields_ShowDarkModeToggle" xml:space="preserve">
    <value>ダークモード切り替え表示</value>
  </data>
  <data name="FormFields_ShowDarkModeToggleHelpText" xml:space="preserve">
    <value>ヘッダーにダーク/ライトテーマ切り替えを表示</value>
  </data>
  <data name="FormFields_ShowSearchBox" xml:space="preserve">
    <value>検索ボックス表示</value>
  </data>
  <data name="FormFields_ShowSearchBoxHelpText" xml:space="preserve">
    <value>ヘッダーに検索入力ボックスを表示</value>
  </data>
  <data name="FormFields_BackgroundColor" xml:space="preserve">
    <value>背景色</value>
  </data>
  <data name="FormFields_BackgroundColorHelpText" xml:space="preserve">
    <value>背景色設定</value>
  </data>
  <data name="FormFields_TextColor" xml:space="preserve">
    <value>テキスト色</value>
  </data>
  <data name="FormFields_TextColorHelpText" xml:space="preserve">
    <value>テキスト色設定</value>
  </data>

  <data name="FormFields_BackgroundColor_White" xml:space="preserve">
    <value>白</value>
  </data>
  <data name="FormFields_BackgroundColor_Muted" xml:space="preserve">
    <value>淡</value>
  </data>
  <data name="FormFields_ColumnsDesktop" xml:space="preserve">
    <value>デスクトップ列数</value>
  </data>
  <data name="FormFields_ColumnsTablet" xml:space="preserve">
    <value>タブレット列数</value>
  </data>

  
  <data name="FormFields_MaxItems" xml:space="preserve">
    <value>最大項目数</value>
  </data>
  <data name="FormFields_ShowViewAllButton" xml:space="preserve">
    <value>"表示"ボタンを表示</value>
  </data>
  <data name="FormFields_ShowCategories" xml:space="preserve">
    <value>カテゴリーを表示</value>
  </data>
  <data name="FormFields_ShowDates" xml:space="preserve">
    <value>日付を表示</value>
  </data>
  <data name="FormFields_ShowExcerpts" xml:space="preserve">
    <value>要約を表示</value>
  </data>
  <data name="FormFields_ViewAllButtonText" xml:space="preserve">
    <value>"表示"ボタン文字</value>
  </data>
  <data name="FormFields_ViewAllButtonUrl" xml:space="preserve">
    <value>"表示"ボタンリンク</value>
  </data>

   <data name="FormFields_RecruitmentSection_Categories" xml:space="preserve">
    <value>求人情報カテゴリー</value>
  </data>

  <!-- Footer コンポーネントフィールド -->
  <data name="FormFields_CompanyDescription" xml:space="preserve">
    <value>会社スローガン</value>
  </data>
  <data name="FormFields_CompanyDescriptionHelpText" xml:space="preserve">
    <value>フッターに表示される会社スローガンテキスト</value>
  </data>
  <data name="FormFields_Copyright" xml:space="preserve">
    <value>著作権情報</value>
  </data>
  <data name="FormFields_CopyrightHelpText" xml:space="preserve">
    <value>フッターに表示される著作権表示テキスト</value>
  </data>
  <data name="FormFields_FooterSections" xml:space="preserve">
    <value>フッターセクション</value>
  </data>
  <data name="FormFields_FooterSectionsHelpText" xml:space="preserve">
    <value>フッターの各カラムセクション</value>
  </data>
  <data name="FormFields_SectionTitle" xml:space="preserve">
    <value>セクションタイトル</value>
  </data>
  <data name="FormFields_SectionTitleHelpText" xml:space="preserve">
    <value>フッターセクションのタイトルテキスト</value>
  </data>
  <data name="FormFields_SectionLinks" xml:space="preserve">
    <value>セクションリンク</value>
  </data>
  <data name="FormFields_SectionLinksHelpText" xml:space="preserve">
    <value>このセクション下のリンク項目</value>
  </data>
  <data name="FormFields_LinkText" xml:space="preserve">
    <value>リンクテキスト</value>
  </data>
  <data name="FormFields_LinkTextHelpText" xml:space="preserve">
    <value>リンクに表示されるテキスト</value>
  </data>
  <data name="FormFields_LinkUrl" xml:space="preserve">
    <value>リンクURL</value>
  </data>
  <data name="FormFields_LinkUrlHelpText" xml:space="preserve">
    <value>リンクをクリックした時の遷移先URL</value>
  </data>
  <data name="FormFields_OpenInNewTab" xml:space="preserve">
    <value>新しいタブで開く</value>
  </data>
  <data name="FormFields_OpenInNewTabHelpText" xml:space="preserve">
    <value>リンクを新しいブラウザタブで開くかどうか</value>
  </data>
  <data name="FormFields_SocialLinks" xml:space="preserve">
    <value>ソーシャルメディアリンク</value>
  </data>
  <data name="FormFields_SocialLinksHelpText" xml:space="preserve">
    <value>フッターに表示されるソーシャルメディアプラットフォームリンク</value>
  </data>
  <data name="FormFields_SocialPlatform" xml:space="preserve">
    <value>ソーシャルプラットフォーム</value>
  </data>
  <data name="FormFields_SocialPlatformHelpText" xml:space="preserve">
    <value>ソーシャルメディアプラットフォームタイプを選択</value>
  </data>
  <data name="FormFields_SocialUrl" xml:space="preserve">
    <value>ソーシャルメディアURL</value>
  </data>
  <data name="FormFields_SocialUrlHelpText" xml:space="preserve">
    <value>ソーシャルメディアプラットフォームのURL</value>
  </data>
  <data name="FormFields_SocialIcon" xml:space="preserve">
    <value>ソーシャルメディアアイコン</value>
  </data>
  <data name="FormFields_SocialIconHelpText" xml:space="preserve">
    <value>ソーシャルメディアプラットフォームのアイコンクラス名または画像</value>
  </data>
  <data name="FormFields_ShowAdminLinks" xml:space="preserve">
    <value>管理リンク表示</value>
  </data>
  <data name="FormFields_ShowAdminLinksHelpText" xml:space="preserve">
    <value>フッターに管理者関連リンクを表示</value>
  </data>
  <data name="FormFields_BorderColor" xml:space="preserve">
    <value>ボーダー色</value>
  </data>
  <data name="FormFields_BorderColorHelpText" xml:space="preserve">
    <value>ボーダー色設定</value>
  </data>
  <data name="FormFields_LinkHoverColor" xml:space="preserve">
    <value>リンクホバー色</value>
  </data>
  <data name="FormFields_LinkHoverColorHelpText" xml:space="preserve">
    <value>リンクにマウスオーバーした時のテキスト色</value>
  </data>
  <data name="FormFields_PleaseSelect" xml:space="preserve">
    <value>選択してください</value>
  </data>
  <data name="FormFields_Other" xml:space="preserve">
    <value>その他</value>
  </data>

  <!-- Contentコンポーネントフィールド -->
  <data name="FormFields_Content" xml:space="preserve">
    <value>コンテンツ</value>
  </data>
  <data name="FormFields_ContentHelpText" xml:space="preserve">
    <value>多言語リッチテキスト対応のメインコンテンツを入力</value>
  </data>
  <data name="FormFields_ContentBlocks" xml:space="preserve">
    <value>コンテンツブロック</value>
  </data>
  <data name="FormFields_ContentBlocksHelpText" xml:space="preserve">
    <value>画像、アイコン、リンク付きの複数コンテンツブロックを追加</value>
  </data>
  <data name="FormFields_BlockTitle" xml:space="preserve">
    <value>ブロックタイトル</value>
  </data>
  <data name="FormFields_BlockContent" xml:space="preserve">
    <value>ブロックコンテンツ</value>
  </data>
  <data name="FormFields_Image" xml:space="preserve">
    <value>画像</value>
  </data>
  <data name="FormFields_Icon" xml:space="preserve">
    <value>アイコン</value>
  </data>
  <data name="FormFields_Link" xml:space="preserve">
    <value>リンク</value>
  </data>
  <data name="FormFields_Layout" xml:space="preserve">
    <value>レイアウト</value>
  </data>
  <data name="FormFields_LayoutHelpText" xml:space="preserve">
    <value>コンテンツブロックの表示レイアウトを選択</value>
  </data>
  <data name="FormFields_GridLayout" xml:space="preserve">
    <value>グリッドレイアウト</value>
  </data>
  <data name="FormFields_ListLayout" xml:space="preserve">
    <value>リストレイアウト</value>
  </data>
  <data name="FormFields_ShowDivider" xml:space="preserve">
    <value>区切り線を表示</value>
  </data>
  <data name="FormFields_ShowDividerHelpText" xml:space="preserve">
    <value>コンテンツブロック間に区切り線を表示</value>
  </data>
  <data name="FormFields_AnimationEnabled" xml:space="preserve">
    <value>アニメーションを有効化</value>
  </data>
  <data name="FormFields_AnimationEnabledHelpText" xml:space="preserve">
    <value>スクロール時のフェードインアニメーション効果を有効化</value>
  </data>
  <data name="FormFields_AnimationType" xml:space="preserve">
    <value>アニメーションタイプ</value>
  </data>
  <data name="FormFields_AnimationTypeHelpText" xml:space="preserve">
    <value>コンテンツブロックのアニメーションタイプを選択</value>
  </data>

  <!-- Contentコンポーネントグループ -->
  <data name="FormGroups_ContentBlocks" xml:space="preserve">
    <value>コンテンツブロック</value>
  </data>

  <!-- フォームグループ -->
  <data name="FormGroups_ContactInfo" xml:space="preserve">
    <value>連絡先情報</value>
  </data>
  <data name="FormGroups_Navigation" xml:space="preserve">
    <value>ナビゲーション設定</value>
  </data>
  <data name="FormGroups_MobileSettings" xml:space="preserve">
    <value>モバイル設定</value>
  </data>
  <data name="FormGroups_CompanyInfo" xml:space="preserve">
    <value>会社情報</value>
  </data>
  <data name="FormGroups_SocialMedia" xml:space="preserve">
    <value>ソーシャルメディア</value>
  </data>

  <!-- メッセージコンポーネント関連 -->
  <data name="ShowTitle" xml:space="preserve">
    <value>タイトルを表示</value>
  </data>
  <data name="ShowTitleHelpText" xml:space="preserve">
    <value>コンポーネント上部にタイトルを表示するかどうか</value>
  </data>
  <data name="ShowSubtitle" xml:space="preserve">
    <value>サブタイトルを表示</value>
  </data>
  <data name="ShowSubtitleHelpText" xml:space="preserve">
    <value>タイトルの下にサブタイトルを表示するかどうか</value>
  </data>
  <data name="ShowDescription" xml:space="preserve">
    <value>説明を表示</value>
  </data>
  <data name="ShowDescriptionHelpText" xml:space="preserve">
    <value>フォームの説明テキストを表示するかどうか</value>
  </data>
  <data name="FormWidth" xml:space="preserve">
    <value>フォーム幅</value>
  </data>
  <data name="FormWidthHelpText" xml:space="preserve">
    <value>フォームコンテナの幅を選択</value>
  </data>
  <data name="SmallWidth" xml:space="preserve">
    <value>小</value>
  </data>
  <data name="MediumWidth" xml:space="preserve">
    <value>中</value>
  </data>
  <data name="LargeWidth" xml:space="preserve">
    <value>大</value>
  </data>
  <data name="FullWidth" xml:space="preserve">
    <value>全幅</value>
  </data>
  <data name="SubmitButtonText" xml:space="preserve">
    <value>送信ボタンテキスト</value>
  </data>
  <data name="SubmitButtonTextHelpText" xml:space="preserve">
    <value>フォーム送信ボタンに表示するテキスト</value>
  </data>
  <data name="RequiredFieldsNote" xml:space="preserve">
    <value>必須項目の注記</value>
  </data>
  <data name="RequiredFieldsNoteHelpText" xml:space="preserve">
    <value>フォームの必須項目に関する注記テキスト</value>
  </data>
  <data name="PrivacyNotice" xml:space="preserve">
    <value>プライバシー通知</value>
  </data>
  <data name="PrivacyNoticeHelpText" xml:space="preserve">
    <value>フォーム下部のプライバシーポリシー通知テキスト</value>
  </data>

  <!-- カルーセルコンポーネントリソース -->
  <data name="FormFields_CarouselItems" xml:space="preserve">
    <value>カルーセル項目</value>
  </data>
  <data name="FormFields_CarouselItemsHelpText" xml:space="preserve">
    <value>カルーセル画像とコンテンツ項目を追加</value>
  </data>
  <data name="FormFields_GalleryItems" xml:space="preserve">
    <value>ギャラリー項目</value>
  </data>
  <data name="FormFields_GalleryItemsHelpText" xml:space="preserve">
    <value>ギャラリー表示項目を追加</value>
  </data>
  <data name="FormFields_SliderItems" xml:space="preserve">
    <value>スライダー項目</value>
  </data>
  <data name="FormFields_SliderItemsHelpText" xml:space="preserve">
    <value>コンパクトスライダー項目を追加</value>
  </data>
  <data name="FormFields_AutoPlay" xml:space="preserve">
    <value>自動再生</value>
  </data>
  <data name="FormFields_AutoPlayHelpText" xml:space="preserve">
    <value>自動カルーセル回転を有効化</value>
  </data>
  <data name="FormFields_AutoPlayInterval" xml:space="preserve">
    <value>自動再生間隔</value>
  </data>
  <data name="FormFields_AutoPlayIntervalHelpText" xml:space="preserve">
    <value>自動再生の時間間隔（ミリ秒）</value>
  </data>
  <data name="FormFields_ShowIndicators" xml:space="preserve">
    <value>インジケーター表示</value>
  </data>
  <data name="FormFields_ShowNavigation" xml:space="preserve">
    <value>ナビゲーション表示</value>
  </data>
  <data name="FormFields_PauseOnHover" xml:space="preserve">
    <value>ホバー時一時停止</value>
  </data>
  <data name="FormFields_InfiniteLoop" xml:space="preserve">
    <value>無限ループ</value>
  </data>
  <data name="FormFields_TransitionEffect" xml:space="preserve">
    <value>トランジション効果</value>
  </data>
  <data name="FormFields_TransitionDuration" xml:space="preserve">
    <value>トランジション時間</value>
  </data>
  <data name="FormFields_TransitionDurationHelpText" xml:space="preserve">
    <value>トランジションアニメーションの持続時間（ミリ秒）</value>
  </data>
  <data name="FormFields_Height" xml:space="preserve">
    <value>高さ</value>
  </data>
  <data name="FormFields_CustomHeight" xml:space="preserve">
    <value>カスタム高さ</value>
  </data>
  <data name="FormFields_CustomHeightHelpText" xml:space="preserve">
    <value>カスタム高さ値を入力、例：400px, 50vh</value>
  </data>
  <data name="FormFields_ShowCaptions" xml:space="preserve">
    <value>キャプション表示</value>
  </data>
  <data name="FormFields_CaptionPosition" xml:space="preserve">
    <value>キャプション位置</value>
  </data>
  <data name="FormFields_CarouselType" xml:space="preserve">
    <value>カルーセルタイプ</value>
  </data>
  <data name="FormFields_ActionButtons" xml:space="preserve">
    <value>アクションボタン</value>
  </data>
  <data name="FormFields_ButtonText" xml:space="preserve">
    <value>ボタンテキスト</value>
  </data>
  <data name="FormFields_ButtonUrl" xml:space="preserve">
    <value>ボタンURL</value>
  </data>
  <data name="FormFields_ButtonStyle" xml:space="preserve">
    <value>ボタンスタイル</value>
  </data>
  <data name="FormFields_AltText" xml:space="preserve">
    <value>代替テキスト</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>サブタイトル</value>
  </data>

  <!-- フォームグループ -->
  <data name="FormGroups_Content" xml:space="preserve">
    <value>コンテンツ設定</value>
  </data>
  <data name="FormGroups_Settings" xml:space="preserve">
    <value>機能設定</value>
  </data>
  <data name="FormGroups_Appearance" xml:space="preserve">
    <value>外観設定</value>
  </data>
  <data name="FormGroups_Statistics" xml:space="preserve">
    <value>統計設定</value>
  </data>
  <data name="CompanyOverview_Stats" xml:space="preserve">
    <value>統計設定</value>
  </data>
  <data name="StatItem_Label" xml:space="preserve">
    <value>項目名</value>
  </data>
  <data name="StatItem_Value" xml:space="preserve">
    <value>値</value>
  </data>
  <data name="StatItem_Icon" xml:space="preserve">
    <value>アイコン</value>
  </data>


  <!-- オプション値 -->
  <data name="TransitionEffect_Slide" xml:space="preserve">
    <value>スライド</value>
  </data>
  <data name="TransitionEffect_Fade" xml:space="preserve">
    <value>フェード</value>
  </data>
  <data name="Height_Auto" xml:space="preserve">
    <value>自動</value>
  </data>
  <data name="Height_Small" xml:space="preserve">
    <value>小</value>
  </data>
  <data name="Height_Medium" xml:space="preserve">
    <value>中</value>
  </data>
  <data name="Height_Large" xml:space="preserve">
    <value>大</value>
  </data>
  <data name="Height_Full" xml:space="preserve">
    <value>全画面</value>
  </data>
  <data name="Height_Custom" xml:space="preserve">
    <value>カスタム</value>
  </data>
  <data name="CaptionPosition_Bottom" xml:space="preserve">
    <value>下部</value>
  </data>
  <data name="CaptionPosition_Top" xml:space="preserve">
    <value>上部</value>
  </data>
  <data name="CaptionPosition_Center" xml:space="preserve">
    <value>中央</value>
  </data>
  <data name="CaptionPosition_Overlay" xml:space="preserve">
    <value>オーバーレイ</value>
  </data>
  <data name="CarouselType_Banner" xml:space="preserve">
    <value>バナーカルーセル</value>
  </data>
  <data name="CarouselType_Gallery" xml:space="preserve">
    <value>ギャラリー表示</value>
  </data>
  <data name="CarouselType_Slider" xml:space="preserve">
    <value>コンパクトスライダー</value>
  </data>
  <data name="ButtonStyle_Primary" xml:space="preserve">
    <value>プライマリ</value>
  </data>
  <data name="ButtonStyle_Secondary" xml:space="preserve">
    <value>セカンダリ</value>
  </data>
  <data name="ButtonStyle_Outline" xml:space="preserve">
    <value>アウトライン</value>
  </data>
  <data name="ButtonStyle_Ghost" xml:space="preserve">
    <value>ゴースト</value>
  </data>
  <!-- パンくずナビゲーションコンポーネントリソース -->
  <data name="BreadcrumbHomeLinkText" xml:space="preserve">
    <value>ホームリンクテキスト</value>
  </data>
  <data name="BreadcrumbHomeLinkTextHelpText" xml:space="preserve">
    <value>パンくずナビゲーションのホームリンクに表示するテキスト</value>
  </data>
  <data name="BreadcrumbHomeUrl" xml:space="preserve">
    <value>ホームURL</value>
  </data>
  <data name="BreadcrumbHomeUrlHelpText" xml:space="preserve">
    <value>ホームリンクをクリックした時に移動するURL</value>
  </data>
  <data name="BreadcrumbAriaLabel" xml:space="preserve">
    <value>アリアラベル</value>
  </data>
  <data name="BreadcrumbAriaLabelHelpText" xml:space="preserve">
    <value>パンくずナビゲーションのアクセシビリティラベル</value>
  </data>
  <data name="BreadcrumbShowHomeIcon" xml:space="preserve">
    <value>ホームアイコン表示</value>
  </data>
  <data name="BreadcrumbShowHomeIconHelpText" xml:space="preserve">
    <value>ホームリンクの前に家のアイコンを表示</value>
  </data>
  <data name="BreadcrumbRoundedCorners" xml:space="preserve">
    <value>角丸</value>
  </data>
  <data name="BreadcrumbRoundedCornersHelpText" xml:space="preserve">
    <value>パンくずナビゲーションコンテナに角丸を追加</value>
  </data>
  <data name="BreadcrumbBackgroundStyle" xml:space="preserve">
    <value>背景スタイル</value>
  </data>
  <data name="BreadcrumbBackgroundStyleHelpText" xml:space="preserve">
    <value>パンくずナビゲーションの背景スタイルを選択</value>
  </data>
  <data name="BreadcrumbBackgroundStyleLight" xml:space="preserve">
    <value>ライト背景</value>
  </data>
  <data name="BreadcrumbBackgroundStyleDark" xml:space="preserve">
    <value>ダーク背景</value>
  </data>
  <data name="BreadcrumbBackgroundStyleTransparent" xml:space="preserve">
    <value>透明背景</value>
  </data>
  <data name="BreadcrumbItems" xml:space="preserve">
    <value>パンくず項目</value>
  </data>
  <data name="BreadcrumbItemsHelpText" xml:space="preserve">
    <value>パンくずナビゲーションの中間階層項目を追加（ホームと現在のページは追加不要）</value>
  </data>
  <data name="BreadcrumbItemText" xml:space="preserve">
    <value>項目テキスト</value>
  </data>
  <data name="BreadcrumbItemUrl" xml:space="preserve">
    <value>項目URL</value>
  </data>

  <!-- Cookie Policy Form Fields -->
  <data name="FormFields_CookiePolicyTitleHelpText" xml:space="preserve">
    <value>Cookieポリシーバナーのタイトル、オプション</value>
  </data>
  <data name="FormFields_CookiePolicyMessageHelpText" xml:space="preserve">
    <value>Cookieポリシーの説明文、多言語対応</value>
  </data>
  <data name="FormFields_AcceptButtonText" xml:space="preserve">
    <value>同意ボタンテキスト</value>
  </data>
  <data name="FormFields_AcceptButtonTextHelpText" xml:space="preserve">
    <value>ユーザーがCookieポリシーに同意する際に表示されるボタンテキスト</value>
  </data>
  <data name="FormFields_DeclineButtonText" xml:space="preserve">
    <value>拒否ボタンテキスト</value>
  </data>
  <data name="FormFields_DeclineButtonTextHelpText" xml:space="preserve">
    <value>ユーザーがCookieポリシーを拒否する際に表示されるボタンテキスト</value>
  </data>
  <data name="FormFields_LearnMoreText" xml:space="preserve">
    <value>詳細リンクテキスト</value>
  </data>
  <data name="FormFields_LearnMoreTextHelpText" xml:space="preserve">
    <value>詳細なCookieポリシーページへのリンクテキスト</value>
  </data>
  <data name="FormFields_LearnMoreUrl" xml:space="preserve">
    <value>詳細リンクURL</value>
  </data>
  <data name="FormFields_LearnMoreUrlHelpText" xml:space="preserve">
    <value>詳細なCookieポリシーページのURLアドレス</value>
  </data>
  <data name="FormFields_Position" xml:space="preserve">
    <value>表示位置</value>
  </data>
  <data name="FormFields_PositionHelpText" xml:space="preserve">
    <value>ページ内でのCookieバナーの表示位置</value>
  </data>
  <data name="FormFields_PositionBottom" xml:space="preserve">
    <value>下部</value>
  </data>
  <data name="FormFields_PositionTop" xml:space="preserve">
    <value>上部</value>
  </data>
  <data name="FormFields_BackgroundColor" xml:space="preserve">
    <value>背景色</value>
  </data>
  <data name="FormFields_BackgroundColorHelpText" xml:space="preserve">
    <value>Cookieバナーの背景色テーマ</value>
  </data>
  <data name="FormFields_BackgroundColorDark" xml:space="preserve">
    <value>ダーク</value>
  </data>
  <data name="FormFields_BackgroundColorLight" xml:space="preserve">
    <value>ライト</value>
  </data>
  <data name="FormFields_ShowDeclineButton" xml:space="preserve">
    <value>拒否ボタンを表示</value>
  </data>
  <data name="FormFields_ShowDeclineButtonHelpText" xml:space="preserve">
    <value>Cookie拒否ボタンを表示するかどうか</value>
  </data>
  <data name="FormFields_ShowLearnMoreLink" xml:space="preserve">
    <value>詳細リンクを表示</value>
  </data>
  <data name="FormFields_ShowLearnMoreLinkHelpText" xml:space="preserve">
    <value>詳細なCookieポリシーへのリンクを表示するかどうか</value>
  </data>
  <data name="FormFields_AutoHide" xml:space="preserve">
    <value>自動非表示</value>
  </data>
  <data name="FormFields_AutoHideHelpText" xml:space="preserve">
    <value>指定時間後に自動的に同意してバナーを非表示にするかどうか</value>
  </data>
  <data name="FormFields_AutoHideDelay" xml:space="preserve">
    <value>自動非表示遅延（ミリ秒）</value>
  </data>
  <data name="FormFields_AutoHideDelayHelpText" xml:space="preserve">
    <value>自動非表示前の待機時間（ミリ秒単位）</value>
  </data>

  <!-- Form Groups -->
  <data name="FormGroups_AdvancedSettings" xml:space="preserve">
    <value>詳細設定</value>
  </data>

  
    <!-- CTA Component Resources -->
  <!-- Background Color Options -->
  <data name="FormFields_BackgroundColor_Primary" xml:space="preserve">
    <value>プライマリ</value>
  </data>
  <data name="FormFields_BackgroundColor_Secondary" xml:space="preserve">
    <value>セカンダリ</value>
  </data>
  <data name="FormFields_BackgroundColor_Accent" xml:space="preserve">
    <value>アクセント</value>
  </data>
  <data name="FormFields_BackgroundColor_Gray" xml:space="preserve">
    <value>グレー</value>
  </data>
  <data name="FormFields_BackgroundColor_Transparent" xml:space="preserve">
    <value>透明</value>
  </data>

  <!-- Text Color Options -->
  <data name="WhiteText" xml:space="preserve">
    <value>白文字</value>
  </data>
  <data name="BlackText" xml:space="preserve">
    <value>黒文字</value>
  </data>
  <data name="PrimaryText" xml:space="preserve">
    <value>プライマリ文字</value>
  </data>
  <data name="GrayText" xml:space="preserve">
    <value>グレー文字</value>
  </data>

  <!-- Spacing Options -->
  <data name="TightSpacing" xml:space="preserve">
    <value>狭い間隔</value>
  </data>
  <data name="NormalSpacing" xml:space="preserve">
    <value>標準の間隔</value>
  </data>
  <data name="LooseSpacing" xml:space="preserve">
    <value>広い間隔</value>
  </data>

  <!-- Border Radius Options -->
  <data name="NoBorderRadius" xml:space="preserve">
    <value>角なし</value>
  </data>
  <data name="SmallBorderRadius" xml:space="preserve">
    <value>小さい角丸</value>
  </data>
  <data name="NormalBorderRadius" xml:space="preserve">
    <value>標準の角丸</value>
  </data>
  <data name="LargeBorderRadius" xml:space="preserve">
    <value>大きい角丸</value>
  </data>
  <data name="FullBorderRadius" xml:space="preserve">
    <value>完全な角丸</value>
  </data>

  <!-- Animation Options -->
  <data name="FadeInAnimation" xml:space="preserve">
    <value>フェードイン</value>
  </data>
  <data name="SlideInLeftAnimation" xml:space="preserve">
    <value>左からスライドイン</value>
  </data>
  <data name="SlideInRightAnimation" xml:space="preserve">
    <value>右からスライドイン</value>
  </data>
  <data name="ScaleInAnimation" xml:space="preserve">
    <value>拡大イン</value>
  </data>

  <!-- FAQ コンポーネントフィールド -->
  <data name="FormFields_FAQItems" xml:space="preserve">
    <value>よくある質問リスト</value>
  </data>
  <data name="FormFields_FAQItemsHelpText" xml:space="preserve">
    <value>よくある質問とその回答を追加・管理します</value>
  </data>
  <data name="FormFields_Question" xml:space="preserve">
    <value>質問</value>
  </data>
  <data name="FormFields_Answer" xml:space="preserve">
    <value>回答</value>
  </data>
  <data name="FormFields_Category" xml:space="preserve">
    <value>カテゴリ</value>
  </data>
  <data name="FormFields_Order" xml:space="preserve">
    <value>順序</value>
  </data>
  <data name="FormFields_IsPopular" xml:space="preserve">
    <value>人気の質問</value>
  </data>
  <data name="FormFields_ShowSearch" xml:space="preserve">
    <value>検索ボックスを表示</value>
  </data>
  <data name="FormFields_ShowSearchHelpText" xml:space="preserve">
    <value>ユーザーがFAQを検索できるようにします</value>
  </data>
  <data name="FormFields_ShowCategoriesHelpText" xml:space="preserve">
    <value>カテゴリフィルターボタンを表示します</value>
  </data>
  <data name="FormFields_FAQLayoutHelpText" xml:space="preserve">
    <value>FAQアイテムの表示レイアウトを選択します</value>
  </data>
  <data name="FormFields_AccordionLayout" xml:space="preserve">
    <value>アコーディオンレイアウト</value>
  </data>
  <data name="FormFields_ListLayout" xml:space="preserve">
    <value>リストレイアウト</value>
  </data>
  <data name="FormFields_AllowMultipleOpen" xml:space="preserve">
    <value>複数同時展開を許可</value>
  </data>
  <data name="FormFields_AllowMultipleOpenHelpText" xml:space="preserve">
    <value>アコーディオンモードで複数の質問を同時に展開できるようにします</value>
  </data>
  <data name="FormFields_AccentColor" xml:space="preserve">
    <value>アクセントカラー</value>
  </data>
  <data name="FormFields_AccentColorHelpText" xml:space="preserve">
    <value>コンポーネントの主要なアクセントカラーを選択します</value>
  </data>
  <data name="FormFields_PrimaryColor" xml:space="preserve">
    <value>プライマリ</value>
  </data>
  <data name="FormFields_SecondaryColor" xml:space="preserve">
    <value>セカンダリ</value>
  </data>
  <data name="FormFields_SuccessColor" xml:space="preserve">
    <value>成功</value>
  </data>
  <data name="FormFields_InfoColor" xml:space="preserve">
    <value>情報</value>
  </data>

  <!-- FAQ コンポーネントグループ -->
  <data name="FormGroups_FAQContent" xml:space="preserve">
    <value>FAQコンテンツ</value>
  </data>

  <!-- 基本フォームフィールド -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>タイトル</value>
  </data>
  <data name="FormFields_TitleHelpText" xml:space="preserve">
    <value>コンポーネントのメインタイトル</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>サブタイトル</value>
  </data>
  <data name="FormFields_SubtitleHelpText" xml:space="preserve">
    <value>コンポーネントのサブタイトルまたは説明テキスト</value>
  </data>
  <data name="FormFields_Description" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="FormFields_DescriptionHelpText" xml:space="preserve">
    <value>コンポーネントの詳細説明</value>
  </data>
  <data name="FormFields_Content" xml:space="preserve">
    <value>コンテンツ</value>
  </data>
  <data name="FormFields_ContentHelpText" xml:space="preserve">
    <value>コンポーネントのメインコンテンツ</value>
  </data>
  <data name="FormFields_AnimationEnabled" xml:space="preserve">
    <value>アニメーション有効</value>
  </data>
  <data name="FormFields_AnimationEnabledHelpText" xml:space="preserve">
    <value>コンポーネントのアニメーション効果を有効にします</value>
  </data>
  <data name="FormFields_Layout" xml:space="preserve">
    <value>レイアウト</value>
  </data>

  <!-- CompanyBasicInfo コンポーネントリソース -->
  <data name="CompanyBasicInfo_Title" xml:space="preserve">
    <value>会社基本情報</value>
  </data>
  <data name="CompanyBasicInfo_EstablishedDate" xml:space="preserve">
    <value>設立年月日</value>
  </data>
  <data name="CompanyBasicInfo_Capital" xml:space="preserve">
    <value>資本金</value>
  </data>
  <data name="CompanyBasicInfo_Representative" xml:space="preserve">
    <value>代表者</value>
  </data>
  <data name="CompanyBasicInfo_EmployeeCount" xml:space="preserve">
    <value>従業員数</value>
  </data>
  <data name="CompanyBasicInfo_BusinessType" xml:space="preserve">
    <value>事業内容</value>
  </data>
  <data name="CompanyBasicInfo_HeadOffice" xml:space="preserve">
    <value>本社所在地</value>
  </data>
  <data name="CompanyBasicInfo_Website" xml:space="preserve">
    <value>ウェブサイト</value>
  </data>
  <data name="CompanyBasicInfo_CustomFields" xml:space="preserve">
    <value>カスタムフィールド</value>
  </data>
  <data name="CompanyBasicInfo_Layout" xml:space="preserve">
    <value>レイアウトスタイル</value>
  </data>
  <data name="CompanyBasicInfo_ShowTitle" xml:space="preserve">
    <value>タイトル表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowBorder" xml:space="preserve">
    <value>枠線表示</value>
  </data>
  <data name="CompanyBasicInfo_TitleText" xml:space="preserve">
    <value>カスタムタイトル</value>
  </data>
  <data name="CompanyBasicInfo_BackgroundStyle" xml:space="preserve">
    <value>背景スタイル</value>
  </data>

  <!-- カスタムフィールド関連 -->
  <data name="CustomField_Label" xml:space="preserve">
    <value>フィールドラベル</value>
  </data>
  <data name="CustomField_Value" xml:space="preserve">
    <value>フィールド値</value>
  </data>
  <data name="CustomField_Icon" xml:space="preserve">
    <value>アイコン</value>
  </data>
  <data name="CustomField_IsVisible" xml:space="preserve">
    <value>表示</value>
  </data>

  <!-- レイアウトオプション -->
  <data name="Layout_Grid" xml:space="preserve">
    <value>グリッドレイアウト</value>
  </data>
  <data name="Layout_List" xml:space="preserve">
    <value>リストレイアウト</value>
  </data>
  <data name="Layout_Card" xml:space="preserve">
    <value>カードレイアウト</value>
  </data>

  <!-- 背景スタイルオプション -->
  <data name="Background_White" xml:space="preserve">
    <value>白背景</value>
  </data>
  <data name="Background_Gray" xml:space="preserve">
    <value>グレー背景</value>
  </data>
  <data name="Background_Transparent" xml:space="preserve">
    <value>透明背景</value>
  </data>

  <!-- フィールド表示制御 -->
  <data name="CompanyBasicInfo_ShowEstablishedDate" xml:space="preserve">
    <value>設立年月日を表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowCapital" xml:space="preserve">
    <value>資本金を表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowEmployeeScale" xml:space="preserve">
    <value>従業員規模を表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowAddress" xml:space="preserve">
    <value>住所を表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowPhone" xml:space="preserve">
    <value>電話番号を表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowEmail" xml:space="preserve">
    <value>メールアドレスを表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowWebsite" xml:space="preserve">
    <value>ウェブサイトを表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowPresident" xml:space="preserve">
    <value>代表取締役社長を表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowPostalCode" xml:space="preserve">
    <value>郵便番号を表示</value>
  </data>
  <data name="CompanyBasicInfo_ShowRegistrationNumber" xml:space="preserve">
    <value>登録番号を表示</value>
  </data>
  <data name="CompanyBasicInfo_President" xml:space="preserve">
    <value>代表取締役社長</value>
  </data>
  <data name="CompanyBasicInfo_PostalCode" xml:space="preserve">
    <value>郵便番号</value>
  </data>
  <data name="CompanyBasicInfo_RegistrationNumber" xml:space="preserve">
    <value>登録番号</value>
  </data>
  <data name="CompanyBasicInfo_BasicInfo" xml:space="preserve">
    <value>基本情報</value>
  </data>
  <data name="CompanyBasicInfo_ContactInfo" xml:space="preserve">
    <value>連絡先情報</value>
  </data>
  <data name="CompanyBasicInfo_Fax" xml:space="preserve">
    <value>FAX</value>
  </data>

  <!-- PresidentMessage コンポーネントリソース -->
  <data name="PresidentMessage_Title" xml:space="preserve">
    <value>社長メッセージ</value>
  </data>
  <data name="PresidentMessage_Biography" xml:space="preserve">
    <value>経歴</value>
  </data>
  <data name="PresidentMessage_NoData" xml:space="preserve">
    <value>社長情報がありません</value>
  </data>
  <data name="PresidentMessage_MessageFrom" xml:space="preserve">
    <value>社長からのメッセージ</value>
  </data>
  <data name="PresidentMessage_ShowPhoto" xml:space="preserve">
    <value>写真を表示</value>
  </data>
  <data name="PresidentMessage_ShowPosition" xml:space="preserve">
    <value>役職を表示</value>
  </data>
  <data name="PresidentMessage_ShowBiography" xml:space="preserve">
    <value>経歴を表示</value>
  </data>
  <data name="PresidentMessage_ShowTitle" xml:space="preserve">
    <value>タイトルを表示</value>
  </data>
  <data name="PresidentMessage_ShowBorder" xml:space="preserve">
    <value>枠線を表示</value>
  </data>
  <data name="PresidentMessage_TitleText" xml:space="preserve">
    <value>カスタムタイトル</value>
  </data>
  <data name="PresidentMessage_BackgroundStyle" xml:space="preserve">
    <value>背景スタイル</value>
  </data>
  <data name="PresidentMessage_PhotoPosition" xml:space="preserve">
    <value>写真位置</value>
  </data>
  <data name="PresidentMessage_PhotoSize" xml:space="preserve">
    <value>写真サイズ</value>
  </data>

  <!-- 写真位置オプション -->
  <data name="PhotoPosition_Left" xml:space="preserve">
    <value>左</value>
  </data>
  <data name="PhotoPosition_Right" xml:space="preserve">
    <value>右</value>
  </data>
  <data name="PhotoPosition_Top" xml:space="preserve">
    <value>上</value>
  </data>

  <!-- 写真サイズオプション -->
  <data name="PhotoSize_Small" xml:space="preserve">
    <value>小</value>
  </data>
  <data name="PhotoSize_Medium" xml:space="preserve">
    <value>中</value>
  </data>
  <data name="PhotoSize_Large" xml:space="preserve">
    <value>大</value>
  </data>

    <!-- Complete变体专用资源 -->
  <data name="PresidentMessage_ToStakeholders" xml:space="preserve">
    <value>ステークホルダーの皆様へ</value>
  </data>

    <data name="FormFields_ShowMap" xml:space="preserve">
    <value>表示する</value>
  </data>
  <data name="FormFields_ShowContactInfo" xml:space="preserve">
    <value>表示する</value>
  </data>
  <data name="FormFields_ItemsPerRow" xml:space="preserve">
    <value>表示する</value>
  </data>

  <!-- フォームグループ -->
  <data name="FormGroups_CustomFields" xml:space="preserve">
    <value>カスタムフィールド</value>
  </data>
  <data name="FormGroups_FieldDisplay" xml:space="preserve">
    <value>フィールド表示</value>
  </data>

  <data name="FormFields_ShowImages" xml:space="preserve">
    <value>写真を表示</value>
  </data>
  <data name="FormFields_BorderRadius" xml:space="preserve">
    <value>角の丸み</value>
  </data>
  <data name="FormFields_SpacingSettings" xml:space="preserve">
    <value>スペース設定</value>
  </data>

  <!-- Customer Cases specific fields -->
  <data name="CustomerCases" xml:space="preserve">
    <value>顧客事例</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>会社名</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>会社ロゴ</value>
  </data>
  <data name="Industry" xml:space="preserve">
    <value>業界</value>
  </data>
  <data name="ProjectTitle" xml:space="preserve">
    <value>プロジェクトタイトル</value>
  </data>
  <data name="ProjectDescription" xml:space="preserve">
    <value>プロジェクト説明</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>メイン画像</value>
  </data>
  <data name="TestimonialText" xml:space="preserve">
    <value>お客様の声</value>
  </data>
  <data name="TestimonialAuthor" xml:space="preserve">
    <value>コメント作成者</value>
  </data>
  <data name="TestimonialPosition" xml:space="preserve">
    <value>作成者役職</value>
  </data>
  <data name="CaseStudyUrl" xml:space="preserve">
    <value>事例詳細URL</value>
  </data>
  <data name="IsFeatured" xml:space="preserve">
    <value>注目事例</value>
  </data>
  <data name="CustomerCases_ShowIndustryFilter" xml:space="preserve">
    <value>業界フィルタを表示</value>
  </data>
  <data name="CustomerCases_ShowTestimonials" xml:space="preserve">
    <value>お客様の声を表示</value>
  </data>
  <data name="CustomerCases_ShowCompanyLogos" xml:space="preserve">
    <value>会社ロゴを表示</value>
  </data>
  <data name="FormGroups_CTA" xml:space="preserve">
    <value>行動喚起</value>
  </data>
  <data name="ShowCtaButton" xml:space="preserve">
    <value>CTAボタンを表示</value>
  </data>
  <data name="FormFields_ButtonUrl" xml:space="preserve">
    <value>ボタンURL</value>
  </data>
  <data name="FormGroups_Content" xml:space="preserve">
    <value>コンテンツ</value>
  </data>

  <!-- Process Steps fields (neutral naming, reusable) -->\n  <data name="ProcessSteps" xml:space="preserve">\n    <value>プロセス手順</value>\n  </data>\n  <data name="StepNumber" xml:space="preserve">\n    <value>ステップ番号</value>\n  </data>\n  <data name="FormFields_ShowStepNumbers" xml:space="preserve">\n    <value>ステップ番号を表示</value>\n  </data>\n  <data name="FormFields_ShowStepIcons" xml:space="preserve">\n    <value>ステップアイコンを表示</value>\n  </data>\n  <data name="FormFields_ShowConnectors" xml:space="preserve">\n    <value>コネクタを表示</value>\n  </data>\n  <data name="DetailText" xml:space="preserve">\n    <value>詳細テキスト</value>\n  </data>\n  <data name="ActionText" xml:space="preserve">\n    <value>アクションボタンテキスト</value>\n  </data>\n  <data name="ActionUrl" xml:space="preserve">\n    <value>アクションリンク</value>\n  </data>\n  <data name="IsHighlighted" xml:space="preserve">\n    <value>ハイライト表示</value>\n  </data>\n  <data name="NumberStyle" xml:space="preserve">\n    <value>番号スタイル</value>\n  </data>

  <!-- Pagination Component -->
  <data name="PageNumber" xml:space="preserve">
    <value>現在のページ</value>
  </data>
  <data name="ItemsPerPage" xml:space="preserve">
    <value>ページ毎の項目数</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>合計</value>
  </data>
  <data name="DisplayRange" xml:space="preserve">
    <value>表示範囲</value>
  </data>
  <data name="Url" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>パラメータ</value>
  </data>
  <data name="ShowFirstLast" xml:space="preserve">
    <value>最初/最後を表示</value>
  </data>
  <data name="ShowPrevNext" xml:space="preserve">
    <value>前/次を表示</value>
  </data>
  <data name="ShowPageNumbers" xml:space="preserve">
    <value>ページ番号を表示</value>
  </data>
  <data name="ShowTotal" xml:space="preserve">
    <value>合計を表示</value>
  </data>

  <!-- SearchBox コンポーネントフィールド (中性命名、再利用可能) -->
  <data name="FormFields_SearchFields" xml:space="preserve">
    <value>検索フィールド</value>
  </data>
  <data name="FormFields_SearchFieldsHelpText" xml:space="preserve">
    <value>検索フォームの入力フィールドを設定</value>
  </data>
  <data name="FormFields_FieldName" xml:space="preserve">
    <value>フィールド名</value>
  </data>
  <data name="FormFields_FieldType" xml:space="preserve">
    <value>フィールドタイプ</value>
  </data>
  <data name="FormFields_TextInput" xml:space="preserve">
    <value>テキスト入力</value>
  </data>
  <data name="FormFields_SelectDropdown" xml:space="preserve">
    <value>ドロップダウン選択</value>
  </data>
  <data name="FormFields_Placeholder" xml:space="preserve">
    <value>プレースホルダー</value>
  </data>
  <data name="FormFields_FieldWidth" xml:space="preserve">
    <value>フィールド幅</value>
  </data>
  <data name="FormFields_WidthAuto" xml:space="preserve">
    <value>自動幅</value>
  </data>
  <data name="FormFields_WidthHalf" xml:space="preserve">
    <value>半分幅</value>
  </data>
  <data name="FormFields_WidthThird" xml:space="preserve">
    <value>三分の一幅</value>
  </data>
  <data name="FormFields_Required" xml:space="preserve">
    <value>必須</value>
  </data>
  <data name="FormFields_SelectOptions" xml:space="preserve">
    <value>選択オプション</value>
  </data>
  <data name="FormFields_SelectOptionsHelpText" xml:space="preserve">
    <value>ドロップダウン選択フィールドのオプションを設定</value>
  </data>
  <data name="FormFields_OptionValue" xml:space="preserve">
    <value>オプション値</value>
  </data>
  <data name="FormFields_OptionLabel" xml:space="preserve">
    <value>オプションラベル</value>
  </data>
  <data name="FormFields_ShowSearchButton" xml:space="preserve">
    <value>検索ボタンを表示</value>
  </data>
  <data name="FormFields_ShowResetButton" xml:space="preserve">
    <value>リセットボタンを表示</value>
  </data>
  <data name="FormFields_SearchButtonText" xml:space="preserve">
    <value>検索ボタンテキスト</value>
  </data>
  <data name="FormFields_ResetButtonText" xml:space="preserve">
    <value>リセットボタンテキスト</value>
  </data>
  <data name="FormFields_SearchAction" xml:space="preserve">
    <value>検索アクション</value>
  </data>
  <data name="FormFields_SearchActionHelpText" xml:space="preserve">
    <value>検索フォーム送信のターゲットURL</value>
  </data>
  <data name="FormFields_SearchMethod" xml:space="preserve">
    <value>検索メソッド</value>
  </data>

  <!-- NewsList Component Fields -->
  <data name="FormFields_DateFormat" xml:space="preserve">
    <value>日付形式</value>
  </data>

</root>