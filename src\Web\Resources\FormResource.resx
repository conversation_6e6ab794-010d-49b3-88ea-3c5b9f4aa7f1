﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- 按钮相关字段 -->
  <data name="PrimaryButtonText" xml:space="preserve">
    <value>主按钮文字</value>
  </data>
  <data name="PrimaryButtonLink" xml:space="preserve">
    <value>主按钮链接</value>
  </data>
  <data name="SecondaryButtonText" xml:space="preserve">
    <value>次按钮文字</value>
  </data>
  <data name="SecondaryButtonLink" xml:space="preserve">
    <value>次按钮链接</value>
  </data>

  <!-- 布局设置字段 -->
  <data name="TextAlignment" xml:space="preserve">
    <value>文本对齐</value>
  </data>
  <data name="SectionHeight" xml:space="preserve">
    <value>区域高度</value>
  </data>
  <data name="OverlayOpacity" xml:space="preserve">
    <value>遮罩透明度</value>
  </data>
  <data name="ShowOverlay" xml:space="preserve">
    <value>显示遮罩层</value>
  </data>
  <data name="EnableAnimation" xml:space="preserve">
    <value>启用动画效果</value>
  </data>
  <data name="EnableParallax" xml:space="preserve">
    <value>启用视差滚动效果</value>
  </data>
  <data name="ShowScrollIndicator" xml:space="preserve">
    <value>显示滚动指示器</value>
  </data>

  <!-- 对齐选项 -->
  <data name="AlignLeft" xml:space="preserve">
    <value>左对齐</value>
  </data>
  <data name="AlignCenter" xml:space="preserve">
    <value>居中</value>
  </data>
  <data name="AlignRight" xml:space="preserve">
    <value>右对齐</value>
  </data>

  <data name="SectionSize" xml:space="preserve">
    <value>区域尺寸</value>
  </data>
  <!-- 高度选项 -->
  <data name="AutoHeight" xml:space="preserve">
    <value>自动高度</value>
  </data>
  <data name="FullScreenHeight" xml:space="preserve">
    <value>全屏高度</value>
  </data>
    <data name="LargeSize" xml:space="preserve">
    <value>大尺寸</value>
  </data>
  <data name="SmallSize" xml:space="preserve">
    <value>小尺寸</value>
  </data>
  <data name="MediumSize" xml:space="preserve">
    <value>中等尺寸</value>
  </data>

  <!-- 帮助文本 -->
  <data name="PrimaryButtonTextHelpText" xml:space="preserve">
    <value>主要操作按钮的显示文字</value>
  </data>
  <data name="PrimaryButtonLinkHelpText" xml:space="preserve">
    <value>主按钮点击后跳转的URL地址</value>
  </data>
  <data name="SecondaryButtonTextHelpText" xml:space="preserve">
    <value>次要操作按钮的显示文字</value>
  </data>
  <data name="SecondaryButtonLinkHelpText" xml:space="preserve">
    <value>次按钮点击后跳转的URL地址</value>
  </data>
  <data name="TextAlignmentHelpText" xml:space="preserve">
    <value>文本内容的对齐方式</value>
  </data>
  <data name="SectionHeightHelpText" xml:space="preserve">
    <value>主视觉区域的高度设置</value>
  </data>
  <data name="OverlayOpacityHelpText" xml:space="preserve">
    <value>遮罩层的透明度 (0-100)</value>
  </data>
  <data name="ShowOverlayHelpText" xml:space="preserve">
    <value>在背景图片/视频上显示半透明遮罩层</value>
  </data>
  <data name="EnableAnimationHelpText" xml:space="preserve">
    <value>启用文字渐入和按钮悬停动画</value>
  </data>
  <data name="EnableParallaxHelpText" xml:space="preserve">
    <value>启用视差滚动效果</value>
  </data>
  <data name="ShowScrollIndicatorHelpText" xml:space="preserve">
    <value>在底部显示向下滚动的指示器箭头</value>
  </data>
  <data name="BackgroundVideoHelpText" xml:space="preserve">
    <value>可选的背景视频，将覆盖背景图片</value>
  </data>

  <!-- Header 组件字段 -->
  <data name="FormFields_CompanyName" xml:space="preserve">
    <value>公司名称</value>
  </data>
  <data name="FormFields_CompanyNameHelpText" xml:space="preserve">
    <value>在页头显示的公司名称</value>
  </data>
  <data name="FormFields_Address" xml:space="preserve">
    <value>地址</value>
  </data>
  <data name="FormFields_AddressHelpText" xml:space="preserve">
    <value>在页面显示的公司地址</value>
  </data>
  <data name="FormFields_Sort" xml:space="preserve">
    <value>排序号</value>
  </data>
  <data name="FormFields_SortHelpText" xml:space="preserve">
    <value>菜单项的排序号</value>
  </data>
  <data name="FormFields_ContactInfo" xml:space="preserve">
    <value>联系信息</value>
  </data>
  <data name="FormFields_ContactInfoHelpText" xml:space="preserve">
    <value>在页头/页脚显示的联系方式信息</value>
  </data>
  <data name="FormFields_Phone" xml:space="preserve">
    <value>电话号码</value>
  </data>
  <data name="FormFields_PhoneHelpText" xml:space="preserve">
    <value>公司的联系电话号码</value>
  </data>
  <data name="FormFields_Email" xml:space="preserve">
    <value>邮箱地址</value>
  </data>
  <data name="FormFields_EmailHelpText" xml:space="preserve">
    <value>公司的联系邮箱地址</value>
  </data>
  <data name="FormFields_MenuItems" xml:space="preserve">
    <value>导航菜单项</value>
  </data>
  <data name="FormFields_MenuItemsHelpText" xml:space="preserve">
    <value>页头导航菜单的各个项目</value>
  </data>
  <data name="FormFields_MenuItemText" xml:space="preserve">
    <value>菜单项文字</value>
  </data>
  <data name="FormFields_MenuItemTextHelpText" xml:space="preserve">
    <value>导航菜单项显示的文字</value>
  </data>
  <data name="FormFields_MenuItemUrl" xml:space="preserve">
    <value>菜单项链接</value>
  </data>
  <data name="FormFields_MenuItemUrlHelpText" xml:space="preserve">
    <value>点击菜单项后跳转的URL地址</value>
  </data>
  <data name="FormFields_IsActive" xml:space="preserve">
    <value>是否激活</value>
  </data>
  <data name="FormFields_IsActiveHelpText" xml:space="preserve">
    <value>该菜单项是否为当前激活状态</value>
  </data>
  <data name="FormFields_SubMenuItems" xml:space="preserve">
    <value>子菜单项</value>
  </data>
  <data name="FormFields_SubMenuItemsHelpText" xml:space="preserve">
    <value>当前菜单项的下拉子菜单</value>
  </data>
  <data name="FormFields_SubMenuItemText" xml:space="preserve">
    <value>子菜单项文字</value>
  </data>
  <data name="FormFields_SubMenuItemTextHelpText" xml:space="preserve">
    <value>子菜单项显示的文字</value>
  </data>
  <data name="FormFields_SubMenuItemUrl" xml:space="preserve">
    <value>子菜单项链接</value>
  </data>
  <data name="FormFields_SubMenuItemUrlHelpText" xml:space="preserve">
    <value>点击子菜单项后跳转的URL地址</value>
  </data>
  <data name="FormFields_MobileMenuButtonText" xml:space="preserve">
    <value>移动端菜单按钮文字</value>
  </data>
  <data name="FormFields_MobileMenuButtonTextHelpText" xml:space="preserve">
    <value>移动端设备上菜单按钮显示的文字</value>
  </data>
  <data name="FormFields_ShowCompanyName" xml:space="preserve">
    <value>显示公司名称</value>
  </data>
  <data name="FormFields_ShowCompanyNameHelpText" xml:space="preserve">
    <value>在页头显示公司名称</value>
  </data>
  <data name="FormFields_ShowLanguageSelector" xml:space="preserve">
    <value>显示语言选择器</value>
  </data>
  <data name="FormFields_ShowLanguageSelectorHelpText" xml:space="preserve">
    <value>在页头显示多语言切换选择器</value>
  </data>
  <data name="FormFields_ShowDarkModeToggle" xml:space="preserve">
    <value>显示深色模式开关</value>
  </data>
  <data name="FormFields_ShowDarkModeToggleHelpText" xml:space="preserve">
    <value>在页头显示深色/浅色主题切换开关</value>
  </data>
  <data name="FormFields_ShowSearchBox" xml:space="preserve">
    <value>显示搜索框</value>
  </data>
  <data name="FormFields_ShowSearchBoxHelpText" xml:space="preserve">
    <value>在页头显示搜索输入框</value>
  </data>
  <data name="FormFields_BackgroundColor" xml:space="preserve">
    <value>背景颜色</value>
  </data>
  <data name="FormFields_BackgroundColorHelpText" xml:space="preserve">
    <value>背景颜色设置</value>
  </data>

  <data name="FormFields_BackgroundColor_White" xml:space="preserve">
    <value>白色</value>
  </data>
  <data name="FormFields_BackgroundColor_Muted" xml:space="preserve">
    <value>柔和</value>
  </data>
  <data name="FormFields_ColumnsDesktop" xml:space="preserve">
    <value>桌面端列数</value>
  </data>
  <data name="FormFields_ColumnsTablet" xml:space="preserve">
    <value>平板端列数</value>
  </data>

  <data name="FormFields_MaxItems" xml:space="preserve">
    <value>最大项目数</value>
  </data>
  <data name="FormFields_ShowViewAllButton" xml:space="preserve">
    <value>显示"查看更多"按钮</value>
  </data>
  <data name="FormFields_ShowCategories" xml:space="preserve">
    <value>显示分类</value>
  </data>
  <data name="FormFields_ShowDates" xml:space="preserve">
    <value>显示日期</value>
  </data>
  <data name="FormFields_ShowExcerpts" xml:space="preserve">
    <value>显示摘要</value>
  </data>
  <data name="FormFields_ViewAllButtonText" xml:space="preserve">
    <value>"查看更多"按钮文字</value>
  </data>
  <data name="FormFields_ViewAllButtonUrl" xml:space="preserve">
    <value>"查看更多"按钮链接</value>
  </data>

  <data name="FormFields_TextColor" xml:space="preserve">
    <value>文字颜色</value>
  </data>
  <data name="FormFields_TextColorHelpText" xml:space="preserve">
    <value>文字颜色设置</value>
  </data>

  <data name="FormFields_RecruitmentSection_Categories" xml:space="preserve">
    <value>招聘信息分类</value>
  </data>

  <!-- Footer 组件字段 -->
  <data name="FormFields_CompanyDescription" xml:space="preserve">
    <value>公司标语(Slogan)</value>
  </data>
  <data name="FormFields_CompanyDescriptionHelpText" xml:space="preserve">
    <value>在页脚显示的公司标语文字</value>
  </data>
  <data name="FormFields_Copyright" xml:space="preserve">
    <value>版权信息</value>
  </data>
  <data name="FormFields_CopyrightHelpText" xml:space="preserve">
    <value>在页脚显示的版权声明文字</value>
  </data>
  <data name="FormFields_FooterSections" xml:space="preserve">
    <value>页脚分栏</value>
  </data>
  <data name="FormFields_FooterSectionsHelpText" xml:space="preserve">
    <value>页脚的各个分栏区块</value>
  </data>
  <data name="FormFields_SectionTitle" xml:space="preserve">
    <value>分栏标题</value>
  </data>
  <data name="FormFields_SectionTitleHelpText" xml:space="preserve">
    <value>页脚分栏的标题文字</value>
  </data>
  <data name="FormFields_SectionLinks" xml:space="preserve">
    <value>分栏链接</value>
  </data>
  <data name="FormFields_SectionLinksHelpText" xml:space="preserve">
    <value>该分栏下的各个链接项目</value>
  </data>
  <data name="FormFields_LinkText" xml:space="preserve">
    <value>链接文字</value>
  </data>
  <data name="FormFields_LinkTextHelpText" xml:space="preserve">
    <value>链接显示的文字</value>
  </data>
  <data name="FormFields_LinkUrl" xml:space="preserve">
    <value>链接地址</value>
  </data>
  <data name="FormFields_LinkUrlHelpText" xml:space="preserve">
    <value>点击链接后跳转的URL地址</value>
  </data>
  <data name="FormFields_OpenInNewTab" xml:space="preserve">
    <value>在新标签页打开</value>
  </data>
  <data name="FormFields_OpenInNewTabHelpText" xml:space="preserve">
    <value>点击链接时是否在新的浏览器标签页中打开</value>
  </data>
  <data name="FormFields_SocialLinks" xml:space="preserve">
    <value>社交媒体链接</value>
  </data>
  <data name="FormFields_SocialLinksHelpText" xml:space="preserve">
    <value>在页脚显示的社交媒体平台链接</value>
  </data>
  <data name="FormFields_SocialPlatform" xml:space="preserve">
    <value>社交平台</value>
  </data>
  <data name="FormFields_SocialPlatformHelpText" xml:space="preserve">
    <value>选择社交媒体平台类型</value>
  </data>
  <data name="FormFields_SocialUrl" xml:space="preserve">
    <value>社交媒体链接</value>
  </data>
  <data name="FormFields_SocialUrlHelpText" xml:space="preserve">
    <value>社交媒体平台的链接地址</value>
  </data>
  <data name="FormFields_SocialIcon" xml:space="preserve">
    <value>社交媒体图标</value>
  </data>
  <data name="FormFields_SocialIconHelpText" xml:space="preserve">
    <value>社交媒体平台的图标类名或图片</value>
  </data>
  <data name="FormFields_ShowAdminLinks" xml:space="preserve">
    <value>显示管理链接</value>
  </data>
  <data name="FormFields_ShowAdminLinksHelpText" xml:space="preserve">
    <value>在页脚显示管理员相关的链接</value>
  </data>
  <data name="FormFields_BorderColor" xml:space="preserve">
    <value>边框颜色</value>
  </data>
  <data name="FormFields_BorderColorHelpText" xml:space="preserve">
    <value>边框颜色设置</value>
  </data>
  <data name="FormFields_LinkHoverColor" xml:space="preserve">
    <value>链接悬停颜色</value>
  </data>
  <data name="FormFields_LinkHoverColorHelpText" xml:space="preserve">
    <value>鼠标悬停在链接上时的文字颜色</value>
  </data>
  <data name="FormFields_PleaseSelect" xml:space="preserve">
    <value>请选择</value>
  </data>
  <data name="FormFields_Other" xml:space="preserve">
    <value>其他</value>
  </data>

  <!-- Content组件字段 -->
  <data name="FormFields_Content" xml:space="preserve">
    <value>内容</value>
  </data>
  <data name="FormFields_ContentHelpText" xml:space="preserve">
    <value>输入主要内容，支持多语言富文本</value>
  </data>
  <data name="FormFields_ContentBlocks" xml:space="preserve">
    <value>内容块</value>
  </data>
  <data name="FormFields_ContentBlocksHelpText" xml:space="preserve">
    <value>添加多个内容块，支持图片、图标和链接</value>
  </data>
  <data name="FormFields_BlockTitle" xml:space="preserve">
    <value>块标题</value>
  </data>
  <data name="FormFields_BlockContent" xml:space="preserve">
    <value>块内容</value>
  </data>
  <data name="FormFields_Image" xml:space="preserve">
    <value>图片</value>
  </data>
  <data name="FormFields_Icon" xml:space="preserve">
    <value>图标</value>
  </data>
  <data name="FormFields_Link" xml:space="preserve">
    <value>链接</value>
  </data>
  <data name="FormFields_Layout" xml:space="preserve">
    <value>布局</value>
  </data>
  <data name="FormFields_LayoutHelpText" xml:space="preserve">
    <value>选择内容块的显示布局方式</value>
  </data>
  <data name="FormFields_GridLayout" xml:space="preserve">
    <value>网格布局</value>
  </data>
  <data name="FormFields_ListLayout" xml:space="preserve">
    <value>列表布局</value>
  </data>
  <data name="FormFields_HorizontalLayout" xml:space="preserve">
    <value>水平布局</value>
  </data>
  <data name="FormFields_VerticalLayout" xml:space="preserve">
    <value>垂直布局</value>
  </data>

  <data name="FormFields_ShowDivider" xml:space="preserve">
    <value>显示分割线</value>
  </data>
  <data name="FormFields_ShowDividerHelpText" xml:space="preserve">
    <value>在内容块之间显示分割线</value>
  </data>
  <data name="FormFields_AnimationEnabled" xml:space="preserve">
    <value>启用动画</value>
  </data>
  <data name="FormFields_AnimationEnabledHelpText" xml:space="preserve">
    <value>启用滚动时的淡入动画效果</value>
  </data>
  <data name="FormFields_AnimationType" xml:space="preserve">
    <value>动画类型</value>
  </data>
  <data name="FormFields_AnimationTypeHelpText" xml:space="preserve">
    <value>选择内容块的动画类型</value>
  </data>

  <!-- Content组件分组 -->
  <data name="FormGroups_ContentBlocks" xml:space="preserve">
    <value>内容块</value>
  </data>

  <!-- 表单分组 -->
  <data name="FormGroups_ContactInfo" xml:space="preserve">
    <value>联系信息</value>
  </data>
  <data name="FormGroups_Navigation" xml:space="preserve">
    <value>导航设置</value>
  </data>
  <data name="FormGroups_MobileSettings" xml:space="preserve">
    <value>移动端设置</value>
  </data>
  <data name="FormGroups_CompanyInfo" xml:space="preserve">
    <value>公司信息</value>
  </data>
  <data name="FormGroups_SocialMedia" xml:space="preserve">
    <value>社交媒体</value>
  </data>

  <!-- Message组件相关 -->
  <data name="ShowTitle" xml:space="preserve">
    <value>显示标题</value>
  </data>
  <data name="ShowTitleHelpText" xml:space="preserve">
    <value>是否在组件顶部显示标题</value>
  </data>
  <data name="ShowSubtitle" xml:space="preserve">
    <value>显示副标题</value>
  </data>
  <data name="ShowSubtitleHelpText" xml:space="preserve">
    <value>是否在标题下方显示副标题</value>
  </data>
  <data name="ShowDescription" xml:space="preserve">
    <value>显示描述</value>
  </data>
  <data name="ShowDescriptionHelpText" xml:space="preserve">
    <value>是否显示表单描述文本</value>
  </data>
  <data name="FormWidth" xml:space="preserve">
    <value>表单宽度</value>
  </data>
  <data name="FormWidthHelpText" xml:space="preserve">
    <value>选择表单容器的宽度</value>
  </data>
  <data name="SmallWidth" xml:space="preserve">
    <value>小尺寸</value>
  </data>
  <data name="MediumWidth" xml:space="preserve">
    <value>中等尺寸</value>
  </data>
  <data name="LargeWidth" xml:space="preserve">
    <value>大尺寸</value>
  </data>
  <data name="FullWidth" xml:space="preserve">
    <value>全宽</value>
  </data>
  <data name="SubmitButtonText" xml:space="preserve">
    <value>提交按钮文字</value>
  </data>
  <data name="SubmitButtonTextHelpText" xml:space="preserve">
    <value>表单提交按钮显示的文字</value>
  </data>
  <data name="RequiredFieldsNote" xml:space="preserve">
    <value>必填项提示</value>
  </data>
  <data name="RequiredFieldsNoteHelpText" xml:space="preserve">
    <value>表单必填项的提示文字</value>
  </data>
  <data name="PrivacyNotice" xml:space="preserve">
    <value>隐私声明</value>
  </data>
  <data name="PrivacyNoticeHelpText" xml:space="preserve">
    <value>表单底部的隐私政策提示文字</value>
  </data>

  <!-- Carousel 组件相关资源 -->
  <data name="FormFields_CarouselItems" xml:space="preserve">
    <value>轮播项目</value>
  </data>
  <data name="FormFields_CarouselItemsHelpText" xml:space="preserve">
    <value>添加轮播图片和内容项目</value>
  </data>
  <data name="FormFields_GalleryItems" xml:space="preserve">
    <value>图库项目</value>
  </data>
  <data name="FormFields_GalleryItemsHelpText" xml:space="preserve">
    <value>添加图库展示项目</value>
  </data>
  <data name="FormFields_SliderItems" xml:space="preserve">
    <value>滑块项目</value>
  </data>
  <data name="FormFields_SliderItemsHelpText" xml:space="preserve">
    <value>添加紧凑滑块项目</value>
  </data>
  <data name="FormFields_AutoPlay" xml:space="preserve">
    <value>自动播放</value>
  </data>
  <data name="FormFields_AutoPlayHelpText" xml:space="preserve">
    <value>启用自动轮播功能</value>
  </data>
  <data name="FormFields_AutoPlayInterval" xml:space="preserve">
    <value>自动播放间隔</value>
  </data>
  <data name="FormFields_AutoPlayIntervalHelpText" xml:space="preserve">
    <value>自动播放的时间间隔（毫秒）</value>
  </data>
  <data name="FormFields_ShowIndicators" xml:space="preserve">
    <value>显示指示器</value>
  </data>
  <data name="FormFields_ShowNavigation" xml:space="preserve">
    <value>显示导航按钮</value>
  </data>
  <data name="FormFields_PauseOnHover" xml:space="preserve">
    <value>悬停时暂停</value>
  </data>
  <data name="FormFields_InfiniteLoop" xml:space="preserve">
    <value>无限循环</value>
  </data>
  <data name="FormFields_TransitionEffect" xml:space="preserve">
    <value>过渡效果</value>
  </data>
  <data name="FormFields_TransitionDuration" xml:space="preserve">
    <value>过渡时长</value>
  </data>
  <data name="FormFields_TransitionDurationHelpText" xml:space="preserve">
    <value>过渡动画的持续时间（毫秒）</value>
  </data>
  <data name="FormFields_Height" xml:space="preserve">
    <value>高度</value>
  </data>
  <data name="FormFields_CustomHeight" xml:space="preserve">
    <value>自定义高度</value>
  </data>
  <data name="FormFields_CustomHeightHelpText" xml:space="preserve">
    <value>输入自定义高度值，如：400px, 50vh</value>
  </data>
  <data name="FormFields_ShowCaptions" xml:space="preserve">
    <value>显示标题</value>
  </data>
  <data name="FormFields_CaptionPosition" xml:space="preserve">
    <value>标题位置</value>
  </data>
  <data name="FormFields_CarouselType" xml:space="preserve">
    <value>轮播类型</value>
  </data>
  <data name="FormFields_ActionButtons" xml:space="preserve">
    <value>操作按钮</value>
  </data>
  <data name="FormFields_ButtonText" xml:space="preserve">
    <value>按钮文字</value>
  </data>
  <data name="FormFields_ButtonUrl" xml:space="preserve">
    <value>按钮链接</value>
  </data>
  <data name="FormFields_ButtonStyle" xml:space="preserve">
    <value>按钮样式</value>
  </data>
  <data name="FormFields_AltText" xml:space="preserve">
    <value>替代文字</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>副标题</value>
  </data>

  <!-- 表单分组 -->
  <data name="FormGroups_Content" xml:space="preserve">
    <value>内容设置</value>
  </data>
  <data name="FormGroups_Settings" xml:space="preserve">
    <value>功能设置</value>
  </data>
  <data name="FormGroups_Appearance" xml:space="preserve">
    <value>外观设置</value>
  </data>
  <data name="FormGroups_Statistics" xml:space="preserve">
    <value>统计设置</value>
  </data>
  <data name="CompanyOverview_Stats" xml:space="preserve">
    <value>统计设置</value>
  </data>
  <data name="StatItem_Label" xml:space="preserve">
    <value>统计项目</value>
  </data>
  <data name="StatItem_Value" xml:space="preserve">
    <value>统计值</value>
  </data>
  <data name="StatItem_Icon" xml:space="preserve">
    <value>统计图标</value>
  </data>

  <!-- 选项值 -->
  <data name="TransitionEffect_Slide" xml:space="preserve">
    <value>滑动</value>
  </data>
  <data name="TransitionEffect_Fade" xml:space="preserve">
    <value>淡入淡出</value>
  </data>
  <data name="Height_Auto" xml:space="preserve">
    <value>自动</value>
  </data>
  <data name="Height_Small" xml:space="preserve">
    <value>小</value>
  </data>
  <data name="Height_Medium" xml:space="preserve">
    <value>中</value>
  </data>
  <data name="Height_Large" xml:space="preserve">
    <value>大</value>
  </data>
  <data name="Height_Full" xml:space="preserve">
    <value>全屏</value>
  </data>
  <data name="Height_Custom" xml:space="preserve">
    <value>自定义</value>
  </data>
  <data name="CaptionPosition_Bottom" xml:space="preserve">
    <value>底部</value>
  </data>
  <data name="CaptionPosition_Top" xml:space="preserve">
    <value>顶部</value>
  </data>
  <data name="CaptionPosition_Center" xml:space="preserve">
    <value>居中</value>
  </data>
  <data name="CaptionPosition_Overlay" xml:space="preserve">
    <value>覆盖</value>
  </data>
  <data name="CarouselType_Banner" xml:space="preserve">
    <value>横幅轮播</value>
  </data>
  <data name="CarouselType_Gallery" xml:space="preserve">
    <value>图库展示</value>
  </data>
  <data name="CarouselType_Slider" xml:space="preserve">
    <value>紧凑滑块</value>
  </data>
  <data name="ButtonStyle_Primary" xml:space="preserve">
    <value>主要</value>
  </data>
  <data name="ButtonStyle_Secondary" xml:space="preserve">
    <value>次要</value>
  </data>
  <data name="ButtonStyle_Outline" xml:space="preserve">
    <value>轮廓</value>
  </data>
  <data name="ButtonStyle_Ghost" xml:space="preserve">
    <value>幽灵</value>
  </data>




  <!-- Breadcrumb 组件相关资源 -->
  <data name="BreadcrumbHomeLinkText" xml:space="preserve">
    <value>首页链接文字</value>
  </data>
  <data name="BreadcrumbHomeLinkTextHelpText" xml:space="preserve">
    <value>面包屑导航中首页链接显示的文字</value>
  </data>
  <data name="BreadcrumbHomeUrl" xml:space="preserve">
    <value>首页链接地址</value>
  </data>
  <data name="BreadcrumbHomeUrlHelpText" xml:space="preserve">
    <value>点击首页链接后跳转的URL地址</value>
  </data>
  <data name="BreadcrumbAriaLabel" xml:space="preserve">
    <value>无障碍标签</value>
  </data>
  <data name="BreadcrumbAriaLabelHelpText" xml:space="preserve">
    <value>面包屑导航的无障碍访问标签</value>
  </data>
  <data name="BreadcrumbShowHomeIcon" xml:space="preserve">
    <value>显示首页图标</value>
  </data>
  <data name="BreadcrumbShowHomeIconHelpText" xml:space="preserve">
    <value>在首页链接前显示房屋图标</value>
  </data>
  <data name="BreadcrumbRoundedCorners" xml:space="preserve">
    <value>圆角边框</value>
  </data>
  <data name="BreadcrumbRoundedCornersHelpText" xml:space="preserve">
    <value>为面包屑导航容器添加圆角边框</value>
  </data>
  <data name="BreadcrumbBackgroundStyle" xml:space="preserve">
    <value>背景样式</value>
  </data>
  <data name="BreadcrumbBackgroundStyleHelpText" xml:space="preserve">
    <value>选择面包屑导航的背景样式</value>
  </data>
  <data name="BreadcrumbBackgroundStyleLight" xml:space="preserve">
    <value>浅色背景</value>
  </data>
  <data name="BreadcrumbBackgroundStyleDark" xml:space="preserve">
    <value>深色背景</value>
  </data>
  <data name="BreadcrumbBackgroundStyleTransparent" xml:space="preserve">
    <value>透明背景</value>
  </data>
  <data name="BreadcrumbItems" xml:space="preserve">
    <value>面包屑项目</value>
  </data>
  <data name="BreadcrumbItemsHelpText" xml:space="preserve">
    <value>添加面包屑导航的中间层级项目(首页和当前页面不需要添加)</value>
  </data>
  <data name="BreadcrumbItemText" xml:space="preserve">
    <value>项目文字</value>
  </data>
  <data name="BreadcrumbItemUrl" xml:space="preserve">
    <value>项目链接</value>
  </data>

  <!-- Cookie Policy Form Fields -->
  <data name="FormFields_CookiePolicyTitleHelpText" xml:space="preserve">
    <value>Cookie政策横幅的标题，可选填写</value>
  </data>
  <data name="FormFields_CookiePolicyMessageHelpText" xml:space="preserve">
    <value>Cookie政策的说明文字，支持多语言</value>
  </data>
  <data name="FormFields_AcceptButtonText" xml:space="preserve">
    <value>接受按钮文字</value>
  </data>
  <data name="FormFields_AcceptButtonTextHelpText" xml:space="preserve">
    <value>用户接受Cookie政策时显示的按钮文字</value>
  </data>
  <data name="FormFields_DeclineButtonText" xml:space="preserve">
    <value>拒绝按钮文字</value>
  </data>
  <data name="FormFields_DeclineButtonTextHelpText" xml:space="preserve">
    <value>用户拒绝Cookie政策时显示的按钮文字</value>
  </data>
  <data name="FormFields_LearnMoreText" xml:space="preserve">
    <value>了解更多链接文字</value>
  </data>
  <data name="FormFields_LearnMoreTextHelpText" xml:space="preserve">
    <value>链接到详细Cookie政策页面的文字</value>
  </data>
  <data name="FormFields_LearnMoreUrl" xml:space="preserve">
    <value>了解更多链接地址</value>
  </data>
  <data name="FormFields_LearnMoreUrlHelpText" xml:space="preserve">
    <value>详细Cookie政策页面的URL地址</value>
  </data>
  <data name="FormFields_Position" xml:space="preserve">
    <value>显示位置</value>
  </data>
  <data name="FormFields_PositionHelpText" xml:space="preserve">
    <value>Cookie横幅在页面中的显示位置</value>
  </data>
  <data name="FormFields_PositionBottom" xml:space="preserve">
    <value>底部</value>
  </data>
  <data name="FormFields_PositionTop" xml:space="preserve">
    <value>顶部</value>
  </data>
  <data name="FormFields_BackgroundColor" xml:space="preserve">
    <value>背景颜色</value>
  </data>
  <data name="FormFields_BackgroundColorHelpText" xml:space="preserve">
    <value>Cookie横幅的背景颜色主题</value>
  </data>
  <data name="FormFields_BackgroundColorDark" xml:space="preserve">
    <value>深色</value>
  </data>
  <data name="FormFields_BackgroundColorLight" xml:space="preserve">
    <value>浅色</value>
  </data>
  <data name="FormFields_ShowDeclineButton" xml:space="preserve">
    <value>显示拒绝按钮</value>
  </data>
  <data name="FormFields_ShowDeclineButtonHelpText" xml:space="preserve">
    <value>是否显示拒绝Cookie的按钮</value>
  </data>
  <data name="FormFields_ShowLearnMoreLink" xml:space="preserve">
    <value>显示了解更多链接</value>
  </data>
  <data name="FormFields_ShowLearnMoreLinkHelpText" xml:space="preserve">
    <value>是否显示链接到详细Cookie政策的链接</value>
  </data>
  <data name="FormFields_AutoHide" xml:space="preserve">
    <value>自动隐藏</value>
  </data>
  <data name="FormFields_AutoHideHelpText" xml:space="preserve">
    <value>是否在指定时间后自动接受并隐藏横幅</value>
  </data>
  <data name="FormFields_AutoHideDelay" xml:space="preserve">
    <value>自动隐藏延迟（毫秒）</value>
  </data>
  <data name="FormFields_AutoHideDelayHelpText" xml:space="preserve">
    <value>自动隐藏前的等待时间，单位为毫秒</value>
  </data>

  <!-- Form Groups -->
  <data name="FormGroups_AdvancedSettings" xml:space="preserve">
    <value>高级设置</value>
  </data>
  <data name="FormFields_ShowShadow" xml:space="preserve">
    <value>显示阴影</value>
  </data>
  <data name="FormFields_ShowShadowHelpText" xml:space="preserve">
    <value>是否显示阴影</value>
  </data>

  <!-- CTA组件专用资源 -->
  <!-- 背景颜色选项 -->
  <data name="FormFields_BackgroundColor_Primary" xml:space="preserve">
    <value>主色调</value>
  </data>
  <data name="FormFields_BackgroundColor_Secondary" xml:space="preserve">
    <value>次色调</value>
  </data>
  <data name="FormFields_BackgroundColor_Accent" xml:space="preserve">
    <value>强调色</value>
  </data>
  <data name="FormFields_BackgroundColor_Gray" xml:space="preserve">
    <value>灰色</value>
  </data>
  <data name="FormFields_BackgroundColor_Transparent" xml:space="preserve">
    <value>透明</value>
  </data>

  <!-- 文字颜色选项 -->
  <data name="WhiteText" xml:space="preserve">
    <value>白色文字</value>
  </data>
  <data name="BlackText" xml:space="preserve">
    <value>黑色文字</value>
  </data>
  <data name="PrimaryText" xml:space="preserve">
    <value>主色调文字</value>
  </data>
  <data name="GrayText" xml:space="preserve">
    <value>灰色文字</value>
  </data>

  <!-- 间距选项 -->
  <data name="TightSpacing" xml:space="preserve">
    <value>紧凑间距</value>
  </data>
  <data name="NormalSpacing" xml:space="preserve">
    <value>正常间距</value>
  </data>
  <data name="LooseSpacing" xml:space="preserve">
    <value>宽松间距</value>
  </data>

  <!-- 边框圆角选项 -->
  <data name="NoBorderRadius" xml:space="preserve">
    <value>无圆角</value>
  </data>
  <data name="SmallBorderRadius" xml:space="preserve">
    <value>小圆角</value>
  </data>
  <data name="NormalBorderRadius" xml:space="preserve">
    <value>正常圆角</value>
  </data>
  <data name="LargeBorderRadius" xml:space="preserve">
    <value>大圆角</value>
  </data>
  <data name="FullBorderRadius" xml:space="preserve">
    <value>完全圆角</value>
  </data>

  <!-- 动画效果选项 -->
  <data name="FadeInAnimation" xml:space="preserve">
    <value>淡入动画</value>
  </data>
  <data name="SlideInLeftAnimation" xml:space="preserve">
    <value>左滑入动画</value>
  </data>
  <data name="SlideInRightAnimation" xml:space="preserve">
    <value>右滑入动画</value>
  </data>
  <data name="ScaleInAnimation" xml:space="preserve">
    <value>缩放动画</value>
  </data>

  <!-- FAQ 组件字段 -->
  <data name="FormFields_FAQItems" xml:space="preserve">
    <value>常见问题列表</value>
  </data>
  <data name="FormFields_FAQItemsHelpText" xml:space="preserve">
    <value>添加和管理常见问题及其答案</value>
  </data>
  <data name="FormFields_Question" xml:space="preserve">
    <value>问题</value>
  </data>
  <data name="FormFields_Answer" xml:space="preserve">
    <value>答案</value>
  </data>
  <data name="FormFields_Category" xml:space="preserve">
    <value>分类</value>
  </data>
  <data name="FormFields_Order" xml:space="preserve">
    <value>排序</value>
  </data>
  <data name="FormFields_IsPopular" xml:space="preserve">
    <value>热门问题</value>
  </data>
  <data name="FormFields_ShowSearch" xml:space="preserve">
    <value>显示搜索框</value>
  </data>
  <data name="FormFields_ShowSearchHelpText" xml:space="preserve">
    <value>允许用户搜索常见问题</value>
  </data>
  <data name="FormFields_ShowCategoriesHelpText" xml:space="preserve">
    <value>显示分类筛选按钮</value>
  </data>
  <data name="FormFields_FAQLayoutHelpText" xml:space="preserve">
    <value>选择FAQ的显示布局方式</value>
  </data>
  <data name="FormFields_AccordionLayout" xml:space="preserve">
    <value>手风琴布局</value>
  </data>
  <data name="FormFields_ListLayout" xml:space="preserve">
    <value>列表布局</value>
  </data>
  <data name="FormFields_AllowMultipleOpen" xml:space="preserve">
    <value>允许同时展开多个</value>
  </data>
  <data name="FormFields_AllowMultipleOpenHelpText" xml:space="preserve">
    <value>在手风琴模式下允许同时展开多个问题</value>
  </data>
  <data name="FormFields_AccentColor" xml:space="preserve">
    <value>强调色</value>
  </data>
  <data name="FormFields_AccentColorHelpText" xml:space="preserve">
    <value>选择组件的主要强调色</value>
  </data>
  <data name="FormFields_PrimaryColor" xml:space="preserve">
    <value>主色调</value>
  </data>
  <data name="FormFields_SecondaryColor" xml:space="preserve">
    <value>次色调</value>
  </data>
  <data name="FormFields_SuccessColor" xml:space="preserve">
    <value>成功色</value>
  </data>
  <data name="FormFields_InfoColor" xml:space="preserve">
    <value>信息色</value>
  </data>

  <!-- FAQ 组件分组 -->
  <data name="FormGroups_FAQContent" xml:space="preserve">
    <value>FAQ内容</value>
  </data>

  <!-- 基础表单字段 -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>标题</value>
  </data>
  <data name="FormFields_TitleHelpText" xml:space="preserve">
    <value>组件的主标题</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>副标题</value>
  </data>
  <data name="FormFields_SubtitleHelpText" xml:space="preserve">
    <value>组件的副标题或说明文字</value>
  </data>
  <data name="FormFields_Description" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="FormFields_DescriptionHelpText" xml:space="preserve">
    <value>组件的详细描述信息</value>
  </data>
  <data name="FormFields_Content" xml:space="preserve">
    <value>内容</value>
  </data>
  <data name="FormFields_ContentHelpText" xml:space="preserve">
    <value>组件的主要内容</value>
  </data>
  <data name="FormFields_AnimationEnabled" xml:space="preserve">
    <value>启用动画</value>
  </data>
  <data name="FormFields_AnimationEnabledHelpText" xml:space="preserve">
    <value>启用组件的动画效果</value>
  </data>
  <data name="FormFields_Layout" xml:space="preserve">
    <value>布局</value>
  </data>

  <!-- CompanyBasicInfo 组件专用资源 -->
  <data name="CompanyBasicInfo_Title" xml:space="preserve">
    <value>公司基本信息</value>
  </data>
  <data name="CompanyBasicInfo_EstablishedDate" xml:space="preserve">
    <value>成立时间</value>
  </data>
  <data name="CompanyBasicInfo_Capital" xml:space="preserve">
    <value>资本金</value>
  </data>
  <data name="CompanyBasicInfo_Representative" xml:space="preserve">
    <value>代表人</value>
  </data>
  <data name="CompanyBasicInfo_EmployeeCount" xml:space="preserve">
    <value>员工人数</value>
  </data>
  <data name="CompanyBasicInfo_BusinessType" xml:space="preserve">
    <value>业务类型</value>
  </data>
  <data name="CompanyBasicInfo_HeadOffice" xml:space="preserve">
    <value>总部地址</value>
  </data>
  <data name="CompanyBasicInfo_Website" xml:space="preserve">
    <value>官方网站</value>
  </data>
  <data name="CompanyBasicInfo_CustomFields" xml:space="preserve">
    <value>自定义字段</value>
  </data>
  <data name="CompanyBasicInfo_Layout" xml:space="preserve">
    <value>布局样式</value>
  </data>
  <data name="CompanyBasicInfo_ShowTitle" xml:space="preserve">
    <value>显示标题</value>
  </data>
  <data name="CompanyBasicInfo_ShowBorder" xml:space="preserve">
    <value>显示边框</value>
  </data>
  <data name="CompanyBasicInfo_TitleText" xml:space="preserve">
    <value>自定义标题</value>
  </data>
  <data name="CompanyBasicInfo_BackgroundStyle" xml:space="preserve">
    <value>背景样式</value>
  </data>

  <!-- 自定义字段相关 -->
  <data name="CustomField_Label" xml:space="preserve">
    <value>字段标签</value>
  </data>
  <data name="CustomField_Value" xml:space="preserve">
    <value>字段值</value>
  </data>
  <data name="CustomField_Icon" xml:space="preserve">
    <value>图标</value>
  </data>
  <data name="CustomField_IsVisible" xml:space="preserve">
    <value>显示</value>
  </data>

  <!-- 布局选项 -->
  <data name="Layout_Grid" xml:space="preserve">
    <value>网格布局</value>
  </data>
  <data name="Layout_List" xml:space="preserve">
    <value>列表布局</value>
  </data>
  <data name="Layout_Card" xml:space="preserve">
    <value>卡片布局</value>
  </data>

  <!-- 背景样式选项 -->
  <data name="Background_White" xml:space="preserve">
    <value>白色背景</value>
  </data>
  <data name="Background_Gray" xml:space="preserve">
    <value>灰色背景</value>
  </data>
  <data name="Background_Transparent" xml:space="preserve">
    <value>透明背景</value>
  </data>

  <!-- 字段显示控制 -->
  <data name="CompanyBasicInfo_ShowEstablishedDate" xml:space="preserve">
    <value>显示成立时间</value>
  </data>
  <data name="CompanyBasicInfo_ShowCapital" xml:space="preserve">
    <value>显示资本金</value>
  </data>
  <data name="CompanyBasicInfo_ShowEmployeeScale" xml:space="preserve">
    <value>显示员工规模</value>
  </data>
  <data name="CompanyBasicInfo_ShowAddress" xml:space="preserve">
    <value>显示地址</value>
  </data>
  <data name="CompanyBasicInfo_ShowPhone" xml:space="preserve">
    <value>显示电话</value>
  </data>
  <data name="CompanyBasicInfo_ShowEmail" xml:space="preserve">
    <value>显示邮箱</value>
  </data>
  <data name="CompanyBasicInfo_ShowWebsite" xml:space="preserve">
    <value>显示网站</value>
  </data>
  <data name="CompanyBasicInfo_ShowPresident" xml:space="preserve">
    <value>显示代表取締役社長</value>
  </data>
  <data name="CompanyBasicInfo_ShowPostalCode" xml:space="preserve">
    <value>显示邮编</value>
  </data>
  <data name="CompanyBasicInfo_ShowRegistrationNumber" xml:space="preserve">
    <value>显示工商注册号</value>
  </data>
  <data name="CompanyBasicInfo_President" xml:space="preserve">
    <value>代表取締役社長</value>
  </data>
  <data name="CompanyBasicInfo_PostalCode" xml:space="preserve">
    <value>邮编</value>
  </data>
  <data name="CompanyBasicInfo_RegistrationNumber" xml:space="preserve">
    <value>工商注册号</value>
  </data>
  <data name="CompanyBasicInfo_BasicInfo" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="CompanyBasicInfo_ContactInfo" xml:space="preserve">
    <value>联系信息</value>
  </data>
  <data name="CompanyBasicInfo_Fax" xml:space="preserve">
    <value>传真</value>
  </data>

  <!-- PresidentMessage 组件专用资源 -->
  <data name="PresidentMessage_Title" xml:space="preserve">
    <value>总经理致辞</value>
  </data>
  <data name="PresidentMessage_Biography" xml:space="preserve">
    <value>简历</value>
  </data>
  <data name="PresidentMessage_NoData" xml:space="preserve">
    <value>暂无总经理信息</value>
  </data>
  <data name="PresidentMessage_MessageFrom" xml:space="preserve">
    <value>来自总经理的话</value>
  </data>
  <data name="PresidentMessage_ShowPhoto" xml:space="preserve">
    <value>显示照片</value>
  </data>
  <data name="PresidentMessage_ShowPosition" xml:space="preserve">
    <value>显示职位</value>
  </data>
  <data name="PresidentMessage_ShowBiography" xml:space="preserve">
    <value>显示简历</value>
  </data>
  <data name="PresidentMessage_ShowTitle" xml:space="preserve">
    <value>显示标题</value>
  </data>
  <data name="PresidentMessage_ShowBorder" xml:space="preserve">
    <value>显示边框</value>
  </data>
  <data name="PresidentMessage_TitleText" xml:space="preserve">
    <value>自定义标题</value>
  </data>
  <data name="PresidentMessage_BackgroundStyle" xml:space="preserve">
    <value>背景样式</value>
  </data>
  <data name="PresidentMessage_PhotoPosition" xml:space="preserve">
    <value>照片位置</value>
  </data>
  <data name="PresidentMessage_PhotoSize" xml:space="preserve">
    <value>照片大小</value>
  </data>

  <!-- 照片位置选项 -->
  <data name="PhotoPosition_Left" xml:space="preserve">
    <value>左侧</value>
  </data>
  <data name="PhotoPosition_Right" xml:space="preserve">
    <value>右侧</value>
  </data>
  <data name="PhotoPosition_Top" xml:space="preserve">
    <value>顶部</value>
  </data>

  <!-- 照片大小选项 -->
  <data name="PhotoSize_Small" xml:space="preserve">
    <value>小</value>
  </data>
  <data name="PhotoSize_Medium" xml:space="preserve">
    <value>中</value>
  </data>
  <data name="PhotoSize_Large" xml:space="preserve">
    <value>大</value>
  </data>

  <!-- Complete变体专用资源 -->
  <data name="PresidentMessage_ToStakeholders" xml:space="preserve">
    <value>致各位利益相关者</value>
  </data>
  <data name="FormFields_ShowMap" xml:space="preserve">
    <value>显示地图</value>
  </data>
  <data name="FormFields_ShowContactInfo" xml:space="preserve">
    <value>显示联系信息</value>
  </data>
  <data name="FormFields_ItemsPerRow" xml:space="preserve">
    <value>每行项目数</value>
  </data>

  
  <!-- 表单分组 -->
  <data name="FormGroups_CustomFields" xml:space="preserve">
    <value>自定义字段</value>
  </data>
  <data name="FormGroups_FieldDisplay" xml:space="preserve">
    <value>字段显示</value>
  </data>

  <data name="FormFields_ShowImages" xml:space="preserve">
    <value>显示图片</value>
  </data>
  <data name="FormFields_BorderRadius" xml:space="preserve">
    <value>圆角设置</value>
  </data>
  <data name="FormFields_SpacingSettings" xml:space="preserve">
    <value>间距设置</value>
  </data>

  <!-- Customer Cases specific fields -->
  <data name="CustomerCases" xml:space="preserve">
    <value>客户案例</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>公司名称</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>公司Logo</value>
  </data>
  <data name="Industry" xml:space="preserve">
    <value>行业</value>
  </data>
  <data name="ProjectTitle" xml:space="preserve">
    <value>项目标题</value>
  </data>
  <data name="ProjectDescription" xml:space="preserve">
    <value>项目描述</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>特色图片</value>
  </data>
  <data name="TestimonialText" xml:space="preserve">
    <value>客户评价</value>
  </data>
  <data name="TestimonialAuthor" xml:space="preserve">
    <value>评价作者</value>
  </data>
  <data name="TestimonialPosition" xml:space="preserve">
    <value>作者职位</value>
  </data>
  <data name="CaseStudyUrl" xml:space="preserve">
    <value>案例详情链接</value>
  </data>
  <data name="IsFeatured" xml:space="preserve">
    <value>推荐案例</value>
  </data>
  <data name="CustomerCases_ShowIndustryFilter" xml:space="preserve">
    <value>显示行业筛选</value>
  </data>
  <data name="CustomerCases_ShowTestimonials" xml:space="preserve">
    <value>显示客户评价</value>
  </data>
  <data name="CustomerCases_ShowCompanyLogos" xml:space="preserve">
    <value>显示公司Logo</value>
  </data>
  <data name="FormGroups_CTA" xml:space="preserve">
    <value>行动按钮</value>
  </data>
  <data name="ShowCtaButton" xml:space="preserve">
    <value>显示行动按钮</value>
  </data>
  <data name="FormFields_ButtonUrl" xml:space="preserve">
    <value>按钮链接</value>
  </data>
  <data name="FormGroups_Content" xml:space="preserve">
    <value>内容</value>
  </data>

  <!-- Process Steps fields (中性命名，可复用) -->
  <data name="ProcessSteps" xml:space="preserve">
    <value>流程步骤</value>
  </data>
  <data name="StepNumber" xml:space="preserve">
    <value>步骤编号</value>
  </data>
  <data name="FormFields_ShowStepNumbers" xml:space="preserve">
    <value>显示步骤编号</value>
  </data>
  <data name="FormFields_ShowStepIcons" xml:space="preserve">
    <value>显示步骤图标</value>
  </data>
  <data name="FormFields_ShowConnectors" xml:space="preserve">
    <value>显示连接线</value>
  </data>
  <data name="DetailText" xml:space="preserve">
    <value>详细说明</value>
  </data>

  <!-- SearchBox 组件字段 (中性命名，可复用) -->
  <data name="FormFields_SearchFields" xml:space="preserve">
    <value>搜索字段</value>
  </data>
  <data name="FormFields_SearchFieldsHelpText" xml:space="preserve">
    <value>配置搜索表单的输入字段</value>
  </data>
  <data name="FormFields_FieldName" xml:space="preserve">
    <value>字段名称</value>
  </data>
  <data name="FormFields_FieldType" xml:space="preserve">
    <value>字段类型</value>
  </data>
  <data name="FormFields_TextInput" xml:space="preserve">
    <value>文本输入</value>
  </data>
  <data name="FormFields_SelectDropdown" xml:space="preserve">
    <value>下拉选择</value>
  </data>
  <data name="FormFields_Placeholder" xml:space="preserve">
    <value>占位符</value>
  </data>
  <data name="FormFields_FieldWidth" xml:space="preserve">
    <value>字段宽度</value>
  </data>
  <data name="FormFields_WidthAuto" xml:space="preserve">
    <value>自动宽度</value>
  </data>
  <data name="FormFields_WidthHalf" xml:space="preserve">
    <value>半宽</value>
  </data>
  <data name="FormFields_WidthThird" xml:space="preserve">
    <value>三分之一宽</value>
  </data>
  <data name="FormFields_Required" xml:space="preserve">
    <value>必填</value>
  </data>
  <data name="FormFields_SelectOptions" xml:space="preserve">
    <value>选项列表</value>
  </data>
  <data name="FormFields_SelectOptionsHelpText" xml:space="preserve">
    <value>为下拉选择字段配置可选项</value>
  </data>
  <data name="FormFields_OptionValue" xml:space="preserve">
    <value>选项值</value>
  </data>
  <data name="FormFields_OptionLabel" xml:space="preserve">
    <value>选项标签</value>
  </data>
  <data name="FormFields_ShowSearchButton" xml:space="preserve">
    <value>显示搜索按钮</value>
  </data>
  <data name="FormFields_ShowResetButton" xml:space="preserve">
    <value>显示重置按钮</value>
  </data>
  <data name="FormFields_SearchButtonText" xml:space="preserve">
    <value>搜索按钮文字</value>
  </data>
  <data name="FormFields_ResetButtonText" xml:space="preserve">
    <value>重置按钮文字</value>
  </data>
  <data name="FormFields_SearchAction" xml:space="preserve">
    <value>搜索动作</value>
  </data>
  <data name="FormFields_SearchActionHelpText" xml:space="preserve">
    <value>搜索表单提交的目标URL地址</value>
  </data>
  <data name="FormFields_SearchMethod" xml:space="preserve">
    <value>搜索方法</value>
  </data>
  <data name="ActionText" xml:space="preserve">
    <value>操作按钮文本</value>
  </data>
  <data name="ActionUrl" xml:space="preserve">
    <value>操作链接</value>
  </data>
  <data name="IsHighlighted" xml:space="preserve">
    <value>高亮显示</value>
  </data>
  <data name="NumberStyle" xml:space="preserve">
    <value>编号样式</value>
  </data>

  <!-- Pagination Component -->
  <data name="PageNumber" xml:space="preserve">
    <value>当前页码</value>
  </data>
  <data name="ItemsPerPage" xml:space="preserve">
    <value>每页显示数</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>总计</value>
  </data>
  <data name="DisplayRange" xml:space="preserve">
    <value>显示范围</value>
  </data>
  <data name="Url" xml:space="preserve">
    <value>链接地址</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>参数</value>
  </data>
  <data name="ShowFirstLast" xml:space="preserve">
    <value>显示首末页</value>
  </data>
  <data name="ShowPrevNext" xml:space="preserve">
    <value>显示上下页</value>
  </data>
  <data name="ShowPageNumbers" xml:space="preserve">
    <value>显示页码</value>
  </data>
  <data name="ShowTotal" xml:space="preserve">
    <value>显示总计</value>
  </data>

  <!-- NewsList 组件字段 -->
  <data name="FormFields_DateFormat" xml:space="preserve">
    <value>日期格式</value>
  </data>

</root>