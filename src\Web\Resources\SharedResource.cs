﻿using Microsoft.Extensions.Localization;

namespace MlSoft.Sites.Web.Resources
{
    /// <summary>
    /// 共享资源服务 - 同时支持依赖注入和静态访问
    /// </summary>
    public class SharedResource
    {
        private readonly IStringLocalizer<SharedResource> _localizer;
        private static IStringLocalizer<SharedResource>? _staticLocalizer;

        public SharedResource(IStringLocalizer<SharedResource> localizer)
        {
            _localizer = localizer;
            // 每次创建实例都更新静态本地化器，确保语言切换时能正确更新
            _staticLocalizer = localizer;
        }

        /// <summary>
        /// 初始化静态本地化器（用于Data Annotations）
        /// </summary>
        public static void InitializeStatic(IStringLocalizer<SharedResource> localizer)
        {
            _staticLocalizer = localizer;
        }

        // 统一索引器访问（实例方法）
        public string this[string key] => _localizer[key];

        public static string Home => _staticLocalizer?["Home"] ?? "Home";

        // ===========================================
        // 静态属性（用于Data Annotations）
        // ===========================================

        // 联系表单字段名称
        public static string Contact_Name => _staticLocalizer?["Contact_Name"] ?? "姓名";
        public static string Contact_Email => _staticLocalizer?["Contact_Email"] ?? "邮箱";
        public static string Contact_Phone => _staticLocalizer?["Contact_Phone"] ?? "电话";
        public static string Contact_Company => _staticLocalizer?["Contact_Company"] ?? "公司";
        public static string Contact_Position => _staticLocalizer?["Contact_Position"] ?? "职位";
        public static string Contact_MessageType => _staticLocalizer?["Contact_MessageType"] ?? "咨询类型";
        public static string Contact_MessageContent => _staticLocalizer?["Contact_MessageContent"] ?? "留言内容";

        // 联系表单验证错误消息
        public static string Contact_NameRequired => _staticLocalizer?["Contact_NameRequired"] ?? "姓名是必填项";
        public static string Contact_NameMaxLength => _staticLocalizer?["Contact_NameMaxLength"] ?? "姓名不能超过100个字符";
        public static string Contact_EmailRequired => _staticLocalizer?["Contact_EmailRequired"] ?? "邮箱是必填项";
        public static string Contact_EmailInvalid => _staticLocalizer?["Contact_EmailInvalid"] ?? "请输入有效的邮箱地址";
        public static string Contact_EmailMaxLength => _staticLocalizer?["Contact_EmailMaxLength"] ?? "邮箱不能超过100个字符";
        public static string Contact_PhoneInvalid => _staticLocalizer?["Contact_PhoneInvalid"] ?? "请输入有效的电话号码";
        public static string Contact_PhoneMaxLength => _staticLocalizer?["Contact_PhoneMaxLength"] ?? "电话号码不能超过20个字符";
        public static string Contact_CompanyMaxLength => _staticLocalizer?["Contact_CompanyMaxLength"] ?? "公司名称不能超过100个字符";
        public static string Contact_PositionMaxLength => _staticLocalizer?["Contact_PositionMaxLength"] ?? "职位不能超过50个字符";
        public static string Contact_MessageRequired => _staticLocalizer?["Contact_MessageRequired"] ?? "留言内容是必填项";
        public static string Contact_MessageMinLength => _staticLocalizer?["Contact_MessageMinLength"] ?? "留言内容至少需要10个字符";
        public static string Contact_MessageMaxLength => _staticLocalizer?["Contact_MessageMaxLength"] ?? "留言内容不能超过2000个字符";
        public static string Contact_MessageTypeRequired => _staticLocalizer?["Contact_MessageTypeRequired"] ?? "请选择咨询类型";
    }
}
