﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="SlideOf" xml:space="preserve">
    <value>Slide {0} of {1}</value>
  </data>
  
  <!-- 自定义弹窗资源 -->
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Alert" xml:space="preserve">
    <value>Alert</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="OpenMobileMenu" xml:space="preserve">
    <value>Open menu</value>
  </data>

  <!-- Form Groups -->
  <data name="FormGroups_BasicInfo" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="FormGroups_MediaContent" xml:space="preserve">
    <value>Media Content</value>
  </data>
  <data name="FormGroups_LayoutSettings" xml:space="preserve">
    <value>Layout Settings</value>
  </data>
  <data name="FormGroups_DisplaySettings" xml:space="preserve">
    <value>Display Settings</value>
  </data>
  <data name="FormGroups_ButtonSettings" xml:space="preserve">
    <value>Button Settings</value>
  </data>
  <data name="FormGroups_Resources" xml:space="preserve">
    <value>Resource Files</value>
  </data>
  <data name="FormGroups_SEOSettings" xml:space="preserve">
    <value>SEO Settings</value>
  </data>
  <data name="FormGroups_Other" xml:space="preserve">
    <value>Other</value>
  </data>

  <!-- Field Labels -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>Subtitle</value>
  </data>
  <data name="FormFields_Logo" xml:space="preserve">
    <value>Logo Image</value>
  </data>
  <data name="FormFields_BackgroundImage" xml:space="preserve">
    <value>Background Image</value>
  </data>
  <data name="FormFields_BackgroundVideo" xml:space="preserve">
    <value>Background Video</value>
  </data>
  <data name="FormFields_Documents" xml:space="preserve">
    <value>Related Documents</value>
  </data>
  <data name="FormFields_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="FormFields_FeaturedImage" xml:space="preserve">
    <value>Featured Image</value>
  </data>
  <data name="FormFields_Gallery" xml:space="preserve">
    <value>Image Gallery</value>
  </data>
  <data name="FormFields_VideoFile" xml:space="preserve">
    <value>Video File</value>
  </data>
  <data name="FormFields_DisplayMode" xml:space="preserve">
    <value>Display Mode</value>
  </data>
  <data name="FormFields_Alignment" xml:space="preserve">
    <value>Text Alignment</value>
  </data>
  <data name="FormFields_ShowCaptions" xml:space="preserve">
    <value>Show Captions</value>
  </data>
  <data name="FormFields_EnableLightbox" xml:space="preserve">
    <value>Enable Lightbox</value>
  </data>
  <data name="FormFields_AutoPlay" xml:space="preserve">
    <value>Auto Play</value>
  </data>

  <!-- Help Text -->
  <data name="FormFields_TitleHelpText" xml:space="preserve">
    <value>Enter page title, supports multiple languages</value>
  </data>
  <data name="FormFields_SubtitleHelpText" xml:space="preserve">
    <value>Enter page subtitle, optional</value>
  </data>
  <data name="FormFields_LogoHelpText" xml:space="preserve">
    <value>Upload company logo, recommended size 200x100 pixels</value>
  </data>
  <data name="FormFields_BackgroundImageHelpText" xml:space="preserve">
    <value>Upload background image, recommended size 1920x1080 pixels</value>
  </data>
  <data name="FormFields_DescriptionHelpText" xml:space="preserve">
    <value>Enter content description, supports multiple languages</value>
  </data>
  <data name="FormFields_FeaturedImageHelpText" xml:space="preserve">
    <value>Click to upload or drag files here, images, max 3MB</value>
  </data>
  <data name="FormFields_GalleryHelpText" xml:space="preserve">
    <value>Click to upload or drag files here, images, max 2MB</value>
  </data>

  <!-- File Upload -->
  <data name="FileUpload_SelectFile" xml:space="preserve">
    <value>Select File</value>
  </data>
  <data name="FileUpload_ClickToUpload" xml:space="preserve">
    <value>Click to upload</value>
  </data>
  <data name="FileUpload_OrDragDrop" xml:space="preserve">
    <value>or drag and drop files here</value>
  </data>
  <data name="FileUpload_DragDropZone" xml:space="preserve">
    <value>Drag files here or click to select</value>
  </data>
  <data name="FileUpload_DeleteFile" xml:space="preserve">
    <value>Delete File</value>
  </data>
  <data name="FileUpload_DeleteConfirm" xml:space="preserve">
    <value>Are you sure you want to delete this file?</value>
  </data>
  <data name="FileUpload_UploadSuccess" xml:space="preserve">
    <value>File uploaded successfully</value>
  </data>
  <data name="FileUpload_UploadError" xml:space="preserve">
    <value>File upload failed</value>
  </data>
  <data name="FileUpload_DeleteSuccess" xml:space="preserve">
    <value>File deleted successfully</value>
  </data>
  <data name="FileUpload_DeleteError" xml:space="preserve">
    <value>File deletion failed</value>
  </data>
  <data name="FileUpload_FileTooLarge" xml:space="preserve">
    <value>File size exceeds limit</value>
  </data>
  <data name="FileUpload_InvalidFileType" xml:space="preserve">
    <value>Unsupported file type</value>
  </data>
  <data name="FileUpload_NoFileSelected" xml:space="preserve">
    <value>No file selected</value>
  </data>
  <data name="FileUpload_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="FileUpload_AllFiles" xml:space="preserve">
    <value>All file types</value>
  </data>
  <data name="FileUpload_MaxSize" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="FormGroups_Goals" xml:space="preserve">
    <value>Goals</value>
  </data>
  <!-- Form Actions -->
  <data name="Form_CollapseGroup" xml:space="preserve">
    <value>Collapse Group</value>
  </data>
  <data name="Form_ExpandGroup" xml:space="preserve">
    <value>Expand Group</value>
  </data>
  <data name="Form_ResetField" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Form_ResetConfirm" xml:space="preserve">
    <value>Are you sure you want to reset all changes?</value>
  </data>
  <data name="Form_ClearAll" xml:space="preserve">
    <value>Clear All</value>
  </data>
  
  <!-- Common Text -->
  <data name="EnterText" xml:space="preserve">
    <value>Enter text</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="SaveSuccess" xml:space="preserve">
    <value>Saved successfully</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>Save failed</value>
  </data>
  <data name="ResetSuccess" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="LoadError" xml:space="preserve">
    <value>Loading failed</value>
  </data>
  <data name="LoadErrorDesc" xml:space="preserve">
    <value>Unable to load component properties, please try again</value>
  </data>
  <data name="FieldRenderError" xml:space="preserve">
    <value>Failed to render field</value>
  </data>
  <data name="FileType_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="FileType_Video" xml:space="preserve">
    <value>Video</value>
  </data>
  <data name="FileType_PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="FileType_Word" xml:space="preserve">
    <value>Word Document</value>
  </data>
  <data name="PleaseSelect" xml:space="preserve">
    <value>Please select...</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>Chinese</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>Japanese</value>
  </data>

  <!-- Pagination Component Resources -->
  <data name="Pagination_Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="Pagination_Records" xml:space="preserve">
    <value>records</value>
  </data>
  <data name="Pagination_Navigation" xml:space="preserve">
    <value>Pagination Navigation</value>
  </data>
  <data name="Pagination_First" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="Pagination_Prev" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Pagination_Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="Pagination_Last" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="Pagination_Page" xml:space="preserve">
    <value>Page </value>
  </data>
  <data name="Pagination_Of" xml:space="preserve">
    <value> of </value>
  </data>
  <data name="Pagination_Pages" xml:space="preserve">
    <value> pages</value>
  </data>
  <data name="Pagination_GoTo" xml:space="preserve">
    <value>Go to</value>
  </data>
  <data name="Pagination_PageSize" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="Pagination_Items" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="Pagination_ShowingItems" xml:space="preserve">
    <value>Showing {0} to {1} of {2} items</value>
  </data>

  <!-- Message Component Resources -->
  <data name="Contact_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Contact_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Contact_Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="Contact_Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="Contact_Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="Contact_MessageType" xml:space="preserve">
    <value>Inquiry Type</value>
  </data>
  <data name="Contact_MessageContent" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="Contact_PleaseSelect" xml:space="preserve">
    <value>Please select...</value>
  </data>
  <data name="Contact_RequiredFieldsNote" xml:space="preserve">
    <value>* Required fields</value>
  </data>
  <data name="Contact_PrivacyNotice" xml:space="preserve">
    <value>Please agree to the privacy policy before submitting.</value>
  </data>
  <data name="Contact_Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Contact_Submitting" xml:space="preserve">
    <value>Submitting...</value>
  </data>
  <data name="Contact_SubmitSuccess" xml:space="preserve">
    <value>Submitted Successfully</value>
  </data>
  <data name="Contact_SubmitSuccessMessage" xml:space="preserve">
    <value>Thank you for your message! We will contact you soon.</value>
  </data>
  <data name="Contact_SubmitError" xml:space="preserve">
    <value>An error occurred while submitting. Please try again later.</value>
  </data>
  <data name="Contact_NamePlaceholder" xml:space="preserve">
    <value>Enter your name</value>
  </data>
  <data name="Contact_EmailPlaceholder" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="Contact_PhonePlaceholder" xml:space="preserve">
    <value>Enter your phone number</value>
  </data>
  <data name="Contact_CompanyPlaceholder" xml:space="preserve">
    <value>Enter your company name</value>
  </data>
  <data name="Contact_PositionPlaceholder" xml:space="preserve">
    <value>Enter your position</value>
  </data>
  <data name="Contact_MessagePlaceholder" xml:space="preserve">
    <value>Please describe your question or requirements in detail</value>
  </data>

  <!-- Contact Form Validation Error Messages -->
  <data name="Contact_NameRequired" xml:space="preserve">
    <value>Name is required</value>
  </data>
  <data name="Contact_NameMaxLength" xml:space="preserve">
    <value>Name cannot exceed 100 characters</value>
  </data>
  <data name="Contact_EmailRequired" xml:space="preserve">
    <value>Email is required</value>
  </data>
  <data name="Contact_EmailInvalid" xml:space="preserve">
    <value>Please enter a valid email address</value>
  </data>
  <data name="Contact_EmailMaxLength" xml:space="preserve">
    <value>Email cannot exceed 100 characters</value>
  </data>
  <data name="Contact_PhoneInvalid" xml:space="preserve">
    <value>Please enter a valid phone number</value>
  </data>
  <data name="Contact_PhoneMaxLength" xml:space="preserve">
    <value>Phone number cannot exceed 20 characters</value>
  </data>
  <data name="Contact_CompanyMaxLength" xml:space="preserve">
    <value>Company name cannot exceed 100 characters</value>
  </data>
  <data name="Contact_PositionMaxLength" xml:space="preserve">
    <value>Position cannot exceed 50 characters</value>
  </data>
  <data name="Contact_MessageRequired" xml:space="preserve">
    <value>Message is required</value>
  </data>
  <data name="Contact_MessageMinLength" xml:space="preserve">
    <value>Message must be at least 10 characters</value>
  </data>
  <data name="Contact_MessageMaxLength" xml:space="preserve">
    <value>Message cannot exceed 2000 characters</value>
  </data>
  <data name="Contact_MessageTypeRequired" xml:space="preserve">
    <value>Please select an inquiry type</value>
  </data>

  <!-- 404 Error Page Resources -->
  <data name="Error404_Title" xml:space="preserve">
    <value>Page Not Found</value>
  </data>
  <data name="Error404_Heading" xml:space="preserve">
    <value>404</value>
  </data>
  <data name="Error404_Message" xml:space="preserve">
    <value>Sorry, the page you are looking for does not exist</value>
  </data>
  <data name="Error404_BackToHome" xml:space="preserve">
    <value>Back to Home</value>
  </data>
  <data name="CompanyProfile_Title" xml:space="preserve">
    <value>Company Profile</value>
  </data>
  <data name="Button_ViewDetail" xml:space="preserve">
    <value>View Detail</value>
  </data>
  <data name="Button_ViewAll" xml:space="preserve">
      <value>View All</value>
  </data>
  <data name="BusinessIntroduction_Title" xml:space="preserve">
      <value>Business Introduction</value>
  </data>
    <data name="BusinessIntroduction_BusinessItems" xml:space="preserve">
      <value>Business Items</value>
  </data>
  <data name="BusinessItem_Features" xml:space="preserve">
      <value>Features</value>
  </data>
  <data name="NewsSection_Title" xml:space="preserve">
      <value>News &amp; Announcements</value>
  </data>
  <data name="NewsSection_Description" xml:space="preserve">
      <value>Latest news and announcements.</value>
  </data>
  <data name="RecruitmentSection_Title" xml:space="preserve">
      <value>Recruitment</value>
  </data>
  <data name="RecruitmentSection_Description" xml:space="preserve">
      <value>Together, let's create a future with technology. We offer diverse working options and growth opportunities.</value>
  </data>

  <!-- Cookie Policy Resources -->
  <data name="CookiePolicy_Title" xml:space="preserve">
    <value>Cookie Policy</value>
  </data>
  <data name="CookiePolicy_Message" xml:space="preserve">
    <value>This website uses cookies to improve your browsing experience. By continuing to use this website, you agree to our use of cookies.</value>
  </data>
  <data name="CookiePolicy_Accept" xml:space="preserve">
    <value>Accept</value>
  </data>
  <data name="CookiePolicy_Decline" xml:space="preserve">
    <value>Decline</value>
  </data>
  <data name="CookiePolicy_LearnMore" xml:space="preserve">
    <value>Learn More</value>
  </data>

  <!-- FAQ Component Frontend Text -->
  <data name="SearchFAQs" xml:space="preserve">
    <value>Search FAQs...</value>
  </data>
  <data name="AllCategories" xml:space="preserve">
    <value>All Categories</value>
  </data>
  <data name="PopularQuestions" xml:space="preserve">
    <value>Popular Questions</value>
  </data>
  <data name="NoFAQsFound" xml:space="preserve">
    <value>No FAQs Found</value>
  </data>
  <data name="NoFAQsFoundDescription" xml:space="preserve">
    <value>Try searching with different keywords or browse other categories</value>
  </data>

  <!-- Customer Cases Component -->
  <data name="CustomerCases_Title" xml:space="preserve">
    <value>Customer Cases</value>
  </data>
  <data name="CustomerCases_Subtitle" xml:space="preserve">
    <value>Success Stories</value>
  </data>
  <data name="AllIndustries" xml:space="preserve">
    <value>All Industries</value>
  </data>
  <data name="IndustryFilter" xml:space="preserve">
    <value>Industry Filter</value>
  </data>
  <data name="ViewCaseStudy" xml:space="preserve">
    <value>View Case Study</value>
  </data>
  <data name="ViewAllCases" xml:space="preserve">
    <value>View All Cases</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>Learn More</value>
  </data>
  <data name="NoCasesAvailable" xml:space="preserve">
    <value>No Cases Available</value>
  </data>
  <data name="NoCasesDescription" xml:space="preserve">
    <value>Customer case studies are being prepared, please stay tuned</value>
  </data>

  <!-- HowToDo Component -->
  <data name="HowToDo_Title" xml:space="preserve">
    <value>How to Do</value>
  </data>
  <data name="HowToDo_Subtitle" xml:space="preserve">
    <value>User Guide</value>
  </data>
  <data name="GetStarted" xml:space="preserve">
    <value>Get Started</value>
  </data>
  <data name="NoStepsAvailable" xml:space="preserve">
    <value>No Steps Available</value>
  </data>
  <data name="NoStepsDescription" xml:space="preserve">
    <value>Process steps are being prepared, please stay tuned</value>
  </data>

  <!-- NewsList Component Resources -->
  <data name="NewsList_Title" xml:space="preserve">
    <value>News List</value>
  </data>
  <data name="NewsList_Description" xml:space="preserve">
    <value>Latest News</value>
  </data>
  <data name="NoNewsAvailable" xml:space="preserve">
    <value>No News Available</value>
  </data>
  <data name="Featured" xml:space="preserve">
    <value>Featured</value>
  </data>
  <data name="Views" xml:space="preserve">
    <value>views</value>
  </data>

</root>