﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Welcome" xml:space="preserve">
    <value>ようこそ</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>ホーム</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>前へ</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>次へ</value>
  </data>
  <data name="SlideOf" xml:space="preserve">
    <value>{1}枚中{0}枚目</value>
  </data>
  
  <!-- 自定义弹窗资源 -->
  <data name="Confirm" xml:space="preserve">
    <value>確認</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>はい</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>いいえ</value>
  </data>
  <data name="Alert" xml:space="preserve">
    <value>アラート</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>警告</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>エラー</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>情報</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>質問</value>
  </data>
  <data name="OpenMobileMenu" xml:space="preserve">
    <value>メニューを開く</value>
  </data>

  <!-- フォームグループ -->
  <data name="FormGroups_BasicInfo" xml:space="preserve">
    <value>基本情報</value>
  </data>
  <data name="FormGroups_MediaContent" xml:space="preserve">
    <value>メディアコンテンツ</value>
  </data>
  <data name="FormGroups_LayoutSettings" xml:space="preserve">
    <value>レイアウト設定</value>
  </data>
  <data name="FormGroups_DisplaySettings" xml:space="preserve">
    <value>表示設定</value>
  </data>
  <data name="FormGroups_ButtonSettings" xml:space="preserve">
    <value>ボタン設定</value>
  </data>
  <data name="FormGroups_Resources" xml:space="preserve">
    <value>リソースファイル</value>
  </data>
  <data name="FormGroups_SEOSettings" xml:space="preserve">
    <value>SEO設定</value>
  </data>
  <data name="FormGroups_Other" xml:space="preserve">
    <value>その他</value>
  </data>

  <!-- フィールドラベル -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>タイトル</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>サブタイトル</value>
  </data>
  <data name="FormFields_Logo" xml:space="preserve">
    <value>ロゴ画像</value>
  </data>
  <data name="FormFields_BackgroundImage" xml:space="preserve">
    <value>背景画像</value>
  </data>
  <data name="FormFields_BackgroundVideo" xml:space="preserve">
    <value>背景ビデオ</value>
  </data>
  <data name="FormFields_Documents" xml:space="preserve">
    <value>関連文書</value>
  </data>
  <data name="FormFields_Description" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="FormFields_FeaturedImage" xml:space="preserve">
    <value>メイン画像</value>
  </data>
  <data name="FormFields_Gallery" xml:space="preserve">
    <value>画像ギャラリー</value>
  </data>
  <data name="FormFields_VideoFile" xml:space="preserve">
    <value>動画ファイル</value>
  </data>
  <data name="FormFields_DisplayMode" xml:space="preserve">
    <value>表示モード</value>
  </data>
  <data name="FormFields_Alignment" xml:space="preserve">
    <value>テキスト配置</value>
  </data>
  <data name="FormFields_ShowCaptions" xml:space="preserve">
    <value>キャプション表示</value>
  </data>
  <data name="FormFields_EnableLightbox" xml:space="preserve">
    <value>ライトボックス有効</value>
  </data>
  <data name="FormFields_AutoPlay" xml:space="preserve">
    <value>自動再生</value>
  </data>

  <!-- ヘルプテキスト -->
  <data name="FormFields_TitleHelpText" xml:space="preserve">
    <value>ページタイトルを入力、多言語対応</value>
  </data>
  <data name="FormFields_SubtitleHelpText" xml:space="preserve">
    <value>ページサブタイトルを入力、任意</value>
  </data>
  <data name="FormFields_LogoHelpText" xml:space="preserve">
    <value>企業ロゴをアップロード、推奨サイズ200x100ピクセル</value>
  </data>
  <data name="FormFields_BackgroundImageHelpText" xml:space="preserve">
    <value>背景画像をアップロード、推奨サイズ1920x1080ピクセル</value>
  </data>
  <data name="FormFields_DescriptionHelpText" xml:space="preserve">
    <value>コンテンツの説明を入力、多言語対応</value>
  </data>
  <data name="FormFields_FeaturedImageHelpText" xml:space="preserve">
    <value>クリックしてアップロードまたはファイルをドラッグ、画像、最大3MB</value>
  </data>
  <data name="FormFields_GalleryHelpText" xml:space="preserve">
    <value>クリックしてアップロードまたはファイルをドラッグ、画像、最大2MB</value>
  </data>

  <!-- ファイルアップロード -->
  <data name="FileUpload_SelectFile" xml:space="preserve">
    <value>ファイル選択</value>
  </data>
  <data name="FileUpload_ClickToUpload" xml:space="preserve">
    <value>クリックしてアップロード</value>
  </data>
  <data name="FileUpload_OrDragDrop" xml:space="preserve">
    <value>またはファイルをここにドラッグ</value>
  </data>
  <data name="FileUpload_DragDropZone" xml:space="preserve">
    <value>ファイルをここにドラッグするかクリックして選択</value>
  </data>
  <data name="FileUpload_DeleteFile" xml:space="preserve">
    <value>ファイル削除</value>
  </data>
  <data name="FileUpload_DeleteConfirm" xml:space="preserve">
    <value>このファイルを削除してもよろしいですか？</value>
  </data>
  <data name="FileUpload_UploadSuccess" xml:space="preserve">
    <value>ファイルのアップロードが成功しました</value>
  </data>
  <data name="FileUpload_UploadError" xml:space="preserve">
    <value>ファイルのアップロードに失敗しました</value>
  </data>
  <data name="FileUpload_DeleteSuccess" xml:space="preserve">
    <value>ファイルの削除が成功しました</value>
  </data>
  <data name="FileUpload_DeleteError" xml:space="preserve">
    <value>ファイルの削除に失敗しました</value>
  </data>
  <data name="FileUpload_FileTooLarge" xml:space="preserve">
    <value>ファイルサイズが制限を超えています</value>
  </data>
  <data name="FileUpload_InvalidFileType" xml:space="preserve">
    <value>サポートされていないファイル形式</value>
  </data>
  <data name="FileUpload_NoFileSelected" xml:space="preserve">
    <value>ファイルが選択されていません</value>
  </data>
  <data name="FileUpload_Preview" xml:space="preserve">
    <value>プレビュー</value>
  </data>
  <data name="FileUpload_AllFiles" xml:space="preserve">
    <value>すべてのファイル形式</value>
  </data>
  <data name="FileUpload_MaxSize" xml:space="preserve">
    <value>最大</value>
  </data>
  <data name="FormGroups_Goals" xml:space="preserve">
    <value>目標</value>
  </data>

  <!-- フォームアクション -->
  <data name="Form_CollapseGroup" xml:space="preserve">
    <value>グループを折りたたむ</value>
  </data>
  <data name="Form_ExpandGroup" xml:space="preserve">
    <value>グループを展開</value>
  </data>
  <data name="Form_ResetField" xml:space="preserve">
    <value>リセット</value>
  </data>
  <data name="Form_ResetConfirm" xml:space="preserve">
    <value>すべての変更をリセットしてもよろしいですか？</value>
  </data>
  <data name="Form_ClearAll" xml:space="preserve">
    <value>すべてクリア</value>
  </data>
  
  <!-- 共通テキスト -->
  <data name="EnterText" xml:space="preserve">
    <value>テキストを入力</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>リセット</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>閉じる</value>
  </data>
  <data name="SaveSuccess" xml:space="preserve">
    <value>正常に保存されました</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>保存に失敗しました</value>
  </data>
  <data name="ResetSuccess" xml:space="preserve">
    <value>リセット済み</value>
  </data>
  <data name="LoadError" xml:space="preserve">
    <value>読み込みに失敗しました</value>
  </data>
  <data name="LoadErrorDesc" xml:space="preserve">
    <value>コンポーネントプロパティを読み込めませんでした。再試行してください</value>
  </data>
  <data name="FieldRenderError" xml:space="preserve">
    <value>フィールドレンダリング失敗</value>
  </data>
  <data name="FileType_Image" xml:space="preserve">
    <value>画像</value>
  </data>
  <data name="FileType_Video" xml:space="preserve">
    <value>動画</value>
  </data>
  <data name="FileType_PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="FileType_Word" xml:space="preserve">
    <value>Word文書</value>
  </data>
  <data name="PleaseSelect" xml:space="preserve">
    <value>選択してください...</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>中国語</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>英語</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>日本語</value>
  </data>

  <!-- ページネーションコンポーネントリソース -->
  <data name="Pagination_Total" xml:space="preserve">
    <value>合計</value>
  </data>
  <data name="Pagination_Records" xml:space="preserve">
    <value>件</value>
  </data>
  <data name="Pagination_Navigation" xml:space="preserve">
    <value>ページナビゲーション</value>
  </data>
  <data name="Pagination_First" xml:space="preserve">
    <value>最初</value>
  </data>
  <data name="Pagination_Prev" xml:space="preserve">
    <value>前へ</value>
  </data>
  <data name="Pagination_Next" xml:space="preserve">
    <value>次へ</value>
  </data>
  <data name="Pagination_Last" xml:space="preserve">
    <value>最後</value>
  </data>
  <data name="Pagination_Page" xml:space="preserve">
    <value>ページ</value>
  </data>
  <data name="Pagination_Of" xml:space="preserve">
    <value>/</value>
  </data>
  <data name="Pagination_Pages" xml:space="preserve">
    <value>ページ</value>
  </data>
  <data name="Pagination_GoTo" xml:space="preserve">
    <value>ジャンプ</value>
  </data>
  <data name="Pagination_PageSize" xml:space="preserve">
    <value>表示</value>
  </data>
  <data name="Pagination_Items" xml:space="preserve">
    <value>件</value>
  </data>
  <data name="Pagination_ShowingItems" xml:space="preserve">
    <value>{2}件中 {0} から {1} を表示</value>
  </data>

  <!-- メッセージコンポーネントリソース -->
  <data name="Contact_Name" xml:space="preserve">
    <value>お名前</value>
  </data>
  <data name="Contact_Email" xml:space="preserve">
    <value>メールアドレス</value>
  </data>
  <data name="Contact_Phone" xml:space="preserve">
    <value>電話番号</value>
  </data>
  <data name="Contact_Company" xml:space="preserve">
    <value>会社名</value>
  </data>
  <data name="Contact_Position" xml:space="preserve">
    <value>お役職</value>
  </data>
  <data name="Contact_MessageType" xml:space="preserve">
    <value>お問い合わせ種別</value>
  </data>
  <data name="Contact_MessageContent" xml:space="preserve">
    <value>お問い合わせ内容</value>
  </data>
  <data name="Contact_PleaseSelect" xml:space="preserve">
    <value>選択してください</value>
  </data>
  <data name="Contact_RequiredFieldsNote" xml:space="preserve">
    <value>※は必須項目です</value>
  </data>
  <data name="Contact_PrivacyNotice" xml:space="preserve">
    <value>個人情報の取り扱いについて同意の上、送信してください。</value>
  </data>
  <data name="Contact_Submit" xml:space="preserve">
    <value>送信</value>
  </data>
  <data name="Contact_Submitting" xml:space="preserve">
    <value>送信中...</value>
  </data>
  <data name="Contact_SubmitSuccess" xml:space="preserve">
    <value>送信完了</value>
  </data>
  <data name="Contact_SubmitSuccessMessage" xml:space="preserve">
    <value>お問い合わせありがとうございます。後日、担当者よりご連絡いたします。</value>
  </data>
  <data name="Contact_SubmitError" xml:space="preserve">
    <value>送信中にエラーが発生しました。しばらく経ってから再度お試しください。</value>
  </data>
  <data name="Contact_NamePlaceholder" xml:space="preserve">
    <value>山田太郎</value>
  </data>
  <data name="Contact_EmailPlaceholder" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="Contact_PhonePlaceholder" xml:space="preserve">
    <value>03-1234-5678</value>
  </data>
  <data name="Contact_CompanyPlaceholder" xml:space="preserve">
    <value>株式会社〇〇</value>
  </data>
  <data name="Contact_PositionPlaceholder" xml:space="preserve">
    <value>営業部長</value>
  </data>
  <data name="Contact_MessagePlaceholder" xml:space="preserve">
    <value>お問い合わせ内容をご記入ください</value>
  </data>

  <!-- 連絡フォーム検証エラーメッセージ -->
  <data name="Contact_NameRequired" xml:space="preserve">
    <value>お名前は必須です</value>
  </data>
  <data name="Contact_NameMaxLength" xml:space="preserve">
    <value>お名前は100文字以内で入力してください</value>
  </data>
  <data name="Contact_EmailRequired" xml:space="preserve">
    <value>メールアドレスは必須です</value>
  </data>
  <data name="Contact_EmailInvalid" xml:space="preserve">
    <value>有効なメールアドレスを入力してください</value>
  </data>
  <data name="Contact_EmailMaxLength" xml:space="preserve">
    <value>メールアドレスは100文字以内で入力してください</value>
  </data>
  <data name="Contact_PhoneInvalid" xml:space="preserve">
    <value>有効な電話番号を入力してください</value>
  </data>
  <data name="Contact_PhoneMaxLength" xml:space="preserve">
    <value>電話番号は20文字以内で入力してください</value>
  </data>
  <data name="Contact_CompanyMaxLength" xml:space="preserve">
    <value>会社名は100文字以内で入力してください</value>
  </data>
  <data name="Contact_PositionMaxLength" xml:space="preserve">
    <value>お役職は50文字以内で入力してください</value>
  </data>
  <data name="Contact_MessageRequired" xml:space="preserve">
    <value>お問い合わせ内容は必須です</value>
  </data>
  <data name="Contact_MessageMinLength" xml:space="preserve">
    <value>お問い合わせ内容は10文字以上で入力してください</value>
  </data>
  <data name="Contact_MessageMaxLength" xml:space="preserve">
    <value>お問い合わせ内容は2000文字以内で入力してください</value>
  </data>
  <data name="Contact_MessageTypeRequired" xml:space="preserve">
    <value>お問い合わせ種別を選択してください</value>
  </data>

  <!-- 404 Error Page Resources -->
  <data name="Error404_Title" xml:space="preserve">
    <value>ページが見つかりません</value>
  </data>
  <data name="Error404_Heading" xml:space="preserve">
    <value>404</value>
  </data>
  <data name="Error404_Message" xml:space="preserve">
    <value>申し訳ございません。お探しのページは存在しません</value>
  </data>
  <data name="Error404_BackToHome" xml:space="preserve">
    <value>ホームに戻る</value>
  </data>

  <data name="CompanyProfile_Title" xml:space="preserve">
    <value>会社概要</value>
  </data>
  <data name="Button_ViewDetail" xml:space="preserve">
      <value>詳細を見る</value>
  </data>
  <data name="Button_ViewAll" xml:space="preserve">
      <value>すべて見る</value>
  </data>
  <data name="BusinessIntroduction_Title" xml:space="preserve">
      <value>事業紹介</value>
  </data>
  <data name="BusinessIntroduction_BusinessItems" xml:space="preserve">
      <value>事業内容</value>
  </data>
  <data name="BusinessItem_Features" xml:space="preserve">
      <value>機能特点</value>
  </data>
    <data name="NewsSection_Title" xml:space="preserve">
      <value>ニュース・お知らせ</value>
  </data>
    <data name="NewsSection_Description" xml:space="preserve">
      <value>最新のニュースとお知らせをご紹介します。</value>
  </data>
    <data name="RecruitmentSection_Title" xml:space="preserve">
      <value>採用情報</value>
  </data>
  <data name="RecruitmentSection_Description" xml:space="preserve">
      <value>私たちと一緒に、テクノロジーで未来を創造しませんか？多様な働き方と成長機会を提供します。</value>
  </data>

  <!-- Cookie Policy Resources -->
  <data name="CookiePolicy_Title" xml:space="preserve">
    <value>Cookieポリシー</value>
  </data>
  <data name="CookiePolicy_Message" xml:space="preserve">
    <value>このウェブサイトでは、ブラウジング体験を向上させるためにCookieを使用しています。このウェブサイトを引き続き使用することで、Cookieの使用に同意したものとみなされます。</value>
  </data>
  <data name="CookiePolicy_Accept" xml:space="preserve">
    <value>同意する</value>
  </data>
  <data name="CookiePolicy_Decline" xml:space="preserve">
    <value>拒否する</value>
  </data>
  <data name="CookiePolicy_LearnMore" xml:space="preserve">
    <value>詳細を見る</value>
  </data>

  <!-- FAQ コンポーネントフロントエンドテキスト -->
  <data name="SearchFAQs" xml:space="preserve">
    <value>よくある質問を検索...</value>
  </data>
  <data name="AllCategories" xml:space="preserve">
    <value>すべてのカテゴリ</value>
  </data>
  <data name="PopularQuestions" xml:space="preserve">
    <value>人気の質問</value>
  </data>
  <data name="NoFAQsFound" xml:space="preserve">
    <value>関連する質問が見つかりません</value>
  </data>
  <data name="NoFAQsFoundDescription" xml:space="preserve">
    <value>異なるキーワードで検索するか、他のカテゴリをご覧ください</value>
  </data>

  <!-- Customer Cases コンポーネント -->
  <data name="CustomerCases_Title" xml:space="preserve">
    <value>顧客事例</value>
  </data>
  <data name="CustomerCases_Subtitle" xml:space="preserve">
    <value>成功事例</value>
  </data>
  <data name="AllIndustries" xml:space="preserve">
    <value>全業界</value>
  </data>
  <data name="IndustryFilter" xml:space="preserve">
    <value>業界フィルタ</value>
  </data>
  <data name="ViewCaseStudy" xml:space="preserve">
    <value>事例詳細を見る</value>
  </data>
  <data name="ViewAllCases" xml:space="preserve">
    <value>すべての事例を見る</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>詳細はこちら</value>
  </data>
  <data name="NoCasesAvailable" xml:space="preserve">
    <value>事例がありません</value>
  </data>
  <data name="NoCasesDescription" xml:space="preserve">
    <value>顧客事例を準備中です。しばらくお待ちください</value>
  </data>

  <!-- HowToDo Component -->\n  <data name="HowToDo_Title" xml:space="preserve">\n    <value>手順ガイド</value>\n  </data>\n  <data name="HowToDo_Subtitle" xml:space="preserve">\n    <value>利用ガイド</value>\n  </data>\n  <data name="GetStarted" xml:space="preserve">\n    <value>開始する</value>\n  </data>\n  <data name="NoStepsAvailable" xml:space="preserve">\n    <value>ステップがありません</value>\n  </data>\n  <data name="NoStepsDescription" xml:space="preserve">\n    <value>プロセスステップを準備中です。しばらくお待ちください</value>\n  </data>

</root>