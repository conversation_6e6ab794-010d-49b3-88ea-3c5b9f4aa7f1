﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Welcome" xml:space="preserve">
    <value>欢迎</value>
  </data>

  <data name="Home" xml:space="preserve">
    <value>首页</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>上一个</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>下一个</value>
  </data>
  <data name="SlideOf" xml:space="preserve">
    <value>第 {0} 张，共 {1} 张</value>
  </data>
  
  <!-- 自定义弹窗资源 -->
  <data name="Confirm" xml:space="preserve">
    <value>确认</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>是</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>否</value>
  </data>
  <data name="Alert" xml:space="preserve">
    <value>提示</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>警告</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>信息</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>询问</value>
  </data>
  <data name="OpenMobileMenu" xml:space="preserve">
    <value>打开菜单</value>
  </data>

  <!-- 表单分组 -->
  <data name="FormGroups_BasicInfo" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="FormGroups_MediaContent" xml:space="preserve">
    <value>媒体内容</value>
  </data>
  <data name="FormGroups_LayoutSettings" xml:space="preserve">
    <value>布局设置</value>
  </data>
  <data name="FormGroups_DisplaySettings" xml:space="preserve">
    <value>显示设置</value>
  </data>
    <data name="FormGroups_ButtonSettings" xml:space="preserve">
    <value>按钮设置</value>
  </data>
  <data name="FormGroups_Resources" xml:space="preserve">
    <value>资源文件</value>
  </data>
  <data name="FormGroups_SEOSettings" xml:space="preserve">
    <value>SEO设置</value>
  </data>
  <data name="FormGroups_Other" xml:space="preserve">
    <value>其他</value>
  </data>

  <!-- 字段标签 -->
  <data name="FormFields_Title" xml:space="preserve">
    <value>标题</value>
  </data>
  <data name="FormFields_Subtitle" xml:space="preserve">
    <value>副标题</value>
  </data>
  <data name="FormFields_Logo" xml:space="preserve">
    <value>标志图片</value>
  </data>
  <data name="FormFields_BackgroundImage" xml:space="preserve">
    <value>背景图片</value>
  </data>
  <data name="FormFields_BackgroundVideo" xml:space="preserve">
    <value>背景视频</value>
  </data>
  <data name="FormFields_Documents" xml:space="preserve">
    <value>相关文档</value>
  </data>
  <data name="FormFields_Description" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="FormFields_FeaturedImage" xml:space="preserve">
    <value>特色图片</value>
  </data>
  <data name="FormFields_Gallery" xml:space="preserve">
    <value>图片画廊</value>
  </data>
  <data name="FormFields_VideoFile" xml:space="preserve">
    <value>视频文件</value>
  </data>
  <data name="FormFields_DisplayMode" xml:space="preserve">
    <value>显示模式</value>
  </data>
  <data name="FormFields_Alignment" xml:space="preserve">
    <value>文本对齐</value>
  </data>
  <data name="FormFields_ShowCaptions" xml:space="preserve">
    <value>显示标题</value>
  </data>
  <data name="FormFields_EnableLightbox" xml:space="preserve">
    <value>启用灯箱</value>
  </data>
  <data name="FormFields_AutoPlay" xml:space="preserve">
    <value>自动播放</value>
  </data>

  <!-- 帮助文本 -->
  <data name="FormFields_TitleHelpText" xml:space="preserve">
    <value>输入页面主标题，支持多语言</value>
  </data>
  <data name="FormFields_SubtitleHelpText" xml:space="preserve">
    <value>输入页面副标题，可选填写</value>
  </data>
  <data name="FormFields_LogoHelpText" xml:space="preserve">
    <value>上传企业标志，推荐尺寸200x100像素</value>
  </data>
  <data name="FormFields_BackgroundImageHelpText" xml:space="preserve">
    <value>上传背景图片，推荐尺寸1920x1080像素</value>
  </data>
  <data name="FormFields_DescriptionHelpText" xml:space="preserve">
    <value>输入内容描述，支持多语言</value>
  </data>
  <data name="FormFields_FeaturedImageHelpText" xml:space="preserve">
    <value>点击上传或拖拽文件到此处，图片，最大3MB</value>
  </data>
  <data name="FormFields_GalleryHelpText" xml:space="preserve">
    <value>点击上传或拖拽文件到此处，图片，最大2MB</value>
  </data>

  <!-- 文件上传相关 -->
  <data name="FileUpload_SelectFile" xml:space="preserve">
    <value>选择文件</value>
  </data>
  <data name="FileUpload_ClickToUpload" xml:space="preserve">
    <value>点击上传</value>
  </data>
  <data name="FileUpload_OrDragDrop" xml:space="preserve">
    <value>或拖拽文件到此处</value>
  </data>
  <data name="FileUpload_DragDropZone" xml:space="preserve">
    <value>拖拽文件到此处或点击选择</value>
  </data>
  <data name="FileUpload_DeleteFile" xml:space="preserve">
    <value>删除文件</value>
  </data>
  <data name="FileUpload_DeleteConfirm" xml:space="preserve">
    <value>确定要删除这个文件吗？</value>
  </data>
  <data name="FileUpload_UploadSuccess" xml:space="preserve">
    <value>文件上传成功</value>
  </data>
  <data name="FileUpload_UploadError" xml:space="preserve">
    <value>文件上传失败</value>
  </data>
  <data name="FileUpload_DeleteSuccess" xml:space="preserve">
    <value>文件删除成功</value>
  </data>
  <data name="FileUpload_DeleteError" xml:space="preserve">
    <value>文件删除失败</value>
  </data>
  <data name="FileUpload_FileTooLarge" xml:space="preserve">
    <value>文件大小超过限制</value>
  </data>
  <data name="FileUpload_InvalidFileType" xml:space="preserve">
    <value>不支持的文件类型</value>
  </data>
  <data name="FileUpload_NoFileSelected" xml:space="preserve">
    <value>未选择文件</value>
  </data>
  <data name="FileUpload_Preview" xml:space="preserve">
    <value>预览</value>
  </data>
  <data name="FileUpload_AllFiles" xml:space="preserve">
    <value>所有文件类型</value>
  </data>
  <data name="FileUpload_MaxSize" xml:space="preserve">
    <value>最大</value>
  </data>

  <data name="FormGroups_Goals" xml:space="preserve">
    <value>目标</value>
  </data>

  <!-- 表单操作 -->
  <data name="Form_CollapseGroup" xml:space="preserve">
    <value>收起分组</value>
  </data>
  <data name="Form_ExpandGroup" xml:space="preserve">
    <value>展开分组</value>
  </data>
  <data name="Form_ResetField" xml:space="preserve">
    <value>重置</value>
  </data>
  <data name="Form_ResetConfirm" xml:space="preserve">
    <value>确定要重置所有更改吗？</value>
  </data>
  <data name="Form_ClearAll" xml:space="preserve">
    <value>清空所有</value>
  </data>
  
  <!-- 通用文本 -->
  <data name="EnterText" xml:space="preserve">
    <value>输入文本</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>重置</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>关闭</value>
  </data>
  <data name="SaveSuccess" xml:space="preserve">
    <value>保存成功</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>保存失败</value>
  </data>
  <data name="ResetSuccess" xml:space="preserve">
    <value>已重置</value>
  </data>
  <data name="LoadError" xml:space="preserve">
    <value>加载失败</value>
  </data>
  <data name="LoadErrorDesc" xml:space="preserve">
    <value>无法加载组件属性，请重试</value>
  </data>
  <data name="FieldRenderError" xml:space="preserve">
    <value>渲染字段失败</value>
  </data>
  <data name="FileType_Image" xml:space="preserve">
    <value>图片</value>
  </data>
  <data name="FileType_Video" xml:space="preserve">
    <value>视频</value>
  </data>
  <data name="FileType_PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="FileType_Word" xml:space="preserve">
    <value>Word文档</value>
  </data>
  <data name="PleaseSelect" xml:space="preserve">
    <value>请选择...</value>
  </data>
  <data name="Chinese" xml:space="preserve">
    <value>中文</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Japanese" xml:space="preserve">
    <value>日本語</value>
  </data>

  <!-- 分页组件相关资源 -->
  <data name="Pagination_Total" xml:space="preserve">
    <value>总计</value>
  </data>
  <data name="Pagination_Records" xml:space="preserve">
    <value>条记录</value>
  </data>
  <data name="Pagination_Navigation" xml:space="preserve">
    <value>分页导航</value>
  </data>
  <data name="Pagination_First" xml:space="preserve">
    <value>首页</value>
  </data>
  <data name="Pagination_Prev" xml:space="preserve">
    <value>上一页</value>
  </data>
  <data name="Pagination_Next" xml:space="preserve">
    <value>下一页</value>
  </data>
  <data name="Pagination_Last" xml:space="preserve">
    <value>末页</value>
  </data>
  <data name="Pagination_Page" xml:space="preserve">
    <value>第</value>
  </data>
  <data name="Pagination_Of" xml:space="preserve">
    <value>页，共</value>
  </data>
  <data name="Pagination_Pages" xml:space="preserve">
    <value>页</value>
  </data>
  <data name="Pagination_GoTo" xml:space="preserve">
    <value>跳转到</value>
  </data>
  <data name="Pagination_PageSize" xml:space="preserve">
    <value>每页显示</value>
  </data>
  <data name="Pagination_Items" xml:space="preserve">
    <value>条</value>
  </data>
  <data name="Pagination_ShowingItems" xml:space="preserve">
    <value>显示第 {0} 到 {1} 项，共 {2} 项</value>
  </data>

  <!-- Message组件相关资源 -->
  <data name="Contact_Name" xml:space="preserve">
    <value>姓名</value>
  </data>
  <data name="Contact_Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="Contact_Phone" xml:space="preserve">
    <value>电话</value>
  </data>
  <data name="Contact_Company" xml:space="preserve">
    <value>公司</value>
  </data>
  <data name="Contact_Position" xml:space="preserve">
    <value>职位</value>
  </data>
  <data name="Contact_MessageType" xml:space="preserve">
    <value>咨询类型</value>
  </data>
  <data name="Contact_MessageContent" xml:space="preserve">
    <value>留言内容</value>
  </data>
  <data name="Contact_PleaseSelect" xml:space="preserve">
    <value>请选择...</value>
  </data>
  <data name="Contact_RequiredFieldsNote" xml:space="preserve">
    <value>※为必填项</value>
  </data>
  <data name="Contact_PrivacyNotice" xml:space="preserve">
    <value>请同意个人信息处理政策后提交。</value>
  </data>
  <data name="Contact_Submit" xml:space="preserve">
    <value>提交</value>
  </data>
  <data name="Contact_Submitting" xml:space="preserve">
    <value>提交中...</value>
  </data>
  <data name="Contact_SubmitSuccess" xml:space="preserve">
    <value>提交成功</value>
  </data>
  <data name="Contact_SubmitSuccessMessage" xml:space="preserve">
    <value>感谢您的留言！我们会尽快与您联系。</value>
  </data>
  <data name="Contact_SubmitError" xml:space="preserve">
    <value>提交时发生错误，请稍后重试。</value>
  </data>
  <data name="Contact_NamePlaceholder" xml:space="preserve">
    <value>请输入您的姓名</value>
  </data>
  <data name="Contact_EmailPlaceholder" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="Contact_PhonePlaceholder" xml:space="preserve">
    <value>请输入您的电话号码</value>
  </data>
  <data name="Contact_CompanyPlaceholder" xml:space="preserve">
    <value>请输入您的公司名称</value>
  </data>
  <data name="Contact_PositionPlaceholder" xml:space="preserve">
    <value>请输入您的职位</value>
  </data>
  <data name="Contact_MessagePlaceholder" xml:space="preserve">
    <value>请详细描述您的问题或需求</value>
  </data>

  <!-- 联系表单验证错误消息 -->
  <data name="Contact_NameRequired" xml:space="preserve">
    <value>姓名是必填项</value>
  </data>
  <data name="Contact_NameMaxLength" xml:space="preserve">
    <value>姓名不能超过100个字符</value>
  </data>
  <data name="Contact_EmailRequired" xml:space="preserve">
    <value>邮箱是必填项</value>
  </data>
  <data name="Contact_EmailInvalid" xml:space="preserve">
    <value>请输入有效的邮箱地址</value>
  </data>
  <data name="Contact_EmailMaxLength" xml:space="preserve">
    <value>邮箱不能超过100个字符</value>
  </data>
  <data name="Contact_PhoneInvalid" xml:space="preserve">
    <value>请输入有效的电话号码</value>
  </data>
  <data name="Contact_PhoneMaxLength" xml:space="preserve">
    <value>电话号码不能超过20个字符</value>
  </data>
  <data name="Contact_CompanyMaxLength" xml:space="preserve">
    <value>公司名称不能超过100个字符</value>
  </data>
  <data name="Contact_PositionMaxLength" xml:space="preserve">
    <value>职位不能超过50个字符</value>
  </data>
  <data name="Contact_MessageRequired" xml:space="preserve">
    <value>留言内容是必填项</value>
  </data>
  <data name="Contact_MessageMinLength" xml:space="preserve">
    <value>留言内容至少需要10个字符</value>
  </data>
  <data name="Contact_MessageMaxLength" xml:space="preserve">
    <value>留言内容不能超过2000个字符</value>
  </data>
  <data name="Contact_MessageTypeRequired" xml:space="preserve">
    <value>请选择咨询类型</value>
  </data>

  <!-- 404 Error Page Resources -->
  <data name="Error404_Title" xml:space="preserve">
    <value>页面未找到</value>
  </data>
  <data name="Error404_Heading" xml:space="preserve">
    <value>404</value>
  </data>
  <data name="Error404_Message" xml:space="preserve">
    <value>抱歉，您访问的页面不存在</value>
  </data>
  <data name="Error404_BackToHome" xml:space="preserve">
    <value>返回首页</value>
  </data>  
  <data name="CompanyProfile_Title" xml:space="preserve">
      <value>企业简介</value>
  </data>
  <data name="Button_ViewDetail" xml:space="preserve">
      <value>查看详情</value>
  </data>
  <data name="Button_ViewAll" xml:space="preserve">
      <value>查看更多</value>
  </data>

  <data name="BusinessIntroduction_Title" xml:space="preserve">
      <value>业务信息</value>
  </data>
  <data name="BusinessIntroduction_BusinessItems" xml:space="preserve">
      <value>业务项目</value>
  </data>
  <data name="BusinessItem_Features" xml:space="preserve">
      <value>功能特点</value>
  </data>
  
  <data name="NewsSection_Title" xml:space="preserve">
      <value>新闻・公告</value>
  </data>
  <data name="NewsSection_Description" xml:space="preserve">
      <value>为您带来最新的新闻资讯与公告。</value>
  </data>

  <data name="RecruitmentSection_Title" xml:space="preserve">
      <value>招聘信息</value>
  </data>
  <data name="RecruitmentSection_Description" xml:space="preserve">
      <value>您愿意与我们一起，用技术创造未来吗？我们将提供多样化的工作方式和成长机会。</value>
  </data>

  <!-- Cookie Policy Resources -->
  <data name="CookiePolicy_Title" xml:space="preserve">
    <value>Cookie政策</value>
  </data>
  <data name="CookiePolicy_Message" xml:space="preserve">
    <value>本网站使用Cookie来改善您的浏览体验。继续使用本网站即表示您同意我们使用Cookie。</value>
  </data>
  <data name="CookiePolicy_Accept" xml:space="preserve">
    <value>接受</value>
  </data>
  <data name="CookiePolicy_Decline" xml:space="preserve">
    <value>拒绝</value>
  </data>
  <data name="CookiePolicy_LearnMore" xml:space="preserve">
    <value>了解更多</value>
  </data>

  <!-- FAQ 组件前端文本 -->
  <data name="SearchFAQs" xml:space="preserve">
    <value>搜索常见问题...</value>
  </data>
  <data name="AllCategories" xml:space="preserve">
    <value>全部分类</value>
  </data>
  <data name="PopularQuestions" xml:space="preserve">
    <value>热门问题</value>
  </data>
  <data name="NoFAQsFound" xml:space="preserve">
    <value>未找到相关问题</value>
  </data>
  <data name="NoFAQsFoundDescription" xml:space="preserve">
    <value>请尝试使用不同的关键词搜索或浏览其他分类</value>
  </data>

  <!-- Customer Cases 组件 -->
  <data name="CustomerCases_Title" xml:space="preserve">
    <value>客户案例</value>
  </data>
  <data name="CustomerCases_Subtitle" xml:space="preserve">
    <value>成功案例</value>
  </data>
  <data name="AllIndustries" xml:space="preserve">
    <value>全部行业</value>
  </data>
  <data name="IndustryFilter" xml:space="preserve">
    <value>行业筛选</value>
  </data>
  <data name="ViewCaseStudy" xml:space="preserve">
    <value>查看案例详情</value>
  </data>
  <data name="ViewAllCases" xml:space="preserve">
    <value>查看全部案例</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>了解更多</value>
  </data>
  <data name="NoCasesAvailable" xml:space="preserve">
    <value>暂无案例</value>
  </data>
  <data name="NoCasesDescription" xml:space="preserve">
    <value>客户案例正在完善中，敬请期待</value>
  </data>

  <!-- HowToDo Component -->
  <data name="HowToDo_Title" xml:space="preserve">
    <value>操作流程</value>
  </data>
  <data name="HowToDo_Subtitle" xml:space="preserve">
    <value>使用指南</value>
  </data>
  <data name="GetStarted" xml:space="preserve">
    <value>开始使用</value>
  </data>
  <data name="NoStepsAvailable" xml:space="preserve">
    <value>暂无步骤</value>
  </data>
  <data name="NoStepsDescription" xml:space="preserve">
    <value>操作步骤正在完善中，敬请期待</value>
  </data>

</root>