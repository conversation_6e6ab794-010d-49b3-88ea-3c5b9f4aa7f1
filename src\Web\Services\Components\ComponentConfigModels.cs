using System.Collections.Generic;
using MlSoft.Sites.Web.ViewModels.Components;

namespace MlSoft.Sites.Web.Services.Components
{

    public class ComponentInfo
    {
        public string Id { get; set; } = string.Empty;
        public Dictionary<string, string> Names { get; set; } = new();
        public Dictionary<string, string> Descriptions { get; set; } = new();

        public List<ComponentVariant> Variants { get; set; } = new();

    }


    /// <summary>
    /// 组件变体数据模型（统一模型）
    /// </summary>
    public class ComponentVariant
    {
        /// <summary>
        /// 所属组件Id
        /// </summary>
        public string ComponentId { get; set; } = string.Empty;

        /// <summary>
        /// 变体Id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        public Dictionary<string, string> Names { get; set; } = new();
        public Dictionary<string, string> Descriptions { get; set; } = new();
        public List<FormField>? FormFields { get; set; } = new();

        public object? DefaultData { get; set; }
    }

    /// <summary>
    /// 组件变体选项
    /// </summary>
    public class ComponentVariantOption
    {
        /// <summary>
        /// 变体ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 变体名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 变体描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    // 表单字段配置模型
    public class FormField
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public FormFieldDisplay Display { get; set; } = new();
        public FormFieldValidation Validation { get; set; } = new();
        public FileUploadConfig FileConfig { get; set; } = new();
        public List<SelectOption> Options { get; set; } = new();
        public object DefaultValue { get; set; } = null;
        public Dictionary<string, object> EditorConfig { get; set; } = new();

        // 添加对group和repeater字段类型的支持
        public List<FormField>? Fields { get; set; } // 用于group类型的子字段
        public FormFieldTemplate? Template { get; set; } // 用于repeater类型的模板
        public int? MinItems { get; set; } // repeater最小项目数
        public int? MaxItems { get; set; } // repeater最大项目数
    }

    // 表单字段模板配置（用于repeater）
    public class FormFieldTemplate
    {
        public List<FormField> Fields { get; set; } = new();
    }

    public class FormFieldDisplay
    {
        public string Group { get; set; } = string.Empty;
        public string Width { get; set; } = "col-span-12";
        public int Order { get; set; }
        public bool Collapsed { get; set; }
        public string HelpText { get; set; } = string.Empty;
        public string Layout { get; set; } = "default";
        public int? Rows { get; set; }
        public int? Cols { get; set; }
    }

    public class FormFieldValidation
    {
        public bool Required { get; set; }
        public int? MinLength { get; set; }
        public int? MaxLength { get; set; }
        public int? Min { get; set; }
        public int? Max { get; set; }
        public string Pattern { get; set; } = string.Empty;
        public string CustomValidationMessage { get; set; } = string.Empty;
    }

    public class FileUploadConfig
    {
        public string Folder { get; set; } = string.Empty;
        public List<string> Types { get; set; } = new();
        public string MaxSize { get; set; } = string.Empty;
        public bool Multiple { get; set; }
        public bool Preview { get; set; } = true;
        public CropConfig Crop { get; set; } = new();
    }

    public class CropConfig
    {
        public string AspectRatio { get; set; } = string.Empty;
        public int? MinWidth { get; set; }
        public int? MinHeight { get; set; }
    }

    public class SelectOption
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public bool Selected { get; set; }
    }



}