using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.VisualBasic.FileIO;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Extensions;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System;
using System.Buffers.Text;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Services.Components
{
    /// <summary>
    /// 组件配置服务实现
    /// </summary>
    public class ComponentConfigService : IComponentConfigService
    {
        private readonly IMemoryCache _cache;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ComponentConfigService> _logger;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly IStringLocalizer<FormResource> _localizerForm;
        private readonly string _componentsPath;

        private readonly CompanyService _companyService;
        private readonly SiteSettingsService _siteSettingsService;

        private readonly string _defaultLanguage;

        /// <summary>
        /// 全局组件信息
        /// </summary>
        public const string CACHE_ALL_COMPONENTS = "cache_all_components";

        /// <summary>
        /// 全局站点信息
        /// </summary>
        public const string CACHE_GLOBAL_SITEINFO = "cache_global_siteinfo";

        // 静态内存缓存
        private static readonly object _lockObject = new object();

        public ComponentConfigService(
        IMemoryCache cache,
        IConfiguration configuration,
        ILogger<ComponentConfigService> logger,
        IWebHostEnvironment webHostEnvironment,
        CompanyService companyService,
        SiteSettingsService siteSettingsService,
        IStringLocalizer<FormResource> localizerForm,
        IStringLocalizer<SharedResource> localizer)
        {
            _cache = cache;
            _configuration = configuration;
            _logger = logger;
            _webHostEnvironment = webHostEnvironment;
            _localizer = localizer;
            _localizerForm = localizerForm;
            _companyService = companyService;
            _siteSettingsService = siteSettingsService;

            _componentsPath = Path.Combine(_webHostEnvironment.ContentRootPath, "Views", "Shared", "Components");
            _defaultLanguage = configuration.GetValue<string>("DefaultLanguage") ?? "en";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<SiteInfo?> GetGlobalSiteInfo()
        {
            if (_cache.TryGetValue(CACHE_GLOBAL_SITEINFO, out SiteInfo? info) && info != null)
            {
                return info;
            }

            var siteSettings = await _siteSettingsService.FindOneAsync(x => true);
            var companyInfo = await _companyService.FindOneAsync(x => true);

            if (siteSettings != null && companyInfo != null)
            {
                // 加载组件并缓存
                lock (_lockObject)
                {
                    info = new SiteInfo();

                    // SiteSettings
                    info.SiteNames = siteSettings.Locale.ToDictionary(d => d.Key, d => d.Value?.SiteTitle ?? "");
                    info.CountCode = siteSettings.CountCode ?? "";
                    info.Domain = siteSettings.CustomSettings.ContainsKey("Domain") ? siteSettings.CustomSettings["Domain"] : "";

                    if (!string.IsNullOrEmpty(siteSettings.LogoUrl))
                    {
                        info.LogoUrl = $"/upload/{EnumFolderType.BasicInfo}/{siteSettings.LogoUrl}";
                    }
                    else
                    {
                        info.LogoUrl = "";
                    }

                    // Company contact
                    info.CompanyNames = companyInfo.Locale?.ToDictionary(d => d.Key, d => d.Value.CompanyName ?? "") ?? new Dictionary<string, string>();
                    info.Phone = companyInfo.ContactInfo?.Phone ?? "";
                    info.Email = companyInfo.ContactInfo?.Email ?? "";
                    info.Fax = companyInfo.ContactInfo?.Fax ?? "";
                    info.PostalCode = companyInfo.ContactInfo?.PostalCode ?? "";
                    info.Website = companyInfo.ContactInfo?.Website ?? "";
                    info.Address = companyInfo.ContactInfo?.Locale?.ToDictionary(d => d.Key, d => d.Value.Address ?? "") ?? new Dictionary<string, string>();



                    // 缓存到IMemoryCache
                    _cache.Set(CACHE_GLOBAL_SITEINFO, info, TimeSpan.FromDays(10));

                    return info;
                }
            }

            return null;
        }

        public async Task<List<ComponentVariantOption>> GetComponentVariants(string componentId, string language)
        {
            var component = await GetComponent(componentId);
            if (component != null && component.Variants != null)
            {
                return component.Variants.Select(x => new ComponentVariantOption()
                {
                    Id = x.Id,
                    Name = x.Names[language],
                    Description = x.Descriptions[language]
                }).ToList();
            }
            return new List<ComponentVariantOption>();
        }
        public async Task<List<string>> GetComponentMultilingualFields(string componentId, string variantId)
        {
            var multilingualFields = new List<string>();

            var variant = await GetVariant(componentId, variantId);

            if (variant != null)
            {
                // 从 formFields 中提取多语言字段
                if (variant.FormFields != null && variant.FormFields.Any())
                {
                    foreach (var field in variant.FormFields)
                    {
                        // 检查是否是多语言类型的字段
                        if (!string.IsNullOrEmpty(field.Name) &&
                            (field.Type == "multilingual-text" || field.Type == "multilingual-textarea"))
                        {
                            multilingualFields.Add(field.Name);
                        }
                    }
                }
            }

            return multilingualFields;
        }


        private async Task<List<string>> GetMultilingualFields(List<FormField> formFields)
        {
            var multilingualFields = new List<string>();


            // 从 formFields 中提取多语言字段
            if (formFields != null && formFields.Any())
            {
                foreach (var field in formFields)
                {
                    // 检查是否是多语言类型的字段
                    if (!string.IsNullOrEmpty(field.Name) &&
                        (field.Type == "multilingual-text" || field.Type == "multilingual-textarea" || field.Type == "multilingual-richtext"))
                    {
                        multilingualFields.Add(field.Name);
                    }
                    else if (field.Template != null && field.Template.Fields != null && field.Template.Fields.Any())
                    {
                        var tempmufieds =await  GetMultilingualFields(field.Template.Fields);
                        if(tempmufieds != null && tempmufieds.Any())
                        {
                            multilingualFields.AddRange(tempmufieds);
                        }
                    }
                }
            }

            return multilingualFields;
        }

        public async Task<List<string>> GetComponentMultilingualFields(ComponentVariant variant)
        {
            var multilingualFields = new List<string>();


            // 从 formFields 中提取多语言字段
            if (variant.FormFields != null && variant.FormFields.Any())
            {
                multilingualFields = await GetMultilingualFields(variant.FormFields);
            }

            return multilingualFields.Distinct().ToList();
        }
        public async Task<List<ComponentInfo>> GetAllComponents()
        {
            if (_cache.TryGetValue(CACHE_ALL_COMPONENTS, out List<ComponentInfo>? components) && components != null)
            {
                return components;
            }

            // 加载组件并缓存
            lock (_lockObject)
            {
                components = LoadAllComponents();

                // 缓存到IMemoryCache
                _cache.Set(CACHE_ALL_COMPONENTS, components, TimeSpan.FromDays(10));

                return components;
            }
        }

        public async Task<ComponentInfo> GetComponent(string id)
        {
            var components = await GetAllComponents();
            if (components != null)
            {
                return components.FirstOrDefault(x => x.Id == id);
            }

            _logger.LogWarning("Component not found: {ComponentId}", id);
            throw new ArgumentException($"Component '{id}' not found", nameof(id));
        }

        public async Task<ComponentVariant> GetVariant(string componentId, string variantId)
        {
            var component = await GetComponent(componentId);
            if (component != null)
            {
                return component.Variants.FirstOrDefault(x => x.Id == variantId);
            }

            _logger.LogWarning("Variant not found: {ComponentId}_{VariantId}", componentId, variantId);
            throw new ArgumentException($"Variant '{componentId}_{variantId}' not found");
        }


        public async Task<JObject> GetAdaptedComponentDataAsync(string componentId, string variantId, string culture, JObject sourceData)
        {
            try
            {
                var config = await GetVariant(componentId, variantId);
                if (config == null)
                {
                    _logger.LogWarning("No config found for component {ComponentName}_{Variant}", componentId, variantId);
                    return sourceData;
                }

                var multilingualFields = await GetComponentMultilingualFields(config);
                return AdaptMultilingualFields(sourceData, culture, multilingualFields);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adapting component data: {ComponentName}_{Variant}", componentId, variantId);
                return sourceData;
            }
        }

        private string ReplaceCulture(string value, string culture)
        {
            var ret = value;
            if (ret.Contains("{culture}"))
            {
                if (culture != _defaultLanguage)
                {
                    ret = ret.Replace("/{culture}/", $"/{culture}/").Replace("{culture}", $"{culture}");
                }
                else
                {
                    ret = ret.Replace("/{culture}/", "/").Replace("{culture}", "");
                }
            }

            return ret;
        }

        public JObject AdaptMultilingualFields(JObject data, string culture, List<string> multilingualFields)
        {
            try
            {
                var adaptedData = new JObject();

                foreach (var property in data.Properties())
                {
                    var propertyName = property.Name;
                    var propertyValue = property.Value;

                    if (multilingualFields.Contains(propertyName))
                    {
                        // 多语言字段处理
                        if (propertyValue is JObject multilingualObject)
                        {
                            // 优先使用指定文化的值
                            if (multilingualObject[culture] != null)
                            {
                                var cultureValue = multilingualObject[culture];
                                // 如果是字符串类型，处理 {culture} 占位符
                                if (cultureValue?.Type == JTokenType.String)
                                {
                                    var stringValue = cultureValue.ToString();
                                    adaptedData[propertyName] = ReplaceCulture(stringValue, culture);
                                }
                                else
                                {
                                    adaptedData[propertyName] = cultureValue;
                                }
                            }
                        }
                        else
                        {
                            // 如果多语言字段不是对象，直接使用值（向后兼容）
                            // 如果是字符串类型，处理 {culture} 占位符
                            if (propertyValue?.Type == JTokenType.String)
                            {
                                var stringValue = propertyValue.ToString();
                                adaptedData[propertyName] = ReplaceCulture(stringValue, culture);
                            }
                            else
                            {
                                adaptedData[propertyName] = propertyValue;
                            }
                        }
                    }
                    else
                    {
                        if (propertyValue != null && propertyValue is JArray)
                        {
                            var jaRst = new JArray();
                            foreach (JObject jpo in propertyValue)
                            {
                                jaRst.Add(AdaptMultilingualFields(jpo, culture, multilingualFields));
                            }
                            adaptedData[propertyName] = jaRst;
                        }
                        else
                        {
                            // 非多语言字段，检查是否是文件字段
                            var processedValue = ProcessFileField(propertyName, propertyValue);

                            // 如果是字符串类型，处理 {culture} 占位符
                            if (processedValue?.Type == JTokenType.String)
                            {
                                var stringValue = processedValue.ToString();
                                adaptedData[propertyName] = ReplaceCulture(stringValue, culture);
                            }
                            else
                            {
                                adaptedData[propertyName] = processedValue;
                            }
                        }
                    }
                }

                return adaptedData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adapting multilingual fields for culture: {Culture}", culture);
                return data;
            }
        }

        /// <summary>
        /// 处理文件字段，特别处理文件字段
        /// </summary>
        private JToken ProcessFileField(string propertyName, JToken propertyValue)
        {
            // 检查是否可能是文件字段
            var isFileField = propertyName.EndsWith("Image", StringComparison.OrdinalIgnoreCase) ||
                              propertyName.EndsWith("Video", StringComparison.OrdinalIgnoreCase) ||
                              propertyName.EndsWith("File", StringComparison.OrdinalIgnoreCase) ||
                              propertyName.EndsWith("Document", StringComparison.OrdinalIgnoreCase) ||
                              propertyName.EndsWith("Logo", StringComparison.OrdinalIgnoreCase) ||
                              propertyName.EndsWith("Icon", StringComparison.OrdinalIgnoreCase);

            if (isFileField && propertyValue is JArray fileArray)
            {
                // 文件数组，取第一个文件的路径
                if (fileArray.Count > 0)
                {
                    var firstFile = fileArray[0];
                    if (firstFile is JObject fileObject && fileObject["path"] != null)
                    {
                        return fileObject["path"];
                    }
                    else if (firstFile.Type == JTokenType.String)
                    {
                        return firstFile;
                    }
                }
                return "";
            }
            else if (isFileField && propertyValue is JObject fileObject)
            {
                // 单个文件对象，提取路径
                if (fileObject["path"] != null)
                {
                    return fileObject["path"];
                }
                else if (fileObject["filePath"] != null)
                {
                    return fileObject["filePath"];
                }
                else if (fileObject["url"] != null)
                {
                    return fileObject["url"];
                }
            }

            // 非文件字段或其他情况，直接返回原值
            return propertyValue;
        }


        public async Task RefreshComponentsCache()
        {
            lock (_lockObject)
            {
                _logger.LogInformation("Refreshing components cache...");
                _cache.Remove(CACHE_ALL_COMPONENTS);
            }

            // 重新加载
            await GetAllComponents();
            _logger.LogInformation("Components cache refreshed successfully");
        }

        /// <summary>
        /// 从文件系统加载所有组件
        /// </summary>
        private List<ComponentInfo> LoadAllComponents()
        {
            var components = new List<ComponentInfo>();

            try
            {
                if (!Directory.Exists(_componentsPath))
                {
                    _logger.LogWarning("Components directory not found: {Path}", _componentsPath);
                    return components;
                }

                var componentConfigJson = Path.Combine(_componentsPath, "components.json");
                if (!File.Exists(componentConfigJson))
                {
                    _logger.LogWarning("components.json not found: {Path}", _componentsPath);
                }
                else
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true
                    };

                    //
                    var jsonContent = File.ReadAllText(componentConfigJson);
                    var compontsConfig = JsonSerializer.Deserialize<List<ComponentInfo>>(jsonContent, options);
                    if (compontsConfig != null)
                    {
                        foreach (var component in compontsConfig)
                        {
                            try
                            {
                                var variants = LoadComponentVariants(component.Id);
                                if (variants != null)
                                {
                                    component.Variants = variants;
                                    components.Add(component);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error loading component: {componentId}", component.Id);
                            }
                        }
                    }
                }

                _logger.LogInformation("Loaded {Count} components", components.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading components from directory: {Path}", _componentsPath);
            }

            return components;
        }

        /// <summary>
        /// 从指定目录加载单个组件
        /// </summary>
        private List<ComponentVariant> LoadComponentVariants(string componentId)
        {
            var componentPath = Path.Combine(_componentsPath, componentId);
            var jsonFiles = Directory.GetFiles(componentPath, "*.json")
                .Where(file => !Path.GetFileName(file).Equals("variants.json", StringComparison.OrdinalIgnoreCase))
                .ToArray();

            if (jsonFiles.Length == 0)
            {
                _logger.LogDebug("No variant JSON files found for component: {componentId}", componentId);
                return null;
            }

            var variants = new List<ComponentVariant>();

            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };

            foreach (var jsonFile in jsonFiles)
            {
                try
                {
                    var jsonContent = File.ReadAllText(jsonFile);
                    // 直接反序列化为ComponentVariant
                    var variant = JsonSerializer.Deserialize<ComponentVariant>(jsonContent, options);

                    if (variant != null)
                    {
                        variants.Add(variant);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning("Failed to parse variant file {FilePath}: {Message}", jsonFile, ex.Message);
                }
            }



            _logger.LogDebug("Loaded component {ComponentName} with {VariantCount} variants",
                componentId, variants.Count);

            return variants;
        }



        public async Task<ComponentVariant> ProcessVariantLocalization(ComponentVariant variant)
        {
            if (variant == null) return null;

            try
            {
                // 创建副本以避免修改缓存的原对象
                var localizedVariant = JsonSerializer.Deserialize<ComponentVariant>(
                    JsonSerializer.Serialize(variant));

                // 处理表单字段本地化
                ProcessFormFieldsLocalization(localizedVariant);

                return localizedVariant;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing variant localization for {VariantId}", variant.Id);
                return variant;
            }
        }

        private void ProcessFormFieldsLocalization(ComponentVariant variant)
        {
            if (variant.FormFields == null) return;

            foreach (var field in variant.FormFields)
            {
                ProcessSingleFieldLocalization(field);
            }
        }

        private void ProcessSingleFieldLocalization(FormField field)
        {
            // 处理字段标签
            if (!string.IsNullOrEmpty(field.Label))
            {
                var localizedLabel = field.Label.StartsWith("@FormRes") ? _localizerForm[field.Label.Replace("@FormResource:", "")] : _localizer[field.Label.Replace("@SharedResource:", "")];
                if (!localizedLabel.ResourceNotFound)
                {
                    field.Label = localizedLabel.Value;
                }
            }

            // 处理显示属性
            if (field.Display != null)
            {
                // 处理分组名称
                if (!string.IsNullOrEmpty(field.Display.Group))
                {
                    var localizedGroup = field.Display.Group.StartsWith("@FormRes") ? _localizerForm[field.Display.Group.Replace("@SharedResource:", "")] : _localizer[field.Display.Group.Replace("@SharedResource:", "")];
                    if (!localizedGroup.ResourceNotFound)
                    {
                        field.Display.Group = localizedGroup.Value;
                    }
                }

                // 处理帮助文本
                if (!string.IsNullOrEmpty(field.Display.HelpText))
                {
                    var localizedHelp = field.Display.HelpText.StartsWith("@FormRes") ? _localizerForm[field.Display.HelpText.Replace("@FormResource:", "")] : _localizer[field.Display.HelpText.Replace("@SharedResource:", "")];
                    if (!localizedHelp.ResourceNotFound)
                    {
                        field.Display.HelpText = localizedHelp.Value;
                    }
                }
            }

            // 处理选项本地化
            if (field.Options != null)
            {
                foreach (var option in field.Options)
                {
                    if (!string.IsNullOrEmpty(option.Label))
                    {
                        var localizedOption = option.Label.StartsWith("@FormRes") ? _localizerForm[option.Label.Replace("@FormResource:", "")] : _localizer[option.Label.Replace("@SharedResource:", "")];
                        if (!localizedOption.ResourceNotFound)
                        {
                            option.Label = localizedOption.Value;
                        }
                    }
                }
            }

            // 处理组字段的子字段
            if (field.Type == "group" && field.Fields != null)
            {
                foreach (var subField in field.Fields)
                {
                    ProcessSingleFieldLocalization(subField);
                }
            }

            // 处理重复字段的模板字段
            if (field.Type == "repeater" && field.Template?.Fields != null)
            {
                foreach (var templateField in field.Template.Fields)
                {
                    ProcessSingleFieldLocalization(templateField);
                }
            }
        }
    }
}