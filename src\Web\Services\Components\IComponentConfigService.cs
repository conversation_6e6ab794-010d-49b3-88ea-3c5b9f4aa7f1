using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Services
{
    /// <summary>
    /// 组件配置服务接口
    /// </summary>
    public interface IComponentConfigService
    {
        /// <summary>
        /// 获取所有有效的组件，包括组件变体信息
        /// 全部缓存到内存中，其他方法都从内存中读取
        /// </summary>
        /// <returns></returns>
        Task<List<ComponentInfo>> GetAllComponents();

        /// <summary>
        /// 获取指定Id的组件信息，包括其下变体
        /// </summary>
        /// <param name="id">组件Id</param>
        /// <returns></returns>
        Task<ComponentInfo> GetComponent(string id);

        /// <summary>
        /// 获取全局要用的站点信息
        /// </summary>
        /// <returns></returns>
        Task<SiteInfo?> GetGlobalSiteInfo();


        /// <summary>
        /// 获取指定组件Id下的指定变体Id信息
        /// </summary>
        /// <param name="componentId"></param>
        /// <returns></returns>
        Task<ComponentVariant> GetVariant(string componentId, string variantId);

        Task<List<ComponentVariantOption>> GetComponentVariants(string componentId, string language);


        Task<List<string>> GetComponentMultilingualFields(string componentId, string variantId);

        /// <summary>
        /// 更新组件缓存信息
        /// </summary>
        /// <returns></returns>
        Task RefreshComponentsCache();


        Task<ComponentVariant> ProcessVariantLocalization(ComponentVariant variant);


        Task<JObject> GetAdaptedComponentDataAsync(string componentName, string variant, string culture, JObject sourceData);

        JObject AdaptMultilingualFields(JObject data, string culture, List<string> multilingualFields);


    }
}