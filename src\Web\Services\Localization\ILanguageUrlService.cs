using Microsoft.AspNetCore.Http;
using MlSoft.Sites.Model.Configuration;

namespace MlSoft.Sites.Web.Services.Localization
{
    /// <summary>
    /// Service for generating multilingual URLs
    /// </summary>
    public interface ILanguageUrlService
    {
        /// <summary>
        /// Generate URL for the specified language while preserving current route and parameters
        /// </summary>
        string GenerateLanguageUrl(HttpContext context, SupportedLanguage targetLanguage);

        /// <summary>
        /// Get the current language from the request context
        /// </summary>
        SupportedLanguage? GetCurrentLanguage(HttpContext context);
    }
}