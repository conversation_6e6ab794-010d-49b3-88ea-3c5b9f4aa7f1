using Microsoft.AspNetCore.Http;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Web.Utilities;
using System.Globalization;
using System.Linq;

namespace MlSoft.Sites.Web.Services.Localization
{
    /// <summary>
    /// Implementation of language URL service
    /// </summary>
    public class LanguageUrlService : ILanguageUrlService
    {
        private readonly SupportedLanguage[] _supportedLanguages;

        public LanguageUrlService(SupportedLanguage[] supportedLanguages)
        {
            _supportedLanguages = supportedLanguages;
        }

        public string GenerateLanguageUrl(HttpContext context, SupportedLanguage targetLanguage)
        {
            var currentPath = context.Request.Path.Value ?? "/";

            // Remove current language prefix if exists
            var cleanPath = CulturePathHelper.RemoveCulturePrefix(currentPath, _supportedLanguages);

            // Add target language prefix using unified helper
            return CulturePathHelper.BuildUrlWithBaseUrl(targetLanguage, cleanPath, context.Request.QueryString.Value);
        }

        public SupportedLanguage? GetCurrentLanguage(HttpContext context)
        {
            // Use the culture that was set by CultureMiddleware
            var currentCulture = CultureInfo.CurrentCulture.Name;
            return _supportedLanguages.FirstOrDefault(x => x.Culture == currentCulture);
        }
    }
}