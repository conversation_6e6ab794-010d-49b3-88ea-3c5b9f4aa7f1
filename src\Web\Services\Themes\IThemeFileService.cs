using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MlSoft.Sites.Model.Entities.Themes;

namespace MlSoft.Sites.Web.Services.Themes
{
    /// <summary>
    /// 主题文件服务接口
    /// </summary>
    public interface IThemeFileService
    {
        /// <summary>
        /// 获取所有可用主题
        /// </summary>
        /// <returns></returns>
        Task<List<ThemeInfo>> GetAvailableThemesAsync();
        
        /// <summary>
        /// 获取当前激活主题ID
        /// </summary>
        /// <returns></returns>
        Task<string> GetCurrentThemeIdAsync();
        
        /// <summary>
        /// 应用指定主题
        /// </summary>
        /// <param name="themeId">主题ID</param>
        /// <returns></returns>
        Task<bool> ApplyThemeAsync(string themeId);
        
        /// <summary>
        /// 备份当前主题
        /// </summary>
        /// <returns></returns>
        Task<bool> BackupCurrentThemeAsync();
        
        /// <summary>
        /// 恢复主题备份
        /// </summary>
        /// <returns></returns>
        Task<bool> RestoreThemeBackupAsync();
        
        /// <summary>
        /// 验证主题文件是否存在且有效
        /// </summary>
        /// <param name="themeId">主题ID</param>
        /// <returns></returns>
        Task<bool> ValidateThemeAsync(string themeId);
        
        /// <summary>
        /// 获取主题文件内容
        /// </summary>
        /// <param name="themeId">主题ID</param>
        /// <returns></returns>
        Task<string> GetThemeContentAsync(string themeId);
        
        /// <summary>
        /// 重新编译Tailwind CSS（包含主题变量）
        /// </summary>
        /// <returns></returns>
        Task<bool> RebuildTailwindAsync();
        
        /// <summary>
        /// 清理主题文件缓存
        /// </summary>
        /// <returns></returns>
        Task ClearThemeCacheAsync();
    }
}