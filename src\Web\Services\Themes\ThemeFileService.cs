using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Entities.Themes;

namespace MlSoft.Sites.Web.Services.Themes
{
    /// <summary>
    /// 主题配置模型
    /// </summary>
    public class ThemeConfig
    {
        public string Id { get; set; } = string.Empty;
        public Dictionary<string, string> Names { get; set; } = new();
        public Dictionary<string, string> Descriptions { get; set; } = new();
        public string Thumbnail { get; set; } = string.Empty;
    }

    /// <summary>
    /// 主题文件服务实现
    /// </summary>
    public class ThemeFileService : IThemeFileService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<ThemeFileService> _logger;
        private readonly string _themesPath;
        private readonly string _activeThemePath;
        private readonly string _backupPath;
        private static readonly SemaphoreSlim _fileLock = new(1, 1);

        public ThemeFileService(IWebHostEnvironment environment, ILogger<ThemeFileService> logger)
        {
            _environment = environment;
            _logger = logger;
            _themesPath = Path.Combine(_environment.WebRootPath, "css", "themes");
            _activeThemePath = Path.Combine(_environment.WebRootPath, "css", "active-theme.css");
            _backupPath = Path.Combine(_environment.WebRootPath, "css", "theme-backup.css");
            
            // 确保目录存在
            Directory.CreateDirectory(_themesPath);
            Directory.CreateDirectory(Path.GetDirectoryName(_activeThemePath)!);
        }

        public async Task<List<ThemeInfo>> GetAvailableThemesAsync()
        {
            var themes = new List<ThemeInfo>();
            
            try
            {
                var themeFiles = Directory.GetFiles(_themesPath, "*.css");
                
                foreach (var filePath in themeFiles)
                {
                    var fileName = Path.GetFileNameWithoutExtension(filePath);
                    var fileInfo = new FileInfo(filePath);
                    var config = LoadThemeConfig(fileName);
                    
                    var theme = new ThemeInfo
                    {
                        Id = fileName,
                        Name = GetThemeDisplayName(fileName),
                        Description = GetThemeDescription(fileName),
                        PreviewImage = config?.Thumbnail ?? $"/images/themes/{fileName}-preview.jpg",
                        FilePath = filePath,
                        IsBuiltIn = true,
                        CreatedAt = fileInfo.CreationTime,
                        FileSize = fileInfo.Length,
                        ColorPreview = await ExtractColorsFromThemeAsync(filePath),
                        Version = "1.0.0",
                        Author = "MlSoft",
                        IsActive = true
                    };
                    
                    themes.Add(theme);
                }
                
                _logger.LogInformation("Found {Count} available themes", themes.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading available themes");
            }
            
            return themes.OrderBy(t => t.Name).ToList();
        }

        public async Task<string> GetCurrentThemeIdAsync()
        {
            try
            {
                if (!File.Exists(_activeThemePath))
                {
                    // 如果没有激活主题文件，使用默认主题
                    await ApplyThemeAsync("business-blue");
                    return "business-blue";
                }
                
                // 通过文件内容识别当前主题
                var content = await File.ReadAllTextAsync(_activeThemePath);
                var match = Regex.Match(content, @"\/\* (.+) Theme");
                
                if (match.Success)
                {
                    var themeName = match.Groups[1].Value.ToLower().Replace(" ", "-");
                    return ConvertDisplayNameToId(themeName);
                }
                
                return "business-blue"; // 默认主题
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current theme ID");
                return "business-blue";
            }
        }

        public async Task<bool> ApplyThemeAsync(string themeId)
        {
            await _fileLock.WaitAsync();
            
            try
            {
                _logger.LogInformation("Applying theme: {ThemeId}", themeId);
                
                // 1. 验证主题ID安全性
                if (!IsValidThemeId(themeId))
                {
                    _logger.LogWarning("Invalid theme ID: {ThemeId}", themeId);
                    return false;
                }
                
                // 2. 验证主题文件存在
                var themeFilePath = Path.Combine(_themesPath, $"{themeId}.css");
                if (!File.Exists(themeFilePath))
                {
                    _logger.LogError("Theme file not found: {ThemeFilePath}", themeFilePath);
                    return false;
                }
                
                // 3. 备份当前主题
                await BackupCurrentThemeAsync();
                
                // 4. 复制主题文件到激活位置
                File.Copy(themeFilePath, _activeThemePath, true);
                
                // 5. 重新编译Tailwind（如果需要）
                await RebuildTailwindAsync();
                
                // 6. 清理缓存
                await ClearThemeCacheAsync();
                
                _logger.LogInformation("Successfully applied theme: {ThemeId}", themeId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying theme: {ThemeId}", themeId);
                
                // 尝试恢复备份
                await RestoreThemeBackupAsync();
                return false;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task<bool> BackupCurrentThemeAsync()
        {
            try
            {
                if (File.Exists(_activeThemePath))
                {
                    File.Copy(_activeThemePath, _backupPath, true);
                    _logger.LogDebug("Current theme backed up successfully");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error backing up current theme");
                return false;
            }
        }

        public async Task<bool> RestoreThemeBackupAsync()
        {
            try
            {
                if (File.Exists(_backupPath))
                {
                    File.Copy(_backupPath, _activeThemePath, true);
                    _logger.LogInformation("Theme backup restored successfully");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring theme backup");
                return false;
            }
        }

        public async Task<bool> ValidateThemeAsync(string themeId)
        {
            try
            {
                if (!IsValidThemeId(themeId))
                    return false;
                    
                var themeFilePath = Path.Combine(_themesPath, $"{themeId}.css");
                if (!File.Exists(themeFilePath))
                    return false;
                
                var content = await File.ReadAllTextAsync(themeFilePath);
                
                // 验证基本CSS结构
                return content.Contains(":root") && content.Contains("--color-primary");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating theme: {ThemeId}", themeId);
                return false;
            }
        }

        public async Task<string> GetThemeContentAsync(string themeId)
        {
            try
            {
                if (!IsValidThemeId(themeId))
                    return string.Empty;
                    
                var themeFilePath = Path.Combine(_themesPath, $"{themeId}.css");
                if (File.Exists(themeFilePath))
                {
                    return await File.ReadAllTextAsync(themeFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading theme content: {ThemeId}", themeId);
            }
            
            return string.Empty;
        }

        public async Task<bool> RebuildTailwindAsync()
        {
            try
            {
                //// 更新Tailwind输入文件，包含主题变量 input文件已经包了active-theme.css,不用改写
                //var inputCssPath = Path.Combine(_environment.WebRootPath, "css", "input.css");
                //var themeImport = "@import './active-theme.css';\n";
                //var tailwindDirectives = "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Custom components and utilities can be added here */";

                //var newContent = themeImport + tailwindDirectives;
                //await File.WriteAllTextAsync(inputCssPath, newContent);

                // 执行Tailwind编译
                return await ExecuteTailwindBuildAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rebuilding Tailwind");
                return false;
            }
        }

        public async Task ClearThemeCacheAsync()
        {
            try
            {
                // 这里可以实现具体的缓存清理逻辑
                // 比如删除编译缓存、通知CDN刷新等
                _logger.LogDebug("Theme cache cleared");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing theme cache");
            }
        }

        private async Task<bool> ExecuteTailwindBuildAsync()
        {
            try
            {
                var workingDirectory = _environment.ContentRootPath;
                
                // Try different package managers in order of preference
                var packageManagers = new[] { "pnpm", "npm", "yarn" };
                
                foreach (var packageManager in packageManagers)
                {
                    if (await TryExecutePackageManagerAsync(packageManager, workingDirectory))
                    {
                        return true;
                    }
                }
                
                _logger.LogWarning("No suitable package manager found (tried: {PackageManagers})", string.Join(", ", packageManagers));
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing Tailwind build");
                return false;
            }
        }

        private async Task<bool> TryExecutePackageManagerAsync(string packageManager, string workingDirectory)
        {
            try
            {
                var processInfo = CreateProcessStartInfo(packageManager, workingDirectory);

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0)
                    {
                        _logger.LogDebug("Tailwind build completed successfully using {PackageManager}", packageManager);
                        return true;
                    }
                    else
                    {
                        var error = await process.StandardError.ReadToEndAsync();
                        _logger.LogDebug("Tailwind build failed with {PackageManager}: {Error}", packageManager, error);
                    }
                }
                
                return false;
            }
            catch (System.ComponentModel.Win32Exception)
            {
                // Package manager not found, try next one
                _logger.LogDebug("Package manager {PackageManager} not found", packageManager);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error trying package manager {PackageManager}", packageManager);
                return false;
            }
        }

        private ProcessStartInfo CreateProcessStartInfo(string packageManager, string workingDirectory)
        {
            var isWindows = System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Windows);
            
            if (isWindows)
            {
                return new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c {packageManager} run build-css-prod",
                    WorkingDirectory = workingDirectory,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
            }
            else
            {
                // Linux/Ubuntu/macOS
                return new ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"{packageManager} run build-css-prod\"",
                    WorkingDirectory = workingDirectory,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
            }
        }

        private async Task<Dictionary<string, string>> ExtractColorsFromThemeAsync(string filePath)
        {
            var colors = new Dictionary<string, string>();
            
            try
            {
                var content = await File.ReadAllTextAsync(filePath);
                var matches = Regex.Matches(content, @"--color-primary-(\d+):\s*([^;]+);");
                
                foreach (Match match in matches)
                {
                    var colorLevel = match.Groups[1].Value;
                    var colorValue = match.Groups[2].Value.Trim();
                    colors[$"primary-{colorLevel}"] = colorValue;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting colors from theme file: {FilePath}", filePath);
            }
            
            return colors;
        }

        private bool IsValidThemeId(string themeId)
        {
            // 只允许字母、数字、连字符
            return !string.IsNullOrEmpty(themeId) && Regex.IsMatch(themeId, @"^[a-zA-Z0-9\-]+$");
        }

        private string GetThemeDisplayName(string themeId)
        {
            var config = LoadThemeConfig(themeId);
            if (config != null && config.Names.Count > 0)
            {
                var currentCulture = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                
                // 尝试获取当前语言的名称
                if (config.Names.ContainsKey(currentCulture))
                    return config.Names[currentCulture];
                
                // 回退到中文
                if (config.Names.ContainsKey("zh"))
                    return config.Names["zh"];
                
                // 如果都没有，返回第一个可用的名称
                return config.Names.Values.First();
            }
            
            // 回退到原来的硬编码逻辑
            return themeId switch
            {
                "business-blue" => "商务蓝",
                "nature-green" => "自然绿",
                "elegant-purple" => "典雅紫",
                "warm-orange" => "温暖橙",
                _ => themeId.Replace("-", " ").ToTitleCase()
            };
        }

        private string GetThemeDescription(string themeId)
        {
            var config = LoadThemeConfig(themeId);
            if (config != null && config.Descriptions.Count > 0)
            {
                var currentCulture = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                
                // 尝试获取当前语言的描述
                if (config.Descriptions.ContainsKey(currentCulture))
                    return config.Descriptions[currentCulture];
                
                // 回退到中文
                if (config.Descriptions.ContainsKey("zh"))
                    return config.Descriptions["zh"];
                
                // 如果都没有，返回第一个可用的描述
                return config.Descriptions.Values.First();
            }
            
            // 回退到原来的硬编码逻辑
            return themeId switch
            {
                "business-blue" => "专业稳重的企业蓝色主题，适合科技、金融类企业",
                "nature-green" => "清新自然的绿色主题，适合环保、健康类企业",
                "elegant-purple" => "优雅神秘的紫色主题，适合创意、设计类企业",
                "warm-orange" => "活力温暖的橙色主题，适合教育、服务类企业",
                _ => $"自定义主题：{themeId}"
            };
        }

        private ThemeConfig? LoadThemeConfig(string themeId)
        {
            try
            {
                var configPath = Path.Combine(_themesPath, $"{themeId}.json");
                if (File.Exists(configPath))
                {
                    var jsonContent = File.ReadAllText(configPath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    return JsonSerializer.Deserialize<ThemeConfig>(jsonContent, options);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load theme config for {ThemeId}", themeId);
            }
            
            return null;
        }

        private string ConvertDisplayNameToId(string displayName)
        {
            return displayName switch
            {
                "business-blue" => "business-blue",
                "nature-green" => "nature-green",
                "elegant-purple" => "elegant-purple",
                "warm-orange" => "warm-orange",
                _ => displayName.ToLower().Replace(" ", "-")
            };
        }
    }

    /// <summary>
    /// 字符串扩展方法
    /// </summary>
    public static class StringExtensions
    {
        public static string ToTitleCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var words = input.Split(' ');
            for (int i = 0; i < words.Length; i++)
            {
                if (words[i].Length > 0)
                {
                    words[i] = char.ToUpper(words[i][0]) + words[i][1..].ToLower();
                }
            }
            return string.Join(" ", words);
        }
    }
}