using System;
using System.Threading.Tasks;
using MongoDB.Driver;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Entities.Themes;

namespace MlSoft.Sites.Web.Services.Themes
{
    /// <summary>
    /// 主题设置服务接口
    /// </summary>
    public interface IThemeSettingsService
    {
        Task<SiteThemeSettings> GetSiteThemeSettingsAsync(string companyId);
        Task<bool> UpdateSiteThemeAsync(string companyId, string themeId, string updatedBy);
        Task<bool> RecordThemeApplicationAsync(string companyId, string themeId, string appliedBy, bool success, string? errorMessage = null);
    }

    /// <summary>
    /// 主题设置服务实现
    /// </summary>
    public class ThemeSettingsService : IThemeSettingsService
    {
        private readonly IMongoDatabase _database;
        private readonly ILogger<ThemeSettingsService> _logger;
        private readonly IMongoCollection<SiteThemeSettings> _collection;

        public ThemeSettingsService(IMongoDatabase database, ILogger<ThemeSettingsService> logger)
        {
            _database = database;
            _logger = logger;
            _collection = _database.GetCollection<SiteThemeSettings>("SiteThemeSettings");
        }

        public async Task<SiteThemeSettings> GetSiteThemeSettingsAsync(string companyId)
        {
            try
            {
                var settings = await _collection.Find(x => x.CompanyId == companyId).FirstOrDefaultAsync();
                
                if (settings == null)
                {
                    // 创建默认主题设置
                    settings = new SiteThemeSettings
                    {
                        CompanyId = companyId,
                        ActiveThemeId = "business-blue",
                        UpdatedAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedBy = "System",
                        IsEnabled = true
                    };
                    
                    await _collection.InsertOneAsync(settings);
                    _logger.LogInformation("Created default theme settings for company: {CompanyId}", companyId);
                }
                
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting site theme settings for company: {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<bool> UpdateSiteThemeAsync(string companyId, string themeId, string updatedBy)
        {
            try
            {
                var filter = Builders<SiteThemeSettings>.Filter.Eq(x => x.CompanyId, companyId);
                
                var update = Builders<SiteThemeSettings>.Update
                    .Set(x => x.BackupThemeId, await GetCurrentThemeIdAsync(companyId))
                    .Set(x => x.ActiveThemeId, themeId)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow)
                    .Set(x => x.UpdatedBy, updatedBy);

                var options = new UpdateOptions { IsUpsert = true };
                var result = await _collection.UpdateOneAsync(filter, update, options);
                
                _logger.LogInformation("Updated theme for company {CompanyId} to {ThemeId} by {UpdatedBy}", 
                    companyId, themeId, updatedBy);
                
                return result.ModifiedCount > 0 || result.UpsertedId != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating site theme for company: {CompanyId}", companyId);
                return false;
            }
        }

        public async Task<bool> RecordThemeApplicationAsync(string companyId, string themeId, string appliedBy, bool success, string? errorMessage = null)
        {
            try
            {
                var historyEntry = new ThemeApplicationHistory
                {
                    ThemeId = themeId,
                    AppliedAt = DateTime.UtcNow,
                    AppliedBy = appliedBy,
                    Success = success,
                    ErrorMessage = errorMessage
                };

                var filter = Builders<SiteThemeSettings>.Filter.Eq(x => x.CompanyId, companyId);
                var update = Builders<SiteThemeSettings>.Update.Push(x => x.History, historyEntry);

                await _collection.UpdateOneAsync(filter, update);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording theme application history for company: {CompanyId}", companyId);
                return false;
            }
        }

        private async Task<string?> GetCurrentThemeIdAsync(string companyId)
        {
            try
            {
                var settings = await _collection.Find(x => x.CompanyId == companyId).FirstOrDefaultAsync();
                return settings?.ActiveThemeId;
            }
            catch
            {
                return null;
            }
        }
    }
}