using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Routing;
using System;
using System.IO;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.Services
{
    public interface IViewRenderService
    {
        Task<string> RenderPartialViewAsync<TModel>(
            string partialViewName,
            TModel model,
            ViewDataDictionary viewData = null,
            ITempDataDictionary tempData = null
        );
    }

    public class ViewRenderService : IViewRenderService
    {
        private readonly IRazorViewEngine _razorViewEngine;
        private readonly ITempDataProvider _tempDataProvider;
        private readonly IServiceProvider _serviceProvider;

        public ViewRenderService(
            IRazorViewEngine razorViewEngine,
            ITempDataProvider tempDataProvider,
            IServiceProvider serviceProvider)
        {
            _razorViewEngine = razorViewEngine;
            _tempDataProvider = tempDataProvider;
            _serviceProvider = serviceProvider;
        }

        public async Task<string> RenderPartialViewAsync<TModel>(
            string partialViewName,
            TModel model,
            ViewDataDictionary viewData = null,
            ITempDataDictionary tempData = null)
        {
            var httpContext = new DefaultHttpContext { RequestServices = _serviceProvider };
            var actionContext = new ActionContext(httpContext, new RouteData(), new ActionDescriptor());

            using var sw = new StringWriter();

            // Try to find the view using GetView first (for absolute paths)
            var viewResult = _razorViewEngine.FindView(actionContext, partialViewName, false); 

            // If not found, try FindView
            if (!viewResult.Success)
            {
                viewResult = _razorViewEngine.GetView(executingFilePath: null, viewPath: partialViewName, isMainPage: false);
            }

            if (!viewResult.Success)
            {
                var searchedLocations = string.Join(", ", viewResult.SearchedLocations ?? new string[0]);
                throw new FileNotFoundException($"View '{partialViewName}' not found. Searched locations: {searchedLocations}");
            }

            var viewDictionary = new ViewDataDictionary<TModel>(
                new EmptyModelMetadataProvider(),
                new ModelStateDictionary())
            {
                Model = model
            };

            // 合并传入的ViewData
            if (viewData != null)
            {
                foreach (var kvp in viewData)
                {
                    viewDictionary[kvp.Key] = kvp.Value;
                }
            }

            var viewContext = new ViewContext(
                actionContext,
                viewResult.View,
                viewDictionary,
                tempData ?? new TempDataDictionary(actionContext.HttpContext, _tempDataProvider),
                sw,
                new HtmlHelperOptions()
            );

            await viewResult.View.RenderAsync(viewContext);
            return sw.ToString();
        }
    }
}