using Microsoft.AspNetCore.Razor.TagHelpers;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Web.Services.Localization;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using System;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace MlSoft.Sites.Web.TagHelpers
{
    /// <summary>
    /// Simplified language switcher tag helper
    /// </summary>
    [HtmlTargetElement("language-switcher")]
    public class SimplifiedLanguageSwitcherTagHelper : TagHelper
    {
        private readonly ILanguageUrlService _languageUrlService;

        [ViewContext]
        public ViewContext ViewContext { get; set; } = null!;

        [HtmlAttributeName("supported-languages")]
        public SupportedLanguage[] SupportedLanguages { get; set; } = Array.Empty<SupportedLanguage>();

        [HtmlAttributeName("css-class")]
        public string CssClass { get; set; } = "language-switcher";

        [HtmlAttributeName("show-flags")]
        public bool ShowFlags { get; set; } = true;

        [HtmlAttributeName("show-names")]
        public bool ShowNames { get; set; } = true;

        [HtmlAttributeName("dropdown")]
        public bool AsDropdown { get; set; } = false;

        public SimplifiedLanguageSwitcherTagHelper(ILanguageUrlService languageUrlService)
        {
            _languageUrlService = languageUrlService;
        }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            // Get supported languages from ViewData or attribute
            var supportedLangs = ViewContext.ViewData["SupportedLanguages"] as SupportedLanguage[] ?? SupportedLanguages;

            if (supportedLangs == null || supportedLangs.Length == 0)
            {
                output.SuppressOutput();
                return;
            }

            // Get current language using the service
            var currentLanguage = _languageUrlService.GetCurrentLanguage(ViewContext.HttpContext);

            if (AsDropdown)
            {
                output.TagName = null;
                GenerateDropdownSwitcher(output, supportedLangs, currentLanguage);
            }
            else
            {
                output.TagName = "ul";
                output.Attributes.SetAttribute("class", CssClass);
                GenerateListSwitcher(output, supportedLangs, currentLanguage);
            }
        }

        private void GenerateListSwitcher(TagHelperOutput output, SupportedLanguage[] supportedLangs, SupportedLanguage? currentLanguage)
        {
            var content = "";

            foreach (var language in supportedLangs)
            {
                var isActive = currentLanguage?.Code == language.Code;
                var url = _languageUrlService.GenerateLanguageUrl(ViewContext.HttpContext, language);

                var activeClass = isActive ? "bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-300" : "text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white";
                var flagEmoji = ShowFlags && !string.IsNullOrEmpty(language.Emoji) ? language.Emoji + " " : "";
                var languageName = ShowNames ? language.Name : "";

                content += $@"<li>
                    <a href=""{url}"" class=""block px-3 py-2 rounded-lg {activeClass} transition-colors duration-200"" data-language=""{language.Code}"">
                        {flagEmoji}{languageName}
                    </a>
                </li>";
            }

            output.Content.SetHtmlContent(content);
        }

        private void GenerateDropdownSwitcher(TagHelperOutput output, SupportedLanguage[] supportedLangs, SupportedLanguage? currentLanguage)
        {
            var defaultLanguage = supportedLangs.First();
            var displayLanguage = currentLanguage ?? defaultLanguage;

            var currentFlag = ShowFlags && !string.IsNullOrEmpty(displayLanguage.Emoji) ? displayLanguage.Emoji + " " : "";
            var currentName = ShowNames ? displayLanguage.Name : displayLanguage.Code;

            var content = $@"
            <div class=""{CssClass}"">
                <button id=""languageDropdownButton"" data-dropdown-toggle=""languageDropdown""
                        class=""focus:outline-none focus:ring-4 rounded-lg text-sm px-3 py-2.5 inline-flex items-center border bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100 focus:ring-primary-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 dark:focus:ring-primary-800""
                        type=""button"">
                    {currentFlag}{currentName}
                    <svg class=""w-2.5 h-2.5 ms-2.5"" aria-hidden=""true"" xmlns=""http://www.w3.org/2000/svg"" fill=""none"" viewBox=""0 0 10 6"">
                        <path stroke=""currentColor"" stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""m1 1 4 4 4-4""/>
                    </svg>
                </button>

                <div id=""languageDropdown"" class=""z-10 hidden divide-y rounded-lg shadow w-44 border bg-white border-gray-200 dark:bg-gray-700 dark:border-gray-600"">
                    <ul class=""py-2 text-sm text-gray-700 dark:text-gray-200"" aria-labelledby=""languageDropdownButton"">";

            foreach (var language in supportedLangs)
            {
                if (language.Code == currentLanguage?.Code) continue;

                var url = _languageUrlService.GenerateLanguageUrl(ViewContext.HttpContext, language);
                var flagEmoji = ShowFlags && !string.IsNullOrEmpty(language.Emoji) ? language.Emoji + " " : "";
                var languageName = ShowNames ? language.Name : "";

                content += $@"
                        <li>
                            <a href=""{url}"" class=""block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white rounded"" data-language=""{language.Code}"">
                                {flagEmoji}{languageName}
                            </a>
                        </li>";
            }

            content += @"
                    </ul>
                </div>
            </div>";

            output.Content.SetHtmlContent(content);
        }
    }
}