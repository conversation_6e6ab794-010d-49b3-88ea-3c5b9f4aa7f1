using MlSoft.Sites.Model.Configuration;
using System.Linq;

namespace MlSoft.Sites.Web.Utilities
{
    /// <summary>
    /// Utility class for handling culture-related path operations
    /// </summary>
    public static class CulturePathHelper
    {
        /// <summary>
        /// Extract culture code from URL path segments
        /// </summary>
        /// <param name="path">URL path (e.g., "/zh/home/<USER>")</param>
        /// <param name="supportedLanguages">Array of supported languages</param>
        /// <returns>Culture code if found, null otherwise</returns>
        public static string? ExtractCultureFromPath(string? path, SupportedLanguage[] supportedLanguages)
        {
            if (string.IsNullOrEmpty(path) || path.Length <= 1)
                return null;

            var segments = path.TrimStart('/').Split('/');
            if (segments.Length == 0)
                return null;

            var potentialCultureCode = segments[0];
            return supportedLanguages.Any(x => x.Code == potentialCultureCode) ? potentialCultureCode : null;
        }

        /// <summary>
        /// Remove culture prefix from URL path
        /// </summary>
        /// <param name="path">URL path (e.g., "/zh/home/<USER>")</param>
        /// <param name="supportedLanguages">Array of supported languages</param>
        /// <returns>Path without culture prefix (e.g., "/home/<USER>")</returns>
        public static string RemoveCulturePrefix(string? path, SupportedLanguage[] supportedLanguages)
        {
            if (string.IsNullOrEmpty(path) || path == "/")
                return "/";

            var segments = path.TrimStart('/').Split('/');
            if (segments.Length == 0)
                return "/";

            var firstSegment = segments[0];
            // If first segment is a supported language code, remove it
            if (supportedLanguages.Any(x => x.Code == firstSegment))
            {
                var remainingPath = "/" + string.Join("/", segments.Skip(1));
                return remainingPath == "/" ? "/" : remainingPath;
            }

            return path;
        }

        /// <summary>
        /// Check if the given path should skip culture processing (static resources, etc.)
        /// </summary>
        /// <param name="path">URL path</param>
        /// <returns>True if should skip processing</returns>
        public static bool ShouldSkipCultureProcessing(string? path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            // Skip common static file extensions
            var staticExtensions = new[] { ".css", ".js", ".png","webp", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".map", ".woff", ".woff2", ".ttf", ".eot" };
            if (staticExtensions.Any(ext => path.EndsWith(ext, System.StringComparison.OrdinalIgnoreCase)))
                return true;

            // Skip well-known paths
            if (path.StartsWith("/.well-known/", System.StringComparison.OrdinalIgnoreCase))
                return true;

            // Skip API paths if any
            if (path.StartsWith("/api/", System.StringComparison.OrdinalIgnoreCase))
                return true;

            return false;
        }

        /// <summary>
        /// Build URL with language-specific BaseUrl (unified logic)
        /// </summary>
        /// <param name="targetLanguage">Target language configuration</param>
        /// <param name="actionUrl">Action URL path</param>
        /// <param name="queryString">Query string (optional)</param>
        /// <returns>Complete URL with language prefix</returns>
        public static string BuildUrlWithBaseUrl(SupportedLanguage targetLanguage, string actionUrl, string? queryString = null)
        {
            var baseUrl = targetLanguage.BaseUrl.TrimEnd('/');

            // If BaseUrl is absolute URL (contains protocol), directly concatenate
            if (targetLanguage.BaseUrl.StartsWith("http://") || targetLanguage.BaseUrl.StartsWith("https://"))
            {
                return $"{baseUrl}{actionUrl}{queryString}";
            }

            // Handle relative path
            if (baseUrl == "" || baseUrl == "/")
            {
                return $"{actionUrl}{queryString}"; // Default language, return original path
            }

            // Ensure actionUrl starts with /
            if (!actionUrl.StartsWith("/"))
            {
                actionUrl = "/" + actionUrl;
            }

            return $"{baseUrl}{actionUrl}{queryString}";
        }
    }
}