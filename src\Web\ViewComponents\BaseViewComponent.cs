﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Extensions;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Linq;


namespace MlSoft.Sites.Web.ViewComponents
{
    public abstract class BaseViewComponent : ViewComponent
    {
        private readonly IComponentConfigService _componentConfigService;
        private readonly ILogger<BaseViewComponent> _logger;

        public BaseViewComponent(IComponentConfigService componentConfigService,
            ILogger<BaseViewComponent> logger)
        {
            _componentConfigService = componentConfigService;
            _logger = logger;
        }


        public string BuildMultilingualUrl(string url)
        {
            var culture = ViewData["CurrentLanguage"].ToString();
            var defaultCulture = ViewData["DefaultLanguage"].ToString();

            if (culture != defaultCulture)
            {
                url = $"/{culture}{url}";
            }

            return url;
        }

        protected async Task<IViewComponentResult> InvokeViewAsync<T>(object model, string componentId, string variant = "Default")
        {
            JObject jObjectData = new JObject();
            T? componentModel = default;
            try
            {
                // 获取当前文化
                var culture = ViewData["CurrentLanguage"].ToString();


                // 检查模型类型
                if (model is JsonElement jsonElement)
                {
                    var sourceJObject = JObject.FromObject(model);

                    // 直接使用JSON模式
                    jObjectData = await _componentConfigService.GetAdaptedComponentDataAsync(componentId, variant, culture, sourceJObject);
                }
                else if (model is JObject jObject)
                {
                    // 直接使用JObject
                    jObjectData = await _componentConfigService.GetAdaptedComponentDataAsync(componentId, variant, culture, jObject);
                }

                _logger.LogDebug("Rendering {ComponentId} component with variant: {Variant}, culture: {Culture}", componentId, variant, culture);

                // 转换嵌套属性的JSON数据
                var normalizedData = NormalizeNestedProperties(jObjectData);
                componentModel = normalizedData.ToObject<T>();


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rendering  {ComponentId}  component with variant: {Variant}", componentId, variant);
            }

            return View(variant, componentModel);
        }

        /// <summary>
        /// 统一的调用方法 - 支持JSON和ViewModel模式
        /// </summary>
        protected async Task<IViewComponentResult> InvokeViewAsync(object model, string componentId, string variant = "Default")
        {
            JObject jObjectData = new JObject();

            try
            {
                // 获取当前文化
                var culture = ViewData["CurrentLanguage"].ToString();



                // 检查模型类型
                if (model is JsonElement jsonElement)
                {
                    var sourceJObject = JObject.FromObject(model);

                    // 直接使用JSON模式
                    jObjectData = await _componentConfigService.GetAdaptedComponentDataAsync(componentId, variant, culture, sourceJObject);
                }
                else if (model is JObject jObject)
                {
                    // 直接使用JObject
                    jObjectData = await _componentConfigService.GetAdaptedComponentDataAsync(componentId, variant, culture, jObject);
                }

                _logger.LogDebug("Rendering {ComponentId} component with variant: {Variant}, culture: {Culture}", componentId, variant, culture);


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rendering  {ComponentId}  component with variant: {Variant}", componentId, variant);
            }

            return View(variant, jObjectData);
        }

        /// <summary>
        /// 将点号表示法的属性转换为嵌套对象结构
        /// 例如: "Settings.AutoPlay" -> { "Settings": { "AutoPlay": value } }
        /// </summary>
        private JObject NormalizeNestedProperties(JObject source)
        {
            var result = new JObject();

            foreach (var property in source.Properties().ToList())
            {
                var propertyName = property.Name;
                var propertyValue = property.Value;

                if (propertyName.Contains('.'))
                {
                    // 处理嵌套属性
                    var parts = propertyName.Split('.');
                    var current = result;

                    // 遍历路径，创建嵌套结构
                    for (int i = 0; i < parts.Length - 1; i++)
                    {
                        var part = parts[i];
                        if (current[part] == null)
                        {
                            current[part] = new JObject();
                        }
                        current = (JObject)current[part];
                    }

                    // 设置最终值
                    current[parts[parts.Length - 1]] = propertyValue;
                }
                else
                {
                    // 直接属性
                    result[propertyName] = propertyValue;
                }
            }

            return result;
        }

    }
}
