using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class BreadcrumbViewComponent : BaseViewComponent
    {
        private const string componentId = "Breadcrumb";


        private readonly IComponentConfigService _componentConfigService;
        public BreadcrumbViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<BreadcrumbViewComponent> logger) : base(componentConfigService, logger)
        {
            _componentConfigService = componentConfigService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            var culture = ViewData["CurrentLanguage"]?.ToString() ?? "en";
            // 自动添加首页，中间的从Items取
            var breadcrumbMode = ((JObject)model).ToObject<BreadcrumbComponentViewModel>();

            var items = new List<BreadcrumbItem>();
            // 添加首页

            var homeItem = new BreadcrumbItem
            {
                Text = SharedResource.Home,
                Url = BuildMultilingualUrl("/")
            };

            items.Add(homeItem);

            if (breadcrumbMode.Items != null && breadcrumbMode.Items.Count > 0)
            {
                items.AddRange(breadcrumbMode.Items);
            }

            items.Add(new BreadcrumbItem()
            {
                Text = ViewData["PageName"]?.ToString(),
                Url = ""
            });

            breadcrumbMode.Items = items;

            return View(variant, breadcrumbMode);
        }
    }
}