using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.CSR;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CSRSectionViewComponent : BaseViewComponent
    {
        private const string componentId = "CSRSection";

        private readonly IComponentConfigService _componentConfigService;
        private readonly CSRActivityService _csrActivityService;
        private readonly IStringLocalizer<AdminResource> _adminRes;

        public CSRSectionViewComponent(
            IComponentConfigService componentConfigService,
            CSRActivityService csrActivityService,
            IStringLocalizer<AdminResource> adminRes,
        ILogger<CSRSectionViewComponent> logger) : base(componentConfigService, logger)
        {
            _componentConfigService = componentConfigService;
            _csrActivityService = csrActivityService;
            _adminRes = adminRes;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            var viewModel = ((JObject)model).ToObject<CSRSectionComponentViewModel>();

            var culture = ViewData["CurrentLanguage"]?.ToString() ?? "en";


            if(viewModel.Initiatives == null || viewModel.Initiatives.Count == 0)
            {
                var activities = await _csrActivityService.GetActiveActivitiesAsync(4);
                foreach(var activity in activities)
                {
                    var locale = activity.Locale != null && activity.Locale.ContainsKey(culture) ? activity.Locale[culture] : null;
                    if (locale != null)
                    {
                        var csrInitiativeItem = new CSRInitiativeItem();

                        var icon = "";
                        var title = "";

                        switch(activity.Category)
                        {
                            case Model.Entities.Enums.CSRCategory.Environment:
                                icon = "leaf";
                                title = _adminRes["Environment"];
                                break;
                            case Model.Entities.Enums.CSRCategory.Education:
                                title = _adminRes["Education"];
                                icon = "book";
                                break;
                            case Model.Entities.Enums.CSRCategory.Governance:
                                title = _adminRes["Governance"];
                                icon = "scale-balanced";
                                break;
                            case Model.Entities.Enums.CSRCategory.SocialContribution:
                                title = _adminRes["SocialContribution"];
                                icon = "hand-holding-heart";
                                break;
                            case Model.Entities.Enums.CSRCategory.EmployeeWelfare:
                                title = _adminRes["EmployeeWelfare"];
                                icon = "user-shield";
                                break;
                            case Model.Entities.Enums.CSRCategory.CommunitySupport:
                                title = _adminRes["CommunitySupport"];
                                icon = "people-group";
                                break;
                            case Model.Entities.Enums.CSRCategory.DisasterRelief:
                                title = _adminRes["DisasterRelief"];
                                icon = "kit-medical";
                                break;
                            default:
                                icon = "handshake";
                                break;
                        }

                        csrInitiativeItem.Icon = icon;
                        csrInitiativeItem.Title = title;
                        csrInitiativeItem.Description = locale.Title;



                        viewModel.Initiatives.Add(csrInitiativeItem);
                    }


                   
                }
            }




            return View(variant, viewModel);
        }
    }
}