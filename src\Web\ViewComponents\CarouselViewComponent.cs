using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CarouselViewComponent : BaseViewComponent
    {
        public CarouselViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<CarouselViewComponent> logger) : base(componentConfigService, logger)
        {
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            return await base.InvokeViewAsync<CarouselComponentViewModel>(model, "Carousel", variant);
        }
    }
}