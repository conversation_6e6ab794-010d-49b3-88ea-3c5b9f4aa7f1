using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewComponents;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.Organization;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.ComponentModel;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CompanyBasicInfoViewComponent : BaseViewComponent
    {
        private const string componentId = "CompanyBasicInfoView";
        private readonly CompanyService _companyService;
        private readonly ExecutiveService _executiveService;
        private readonly IComponentConfigService _componentConfigService;
        public CompanyBasicInfoViewComponent(
            IComponentConfigService componentConfigService,
            CompanyService companyService,
            ExecutiveService executiveService,
            ILogger<CompanyBasicInfoViewComponent> logger) : base(componentConfigService, logger)
        {
            _companyService = companyService;
            _executiveService = executiveService;
            _componentConfigService = componentConfigService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {

            var viewModel = ((JObject)model).ToObject<CompanyBasicInfoComponentViewModel>();
            // 从数据库获取公司信息和社长信息
            viewModel.CompanyData = await _companyService.GetCompany();
            viewModel.PresidentData = await _executiveService.GetPresidentAsync();

            return View(variant, viewModel);
        }
    }
}