using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewComponents;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.History;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CompanyHistoryViewComponent : BaseViewComponent
    {
        private const string componentId = "CompanyHistoryView";

        private readonly CompanyHistoryService _companyHistoryService;
        private readonly IComponentConfigService _componentConfigService;

        public CompanyHistoryViewComponent(
            IComponentConfigService componentConfigService,
            CompanyHistoryService companyHistoryService,
            ILogger<CompanyHistoryViewComponent> logger) : base(componentConfigService, logger)
        {
            _companyHistoryService = companyHistoryService;
            _componentConfigService = componentConfigService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {

            var viewModel = ((JObject)model).ToObject<CompanyHistoryComponentViewModel>();


            // 从数据库获取历史数据
            var historyData = await _companyHistoryService.GetHistoryOrderedAsync();
            viewModel.HistoryData = historyData.ToList();

            return View(variant, viewModel);
        }
    }
}