using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewComponents;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CompanyLocationsViewComponent : BaseViewComponent
    {
        private const string componentId = "CompanyLocations";

        private readonly CompanyLocationService _companyLocationService;
        private readonly IComponentConfigService _componentConfigService;

        public CompanyLocationsViewComponent(
            IComponentConfigService componentConfigService,
            CompanyLocationService companyLocationService,
            ILogger<CompanyLocationsViewComponent> logger) : base(componentConfigService, logger)
        {
            _companyLocationService = companyLocationService;
            _componentConfigService = componentConfigService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            var viewModel = ((JObject)model).ToObject<CompanyLocationsComponentViewModel>();

            // 从数据库获取据点数据
            var locationData = await _companyLocationService.GetLocationsOrderedAsync();
            viewModel.Locations = locationData.ToList();


            return View(variant, viewModel);
        }
    }
}