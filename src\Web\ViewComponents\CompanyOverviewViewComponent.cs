using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CompanyOverviewViewComponent : BaseViewComponent
    {
        private const string componentId = "CompanyOverview";

        private readonly IComponentConfigService _componentConfigService;

        private readonly IStringLocalizer<SharedResource> _sharedRes;
        private readonly IStringLocalizer<AdminResource> _adminRes;

        private readonly CompanyService _companyService;
        public CompanyOverviewViewComponent(
            IComponentConfigService componentConfigService,
            IStringLocalizer<SharedResource> sharedRes,
            IStringLocalizer<AdminResource> adminRes,
            CompanyService companyService,
        ILogger<CompanyOverviewViewComponent> logger) : base(componentConfigService, logger)
        {
            _componentConfigService = componentConfigService;
            _sharedRes = sharedRes;
            _companyService = companyService;
            _adminRes = adminRes;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            var viewModel = ((JObject)model).ToObject<CompanyOverviewComponentViewModel>();

            var culture = ViewData["CurrentLanguage"]?.ToString() ?? "en";

          

            var companyInfo = await _companyService.GetCompany();
            if (companyInfo != null)
            {
                if (companyInfo.Locale != null && companyInfo.Locale.ContainsKey(culture))
                {
                    var locale = companyInfo.Locale[culture];

                    viewModel.Description = string.IsNullOrEmpty(viewModel.Description) ? locale.CompanyDescription : viewModel.Description;
                    viewModel.PhilosophyContent = string.IsNullOrEmpty(viewModel.PhilosophyContent) ? locale.Mission : viewModel.PhilosophyContent;

                    viewModel.CompanyImageAlt = string.IsNullOrEmpty(viewModel.CompanyImageAlt) ? locale.CompanyName : viewModel.CompanyImageAlt;
                }

            }





            return View(variant, viewModel);
        }
    }
}