using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.ViewModels.Components;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class ContentViewComponent : BaseViewComponent
    {
        public ContentViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<ContentViewComponent> logger) : base(componentConfigService, logger)
        {
        }

        /// <summary>
        /// 统一的调用方法 - 支持JSON和ViewModel模式
        /// </summary>
        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            return await base.InvokeViewAsync<ContentComponentViewModel>(model, "Content", variant);
        }
    }
}