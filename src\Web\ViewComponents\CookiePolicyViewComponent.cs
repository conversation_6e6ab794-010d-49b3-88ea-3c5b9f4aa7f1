using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.ComponentModel;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CookiePolicyViewComponent : BaseViewComponent
    {
        private readonly string componentId = "CookiePolicy";
        private readonly IComponentConfigService _componentConfigService;
        private readonly ComponentConfigDataService _componentDataService;


        public CookiePolicyViewComponent(
            IComponentConfigService componentConfigService,
             ComponentConfigDataService componentDataService,
            ILogger<CookiePolicyViewComponent> logger) : base(componentConfigService, logger)
        {
            _componentConfigService = componentConfigService;
            _componentDataService = componentDataService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            var culture = ViewData["CurrentLanguage"]?.ToString() ?? "en";
            JObject joData = new JObject();

            // 读取站点配置的Header数据
            var cookieData = await _componentDataService.GetByComponentAndVariantAsync(componentId, variant);
            if (cookieData != null && !string.IsNullOrEmpty(cookieData.JsonData))
            {
                joData = JObject.Parse(cookieData.JsonData);
            }

            // 直接使用JObject
            var jObjectData = await _componentConfigService.GetAdaptedComponentDataAsync(componentId, variant, culture, joData);

            var viewMode = jObjectData.ToObject<CookiePolicyComponentViewModel>();

            //直接输出视图，不走BaseView
            return View(variant, viewMode);
        }
    }
}