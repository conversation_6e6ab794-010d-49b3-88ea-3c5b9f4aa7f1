using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class CustomerCaseShowcaseViewComponent : BaseViewComponent
    {
        public CustomerCaseShowcaseViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<CustomerCaseShowcaseViewComponent> logger) : base(componentConfigService, logger)
        {
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            return await base.InvokeViewAsync<CustomerCaseShowcaseComponentViewModel>(model, "CustomerCaseShowcase", variant);
        }
    }
}