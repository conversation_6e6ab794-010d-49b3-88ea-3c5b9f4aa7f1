using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    /// <summary>
    /// Footer组件ViewComponent
    /// </summary>
    public class FooterViewComponent : BaseViewComponent
    {
        private readonly IComponentConfigService _componentConfigService;
        private readonly ComponentConfigDataService _componentDataService;
        private const string ComponentId = "Footer";


        public FooterViewComponent(
             IComponentConfigService componentConfigService,
             ComponentConfigDataService componentDataService,
        ILogger<HeaderViewComponent> logger) : base(componentConfigService, logger)
        {
            _componentConfigService = componentConfigService;
            _componentDataService = componentDataService;
        }

        /// <summary>
        /// 统一的调用方法 - 支持JSON和ViewModel模式
        /// </summary>
        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            JObject joData = new JObject();

            // 读取站点配置的Header数据
            var footerData = await _componentDataService.GetByComponentAndVariantAsync(ComponentId, variant);
            if (footerData != null && !string.IsNullOrEmpty(footerData.JsonData))
            {
                joData = JObject.Parse(footerData.JsonData);
            }

            // 直接使用JObject
            var jObjectData = await _componentConfigService.GetAdaptedComponentDataAsync(ComponentId, variant, ViewData["CurrentLanguage"]?.ToString() ?? "en", joData);

            var footerMode = jObjectData.ToObject<FooterComponentViewModel>();
          
            //读取全局站点信息，用于补充 Footer 未填写数据
            var siteInfo = await _componentConfigService.GetGlobalSiteInfo();

            if (siteInfo != null)
            {
                var currentLanguage = ViewData["CurrentLanguage"]?.ToString() ?? "en";

                // 填充公司名称 

                if (string.IsNullOrEmpty(footerMode.CompanyName) && siteInfo.CompanyNames != null && siteInfo.CompanyNames.Any())
                {
                    if (siteInfo.CompanyNames.ContainsKey(currentLanguage) && !string.IsNullOrEmpty(siteInfo.CompanyNames[currentLanguage]))
                    {
                        footerMode.CompanyName = siteInfo.CompanyNames[currentLanguage];
                    }
                    else if (siteInfo.CompanyNames.ContainsKey("en") && !string.IsNullOrEmpty(siteInfo.CompanyNames["en"]))
                    {
                        footerMode.CompanyName = siteInfo.CompanyNames["en"];
                    }
                    else
                    {
                        // 使用第一个可用的公司名称
                        var firstCompanyName = siteInfo.CompanyNames.FirstOrDefault(x => !string.IsNullOrEmpty(x.Value));
                        if (!string.IsNullOrEmpty(firstCompanyName.Value))
                        {
                            footerMode.CompanyName = firstCompanyName.Value;
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(footerMode.Logo) && !string.IsNullOrEmpty(siteInfo.LogoUrl))
            {
                footerMode.Logo = siteInfo.LogoUrl;
            }







            //直接输出视图，不走BaseView
            return View(variant, footerMode);
        }
    }
}