using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using MongoDB.Bson.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    /// <summary>
    /// Header组件ViewComponent
    /// </summary>
    public class HeaderViewComponent : BaseViewComponent
    {

        private const string ComponentId = "Header";

        private readonly IComponentConfigService _componentConfigService;
        private readonly ComponentConfigDataService _componentDataService;
        public HeaderViewComponent(
             IComponentConfigService componentConfigService,
             ComponentConfigDataService componentDataService,
        ILogger<HeaderViewComponent> logger) : base(componentConfigService, logger)
        {
            _componentConfigService = componentConfigService;
            _componentDataService = componentDataService;
        }

        /// <summary>
        /// 统一的调用方法 - 支持JSON和ViewModel模式
        /// </summary>
        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            JObject joData = new JObject();

            // 读取站点配置的Header数据
            var headerData = await _componentDataService.GetByComponentAndVariantAsync(ComponentId, variant);
            if (headerData != null && !string.IsNullOrEmpty(headerData.JsonData))
            {
                joData = JObject.Parse(headerData.JsonData);
            }

            // 直接使用JObject
            var jObjectData = await _componentConfigService.GetAdaptedComponentDataAsync(ComponentId, variant, ViewData["CurrentLanguage"]?.ToString() ?? "en", joData);

            var headerMode = jObjectData.ToObject<HeaderComponentViewModel>();

            //读取全局站点信息，用于补充 Header未填写数据
            var siteInfo = await _componentConfigService.GetGlobalSiteInfo();

            if (siteInfo != null)
            {
                // 如果 headerMode 对象中某个字段为空或NULL，并且siteInfo有对应的值，就用siteInfo中对应字段值填充headerMode

                // 填充Logo
                if (string.IsNullOrEmpty(headerMode.Logo) && !string.IsNullOrEmpty(siteInfo.LogoUrl))
                {
                    headerMode.Logo = siteInfo.LogoUrl;
                }

                var currentLanguage = ViewData["CurrentLanguage"]?.ToString() ?? "en";
                // 填充公司名称 - 优先站点名称，如果没有设置，再使用公司名称我 使用当前语言，回退到默认语言

                if (string.IsNullOrEmpty(headerMode.CompanyName) && siteInfo.SiteNames != null && siteInfo.SiteNames.Any())
                {
                    
                    if (siteInfo.SiteNames.ContainsKey(currentLanguage) && !string.IsNullOrEmpty(siteInfo.SiteNames[currentLanguage]))
                    {
                        headerMode.CompanyName = siteInfo.SiteNames[currentLanguage];
                    }
                    else if (siteInfo.SiteNames.ContainsKey("en") && !string.IsNullOrEmpty(siteInfo.SiteNames["en"]))
                    {
                        headerMode.CompanyName = siteInfo.SiteNames["en"];
                    }
                    else
                    {
                        // 使用第一个可用的公司名称
                        var firstCompanyName = siteInfo.SiteNames.FirstOrDefault(x => !string.IsNullOrEmpty(x.Value));
                        if (!string.IsNullOrEmpty(firstCompanyName.Value))
                        {
                            headerMode.CompanyName = firstCompanyName.Value;
                        }
                    }
                }

                if (string.IsNullOrEmpty(headerMode.CompanyName) && siteInfo.CompanyNames != null && siteInfo.CompanyNames.Any())
                {
                    if (siteInfo.CompanyNames.ContainsKey(currentLanguage) && !string.IsNullOrEmpty(siteInfo.CompanyNames[currentLanguage]))
                    {
                        headerMode.CompanyName = siteInfo.CompanyNames[currentLanguage];
                    }
                    else if (siteInfo.CompanyNames.ContainsKey("en") && !string.IsNullOrEmpty(siteInfo.CompanyNames["en"]))
                    {
                        headerMode.CompanyName = siteInfo.CompanyNames["en"];
                    }
                    else
                    {
                        // 使用第一个可用的公司名称
                        var firstCompanyName = siteInfo.CompanyNames.FirstOrDefault(x => !string.IsNullOrEmpty(x.Value));
                        if (!string.IsNullOrEmpty(firstCompanyName.Value))
                        {
                            headerMode.CompanyName = firstCompanyName.Value;
                        }
                    }
                }

                // 填充联系信息
                if (headerMode.ContactInfo == null)
                {
                    headerMode.ContactInfo = new ContactInfo();
                }

                if (string.IsNullOrEmpty(headerMode.ContactInfo.Phone) && !string.IsNullOrEmpty(siteInfo.Phone))
                {
                    headerMode.ContactInfo.Phone = siteInfo.Phone;
                }

                if (string.IsNullOrEmpty(headerMode.ContactInfo.Email) && !string.IsNullOrEmpty(siteInfo.Email))
                {
                    headerMode.ContactInfo.Email = siteInfo.Email;
                }

                // 填充地址 - 优先使用当前语言，回退到默认语言
                if (string.IsNullOrEmpty(headerMode.ContactInfo.Address) && siteInfo.Address != null && siteInfo.Address.Any())
                {
                    if (siteInfo.Address.ContainsKey(currentLanguage) && !string.IsNullOrEmpty(siteInfo.Address[currentLanguage]))
                    {
                        headerMode.ContactInfo.Address = siteInfo.Address[currentLanguage];
                    }
                    else if (siteInfo.Address.ContainsKey("en") && !string.IsNullOrEmpty(siteInfo.Address["en"]))
                    {
                        headerMode.ContactInfo.Address = siteInfo.Address["en"];
                    }
                    else
                    {
                        // 使用第一个可用的地址
                        var firstAddress = siteInfo.Address.FirstOrDefault(x => !string.IsNullOrEmpty(x.Value));
                        if (!string.IsNullOrEmpty(firstAddress.Value))
                        {
                            headerMode.ContactInfo.Address = firstAddress.Value;
                        }
                    }
                }
            }

            //直接输出视图，不走BaseView
            return View(variant, headerMode);
        }
    }
}