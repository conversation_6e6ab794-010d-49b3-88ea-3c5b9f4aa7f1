using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class HowToDoViewComponent : BaseViewComponent
    {
        public HowToDoViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<HowToDoViewComponent> logger) : base(componentConfigService, logger)
        {
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            return await base.InvokeViewAsync<HowToDoComponentViewModel>(model, "HowToDo", variant);
        }
    }
}