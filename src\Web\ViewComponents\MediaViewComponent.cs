using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MlSoft.Sites.Web.ViewModels.Components;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class MediaViewComponent : ViewComponent
    {
        public async Task<IViewComponentResult> InvokeAsync(MediaComponentViewModel model, string variant = "Default")
        {
            if (model == null)
            {
                model = new MediaComponentViewModel();
            }
            
            return View(variant, model);
        }
    }
}