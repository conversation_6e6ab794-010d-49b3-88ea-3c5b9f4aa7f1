using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Extensions;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels;
using MlSoft.Sites.Web.ViewModels.Components;
using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class MessageViewComponent : BaseViewComponent
    {
        public MessageViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<MessageViewComponent> logger) : base(componentConfigService, logger)
        {
        }

        /// <summary>
        /// 统一的调用方法 - 支持JSON和ViewModel模式
        /// </summary>
        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            return await base.InvokeViewAsync<MessageComponentViewModel>(model, "Message", variant);
        }
    }
}