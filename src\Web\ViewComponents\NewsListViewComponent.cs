using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.News;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class NewsListViewComponent : BaseViewComponent
    {
        private readonly NewsAnnouncementService _newsService;
        private readonly IStringLocalizer<AdminResource> _adminRes;

        public NewsListViewComponent(
            IComponentConfigService componentConfigService,
            NewsAnnouncementService newsService,
            IStringLocalizer<AdminResource> adminRes,
            ILogger<NewsListViewComponent> logger) : base(componentConfigService, logger)
        {
            _newsService = newsService;
            _adminRes = adminRes;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {

            var viewModel = ((JObject)model).ToObject<NewsListComponentViewModel>();


            var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";

            // 获取每页显示数量，默认为10
            var itemsPerPage = viewModel.ItemsPerPage > 0 ? viewModel.ItemsPerPage : 10;

            // 从服务获取新闻数据
            var newsAnnouncements = await _newsService.GetNewsForComponent(itemsPerPage);

            if (newsAnnouncements != null && newsAnnouncements.Count > 0)
            {
                var newsItems = new List<NewsListItem>();

                foreach (var newsItem in newsAnnouncements)
                {
                    // 获取当前语言的本地化内容
                    var locale = newsItem.Locale.ContainsKey(culture) ? newsItem.Locale[culture] : null;
                    if (locale != null)
                    {
                        // 获取新闻类型的本地化名称
                        var category = _adminRes[$"NewsType_{newsItem.Type}"].ToString();

                        var newsListItem = new NewsListItem
                        {
                            Id = newsItem.Id,
                            Title = locale.Title,
                            Excerpt = viewModel.ShowExcerpts ? locale.Summary : null,
                            Date = viewModel.ShowDates ? newsItem.PublishDate.ToString(viewModel.DateFormat) : null,
                            Category = viewModel.ShowCategories ? (category ?? _adminRes["Uncategorized"]) : null,
                            CategoryColor = GetCategoryColor(newsItem.Type.ToString()),
                            Url = $"/news/{newsItem.Slug}/",
                            ThumbnailUrl = viewModel.ShowImages ? newsItem.ThumbnailUrl : null,
                            IsFeatured = newsItem.IsFeatured,
                            ViewCount = newsItem.ViewCount
                        };

                        newsItems.Add(newsListItem);
                    }
                }

                viewModel.NewsItems = newsItems;
            }

            return View(variant, viewModel);
        }

        /// <summary>
        /// 根据新闻类型获取分类颜色
        /// </summary>
        private string GetCategoryColor(string newsType)
        {
            return newsType?.ToLower() switch
            {
                "announcement" => "primary",
                "press" => "info", 
                "product" => "success",
                "event" => "warning",
                "media" => "secondary",
                _ => "secondary"
            };
        }
    }
}
