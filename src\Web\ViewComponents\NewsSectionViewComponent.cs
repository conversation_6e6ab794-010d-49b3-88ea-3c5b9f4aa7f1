using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.CSR;
using MlSoft.Sites.Service.News;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class NewsSectionViewComponent : BaseViewComponent
    {
        private const string componentId = "NewsSection";

        private readonly NewsAnnouncementService _newsService;
        private readonly IStringLocalizer<AdminResource> _adminRes;

        private readonly IComponentConfigService _componentConfigService;

        public NewsSectionViewComponent(
            IComponentConfigService componentConfigService,
            NewsAnnouncementService newsService,
            IStringLocalizer<AdminResource> adminRes,
        ILogger<NewsSectionViewComponent> logger) : base(componentConfigService, logger)
        {
            this._newsService = newsService;
            _adminRes = adminRes;
            _componentConfigService = componentConfigService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {

            var viewModel = ((JObject)model).ToObject<NewsSectionComponentViewModel>();

            var culture = ViewData["CurrentLanguage"]?.ToString() ?? "en";

            
            var maxItems = viewModel.MaxItems > 0 ? viewModel.MaxItems : 5;

            var newsAnnounsList = await _newsService.GetNewsForComponent(maxItems);
            if (newsAnnounsList != null && newsAnnounsList.Count != 0)
            {
                var newsItems = new List<NewsItem>();
                foreach (var newItem in newsAnnounsList)
                {
                    var category = _adminRes[$"NewsType_{newItem.Type}"].ToString();
                    var locale = newItem.Locale.ContainsKey(culture) ? newItem.Locale[culture] : null;
                    if (locale != null)
                    {
                        var newsVm = new NewsItem
                        {
                            Title = locale.Title,
                            Url = $"/news/{newItem.Slug}/",
                            Date = newItem.PublishDate.ToString(viewModel.DateFormat),
                            Excerpt = viewModel.ShowExcerpts ? locale.Summary : null,
                            Category = viewModel.ShowCategories ? (category ?? _adminRes["Uncategorized"]) : null,
                            CategoryColor = "secondary"
                        };
                        newsItems.Add(newsVm);
                    }


                }


                viewModel.NewsItems = newsItems;
            }



            return View(variant, viewModel);
        }
    }
}