using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Entities.Organization;
using MlSoft.Sites.Model.Extensions;
using MlSoft.Sites.Service.Organization;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class OrganizationStructureViewComponent : BaseViewComponent
    {

        private const string componentId = "OrganizationStructure";
        private readonly OrganizationStructureService _organizationStructureService;
        private readonly ILogger<OrganizationStructureViewComponent> _logger;
        private readonly IComponentConfigService _componentConfigService;
        public OrganizationStructureViewComponent(
            IComponentConfigService componentConfigService,
            OrganizationStructureService organizationStructureService,
            ILogger<OrganizationStructureViewComponent> logger) : base(componentConfigService, logger)
        {
            _organizationStructureService = organizationStructureService;
            _logger = logger;
            _componentConfigService = componentConfigService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            var viewModel = ((JObject)model).ToObject<OrganizationStructureComponentViewModel>();

            var allDepartments = await _organizationStructureService.GetActiveDepartmentsAsync();
            viewModel.Departments = BuildHierarchy(allDepartments.OrderBy(d => d.DisplayOrder).ToList(), null);

            return View(variant, viewModel);
        }

        private List<OrganizationStructureNode> BuildHierarchy(List<OrganizationStructure> departments, string? parentId)
        {
            var nodes = new List<OrganizationStructureNode>();
            var children = departments.Where(d => d.ParentDepartmentId == parentId).ToList();

            foreach (var dept in children)
            {
                var node = new OrganizationStructureNode
                {
                    Department = dept,
                    Children = BuildHierarchy(departments, dept.Id)
                };
                nodes.Add(node);
            }
            return nodes;
        }
    }
}
