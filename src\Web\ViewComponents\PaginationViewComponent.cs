using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ZstdSharp;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class PaginationViewComponent : BaseViewComponent
    {
        public PaginationViewComponent(
            IComponentConfigService componentConfigService,
            ILogger<PaginationViewComponent> logger) : base(componentConfigService, logger)
        {
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            var viewModel = ((JObject)model).ToObject<PaginationComponentViewModel>();

            viewModel.CurrentPageIndex = 1;
            if (Request.Query.Count != 0)
            {
                if (!string.IsNullOrEmpty(Request.Query["page"]))
                {
                    if (int.TryParse(Request.Query["page"].ToString(), out int pageIndex))
                    {
                        viewModel.CurrentPageIndex = pageIndex;
                    }
                }

                var queryParams = new Dictionary<string, string>();

                foreach (var pq in Request.Query)
                {
                    if (pq.Key != "page")
                    {
                        queryParams.Add(pq.Key, pq.Value.ToString());
                    }
                }

                if (queryParams.Count != 0)
                {
                    viewModel.QueryParams = string.Join("&", queryParams.Select(x => $"{x.Key}={x.Value}"));
                }
            }


            // Host Page Pass TotalCount by ViewData
            if (ViewData.ContainsKey(PageComponentContext.ViewDataKey))
            {
                var ctx = (PageComponentContext)ViewData[PageComponentContext.ViewDataKey];
                if (ctx != null && ctx.Data != null &&  ctx.Data.ContainsKey(PageComponentContext.ViewDataKey))
                {
                    if (int.TryParse(ctx.Data[PageComponentContext.ViewDataKey].ToString(), out int totalCount))
                    {
                        viewModel.TotalCount = totalCount;
                    }
                }
            }


            return View(variant, viewModel);
        }
    }
}