using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewComponents;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Service.Organization;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class PresidentMessageViewComponent : BaseViewComponent
    {
        private readonly ExecutiveService _executiveService;

        public PresidentMessageViewComponent(
            IComponentConfigService componentConfigService,
            ExecutiveService executiveService,
            ILogger<PresidentMessageViewComponent> logger) : base(componentConfigService, logger)
        {
            _executiveService = executiveService;
        }

        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
            // 获取组件配置
            var viewModel = ((JObject)model).ToObject<PresidentMessageComponentViewModel>();

            // 从数据库获取社长信息
            viewModel.PresidentData = await _executiveService.GetPresidentAsync();

            return View(variant, viewModel);

        }
    }
}