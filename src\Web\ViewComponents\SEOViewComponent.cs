using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MlSoft.Sites.Model.Configuration;
using MlSoft.Sites.Model.Entities.Company;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Entities.Settings;
using MlSoft.Sites.Model.Extensions;
using MlSoft.Sites.Service.Company;
using MlSoft.Sites.Service.Settings;
using MlSoft.Sites.Web.Services;
using MlSoft.Sites.Web.Services.Components;
using MlSoft.Sites.Web.ViewModels.Components;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MlSoft.Sites.Web.ViewComponents
{
    public class SEOViewComponent : BaseViewComponent
    {
        private readonly SEOSettingsService _seoSettingsService;
        private readonly SiteSettingsService _siteSettingsService;
        private readonly CompanyService _companyService;
        private readonly IConfiguration _configuration;

        private string baseUrl { get; set; } = string.Empty;

        public SEOViewComponent(
            SEOSettingsService seoSettingsService, 
            SiteSettingsService siteSettingsService,
            CompanyService companyService,
            IComponentConfigService componentConfigService,
            ILogger<HeroViewComponent> logger,
            IConfiguration configuration) : base(componentConfigService, logger)
        {
            _seoSettingsService = seoSettingsService;
            _siteSettingsService = siteSettingsService;
            _companyService = companyService;
            _configuration = configuration;
        }

        /// <summary>
        /// 统一的调用方法 - 支持JSON和ViewModel模式
        /// </summary>
        public async Task<IViewComponentResult> InvokeAsync(object model, string variant = "Default")
        {
           
            var viewModel = new SEOComponentViewModel();
            var currentLanguage = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
            viewModel.CurrentLanguage = currentLanguage;

            // 获取站点设置作为默认值
            var siteSettings = await GetSiteSettingsAsync();
            var company = await GetCompanyInfoAsync();


            if (siteSettings != null && siteSettings.CustomSettings != null && siteSettings.CustomSettings.ContainsKey("Domain"))
            {
                baseUrl = siteSettings.CustomSettings["Domain"].ToString();
            }


            //// 从页面配置加载SEO设置
            //SEOSettings? seoSettings = null;
            //if (!string.IsNullOrEmpty(pageConfigurationId))
            //{
            //    seoSettings = await _seoSettingsService.GetSEOSettingsByPageAsync(pageConfigurationId);
            //}

            //// 构建基础TDK
            //BuildBasicSEO(viewModel, seoSettings, siteSettings, title, description, keywords, currentLanguage);

            //// 构建Open Graph
            //BuildOpenGraph(viewModel, seoSettings, siteSettings, canonicalUrl, ogImage, currentLanguage);

            //// 构建Twitter Card
            //BuildTwitterCard(viewModel, seoSettings, siteSettings, twitterCard, twitterSite, twitterCreator, twitterImage, currentLanguage);

            //// 构建Canonical和Hreflang
            //BuildCanonicalAndHreflang(viewModel, seoSettings, canonicalUrl, hreflangUrls);

            //// 构建Schema.org
            //if (enableSchema)
            //{
            //    await BuildSchemaMarkup(viewModel, seoSettings, company, siteSettings, schemaTypes, schemaData, currentLanguage);
            //}

            return View("Default", viewModel);
        }

        private void BuildBasicSEO(SEOComponentViewModel viewModel, SEOSettings? seoSettings, 
            SiteSettings? siteSettings, string? title, string? description, string? keywords, string currentLanguage)
        {
            //// Title优先级: 参数 > SEO设置 > 站点默认
            //viewModel.Title = title ?? 
            //                  seoSettings.GetLocalizedProperty<SEOSettings, SEOSettingsLocaleFields, string>(currentLanguage, locale => locale.MetaTitle) ?? 
            //                  siteSettings.GetLocalizedProperty<SiteSettings, SiteSettingsLocaleFields, string>(currentLanguage, locale => locale.SiteName) ?? 
            //                  "Default Site";

            //// Description
            //viewModel.Description = description ?? 
            //                       seoSettings.GetLocalizedProperty<SEOSettings, SEOSettingsLocaleFields, string>(currentLanguage, locale => locale.MetaDescription) ?? 
            //                       siteSettings.GetLocalizedProperty<SiteSettings, SiteSettingsLocaleFields, string>(currentLanguage, locale => locale.SiteDescription);

            //// Keywords
            //viewModel.Keywords = keywords ?? 
            //                    seoSettings.GetLocalizedProperty<SEOSettings, SEOSettingsLocaleFields, string>(currentLanguage, locale => locale.MetaKeywords);
        }

        private void BuildOpenGraph(SEOComponentViewModel viewModel, SEOSettings? seoSettings, 
            SiteSettings? siteSettings, string? canonicalUrl, string? ogImage, string currentLanguage)
        {
            viewModel.OgTitle = viewModel.Title;
            viewModel.OgDescription = viewModel.Description;
            viewModel.OgImage = ogImage ?? seoSettings?.OgImageUrl ?? GetDefaultOgImage();
            viewModel.OgUrl = canonicalUrl ?? seoSettings?.CanonicalUrl ?? GetCurrentUrl();
            viewModel.OgLocale = GetOgLocale(currentLanguage);
            //viewModel.SiteName = siteSettings.GetLocalizedProperty<SiteSettings, SiteSettingsLocaleFields, string>(currentLanguage, locale => locale.SiteName);
        }

        private void BuildTwitterCard(SEOComponentViewModel viewModel, SEOSettings? seoSettings,
            SiteSettings? siteSettings, string? twitterCard, string? twitterSite, string? twitterCreator, string? twitterImage, string currentLanguage)
        {
            // Twitter Card type - prioritize parameter, then settings, then default
            viewModel.TwitterCard = twitterCard ?? seoSettings?.TwitterCard ?? "summary_large_image";
            
            // Twitter site and creator - prioritize parameters, then settings
            viewModel.TwitterSite = twitterSite ?? seoSettings?.TwitterSite;
            viewModel.TwitterCreator = twitterCreator ?? seoSettings?.TwitterCreator;
            
            // Twitter title - prioritize specific Twitter title, fall back to OG title, then page title
            viewModel.TwitterTitle = seoSettings.GetLocalizedProperty<SEOSettings, SEOSettingsLocaleFields, string>(
                currentLanguage, locale => locale.TwitterTitle) ?? 
                viewModel.OgTitle ?? 
                viewModel.Title;
            
            // Twitter description - prioritize specific Twitter description, fall back to OG description, then meta description
            viewModel.TwitterDescription = seoSettings.GetLocalizedProperty<SEOSettings, SEOSettingsLocaleFields, string>(
                currentLanguage, locale => locale.TwitterDescription) ?? 
                viewModel.OgDescription ?? 
                viewModel.Description;
            
            // Twitter image - prioritize parameter, then specific Twitter image, fall back to OG image, then default
            viewModel.TwitterImage = twitterImage ?? 
                seoSettings?.TwitterImageUrl ?? 
                viewModel.OgImage ?? 
                GetDefaultOgImage();
        }

        private void BuildCanonicalAndHreflang(SEOComponentViewModel viewModel, SEOSettings? seoSettings, 
            string? canonicalUrl, Dictionary<string, string>? hreflangUrls)
        {
            viewModel.CanonicalUrl = canonicalUrl ?? seoSettings?.CanonicalUrl ?? GetCurrentUrl();
            
            if (hreflangUrls != null)
            {
                viewModel.HreflangUrls = hreflangUrls;
            }
            else if (seoSettings?.HreflangUrls.Any() == true)
            {
                viewModel.HreflangUrls = seoSettings.HreflangUrls;
            }
            else
            {
                viewModel.HreflangUrls = GenerateAutoHreflang();
            }
        }

        private async Task BuildSchemaMarkup(SEOComponentViewModel viewModel, SEOSettings? seoSettings, 
            Company? company, SiteSettings? siteSettings, string[]? schemaTypes, object? schemaData, string currentLanguage)
        {
            var schemas = new List<SchemaData>();

            // 自动生成基础Schema
            if (seoSettings?.EnableAutoSchema != false)
            {
                schemas.Add(await CreateWebSiteSchema(siteSettings, currentLanguage));
                
                if (company != null)
                {
                    schemas.Add(await CreateOrganizationSchema(company, siteSettings, currentLanguage));
                }
            }

            // 添加指定的Schema类型
            if (schemaTypes != null)
            {
                foreach (var type in schemaTypes)
                {
                    var schema = await CreateSchemaByType(type, schemaData, company, currentLanguage);
                    if (schema != null)
                    {
                        schemas.Add(schema);
                    }
                }
            }

            // 从SEO设置添加自定义Schema
            if (seoSettings?.EnabledSchemaTypes.Any() == true)
            {
                foreach (var type in seoSettings.EnabledSchemaTypes)
                {
                    var customData = seoSettings.SchemaData.ContainsKey(type) ? seoSettings.SchemaData[type] : null;
                    var schema = await CreateSchemaByType(type, customData, company, currentLanguage);
                    if (schema != null)
                    {
                        schemas.Add(schema);
                    }
                }
            }

            viewModel.SchemaMarkups = schemas.OrderBy(s => s.Priority).ToList();
        }

        private async Task<SchemaData> CreateWebSiteSchema(SiteSettings? siteSettings, string currentLanguage)
        {
            var siteName = siteSettings.GetLocalizedProperty<SiteSettings, SiteSettingsLocaleFields, string>(currentLanguage, locale => locale.SiteTitle, "");
            

            return new SchemaData
            {
                Type = "WebSite",
                Priority = 10,
                Data = new
                {
                    context = "https://schema.org",
                    type = "WebSite",
                    name = siteName,
                    url = baseUrl,
                    //potentialAction = new
                    //{
                    //    type = "SearchAction",
                    //    target = $"{baseUrl}/search?q={{search_term_string}}",
                    //    queryInput = "required name=search_term_string"
                    //}
                }
            };
        }

        private async Task<SchemaData> CreateOrganizationSchema(Company company, SiteSettings? siteSettings, string currentLanguage)
        {
            var companyName = company.GetLocalizedProperty<Company, CompanyLocaleFields, string>(currentLanguage, locale => locale.CompanyName);
            var companyDescription = company.GetLocalizedProperty<Company, CompanyLocaleFields, string>(currentLanguage, locale => locale.CompanyDescription);

            var logoUrl = "";
            if(siteSettings != null && !string.IsNullOrEmpty(siteSettings.LogoUrl))
            {
                logoUrl = $"{baseUrl}/upload{EnumFolderType.BasicInfo}/{siteSettings.LogoUrl}";
            }
            
            return new SchemaData
            {
                Type = "Organization",
                Priority = 20,
                Data = new
                {
                    context = "https://schema.org",
                    type = "Organization",
                    name = companyName,
                    url = baseUrl,
                    description = companyDescription,
                    logo = logoUrl,
                   // foundingDate = company.FoundingDate?.ToString("yyyy-MM-dd"),
                   // numberOfEmployees = company.EmployeeCount
                }
            };
        }

        private async Task<SchemaData?> CreateSchemaByType(string type, object? data, Company? company, string currentLanguage)
        {
            return type.ToLower() switch
            {
                "breadcrumblist" => CreateBreadcrumbSchema(),
                "webpage" => CreateWebPageSchema(currentLanguage),
                _ => null
            };
        }

        private SchemaData CreateBreadcrumbSchema()
        {
            var breadcrumbs = GetBreadcrumbsFromRoute();
            
            return new SchemaData
            {
                Type = "BreadcrumbList",
                Priority = 30,
                Data = new
                {
                    context = "https://schema.org",
                    type = "BreadcrumbList",
                    itemListElement = breadcrumbs.Select((breadcrumb, index) => new
                    {
                        type = "ListItem",
                        position = index + 1,
                        name = breadcrumb.Name,
                        item = breadcrumb.Url
                    }).ToArray()
                }
            };
        }

        private SchemaData CreateWebPageSchema(string currentLanguage)
        {
            return new SchemaData
            {
                Type = "WebPage",
                Priority = 40,
                Data = new
                {
                    context = "https://schema.org",
                    type = "WebPage",
                    url = GetCurrentUrl(),
                    inLanguage = currentLanguage
                }
            };
        }

        private async Task<SiteSettings?> GetSiteSettingsAsync()
        {
            try
            {
                return await _siteSettingsService.GetCurrentSettingsAsync();
            }
            catch
            {
                return null;
            }
        }

        private async Task<Company?> GetCompanyInfoAsync()
        {
            try
            {
                return (await _companyService.GetActiveCompaniesAsync()).FirstOrDefault();
            }
            catch
            {
                return null;
            }
        }

        private string GetCurrentUrl()
        {
            return $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}";
        }


        private string GetDefaultOgImage()
        {
            return $"{baseUrl}/images/default-og-image.jpg";
        }

        private string GetOgLocale(string currentLanguage)
        {
            return currentLanguage switch
            {
                "ja" => "ja_JP",
                "en" => "en_US",
                _ => "en_US"
            };
        }

        private Dictionary<string, string> GenerateAutoHreflang()
        {
            var hreflang = new Dictionary<string, string>();
            var supportedLanguages = ViewContext.ViewData["SupportedLanguages"] as SupportedLanguage[];
            var path = Request.Path.Value ?? "/";

            foreach (var lang in supportedLanguages)
            {
                hreflang[lang.Code] = $"{baseUrl}/{lang.Code}{path}";
            }

            hreflang["x-default"] = $"{baseUrl}{path}";
            return hreflang;
        }

        private List<(string Name, string Url)> GetBreadcrumbsFromRoute()
        {
            var breadcrumbs = new List<(string, string)>();
            
            breadcrumbs.Add(("Home", baseUrl));
            
            var segments = Request.Path.Value?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();
            var currentPath = "";
            
            foreach (var segment in segments.Skip(1)) // Skip language code
            {
                currentPath += "/" + segment;
                breadcrumbs.Add((segment.Replace("-", " "), baseUrl + currentPath));
            }

            return breadcrumbs;
        }
    }
}