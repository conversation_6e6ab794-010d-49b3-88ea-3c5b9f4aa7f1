using MlSoft.Sites.Model.Entities.Messages;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Service.Messages;
using MlSoft.Sites.Model.Entities.Common;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class AdminMessageIndexViewModel
    {
        public PagedResult<MessageInquiry> Messages { get; set; } = new();
        public MessageStatistics Statistics { get; set; } = new();
        public MessageStatus? CurrentStatus { get; set; }
        public string? SearchQuery { get; set; }
    }
}