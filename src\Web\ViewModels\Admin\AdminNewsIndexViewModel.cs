using MlSoft.Sites.Model.Entities.News;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Common;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class AdminNewsIndexViewModel
    {
        public PagedResult<NewsAnnouncement> News { get; set; } = new();
        public NewsType? SelectedType { get; set; }
        public NewsStatus? SelectedStatus { get; set; }
        public string SearchTerm { get; set; } = string.Empty;
        public IEnumerable<NewsType> AvailableTypes { get; set; } = new List<NewsType>();
    }
}
