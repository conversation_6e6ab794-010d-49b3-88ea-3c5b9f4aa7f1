using System.Collections.Generic;
using MlSoft.Sites.Model.Entities.Enums;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class BatchUpdateRequest
    {
        public List<string> NewsIds { get; set; } = new();
        public string Action { get; set; } = string.Empty; // "publish", "unpublish", "featured", "delete", "archive"
        public NewsStatus? Status { get; set; }
        public bool? IsFeatured { get; set; }
    }
}
