using System;
using System.Collections.Generic;
using MlSoft.Sites.Model.Entities.Business;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class BusinessInfoViewModel
    {
        public List<BusinessDivisionViewModel> BusinessDivisions { get; set; } = new();
        public List<ProductServiceViewModel> ProductServices { get; set; } = new();
    }

    public class BusinessDivisionViewModel
    {
        public string Id { get; set; } = string.Empty;
        public Dictionary<string, BusinessDivisionLocaleFields> Locale { get; set; } = new();
        public string? ImageUrl { get; set; }
        public string? IconUrl { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class ProductServiceViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string? BusinessDivisionId { get; set; }
        public string? BusinessDivisionName { get; set; } // 用于显示
        public Dictionary<string, ProductServiceLocaleFields> Locale { get; set; } = new();
        public List<string> ImageUrls { get; set; } = new();
        public List<ProductDocumentViewModel> Documents { get; set; } = new();
        public ProductCategory Category { get; set; }
        public decimal? Price { get; set; }
        public string? Currency { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class ProductDocumentViewModel
    {
        public Dictionary<string, DocumentLocaleFields> Locale { get; set; } = new();
        public string FileUrl { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime UploadDate { get; set; }
    }
}
