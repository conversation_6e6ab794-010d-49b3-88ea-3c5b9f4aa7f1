using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Web.Resources;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class ChangeEmailViewModel
    {
        [Required(ErrorMessageResourceName = "NewEmailRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [EmailAddress(ErrorMessageResourceName = "InvalidEmailFormat", ErrorMessageResourceType = typeof(AdminResource))]
        [Display(Name = "NewEmailAddress", ResourceType = typeof(AdminResource))]
        public string NewEmail { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "PasswordRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [DataType(DataType.Password)]
        [Display(Name = "ConfirmPassword", ResourceType = typeof(AdminResource))]
        public string Password { get; set; } = string.Empty;
    }
}