using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Web.Resources;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class ChangePasswordViewModel
    {
        [Required(ErrorMessageResourceName = "CurrentPasswordRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [DataType(DataType.Password)]
        [Display(Name = "CurrentPassword", ResourceType = typeof(AdminResource))]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "NewPasswordRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [StringLength(100, ErrorMessageResourceName = "PasswordLengthError", ErrorMessageResourceType = typeof(AdminResource), MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "NewPassword", ResourceType = typeof(AdminResource))]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "ConfirmPasswordRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [DataType(DataType.Password)]
        [Display(Name = "ConfirmNewPassword", ResourceType = typeof(AdminResource))]
        [Compare("NewPassword", ErrorMessageResourceName = "PasswordMismatch", ErrorMessageResourceType = typeof(AdminResource))]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}