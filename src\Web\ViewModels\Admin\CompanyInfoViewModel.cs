using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Attributes;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    /// <summary>
    /// 企业信息管理页面视图模型
    /// </summary>
    public class CompanyInfoViewModel
    {
        /// <summary>
        /// 基本信息
        /// </summary>
        public CompanyBasicInfoViewModel BasicInfo { get; set; } = new();

        /// <summary>
        /// 企业历史
        /// </summary>
        public List<CompanyHistoryItemViewModel> CompanyHistory { get; set; } = new();

        /// <summary>
        /// 高管和组织架构
        /// </summary>
        public ExecutiveOrganizationViewModel ExecutiveOrganization { get; set; } = new();

        /// <summary>
        /// 联系信息
        /// </summary>
        public ContactInfoViewModel ContactInfo { get; set; } = new();

        /// <summary>
        /// CSR活动
        /// </summary>
        public List<CSRActivityItemViewModel> CSRActivities { get; set; } = new();

        /// <summary>
        /// 投资者关系
        /// </summary>
        public InvestorRelationsViewModel InvestorRelations { get; set; } = new();
    }

    /// <summary>
    /// 企业基本信息视图模型
    /// </summary>
    public class CompanyBasicInfoViewModel
    {
        /// <summary>
        /// 多语言企业名称
        /// </summary>
        [Display(Name = "CompanyName", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> CompanyNames { get; set; } = new();

        /// <summary>
        /// 多语言企业简介
        /// </summary>
        [Display(Name = "CompanyDescription", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> CompanyDescriptions { get; set; } = new();

        /// <summary>
        /// 多语言经营理念
        /// </summary>
        [Display(Name = "Philosophy", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Philosophy { get; set; } = new();

        /// <summary>
        /// 设立日期
        /// </summary>
        [Display(Name = "EstablishedDate", ResourceType = typeof(AdminResource))]
        [Required(ErrorMessageResourceName = "EstablishedDateRequired", ErrorMessageResourceType = typeof(AdminResource))]
        public DateTime EstablishedDate { get; set; }

        /// <summary>
        /// 工商注册号
        /// </summary>
        [Display(Name = "RegistrationNumber", ResourceType = typeof(AdminResource))]
        public string? RegistrationNumber { get; set; }

        /// <summary>
        /// 注册资本
        /// </summary>
        [Display(Name = "Capital", ResourceType = typeof(AdminResource))]
        public decimal? Capital { get; set; }

        /// <summary>
        /// 货币单位
        /// </summary>
        [Display(Name = "Currency", ResourceType = typeof(AdminResource))]
        public string? Currency { get; set; }

        /// <summary>
        /// 企业规模
        /// </summary>
        [Display(Name = "EmployeeScale", ResourceType = typeof(AdminResource))]
        public EmployeeScale? EmployeeScale { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 企业历史事件视图模型
    /// </summary>
    public class CompanyHistoryItemViewModel
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 事件日期
        /// </summary>
        [Display(Name = "EventDate", ResourceType = typeof(AdminResource))]
        [Required(ErrorMessageResourceName = "EventDateRequired", ErrorMessageResourceType = typeof(AdminResource))]
        public DateTime EventDate { get; set; }

        /// <summary>
        /// 事件类型
        /// </summary>
        [Display(Name = "EventType", ResourceType = typeof(AdminResource))]
        [Required(ErrorMessageResourceName = "EventTypeRequired", ErrorMessageResourceType = typeof(AdminResource))]
        public HistoryEventType EventType { get; set; }

        /// <summary>
        /// 多语言事件标题
        /// </summary>
        [Display(Name = "EventTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> EventTitles { get; set; } = new();

        /// <summary>
        /// 多语言事件描述
        /// </summary>
        [Display(Name = "EventDescription", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> EventDescriptions { get; set; } = new();

        /// <summary>
        /// 事件配图
        /// </summary>
        [Display(Name = "EventImage", ResourceType = typeof(AdminResource))]
        public string? ImageUrl { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        [Display(Name = "DisplayOrder", ResourceType = typeof(AdminResource))]
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 高管和组织架构视图模型
    /// </summary>
    public class ExecutiveOrganizationViewModel
    {
        /// <summary>
        /// 高管信息
        /// </summary>
        public List<ExecutiveItemViewModel> Executives { get; set; } = new();

        /// <summary>
        /// 组织架构
        /// </summary>
        public List<OrganizationItemViewModel> OrganizationStructures { get; set; } = new();
    }

    /// <summary>
    /// 高管信息视图模型
    /// </summary>
    public class ExecutiveItemViewModel
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 多语言姓名
        /// </summary>
        [Display(Name = "ExecutiveName", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Names { get; set; } = new();

        /// <summary>
        /// 多语言职位
        /// </summary>
        [Display(Name = "Position", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Positions { get; set; } = new();

        /// <summary>
        /// 多语言简历
        /// </summary>
        [Display(Name = "Biography", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Biographies { get; set; } = new();

        /// <summary>
        /// 多语言致辞内容
        /// </summary>
        [Display(Name = "Message", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Messages { get; set; } = new();

        /// <summary>
        /// 高管照片
        /// </summary>
        [Display(Name = "PhotoUrl", ResourceType = typeof(AdminResource))]
        public string? PhotoUrl { get; set; }

        /// <summary>
        /// 是否为社长
        /// </summary>
        [Display(Name = "IsPresident", ResourceType = typeof(AdminResource))]
        public bool IsPresident { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        [Display(Name = "DisplayOrder", ResourceType = typeof(AdminResource))]
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 组织架构视图模型
    /// </summary>
    public class OrganizationItemViewModel
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 上级部门ID
        /// </summary>
        public string? ParentDepartmentId { get; set; }

        /// <summary>
        /// 多语言部门名称
        /// </summary>
        [Display(Name = "DepartmentName", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> DepartmentNames { get; set; } = new();

        /// <summary>
        /// 多语言部门描述
        /// </summary>
        [Display(Name = "DepartmentDescription", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> DepartmentDescriptions { get; set; } = new();

        /// <summary>
        /// 层级
        /// </summary>
        [Display(Name = "Level", ResourceType = typeof(AdminResource))]
        public int Level { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        [Display(Name = "DisplayOrder", ResourceType = typeof(AdminResource))]
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 联系信息视图模型
    /// </summary>
    public class ContactInfoViewModel
    {
        /// <summary>
        /// 基础联系信息
        /// </summary>
        public ContactInfoDetailViewModel BasicContactInfo { get; set; } = new();

        /// <summary>
        /// 企业据点信息
        /// </summary>
        public List<CompanyLocationViewModel> Locations { get; set; } = new();
    }

    /// <summary>
    /// 联系信息详情视图模型
    /// </summary>
    public class ContactInfoDetailViewModel
    {
        /// <summary>
        /// 电话号码
        /// </summary>
        [Display(Name = "Phone", ResourceType = typeof(AdminResource))]
        public string? Phone { get; set; }

        /// <summary>
        /// 传真号码
        /// </summary>
        [Display(Name = "Fax", ResourceType = typeof(AdminResource))]
        public string? Fax { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [Display(Name = "Email", ResourceType = typeof(AdminResource))]
        [OptionalEmailAddress(ErrorMessageResourceName = "EmailInvalid", ErrorMessageResourceType = typeof(AdminResource))]
        public string? Email { get; set; }

        /// <summary>
        /// 官方网站
        /// </summary>
        [Display(Name = "Website", ResourceType = typeof(AdminResource))]
        [Url(ErrorMessageResourceName = "WebsiteInvalid", ErrorMessageResourceType = typeof(AdminResource))]
        public string? Website { get; set; }

        /// <summary>
        /// 邮政编码
        /// </summary>
        [Display(Name = "PostalCode", ResourceType = typeof(AdminResource))]
        public string? PostalCode { get; set; }

        /// <summary>
        /// 多语言地址
        /// </summary>
        [Display(Name = "Address", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Addresses { get; set; } = new();

        /// <summary>
        /// 多语言营业时间
        /// </summary>
        [Display(Name = "BusinessHours", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> BusinessHours { get; set; } = new();

        /// <summary>
        /// 多语言交通信息
        /// </summary>
        [Display(Name = "AccessInfo", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> AccessInfo { get; set; } = new();


        /// <summary>
        /// 地图
        /// </summary>
        public string MapLink { get; set; } = string.Empty;

        ///// <summary>
        ///// 纬度
        ///// </summary>
        //[Display(Name = "Latitude", ResourceType = typeof(AdminResource))]
        //public double? Latitude { get; set; }

        ///// <summary>
        ///// 经度
        ///// </summary>
        //[Display(Name = "Longitude", ResourceType = typeof(AdminResource))]
        //public double? Longitude { get; set; }
    }

    /// <summary>
    /// 企业据点视图模型
    /// </summary>
    public class CompanyLocationViewModel
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 多语言据点名称
        /// </summary>
        [Display(Name = "LocationName", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Names { get; set; } = new();

        /// <summary>
        /// 据点类型
        /// </summary>
        [Display(Name = "LocationType", ResourceType = typeof(AdminResource))]
        public LocationType Type { get; set; }



        /// <summary>
        /// 是否为主要据点
        /// </summary>
        [Display(Name = "IsPrimary", ResourceType = typeof(AdminResource))]
        public bool IsPrimary { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        [Display(Name = "DisplayOrder", ResourceType = typeof(AdminResource))]
        public int DisplayOrder { get; set; }

        /// <summary>
        /// 联系信息
        /// </summary>
        public ContactInfoDetailViewModel ContactInfo { get; set; } = new();

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// CSR活动视图模型
    /// </summary>
    public class CSRActivityItemViewModel
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 多语言活动名称
        /// </summary>
        [Display(Name = "ActivityTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Titles { get; set; } = new();

        /// <summary>
        /// 多语言活动描述
        /// </summary>
        [Display(Name = "ActivityDescription", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Descriptions { get; set; } = new();

        /// <summary>
        /// 多语言成果总结
        /// </summary>
        [Display(Name = "ActivitySummary", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Summaries { get; set; } = new();

        /// <summary>
        /// CSR活动类型
        /// </summary>
        [Display(Name = "CSRCategory", ResourceType = typeof(AdminResource))]
        public CSRCategory Category { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        [Display(Name = "StartDate", ResourceType = typeof(AdminResource))]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        [Display(Name = "EndDate", ResourceType = typeof(AdminResource))]
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 活动图片
        /// </summary>
        [Display(Name = "ActivityImages", ResourceType = typeof(AdminResource))]
        public List<string> ImageUrls { get; set; } = new();

        /// <summary>
        /// CSR报告文件
        /// </summary>
        [Display(Name = "ReportFile", ResourceType = typeof(AdminResource))]
        public string? ReportFileUrl { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 投资者关系视图模型
    /// </summary>
    public class InvestorRelationsViewModel
    {
        /// <summary>
        /// 财务报告
        /// </summary>
        public List<FinancialReportItemViewModel> FinancialReports { get; set; } = new();

        /// <summary>
        /// 股东大会
        /// </summary>
        public List<ShareholderMeetingItemViewModel> ShareholderMeetings { get; set; } = new();
    }

    /// <summary>
    /// 财务报告视图模型
    /// </summary>
    public class FinancialReportItemViewModel
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 报告类型
        /// </summary>
        [Display(Name = "ReportType", ResourceType = typeof(AdminResource))]
        public ReportType Type { get; set; }


        /// <summary>
        /// 报告年份
        /// </summary>
        [Display(Name = "ReportYear", ResourceType = typeof(AdminResource))]
        public int Year { get; set; }

        /// <summary>
        /// 报告季度
        /// </summary>
        [Display(Name = "Quarter", ResourceType = typeof(AdminResource))]
        public int? Quarter { get; set; }

        /// <summary>
        /// 多语言报告标题
        /// </summary>
        [Display(Name = "ReportTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Titles { get; set; } = new();

        /// <summary>
        /// 多语言报告摘要
        /// </summary>
        [Display(Name = "ReportSummary", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Summaries { get; set; } = new();

        /// <summary>
        /// 报告文件
        /// </summary>
        [Display(Name = "ReportFile", ResourceType = typeof(AdminResource))]
        public string? ReportFileUrl { get; set; }

        /// <summary>
        /// 发布日期
        /// </summary>
        [Display(Name = "PublishDate", ResourceType = typeof(AdminResource))]
        public DateTime PublishDate { get; set; }

        /// <summary>
        /// 营业收入
        /// </summary>
        [Display(Name = "Revenue", ResourceType = typeof(AdminResource))]
        public decimal? Revenue { get; set; }

        /// <summary>
        /// 净利润
        /// </summary>
        [Display(Name = "NetIncome", ResourceType = typeof(AdminResource))]
        public decimal? NetIncome { get; set; }

        /// <summary>
        /// 总资产
        /// </summary>
        [Display(Name = "TotalAssets", ResourceType = typeof(AdminResource))]
        public decimal? TotalAssets { get; set; }

        /// <summary>
        /// 货币单位
        /// </summary>
        [Display(Name = "Currency", ResourceType = typeof(AdminResource))]
        public string? Currency { get; set; }

        /// <summary>
        /// 是否已发布
        /// </summary>
        [Display(Name = "IsPublished", ResourceType = typeof(AdminResource))]
        public bool IsPublished { get; set; } = true;
    }

    /// <summary>
    /// 股东大会视图模型
    /// </summary>
    public class ShareholderMeetingItemViewModel
    {
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 会议日期
        /// </summary>
        [Display(Name = "MeetingDate", ResourceType = typeof(AdminResource))]
        public DateTime MeetingDate { get; set; }

        /// <summary>
        /// 多语言会议标题
        /// </summary>
        [Display(Name = "MeetingTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Titles { get; set; } = new();

        /// <summary>
        /// 多语言会议描述
        /// </summary>
        [Display(Name = "MeetingDescription", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Descriptions { get; set; } = new();

        /// <summary>
        /// 会议状态
        /// </summary>
        [Display(Name = "MeetingStatus", ResourceType = typeof(AdminResource))]
        public MeetingStatus Status { get; set; }

        /// <summary>
        /// 会议文档
        /// </summary>
        [Display(Name = "MeetingDocuments", ResourceType = typeof(AdminResource))]
        public List<MeetingDocumentViewModel> Documents { get; set; } = new();
    }

    /// <summary>
    /// 会议文档视图模型
    /// </summary>
    public class MeetingDocumentViewModel
    {
        /// <summary>
        /// 多语言文档标题
        /// </summary>
        [Display(Name = "DocumentTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Titles { get; set; } = new();

        /// <summary>
        /// 多语言文档描述
        /// </summary>
        [Display(Name = "DocumentDescription", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Descriptions { get; set; } = new();

        /// <summary>
        /// 文档类型
        /// </summary>
        [Display(Name = "DocumentType", ResourceType = typeof(AdminResource))]
        public DocumentType Type { get; set; }

        /// <summary>
        /// 文档文件
        /// </summary>
        [Display(Name = "DocumentFile", ResourceType = typeof(AdminResource))]
        public string FileUrl { get; set; } = string.Empty;

        /// <summary>
        /// 上传日期
        /// </summary>
        public DateTime UploadDate { get; set; }
    }

    /// <summary>
    /// CSR活动编辑视图模型
    /// </summary>
    public class CSRActivityViewModel
    {
        public string Id { get; set; } = string.Empty;

        [Required]
        [Display(Name = "CSRCategory", ResourceType = typeof(AdminResource))]
        public CSRCategory Category { get; set; }

        [Required]
        [Display(Name = "StartDate", ResourceType = typeof(AdminResource))]
        public DateTime StartDate { get; set; }

        [Display(Name = "EndDate", ResourceType = typeof(AdminResource))]
        public DateTime? EndDate { get; set; }

        [Display(Name = "ActivityImages", ResourceType = typeof(AdminResource))]
        public List<string> ImageUrls { get; set; } = new();

        [Display(Name = "ReportFile", ResourceType = typeof(AdminResource))]
        public string? ReportFileUrl { get; set; }

        public bool IsActive { get; set; } = true;

        // Multilingual fields
        [Display(Name = "ActivityTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Titles { get; set; } = new();

        [Display(Name = "ActivityDescription", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Descriptions { get; set; } = new();

        [Display(Name = "ActivityImpact", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Impacts { get; set; } = new();
    }

    /// <summary>
    /// 财务报告编辑视图模型
    /// </summary>
    public class FinancialReportViewModel
    {
        public string Id { get; set; } = string.Empty;

        [Required]
        [Display(Name = "ReportType", ResourceType = typeof(AdminResource))]
        public ReportType Type { get; set; }


        [Required]
        [Display(Name = "Year", ResourceType = typeof(AdminResource))]
        public int Year { get; set; }

        [Display(Name = "Quarter", ResourceType = typeof(AdminResource))]
        public int? Quarter { get; set; }

        [Display(Name = "ReportFile", ResourceType = typeof(AdminResource))]
        public string? ReportFileUrl { get; set; }

        [Required]
        [Display(Name = "PublishDate", ResourceType = typeof(AdminResource))]
        public DateTime PublishDate { get; set; }

        [Display(Name = "Revenue", ResourceType = typeof(AdminResource))]
        public decimal? Revenue { get; set; }

        [Display(Name = "NetIncome", ResourceType = typeof(AdminResource))]
        public decimal? NetIncome { get; set; }

        [Display(Name = "TotalAssets", ResourceType = typeof(AdminResource))]
        public decimal? TotalAssets { get; set; }

        [Display(Name = "Currency", ResourceType = typeof(AdminResource))]
        public string? Currency { get; set; }

        public bool IsPublished { get; set; } = true;

        // Multilingual fields
        [Display(Name = "ReportTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Titles { get; set; } = new();

        [Display(Name = "ReportSummary", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Summaries { get; set; } = new();
    }

    /// <summary>
    /// 股东大会编辑视图模型
    /// </summary>
    public class ShareholderMeetingViewModel
    {
        public string Id { get; set; } = string.Empty;

        [Required]
        [Display(Name = "MeetingDate", ResourceType = typeof(AdminResource))]
        public DateTime MeetingDate { get; set; }

        [Required]
        [Display(Name = "MeetingStatus", ResourceType = typeof(AdminResource))]
        public MeetingStatus Status { get; set; }

        // Multilingual fields
        [Display(Name = "MeetingTitle", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Titles { get; set; } = new();

        [Display(Name = "MeetingAgenda", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Agendas { get; set; } = new();

        [Display(Name = "MeetingLocation", ResourceType = typeof(AdminResource))]
        public Dictionary<string, string> Locations { get; set; } = new();
    }
}