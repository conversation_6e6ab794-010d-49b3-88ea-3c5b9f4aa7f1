using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class DashboardViewModel
    {
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public DateTime LastLoginTime { get; set; }
        
        // 统计数据
        public int TotalCompanies { get; set; }
        public int TotalNews { get; set; }
        public int TotalComponents { get; set; }
        public int TotalPages { get; set; }
        
        // 最近活动
        public List<RecentActivity> RecentActivities { get; set; } = new List<RecentActivity>();
    }

    public class RecentActivity
    {
        public string Action { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}