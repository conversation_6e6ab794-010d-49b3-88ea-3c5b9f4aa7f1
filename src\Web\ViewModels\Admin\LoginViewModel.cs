using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Web.Resources;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class LoginViewModel
    {
        [Required(ErrorMessageResourceName = "UserNameRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [Display(Name = "UserName", ResourceType = typeof(AdminResource))]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceName = "PasswordRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [DataType(DataType.Password)]
        [Display(Name = "Password", ResourceType = typeof(AdminResource))]
        public string Password { get; set; } = string.Empty;

        [Display(Name = "RememberMe", ResourceType = typeof(AdminResource))]
        public bool RememberMe { get; set; }

        public string? ReturnUrl { get; set; }
    }
}