using MlSoft.Sites.Model.Entities.News;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Web.Resources;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class NewsAnnouncementViewModel
    {
        public string Id { get; set; } = string.Empty;
        public NewsType Type { get; set; }
        public DateTime PublishDate { get; set; }
        public List<string> ImageUrls { get; set; } = new();
        public string? ThumbnailUrl { get; set; }
        public bool IsFeatured { get; set; }
        public Dictionary<string, NewsAnnouncementLocaleFields> Locale { get; set; } = new();
        public NewsStatus Status { get; set; }

        [Range(0, 3, ErrorMessageResourceName = "PriorityRangeError", ErrorMessageResourceType = typeof(AdminResource))]
        public int Priority { get; set; }

        public List<string> Tags { get; set; } = new();
        public NewsSource Source { get; set; } = NewsSource.Internal;
        public string? ExternalUrl { get; set; }
        public int ViewCount { get; set; } = 0;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? CreatedById { get; set; }
        public string? LastModifiedById { get; set; }
        public List<string> RelatedNewsIds { get; set; } = new();
    }
}
