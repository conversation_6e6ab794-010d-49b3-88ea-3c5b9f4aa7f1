using MlSoft.Sites.Model.Entities.News;
using MlSoft.Sites.Model.Entities.Enums;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class NewsStatisticsViewModel
    {
        public int TotalPublished { get; set; }
        public int Drafts { get; set; }
        public int Featured { get; set; }
        public int TodayPublished { get; set; }
        public Dictionary<NewsType, int> TypeDistribution { get; set; } = new();
        public List<NewsAnnouncement> TopViewedNews { get; set; } = new();
        public Dictionary<string, int> MonthlyPublishTrend { get; set; } = new();
        public Dictionary<string, int> ReviewStatistics { get; set; } = new();
    }
}
