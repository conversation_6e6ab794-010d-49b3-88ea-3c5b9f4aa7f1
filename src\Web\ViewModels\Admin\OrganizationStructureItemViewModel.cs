using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class OrganizationStructureItemViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string? ParentDepartmentId { get; set; }
        public Dictionary<string, string> DepartmentNames { get; set; } = new();
        public Dictionary<string, string> Descriptions { get; set; } = new();
        public int Level { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}