using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Pages;
using MlSoft.Sites.Web.Resources;
using MongoDB.Bson;

namespace MlSoft.Sites.Web.ViewModels.Admin
{

    /// <summary>
    /// 页面配置编辑视图模型
    /// </summary>
    public class PageConfigurationEditViewModel
    {
        // PageConfiguration 元数据字段
        public string Id { get; set; } = string.Empty;

        [Required]
        public Dictionary<string, string> Name { get; set; } = new();

        public PageStatus Status { get; set; } = PageStatus.Draft;
        public DateTime? PublishDate { get; set; }

        // PageConfigContent 详细配置字段
        [Required]
        public string PageKey { get; set; } = string.Empty;

        [Required]
        public string Route { get; set; } = string.Empty;

        public string LayoutTemplate { get; set; } = "_Layout";

        public List<PageComponentViewModel> Components { get; set; } = new();

        /// <summary>
        /// 组件配置的JSON字符串（用于前端表单提交）
        /// </summary>
        public string ComponentsJson { get; set; } = string.Empty;

        public string? GeneratedViewPath { get; set; }

        // 配置选项
        public PagePerformanceConfigViewModel Performance { get; set; } = new();
        public PageCacheConfigViewModel CacheConfig { get; set; } = new();
        public PageAccessControlViewModel AccessControl { get; set; } = new();

        // 可用组件列表（用于编辑器）
        public List<ComponentDefinitionViewModel> AvailableComponents { get; set; } = new();

        // 可用布局模板列表
        public List<string> AvailableLayouts { get; set; } = new() { "_Layout" };

        /// <summary>
        /// 是否为新建页面
        /// </summary>
        public bool IsNew => string.IsNullOrEmpty(Id);

        /// <summary>
        /// 转换为 PageConfigContent
        /// </summary>
        public PageConfigContent ToPageConfigContent()
        {
            // 优先使用ComponentsJson，如果不为空的话
            var components = new List<PageComponentConfig>();
            
            if (!string.IsNullOrWhiteSpace(ComponentsJson))
            {
                try
                {
                    var componentViewModels = Newtonsoft.Json.JsonConvert.DeserializeObject<List<PageComponentViewModel>>(ComponentsJson);
                    if (componentViewModels != null)
                    {
                        components = componentViewModels.Select(c => new PageComponentConfig
                        {
                            ComponentDefinitionId = c.ComponentDefinitionId,
                            TemplateKey = c.TemplateKey,
                            ParametersJson = c.ParametersJson,
                            DisplayOrder = c.DisplayOrder,
                            ColumnSpan = c.ColumnSpan,
                            IsVisible = c.IsVisible,
                            IdNo = string.IsNullOrEmpty(c.IdNo)? ObjectId.GenerateNewId().ToString().Substring(16):c.IdNo
                        }).ToList();
                    }
                }
                catch (System.Text.Json.JsonException)
                {
                    // 如果JSON解析失败，回退到使用Components集合
                    components = Components.Select(c => new PageComponentConfig
                    {
                        ComponentDefinitionId = c.ComponentDefinitionId,
                        TemplateKey = c.TemplateKey,
                        ParametersJson = c.ParametersJson,
                        DisplayOrder = c.DisplayOrder,
                        ColumnSpan = c.ColumnSpan,
                        IsVisible = c.IsVisible,
                        IdNo = string.IsNullOrEmpty(c.IdNo) ? ObjectId.GenerateNewId().ToString().Substring(16) : c.IdNo
                    }).ToList();
                }
            }
            else
            {
                // 如果ComponentsJson为空，使用Components集合
                components = Components.Select(c => new PageComponentConfig
                {
                    ComponentDefinitionId = c.ComponentDefinitionId,
                    TemplateKey = c.TemplateKey,
                    ParametersJson = c.ParametersJson,
                    DisplayOrder = c.DisplayOrder,
                    IsVisible = c.IsVisible,
                    IdNo = string.IsNullOrEmpty(c.IdNo) ? ObjectId.GenerateNewId().ToString().Substring(16) : c.IdNo
                }).ToList();
            }

            return new PageConfigContent
            {
                PageKey = PageKey,
                Route = Route,
                LayoutTemplate = LayoutTemplate,
                Components = components,
                GeneratedViewPath = GeneratedViewPath,
                Performance = new PagePerformanceConfig
                {
                    EnableImageLazyLoading = Performance.EnableImageLazyLoading,
                    EnableComponentLazyLoading = Performance.EnableComponentLazyLoading,
                    MaxComponentsPerPage = Performance.MaxComponentsPerPage,
                    EnableBundleOptimization = Performance.EnableBundleOptimization,
                    ImageQuality = Performance.ImageQuality,
                    EnableWebpFormat = Performance.EnableWebpFormat
                },
                CacheConfig = new PageCacheConfiguration
                {
                    CacheDurationMinutes = CacheConfig.CacheDurationMinutes,
                    CacheVaryByParams = CacheConfig.CacheVaryByParams,
                    EnableCDNCache = CacheConfig.EnableCDNCache,
                    CacheInvalidationTags = CacheConfig.CacheInvalidationTags,
                    EnableOutputCache = CacheConfig.EnableOutputCache,
                    CacheProfile = CacheConfig.CacheProfile
                },
                AccessControl = new PageAccessControlConfig
                {
                    RequireAuthentication = AccessControl.RequireAuthentication,
                    RequiredRoles = AccessControl.RequiredRoles,
                    AllowedUsers = AccessControl.AllowedUsers,
                    PublishDate = AccessControl.PublishDate,
                    ExpiryDate = AccessControl.ExpiryDate,
                    AllowedCountries = AccessControl.AllowedCountries,
                    EnableGeoRestriction = AccessControl.EnableGeoRestriction
                }
            };
        }

        /// <summary>
        /// 从 PageConfigContent 加载数据
        /// </summary>
        public void LoadFromPageConfigContent(PageConfigContent content)
        {
            PageKey = content.PageKey;
            Route = content.Route;
            LayoutTemplate = content.LayoutTemplate;
            GeneratedViewPath = content.GeneratedViewPath;
           
            Components = content.Components.Select(c => new PageComponentViewModel
            {
                ComponentDefinitionId = c.ComponentDefinitionId,
                TemplateKey = c.TemplateKey,
                ColumnSpan = c.ColumnSpan,
                ParametersJson = c.ParametersJson,
                DisplayOrder = c.DisplayOrder,
                IsVisible = c.IsVisible,
                IdNo = c.IdNo
            }).ToList();

            Performance.EnableImageLazyLoading = content.Performance.EnableImageLazyLoading;
            Performance.EnableComponentLazyLoading = content.Performance.EnableComponentLazyLoading;
            Performance.MaxComponentsPerPage = content.Performance.MaxComponentsPerPage;
            Performance.EnableBundleOptimization = content.Performance.EnableBundleOptimization;
            Performance.ImageQuality = content.Performance.ImageQuality;
            Performance.EnableWebpFormat = content.Performance.EnableWebpFormat;

            CacheConfig.CacheDurationMinutes = content.CacheConfig.CacheDurationMinutes;
            CacheConfig.CacheVaryByParams = content.CacheConfig.CacheVaryByParams;
            CacheConfig.EnableCDNCache = content.CacheConfig.EnableCDNCache;
            CacheConfig.CacheInvalidationTags = content.CacheConfig.CacheInvalidationTags;
            CacheConfig.EnableOutputCache = content.CacheConfig.EnableOutputCache;
            CacheConfig.CacheProfile = content.CacheConfig.CacheProfile;

            AccessControl.RequireAuthentication = content.AccessControl.RequireAuthentication;
            AccessControl.RequiredRoles = content.AccessControl.RequiredRoles;
            AccessControl.AllowedUsers = content.AccessControl.AllowedUsers;
            AccessControl.PublishDate = content.AccessControl.PublishDate;
            AccessControl.ExpiryDate = content.AccessControl.ExpiryDate;
            AccessControl.AllowedCountries = content.AccessControl.AllowedCountries;
            AccessControl.EnableGeoRestriction = content.AccessControl.EnableGeoRestriction;
        }
    }

    /// <summary>
    /// 页面组件视图模型
    /// </summary>
    public class PageComponentViewModel
    {
        public string ComponentDefinitionId { get; set; } = string.Empty;
        public string TemplateKey { get; set; } = string.Empty;
        public string ParametersJson { get; set; } = string.Empty;
        public int DisplayOrder { get; set; }

        public int ColumnSpan { get; set; } = 12;
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// 组件在当前页面中的唯一标识，每次添加时会自动加上，保证一个页面有多个相同组件时，在页面填充数据时不会搞乱。
        /// </summary>
        public string IdNo { get; set; } = string.Empty;

        // 用于UI显示的附加字段
        public string ComponentName { get; set; } = string.Empty;
        public string ComponentDescription { get; set; } = string.Empty;
        public List<string> AvailableTemplates { get; set; } = new();
    }

    /// <summary>
    /// 页面性能配置视图模型
    /// </summary>
    public class PagePerformanceConfigViewModel
    {
        public bool EnableImageLazyLoading { get; set; } = true;
        public bool EnableComponentLazyLoading { get; set; } = false;

        [Range(1, 100)]
        public int MaxComponentsPerPage { get; set; } = 20;

        public bool EnableBundleOptimization { get; set; } = true;

        [Range(1, 100)]
        public int ImageQuality { get; set; } = 85;

        public bool EnableWebpFormat { get; set; } = true;
    }

    /// <summary>
    /// 页面缓存配置视图模型
    /// </summary>
    public class PageCacheConfigViewModel
    {
        [Range(0, 10080)] // 最大1周
        public int CacheDurationMinutes { get; set; } = 60;

        public List<string> CacheVaryByParams { get; set; } = new();
        public bool EnableCDNCache { get; set; } = true;
        public List<string> CacheInvalidationTags { get; set; } = new();
        public bool EnableOutputCache { get; set; } = true;
        public string CacheProfile { get; set; } = string.Empty;
    }

    /// <summary>
    /// 页面访问控制视图模型
    /// </summary>
    public class PageAccessControlViewModel
    {
        public bool RequireAuthentication { get; set; } = false;
        public List<string> RequiredRoles { get; set; } = new();
        public List<string> AllowedUsers { get; set; } = new();
        public DateTime? PublishDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public List<string> AllowedCountries { get; set; } = new();
        public bool EnableGeoRestriction { get; set; } = false;
    }

    /// <summary>
    /// 组件定义视图模型
    /// </summary>
    public class ComponentDefinitionViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public List<string> AvailableTemplates { get; set; } = new();
        public string DefaultTemplate { get; set; } = string.Empty;
        public string ParameterSchema { get; set; } = string.Empty;
        public string IconCssClass { get; set; } = string.Empty;
    }


}