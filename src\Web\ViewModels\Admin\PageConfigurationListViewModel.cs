

using MlSoft.Sites.Model.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using MlSoft.Sites.Web.Resources;
using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Localization;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    /// <summary>
    /// 页面配置列表视图模型
    /// </summary>
    public class PageConfigurationListViewModel
    {
        public List<PageConfigurationListItemViewModel> Pages { get; set; } = new();
        public long TotalCount { get; set; }
        public int CurrentPage { get; set; }
        public int PageSize { get; set; } = 20;
        public PageStatus? FilterStatus { get; set; }
        public string? SearchKeyword { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    /// <summary>
    /// 页面配置列表项视图模型
    /// </summary>
    public class PageConfigurationListItemViewModel
    {
        public string Id { get; set; } = string.Empty;
        public Dictionary<string, string> Name { get; set; } = new();
        public string PageKey { get; set; } = string.Empty;
        public string Route { get; set; } = string.Empty;
        public PageStatus Status { get; set; }
        public DateTime? PublishDate { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        public int ComponentCount { get; set; }

        /// <summary>
        /// 获取当前语言的页面名称
        /// </summary>
        public string GetDisplayName(string language = "zh")
        {
            if (Name.TryGetValue(language, out var name) && !string.IsNullOrWhiteSpace(name))
                return name;

            // 如果当前语言不存在，尝试其他语言
            if (Name.Count > 0)
                return Name.Values.First(v => !string.IsNullOrWhiteSpace(v));

            return PageKey; // 最后退回到 PageKey
        }

        /// <summary>
        /// 获取状态显示文本
        /// </summary>
        public string GetStatusDisplayText(IStringLocalizer<AdminResource> localizer)
        {
            return Status switch
            {
                PageStatus.Published => localizer["Published"],
                PageStatus.Draft => localizer["Draft"],
                PageStatus.Disabled => localizer["Disabled"],
                _ => localizer["Unknown"]
            };
        }



        /// <summary>
        /// 获取状态CSS类
        /// </summary>
        public string StatusCssClass => Status switch
        {
            PageStatus.Published => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
            PageStatus.Draft => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
            PageStatus.Disabled => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
            _ => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
        };
    }

}