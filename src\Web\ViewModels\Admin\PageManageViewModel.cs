using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.Pages;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    /// <summary>
    /// 网页内容管理编辑视图模型
    /// </summary>
    public class PageManageEditViewModel
    {
        public string? Id { get; set; }

        [Display(Name = "页面配置ID")]
        [Required(ErrorMessage = "页面配置ID不能为空")]
        public string PageConfigurationId { get; set; } = string.Empty;

        [Display(Name = "页面标识")]
        [Required(ErrorMessage = "页面标识不能为空")]
        public string PageKey { get; set; } = string.Empty;

        [Display(Name = "页面名称")]
        public Dictionary<string, string> PageName { get; set; } = new();

        [Display(Name = "组件内容")]
        public List<ComponentDataViewModel> ComponentsData { get; set; } = new();

        [Display(Name = "状态")]
        public PageContentStatus Status { get; set; }

        [Display(Name = "发布日期")]
        public DateTime? PublishDate { get; set; }

        [Display(Name = "版本")]
        public int Version { get; set; } = 1;

        public string IdNo { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 可用组件列表
        /// </summary>
        public List<ComponentDefinitionViewModel> AvailableComponents { get; set; } = new();

        /// <summary>
        /// 支持的语言列表
        /// </summary>
        public List<string> SupportedLanguages { get; set; } = new() { "zh", "en", "ja" };
    }

    /// <summary>
    /// 组件数据视图模型
    /// </summary>
    public class ComponentDataViewModel
    {
        [Display(Name = "组件类型")]
        public string ComponentDefinitionId { get; set; } = string.Empty;

        [Display(Name = "模板")]
        public string TemplateKey { get; set; } = string.Empty;

        [Display(Name = "显示顺序")]
        public int DisplayOrder { get; set; }

        [Display(Name = "是否可见")]
        public bool IsVisible { get; set; } = true;

        [Display(Name = "组件数据")]
        public string DataJson { get; set; } = string.Empty;

        /// <summary>
        /// 页面配置时，添加组件时生成的IdNo,用于关联数据，取代用 TemplateKey+DisplayOrder 处理页面多个相同组件时数据问题
        /// </summary>
        public string IdNo { get; set; }

        /// <summary>
        /// 组件的多语言字段列表
        /// </summary>
        public List<string> MultilingualFields { get; set; } = new();

        /// <summary>
        /// 可用模板列表
        /// </summary>
        public List<ComponentVariantInfo> AvailableTemplates { get; set; } = new();

        public DateTime LastModified { get; set; }
    }

    /// <summary>
    /// 组件变体信息
    /// </summary>
    public class ComponentVariantInfo
    {
        public string Id { get; set; } = string.Empty;
        public Dictionary<string, string> Names { get; set; } = new();
        public Dictionary<string, string> Descriptions { get; set; } = new();
    }

    /// <summary>
    /// 网页内容管理列表视图模型
    /// </summary>
    public class PageManageListViewModel
    {
        public List<PageManageListItemViewModel> Contents { get; set; } = new();
        public long TotalCount { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public PageContentStatus? FilterStatus { get; set; }
        public string? SearchKeyword { get; set; }
        
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    /// <summary>
    /// 网页内容管理列表项视图模型
    /// </summary>
    public class PageManageListItemViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string PageConfigurationId { get; set; } = string.Empty;
        public string PageKey { get; set; } = string.Empty;
        public Dictionary<string, string> PageName { get; set; } = new();
        public PageContentStatus Status { get; set; }
        public DateTime? PublishDate { get; set; }
        public int Version { get; set; }
        public int ComponentCount { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        
        /// <summary>
        /// 是否已有内容数据
        /// </summary>
        public bool HasContent { get; set; }
    }
}