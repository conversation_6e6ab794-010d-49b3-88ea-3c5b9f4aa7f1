using System;
using System.Collections.Generic;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Model.Entities.LocaleFields;
using MlSoft.Sites.Model.Entities.Common;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    public class RecruitmentIndexViewModel
    {
        public PagedResult<JobPositionViewModel> Jobs { get; set; } = new();
        public List<JobType> AvailableJobTypes { get; set; } = new();
        public List<EmploymentType> AvailableEmploymentTypes { get; set; } = new();
        public List<ExperienceLevel> AvailableExperienceLevels { get; set; } = new();
        public List<InterviewType> AvailableInterviewTypes { get; set; } = new();
    }

    public class JobPositionsTabViewModel
    {
        public List<JobPositionViewModel> Jobs { get; set; } = new();
        public Dictionary<string, object> Statistics { get; set; } = new();
    }

    public class EmployeeInterviewsTabViewModel
    {
        public List<EmployeeInterviewViewModel> Interviews { get; set; } = new();

        public Dictionary<string, string> Organs { get; set; } 
    }

    public class JobPositionViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string JobTitle { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string EmploymentType { get; set; } = string.Empty;
        public string ExperienceLevel { get; set; } = string.Empty;
        public DateTime PostDate { get; set; }
        public DateTime? ApplicationDeadline { get; set; }
        public bool IsActive { get; set; }
        public bool IsFeatured { get; set; }
        public string SalaryRange { get; set; } = string.Empty;
        public int ApplicationCount { get; set; }
        public string? WorkingHours { get; set; }
    }

    public class EmployeeInterviewViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string DepartmentId { get; set; } = string.Empty;
        public int YearsOfService { get; set; }
        public DateTime InterviewDate { get; set; }
        public string? PhotoUrl { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsFeatured { get; set; }
        public string Status { get; set; } = string.Empty;
        public string InterviewType { get; set; } = string.Empty;
    }

    // Request Models for API operations
    public class JobPositionCreateRequest
    {
        public JobType Type { get; set; }
        public EmploymentType EmploymentType { get; set; }
        public ExperienceLevel ExperienceLevel { get; set; }
        public decimal? SalaryMin { get; set; }
        public decimal? SalaryMax { get; set; }
        public string? Currency { get; set; }
        public DateTime PostDate { get; set; }
        public DateTime? ApplicationDeadline { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsFeatured { get; set; }
        public string? DepartmentId { get; set; }
        public string? WorkingHours { get; set; }
        public string? ProbationPeriod { get; set; }
        public Dictionary<string, JobPositionLocaleFields>? Locale { get; set; }
    }

    public class JobPositionUpdateRequest : JobPositionCreateRequest
    {
        // Inherits all properties from create request
    }

    public class EmployeeInterviewCreateRequest
    {
        public string? DepartmentId { get; set; }
        public int YearsOfService { get; set; }
        public string? PhotoUrl { get; set; }
        public List<string>? Tags { get; set; }
        public DateTime InterviewDate { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsPublished { get; set; } = true;
        public bool IsFeatured { get; set; }
        public PublishStatus Status { get; set; } = PublishStatus.Draft;
        public InterviewType InterviewType { get; set; }
        public int SortOrder { get; set; }
        public Dictionary<string, EmployeeInterviewLocaleFields>? Locale { get; set; }
    }

    public class EmployeeInterviewUpdateRequest : EmployeeInterviewCreateRequest
    {
        // Inherits all properties from create request
    }

}