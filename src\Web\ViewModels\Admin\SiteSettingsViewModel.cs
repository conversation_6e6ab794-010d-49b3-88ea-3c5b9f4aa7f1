using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Web.Resources;
using MlSoft.Sites.Web.Services.Components;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    /// <summary>
    /// 站点设置页面视图模型
    /// </summary>
    public class SiteSettingsViewModel
    {
        /// <summary>
        /// 基本信息设置
        /// </summary>
        public BasicInfoSettings BasicInfo { get; set; } = new();
        
        /// <summary>
        /// 主题设置
        /// </summary>
        public ThemeSettings ThemeSettings { get; set; } = new();
        
        /// <summary>
        /// 全局组件设置
        /// </summary>
        public ComponentSettings ComponentSettings { get; set; } = new();
    }
    
    /// <summary>
    /// 基本信息设置
    /// </summary>
    public class BasicInfoSettings
    {
        /// <summary>
        /// 多语言站点名称 (语言代码 -> 站点名称)
        /// </summary>
        [Display(Name = "SiteName", ResourceType = typeof(AdminResource))]
        [Required(ErrorMessageResourceName = "SiteNameRequired", ErrorMessageResourceType = typeof(AdminResource))]
        public Dictionary<string, string> SiteNames { get; set; } = new();
        
        
        /// <summary>
        /// 域名
        /// </summary>
        [Display(Name = "Domain", ResourceType = typeof(AdminResource))]
        [Required(ErrorMessageResourceName = "DomainRequired", ErrorMessageResourceType = typeof(AdminResource))]
        [MaxLength(200, ErrorMessageResourceName = "DomainMaxLength", ErrorMessageResourceType = typeof(AdminResource))]
        public string Domain { get; set; } = string.Empty;


        public string CountCode { get; set;  } = string.Empty;
        
        ///// <summary>
        ///// 默认语言
        ///// </summary>
        //[Display(Name = "DefaultLanguage", ResourceType = typeof(AdminResource))]
        //[Required(ErrorMessageResourceName = "DefaultLanguageRequired", ErrorMessageResourceType = typeof(AdminResource))]
        //public string DefaultLanguage { get; set; } = "zh";
        
        
        /// <summary>
        /// Logo文件名 (存储在 UploadRootPath + EnumFolderType.BasicInfo 目录)
        /// </summary>
        [Display(Name = "Logo", ResourceType = typeof(AdminResource))]
        public string? LogoFileName { get; set; }
        
        /// <summary>
        /// Favicon文件名 (存储在 UploadRootPath + EnumFolderType.BasicInfo 目录)
        /// </summary>
        [Display(Name = "Favicon", ResourceType = typeof(AdminResource))]
        public string? FaviconFileName { get; set; }
        
        /// <summary>
        /// Logo完整URL (用于显示)
        /// </summary>
        public string? LogoUrl => !string.IsNullOrEmpty(LogoFileName) ? $"/upload/basic/{LogoFileName}" : null;
        
        /// <summary>
        /// Favicon完整URL (用于显示)
        /// </summary>
        public string? FaviconUrl => !string.IsNullOrEmpty(FaviconFileName) ? $"/{FaviconFileName}" : null;
    }
    
    /// <summary>
    /// 主题设置
    /// </summary>
    public class ThemeSettings
    {
        /// <summary>
        /// 可用主题列表
        /// </summary>
        public List<ThemeOption> AvailableThemes { get; set; } = new();
        
        /// <summary>
        /// 当前主题ID
        /// </summary>
        public string CurrentThemeId { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// 主题选项
    /// </summary>
    public class ThemeOption
    {
        /// <summary>
        /// 主题ID
        /// </summary>
        public string Id { get; set; } = string.Empty;
        
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 主题描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// 预览图片
        /// </summary>
        public string PreviewImage { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否为当前激活主题
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 颜色预览
        /// </summary>
        public Dictionary<string, string> ColorPreview { get; set; } = new();
        
        /// <summary>
        /// 主题版本
        /// </summary>
        public string Version { get; set; } = "1.0.0";
        
        /// <summary>
        /// 作者
        /// </summary>
        public string Author { get; set; } = "MlSoft";
    }
    
    /// <summary>
    /// 组件设置
    /// </summary>
    public class ComponentSettings
    {
        /// <summary>
        /// Header组件变体
        /// </summary>
        [Display(Name = "HeaderStyle", ResourceType = typeof(AdminResource))]
        public string HeaderVariant { get; set; } = "Default";
        
        /// <summary>
        /// Footer组件变体
        /// </summary>
        [Display(Name = "FooterStyle", ResourceType = typeof(AdminResource))]
        public string FooterVariant { get; set; } = "Default";

        public string CookieVariant { get; set; } = "Default";
        
        /// <summary>
        /// 可用的Header变体选项
        /// </summary>
        public List<ComponentVariantOption> HeaderVariants { get; set; } = new();
        
        /// <summary>
        /// 可用的Footer变体选项
        /// </summary>
        public List<ComponentVariantOption> FooterVariants { get; set; } = new();

        public List<ComponentVariantOption> CookieVariants { get; set; } = new();

    }
    
    
}