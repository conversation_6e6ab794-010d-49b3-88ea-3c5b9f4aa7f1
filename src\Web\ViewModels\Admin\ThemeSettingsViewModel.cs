using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Admin
{
    /// <summary>
    /// 主题设置页面视图模型
    /// </summary>
    public class ThemeSettingsViewModel
    {
        /// <summary>
        /// 可用主题列表
        /// </summary>
        public List<ThemeInfo> AvailableThemes { get; set; } = new();
        
        /// <summary>
        /// 当前激活的主题
        /// </summary>
        public ThemeInfo? CurrentTheme { get; set; }
        
        /// <summary>
        /// 公司ID
        /// </summary>
        public string CompanyId { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否有备份可恢复
        /// </summary>
        public bool HasBackup { get; set; }
        
        /// <summary>
        /// 备份主题信息
        /// </summary>
        public ThemeInfo? BackupTheme { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? LastUpdated { get; set; }
        
        /// <summary>
        /// 最后更新者
        /// </summary>
        public string? LastUpdatedBy { get; set; }
    }
    
    /// <summary>
    /// 主题信息（简化版，用于列表显示）
    /// </summary>
    public class ThemeInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string PreviewImage { get; set; } = string.Empty;
        public bool IsBuiltIn { get; set; } = true;
        public bool IsActive { get; set; }
        public Dictionary<string, string> ColorPreview { get; set; } = new();
        public string Version { get; set; } = "1.0.0";
        public string Author { get; set; } = "MlSoft";
    }
}