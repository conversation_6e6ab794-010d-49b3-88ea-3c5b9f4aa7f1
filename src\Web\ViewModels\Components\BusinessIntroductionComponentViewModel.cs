using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class BusinessIntroductionComponentViewModel
    {
        public string? TitleText { get; set; }
        public string? Description { get; set; }
        public List<BusinessItem> BusinessItems { get; set; } = new();
        
        // Display settings
        public int ColumnsDesktop { get; set; } = 4;
        public int ColumnsTablet { get; set; } = 2;
        public int ColumnsMobile { get; set; } = 1;
    }

    public class BusinessItem
    {
        public string? Icon { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Features { get; set; }
        public string? ButtonText { get; set; }
        public string? ButtonUrl { get; set; }
    }
}