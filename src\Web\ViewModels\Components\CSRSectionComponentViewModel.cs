using System.Collections.Generic;

using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CSRSectionComponentViewModel
    {
        public string? Title { get; set; }
        public string? Description { get; set; }
        public List<CSRInitiativeItem> Initiatives { get; set; } = new();
        
        // Goals section
        public string? GoalsTitle { get; set; }
        public string? Goals { get; set; }
        public string? GoalsButtonText { get; set; }
        public string? GoalsButtonUrl { get; set; }
        
        // Image section
        public string? CSRImage { get; set; }
        public string? CSRImageAlt { get; set; }
        
        // Display settings
        public string? BackgroundColor { get; set; } = "muted";
        public int ColumnsDesktop { get; set; } = 4;
        public int ColumnsTablet { get; set; } = 2;
        public string? ButtonVariant { get; set; } = "primary";
    }

    public class CSRInitiativeItem
    {
        public string? Icon { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
    }
}