namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CTAComponentViewModel
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Description { get; set; }

        // Primary CTA button
        public string? PrimaryButtonText { get; set; }
        public string? PrimaryButtonUrl { get; set; }
        public string? PrimaryButtonStyle { get; set; } = "primary";
        public bool PrimaryButtonOpenNewTab { get; set; } = false;

        // Secondary CTA button (optional)
        public string? SecondaryButtonText { get; set; }
        public string? SecondaryButtonUrl { get; set; }
        public string? SecondaryButtonStyle { get; set; } = "secondary";
        public bool SecondaryButtonOpenNewTab { get; set; } = false;

        // Visual settings
        public string? BackgroundColor { get; set; } = "primary";
        public string? BackgroundImage { get; set; }
        public string? TextColor { get; set; } = "white";
        public string? TextAlignment { get; set; } = "center";
        public string? Size { get; set; } = "medium"; // small, medium, large

        // Layout settings
        public string? Layout { get; set; } = "horizontal"; // horizontal, vertical
        public string? Spacing { get; set; } = "normal";
        public string? BorderRadius { get; set; } = "normal";
        public bool ShowShadow { get; set; } = true;

        // Animation settings
        public bool AnimationEnabled { get; set; } = true;
        public string? AnimationType { get; set; } = "fadeIn";

        // Background overlay (for background images)
        public bool ShowOverlay { get; set; } = true;
        public string? OverlayOpacity { get; set; } = "0.7";
    }
}