using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CarouselComponentViewModel
    {
        public List<CarouselItem> Items { get; set; } = new();
        public CarouselSettings Settings { get; set; } = new();
        public string? CarouselType { get; set; } = "banner"; // banner, gallery, slider
        public string Id { get; set; } = Guid.NewGuid().ToString("N")[..8];
    }

    public class CarouselItem
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Content { get; set; }
        public string? ImageUrl { get; set; }
        public string? LinkUrl { get; set; }
        public bool OpenInNewTab { get; set; } = false;
        public string? BackgroundColor { get; set; }
        public string? TextColor { get; set; }
        public string? Alt { get; set; }
    }

    public class CarouselSettings
    {
        public bool AutoPlay { get; set; } = true;
        public int AutoPlayInterval { get; set; } = 5000; // milliseconds
        public bool ShowIndicators { get; set; } = true;
        public bool ShowNavigation { get; set; } = true;
        public bool InfiniteLoop { get; set; } = true;
        public int TransitionDuration { get; set; } = 600; // milliseconds
        public string? Height { get; set; } = "auto"; // auto, fixed, responsive
        public string? CustomHeight { get; set; } // e.g., "400px", "50vh"
        public bool ShowCaptions { get; set; } = true;
        public string? CaptionPosition { get; set; } = "bottom"; // bottom, top, center, overlay
    }
}