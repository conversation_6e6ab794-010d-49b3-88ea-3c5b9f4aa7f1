using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CompanyBasicInfoComponentViewModel
    {
        // 显示设置 - 这些是通过组件配置控制的
        public string? Layout { get; set; } = "grid"; // grid, list, card
        public bool ShowTitle { get; set; } = true;
        public string? TitleText { get; set; }
        public bool ShowBorder { get; set; } = true;
        public string? BackgroundStyle { get; set; } = "white"; // white, gray, transparent

        // 字段显示控制 - 控制哪些字段显示
        public bool ShowEstablishedDate { get; set; } = true;
        public bool ShowCapital { get; set; } = true;
        public bool ShowEmployeeScale { get; set; } = true;
        public bool ShowPresident { get; set; } = true;
        public bool ShowAddress { get; set; } = true;
        public bool ShowPostalCode { get; set; } = true;
        public bool ShowPhone { get; set; } = true;
        public bool ShowEmail { get; set; } = true;
        public bool ShowWebsite { get; set; } = true;
        public bool ShowRegistrationNumber { get; set; } = false;

        // 实际的公司数据 - 这些从Company实体中读取
        public Model.Entities.Company.Company? CompanyData { get; set; }
        
        // 社长数据 - 从Executive实体中读取
        public Model.Entities.Organization.Executive? PresidentData { get; set; }
    }
}