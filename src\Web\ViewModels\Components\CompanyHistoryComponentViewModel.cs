using System.Collections.Generic;
using MlSoft.Sites.Model.Entities.History;
using MlSoft.Sites.Model.Entities.Enums;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CompanyHistoryComponentViewModel
    {
        // 显示设置
        public string? Layout { get; set; } = "timeline"; // timeline, cards
        public bool ShowTitle { get; set; } = true;
        public string? TitleText { get; set; }
        public string? Description { get; set; }
        public string? BackgroundStyle { get; set; } = "white"; // white, gray, transparent
        public bool ShowEventImages { get; set; } = true;

        public bool ShowYearLabels { get; set; } = true;

        public bool EnableAnimation { get; set; } = true;

        public string? BorderRadius { get; set; } = "md"; // sm, md, lg, xl, full
        public string? Spacing { get; set; } = "normal"; // compact, normal, loose

        // 数据 - 从CompanyHistoryService获取
        public List<CompanyHistory>? HistoryData { get; set; }
    }
}