using System.Collections.Generic;
using MlSoft.Sites.Model.Entities.Company;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CompanyLocationsComponentViewModel
    {
        public List<CompanyLocation>? Locations { get; set; }
        // Display settings with defaults
        public string? Layout { get; set; } = "grid";
        public bool ShowMap { get; set; } = true;
        public bool ShowContactInfo { get; set; } = true;
        public bool ShowPrimaryBadge { get; set; } = true;
        public int ItemsPerRow { get; set; } = 3;
        public string? BackgroundColor { get; set; } = "transparent";
        public bool ShowTitle { get; set; } = true;
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
    }
}