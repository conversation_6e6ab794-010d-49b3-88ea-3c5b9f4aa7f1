using System.Collections.Generic;

using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CompanyOverviewComponentViewModel
    {

        public string? Description { get; set; }
        public List<CompanyStatItem> Stats { get; set; } = new();
        

        public string? PhilosophyContent { get; set; }

        public string? PhilosophyButtonUrl { get; set; }

        // Image section
        public string? CompanyImage { get; set; }
        public string? CompanyImageAlt { get; set; }
    }

    public class CompanyStatItem
    {
        public string? Icon { get; set; }
        public string? Label { get; set; }
        public string? Value { get; set; }
    }
}