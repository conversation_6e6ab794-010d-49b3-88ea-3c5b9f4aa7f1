using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class ContentComponentViewModel
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Content { get; set; }
        public List<ContentBlock> ContentBlocks { get; set; } = new();
        public string? Layout { get; set; } = "grid"; // grid,list
        public bool ShowDivider { get; set; } = false;

        public bool AnimationEnabled { get; set; } = true;
    }

    public class ContentBlock
    {
        public string? Title { get; set; }
        public string? Content { get; set; }
        public string? Image { get; set; }
        public string? Link { get; set; }
        public string? LinkText { get; set; }
    }
}