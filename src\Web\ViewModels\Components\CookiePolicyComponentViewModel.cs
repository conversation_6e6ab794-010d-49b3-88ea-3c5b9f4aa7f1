namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CookiePolicyComponentViewModel
    {
        public string? Title { get; set; }
        public string? Message { get; set; }
        public string? AcceptButtonText { get; set; }
        public string? DeclineButtonText { get; set; }
        public string? LearnMoreText { get; set; }
        public string? LearnMoreUrl { get; set; }
        
        // Display settings
        public string? Position { get; set; } = "bottom"; // bottom, top
        public bool ShowDeclineButton { get; set; } = false;
        public bool ShowLearnMoreLink { get; set; } = true;
        public string? BackgroundColor { get; set; } = "dark"; // dark, light
        public bool AutoHide { get; set; } = false;
        public int AutoHideDelay { get; set; } = 5000; // milliseconds
    }
}