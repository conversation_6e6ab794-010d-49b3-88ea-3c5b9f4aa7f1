using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class CustomerCaseShowcaseComponentViewModel
    {
        public string? TitleText { get; set; }
        public string? SubtitleText { get; set; }
        public string? Description { get; set; }
        public List<CustomerCase> CustomerCases { get; set; } = new();

        // Display settings
        public int ColumnsDesktop { get; set; } = 3;
        public int ColumnsTablet { get; set; } = 2;
        public int ColumnsMobile { get; set; } = 1;
        public bool ShowIndustryFilter { get; set; } = true;
        public bool ShowTestimonials { get; set; } = true;
        public bool ShowCompanyLogos { get; set; } = true;
        public string? BackgroundStyle { get; set; } = "light"; // light, dark, gradient
        public bool EnableCarousel { get; set; } = false;

        // CTA settings
        public string? CtaButtonText { get; set; }
        public string? CtaButtonUrl { get; set; }
        public bool ShowCtaButton { get; set; } = false;
    }

    public class CustomerCase
    {
        public string? CompanyName { get; set; }
        public string? CompanyLogo { get; set; }
        public string? Industry { get; set; }
        public string? ProjectTitle { get; set; }
        public string? ProjectDescription { get; set; }
        public string? ChallengeDescription { get; set; }
        public string? SolutionDescription { get; set; }
        public string? ResultDescription { get; set; }
        public string? TestimonialText { get; set; }
        public string? TestimonialAuthor { get; set; }
        public string? TestimonialPosition { get; set; }
        public string? TestimonialImage { get; set; }
        public string? CaseStudyUrl { get; set; }
        public List<string> Technologies { get; set; } = new();
        public List<CaseMetric> Metrics { get; set; } = new();
        public string? FeaturedImage { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? ProjectDuration { get; set; }
        public bool IsFeatured { get; set; } = false;
    }

    public class CaseMetric
    {
        public string? Label { get; set; }
        public string? Value { get; set; }
        public string? Unit { get; set; }
        public string? Icon { get; set; }
    }
}