using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class FAQComponentViewModel
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Description { get; set; }
        public List<FAQItem> FAQItems { get; set; } = new();
        
        // Display settings
        public string? Layout { get; set; } = "accordion"; // accordion, list
        public bool ShowSearch { get; set; } = true;
        public bool ShowCategories { get; set; } = false;
        public bool AnimationEnabled { get; set; } = true;
        public bool AllowMultipleOpen { get; set; } = false;
        public string? AccentColor { get; set; } = "primary";
    }

    public class FAQItem
    {
        public string? Question { get; set; }
        public string? Answer { get; set; }
        public string? Category { get; set; }
        public int Order { get; set; } = 0;
        public bool IsPopular { get; set; } = false;
    }
}