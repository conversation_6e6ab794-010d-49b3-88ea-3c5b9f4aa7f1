using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class FooterComponentViewModel
    {
        public string? CompanyName { get; set; }

        /// <summary>
        /// Company description
        /// </summary>
        public string? CompanyDescription { get; set; }
        public string? Logo { get; set; }
        public ContactInfo? ContactInfo { get; set; }
        public List<FooterSection> Sections { get; set; } = new();
        public List<SocialLink> SocialLinks { get; set; } = new();
        public string? Copyright { get; set; }

        public bool ShowAdminLinks { get; set; } = true;
    }

    public class FooterSection
    {
        public string? Title { get; set; }
        public List<FooterLink> Links { get; set; } = new();
    }

    public class FooterLink
    {
        public string? Text { get; set; }
        public string? Url { get; set; }
        public bool OpenInNewTab { get; set; } = false;
    }

    public class SocialLink
    {
        public string? Platform { get; set; }
        public string? Url { get; set; }
        public string? Icon { get; set; }
    }
}