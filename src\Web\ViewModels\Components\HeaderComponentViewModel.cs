using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class HeaderComponentViewModel
    {
        public string? Logo { get; set; }
        public string? CompanyName { get; set; }

        public bool ShowCompanyName { get; set; } = true;

        public List<NavigationItem> MenuItems { get; set; } = new();
        public ContactInfo? ContactInfo { get; set; }
        public bool ShowLanguageSelector { get; set; } = true;
        public bool ShowSearchBox { get; set; } = false;
    }

    public class NavigationItem
    {
        public string? Text { get; set; }
        public string? Url { get; set; }
        public string? Icon { get; set; }
        public List<NavigationItem> SubItems { get; set; } = new();
        public bool IsActive { get; set; } = false;
        public bool IsExternal { get; set; } = false;
        /// <summary>
        /// 菜单排序
        /// </summary>
        public int Sort { get; set; }
    }

    public class ContactInfo
    {
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
    }
}