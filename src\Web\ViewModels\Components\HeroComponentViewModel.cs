namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class HeroComponentViewModel
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Description { get; set; }
        public string? BackgroundImage { get; set; }
        public string? BackgroundVideo { get; set; }
        
        // 主按钮
        public string? PrimaryButtonText { get; set; }
        public string? PrimaryButtonUrl { get; set; }
        
        // 次按钮
        public string? SecondaryButtonText { get; set; }
        public string? SecondaryButtonUrl { get; set; }
        
        public bool ShowScrollIndicator { get; set; } = true;
        public string? OverlayOpacity { get; set; } = "0.5";
        public string? TextAlignment { get; set; } = "center";
        public bool ShowOverlay { get; set; } = true;
        
        // 新增配置字段
        public string? Height { get; set; } = "large";
        public bool AnimationEnabled { get; set; } = true;
        
        // 视差滚动配置
        public bool ParallaxEnabled { get; set; } = false;
        public float ParallaxSpeed { get; set; } = 0.5f;
    }
}