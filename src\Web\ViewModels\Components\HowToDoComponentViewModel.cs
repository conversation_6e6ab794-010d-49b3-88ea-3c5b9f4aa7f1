using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class HowToDoComponentViewModel
    {
        public string? TitleText { get; set; }
        public string? SubtitleText { get; set; }
        public string? Description { get; set; }
        public List<ProcessStep> Steps { get; set; } = new();

        // Display settings
        public string? Layout { get; set; } = "grid"; // grid, list, timeline, cards
        public int ColumnsDesktop { get; set; } = 2;
        public int ColumnsTablet { get; set; } = 2;
        public int ColumnsMobile { get; set; } = 1;
        public string? BackgroundStyle { get; set; } = "light"; // light, dark, gradient

        // Visual settings
        public bool ShowStepNumbers { get; set; } = true;
        public bool ShowStepIcons { get; set; } = false;
        public bool ShowConnectors { get; set; } = true;
        public string? NumberStyle { get; set; } = "circle"; // circle, square, plain
        public bool AnimationEnabled { get; set; } = true;

        // CTA settings
        public string? CtaButtonText { get; set; }
        public string? CtaButtonUrl { get; set; }
        public bool ShowCtaButton { get; set; } = false;
    }

    public class ProcessStep
    {
        public string? StepNumber { get; set; }
        public string? Icon { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? DetailText { get; set; }
        public string? ActionText { get; set; }
        public string? ActionUrl { get; set; }
        public bool IsHighlighted { get; set; } = false;
        public int Order { get; set; }
        public string? Image { get; set; }
    }
}