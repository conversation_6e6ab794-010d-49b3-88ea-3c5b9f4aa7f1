using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class MediaComponentViewModel
    {
        public string? Title { get; set; }
        public string? Description { get; set; }
        public List<MediaItem> MediaItems { get; set; } = new();
        public string? Layout { get; set; } = "grid"; // grid, carousel, gallery, single
        public bool ShowThumbnails { get; set; } = true;
        public bool AutoPlay { get; set; } = false;
        public bool ShowControls { get; set; } = true;
    }

    public class MediaItem
    {
        public string? Type { get; set; } = "image"; // image, video, audio
        public string? Url { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Alt { get; set; }
        public string? Caption { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
    }
}