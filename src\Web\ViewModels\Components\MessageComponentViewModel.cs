﻿namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class MessageComponentViewModel
    {
        // 配置属性
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Description { get; set; }
        public bool ShowTitle { get; set; } = true;
        public bool ShowSubtitle { get; set; } = false;
        public bool ShowDescription { get; set; } = true;
        public string TextAlignment { get; set; } = "center";
        public string BackgroundColor { get; set; } = "gray-50";
        public string FormWidth { get; set; } = "large";
        public string? SubmitButtonText { get; set; }
        public string? RequiredFieldsNote { get; set; }
        public string? PrivacyNotice { get; set; }
    }
}
