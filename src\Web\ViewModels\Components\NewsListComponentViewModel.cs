using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class NewsListComponentViewModel
    {
        /// <summary>
        /// 组件标题 - 多语言支持
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 组件描述 - 多语言支持
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 每页显示的新闻数量
        /// </summary>
        public int ItemsPerPage { get; set; } = 10;

        /// <summary>
        /// 是否显示新闻图片
        /// </summary>
        public bool ShowImages { get; set; } = true;

        /// <summary>
        /// 是否显示新闻分类
        /// </summary>
        public bool ShowCategories { get; set; } = true;

        /// <summary>
        /// 是否显示发布日期
        /// </summary>
        public bool ShowDates { get; set; } = true;

        /// <summary>
        /// 是否显示新闻摘要
        /// </summary>
        public bool ShowExcerpts { get; set; } = true;

        /// <summary>
        /// 日期显示格式
        /// </summary>
        public string? DateFormat { get; set; } = "yyyy.MM.dd";

        /// <summary>
        /// 布局样式 - grid(网格) 或 list(列表)
        /// </summary>
        public string? Layout { get; set; } = "list";

        /// <summary>
        /// 网格布局时的列数设置
        /// </summary>
        public int GridColumns { get; set; } = 2;

        /// <summary>
        /// 背景颜色设置
        /// </summary>
        public string? BackgroundColor { get; set; } = "white";

        /// <summary>
        /// 新闻项目列表 - 由ViewComponent填充
        /// </summary>
        public List<NewsListItem> NewsItems { get; set; } = new();

        /// <summary>
        /// 是否启用动画效果
        /// </summary>
        public bool AnimationEnabled { get; set; } = true;
    }

    /// <summary>
    /// 新闻列表项目数据模型
    /// </summary>
    public class NewsListItem
    {
        /// <summary>
        /// 新闻ID
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 新闻标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 新闻摘要
        /// </summary>
        public string? Excerpt { get; set; }

        /// <summary>
        /// 发布日期
        /// </summary>
        public string? Date { get; set; }

        /// <summary>
        /// 新闻分类
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 分类颜色样式
        /// </summary>
        public string? CategoryColor { get; set; } = "secondary";

        /// <summary>
        /// 新闻链接URL
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// 缩略图URL
        /// </summary>
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// 是否为推荐新闻
        /// </summary>
        public bool IsFeatured { get; set; } = false;

        /// <summary>
        /// 阅读量
        /// </summary>
        public int ViewCount { get; set; } = 0;
    }
}
