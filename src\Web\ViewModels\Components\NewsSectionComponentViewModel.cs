using System.Collections.Generic;

using System.Collections.Generic;
using System.Linq;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class NewsSectionComponentViewModel
    {
        public string? Title { get; set; }
        public string? Description { get; set; }
        public List<NewsItem> NewsItems { get; set; } = new();
        
        // Display settings
        public string? BackgroundColor { get; set; } = "white";
        public int MaxItems { get; set; } = 4;
        public bool ShowViewAllButton { get; set; } = true;
        public string? ViewAllButtonText { get; set; }
        public string? ViewAllButtonUrl { get; set; }
        public bool ShowCategories { get; set; } = true;
        public bool ShowDates { get; set; } = true;
        public bool ShowExcerpts { get; set; } = true;
        public string? DateFormat { get; set; } = "yyyy.MM.dd";
    }

    public class NewsItem
    {
        public string? Date { get; set; }
        public string? Category { get; set; }
        public string? Title { get; set; }
        public string? Excerpt { get; set; }
        public string? Url { get; set; }
        public string? CategoryColor { get; set; } = "secondary";
    }
}