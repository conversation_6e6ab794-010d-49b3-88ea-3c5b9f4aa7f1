using System.Collections.Generic;
using MlSoft.Sites.Model.Entities.Organization;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class OrganizationStructureComponentViewModel
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public List<OrganizationStructureNode> Departments { get; set; } = new();
    }

    public class OrganizationStructureNode
    {
        public OrganizationStructure Department { get; set; } = new();
        public List<OrganizationStructureNode> Children { get; set; } = new();
    }
}
