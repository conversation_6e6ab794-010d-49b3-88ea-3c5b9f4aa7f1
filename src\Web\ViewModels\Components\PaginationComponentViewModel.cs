using System;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class PaginationComponentViewModel
    {
        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPageIndex { get; set; } = 1;

        /// <summary>
        /// 每页显示的记录数
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; } = 0;

        /// <summary>
        /// 自定义信息
        /// </summary>
        public string? Info { get; set; }

        /// <summary>
        /// 当前页前后显示的页码数量
        /// </summary>
        public int DiffCount { get; set; } = 2;

        // 显示控制选项
        /// <summary>
        /// 是否显示首页/末页按钮
        /// </summary>
        public bool ShowFirstLast { get; set; } = true;

        /// <summary>
        /// 是否显示上一页/下一页按钮
        /// </summary>
        public bool ShowPrevNext { get; set; } = true;

        /// <summary>
        /// 是否显示页码按钮
        /// </summary>
        public bool ShowPageNumbers { get; set; } = true;

        /// <summary>
        /// 是否显示页码信息
        /// </summary>
        public bool ShowInfo { get; set; } = true;

        /// <summary>
        /// 是否显示总记录数
        /// </summary>
        public bool ShowTotalCount { get; set; } = true;

        /// <summary>
        /// 组件尺寸 (sm, md, lg)
        /// </summary>
        public string Size { get; set; } = "md";

        /// <summary>
        /// 页面URL生成的基础路径
        /// </summary>
        public string? BaseUrl { get; set; }

        /// <summary>
        /// URL查询参数
        /// </summary>
        public string? QueryParams { get; set; }

        // 计算属性
        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => PageSize > 0 ? (int)Math.Ceiling(TotalCount / (double)PageSize) : 0;

        /// <summary>
        /// 是否有分页
        /// </summary>
        public bool HasPagination => TotalPages > 1;

        /// <summary>
        /// 显示范围开始页码
        /// </summary>
        public int StartPage => Math.Max(1, Math.Min(CurrentPageIndex - DiffCount, TotalPages - (DiffCount * 2)));

        /// <summary>
        /// 显示范围结束页码
        /// </summary>
        public int EndPage => Math.Min(TotalPages, Math.Max(CurrentPageIndex + DiffCount, (DiffCount * 2 + 1)));

        /// <summary>
        /// 生成指定页码的URL
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <returns>URL字符串</returns>
        public string GeneratePageUrl(int pageNumber)
        {
            var baseUrl = BaseUrl ?? "";
            var queryParams = QueryParams ?? "";

            if (string.IsNullOrEmpty(queryParams))
            {
                return $"{baseUrl}?page={pageNumber}";
            }
            else if (queryParams.Contains("page="))
            {
                // 替换已存在的page参数
                var regex = new System.Text.RegularExpressions.Regex(@"page=\d+");
                queryParams = regex.Replace(queryParams, $"page={pageNumber}");
                return $"{baseUrl}?{queryParams}";
            }
            else
            {
                // 添加新的page参数
                return $"{baseUrl}?{queryParams}&page={pageNumber}";
            }
        }
    }
}