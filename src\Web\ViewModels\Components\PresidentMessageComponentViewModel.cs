using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class PresidentMessageComponentViewModel
    {
        // 显示设置 - 这些是通过组件配置控制的
        public string? Layout { get; set; } = "default"; // default, card, minimal, featured
        public bool ShowTitle { get; set; } = true;
        public string? TitleText { get; set; }
        public bool ShowPhoto { get; set; } = true;
        public bool ShowPosition { get; set; } = true;
        public bool ShowBiography { get; set; } = false;
        public bool ShowBorder { get; set; } = true;
        public string? BackgroundStyle { get; set; } = "white"; // white, gray, transparent
        public string? PhotoPosition { get; set; } = "left"; // left, right, top
        public string? PhotoSize { get; set; } = "medium"; // small, medium, large
        
        // 实际的社长数据 - 这些从Executive实体中读取
        public Model.Entities.Organization.Executive? PresidentData { get; set; }
    }
}