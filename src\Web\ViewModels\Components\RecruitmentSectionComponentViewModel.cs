using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class RecruitmentSectionComponentViewModel
    {
        public string? Title { get; set; }
        public string? Description { get; set; }
        public List<RecruitmentCategoryItem> Categories { get; set; } = new();
        
        // Display settings
        public string? BackgroundColor { get; set; } = "white";
        public string? ViewAllJobsButtonText { get; set; }
        public string? ViewAllJobsButtonUrl { get; set; }
        public string? ButtonVariant { get; set; } = "outline";
    }

    public class RecruitmentCategoryItem
    {
        public string? Icon { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? ButtonText { get; set; }
        public string? ButtonUrl { get; set; }
    }

}