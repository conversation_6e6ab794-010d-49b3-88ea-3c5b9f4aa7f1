using System;
using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class SEOComponentViewModel
    {
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Keywords { get; set; }
        
        public string? OgTitle { get; set; }
        public string? OgDescription { get; set; }
        public string? OgImage { get; set; }
        public string? OgUrl { get; set; }
        public string? OgType { get; set; } = "website";
        public string? OgLocale { get; set; }
        
        // Twitter Card properties
        public string? TwitterCard { get; set; } = "summary_large_image";
        public string? TwitterSite { get; set; }
        public string? TwitterCreator { get; set; }
        public string? TwitterTitle { get; set; }
        public string? TwitterDescription { get; set; }
        public string? TwitterImage { get; set; }
        
        public string? CanonicalUrl { get; set; }
        public Dictionary<string, string> HreflangUrls { get; set; } = new();
        
        public List<SchemaData> SchemaMarkups { get; set; } = new();
        public bool EnableSchema { get; set; } = true;
        
        public string? SiteName { get; set; }
        public string? CurrentLanguage { get; set; }
    }

    public class SchemaData
    {
        public string Type { get; set; } = string.Empty;
        public object Data { get; set; } = new();
        public int Priority { get; set; } = 100;
    }
}