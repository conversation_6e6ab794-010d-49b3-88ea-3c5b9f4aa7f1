using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels.Components
{
    public class SearchBoxComponentViewModel
    {
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Description { get; set; }
        public List<SearchField> SearchFields { get; set; } = new();
        
        // Display settings
        public string? Layout { get; set; } = "horizontal"; // horizontal, vertical
        public bool ShowTitle { get; set; } = true;
        public bool ShowSearchButton { get; set; } = true;
        public bool ShowResetButton { get; set; } = true;
        public bool AnimationEnabled { get; set; } = true;
        public string? SearchButtonText { get; set; }
        public string? ResetButtonText { get; set; }
        public string? SearchAction { get; set; } = "/search";
        public string? SearchMethod { get; set; } = "GET";
        
        // Styling
        public string? BackgroundStyle { get; set; } = "light"; // light, dark, transparent
        public string? BorderRadius { get; set; } = "medium"; // small, medium, large
        public bool ShowShadow { get; set; } = true;
    }

    public class SearchField
    {
        public string? Name { get; set; }
        public string? Label { get; set; }
        public string? Type { get; set; } = "text"; // text, select
        public string? Placeholder { get; set; }
        public bool Required { get; set; } = false;
        public int Order { get; set; } = 0;
        public List<SearchOption> Options { get; set; } = new(); // For select type
        public string? DefaultValue { get; set; }
        public string? Width { get; set; } = "auto"; // auto, full, half, third
    }

    public class SearchOption
    {
        public string? Value { get; set; }
        public string? Label { get; set; }
        public bool Selected { get; set; } = false;
    }
}
