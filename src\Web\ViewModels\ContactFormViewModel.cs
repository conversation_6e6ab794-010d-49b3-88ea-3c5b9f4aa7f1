using System.ComponentModel.DataAnnotations;
using MlSoft.Sites.Model.Entities.Enums;
using MlSoft.Sites.Web.Resources;

namespace MlSoft.Sites.Web.ViewModels
{
    /// <summary>
    /// 前台联系表单ViewModel
    /// </summary>
    public class ContactFormViewModel
    {     

        // 表单字段
        [Required(ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_NameRequired")]
        [Display(ResourceType = typeof(SharedResource), Name = "Contact_Name")]
        [MaxLength(100, ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_NameMaxLength")]
        public string ContactName { get; set; } = string.Empty;

        [Required(ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_EmailRequired")]
        [EmailAddress(ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_EmailInvalid")]
        [Display(ResourceType = typeof(SharedResource), Name = "Contact_Email")]
        [MaxLength(100, ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_EmailMaxLength")]
        public string ContactEmail { get; set; } = string.Empty;

        [Phone(ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_PhoneInvalid")]
        [Display(ResourceType = typeof(SharedResource), Name = "Contact_Phone")]
        [MaxLength(20, ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_PhoneMaxLength")]
        public string? ContactPhone { get; set; }

        [Display(ResourceType = typeof(SharedResource), Name = "Contact_Company")]
        [MaxLength(100, ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_CompanyMaxLength")]
        public string? CompanyName { get; set; }

        [Display(ResourceType = typeof(SharedResource), Name = "Contact_Position")]
        [MaxLength(50, ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_PositionMaxLength")]
        public string? JobTitle { get; set; }

        [Required(ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_MessageRequired")]
        [Display(ResourceType = typeof(SharedResource), Name = "Contact_MessageContent")]
        [MinLength(10, ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_MessageMinLength")]
        [MaxLength(2000, ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_MessageMaxLength")]
        public string Message { get; set; } = string.Empty;

        [Required(ErrorMessageResourceType = typeof(SharedResource), ErrorMessageResourceName = "Contact_MessageTypeRequired")]
        [Display(ResourceType = typeof(SharedResource), Name = "Contact_MessageType")]
        public MessageType Type { get; set; }

        /// <summary>
        /// 送信元ページ（隠藏字段）
        /// </summary>
        public string? SourcePage { get; set; }

        /// <summary>
        /// 参照URL（隐藏字段）
        /// </summary>
        public string? ReferrerUrl { get; set; }
    }
}