﻿using System.Collections.Generic;

namespace MlSoft.Sites.Web.ViewModels
{

    /// <summary>
    /// 本类为组件间传递数据使用
    /// </summary>
    public class PageComponentContext
    {
        /*
        
        数据方填充数据使用
        if (ViewData.ContainsKey(PageComponentContext.ViewDataKey))
        {
            var ctx = (PageComponentContext)ViewData[PageComponentContext.ViewDataKey];
            if (ctx != null && ctx.Data != null)
            {
                ctx.Data[EnumPageComponentContextDataKey.PaginationTotalCount] = 100;
            }
        }


        分页组件中使用
        if (ViewData.ContainsKey(PageComponentContext.ViewDataKey))
        {
            var ctx = (PageComponentContext)ViewData[PageComponentContext.ViewDataKey];
            if (ctx != null && ctx.Data != null &&  ctx.Data.ContainsKey(PageComponentContext.ViewDataKey))
            {
                if (int.TryParse(ctx.Data[PageComponentContext.ViewDataKey].ToString(), out int totalCount))
                {
                    viewModel.TotalCount = totalCount;
                }
            }
        }

         
         */


        public const string ViewDataKey = "PageContext";

       


        public Dictionary<string, object> Data { get; set; } = new();
    }

    public class EnumPageComponentContextDataKey
    {
        /// <summary>
        /// 分页组件使用的总记录数
        /// </summary>
        public static string PaginationTotalCount = "TotalCount";
    }

}
