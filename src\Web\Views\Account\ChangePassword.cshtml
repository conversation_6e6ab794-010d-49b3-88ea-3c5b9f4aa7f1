@model MlSoft.Sites.Web.ViewModels.Admin.ChangePasswordViewModel
@{
    ViewData["Title"] = AccountRes["ChangePassword"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="bg-white dark:bg-gray-800 shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
            @AccountRes["ChangePassword"]
        </h3>
        
        @if (ViewBag.Message != null)
        {
            <div class="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-4 py-3 rounded">
                @ViewBag.Message
            </div>
        }

        <form asp-action="ChangePassword" method="post">
            <div class="space-y-6">
                <div>
                    <label asp-for="CurrentPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AccountRes["CurrentPassword"]</label>
                    <div class="mt-1">
                        <input asp-for="CurrentPassword" type="password"
                               class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" />
                        <span asp-validation-for="CurrentPassword" class="text-red-600 text-sm"></span>
                    </div>
                </div>

                <div>
                    <label asp-for="NewPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AccountRes["NewPassword"]</label>
                    <div class="mt-1">
                        <input asp-for="NewPassword" type="password"
                               class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" />
                        <span asp-validation-for="NewPassword" class="text-red-600 text-sm"></span>
                    </div>
                </div>

                <div>
                    <label asp-for="ConfirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AccountRes["ConfirmNewPassword"]</label>
                    <div class="mt-1">
                        <input asp-for="ConfirmPassword" type="password"
                               class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white" />
                        <span asp-validation-for="ConfirmPassword" class="text-red-600 text-sm"></span>
                    </div>
                </div>

                <div asp-validation-summary="ModelOnly" class="text-red-600"></div>

                <div class="flex justify-end space-x-3">
                    <a asp-controller="Admin" asp-action="Settings"
                       class="bg-white dark:bg-gray-800 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AccountRes["Cancel"]
                    </a>
                    <button type="submit"
                            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AccountRes["ChangePassword"]
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}