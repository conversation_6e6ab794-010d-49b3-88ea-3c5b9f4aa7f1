@model MlSoft.Sites.Web.ViewModels.Admin.LoginViewModel
@{
    ViewData["Title"] = AccountRes["AdminLogin"];
    Layout = null;
}

<!DOCTYPE html>
<html lang="@ViewData["CurrentLanguage"]">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - @AccountRes["BackgroundManagement"]</title>
    <link rel="stylesheet" href="~/css/output.css" asp-append-version="true">
    <link href="~/lib/font-awesome/font-awesome-7.0.1-local.css" rel="stylesheet" asp-append-version="true">
   
    
    <!-- Dark Mode Detection Script - Must run before page renders -->
    <script>
        // Load dark mode initialization if available, fallback to inline
        if (window.DarkModeInit) {
            DarkModeInit.init();
        } else {
            // Fallback inline initialization
            (function() {
                const savedTheme = localStorage.getItem('theme');
                const systemPrefersDark = window.matchMedia && 
                    window.matchMedia('(prefers-color-scheme: dark)').matches;
                const shouldUseDark = savedTheme === 'dark' || 
                    (!savedTheme && systemPrefersDark);
                if (shouldUseDark) {
                    document.documentElement.classList.add('dark');
                }
                window.__INITIAL_THEME__ = shouldUseDark ? 'dark' : 'light';
            })();
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center">
                <i class="fas fa-user-shield text-white text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                @AccountRes["AdminLoginTitle"]
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
                @AccountRes["PleaseUseAdminAccount"]
            </p>
        </div>
        
        <form class="space-y-6" asp-action="Login" method="post">
            <input type="hidden" name="ReturnUrl" value="@ViewData["ReturnUrl"]" />
            
            <div class="space-y-4">
                <div>
                    <label asp-for="UserName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AccountRes["UserName"]</label>
                    <input asp-for="UserName" 
                           class="block w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                           placeholder="@AccountRes["EnterUserName"]" />
                    <span asp-validation-for="UserName" class="mt-1 text-sm text-red-600"></span>
                </div>
                <div>
                    <label asp-for="Password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AccountRes["Password"]</label>
                    <input asp-for="Password" type="password"
                           class="block w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                           placeholder="@AccountRes["EnterPassword"]" />
                    <span asp-validation-for="Password" class="mt-1 text-sm text-red-600"></span>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input asp-for="RememberMe" type="checkbox" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label asp-for="RememberMe" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        @AccountRes["RememberMe"]
                    </label>
                </div>
            </div>

            <div asp-validation-summary="ModelOnly" class="text-sm text-red-600"></div>

            <div>
                <button type="submit" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-lock mr-2"></i>
                    @AccountRes["Login"]
                </button>
            </div>

            <div class="text-center">
                <a asp-controller="Home" asp-action="Index" class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400">
                    @AccountRes["BackToHome"]
                </a>
            </div>
        </form>
    </div>
    <script src="~/js/vendor/flowbite.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @section Scripts {
        @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    }
</body>
</html>