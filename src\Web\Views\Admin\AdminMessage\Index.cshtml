@model MlSoft.Sites.Web.ViewModels.Admin.AdminMessageIndexViewModel
@{
    ViewData["Title"] = AdminRes["MessageManagement"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="space-y-6">

    <!-- Alert Messages -->
    @if (!string.IsNullOrEmpty((string)TempData["Success"]))
    {
        <div id="success-alert" class="mb-6 p-4 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 text-primary-700 dark:text-primary-300 rounded-md">
            <div class="flex">
                <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                @TempData["Success"]
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty((string)TempData["Error"]))
    {
        <div id="error-alert" class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-md">
            <div class="flex">
                <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                @TempData["Error"]
            </div>
        </div>
    }



    <!-- Filter and Search -->
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">

        <div class="grid grid-cols-2 gap-4 mb-4">

            <div>
                <form method="get" class="flex gap-4 items-end">
                    <!-- Status Filter -->
                    <div class="flex-1">
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["StatusFilter"]</label>
                        <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md focus:ring-primary-500 focus:border-primary-500">
                            <option value="">@AdminRes["AllStatus"]</option>
                            <option value="@((int)MessageStatus.New)" selected="@(Model.CurrentStatus == MessageStatus.New ? "selected" : null)">@AdminRes["NewMessage"]</option>
                            <option value="@((int)MessageStatus.InProgress)" selected="@(Model.CurrentStatus == MessageStatus.InProgress ? "selected" : null)">@AdminRes["InProgress"]</option>
                            <option value="@((int)MessageStatus.WaitingForResponse)" selected="@(Model.CurrentStatus == MessageStatus.WaitingForResponse ? "selected" : null)">@AdminRes["WaitingForResponse"]</option>
                            <option value="@((int)MessageStatus.Resolved)" selected="@(Model.CurrentStatus == MessageStatus.Resolved ? "selected" : null)">@AdminRes["Resolved"]</option>
                            <option value="@((int)MessageStatus.Closed)" selected="@(Model.CurrentStatus == MessageStatus.Closed ? "selected" : null)">@AdminRes["Closed"]</option>
                        </select>
                    </div>

                    <!-- Search -->
                    <div class="flex-1">
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Search"]</label>
                        <input type="text" name="search" id="search" value="@Model.SearchQuery"
                               placeholder="@AdminRes["SearchPlaceholder"]"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <button type="submit" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        @AdminRes["Filter"]
                    </button>
                </form>
            </div>
            <!-- Statistics Cards -->
            <div class="flex items-center justify-end space-x-4 text-sm">
                <div class="bg-primary-50 dark:bg-primary-900/20 text-primary-800 dark:text-primary-200 px-3 py-2 rounded-lg">
                    <span class="font-medium">@AdminRes["Total"]:</span> @Model.Statistics.Total
                </div>
                <div class="bg-warning-50 dark:bg-warning-900/20 text-warning-800 dark:text-warning-200 px-3 py-2 rounded-lg">
                    <span class="font-medium">@AdminRes["NewMessages"]:</span> @Model.Statistics.New
                </div>
                <div class="bg-success-50 dark:bg-success-900/20 text-success-800 dark:text-success-200 px-3 py-2 rounded-lg">
                    <span class="font-medium">@AdminRes["Today"]:</span> @Model.Statistics.Today
                </div>
            </div>
        </div>

      
    </div>

    <!-- Messages List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
        @if (Model.Messages.Items.Any())
        {
            @foreach (var message in Model.Messages.Items)
            {
                <div class="border-b border-gray-200 dark:border-gray-700 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 last:border-b-0">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-medium text-gray-900 dark:text-gray-100">
                                    <button type="button" onclick="openMessageModal('@message.Id', this)"
                                            data-message='@Html.Raw(Json.Serialize(message))'
                                            class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-left">
                                        @(string.IsNullOrEmpty(message.Message) ? AdminRes["NoTitle"] :
                                          (message.Message.Length > 50 ? message.Message.Substring(0, 50) + "..." : message.Message))
                                    </button>
                                </h3>
                                @if (!message.IsRead)
                                {
                                    <span class="bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300 text-xs px-2 py-1 rounded">@AdminRes["New"]</span>
                                }
                                @if (message.IsImportant)
                                {
                                    <span class="bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300 text-xs px-2 py-1 rounded">@AdminRes["Important"]</span>
                                }
                            </div>

                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                <strong>@message.ContactName</strong> (@message.ContactEmail)
                                @if (!string.IsNullOrEmpty(message.CompanyName))
                                {
                                    <span> - @message.CompanyName</span>
                                }
                            </p>

                            <p class="text-xs text-gray-500 dark:text-gray-500">
                                @AdminRes["CreatedAt"]: @message.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                                @if (message.ProcessedAt.HasValue)
                                {
                                    <span> | @AdminRes["ProcessedAt"]: @message.ProcessedAt.Value.ToString("yyyy-MM-dd HH:mm")</span>
                                }
                            </p>

                            @if (!string.IsNullOrEmpty(message.DealResult))
                            {
                                <p class="text-sm text-success-600 dark:text-success-400 mt-1">
                                    <strong>@AdminRes["DealResult"]:</strong> @message.DealResult
                                </p>
                            }
                        </div>

                        <div class="text-right">
                            <span class="inline-block px-2 py-1 text-xs rounded
                                @(message.Status == MessageStatus.New ? "bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300" :
                                  message.Status == MessageStatus.InProgress ? "bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300" :
                                  message.Status == MessageStatus.WaitingForResponse ? "bg-info-100 dark:bg-info-900/30 text-info-800 dark:text-info-300" :
                                  message.Status == MessageStatus.Resolved ? "bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300" :
                                  message.Status == MessageStatus.Closed ? "bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300" :
                                  "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300")">
                                @(message.Status switch
                                {
                                    MessageStatus.New => AdminRes["NewMessage"],
                                    MessageStatus.InProgress => AdminRes["InProgress"],
                                    MessageStatus.WaitingForResponse => AdminRes["WaitingForResponse"],
                                    MessageStatus.Resolved => AdminRes["Resolved"],
                                    MessageStatus.Closed => AdminRes["Closed"],
                                    MessageStatus.Spam => AdminRes["Spam"],
                                    _ => AdminRes["Unknown"]
                                })
                            </span>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="p-8 text-center text-gray-500 dark:text-gray-400">
                @AdminRes["NoMessages"]
            </div>
        }
    </div>

    <!-- Pagination -->
    @if (Model.Messages.Total > 0)
    {
        ViewBag.CurrentPageIndex = Model.Messages.Page;
        ViewBag.TotalCount = Model.Messages.Total;
        ViewBag.PageSize = Model.Messages.PageSize;
        ViewBag.PageUrl = new Func<int, string>(pageNum => Url.Action("Index", new { page = pageNum, status = Model.CurrentStatus, search=Model.SearchQuery }));

        @await Html.PartialAsync("_Pagination");

    }
</div>

<!-- Message Details Modal -->
<div id="messageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-5xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="flex justify-between items-center pb-3 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">@AdminRes["MessageDetails"]</h3>
            <button type="button" onclick="closeMessageModal()" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="pt-6 space-y-6">
            <!-- Header Info -->
            <div class="flex justify-between items-start mb-6">
                <div>
                    <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                        <span>@AdminRes["CreatedAt"]: <span id="modal-created-at"></span></span>
                        <span>@AdminRes["UpdatedAt"]: <span id="modal-updated-at"></span></span>
                        <span id="modal-processed-at-wrapper" class="hidden">@AdminRes["ProcessedAt"]: <span id="modal-processed-at"></span></span>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <span id="modal-important-badge" class="bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300 text-sm px-3 py-1 rounded hidden">@AdminRes["Important"]</span>
                    <span id="modal-status-badge" class="bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 text-sm px-3 py-1 rounded"></span>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-3">@AdminRes["ContactInfo"]</h3>
                    <div class="space-y-2 text-sm">
                        <div><strong class="text-gray-700 dark:text-gray-300">@AdminRes["Name"]:</strong> <span id="modal-contact-name" class="text-gray-900 dark:text-gray-100"></span></div>
                        <div><strong class="text-gray-700 dark:text-gray-300">@AdminRes["Email"]:</strong> <span id="modal-contact-email" class="text-gray-900 dark:text-gray-100"></span></div>
                        <div id="modal-contact-phone-wrapper" class="hidden"><strong class="text-gray-700 dark:text-gray-300">@AdminRes["Phone"]:</strong> <span id="modal-contact-phone" class="text-gray-900 dark:text-gray-100"></span></div>
                        <div id="modal-company-name-wrapper" class="hidden"><strong class="text-gray-700 dark:text-gray-300">@AdminRes["Company"]:</strong> <span id="modal-company-name" class="text-gray-900 dark:text-gray-100"></span></div>
                        <div id="modal-job-title-wrapper" class="hidden"><strong class="text-gray-700 dark:text-gray-300">@AdminRes["JobTitle"]:</strong> <span id="modal-job-title" class="text-gray-900 dark:text-gray-100"></span></div>
                    </div>
                </div>

                <div>
                    <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-3">@AdminRes["MessageInfo"]</h3>
                    <div class="space-y-2 text-sm">
                        <div><strong class="text-gray-700 dark:text-gray-300">@AdminRes["Type"]:</strong>
                            <span id="modal-message-type" class="text-gray-900 dark:text-gray-100"></span>
                        </div>
                        <div id="modal-source-page-wrapper" class="hidden"><strong class="text-gray-700 dark:text-gray-300">@AdminRes["SourcePage"]:</strong> <span id="modal-source-page" class="text-gray-900 dark:text-gray-100"></span></div>
                    </div>
                </div>
            </div>

            <!-- Message Content -->
            <div class="mb-6">
                <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-3">@AdminRes["MessageContent"]</h3>
                <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded border border-gray-200 dark:border-gray-600">
                    <div id="modal-message-content" class="text-gray-900 dark:text-gray-100 whitespace-pre-wrap"></div>
                </div>
            </div>

            <!-- Deal Result -->
            <div class="mb-6">
                <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-3">@AdminRes["DealResult"]</h3>
                <form id="dealResultForm" onsubmit="updateDealResult(event)">
                    <input type="hidden" id="modal-message-id" />
                    <textarea id="modal-deal-result" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md mb-2 focus:ring-primary-500 focus:border-primary-500"
                              placeholder="@AdminRes["DealResultPlaceholder"]"></textarea>
                    <button type="submit" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        @AdminRes["SaveDealResult"]
                    </button>
                </form>
            </div>
        </div>

        <!-- Modal Actions -->
        <div class="pt-4 border-t border-gray-200 dark:border-gray-700 mt-4">
            <div class="flex flex-wrap gap-2">
                <!-- Status Update Buttons -->
                <div class="flex flex-wrap gap-2">
                    <button id="btn-in-progress" onclick="updateStatus(1)" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        @AdminRes["MarkAsInProgress"]
                    </button>
                    <button id="btn-waiting-response" onclick="updateStatus(2)" class="px-4 py-2 bg-[var(--color-info)] hover:opacity-90 text-white rounded-md focus:ring-2 focus:ring-[var(--color-info)] focus:ring-offset-2">
                        @AdminRes["MarkAsWaitingForResponse"]
                    </button>
                    <button id="btn-resolved" onclick="updateStatus(4)" class="px-4 py-2 bg-[var(--color-success)] hover:opacity-90 text-white rounded-md focus:ring-2 focus:ring-[var(--color-success)] focus:ring-offset-2">
                        @AdminRes["MarkAsResolved"]
                    </button>
                    <button id="btn-closed" onclick="updateStatus(5)" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        @AdminRes["MarkAsClosed"]
                    </button>
                </div>

                <!-- Important Toggle -->
                <button id="btn-toggle-important" onclick="toggleImportant()" class="px-4 py-2 border border-[var(--color-warning)] text-[color:var(--color-warning)] rounded-md hover:bg-[color:var(--color-warning)] hover:text-white focus:ring-2 focus:ring-[var(--color-warning)] focus:ring-offset-2">
                    <span id="btn-important-text"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const successAlert = document.getElementById('success-alert');
        const errorAlert = document.getElementById('error-alert');
        if (successAlert) successAlert.style.display = 'none';
        if (errorAlert) errorAlert.style.display = 'none';
    }, 5000);

    // Global variable to store current message data
    let currentMessage = null;

    // Modal functionality
    function openMessageModal(messageId, buttonElement) {
        try {
            // Get the message data from the button's data attribute
            currentMessage = JSON.parse(buttonElement.getAttribute('data-message'));

            // Populate modal with message data
            populateModalData(currentMessage);

            // Show modal
            document.getElementById('messageModal').classList.remove('hidden');

            // Mark as read if not already read
            if (!currentMessage.isRead) {
                markAsRead(messageId);
            }
        } catch (error) {
            console.error('Error parsing message data:', error);
            Dialog.error('Error loading message details');
        }
    }

    function closeMessageModal() {
        document.getElementById('messageModal').classList.add('hidden');
        currentMessage = null;
    }

    function populateModalData(message) {
        // Header info
        document.getElementById('modal-created-at').textContent = new Date(message.createdAt).toLocaleString();
        document.getElementById('modal-updated-at').textContent = new Date(message.updatedAt).toLocaleString();

        if (message.processedAt) {
            document.getElementById('modal-processed-at').textContent = new Date(message.processedAt).toLocaleString();
            document.getElementById('modal-processed-at-wrapper').classList.remove('hidden');
        } else {
            document.getElementById('modal-processed-at-wrapper').classList.add('hidden');
        }

        // Badges
        if (message.isImportant) {
            document.getElementById('modal-important-badge').classList.remove('hidden');
        } else {
            document.getElementById('modal-important-badge').classList.add('hidden');
        }
        document.getElementById('modal-status-badge').textContent = getStatusText(message.status);

        // Contact information
        document.getElementById('modal-contact-name').textContent = message.contactName || '';
        document.getElementById('modal-contact-email').textContent = message.contactEmail || '';

        // Optional contact fields
        setOptionalField('modal-contact-phone', 'modal-contact-phone-wrapper', message.contactPhone);
        setOptionalField('modal-company-name', 'modal-company-name-wrapper', message.companyName);
        setOptionalField('modal-job-title', 'modal-job-title-wrapper', message.jobTitle);

        // Message info
        document.getElementById('modal-message-type').textContent = getMessageTypeText(message.type);
        setOptionalField('modal-source-page', 'modal-source-page-wrapper', message.sourcePage);

        // Message content
        document.getElementById('modal-message-content').textContent = message.message || '';

        // Deal result
        document.getElementById('modal-message-id').value = message.id;
        document.getElementById('modal-deal-result').value = message.dealResult || '';

        // Update button states
        updateButtonStates(message);
    }

    function setOptionalField(elementId, wrapperId, value) {
        if (value) {
            document.getElementById(elementId).textContent = value;
            document.getElementById(wrapperId).classList.remove('hidden');
        } else {
            document.getElementById(wrapperId).classList.add('hidden');
        }
    }

    function updateButtonStates(message) {
        // Reset all buttons
        const statusButtons = ['btn-in-progress', 'btn-waiting-response', 'btn-resolved', 'btn-closed'];
        statusButtons.forEach(id => {
            document.getElementById(id).disabled = false;
        });

        // Disable current status button
        switch (message.status) {
            case 1: document.getElementById('btn-in-progress').disabled = true; break;
            case 2: document.getElementById('btn-waiting-response').disabled = true; break;
            case 4: document.getElementById('btn-resolved').disabled = true; break;
            case 5: document.getElementById('btn-closed').disabled = true; break;
        }

        // Update important toggle button
        const importantBtn = document.getElementById('btn-important-text');
        importantBtn.textContent = message.isImportant ? '@Html.Raw(AdminRes["RemoveImportant"])' : '@Html.Raw(AdminRes["MarkAsImportant"])';
    }

    function getStatusText(status) {
        const statusMap = {
            0: '@Html.Raw(AdminRes["NewMessage"])',
            1: '@Html.Raw(AdminRes["InProgress"])',
            2: '@Html.Raw(AdminRes["WaitingForResponse"])',
            3: '@Html.Raw(AdminRes["WaitingForCustomer"])',
            4: '@Html.Raw(AdminRes["Resolved"])',
            5: '@Html.Raw(AdminRes["Closed"])',
            6: '@Html.Raw(AdminRes["Spam"])'
        };
        return statusMap[status] || '@Html.Raw(AdminRes["Unknown"])';
    }

    function getMessageTypeText(type) {
        const typeMap = {
            0: '@Html.Raw(AdminRes["GeneralInquiry"])',
            1: '@Html.Raw(AdminRes["ProductInquiry"])',
            2: '@Html.Raw(AdminRes["ServiceInquiry"])',
            3: '@Html.Raw(AdminRes["TechnicalSupport"])',
            4: '@Html.Raw(AdminRes["BusinessCooperation"])',
            5: '@Html.Raw(AdminRes["PartnershipInquiry"])',
            6: '@Html.Raw(AdminRes["MediaInquiry"])',
            7: '@Html.Raw(AdminRes["CareerInquiry"])',
            8: '@Html.Raw(AdminRes["Complaint"])',
            9: '@Html.Raw(AdminRes["Other"])'
        };
        return typeMap[type] || '@Html.Raw(AdminRes["Other"])';
    }

    function markAsRead(messageId) {
        
        fetch(`${buildMultilingualUrl("MarkAsRead","AdminMessage")}/${messageId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentMessage.isRead = true;
            }
        })
        .catch(error => {
            console.error('Error marking as read:', error);
        });
    }

    function updateStatus(status) {
        if (!currentMessage) return;
         
        fetch(`${buildMultilingualUrl("UpdateStatus","AdminMessage")}/${currentMessage.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
            },
            body: `status=${status}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeMessageModal();
                location.reload(); // Refresh the page to show updated status
            } else {
                Dialog.error('Error updating status: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            Dialog.error('Error updating status');
        });
    }

    function toggleImportant() {
        if (!currentMessage) return;
       
        fetch(`${buildMultilingualUrl("ToggleImportant","AdminMessage")}/${currentMessage.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeMessageModal();
                location.reload(); // Refresh the page to show updated importance
            } else {
                Dialog.error('Error updating importance: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            Dialog.error('Error updating importance');
        });
    }

    function updateDealResult(event) {
        event.preventDefault();
        if (!currentMessage) return;

        const dealResult = document.getElementById('modal-deal-result').value;

        fetch(`${buildMultilingualUrl("UpdateDealResult","AdminMessage")}/${currentMessage.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
            },
            body: `dealResult=${encodeURIComponent(dealResult)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Dialog.notify('@AdminRes["DealResultSaved"]', 'success');
                currentMessage.dealResult = dealResult;
            } else {
                Dialog.error('Error saving deal result: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            Dialog.error('Error saving deal result');
        });
    }

    // Close modal when clicking outside
    document.getElementById('messageModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeMessageModal();
        }
    });
</script>