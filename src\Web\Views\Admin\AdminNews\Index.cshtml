@model MlSoft.Sites.Web.ViewModels.Admin.AdminNewsIndexViewModel
@{

    ViewData["Title"] = AdminRes["NewsManagement"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";



    var searchUrl = Html.MultilingualUrl("","AdminNews",ViewData["CurrentLanguage"]?.ToString());

}

<div class="space-y-6">
    <!-- Tab Navigation -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">

            <!-- 标签页内容容器 -->
            <div id="news-list-tab" class="tab-content">
                <!-- 新闻列表标签页内容 -->
                <div class="space-y-6">

                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["NewsList"]</h3>
                        <button onclick="openCreateModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-2"></i>
                            @AdminRes["CreateNews"]
                        </button>
                    </div>

                    <!-- 筛选功能区域 -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
                        <form method="get" action="@searchUrl" class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1">
                                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["SearchTitle"]</label>
                                <input type="text" id="search" name="search" value="@Model.SearchTerm"
                                       placeholder="@AdminRes["SearchNewsPlaceholder"]"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white">
                            </div>
                            <div class="w-full md:w-48">
                                <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["NewsType"]</label>
                                <select id="type" name="type"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white">
                                    <option value="">@AdminRes["AllTypes"]</option>
                                    @foreach (var newsType in Model.AvailableTypes)
                                    {
                                        <option value="@newsType" selected="@(Model.SelectedType == newsType)">@AdminRes[$"NewsType_{newsType}"]</option>
                                    }
                                </select>
                            </div>
                            <div class="w-full md:w-48">
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["NewsStatus"]</label>
                                <select id="status" name="status"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white">
                                    <option value="">@AdminRes["AllStatuses"]</option>
                                    @foreach (NewsStatus statusValue in Enum.GetValues<NewsStatus>())
                                    {
                                        <option value="@statusValue" selected="@(Model.SelectedStatus == statusValue)">@AdminRes[$"NewsStatus_{statusValue}"]</option>
                                    }
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button type="submit" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-search mr-2"></i>@AdminRes["Search"]
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 新闻列表 -->
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                        <ul id="newsList" class="divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach (var news in Model.News.Items)
                            {
                                <li class="news-item" data-id="@news.Id">
                                    <div class="px-4 py-4 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="ml-4 flex-shrink-0">
                                                @if (!string.IsNullOrEmpty(news.ThumbnailUrl))
                                                {
                                                    <img class="h-12 w-12 rounded-lg object-cover" src="@news.ThumbnailUrl" alt="@news.Locale.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString())?.Title">
                                                }
                                                else
                                                {
                                                    <div class="h-12 w-12 rounded-lg bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                                        <i class="fas fa-newspaper text-gray-400"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div class="ml-4 min-w-0 flex-1">
                                                <div class="flex items-center space-x-2">
                                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                        @news.Locale.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString())?.Title
                                                    </h3>
                                                    @if (news.IsFeatured)
                                                    {
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                                                            <i class="fas fa-star mr-1"></i>@AdminRes["Featured"]
                                                        </span>
                                                    }
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                        @AdminRes[$"NewsType_{news.Type}"]
                                                    </span>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @(news.Status == NewsStatus.Published ? "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200" : news.Status == NewsStatus.Draft ? "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200" : "bg-primary-50 text-primary-700 dark:bg-primary-800 dark:text-primary-300")">
                                                        @AdminRes[$"NewsStatus_{news.Status}"]
                                                    </span>
                                                </div>
                                                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                                                    @news.Locale.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString())?.Summary
                                                </p>
                                                <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                                                    <span><i class="fas fa-calendar mr-1"></i>@news.PublishDate.ToString("yyyy-MM-dd")</span>
                                                    <span><i class="fas fa-eye mr-1"></i>@news.ViewCount</span>
                                                    <span><i class="fas fa-clock mr-1"></i>@news.CreatedAt.ToString("yyyy-MM-dd")</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <button onclick="editNews('@news.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button onclick="toggleFeatured('@news.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                                @if (news.IsFeatured)
                                                {
                                                    <i class="fas fa-star"></i>
                                                }
                                                else
                                                {
                                                    <i class="far fa-star"></i>
                                                }

                                            </button>
                                            <button onclick="togglePublish('@news.Id')" class="text-2xl @(news.Status == NewsStatus.Published ? "text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300" : "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300")">
                                                @if (news.Status == NewsStatus.Published)
                                                {
                                                    <i class="fas fa-eye"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-eye-slash"></i>
                                                }
                                            </button>
                                            <button onclick="deleteNews('@news.Id')" class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            }
                        </ul>
                    </div>

                    <!-- Pagination -->
                    @if (Model.News.Total > 0)
                    {
                        ViewBag.CurrentPageIndex = Model.News.Page;
                        ViewBag.TotalCount = Model.News.Total;
                        ViewBag.PageSize = Model.News.PageSize;
                        ViewBag.PageUrl = new Func<int, string>(pageNum => Url.Action("Index", new { page = pageNum, search = Model.SearchTerm, type = Model.SelectedType, status = Model.SelectedStatus }));

                        @await Html.PartialAsync("_Pagination");
                    }
                </div>
            </div>
        </div>
    </div>

</div>

<!-- 创建/编辑新闻模态框 -->
<div id="newsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-5xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="modalTitle">@AdminRes["CreateNews"]</h3>
                <button onclick="closeNewsModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="modalContent">
                @{
                    var emptyModel = new MlSoft.Sites.Web.ViewModels.Admin.NewsAnnouncementViewModel();
                    var supportedLanguages = ViewData["SupportedLanguages"] as MlSoft.Sites.Model.Configuration.SupportedLanguage[];
                    var defaultLanguage = supportedLanguages?.FirstOrDefault()?.Code ?? ViewData["DefaultLanguage"]?.ToString() ?? "en";
                }

                <form id="newsForm" method="post">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="newsId" name="Id" value="" />

                    <!-- 基本信息 -->
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="Type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["NewsType"]
                                    <span class="text-red-500">*</span>
                                </label>
                                <select id="Type" name="Type" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" required>

                                    @foreach (var enumItem in Enum.GetValues(typeof(NewsType)))
                                    {
                                        var intValue = (int)(NewsType)enumItem;
                                        <option value="@intValue">@AdminRes[$"NewsType_{enumItem.ToString()}"]</option>
                                    }
                                </select>
                            </div>

                            <div>
                                <label for="PublishDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["PublishDate"]
                                    <span class="text-red-500">*</span>
                                </label>
                                <input id="PublishDate" name="PublishDate" type="datetime-local" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" required />
                            </div>

                            <div>
                                <label for="Priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["Priority"]
                                </label>
                                <select id="Priority" name="Priority" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">

                                    @foreach (var enumItem in Enum.GetValues(typeof(NewsPriority)))
                                    {
                                        var intValue = (int)(NewsPriority)enumItem;
                                        <option value="@intValue">@AdminRes[$"Priority_{enumItem.ToString()}"]</option>
                                    }
                                </select>
                            </div>

                            <div>
                                <label for="Status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["Status"]
                                </label>
                                <select id="Status" name="Status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                    @foreach (var enumItem in Enum.GetValues(typeof(NewsStatus)))
                                    {
                                        var intValue = (int)(NewsStatus)enumItem;
                                        <option value="@intValue">@AdminRes[$"NewsStatus_{enumItem.ToString()}"]</option>
                                    }
                                </select>
                            </div>

                            <div>
                                <label for="Source" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["NewsSource"]
                                </label>
                                <select id="Source" name="Source" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                    @foreach (var enumItem in Enum.GetValues(typeof(NewsSource)))
                                    {
                                        var intValue = (int)(NewsSource)enumItem;
                                        <option value="@intValue">@AdminRes[$"NewsSource_{enumItem.ToString()}"]</option>
                                    }
                                </select>
                            </div>

                            <div>
                                <label for="ExternalUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["ExternalUrl"]
                                </label>
                                <input id="ExternalUrl" name="ExternalUrl" type="url" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" />
                            </div>
                        </div>

                        <!-- 选项 -->
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input id="IsFeatured" name="IsFeatured" type="checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded" />
                                <label for="IsFeatured" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                    @AdminRes["FeaturedNews"]
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 多语言内容 -->
                    <div class="mt-6">
                        @if (supportedLanguages != null)
                        {
                            <div class="border-b border-gray-200 dark:border-gray-600">
                                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                                    @foreach (var lang in supportedLanguages)
                                    {
                                        <button type="button"
                                                onclick="window.switchLanguageTab('@lang.Code', '#newsModal', {buttonClass: 'language-tab', contentClass: 'language-content', contentIdPrefix: 'news-lang-'})"
                                                class="language-tab @(lang.Code == defaultLanguage ? "border-primary-500 text-primary-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600") whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                                data-lang="@lang.Code">
                                            @lang.Name
                                        </button>
                                    }
                                </nav>
                            </div>

                            <div class="mt-4">
                                @foreach (var lang in supportedLanguages)
                                {
                                    <div id="<EMAIL>" class="language-content @(lang.Code == defaultLanguage ? "" : "hidden")" data-lang="@lang.Code">
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                    @AdminRes["Title"]
                                                    <span class="text-red-500">*</span>
                                                </label>
                                                <input type="text"
                                                       name="Locale[@lang.Code].Title"
                                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                                       data-required="true" />
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                    @AdminRes["Summary"]
                                                </label>
                                                <textarea name="Locale[@lang.Code].Summary"
                                                          class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                                          rows="3"></textarea>
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                    @AdminRes["Content"]
                                                    <span class="text-red-500">*</span>
                                                </label>
                                                <textarea name="Locale[@lang.Code].Content"
                                                          class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                                          rows="6"
                                                          data-required="true"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    </div>

                    <!-- 图片和其他设置 -->
                    <div class="mt-6 space-y-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["ThumbnailUrl"]
                            </label>
                            <div id="thumbnailUpload" class="mt-2"></div>
                            <input type="hidden" id="ThumbnailUrl" name="ThumbnailUrl" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["Tags"]
                            </label>
                            <input type="text"
                                   id="tagsInput"
                                   class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                   placeholder="@AdminRes["EnterTagsPlaceholder"]" />
                            <input type="hidden" id="Tags" name="Tags" />
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                        <button type="button" onclick="closeNewsModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            @AdminRes["Cancel"]
                        </button>
                        <button type="button" onclick="saveNews()" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-save mr-1"></i>
                            <span id="saveButtonText">@AdminRes["Create"]</span>
                        </button>
                    </div>
                </form>

                <script>
                    // 标签输入处理
                    document.addEventListener('DOMContentLoaded', function() {
                        const tagsInput = document.getElementById('tagsInput');
                        const tagsHidden = document.getElementById('Tags');

                        if (tagsInput && tagsHidden) {
                            tagsInput.addEventListener('input', function() {
                                const tags = this.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
                                tagsHidden.value = JSON.stringify(tags);
                            });
                        }
                    });
                </script>
            </div>
        </div>
    </div>
</div>

<script src="~/js/admin/adminnews.js" asp-append-version="true"></script>
