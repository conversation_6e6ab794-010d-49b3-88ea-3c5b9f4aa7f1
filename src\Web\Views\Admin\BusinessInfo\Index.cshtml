@model MlSoft.Sites.Web.ViewModels.Admin.BusinessInfoViewModel
@{
    ViewData["Title"] = AdminRes["BusinessInfoTitle"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="space-y-6">
    <!-- Tab Navigation -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button onclick="switchTab('business-divisions')" data-tab="business-divisions" class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm  text-primary-600 dark:text-primary-400 border-primary-500">
                        @AdminRes["BusinessDivisions"]
                    </button>
                    <button onclick="switchTab('product-services')" data-tab="product-services"
                            class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm  text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 border-transparent">
                        @AdminRes["ProductServices"]
                    </button>
                </nav>
            </div>


            <!-- Tab Content -->
            <!-- Business Divisions Tab -->
            <div id="business-divisions-tab" class="tab-content">
                @await Html.PartialAsync("~/Views/Admin/BusinessInfo/Partials/_BusinessDivisionsTab.cshtml", Model.BusinessDivisions)
            </div>

            <!-- Product Services Tab -->
            <div id="product-services-tab" class="tab-content hidden">
                <div class="loading-placeholder flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">@AdminRes["Loading"]...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Business Division Modal -->
@await Html.PartialAsync("~/Views/Admin/BusinessInfo/Modals/_BusinessDivisionModal.cshtml")

<!-- Product Service Modal -->
@await Html.PartialAsync("~/Views/Admin/BusinessInfo/Modals/_ProductServiceModal.cshtml")

@section Scripts {
    <script src="~/js/admin/businessinfo.js"  asp-append-version="true"></script>
}
