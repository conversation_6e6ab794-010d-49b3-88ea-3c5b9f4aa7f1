<!-- Business Division Modal -->
<div id="businessDivisionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="businessDivisionModalTitle">
                    @AdminRes["AddBusinessDivision"]
                </h3>
                <button onclick="closeBusinessDivisionModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="businessDivisionForm" class="mt-6">
                <input type="hidden" id="divisionId" name="divisionId" value="">
                
                <!-- Language Tabs -->
                <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        @{
                            var supportedLanguages = (SupportedLanguage[])ViewData["SupportedLanguages"];
                            var isFirst = true;
                        }
                        @foreach (var lang in supportedLanguages)
                        {
                            <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#businessDivisionModal', {buttonClass: 'division-lang-tab-button', contentClass: 'division-lang-content', contentIdPrefix: 'division-lang-'})"
                                    class="division-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                    data-lang="@lang.Code">
                                @lang.Emoji @lang.Name
                            </button>
                            isFirst = false;
                        }
                    </nav>
                </div>

                <!-- Language Content -->
                @{
                    isFirst = true;
                }
                @foreach (var lang in supportedLanguages)
                {
                    <div id="<EMAIL>" class="division-lang-content @(isFirst ? "" : "hidden")">
                        <div class="grid grid-cols-1 gap-6">
                            <!-- Division Name -->
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["DivisionName"] (@lang.Name)
                                </label>
                                <input type="text" id="<EMAIL>" name="<EMAIL>" required
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["DivisionDescription"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="4"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>

                            <!-- Services -->
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["DivisionServices"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                        </div>
                    </div>
                    isFirst = false;
                }

                <!-- Common Fields -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Display Order -->
                    <div>
                        <label for="displayOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @AdminRes["DisplayOrder"]
                        </label>
                        <input type="number" id="displayOrder" name="displayOrder" value="0" min="0"
                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    </div>

                    <!-- Is Active -->
                    <div class="flex items-center">
                        <input type="checkbox" id="isActive" name="isActive" checked
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700">
                        <label for="isActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                            @AdminRes["Active"]
                        </label>
                    </div>
                </div>

                @* <!-- Image URLs -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Division Image -->
                    <div>
                        <label for="imageUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @AdminRes["DivisionImage"]
                        </label>
                        <input type="url" id="imageUrl" name="imageUrl" placeholder="https://example.com/image.jpg"
                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    </div>

                    <!-- Division Icon -->
                    <div>
                        <label for="iconUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @AdminRes["DivisionIcon"]
                        </label>
                        <input type="url" id="iconUrl" name="iconUrl" placeholder="https://example.com/icon.png"
                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    </div>
                </div> *@
            </form>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700 mt-6">
                <button type="button" onclick="closeBusinessDivisionModal()"
                        class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    @AdminRes["Cancel"]
                </button>
                <button type="button" onclick="saveBusinessDivision()"
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    @AdminRes["Save"]
                </button>
            </div>
        </div>
    </div>
</div>

