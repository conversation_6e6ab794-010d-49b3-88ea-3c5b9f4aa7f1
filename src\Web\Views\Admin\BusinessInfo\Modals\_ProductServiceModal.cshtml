<!-- Product Service Modal -->
<div id="productServiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="productServiceModalTitle">
                    @AdminRes["AddProductService"]
                </h3>
                <button onclick="closeProductServiceModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="productServiceForm" class="mt-6">
                <input type="hidden" id="productId" name="productId" value="">

                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4 items-start">

                    <div class="md:col-span-2 lg:col-span-2 grid grid-cols-1 gap-4 items-start">
                        <!-- Language Tabs -->
                        <div class="border-b border-gray-200 dark:border-gray-600 mb-0">
                            <nav class="-mb-px flex space-x-8">
                                @{
                                    var supportedLanguages = (SupportedLanguage[])ViewData["SupportedLanguages"];
                                    var isFirst = true;
                                }
                                @foreach (var lang in supportedLanguages)
                                {
                                    <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#productServiceModal', {buttonClass: 'product-lang-tab-button', contentClass: 'product-lang-content', contentIdPrefix: 'product-lang-'})"
                                            class="product-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                            data-lang="@lang.Code">
                                        @lang.Emoji @lang.Name
                                    </button>
                                    isFirst = false;
                                }
                            </nav>
                        </div>

                        <!-- Language Content -->
                        @{
                            isFirst = true;
                        }
                        @foreach (var lang in supportedLanguages)
                        {
                            <div id="<EMAIL>" class="product-lang-content @(isFirst ? "" : "hidden")">
                                <div class="grid grid-cols-1 gap-4 items-start">
                                    <!-- Product Name -->
                                    <div class="mt-0">
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            @AdminRes["ProductName"] (@lang.Name)
                                        </label>
                                        <input type="text" id="<EMAIL>" name="<EMAIL>" required
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                    </div>

                                    <!-- Description -->
                                    <div class="mt-0">
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            @AdminRes["ProductDescription"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="4"
                                              class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 tinymce-editor"></textarea>
                                    </div>

                                    <!-- Features -->
                                    <div class="mt-0">
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            @AdminRes["ProductFeatures"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                              class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 tinymce-editor"></textarea>
                                    </div>

                                    <!-- Specifications -->
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            @AdminRes["ProductSpecifications"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                              class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 tinymce-editor"></textarea>
                                    </div>
                                </div>
                            </div>
                            isFirst = false;
                        }

                    </div>
                    <div class="md:col-span-1 lg:col-span-1  gap-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Business Division -->
                            <div>
                                <label for="businessDivisionId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["BusinessDivision"]
                                </label>
                                <select id="businessDivisionId" name="businessDivisionId"
                                        class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                    <option value="">@AdminRes["SelectBusinessDivision"]</option>
                                </select>
                            </div>

                            <!-- Product Category -->
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["ProductCategory"]
                                </label>
                                <select id="category" name="category" required
                                        class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                    <option value="0">@AdminRes["Product"]</option>
                                    <option value="1">@AdminRes["Service"]</option>
                                    <option value="2">@AdminRes["Solution"]</option>
                                    <option value="3">@AdminRes["Technology"]</option>
                                </select>
                            </div>

                            <!-- Price and Currency -->
                            <!-- Price -->
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["ProductPrice"]
                                </label>
                                <input type="number" id="price" name="price" step="0.01" min="0"
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            </div>

                            <!-- Currency -->
                            <div>
                                <label for="currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["Currency"]
                                </label>
                                <select id="currency" name="currency"
                                        class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                    <option value="JPY">@AdminRes["Currency_JPY"]</option>
                                    <option value="USD">@AdminRes["Currency_USD"]</option>
                                    <option value="EUR">@AdminRes["Currency_EUR"]</option>
                                    <option value="CNY">@AdminRes["Currency_CNY"]</option>
                                </select>
                            </div>


                            <!-- Display Order -->
                            <div>
                                <label for="displayOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    @AdminRes["DisplayOrder"]
                                </label>
                                <input type="number" id="displayOrder" name="displayOrder" value="0" min="0"
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="isActive" name="isActive" checked
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700">
                                <label for="isActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                                    @AdminRes["Active"]
                                </label>
                            </div>


                        </div>



                        <!--产品图片-->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                @AdminRes["ProductImages"]
                            </label>
                            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md p-3">
                                <div class="flex flex-col items-center" onclick="document.getElementById('productImageFile').click();">
                                    <svg class="w-6 h-6 mb-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500">@AdminRes["ClickUploadLogo"]</p>
                                    <p class="text-xs text-gray-500">@AdminRes["FileUpload_OrDragDrop"]</p>
                                </div>

                                <input type="file" id="productImageFile" name="imageFile"
                                       accept="image/*" multiple
                                       class="hidden" />
                                <div id="productImagesFileList" class="mt-2 space-y-1"></div>
                                <input type="hidden" id="productImagesFileUrl" name="ImageUrls" />
                            </div>
                        </div>

                        <!--产品文档/资料下载-->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                @AdminRes["ProductDocuments"]
                            </label>
                            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md p-3">
                                <div class="flex flex-col items-center" onclick="document.getElementById('productDocumentFiles').click();">
                                    <svg class="w-6 h-6 mb-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500">@AdminRes["ClickUpload"]</p>
                                    <p class="text-xs text-gray-500">@AdminRes["FileUpload_OrDragDrop"]</p>
                                </div>

                                <input type="file" id="productDocumentFiles" name="documentFiles"
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation"
                                       multiple class="hidden" />
                                <div id="productDocumentsFileList" class="mt-2 space-y-1"></div>
                            </div>
                        </div>

                    </div>
                </div>
            </form>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700 mt-6">
                <button type="button" onclick="closeProductServiceModal()"
                        class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    @AdminRes["Cancel"]
                </button>
                <button type="button" onclick="saveProductService()"
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    @AdminRes["Save"]
                </button>
            </div>
        </div>
    </div>
</div>

