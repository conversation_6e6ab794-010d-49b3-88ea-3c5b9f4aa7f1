@model List<MlSoft.Sites.Web.ViewModels.Admin.BusinessDivisionViewModel>

<!-- Tab Header -->
<div class="flex justify-between items-center mb-6">
    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["BusinessDivisions"]</h3>
    <button onclick="openBusinessDivisionModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
        <i class="fas fa-plus mr-2"></i>
        @AdminRes["AddBusinessDivision"]
    </button>
</div>

<!-- Business Divisions Table -->
<div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
    @if (Model != null && Model.Any())
    {
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["DivisionName"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["DivisionDescription"]
                        </th>
                        @* <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Image"]
                        </th> *@
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["DisplayOrder"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Status"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Actions"]
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach (var division in Model.OrderBy(d => d.DisplayOrder))
                    {
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    @if (!string.IsNullOrEmpty(division.IconUrl))
                                    {
                                        <img class="h-8 w-8 rounded-full mr-3" src="@division.IconUrl" alt="@AdminRes["DivisionIcon"]">
                                    }
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        @(division.Locale.ContainsKey(ViewData["CurrentLanguage"]?.ToString())
                                                                            ? division.Locale[ViewData["CurrentLanguage"]?.ToString()].DivisionName
                                                                            : division.Locale.Values.FirstOrDefault()?.DivisionName ?? "Unknown")
                                                                                                                                             </div>
                                                                                                                                         </div>
                                                                                                                                     </div>
                                                                                                                                 </td>
                                                                                                                                 <td class="px-6 py-4">
                                                                                                                                     <div class="text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate">
                                @(division.Locale.ContainsKey(ViewData["CurrentLanguage"]?.ToString())
                                                            ? division.Locale[ViewData["CurrentLanguage"]?.ToString()].Description
                                                            : division.Locale.Values.FirstOrDefault()?.Description ?? "")
                        </div>
                    </td>
                    @* <td class="px-6 py-4 whitespace-nowrap">
                        @if (!string.IsNullOrEmpty(division.ImageUrl))
                                {
                                    <img class="h-10 w-10 rounded object-cover" src="@division.ImageUrl" alt="@AdminRes["DivisionImage"]">
                                }
                                else
                                {
                                    <span class="text-gray-400 dark:text-gray-500">-</span>
                                }
                            </td> *@
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @division.DisplayOrder
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full @(division.IsActive ? "bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200" : "bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200")">
                                    @(division.IsActive? AdminRes["Active"] : AdminRes["Inactive"])
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="editBusinessDivision('@division.Id')"
                                            class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300  text-2xl">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="deleteBusinessDivision('@division.Id')"
                                            class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300  text-2xl">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="text-center py-12">
            <i class="fas fa-building text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">@AdminRes["NoData"]</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">@AdminRes["NoBusinessDivisionsYet"]</p>
            <button onclick="openBusinessDivisionModal()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                <i class="fas fa-plus mr-2"></i>
                @AdminRes["AddBusinessDivision"]
            </button>
        </div>
    }
</div>