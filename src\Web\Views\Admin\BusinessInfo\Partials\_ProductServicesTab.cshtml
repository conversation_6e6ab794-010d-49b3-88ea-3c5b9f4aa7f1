@model List<MlSoft.Sites.Web.ViewModels.Admin.ProductServiceViewModel>

<div class="space-y-6">
    <!-- Tab Header -->
    <div class="flex justify-between items-center">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
            @AdminRes["ProductServices"]
        </h3>
        <button onclick="openProductServiceModal()"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-plus mr-2"></i>
            @AdminRes["AddProductService"]
        </button>
    </div>

    <!-- Filter and Search -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="categoryFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    @AdminRes["ProductCategory"]
                </label>
                <select id="categoryFilter" onchange="filterProductServices()"
                        class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    <option value="">@AdminRes["AllCategories"]</option>
                    <option value="0">@AdminRes["Product"]</option>
                    <option value="1">@AdminRes["Service"]</option>
                    <option value="2">@AdminRes["Solution"]</option>
                    <option value="3">@AdminRes["Technology"]</option>
                </select>
            </div>
            <div>
                <label for="divisionFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    @AdminRes["BusinessDivision"]
                </label>
                <select id="divisionFilter" onchange="filterProductServices()"
                        class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    <option value="">@AdminRes["AllDivisions"]</option>
                </select>
            </div>
            <div>
                <label for="searchInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    @AdminRes["Search"]
                </label>
                <input type="text" id="searchInput" onkeyup="filterProductServices()" placeholder="@AdminRes["SearchProductsPlaceholder"]"
                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
            </div>
        </div>
    </div>

    <!-- Product Services Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        @if (Model != null && Model.Any())
        {
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                @AdminRes["ProductName"]
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                @AdminRes["BusinessDivision"]
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                @AdminRes["ProductCategory"]
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                @AdminRes["ProductPrice"]
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                @AdminRes["Image"]
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                @AdminRes["Status"]
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                @AdminRes["Actions"]
                            </th>
                        </tr>
                    </thead>
                    <tbody id="productServicesTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach (var product in Model.OrderBy(p => p.DisplayOrder))
                        {
                            <tr class="product-service-row hover:bg-gray-50 dark:hover:bg-gray-700" 
                                data-category="@((int)product.Category)" 
                                data-division="@product.BusinessDivisionId"
                                data-search="@(product.Locale.ContainsKey(ViewData["CurrentLanguage"]?.ToString()) 
                                                                    ? product.Locale[ViewData["CurrentLanguage"]?.ToString()].ProductName
                                    : product.Locale.Values.FirstOrDefault()?.ProductName ?? "").ToLower()">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        @(product.Locale.ContainsKey(ViewData["CurrentLanguage"]?.ToString())
                                                                        ? product.Locale[ViewData["CurrentLanguage"]?.ToString()].ProductName
                                                                        : product.Locale.Values.FirstOrDefault()?.ProductName ?? "Unknown")
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-gray-100">
                                        @(product.BusinessDivisionName ?? "-")
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                                        @GetCategoryName(product.Category)
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                    @if (product.Price.HasValue)
                                    {
                                        <span>@product.Price.Value.ToString("N2") @product.Currency</span>
                                    }
                                    else
                                    {
                                        <span class="text-gray-400 dark:text-gray-500">-</span>
                                    }
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if (product.ImageUrls != null && product.ImageUrls.Any())
                                    {
                                        <img class="h-10 w-10 rounded object-cover" src="@product.ImageUrls.First()" alt="@AdminRes["ProductImage"]">
                                    }
                                    else
                                    {
                                        <span class="text-gray-400 dark:text-gray-500">-</span>
                                    }
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full @(product.IsActive ? "bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200" : "bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200")">
                                        @(product.IsActive ? AdminRes["Active"] : AdminRes["Inactive"])
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="editProductService('@product.Id')"
                                                class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-2xl">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteProductService('@product.Id')"
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-2xl">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (Model.Count > 0)
            {
                ViewBag.CurrentPageIndex = 1; // Default to 1 for now since this tab doesn't have pagination logic yet
                ViewBag.TotalCount = Model.Count;

                ViewBag.PageSize = 10;
                ViewBag.PageUrl = new Func<int, string>(pageNum => $"#product-services?page={pageNum}");

                @await Html.PartialAsync("_Pagination");
            }
        }
        else
        {
            <div class="text-center py-12">
                <i class="fas fa-box text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">@AdminRes["NoData"]</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">@AdminRes["NoProductServicesYet"]</p>
                <button onclick="openProductServiceModal()"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                    <i class="fas fa-plus mr-2"></i>
                    @AdminRes["AddProductService"]
                </button>
            </div>
        }
    </div>
</div>

@functions {
    string GetCategoryName(MlSoft.Sites.Model.Entities.Enums.ProductCategory category)
    {
        return category switch
        {
            MlSoft.Sites.Model.Entities.Enums.ProductCategory.Product => AdminRes["Product"],
            MlSoft.Sites.Model.Entities.Enums.ProductCategory.Service => AdminRes["Service"],
            MlSoft.Sites.Model.Entities.Enums.ProductCategory.Solution => AdminRes["Solution"],
            MlSoft.Sites.Model.Entities.Enums.ProductCategory.Technology => AdminRes["Technology"],
            _ => "Unknown"
        };
    }
}

