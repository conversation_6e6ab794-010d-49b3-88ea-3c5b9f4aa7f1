@model MlSoft.Sites.Web.ViewModels.Admin.CompanyInfoViewModel
@{
    ViewData["Title"] = AdminRes["CompanyInfoTitle"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="space-y-6">

    <!-- Alert Messages -->
    @if (!string.IsNullOrEmpty((string)TempData["Success"]))
    {
        <div id="success-alert" class="mb-6 p-4 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 text-primary-700 dark:text-primary-300 rounded-md">
            <div class="flex">
                <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                @TempData["Success"]
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty((string)TempData["Error"]))
    {
        <div id="error-alert" class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-md">
            <div class="flex">
                <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                @TempData["Error"]
            </div>
        </div>
    }

    <!-- Tab Navigation -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button onclick="switchTab('basic-info')"
                            class="tab-button active border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="basic-info">
                        @AdminRes["BasicInfo"]
                    </button>
                    <button onclick="switchTab('company-history')"
                            class="tab-button border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="company-history">
                        @AdminRes["CompanyHistory"]
                    </button>
                    <button onclick="switchTab('executive-organization')"
                            class="tab-button border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="executive-organization">
                        @AdminRes["ExecutiveOrganization"]
                    </button>
                    <button onclick="switchTab('contact-info')"
                            class="tab-button border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="contact-info">
                        @AdminRes["ContactInfo"]
                    </button>
                    <button onclick="switchTab('csr-activities')"
                            class="tab-button border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="csr-activities">
                        @AdminRes["CSRActivities"]
                    </button>
                    <button onclick="switchTab('investor-relations')"
                            class="tab-button border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="investor-relations">
                        @AdminRes["InvestorRelations"]
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <!-- Basic Information Tab -->
            <div id="basic-info-tab" class="tab-content hidden">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">@AdminRes["BasicInfo"]</h3>

                <form id="basic-info-form" class="space-y-6">
                    <div class="grid grid-cols-1 gap-6">

                        <div class="grid grid-cols-5 md:grid-cols-5 gap-6">
                            <!-- 设立日期 -->
                            <div>
                                <label for="establishedDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["EstablishedDate"] *</label>
                                <input type="date" id="establishedDate" name="establishedDate" value="@Model.BasicInfo.EstablishedDate.ToString("yyyy-MM-dd")"
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" required>
                            </div>

                            <!-- 工商注册号 -->
                            <div>
                                <label for="registrationNumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["RegistrationNumber"]</label>
                                <input type="text" id="registrationNumber" name="registrationNumber" value="@Model.BasicInfo.RegistrationNumber"
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">
                            </div>
                            <!-- 注册资本 -->
                            <div>
                                <label for="capital" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["Capital"]</label>
                                <input type="number" id="capital" name="capital" value="@Model.BasicInfo.Capital" step="0.01"
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">
                            </div>
                            <!-- 货币单位 -->
                            <div>
                                <label for="currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["Currency"]</label>
                                <select id="currency" name="currency" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">
                                    <option value="">@AdminRes["PleaseSelect"]</option>
                                    <option value="JPY" selected="@(Model.BasicInfo.Currency == "JPY")">JPY (@AdminRes["JPY"])</option>
                                    <option value="USD" selected="@(Model.BasicInfo.Currency == "USD")">USD (@AdminRes["USD"])</option>
                                    <option value="CNY" selected="@(Model.BasicInfo.Currency == "CNY")">CNY (@AdminRes["CNY"])</option>
                                    <option value="EUR" selected="@(Model.BasicInfo.Currency == "EUR")">EUR (@AdminRes["EUR"])</option>
                                </select>
                            </div>

                            <!-- 企业规模 -->
                            <div>
                                <label for="employeeScale" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["EmployeeScale"]</label>
                                <select id="employeeScale" name="employeeScale" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">
                                    <option value="">@AdminRes["PleaseSelect"]</option>
                                    <option value="1" selected="@(Model.BasicInfo.EmployeeScale == MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Small)">@AdminRes["EmployeeScale_Small"]</option>
                                    <option value="2" selected="@(Model.BasicInfo.EmployeeScale == MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Medium)">@AdminRes["EmployeeScale_Medium"]</option>
                                    <option value="3" selected="@(Model.BasicInfo.EmployeeScale == MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Large)">@AdminRes["EmployeeScale_Large"]</option>
                                    <option value="4" selected="@(Model.BasicInfo.EmployeeScale == MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Enterprise)">@AdminRes["EmployeeScale_Enterprise"]</option>
                                </select>
                            </div>

                        </div>


                        <!-- 企业简介和经营理念 - 多语言标签页方式 -->
                        <div id="company-info-section" class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">@AdminRes["CompanyDescriptionAndPhilosophy"]</h4>

                            <!-- 多语言标签页导航 -->
                            <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                                <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                                    @{
                                        var supportedLanguages = (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"];
                                        var isFirst = true;
                                    }
                                    @foreach (var lang in supportedLanguages)
                                    {
                                        <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#company-info-section', {buttonClass: 'company-info-lang-tab-button', contentClass: 'company-info-lang-content', contentIdPrefix: 'company-info-lang-'})"
                                                class="company-info-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                                data-lang="@lang.Code">
                                            @lang.Emoji @lang.Name
                                        </button>
                                        isFirst = false;
                                    }
                                </nav>
                            </div>

                            <!-- 多语言内容 -->
                            @{
                                isFirst = true;
                            }
                            @foreach (var lang in supportedLanguages)
                            {
                                <div id="<EMAIL>" class="company-info-lang-content @(isFirst ? "" : "hidden")">
                                    <div class="grid grid-cols-1 gap-4">


                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">@AdminRes["CompanyName"] *</label>
                                            <input type="text" id="<EMAIL>" name="companyNames[@lang.Code]"
                                                   value="@(Model.BasicInfo.CompanyNames.GetValueOrDefault(lang.Code, ""))"
                                                   class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" required>
                                        </div>

                                        <!-- 企业简介 -->
                                        <div>
                                            <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                @AdminRes["CompanyDescription"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"].ToString()){<span class="text-red-500">*</span>}
                                            </label>
                                            <textarea id="<EMAIL>" name="companyDescriptions[@lang.Code]" rows="4"
                                                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm tinymce-editor"
                                                      data-required="@(lang.Code == ViewData["DefaultLanguage"].ToString())"
                                                      placeholder="@AdminRes["CompanyDescriptionPlaceholder"]">@(Model.BasicInfo.CompanyDescriptions.GetValueOrDefault(lang.Code, ""))</textarea>
                                        </div>

                                        <!-- 经营理念 -->
                                        <div>
                                            <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                @AdminRes["Philosophy"] (@lang.Name)
                                            </label>
                                            <textarea id="<EMAIL>" name="philosophy[@lang.Code]" rows="4"
                                                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm tinymce-editor"
                                                      placeholder="@AdminRes["PhilosophyPlaceholder"]">@(Model.BasicInfo.Philosophy.GetValueOrDefault(lang.Code, ""))</textarea>
                                        </div>
                                    </div>
                                </div>
                                isFirst = false;
                            }
                        </div>



                        @* <!-- 是否启用 -->
                        <div class="flex items-center">
                            <input id="isActive" name="isActive" type="checkbox" @(Model.BasicInfo.IsActive ? "checked" : "")
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                            <label for="isActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                                @AdminRes["EnableCompanyInfo"]
                            </label>
                        </div> *@
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-save mr-2"></i>
                            @AdminRes["SaveBasicInfo"]
                        </button>
                    </div>
                </form>
            </div>

            <!-- Company History Tab -->
            <div id="company-history-tab" class="tab-content hidden">
                <div class="loading-placeholder flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">@AdminRes["Loading"]...</span>
                </div>
            </div>

            <!-- Executive Organization Tab -->
            <div id="executive-organization-tab" class="tab-content hidden">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">@AdminRes["ExecutiveOrganization"]</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">@AdminRes["ExecutiveOrganizationDeveloping"]</p>
            </div>

            <!-- Contact Info Tab -->
            <div id="contact-info-tab" class="tab-content hidden">
                <div class="loading-placeholder flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">@AdminRes["Loading"]...</span>
                </div>
            </div>

            <!-- CSR Activities Tab -->
            <div id="csr-activities-tab" class="tab-content hidden">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">@AdminRes["CSRActivities"]</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">@AdminRes["CSRActivitiesDeveloping"]</p>
            </div>

            <!-- Investor Relations Tab -->
            <div id="investor-relations-tab" class="tab-content hidden">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">@AdminRes["InvestorRelations"]</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">@AdminRes["InvestorRelationsDeveloping"]</p>
            </div>
        </div>
    </div>
</div>


<!-- CompanyInfo页面脚本 -->
<script src="~/js/admin/companyinfo.js" asp-append-version="true"></script>

<script>
// Initialize company info language tabs on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize company description and philosophy language tabs
    const firstCompanyInfoLangBtn = document.querySelector('.company-info-lang-tab-button');
    if (firstCompanyInfoLangBtn) {
        window.switchLanguageTab(firstCompanyInfoLangBtn.getAttribute('data-lang'), '#company-info-section', {
            buttonClass: 'company-info-lang-tab-button',
            contentClass: 'company-info-lang-content',
            contentIdPrefix: 'company-info-lang-'
        });
    }

    // Initialize TinyMCE for company description and philosophy
    if (window.AdminTinyMCE) {
        AdminTinyMCE.initAll('.tinymce-editor', {
            menubar: false,
            height: 200
        });
    }

    // Defer event image upload initialization to modal open for lazy init
});
</script>