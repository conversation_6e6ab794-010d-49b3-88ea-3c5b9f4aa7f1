@model List<MlSoft.Sites.Web.ViewModels.Admin.CSRActivityItemViewModel>

<div class="space-y-6">
	<div class="flex justify-between items-center mb-6">
		<h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["CSRActivities"]</h3>
		<button onclick="openCSRActivityModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
			<i class="fas fa-plus mr-2"></i>
			@AdminRes["AddCSRActivity"]
		</button>
	</div>

	<!-- CSR活动表格 -->
	<div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
		<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
			<thead class="bg-gray-50 dark:bg-gray-700">
				<tr>
					<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
						@AdminRes["ActivityTitle"]
					</th>
					<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
						@AdminRes["CSRCategory"]
					</th>
					<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
						@AdminRes["StartDate"]
					</th>
					<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
						@AdminRes["EndDate"]
					</th>
					<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
						@AdminRes["Status"]
					</th>
					<th scope="col" class="relative px-6 py-3">
						<span class="sr-only">@AdminRes["Actions"]</span>
					</th>
				</tr>
			</thead>
			<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
				@foreach (var activity in Model.OrderByDescending(a => a.StartDate))
				{
					<tr data-activity-id="@activity.Id">
						<td class="px-6 py-4">
							<div class="flex items-center">
								@if (activity.ImageUrls.Any())
								{
									<div class="flex-shrink-0 h-10 w-10 mr-4">
										<img class="h-10 w-10 rounded object-cover" src="@activity.ImageUrls.First()" alt="@activity.Titles.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")" />
									</div>
								}
								<div>
									<div class="text-sm font-medium text-gray-900 dark:text-gray-100">
										@activity.Titles.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
									</div>
									<div class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
										@activity.Descriptions.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
									</div>
								</div>
							</div>
						</td>
						<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
							<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
								@AdminRes[activity.Category.ToString()]
							</span>
						</td>
						<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
							@activity.StartDate.ToString("yyyy-MM-dd")
						</td>
						<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
							@if (activity.EndDate.HasValue)
							{
								@activity.EndDate.Value.ToString("yyyy-MM-dd")
							}
							else
							{
								<span class="text-gray-500 dark:text-gray-400">@AdminRes["Ongoing"]</span>
							}
						</td>
						<td class="px-6 py-4 whitespace-nowrap">
							@if (activity.IsActive)
							{
								<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
									@AdminRes["Active"]
								</span>
							}
							else
							{
								<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
									@AdminRes["Inactive"]
								</span>
							}
						</td>
						<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
							<button onclick="editCSRActivity('@activity.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"><i class="fas fa-edit"></i></button>
							<button onclick="deleteCSRActivity('@activity.Id')" class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"><i class="fas fa-trash"></i></button>
						</td>
					</tr>
				}
				@if (!Model.Any())
				{
					<tr>
						<td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
							@AdminRes["NoCSRActivityRecords"]
						</td>
					</tr>
				}
			</tbody>
		</table>
	</div>

	<!-- CSR活动统计卡片 -->
	@if (Model.Any())
	{
		<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
			<!-- 活动总数 -->
			<div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
				<div class="p-5">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<i class="fas fa-heart text-2xl text-primary-400"></i>
						</div>
						<div class="ml-5 w-0 flex-1">
							<dl>
								<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
									@AdminRes["TotalActivities"]
								</dt>
								<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
									@Model.Count
								</dd>
							</dl>
						</div>
					</div>
				</div>
			</div>

			<!-- 进行中活动 -->
			<div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
				<div class="p-5">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<i class="fas fa-play-circle text-2xl text-primary-400"></i>
						</div>
						<div class="ml-5 w-0 flex-1">
							<dl>
								<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
									@AdminRes["OngoingActivities"]
								</dt>
								<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
									@Model.Count(a => !a.EndDate.HasValue || a.EndDate.Value > DateTime.Now)
								</dd>
							</dl>
						</div>
					</div>
				</div>
			</div>

			<!-- 有报告文档的活动 -->
			<div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
				<div class="p-5">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<i class="fas fa-file-alt text-2xl text-primary-400"></i>
						</div>
						<div class="ml-5 w-0 flex-1">
							<dl>
								<dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
									@AdminRes["ActivitiesWithReports"]
								</dt>
								<dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
									@Model.Count(a => !string.IsNullOrEmpty(a.ReportFileUrl))
								</dd>
							</dl>
						</div>
					</div>
				</div>
			</div>
		</div>
	}
</div>

<!-- CSR Activity Modal -->
<div id="csrActivityModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
	<div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
		<div class="mt-3">
			<!-- Modal Header -->
			<div class="flex justify-between items-center mb-4">
				<h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="csrActivityModalTitle">@AdminRes["AddCSRActivity"]</h3>
				<button type="button" onclick="closeCSRActivityModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
					<i class="fas fa-times text-xl"></i>
				</button>
			</div>

			<!-- Modal Form -->
			<form id="csrActivityForm" class="space-y-6">
				<input type="hidden" id="csrActivityId" name="id" />

				<!-- Basic Information -->
				<div class="grid grid-cols-3 md:grid-cols-3 gap-4">
					<!-- Category -->
					<div>
						<label for="csrActivityCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							@AdminRes["CSRCategory"] <span class="text-red-500">*</span>
						</label>
						<select id="csrActivityCategory" name="category" required
								class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
							@foreach (var enumItem in Enum.GetValues(typeof(CSRCategory)))
							{

								var intValue = (int)(CSRCategory)enumItem;

								<option value="@intValue">@AdminRes[enumItem.ToString()]</option>
							}
						</select>
					</div>

					<!-- Start Date -->
					<div>
						<label for="csrActivityStartDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							@AdminRes["StartDate"] <span class="text-red-500">*</span>
						</label>
						<input type="date" id="csrActivityStartDate" name="startDate" required
							   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
					</div>

					<!-- End Date -->
					<div>
						<label for="csrActivityEndDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							@AdminRes["EndDate"]
						</label>
						<input type="date" id="csrActivityEndDate" name="endDate"
							   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
					</div>


				</div>

				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- Report File Upload -->
					<div>
						<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
							@AdminRes["ReportFile"]
						</label>
						<div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
							<div class="flex flex-col items-center" onclick="document.getElementById('csrActivityReportFile').click();">
								<svg class="w-8 h-8 mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
								</svg>
								<p class="text-sm text-gray-500">@AdminRes["ClickUploadLogo"]</p>
								<p class="text-sm text-gray-500">@AdminRes["FileUpload_OrDragDrop"]</p>
							</div>
							<input type="file" id="csrActivityReportFile" name="reportFile"
								   accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
								   class="hidden" />
							<div id="csrActivityReportFileList" class="mt-3 space-y-2"></div>
							<input type="hidden" id="csrActivityReportFileUrl" name="reportFileUrl" />
						</div>
					</div>

					<!-- Image Files Upload -->
					<div>
						<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
							@AdminRes["ActivityImages"]
						</label>
						<div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
							<div class="flex flex-col items-center" onclick="document.getElementById('csrActivityImageFiles').click();">
								<svg class="w-8 h-8 mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
								</svg>
								<p class="text-sm text-gray-500">@AdminRes["ClickUploadLogo"]</p>
								<p class="text-sm text-gray-500">@AdminRes["FileUpload_OrDragDrop"]</p>
							</div>
							<input type="file" id="csrActivityImageFiles" name="imageFiles"
								   accept="image/*" multiple
								   class="hidden" />
							<div id="csrActivityImageFileList" class="mt-3 space-y-2"></div>
							<input type="hidden" id="csrActivityImageUrls" name="imageUrls" />
						</div>
					</div>
				</div>

				<!-- Multilingual Fields -->
				<div class="mt-6">
					<div class="border-b border-gray-200 dark:border-gray-600 mb-4">
						<nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
							@{
								var supportedLanguages = (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"];
								var isFirst = true;
							}
							@foreach (var lang in supportedLanguages)
							{
								<button type="button" onclick="window.switchLanguageTab('@lang.Code', '#csrActivityModal', {buttonClass: 'csr-lang-tab-button', contentClass: 'csr-lang-content', contentIdPrefix: 'csr-lang-'})"
										class="csr-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
										data-lang="@lang.Code">
									@lang.Emoji @lang.Name
								</button>
								isFirst = false;
							}
						</nav>
					</div>

					@{
						isFirst = true;
					}
					@foreach (var lang in supportedLanguages)
					{
						<!-- @lang.Name Fields -->
						<div id="<EMAIL>" class="csr-lang-content @(isFirst ? "" : "hidden")">
							<div class="grid grid-cols-1 gap-4">
								<div>
									<label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
										@AdminRes["ActivityTitle"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"].ToString()){
										<span class="text-red-500">*</span>
									}
								</label>
								<input type="text" id="<EMAIL>" name="<EMAIL>"
									   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
									   required="@(lang.Code == ViewData["DefaultLanguage"].ToString())" />
							</div>
							<div class="grid grid-cols-2 gap-4">
								<div>
									<label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
										@AdminRes["ActivityDescription"] (@lang.Name)
									</label>
									<textarea id="<EMAIL>" name="<EMAIL>" rows="3"
											  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
								</div>
								<div>
									<label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
										@AdminRes["ActivitySummary"] (@lang.Name)
									</label>
									<textarea id="<EMAIL>" name="<EMAIL>" rows="3"
											  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
								</div>
							</div>
						</div>
					</div>
					isFirst = false;
										}
				</div>

				<!-- Active Status -->
				<div class="mt-6">
					<div class="flex items-center">
						<input type="checkbox" id="csrActivityIsActive" name="isActive" checked
							   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded" />
						<label for="csrActivityIsActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
							@AdminRes["IsActive"]
						</label>
					</div>
				</div>

				<!-- Action Buttons -->
				<div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
					<button type="button" onclick="closeCSRActivityModal()"
							class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
						@AdminRes["Cancel"]
					</button>
					<button type="submit" id="saveCSRActivityBtn"
							class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
						@AdminRes["Save"]
					</button>
				</div>
			</form>
		</div>
	</div>
</div>