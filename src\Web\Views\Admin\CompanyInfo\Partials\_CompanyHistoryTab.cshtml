@model List<MlSoft.Sites.Web.ViewModels.Admin.CompanyHistoryItemViewModel>

<div class="flex justify-between items-center mb-6">
    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["CompanyHistory"]</h3>
    <button onclick="openHistoryModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
        <i class="fas fa-plus mr-2"></i>
        @AdminRes["AddHistoryEvent"]
    </button>
</div>

<!-- Company History Modal (moved from Index) -->
<div id="historyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="historyModalTitle">@AdminRes["AddHistoryEvent"]</h3>
                <button onclick="closeHistoryModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"><i class="fas fa-times"></i></button>
            </div>

            <form id="historyForm">
                <input type="hidden" id="historyId" name="id" />


                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4">
                    <div class="md:col-span-2 lg:col-span-2 grid grid-cols-2 gap-4">
                        <div>
                            <label for="eventDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["EventDate"] <span class="text-red-500">*</span></label>
                            <input type="date" id="eventDate" name="eventDate" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" required />
                        </div>
                        <div>
                            <label for="eventType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["EventType"] <span class="text-red-500">*</span></label>
                            <select id="eventType" name="eventType" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" required>
                                <option value="">@AdminRes["SelectEventType"]</option>
                                <option value="0">@AdminRes["Establishment"]</option>
                                <option value="1">@AdminRes["Expansion"]</option>
                                <option value="2">@AdminRes["ProductLaunch"]</option>
                                <option value="3">@AdminRes["Acquisition"]</option>
                                <option value="4">@AdminRes["Partnership"]</option>
                                <option value="5">@AdminRes["Award"]</option>
                                <option value="6">@AdminRes["Milestone"]</option>
                                <option value="7">@AdminRes["Other"]</option>
                            </select>
                        </div>
                        <div>
                            <label for="displayOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["DisplayOrder"]</label>
                            <input type="number" id="displayOrder" name="displayOrder" min="0" max="9999" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>
                        <div class="mt-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="isActive" name="isActive" checked class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded" />
                                <label for="isActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">@AdminRes["IsActive"]</label>
                            </div>
                        </div>
                    </div>


                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["EventImage"]</label>
                            <div id="eventImageUpload" class="mt-2"></div>
                            <input type="hidden" id="imageUrl" name="imageUrl" />
                        </div>
                    </div>

                </div>

                <div class="mt-6">
                    <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                        <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                            @{
                                var supportedLanguages = (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"];
                                var isFirst = true;
                            }
                            @foreach (var lang in supportedLanguages)
                            {
                                <button type="button" onclick="switchLanguageTab('@lang.Code')" class="lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" data-lang="@lang.Code">@lang.Emoji @lang.Name</button>
                                isFirst = false;
                            }
                        </nav>
                    </div>

                    @{
                        isFirst = true;
                    }
                    @foreach (var lang in supportedLanguages)
                    {
                        <div id="<EMAIL>" class="lang-content @(isFirst ? "" : "hidden")">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["EventTitle"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"].ToString()){
                                    <span class="text-red-500">*</span>
                                }
</label>
                                <input type="text" id="<EMAIL>" name="<EMAIL>" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" required="@(lang.Code == ViewData["DefaultLanguage"].ToString())" />
                            </div>
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["EventDescription"] (@lang.Name)</label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="3" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                        </div>
                    </div>
                    isFirst = false;
                                        }
                </div>


                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="closeHistoryModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">@AdminRes["Cancel"]</button>
                    <button type="submit" id="saveHistoryBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">@AdminRes["Save"]</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- History Table -->
<div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    @AdminRes["EventDate"]
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    @AdminRes["EventType"]
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    @AdminRes["EventTitle"]
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    @AdminRes["DisplayOrder"]
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    @AdminRes["Status"]
                </th>
                <th scope="col" class="relative px-6 py-3">
                    <span class="sr-only">@AdminRes["Actions"]</span>
                </th>
            </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            @foreach (var history in Model.OrderBy(h => h.DisplayOrder))
            {
                <tr data-history-id="@history.Id">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        @history.EventDate.ToString("yyyy-MM-dd")
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        @(AdminRes[history.EventType.ToString()])
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        @history.EventTitles.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        @history.DisplayOrder
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @if (history.IsActive)
                        {
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                @AdminRes["Active"]
                            </span>
                        }
                        else
                        {
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                @AdminRes["Inactive"]
                            </span>
                        }
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button onclick="editHistory('@history.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"><i class="fas fa-edit"></i></button>
                        <button onclick="deleteHistory('@history.Id')" class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"><i class="fas fa-trash"></i></button>
                    </td>
                </tr>
            }
            @if (!Model.Any())
            {
                <tr>
                    <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                        @AdminRes["NoCompanyHistoryRecords"]
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>