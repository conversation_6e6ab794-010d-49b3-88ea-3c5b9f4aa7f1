@model MlSoft.Sites.Web.ViewModels.Admin.ContactInfoViewModel

<div class="space-y-8">
    <!-- 基本联系信息部分 -->
    <div>
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["BasicContactInfo"]</h3>
            <button onclick="openContactInfoModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-edit mr-2"></i>
                @AdminRes["EditContactInfo"]
            </button>
        </div>

        <!-- 联系信息卡片 -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                @if (Model.BasicContactInfo != null)
                {
                    <dl class="grid grid-cols-3  md:grid-cols-3 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["Phone"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @(Model.BasicContactInfo.Phone ?? "-")
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["Fax"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @(Model.BasicContactInfo.Fax ?? "-")
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["Email"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @(Model.BasicContactInfo.Email ?? "-")
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["Website"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @if (!string.IsNullOrEmpty(Model.BasicContactInfo.Website))
                                {
                                    <a href="@Model.BasicContactInfo.Website" target="_blank" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                        @Model.BasicContactInfo.Website
                                    </a>
                                }
                                else
                                {
                                    <span>-</span>
                                }
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["MapLink"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @if (!string.IsNullOrEmpty(Model.BasicContactInfo.MapLink))
                                {
                                    <button onclick="openMapLinkModal('@Model.BasicContactInfo.MapLink')" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 underline">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        @AdminRes["View"]
                                    </button>
                                }
                                else
                                {
                                    <span>-</span>
                                }
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["PostalCode"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @(Model.BasicContactInfo.PostalCode ?? "-")
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["Address"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @(Model.BasicContactInfo.Addresses.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "") ?? "-")
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["BusinessHours"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @(Model.BasicContactInfo.BusinessHours.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "") ?? "-")
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["AccessInfo"]</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                @(Model.BasicContactInfo.AccessInfo.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "") ?? "-")
                            </dd>
                        </div>
                        @* @if (Model.BasicContactInfo.Latitude.HasValue && Model.BasicContactInfo.Longitude.HasValue)
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">@AdminRes["LocationCoordinates"]</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    @Model.BasicContactInfo.Latitude.Value.ToString("F6"), @Model.BasicContactInfo.Longitude.Value.ToString("F6")
                                </dd>
                            </div>
                        } *@
                    </dl>
                }
                else
                {
                    <div class="text-center py-6">
                        <p class="text-gray-500 dark:text-gray-400">@AdminRes["NoContactInfo"]</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- 分支机构/据点信息部分 -->
    <div>
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["CompanyLocations"]</h3>
            <button onclick="openLocationModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-plus mr-2"></i>
                @AdminRes["AddLocation"]
            </button>
        </div>

        <!-- 据点表格 -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["LocationName"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["LocationType"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Address"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Phone"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Status"]
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">@AdminRes["Actions"]</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach (var location in Model.Locations.OrderBy(l => l.DisplayOrder))
                    {
                        <tr data-location-id="@location.Id">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        @location.Names.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
                                        @if (location.Type == LocationType.Headquarters)
                                        {
                                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                                @AdminRes["Headquarters"]
                                            </span>
                                        }
                                        @if (location.IsPrimary)
                                        {
                                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                                @AdminRes["PrimaryLocation"]
                                            </span>
                                        }
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @switch (location.Type)
                                {
                                    case MlSoft.Sites.Model.Entities.Enums.LocationType.Headquarters:
                                        @AdminRes["Headquarters"]
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.LocationType.Branch:
                                        @AdminRes["Branch"]
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.LocationType.Factory:
                                        @AdminRes["Factory"]
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.LocationType.Office:
                                        @AdminRes["Office"]
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.LocationType.Warehouse:
                                        @AdminRes["Warehouse"]
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.LocationType.Laboratory:
                                        @AdminRes["ResearchCenter"]
                                        break;
                                    default:
                                        @AdminRes["Other"]
                                        break;
                                }
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate">
                                @if (location.ContactInfo != null)
                                {
                                    @(location.ContactInfo.Addresses.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "") ?? "-")
                                }
                                else
                                {
                                    <span>-</span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @if (location.ContactInfo != null)
                                {
                                    @(location.ContactInfo.Phone ?? "-")
                                }
                                else
                                {
                                    <span>-</span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if (location.IsActive)
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                        @AdminRes["Active"]
                                    </span>
                                }
                                else
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                        @AdminRes["Inactive"]
                                    </span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button onclick="editLocation('@location.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"><i class="fas fa-edit"></i></button>
                                <button onclick="deleteLocation('@location.Id')" class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    }
                    @if (!Model.Locations.Any())
                    {
                        <tr>
                            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                                @AdminRes["NoLocationRecords"]
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Contact Info Modal (moved from Index) -->
<div id="contactInfoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="contactInfoModalTitle">@AdminRes["EditContactInfo"]</h3>
                <button onclick="closeContactInfoModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="contactInfoForm">
                <div class="grid grid-cols-2 md:grid-cols-3 gap-6">
                    <div>
                        <label for="contactPhone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Phone"]</label>
                        <input type="tel" id="contactPhone" name="phone" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>
                    <div>
                        <label for="contactFax" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Fax"]</label>
                        <input type="tel" id="contactFax" name="fax" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>
                    <div>
                        <label for="contactEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Email"]</label>
                        <input type="email" id="contactEmail" name="email" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>
                    <div>
                        <label for="contactWebsite" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Website"]</label>
                        <input type="url" id="contactWebsite" name="website" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>
                    <div>
                        <label for="contactPostalCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["PostalCode"]</label>
                        <input type="text" id="contactPostalCode" name="postalCode" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>
                </div>
                
                <div class="mt-6">
                    <div>
                        <label for="contactPostalCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["MapLink"]</label>
                        <input type="text" id="mapLink" name="mapLink" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>
                    <div id="mapPreivew"></div>
                </div>

                <div class="mt-6">
                    <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                        <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                            @{
                                var isFirstContact = true;
                            }
                            @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                            {
                                <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#contactInfoModal', {buttonClass: 'contact-lang-tab-button', contentClass: 'contact-lang-content', contentIdPrefix: 'contact-lang-'})" class="contact-lang-tab-button @(isFirstContact ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" data-lang="@lang.Code">
                                    @lang.Emoji @lang.Name
                                </button>
                                isFirstContact = false;
                            }
                        </nav>
                    </div>

                    @{
                        isFirstContact = true;
                    }
                    @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                    {
                        <div id="<EMAIL>" class="contact-lang-content @(isFirstContact ? "" : "hidden")">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Address"] (@lang.Name)</label>
                                    <input type="text" id="<EMAIL>" name="<EMAIL>"  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["BusinessHours"] (@lang.Name)</label>
                                        <textarea  id="<EMAIL>" rows="2" name="<EMAIL>" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["AccessInfo"] (@lang.Name)</label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="2" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        isFirstContact = false;
                    }
                </div>

                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="closeContactInfoModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">@AdminRes["Cancel"]</button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">@AdminRes["Save"]</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Company Location Modal (moved from Index) -->
<div id="locationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="locationModalTitle">@AdminRes["AddLocation"]</h3>
                <button onclick="closeLocationModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"><i class="fas fa-times"></i></button>
            </div>

            <form id="locationForm">
                <input type="hidden" id="locationId" name="id" />

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="locationType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["LocationType"] <span class="text-red-500">*</span></label>
                        <select id="locationType" name="type" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" required>
                            <option value="">@AdminRes["PleaseSelect"]</option>
                            <option value="0">@AdminRes["Headquarters"]</option>
                            <option value="1">@AdminRes["Branch"]</option>
                            <option value="2">@AdminRes["Factory"]</option>
                            <option value="3">@AdminRes["Office"]</option>
                            <option value="4">@AdminRes["Warehouse"]</option>
                            <option value="5">@AdminRes["ResearchCenter"]</option>
                        </select>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="locationIsPrimary" name="isPrimary" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded" />
                        <label for="locationIsPrimary" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">@AdminRes["IsPrimary"]</label>
                    </div>
                    <div>
                        <label for="locationDisplayOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["DisplayOrder"]</label>
                        <input type="number" id="locationDisplayOrder" name="displayOrder" min="0" max="9999" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>
                   
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">@AdminRes["LocationName"] *</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                        {
                            <div>
                                <label for="<EMAIL>" class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">@lang.Emoji @lang.Name</label>
                                <input type="text" id="<EMAIL>" name="names[@lang.Code]" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" />
                            </div>
                        }
                    </div>
                </div>

                <div class="mt-6 border-t border-gray-200 dark:border-gray-600 pt-6">
                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">@AdminRes["ContactInfo"]</h4>
                    <div class="grid grid-cols-4 md:grid-cols-4 gap-6">
                        <div>
                            <label for="locationPhone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Phone"]</label>
                            <input type="tel" id="locationPhone" name="contactInfo.phone" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>
                        <div>
                            <label for="locationFax" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Fax"]</label>
                            <input type="tel" id="locationFax" name="contactInfo.fax" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>
                        <div>
                            <label for="locationEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Email"]</label>
                            <input type="email" id="locationEmail" name="contactInfo.email" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>
                        <div>
                            <label for="locationPostalCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["PostalCode"]</label>
                            <input type="text" id="locationPostalCode" name="contactInfo.postalCode" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>
                    </div>
                    <div class="mt-6">
                        <label for="locationMapLink" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["MapLink"]</label>
                        <input type="text" id="locationMapLink" name="contactInfo.mapLink" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>

                    <div class="mt-6">
                        <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                            <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                                @{
                                    var isFirstLoc = true;
                                }
                                @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                                {
                                    <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#locationModal', {buttonClass: 'location-lang-tab-button', contentClass: 'location-lang-content', contentIdPrefix: 'location-lang-'})" class="location-lang-tab-button @(isFirstLoc ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" data-lang="@lang.Code">@lang.Emoji @lang.Name</button>
                                    isFirstLoc = false;
                                }
                            </nav>
                        </div>

                        @{
                            isFirstLoc = true;
                        }
                        @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                        {
                            <div id="<EMAIL>" class="location-lang-content @(isFirstLoc ? "" : "hidden")">
                                <div class="grid grid-cols-1 gap-4">
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Address"] (@lang.Name)</label>
                                        <input type="text" id="<EMAIL>" name="<EMAIL>" rows="2" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["BusinessHours"] (@lang.Name)</label>
                                            <textarea rows="2" id="<EMAIL>" name="<EMAIL>" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["AccessInfo"] (@lang.Name)</label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="2" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                                    </div>
                                    </div>
                                </div>
                            </div>
                            isFirstLoc = false;
                        }
                    </div>
                </div>

                <div class="mt-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="locationIsActive" name="isActive" checked class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded" />
                        <label for="locationIsActive" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">@AdminRes["IsActive"]</label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="closeLocationModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">@AdminRes["Cancel"]</button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">@AdminRes["Save"]</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Map Link Modal -->
<div id="mapLinkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">@AdminRes["MapLink"]</h3>
                <button onclick="closeMapLinkModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">@AdminRes["MapLink"]</label>
                <div class="flex">
                    <input type="text" id="mapLinkDisplay" readonly class="flex-1 border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-3 py-2" />
                    <button onclick="copyMapLink()" class="px-4 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">@AdminRes["Preview"]</label>
                <div class="border border-gray-300 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-700">
                    <iframe id="mapPreview" src="" width="100%" height="300" frameborder="0" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button onclick="closeMapLinkModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">@AdminRes["Close"]</button>
                <button onclick="openMapInNewTab()" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    @AdminRes["OpenInNewTab"]
                </button>
            </div>
        </div>
    </div>
</div>