@model MlSoft.Sites.Web.ViewModels.Admin.ExecutiveOrganizationViewModel

<div class="space-y-8">
    <!-- 高管信息部分 -->
    <div>
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["ExecutiveInformation"]</h3>
            <button onclick="openExecutiveModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-plus mr-2"></i>
                @AdminRes["AddExecutive"]
            </button>
        </div>

        <!-- 高管表格 -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md mb-8">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["ExecutiveName"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Position"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["DisplayOrder"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["IsPresident"]
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">@AdminRes["Actions"]</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach (var executive in Model.Executives.OrderBy(e => e.DisplayOrder))
                    {
                        <tr data-executive-id="@executive.Id">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    @if (!string.IsNullOrEmpty(executive.PhotoUrl))
                                    {
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full object-cover" src="@executive.PhotoUrl" alt="@executive.Names.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")" />
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                @executive.Names.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                <i class="fas fa-user text-gray-500 dark:text-gray-400"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                @executive.Names.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
                                            </div>
                                        </div>
                                    }
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @executive.Positions.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @executive.DisplayOrder
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if (executive.IsPresident)
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                        @AdminRes["President"]
                                    </span>
                                }
                                else
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                        @AdminRes["Executive"]
                                    </span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button onclick="editExecutive('@executive.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"><i class="fas fa-edit"></i></button>
                                <button onclick="deleteExecutive('@executive.Id')" class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    }
                    @if (!Model.Executives.Any())
                    {
                        <tr>
                            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                                @AdminRes["NoExecutiveRecords"]
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- 组织架构部分 -->
    <div>
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["OrganizationStructure"]</h3>
            <button onclick="openOrganizationModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-plus mr-2"></i>
                @AdminRes["AddDepartment"]
            </button>
        </div>

        <!-- 组织架构表格 -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["DepartmentName"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Level"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["DisplayOrder"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["ParentDepartment"]
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">@AdminRes["Actions"]</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach (var org in Model.OrganizationStructures.OrderBy(o => o.Level).ThenBy(o => o.DisplayOrder))
                    {
                        <tr data-organization-id="@org.Id">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div style="margin-left: @(org.Level * 20)px;" class="flex items-center">
                                    @if (org.Level > 0)
                                    {
                                        <i class="fas fa-arrow-turn-down-right mr-2 text-gray-400"></i>
                                    }
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        @org.DepartmentNames.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "")
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @org.Level
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @org.DisplayOrder
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @if (!string.IsNullOrEmpty(org.ParentDepartmentId))
                                {
                                    var parentDept = Model.OrganizationStructures.FirstOrDefault(o => o.Id == org.ParentDepartmentId);
                                    @(parentDept?.DepartmentNames.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), "") ?? "-")
                                }
                                else
                                {
                                    <span class="text-gray-500">@AdminRes["RootDepartment"]</span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button onclick="editOrganization('@org.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"><i class="fas fa-edit"></i></button>
                                <button onclick="deleteOrganization('@org.Id')" class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    }
                    @if (!Model.OrganizationStructures.Any())
                    {
                        <tr>
                            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                                @AdminRes["NoOrganizationRecords"]
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Executive Management Modal -->
<div id="executiveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center pb-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="executiveModalTitle">@AdminRes["AddExecutive"]</h3>
                <button onclick="closeExecutiveModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="executiveForm" class="space-y-6">
                <input type="hidden" id="executiveId" name="id" />



                <div class="grid grid-cols-2 gap-4 mt-4">
                    <!-- Photo Upload -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">@AdminRes["PhotoUrl"]</label>
                        <div class="mt-1 space-y-3">
                            <div id="executivePhotoUpload"></div>
                            <!-- Hidden field to store the uploaded photo URL -->
                            <input type="hidden" id="executivePhotoUrl" name="photoUrl" />
                        </div>
                    </div>

                    <div>
                        <!-- Display Order -->
                        <div>
                            <label for="executiveDisplayOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["DisplayOrder"]</label>
                            <input type="number" id="executiveDisplayOrder" name="displayOrder" min="0" class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                        </div>

                        <!-- Is President -->
                        <div class="flex items-center mt-4">
                            <input type="checkbox" id="executiveIsPresident" name="isPresident" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                            <label for="executiveIsPresident" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">@AdminRes["IsPresident"]</label>
                        </div>
                    </div>
                </div>



                <!-- Executive Multilingual Fields -->
                <div class="mt-6">
                    <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                        <nav class="-mb-px flex space-x-8" aria-label="Executive Language Tabs">
                            @{
                                var supportedLanguages = (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData?["SupportedLanguages"];
                                var isFirst = true;
                            }
                            @foreach (var lang in supportedLanguages)
                            {
                                <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#executiveModal', {buttonClass: 'executive-lang-tab-button', contentClass: 'executive-lang-content', contentIdPrefix: 'executive-lang-'})"
                                        class="executive-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                        data-lang="@lang.Code">
                                    @lang.Emoji @lang.Name
                                </button>
                                isFirst = false;
                            }
                        </nav>
                    </div>

                    @{
                        isFirst = true;
                    }
                    @foreach (var lang in supportedLanguages)
                    {
                        <!-- @lang.Name Executive Fields -->
                        <div id="<EMAIL>" class="executive-lang-content @(isFirst ? "" : "hidden")">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        @AdminRes["ExecutiveName"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"]?.ToString()){
                                        <span class="text-red-500">*</span>
                                    }
                                </label>
                                <input type="text" id="<EMAIL>" name="<EMAIL>"
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                       required="@(lang.Code == ViewData["DefaultLanguage"]?.ToString())" />
                            </div>
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["Position"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"]?.ToString()){
                                    <span class="text-red-500">*</span>
                                                                        }
                                </label>
                                <input type="text" id="<EMAIL>" name="<EMAIL>"
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                       required="@(lang.Code == ViewData["DefaultLanguage"]?.ToString())" />
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 mt-4">
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["Biography"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="4"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["Message"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="4"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                        </div>
                    </div>
                                        isFirst = false;
                                        }
                </div>



                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button type="button" onclick="closeExecutiveModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                        @AdminRes["Cancel"]
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AdminRes["Save"]
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Organization Management Modal -->
<div id="organizationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center pb-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="organizationModalTitle">@AdminRes["AddDepartment"]</h3>
                <button onclick="closeOrganizationModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="organizationForm" class="space-y-6">
                <input type="hidden" id="organizationId" name="id" />

                <!-- Organization Multilingual Fields -->
                <div class="mt-6">
                    <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                        <nav class="-mb-px flex space-x-8" aria-label="Organization Language Tabs">
                            @{
                                var supportedLanguages = (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"];
                                var isFirst = true;
                            }
                            @foreach (var lang in supportedLanguages)
                            {
                                <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#organizationModal', {buttonClass: 'organization-lang-tab-button', contentClass: 'organization-lang-content', contentIdPrefix: 'organization-lang-'})"
                                        class="organization-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                        data-lang="@lang.Code">
                                    @lang.Emoji @lang.Name
                                </button>
                                isFirst = false;
                            }
                        </nav>
                    </div>

                    @{
                        isFirst = true;
                    }
                    @foreach (var lang in supportedLanguages)
                    {
                        <!-- @lang.Name Organization Fields -->
                        <div id="<EMAIL>" class="organization-lang-content @(isFirst ? "" : "hidden")">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        @AdminRes["DepartmentName"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"]?.ToString()){
                                        <span class="text-red-500">*</span>
                                    }
                                </label>
                                <input type="text" id="<EMAIL>" name="<EMAIL>"
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                       required="@(lang.Code == ViewData["DefaultLanguage"]?.ToString())" />
                            </div>
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["DepartmentDescription"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                        </div>
                    </div>
                    isFirst = false;
                                        }
                </div>

                <!-- Organization Other Fields -->
                <!-- Parent Department -->
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <label for="organizationParentDepartment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["ParentDepartment"]</label>
                        <select id="organizationParentDepartment" name="parentDepartmentId" class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                            <option value="">@AdminRes["RootDepartment"]</option>
                            <!-- Department options will be populated by JavaScript -->
                        </select>
                    </div>

                    <!-- Level -->
                    <div>
                        <label for="organizationLevel" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["Level"]</label>
                        <input type="number" id="organizationLevel" name="level" min="0" max="10" class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <!-- Display Order -->
                    <div>
                        <label for="organizationDisplayOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["DisplayOrder"]</label>
                        <input type="number" id="organizationDisplayOrder" name="displayOrder" min="0" class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    </div>


                </div>
                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button type="button" onclick="closeOrganizationModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                        @AdminRes["Cancel"]
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AdminRes["Save"]
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>