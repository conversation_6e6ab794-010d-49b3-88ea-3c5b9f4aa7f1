@model MlSoft.Sites.Web.ViewModels.Admin.InvestorRelationsViewModel

<div class="space-y-6">
    <!-- 财务报告管理 -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["FinancialReports"]</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">@AdminRes["ManageFinancialReportsDescription"]</p>
                </div>
                <button onclick="openFinancialReportModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-2"></i>
                    @AdminRes["AddFinancialReport"]
                </button>
            </div>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["ReportTitle"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["ReportType"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Period"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["PublishDate"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Status"]
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">@AdminRes["Actions"]</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach (var report in Model.FinancialReports.OrderByDescending(r => r.PublishDate))
                    {
                        <tr data-report-id="@report.Id">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 mr-4 flex items-center justify-center bg-primary-100 dark:bg-primary-900/30 rounded-lg">
                                        <i class="fas fa-file-alt text-primary-600 dark:text-primary-400"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            @report.Titles.GetValueOrDefault(ViewData["CurrentLanguage"].ToString(), "")
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                                            @report.Summaries.GetValueOrDefault(ViewData["CurrentLanguage"].ToString(), "")
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @switch (report.Type)
                                {
                                    case MlSoft.Sites.Model.Entities.Enums.ReportType.AnnualReport:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                            @AdminRes["AnnualReport"]
                                        </span>
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.ReportType.QuarterlyReport:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            @AdminRes["QuarterlyReport"]
                                        </span>
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.ReportType.EarningsReport:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            @AdminRes["EarningsRelease"]
                                        </span>
                                        break;
                                    default:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            @AdminRes["Other"]
                                        </span>
                                        break;
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @if (report.Quarter.HasValue)
                                {
                                    @($"{report.Year} {AdminRes["YearSuffix"]} Q{report.Quarter}")
                                }
                                else
                                {
                                    @($"{report.Year} {AdminRes["YearSuffix"]}")
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @report.PublishDate.ToString("yyyy-MM-dd")
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if (report.IsPublished)
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                        @AdminRes["Published"]
                                    </span>
                                }
                                else
                                {
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                        @AdminRes["Draft"]
                                    </span>
                                }
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button onclick="editFinancialReport('@report.Id')" class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteFinancialReport('@report.Id')" class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">  <i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    }
                    @if (!Model.FinancialReports.Any())
                    {
                        <tr>
                            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                                @AdminRes["NoFinancialReportRecords"]
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- 股东大会管理 -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">@AdminRes["ShareholderMeetings"]</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">@AdminRes["ManageShareholderMeetingsDescription"]</p>
                </div>
                <button onclick="openShareholderMeetingModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-2"></i>
                    @AdminRes["AddShareholderMeeting"]
                </button>
            </div>
        </div>
        <div class="border-t border-gray-200 dark:border-gray-700">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["MeetingTitle"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["MeetingDate"]
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Status"]
                        </th>
                        @* <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            @AdminRes["Documents"]
                        </th> *@
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">@AdminRes["Actions"]</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach (var meeting in Model.ShareholderMeetings.OrderByDescending(m => m.MeetingDate))
                    {
                        <tr data-meeting-id="@meeting.Id">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 mr-4 flex items-center justify-center bg-primary-100 dark:bg-primary-900/30 rounded-lg">
                                        <i class="fas fa-users text-primary-600 dark:text-primary-400"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            @meeting.Titles.GetValueOrDefault(ViewData["CurrentLanguage"].ToString(), "")
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                                            @meeting.Descriptions.GetValueOrDefault(ViewData["CurrentLanguage"].ToString(), "")
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @meeting.MeetingDate.ToString("yyyy-MM-dd")
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @switch (meeting.Status)
                                {
                                    case MlSoft.Sites.Model.Entities.Enums.MeetingStatus.Scheduled:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            @AdminRes["Scheduled"]
                                        </span>
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.MeetingStatus.InProgress:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                            @AdminRes["InProgress"]
                                        </span>
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.MeetingStatus.Completed:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            @AdminRes["Completed"]
                                        </span>
                                        break;
                                    case MlSoft.Sites.Model.Entities.Enums.MeetingStatus.Cancelled:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                            @AdminRes["Cancelled"]
                                        </span>
                                        break;
                                    default:
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                            @AdminRes["Unknown"]
                                        </span>
                                        break;
                                }
                            </td>
                            @* <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                @if (meeting.Documents.Any())
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300">
                                        <i class="fas fa-file-alt mr-1"></i>
                                        @meeting.Documents.Count @AdminRes["Documents"]
                                    </span>
                                }
                                else
                                {
                                    <span class="text-gray-500 dark:text-gray-400">@AdminRes["NoDocuments"]</span>
                                }
                            </td> *@
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button onclick="editShareholderMeeting('@meeting.Id')" class="text-2xl  text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"><i class="fas fa-edit"></i></button>
                                <button onclick="deleteShareholderMeeting('@meeting.Id')" class="text-2xl  text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    }
                    @if (!Model.ShareholderMeetings.Any())
                    {
                        <tr>
                            <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                                @AdminRes["NoShareholderMeetingRecords"]
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- 投资者关系统计卡片 -->
    @if (Model.FinancialReports.Any() || Model.ShareholderMeetings.Any())
    {
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <!-- 财务报告总数 -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-chart-line text-2xl text-primary-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    @AdminRes["TotalFinancialReports"]
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    @Model.FinancialReports.Count
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已发布报告 -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-2xl text-primary-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    @AdminRes["PublishedReports"]
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    @Model.FinancialReports.Count(r => r.IsPublished)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 股东大会总数 -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-users text-2xl text-primary-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    @AdminRes["TotalMeetings"]
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    @Model.ShareholderMeetings.Count
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 即将举行的会议 -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-calendar-alt text-2xl text-primary-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    @AdminRes["UpcomingMeetings"]
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    @Model.ShareholderMeetings.Count(m => m.MeetingDate > DateTime.Now && m.Status == MlSoft.Sites.Model.Entities.Enums.MeetingStatus.Scheduled)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Financial Report Modal -->
<div id="financialReportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="financialReportModalTitle">@AdminRes["AddFinancialReport"]</h3>
                <button type="button" onclick="closeFinancialReportModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Form -->
            <form id="financialReportForm" class="space-y-6">
                <input type="hidden" id="financialReportId" name="id" />

                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4">

                    <div class="md:col-span-2 lg:col-span-2 grid grid-cols-3 gap-4">


                        <!-- Report Type -->
                        <div>
                            <label for="financialReportType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["ReportType"] <span class="text-red-500">*</span>
                            </label>
                            <select id="financialReportType" name="type" required
                                    class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="">@AdminRes["PleaseSelect"]</option>
                                <option value="@((int)ReportType.AnnualReport)">@AdminRes["AnnualReport"]</option>
                                <option value="@((int)ReportType.QuarterlyReport)">@AdminRes["QuarterlyReport"]</option>
                                <option value="@((int)ReportType.EarningsReport)">@AdminRes["EarningsRelease"]</option>
                                <option value="@((int)ReportType.SecuritiesReport)">@AdminRes["SecuritiesReport"]</option>
                            </select>
                        </div>



                        <!-- Year -->
                        <div>
                            <label for="financialReportYear" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["Year"] <span class="text-red-500">*</span>
                            </label>
                            <input type="number" id="financialReportYear" name="year" required min="2000" max="2050"
                                   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                   value="@DateTime.Now.Year" />
                        </div>

                        <!-- Quarter -->
                        <div>
                            <label for="financialReportQuarter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["Quarter"]
                            </label>
                            <select id="financialReportQuarter" name="quarter"
                                    class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="">@AdminRes["NotApplicable"]</option>
                                <option value="1">Q1</option>
                                <option value="2">Q2</option>
                                <option value="3">Q3</option>
                                <option value="4">Q4</option>
                            </select>
                        </div>



                        <!-- Publish Date -->
                        <div>
                            <label for="financialReportPublishDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["PublishDate"] <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="financialReportPublishDate" name="publishDate" required
                                   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>

                        <!-- Currency -->
                        <div>
                            <label for="financialReportCurrency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["Currency"]
                            </label>
                            <select id="financialReportCurrency" name="currency"
                                    class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="JPY">JPY (@AdminRes["JPY"])</option>
                                <option value="USD">USD (@AdminRes["USD"])</option>
                                <option value="CNY">CNY (@AdminRes["CNY"])</option>
                                <option value="EUR">EUR (@AdminRes["EUR"])</option>
                            </select>
                        </div>

                        <div>&nbsp;</div>
                        <!-- Revenue -->
                        <div>
                            <label for="financialReportRevenue" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["Revenue"]
                            </label>
                            <input type="number" id="financialReportRevenue" name="revenue" step="0.01"
                                   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>

                        <!-- Net Income -->
                        <div>
                            <label for="financialReportNetIncome" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["NetIncome"]
                            </label>
                            <input type="number" id="financialReportNetIncome" name="netIncome" step="0.01"
                                   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>

                        <!-- Total Assets -->
                        <div>
                            <label for="financialReportTotalAssets" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                @AdminRes["TotalAssets"]
                            </label>
                            <input type="number" id="financialReportTotalAssets" name="totalAssets" step="0.01"
                                   class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                        </div>



                    </div>

                    <div class="md:col-span-1 lg:col-span-1">

                        <!-- Report File Upload -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                @AdminRes["ReportFile"]
                            </label>
                            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
                                <div class="flex flex-col items-center" onclick="document.getElementById('financialReportFile').click();">
                                    <svg class="w-8 h-8 mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    <p class="text-sm text-gray-500">@AdminRes["ClickUploadLogo"]</p>
                                    <p class="text-sm text-gray-500">@AdminRes["FileUpload_OrDragDrop"]</p>
                                </div>

                                <input type="file" id="financialReportFile" name="reportFile"
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                                       class="hidden" />
                                <div id="financialReportFileList" class="mt-3 space-y-2"></div>
                                <input type="hidden" id="financialReportFileUrl" name="reportFileUrl" />
                            </div>
                        </div>

                    </div>


                </div>



                <!-- Multilingual Fields -->
                <div class="mt-6">
                    <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                        <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                            @{
                                var supportedLanguages = (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"];
                                var isFirst = true;
                            }
                            @foreach (var lang in supportedLanguages)
                            {
                                <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#financialReportModal', {buttonClass: 'financial-lang-tab-button', contentClass: 'financial-lang-content', contentIdPrefix: 'financial-lang-'})"
                                        class="financial-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                        data-lang="@lang.Code">
                                    @lang.Emoji @lang.Name
                                </button>
                                isFirst = false;
                            }
                        </nav>
                    </div>

                    @{
                        isFirst = true;
                    }
                    @foreach (var lang in supportedLanguages)
                    {
                        <!-- @lang.Name Fields -->
                        <div id="<EMAIL>" class="financial-lang-content @(isFirst ? "" : "hidden")">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        @AdminRes["ReportTitle"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"].ToString()){
                                        <span class="text-red-500">*</span>
                                    }
                                </label>
                                <input type="text" id="<EMAIL>" name="<EMAIL>"
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                       required="@(lang.Code == ViewData["DefaultLanguage"].ToString())" />
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 mt-4">
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["ReportSummary"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["FinancialAnalysis"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                        </div>
                    </div>
                    isFirst = false;
                                        }
                </div>

                <!-- Published Status -->
                <div class="mt-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="financialReportIsPublished" name="isPublished"
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded" />
                        <label for="financialReportIsPublished" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                            @AdminRes["IsPublished"]
                        </label>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="closeFinancialReportModal()"
                            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AdminRes["Cancel"]
                    </button>
                    <button type="submit" id="saveFinancialReportBtn"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AdminRes["Save"]
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Shareholder Meeting Modal -->
<div id="shareholderMeetingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="shareholderMeetingModalTitle">@AdminRes["AddShareholderMeeting"]</h3>
                <button type="button" onclick="closeShareholderMeetingModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Form -->
            <form id="shareholderMeetingForm" class="space-y-6">
                <input type="hidden" id="shareholderMeetingId" name="id" />

                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Meeting Date -->
                    <div>
                        <label for="shareholderMeetingDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            @AdminRes["MeetingDate"] <span class="text-red-500">*</span>
                        </label>
                        <input type="date" id="shareholderMeetingDate" name="meetingDate" required
                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="shareholderMeetingStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            @AdminRes["Status"] <span class="text-red-500">*</span>
                        </label>
                        <select id="shareholderMeetingStatus" name="status" required
                                class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                            <option value="">@AdminRes["PleaseSelect"]</option>
                            <option value="@((int)MeetingStatus.Scheduled)">@AdminRes["Scheduled"]</option>
                            <option value="@((int)MeetingStatus.InProgress)">@AdminRes["InProgress"]</option>
                            <option value="@((int)MeetingStatus.Completed)">@AdminRes["Completed"]</option>
                            <option value="@((int)MeetingStatus.Cancelled)">@AdminRes["Cancelled"]</option>
                        </select>
                    </div>
                </div>

                <!-- Multilingual Fields -->
                <div class="mt-6">
                    <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                        <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                            @{
                                var supportedLangs = (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"];
                                var langIsFirst = true;
                            }
                            @foreach (var lang in supportedLangs)
                            {
                                <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#shareholderMeetingModal', {buttonClass: 'meeting-lang-tab-button', contentClass: 'meeting-lang-content', contentIdPrefix: 'meeting-lang-'})"
                                        class="meeting-lang-tab-button @(langIsFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                        data-lang="@lang.Code">
                                    @lang.Emoji @lang.Name
                                </button>
                                langIsFirst = false;
                            }
                        </nav>
                    </div>

                    @{
                        langIsFirst = true;
                    }
                    @foreach (var lang in supportedLangs)
                    {
                        <!-- @lang.Name Fields -->
                        <div id="<EMAIL>" class="meeting-lang-content @(langIsFirst ? "" : "hidden")">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        @AdminRes["MeetingTitle"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"].ToString()){
                                        <span class="text-red-500">*</span>
                                    }
                                </label>
                                <input type="text" id="<EMAIL>" name="<EMAIL>"
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                       required="@(lang.Code == ViewData["DefaultLanguage"].ToString())" />
                            </div>
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["MeetingDescription"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                            <div>
                                <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    @AdminRes["MeetingAgenda"] (@lang.Name)
                                </label>
                                <textarea id="<EMAIL>" name="<EMAIL>" rows="4"
                                          class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                            </div>
                        </div>
                    </div>
                    langIsFirst = false;
                                        }
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="closeShareholderMeetingModal()"
                            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AdminRes["Cancel"]
                    </button>
                    <button type="submit" id="saveShareholderMeetingBtn"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        @AdminRes["Save"]
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>