@model MlSoft.Sites.Web.ViewModels.Admin.DashboardViewModel
@{
    ViewData["Title"] = AdminRes["DashboardTitle"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="space-y-6">
    <!-- 欢迎信息 -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 bg-primary-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">@AdminRes["WelcomeBack"]</dt>
                        <dd class="text-lg font-medium text-gray-900">@Model.UserName</dd>
                    </dl>
                </div>
                <div class="text-sm text-gray-500">
                    @AdminRes["Email"]: @Model.Email
                </div>
            </div>
        </div>
    </div>
</div>