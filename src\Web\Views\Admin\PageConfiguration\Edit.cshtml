@model MlSoft.Sites.Web.ViewModels.Admin.PageConfigurationEditViewModel

@{
    ViewData["Title"] = Model.IsNew ? AdminRes["CreateNewPage"] : AdminRes["EditPage"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";

    var formActionUrl = $"{Html.MultilingualUrl(Model.IsNew?"Create":"Edit", "PageConfiguration")}";
    if (!Model.IsNew)
    {
        formActionUrl += $"/{Model.Id}";
    }

}

<!-- Navigation below H1 -->
<div class="mb-4">
    <nav class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
        <a href="@Html.MultilingualUrl("", "PageConfiguration")" class="hover:text-primary-600 dark:hover:text-primary-400">
            <i class="fas fa-arrow-left mr-1"></i>@AdminRes["BackToPageList"]
        </a>
        <span>/</span>
        <span class="text-gray-900 dark:text-white">@(Model.IsNew? AdminRes["CreateNewPage"] : AdminRes["EditPage"])</span>
    </nav>
</div>

<div class="flex" style="height: calc(100vh - 200px);">
    <!-- 左侧面板：组件库 -->
    <div class="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">@AdminRes["ComponentLibrary"]</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">@AdminRes["DragComponentsToPage"]</p>
        </div>


        <!-- 组件分类 -->
        <div class="px-4 pb-4">
            @{
                var componentCategories = Model.AvailableComponents.GroupBy(c => c.Category ?? "General").ToDictionary(g => g.Key, g => g.ToList());
            }

            @foreach (var category in componentCategories)
            {
                <div class="mb-4">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white mt-4 mb-2">@category.Key</h3>
                    <div class="space-y-2">
                        @foreach (var component in category.Value)
                        {
                            <div class="component-item bg-gray-50 dark:bg-gray-700 p-3 rounded-lg cursor-move hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                                 data-component-id="@component.Id"
                                 data-component-name="@component.Name"
                                 data-component-category="@component.Category">
                                <div class="flex items-center">
                                    <i class="@(component.IconCssClass ?? "fas fa-cube") text-gray-600 dark:text-gray-400 mr-3"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">@component.Name</div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400">@component.Description</div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- 中央区域：页面预览和编辑 -->
    <div class="flex-1 flex flex-col">
        <!-- 顶部工具栏 - 重新设计为左右两列 -->
        <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-6">
            <form action="@formActionUrl" method="post" id="page-form">
                @Html.AntiForgeryToken()
                @if (!Model.IsNew)
                {
                    <input type="hidden" asp-for="Id" />
                }

                <div class="space-y-4">
                    <!-- 页面名称（多语言） -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @AdminRes["PageName"] *
                        </label>
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-2">
                            @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                            {
                                <div>
                                    <label for="<EMAIL>" class="block text-xs font-medium text-gray-600 mb-1">@lang.Emoji @lang.Name</label>
                                    <input name="Name[@lang.Code]" type="text"
                                           required
                                           placeholder="@AdminRes[$"PageName_{lang.Code.ToTitleCase()}"]"
                                           value="@(Model.Name?.GetValueOrDefault(lang.Code, "") ?? "")"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-primary-500 focus:border-primary-500">
                                    <span class="text-red-500 text-xs"></span>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-4">
                    <!-- 左侧 - 页面信息 -->
                    <div class="space-y-4">
                        <!-- PageKey和Route -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="PageKey">
                                    @AdminRes["PageKey"] * 
                                </label>
                                <input asp-for="PageKey" type="text"  required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-primary-500 focus:border-primary-500">
                                <span asp-validation-for="PageKey" class="text-red-500 text-xs"></span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" for="Route">
                                    @AdminRes["Route"] *
                                </label>
                                <input asp-for="Route" type="text" required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-primary-500 focus:border-primary-500">
                                <span asp-validation-for="Route" class="text-red-500 text-xs"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧 - 操作按钮 -->
                    <div class="flex flex-col justify-end">
                        <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                            <button type="submit" id="save-component-btn"
                                    class="flex items-center justify-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md text-sm font-medium transition-colors">
                                <i class="fas @(Model.IsNew ? "fa-plus" : "fa-save") mr-2"></i>@(Model.IsNew? AdminRes["Create"] : AdminRes["Save"])
                            </button>
                            <a href="@Html.MultilingualUrl("", "PageConfiguration")"
                               class="flex items-center justify-center px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md text-sm font-medium transition-colors">
                                @AdminRes["Cancel"]
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 隐藏字段存储组件数据 -->
                <input type="hidden" id="components-json" name="ComponentsJson" />
                <input asp-for="Status" type="hidden" value="@(Model.IsNew? PageStatus.Draft: Model.Status)" />
            </form>
        </div>

        <!-- 页面编辑区域 -->
        <div class="flex-1 bg-gray-50 dark:bg-gray-900 p-4 overflow-y-auto">
            <div class="max-w-6xl mx-auto">
                <!-- 页面画布 -->
                <div id="page-canvas" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 min-h-96">
                    <!-- 空状态提示 -->
                    <div id="empty-state" class="flex flex-col items-center justify-center py-16 text-gray-500 dark:text-gray-400 @(Model.Components?.Any() == true ? "hidden" : "")">
                        <i class="fas fa-mouse-pointer text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium mb-2">@AdminRes["EmptyPageCanvas"]</h3>
                        <p class="text-sm">@AdminRes["DragComponentsHere"]</p>
                    </div>

                    <!-- 组件列表 -->
                    <div id="component-list" class="@(Model.Components?.Any() != true ? "hidden" : "")">
                        @if (Model.Components?.Any() == true)
                        {
                            @foreach (var component in Model.Components.OrderBy(c => c.DisplayOrder))
                            {
                                var comInfo = Model.AvailableComponents.Where(x => x.Id == component.ComponentDefinitionId).FirstOrDefault();

                                var comName = "";
                                if(comInfo != null)
                                {
                                    comName = comInfo.Name;
                                }





                                <div class="page-component border-b border-gray-200 dark:border-gray-700 last:border-b-0 relative group"
                                     data-component-id="@component.ComponentDefinitionId"
                                     data-template="@component.TemplateKey"
                                     data-column-span="@component.ColumnSpan"
                                     data-display-order="@component.DisplayOrder"
                                     data-id-no="@component.IdNo"
                                     data-visible="@component.IsVisible.ToString().ToLower()">

                                    <!-- 组件工具栏 -->
                                    <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                                        <div class="flex items-center space-x-1 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 p-1">
                                            <button type="button" class="component-edit-btn p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-blue-600 dark:text-blue-400" title="@AdminRes["EditComponent"]">
                                                <i class="fas fa-edit text-sm"></i>
                                            </button>
                                            <button type="button" class="component-visibility-btn p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400" title="@AdminRes["ToggleVisibility"]">
                                                <i class="fas @(component.IsVisible ? "fa-eye" : "fa-eye-slash") text-sm"></i>
                                            </button>
                                            <button type="button" class="component-delete-btn p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-red-600 dark:text-red-400" title="@AdminRes["DeleteComponent"]">
                                                <i class="fas fa-trash text-sm"></i>
                                            </button>
                                            <div class="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
                                            <button type="button" class="component-drag-handle p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-400 cursor-move" title="@AdminRes["DragToReorder"]">
                                                <i class="fas fa-grip-vertical text-sm"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 组件预览 -->
                                    <div class="p-6 @(!component.IsVisible ? "opacity-50" : "")">
                                        <div class="flex items-center justify-between mb-2">
                                            <div class="flex items-center">
                                                <i class="fas fa-cube text-gray-400 mr-2"></i>
                                                <span class="text-sm font-medium text-gray-900 dark:text-white">@($"{comName}({component.ComponentDefinitionId})")</span>
                                                <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">@($"{component.ComponentName}-{component.TemplateKey}")</span>
                                                <span class="ml-2 text-xs text-blue-600 dark:text-blue-400">@($"{AdminRes["ColumnSpan"]}: {component.ColumnSpan}")</span>
                                            </div>
                                        </div>

                                        <!-- 组件参数预览 -->
                                        @if (!string.IsNullOrEmpty(component.ParametersJson) && component.ParametersJson != "{}")
                                        {
                                            <div class="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
                                                <details>
                                                    <summary class="cursor-pointer text-gray-600 dark:text-gray-400">@AdminRes["ComponentParameters"]</summary>
                                                    <pre class="mt-1 text-gray-800 dark:text-gray-200 overflow-x-auto">@component.ParametersJson</pre>
                                                </details>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧面板：属性编辑器 -->
    <div class="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">@AdminRes["ComponentProperties"]</h2>
        </div>

        <!-- 属性编辑面板内容 -->
        <div id="properties-panel" class="p-4">
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="fas fa-hand-pointer text-2xl mb-2"></i>
                <p class="text-sm">@AdminRes["SelectComponentToEdit"]</p>
            </div>
        </div>
    </div>
</div>

<!-- 组件编辑模态框 -->
<div id="component-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-black opacity-50"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <!-- 模态框内容将由JavaScript动态填充 -->
        </div>
    </div>
</div>

@section Scripts {
    <!-- 添加表单字段渲染器 -->
    <script src="~/js/admin/form-field-renderer.js" asp-append-version="true"></script>
    <script src="~/js/admin/form-field-renderer-extensions.js" asp-append-version="true"></script>
    <script src="~/js/admin/page-configuration.js" asp-append-version="true"></script>
    <script>
        // Dialog system is initialized globally in _AdminLayout.cshtml

        // 页面配置编辑器本地化资源
        window.PageConfigL10n = {
            // 现有资源...
            dropComponentHere: window.Resources?.Admin?.DropComponentHere || 'Drop Component Here',
            moveComponentHere: window.Resources?.Admin?.MoveComponentHere || 'Move Component Here',
            selectComponentToEdit: window.Resources?.Admin?.SelectComponentToEdit || 'Select Component To Edit',
            componentInfo: window.Resources?.Admin?.ComponentInfo || 'Component Info',
            componentType: window.Resources?.Admin?.ComponentType || 'Component Type',
            template: window.Resources?.Admin?.Template || 'Template',
            displayOrder: window.Resources?.Admin?.DisplayOrder || 'Display Order',
            componentVisible: window.Resources?.Admin?.ComponentVisible || 'Component Visible',
            componentParameters: window.Resources?.Admin?.ComponentParameters || 'Component Parameters',
            editComponent: window.Resources?.Admin?.EditComponent || 'Edit Component',
            deleteComponent: window.Resources?.Admin?.DeleteComponent || 'Delete Component',
            deleteComponentConfirm: window.Resources?.Admin?.DeleteComponentConfirm || 'Delete Component Confirm',
            componentParametersJson: window.Resources?.Admin?.ComponentParametersJson || 'Component Parameters JSON',
            visibilitySettings: window.Resources?.Admin?.VisibilitySettings || 'Visibility Settings',
            visible: window.Resources?.Admin?.Visible || 'Visible',
            hidden: window.Resources?.Admin?.Hidden || 'Hidden',
            cancel: window.Resources?.Admin?.Cancel || 'Cancel',
            save: window.Resources?.Admin?.Save || 'Save',
            quickActions: window.Resources?.Admin?.QuickActions || 'Quick Actions',

            // 新增表单相关资源
            basicInfo: window.Resources?.Shared?.FormGroups_BasicInfo || 'Basic Info',
            mediaContent: window.Resources?.Shared?.FormGroups_MediaContent || 'Media Content',
            layoutSettings: window.Resources?.Shared?.FormGroups_LayoutSettings || 'Layout Settings',
            resources: window.Resources?.Shared?.FormGroups_Resources || 'Resources',
            seoSettings: window.Resources?.Shared?.FormGroups_SEOSettings || 'SEO Settings',
            defaultGroup: window.Resources?.Shared?.FormGroups_Other || 'Other',

            // 文件上传
            selectFile: window.Resources?.Shared?.FileUpload_SelectFile || 'Select File',
            clickToUpload: window.Resources?.Shared?.FileUpload_ClickToUpload || 'Click To Upload',
            orDragDrop: window.Resources?.Shared?.FileUpload_OrDragDrop || 'Or Drag Drop',
            deleteFile: window.Resources?.Shared?.FileUpload_DeleteFile || 'Delete File',
            deleteFileConfirm: window.Resources?.Shared?.FileUpload_DeleteConfirm || 'Delete File Confirm',
            uploadSuccess: window.Resources?.Shared?.FileUpload_UploadSuccess || 'Upload Success',
            uploadError: window.Resources?.Shared?.FileUpload_UploadError || 'Upload Error',
            deleteSuccess: window.Resources?.Shared?.FileUpload_DeleteSuccess || 'Delete Success',
            deleteError: window.Resources?.Shared?.FileUpload_DeleteError || 'Delete Error',
            preview: window.Resources?.Shared?.FileUpload_Preview || 'Preview',
            allFiles: window.Resources?.Shared?.FileUpload_AllFiles || 'All Files',
            maxSize: window.Resources?.Shared?.FileUpload_MaxSize || 'Max Size',

            // 表单操作
            reset: window.Resources?.Shared?.Reset || 'Reset',
            resetConfirm: window.Resources?.Shared?.Form_ResetConfirm || 'Reset Confirm',
            saveSuccess: window.Resources?.Shared?.SaveSuccess || 'Save Success',
            saveError: window.Resources?.Shared?.SaveError || 'Save Error',
            resetSuccess: window.Resources?.Shared?.ResetSuccess || 'Reset Success',
            loadError: window.Resources?.Shared?.LoadError || 'Load Error',
            loadErrorDesc: window.Resources?.Shared?.LoadErrorDesc || 'Load Error Description',
            close: window.Resources?.Shared?.Close || 'Close',

            // 其他
            enterText: window.Resources?.Shared?.EnterText || 'Enter Text',
            clickComponentToShowProperties: window.Resources?.Admin?.ClickComponentToShowProperties || 'Click Component To Show Properties',
            
            // ColumnSpan 相关
            columnSpan: window.Resources?.Admin?.ColumnSpan || 'Column Span'
        };



        // 初始化页面配置编辑器
        document.addEventListener('DOMContentLoaded', function() {
            const editor = new PageConfigurationEditor({
                componentsData: @Html.Raw(Json.Serialize(Model.Components ?? new List<PageComponentViewModel>())),
                availableComponents: @Html.Raw(Json.Serialize(Model.AvailableComponents)),
                isNewPage: @Model.IsNew.ToString().ToLower()
            });

            window.pageEditor = editor;
        });
    </script>
}