@using MlSoft.Sites.Model.Entities.Enums
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<AdminResource> AdminLocalizer
@model MlSoft.Sites.Web.ViewModels.Admin.PageConfigurationListViewModel
@{
    ViewData["Title"] = AdminLocalizer["PageConfigurationTitle"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="p-4">
    <!-- 页面标题 -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            @AdminLocalizer["PageConfigurationList"]
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
            @AdminLocalizer["PageConfigurationListDescription"]
        </p>
    </div>

    <!-- 成功/错误消息 -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="mb-4 p-4 text-sm text-green-700 bg-green-100 border border-green-300 rounded-lg dark:bg-green-800 dark:text-green-300 dark:border-green-600" role="alert">
            @TempData["SuccessMessage"]
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="mb-4 p-4 text-sm text-red-700 bg-red-100 border border-red-300 rounded-lg dark:bg-red-800 dark:text-red-300 dark:border-red-600" role="alert">
            @TempData["ErrorMessage"]
        </div>
    }

    <!-- 工具栏 -->
    <div class="mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <!-- 筛选器 -->
            <div class="flex flex-col sm:flex-row gap-2">
                <form action="@Html.MultilingualUrl("", "PageConfiguration")" method="get" class="flex gap-2">
                    <input type="hidden" name="page" value="1" />
                    <select name="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-40 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">@AdminLocalizer["AllStatus"]</option>
                        <option value="@PageStatus.Published" selected="@(Model.FilterStatus == PageStatus.Published)">@AdminLocalizer["Published"]</option>
                        <option value="@PageStatus.Draft" selected="@(Model.FilterStatus == PageStatus.Draft)">@AdminLocalizer["Draft"]</option>
                        <option value="@PageStatus.Disabled" selected="@(Model.FilterStatus == PageStatus.Disabled)">@AdminLocalizer["Disabled"]</option>
                    </select>
                    <button type="submit" class="text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                        @AdminLocalizer["Filter"]
                    </button>
                </form>
            </div>

            <!-- 新增按钮 -->
            <div>
                <a href="@Html.MultilingualUrl("Create", "PageConfiguration")" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                    <i class="fas fa-plus mr-2"></i>
                    @AdminLocalizer["CreateNewPage"]
                </a>
            </div>
        </div>
    </div>

    <!-- 页面列表 -->
    @if (Model.Pages.Any())
    {
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <!-- 表格 -->
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">@AdminLocalizer["PageName"]</th>
                            <th scope="col" class="px-6 py-3">@AdminLocalizer["PageKey"]</th>
                            <th scope="col" class="px-6 py-3">@AdminLocalizer["Route"]</th>
                            <th scope="col" class="px-6 py-3">@AdminLocalizer["Status"]</th>
                            <th scope="col" class="px-6 py-3">@AdminLocalizer["ComponentCount"]</th>
                            <th scope="col" class="px-6 py-3">@AdminLocalizer["LastUpdated"]</th>
                            <th scope="col" class="px-6 py-3">@AdminLocalizer["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var pageInfo in Model.Pages)
                        {
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    @pageInfo.GetDisplayName(System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName)
                                </th>
                                <td class="px-6 py-4">
                                    <code class="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">@pageInfo.PageKey</code>
                                </td>
                                <td class="px-6 py-4">
                                    <code class="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">@pageInfo.Route</code>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @pageInfo.StatusCssClass">
                                        @pageInfo.GetStatusDisplayText(AdminLocalizer)
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-800 bg-gray-100 rounded dark:bg-gray-700 dark:text-gray-300">
                                        @pageInfo.ComponentCount @AdminLocalizer["ComponentsCount"]
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                                    <div class="text-sm">@pageInfo.UpdatedAt.ToLocalTime().ToString("yyyy-MM-dd HH:mm")</div>
                                    @if (!string.IsNullOrEmpty(pageInfo.UpdatedBy))
                                    {
                                        <div class="text-xs text-gray-400">@AdminLocalizer["By"] @pageInfo.UpdatedBy</div>
                                    }
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <!-- 编辑按钮 -->
                                        <a href="@Html.MultilingualUrl("Edit", "PageConfiguration", null, new { id = pageInfo.Id })"
                                           class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                                           title="@AdminLocalizer["EditPage"]">
                                            <i class="fas fa-edit text-2xl"></i>
                                        </a>

                                        <!-- 发布/取消发布按钮 -->
                                        @if (pageInfo.Status == PageStatus.Published)
                                        {
                                            <button type="button" 
                                                    class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300" 
                                                    title="@AdminLocalizer["Unpublish"]"
                                                    data-action="unpublish" 
                                                    data-page-id="@pageInfo.Id"
                                                    data-page-name="@pageInfo.Name.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), pageInfo.Name.Values.FirstOrDefault())">
                                                <i class="fas fa-eye-slash text-2xl"></i>
                                            </button>
                                        }
                                        else
                                        {
                                            <button type="button" 
                                                    class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300" 
                                                    title="@AdminLocalizer["Publish"]"
                                                    data-action="publish" 
                                                    data-page-id="@pageInfo.Id"
                                                    data-page-name="@pageInfo.Name.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), pageInfo.Name.Values.FirstOrDefault())">
                                                <i class="fas fa-eye text-2xl"></i>
                                            </button>
                                        }

                                        <!-- 删除按钮 -->
                                        <button type="button" 
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" 
                                                title="@AdminLocalizer["Delete"]"
                                                data-action="delete" 
                                                data-page-id="@pageInfo.Id"
                                                data-page-name="@pageInfo.Name.GetValueOrDefault(ViewData["CurrentLanguage"]?.ToString(), pageInfo.Name.Values.FirstOrDefault())">
                                            <i class="fas fa-trash text-2xl"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            @if (Model.TotalPages > 1)
            {
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            @AdminLocalizer["ShowingResults", (Model.CurrentPage - 1) * Model.PageSize + 1, Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount), Model.TotalCount]
                        </div>
                        
                        <nav aria-label="Page navigation">
                            <ul class="inline-flex items-center -space-x-px">
                                <!-- 上一页 -->
                                @if (Model.HasPreviousPage)
                                {
                                    <li>
                                        <a href="@Html.MultilingualUrl("", "PageConfiguration", null, new { page = Model.CurrentPage - 1, status = Model.FilterStatus })"
                                           class="block px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                }

                                <!-- 页码 -->
                                @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                {
                                    <li>
                                        @if (i == Model.CurrentPage)
                                        {
                                            <span class="px-3 py-2 text-primary-600 border border-gray-300 bg-primary-50 hover:bg-primary-100 hover:text-primary-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                                                @i
                                            </span>
                                        }
                                        else
                                        {
                                            <a href="@Html.MultilingualUrl("", "PageConfiguration", null, new { page = i, status = Model.FilterStatus })"
                                               class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                                @i
                                            </a>
                                        }
                                    </li>
                                }

                                <!-- 下一页 -->
                                @if (Model.HasNextPage)
                                {
                                    <li>
                                        <a href="@Html.MultilingualUrl("", "PageConfiguration", null, new { page = Model.CurrentPage + 1, status = Model.FilterStatus })"
                                           class="block px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </nav>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <!-- 空状态 -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-12">
            <div class="text-center">
                <div class="mx-auto h-12 w-12 text-gray-400 mb-4">
                    <i class="fas fa-file-alt text-4xl"></i>
                </div>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">@AdminLocalizer["NoPages"]</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">@AdminLocalizer["NoPagesDescription"]</p>
                <div class="mt-6">
                    <a asp-action="Create" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800">
                        <i class="fas fa-plus mr-2"></i>
                        @AdminLocalizer["CreateNewPage"]
                    </a>
                </div>
            </div>
        </div>
    }
</div>

<!-- 防伪令牌表单 -->
<form style="display: none;">
    @Html.AntiForgeryToken()
</form>

@section Scripts {
    <script>
        // Dialog system is initialized globally in _AdminLayout.cshtml

        // 处理页面操作按钮
        document.addEventListener('DOMContentLoaded', function() {
            // 获取防伪令牌
            function getAntiForgeryToken() {
                const token = document.querySelector('input[name="__RequestVerificationToken"]');
                return token ? token.value : '';
            }

            // 处理Ajax操作
            async function handlePageAction(action, pageId, pageName) {
                let message, url;
                
                switch(action) {
                    case 'unpublish':
                        message = window.Resources?.Admin?.ConfirmUnpublish || 'Confirm Unpublish';
                        url = `@Html.MultilingualUrl("", "PageConfiguration")/Unpublish/${pageId}`;
                        break;
                    case 'publish':
                        message = window.Resources?.Admin?.ConfirmPublish || 'Confirm Publish';
                        url = `@Html.MultilingualUrl("", "PageConfiguration")/Publish/${pageId}`;
                        break;
                    case 'delete':
                        message = window.Resources?.Admin?.DeleteConfirm || 'Delete Confirm';
                        url = `@Html.MultilingualUrl("", "PageConfiguration")/Delete/${pageId}`;
                        break;
                    default:
                        return;
                }
                
                try {
                    const result = await Dialog.confirm(message.replace('{0}', pageName));
                    if (result) {
                        // 显示加载状态
                        const actionButton = document.querySelector(`[data-action="${action}"][data-page-id="${pageId}"]`);
                        const originalHtml = actionButton.innerHTML;
                        actionButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                        actionButton.disabled = true;
                        
                        // 执行Ajax请求
                        const formData = new FormData();
                        formData.append('__RequestVerificationToken', getAntiForgeryToken());
                        
                        const response = await fetch(url, {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: formData
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            Dialog.notify(result.message, 'success');
                            // 刷新页面以更新状态
                            setTimeout(() => {
                               window.location.reload();
                            }, 1000);
                        } else {
                            Dialog.error(result.message);
                            // 恢复按钮状态
                            actionButton.innerHTML = originalHtml;
                            actionButton.disabled = false;
                        }
                    }
                } catch (error) {
                    console.error('Operation failed:', error);
                    Dialog.error(window.Resources?.Admin?.SystemError || 'System Error');
                }
            }

            // 绑定所有操作按钮的事件
            document.querySelectorAll('[data-action]').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.getAttribute('data-action');
                    const pageId = this.getAttribute('data-page-id');
                    const pageName = this.getAttribute('data-page-name') || 'this page';
                    handlePageAction(action, pageId, pageName);
                });
            });
        });
    </script>
}