@model MlSoft.Sites.Web.ViewModels.Admin.PageManageEditViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@using System.Text.Json
@using MlSoft.Sites.Web.Services
@inject IViewLocalizer Localizer
@inject IStringLocalizer<MlSoft.Sites.Web.Resources.AdminResource> AdminLocalizer
@inject IStringLocalizer<MlSoft.Sites.Web.Resources.SharedResource> SharedRes

@inject IComponentConfigService ComponentConfigService

@{
    ViewData["Title"] = AdminLocalizer["EditPageContent"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";

    var currentLanguage = ViewData["CurrentLanguage"]?.ToString();

    var allComponents = await ComponentConfigService.GetAllComponents();
}

<!-- Navigation below H1 -->
<div class="mb-4">
    <nav class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
        <a href="@Html.MultilingualUrl("", "PageManage")" class="hover:text-primary-600 dark:hover:text-primary-400">
            <i class="fas fa-arrow-left mr-1"></i>@AdminRes["BackToPageList"]
        </a>
        <span>/</span>
        <span class="text-gray-900 dark:text-white">@AdminRes["EditPageContent"]</span>
    </nav>
</div>


<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        @AdminLocalizer["EditPageContent"]
                    </h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">
                        @(Model.PageName.TryGetValue(ViewData["CurrentLanguage"]?.ToString(), out var localeName) ? localeName  : Model.PageKey)
                    </p>
                </div>
            </div>
        </div>

        @if (!Model.ComponentsData.Any())
        {
            <div class="bg-warning-50 dark:bg-warning-900 border border-warning-200 dark:border-warning-700 rounded-lg p-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            @AdminLocalizer["NoComponents"]
                        </h3>
                        <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                            @AdminLocalizer["NoComponentsConfigured"]
                        </p>
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- 主要编辑区域 -->
            <div class="flex gap-6">
                <!-- 左侧：组件列表 -->
                <div class="w-1/3 min-w-0">
                    <form asp-action="Edit" asp-route-pageConfigurationId="@Model.PageConfigurationId" method="post" id="pageContentForm">
                        @Html.AntiForgeryToken()
                        @if (!string.IsNullOrEmpty(Model.Id))
                        {
                            <input type="hidden" asp-for="Id" />
                        }
                        <input type="hidden" asp-for="PageConfigurationId" />
                        <input type="hidden" asp-for="PageKey" />
                        <input type="hidden" asp-for="Status" />
                        <input type="hidden" asp-for="Version" />
                        <input type="hidden" asp-for="IdNo" />

                        <!-- 组件列表区域 -->
                        <div class="space-y-4" id="components-container">
                            @for (int i = 0; i < Model.ComponentsData.Count; i++)
                            {
                                var component = Model.ComponentsData[i];
                                var componentId = $"component_{i}";

                                var componentVariantInfo = component.AvailableTemplates?.Where(t => t.Id == component.TemplateKey).FirstOrDefault();

                                var componentName = component.ComponentDefinitionId;
                                var componentDesc = component.TemplateKey;


                                var componentConfig = allComponents.FirstOrDefault(c => c.Id == component.ComponentDefinitionId);
                                if(componentConfig != null)
                                {
                                    componentName = componentConfig.Names.ContainsKey(currentLanguage) ? componentConfig.Names[currentLanguage] : component.ComponentDefinitionId;
                                }


                                var variantName =  component.TemplateKey;


                                if(componentVariantInfo != null)
                                {
                                    variantName = componentVariantInfo.Names.ContainsKey(currentLanguage) ? componentVariantInfo.Names[currentLanguage] : component.ComponentDefinitionId;
                                    componentDesc = componentVariantInfo.Descriptions.ContainsKey(currentLanguage) ? componentVariantInfo.Descriptions[currentLanguage] : component.TemplateKey;

                                }

                                
                                <div class="page-component relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md hover:-translate-y-0.5 transition-all duration-200 hover:border-primary-300 dark:hover:border-primary-600"
                                     data-component-id="@component.ComponentDefinitionId"
                                     data-template="@component.TemplateKey"
                                     data-parameters='@component.DataJson'
                                     data-display-order="@component.DisplayOrder"
                                     data-id-no="@component.IdNo"
                                     data-visible="@component.IsVisible.ToString().ToLower()"
                                     id="component-card-@i">
                                    <input type="hidden" name="ComponentsData[@i].ComponentDefinitionId" value="@component.ComponentDefinitionId" />
                                    <input type="hidden" name="ComponentsData[@i].TemplateKey" value="@component.TemplateKey" />
                                    <input type="hidden" name="ComponentsData[@i].DisplayOrder" value="@component.DisplayOrder" />
                                    <input type="hidden" name="ComponentsData[@i].IsVisible" value="@component.IsVisible" />
                                    <input type="hidden" name="ComponentsData[@i].IdNo" value="@component.IdNo" />
                                    <textarea name="ComponentsData[@i].DataJson" 
                                             id="dataJson_@componentId" 
                                             class="hidden">@component.DataJson</textarea>
                                    
                                    <!-- 组件标题栏 -->
                                    <div class="flex items-center justify-between p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="flex-shrink-0">
                                                <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-puzzle-piece text-primary-600 dark:text-primary-400"></i>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <div class="space-x-2">
                                                    <div class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                                                      @componentName - @variantName
                                                    </div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        @componentDesc
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <!-- 可见性切换 -->
                                            <label class="flex items-center" onclick="event.stopPropagation()">
                                                <input type="checkbox" @(component.IsVisible ? "checked" : "") 
                                                       class="sr-only peer"
                                                       onchange="toggleComponentVisibility(@i, this.checked)">
                                                <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">@AdminLocalizer["Visible"]</span>
                                            </label>
                                            <!-- 编辑指示器 -->
                                            <div class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600 transition-colors duration-200" id="edit-indicator-@i"></div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- 操作按钮 -->
                        <div class="mt-6 items-center justify-between bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 px-6 py-4">
                            <div class="flex items-center space-x-4">
                                <button type="button"  onclick="saveComponentForm()"
                                        class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                                    @AdminLocalizer["SavePageContent"]
                                </button>
                                @if (Model.Status == PageContentStatus.Draft && !string.IsNullOrEmpty(Model.Id))
                                {
                                    <button type="button" onclick="publishContent()" 
                                            class="bg-success-600 hover:bg-success-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                                        @AdminLocalizer["PublishPageContent"]
                                    </button>
                                }
                            </div>
                            
                            <div class="text-sm text-gray-500 dark:text-gray-400 mt-4  space-x-4">
                                @if (Model.UpdatedAt != default)
                                {
                                    <span>@AdminLocalizer["LastUpdated"]@Model.UpdatedAt.ToLocalTime().ToString("yyyy-MM-dd HH:mm")</span>
                                    @if (!string.IsNullOrEmpty(Model.UpdatedBy))
                                    {
                                        <span class="ml-2">@AdminLocalizer["By"] @Model.UpdatedBy</span>
                                    }
                                }
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- 右侧：属性编辑面板 -->
                <div class="w-2/3 flex-shrink-0">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col min-h-screen" style="min-height: calc(100vh - 300px);" id="properties-panel">
                        <!-- 属性面板内容将由JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        }
    </div>
</div>


@section Scripts {
<script src="~/js/admin/form-field-renderer.js" asp-append-version="true"></script>
<script src="~/js/admin/form-field-renderer-extensions.js" asp-append-version="true"></script>
<script>
// 页面数据和配置
const pageData = {
    supportedLanguages: @Html.Raw(JsonSerializer.Serialize(ViewData["SupportedLanguages"])),
    components: @Html.Raw(JsonSerializer.Serialize(Model.ComponentsData.Select(c => new { 
        ComponentDefinitionId = c.ComponentDefinitionId, 
        MultilingualFields = c.MultilingualFields,
        DataJson = c.DataJson
    }))),
    pageConfigurationId: '@Model.PageConfigurationId'
};

// 页面配置编辑器本地化资源
window.PageConfigL10n = {
    selectComponentToEdit: window.Resources?.Admin?.SelectComponentToEdit || 'Select Component To Edit',
    clickComponentToShowProperties: window.Resources?.Admin?.ClickComponentToShowProperties || 'Click Component To Show Properties',
    componentType: window.Resources?.Admin?.ComponentType || 'Component Type',
    template: window.Resources?.Admin?.Template || 'Template',
    displayOrder: window.Resources?.Admin?.DisplayOrder || 'Display Order',
    visible: window.Resources?.Admin?.Visible || 'Visible',
    save: window.Resources?.Admin?.SavePageContent || 'Save',
    cancel: window.Resources?.Admin?.Cancel || 'Cancel',
    reset: window.Resources?.Admin?.Reset || 'Reset',
    resetConfirm: window.Resources?.Admin?.ResetConfirm || 'Reset Confirm',
    saveSuccess: window.Resources?.Admin?.SaveSuccess || 'Save Success',
    saveError: window.Resources?.Admin?.SaveError || 'Save Error',
    resetSuccess: window.Resources?.Admin?.ResetSuccess || 'Reset Success',
    loadError: window.Resources?.Admin?.LoadError || 'Load Error',
    loadErrorDesc: window.Resources?.Admin?.LoadErrorDesc || 'Load Error Description',
    close: window.Resources?.Admin?.Close || 'Close',
    dropComponentHere: window.Resources?.Admin?.DropComponentHere || 'Drop Component Here',
    moveComponentHere: window.Resources?.Admin?.MoveComponentHere || 'Move Component Here',

    // 表单分组
    basicInfo: window.Resources?.Shared?.FormGroups_BasicInfo || 'Basic Info',
    mediaContent: window.Resources?.Shared?.FormGroups_MediaContent || 'Media Content',
    layoutSettings: window.Resources?.Shared?.FormGroups_LayoutSettings || 'Layout Settings',
    resources: window.Resources?.Shared?.FormGroups_Resources || 'Resources',
    seoSettings: window.Resources?.Shared?.FormGroups_SEOSettings || 'SEO Settings',

    // 文件上传
    selectFile: window.Resources?.Shared?.FileUpload_SelectFile || 'Select File',
    clickToUpload: window.Resources?.Shared?.FileUpload_ClickToUpload || 'Click To Upload',
    orDragDrop: window.Resources?.Shared?.FileUpload_OrDragDrop || 'Or Drag Drop',
    deleteFile: window.Resources?.Shared?.FileUpload_DeleteFile || 'Delete File',
    deleteFileConfirm: window.Resources?.Shared?.FileUpload_DeleteConfirm || 'Delete File Confirm',
    uploadSuccess: window.Resources?.Shared?.FileUpload_UploadSuccess || 'Upload Success',
    uploadError: window.Resources?.Shared?.FileUpload_UploadError || 'Upload Error',
    deleteSuccess: window.Resources?.Shared?.FileUpload_DeleteSuccess || 'Delete Success',
    deleteError: window.Resources?.Shared?.FileUpload_DeleteError || 'Delete Error',
    preview: window.Resources?.Shared?.FileUpload_Preview || 'Preview',
    allFiles: window.Resources?.Shared?.FileUpload_AllFiles || 'All Files',
    maxSize: window.Resources?.Shared?.FileUpload_MaxSize || 'Max Size',

    // 其他
    defaultGroup: window.Resources?.Shared?.FormGroups_Other || 'Other',
    enterText: window.Resources?.Shared?.EnterText || 'Enter Text'
};

// 支持的语言配置
@*window.SupportedLanguages = @Html.Raw(JsonSerializer.Serialize(
    ((SupportedLanguage[])ViewData["SupportedLanguages"]).Select(lang => new {
        code = lang.Code,
        name = lang.Name,
        emoji = GetLanguageEmoji(lang.Code)
    }).ToArray()
));*@

@{
    string GetLanguageEmoji(string code) => code switch {
        "zh" => "🇨🇳",
        "en" => "🇺🇸",
        "ja" => "🇯🇵",
        _ => "🌐"
    };
}

// Dialog system is initialized globally in _AdminLayout.cshtml

// 页面编辑器实例
let pageEditor = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePageEditor();
});

// 初始化页面编辑器
function initializePageEditor() {
    const propertiesPanel = document.getElementById('properties-panel');
    if (!propertiesPanel) return;
    
    // 创建表单渲染器
    pageEditor = {
        formRenderer: new FormFieldRenderer(propertiesPanel, window.PageConfigL10n),
        currentEditingComponent: null,
        currentVariantInfo: null
    };
    
    // 显示空状态
    showEmptyPropertiesState();
    
    // 初始化组件点击事件
    initializeComponentSelection();
}

// 初始化组件选择功能
function initializeComponentSelection() {
    const components = document.querySelectorAll('.page-component');
    components.forEach((component, index) => {
        component.addEventListener('click', (e) => {
            e.stopPropagation();
            selectComponent(component, index);
        });
    });
}

// 选择组件
function selectComponent(componentElement, componentIndex) {
    // 移除其他组件的选中状态
    document.querySelectorAll('[id^="component-card-"]').forEach((el, index) => {
        el.classList.remove('border-primary-500', 'dark:border-primary-400', 'ring-2', 'ring-primary-200', 'dark:ring-primary-800');
        el.classList.add('border-gray-200', 'dark:border-gray-700');
        
        const indicator = document.getElementById(`edit-indicator-${index}`);
        if (indicator) {
            indicator.classList.remove('bg-primary-500');
            indicator.classList.add('bg-gray-300', 'dark:bg-gray-600');
        }
    });
    
    // 添加当前组件的选中状态
    componentElement.classList.remove('border-gray-200', 'dark:border-gray-700');
    componentElement.classList.add('border-primary-500', 'dark:border-primary-400', 'ring-2', 'ring-primary-200', 'dark:ring-primary-800');
    
    const indicator = document.getElementById(`edit-indicator-${componentIndex}`);
    if (indicator) {
        indicator.classList.remove('bg-gray-300', 'dark:bg-gray-600');
        indicator.classList.add('bg-primary-500');
    }
    
    // 显示属性编辑面板
    showComponentProperties(componentElement, componentIndex);
}

// 显示组件属性
async function showComponentProperties(componentElement, componentIndex) {
    try {
        const componentId = componentElement.dataset.componentId;
        const templateKey = componentElement.dataset.template || 'Default';
        
        // 获取组件的变体信息（包含formFields）
        
        var getUrl = `${getLanguagePrefix()}/api/Components/${componentId}/variants/${templateKey}`;

        const response = await fetch(getUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: Failed to load component variant`);
        }
        
        const variantInfo = await response.json();
        
        if (variantInfo && variantInfo.formFields && variantInfo.formFields.length > 0) {
            renderComponentForm(variantInfo, componentElement, componentIndex);
        } else {
            renderSimpleProperties(componentId, componentElement, componentIndex);
        }
        
    } catch (error) {
        console.error('Error loading component properties:', error);
        showErrorState();
    }
}

// 渲染组件表单
function renderComponentForm(variantInfo, componentElement, componentIndex) {
    const propertiesPanel = document.getElementById('properties-panel');
    
    // 创建面板头部
    // const header = document.createElement('div');
    // header.className = 'p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800';
    // header.innerHTML = `
    //     <div class="flex items-center justify-between">
    //         <div>
    //             <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
    //                 ${variantInfo.names?.zh || componentElement.dataset.componentId}
    //             </h3>
    //             <p class="text-sm text-gray-600 dark:text-gray-400">
    //                 ${variantInfo.descriptions?.zh || '组件属性配置'}
    //             </p>
    //         </div>
    //         <button type="button" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
    //                 onclick="showEmptyPropertiesState()">
    //             <i class="fas fa-times"></i>
    //         </button>
    //     </div>
    // `;
    
    // 创建表单容器
    const formContainer = document.createElement('div');
    formContainer.className = 'flex-1 overflow-y-auto';
    formContainer.id = 'component-form-container';
    
    // 创建操作按钮区域
    const footer = document.createElement('div');
    footer.className = 'p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50';
    footer.innerHTML = `
        <div class="flex justify-end space-x-3">
            <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 border border-transparent rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700"
                    onclick="saveComponentForm()">
                <i class="fas fa-save text-2xl mr-2"></i>${window.PageConfigL10n.save || '保存'}
            </button>
        </div>
    `;
    
    // 清空并重建面板
    propertiesPanel.innerHTML = '';
 //   propertiesPanel.appendChild(header);
    propertiesPanel.appendChild(formContainer);
    propertiesPanel.appendChild(footer);
    
    // 渲染表单字段
    pageEditor.formRenderer.container = formContainer;
    pageEditor.formRenderer.render(variantInfo.formFields);
    
    // 加载现有数据
    loadComponentData(componentElement, componentIndex);
    
    // 存储当前编辑的组件引用
    pageEditor.currentEditingComponent = componentElement;
    pageEditor.currentVariantInfo = variantInfo;
    pageEditor.currentComponentIndex = componentIndex;
}

// 渲染简单属性（回退方案）
function renderSimpleProperties(componentId, componentElement, componentIndex) {
    const propertiesPanel = document.getElementById('properties-panel');
    propertiesPanel.innerHTML = `
        <div class="p-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                ${componentId}
            </h3>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ${window.PageConfigL10n.componentType || '组件类型'}
                    </label>
                    <input type="text" value="${componentId}" readonly
                           class="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-600 text-sm">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ${window.PageConfigL10n.template || '模板'}
                    </label>
                    <input type="text" value="${componentElement.dataset.template || 'Default'}" readonly
                           class="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-600 text-sm">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ${window.PageConfigL10n.displayOrder || '显示顺序'}
                    </label>
                    <input type="number" value="${componentElement.dataset.displayOrder || 0}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" ${componentElement.dataset.visible === 'true' ? 'checked' : ''}
                               class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        <span class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                            ${window.PageConfigL10n.visible || '显示'}
                        </span>
                    </label>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700"
                        onclick="showEmptyPropertiesState()">
                    ${window.PageConfigL10n.cancel || '取消'}
                </button>
                <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700"
                        onclick="saveSimpleProperties()">
                    ${window.PageConfigL10n.save || '保存'}
                </button>
            </div>
        </div>
    `;
}

// 显示空状态
function showEmptyPropertiesState() {
    const propertiesPanel = document.getElementById('properties-panel');
    propertiesPanel.innerHTML = `
        <div class="text-center py-12 text-gray-500 dark:text-gray-400">
            <i class="fas fa-hand-pointer text-3xl mb-4 text-gray-300"></i>
            <h3 class="text-lg font-medium mb-2">${window.PageConfigL10n.selectComponentToEdit || '选择组件进行编辑'}</h3>
            <p class="text-sm">${window.PageConfigL10n.clickComponentToShowProperties || '点击左侧组件显示属性编辑面板'}</p>
        </div>
    `;
    
    // 清除选中状态
    document.querySelectorAll('[id^="component-card-"]').forEach((el, index) => {
        el.classList.remove('border-primary-500', 'dark:border-primary-400', 'ring-2', 'ring-primary-200', 'dark:ring-primary-800');
        el.classList.add('border-gray-200', 'dark:border-gray-700');
        
        const indicator = document.getElementById(`edit-indicator-${index}`);
        if (indicator) {
            indicator.classList.remove('bg-primary-500');
            indicator.classList.add('bg-gray-300', 'dark:bg-gray-600');
        }
    });
    
    pageEditor.currentEditingComponent = null;
    pageEditor.currentVariantInfo = null;
    pageEditor.currentComponentIndex = null;
}

// 显示错误状态
function showErrorState() {
    const propertiesPanel = document.getElementById('properties-panel');
    propertiesPanel.innerHTML = `
        <div class="text-center py-12 text-red-500">
            <i class="fas fa-exclamation-triangle text-3xl mb-4"></i>
            <h3 class="text-lg font-medium mb-2">${window.PageConfigL10n.loadError || '加载失败'}</h3>
            <p class="text-sm">${window.PageConfigL10n.loadErrorDesc || '无法加载组件属性，请重试'}</p>
            <button type="button" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                    onclick="showEmptyPropertiesState()">
                ${window.PageConfigL10n.close || '关闭'}
            </button>
        </div>
    `;
}

// 加载组件数据
function loadComponentData(componentElement, componentIndex) {
    if (!pageEditor.formRenderer) return;
    
    try {
        const parametersJson = componentElement.dataset.parameters || '{}';
        const parameters = JSON.parse(parametersJson);
        
        pageEditor.formRenderer.setFormData(parameters);
    } catch (error) {
        console.error('Error loading component data:', error);
    }
}

// 保存组件表单
function saveComponentForm() {
    try {
        // 如果有活动的表单编辑器，保存表单数据
        if (pageEditor.formRenderer && pageEditor.currentEditingComponent && pageEditor.currentComponentIndex !== null) {
            const formData = pageEditor.formRenderer.getFormData();
            const componentIndex = pageEditor.currentComponentIndex;

            // 更新组件数据
            pageEditor.currentEditingComponent.dataset.parameters = JSON.stringify(formData);

            // 更新隐藏的表单字段
            const dataTextarea = document.getElementById(`dataJson_component_${componentIndex}`);
            if (dataTextarea) {
                dataTextarea.value = JSON.stringify(formData, null, 2);
            }
        }

        // 同步所有组件数据到表单（包括可见性状态）
        updateAllComponentData();

        // 提交整个表单到服务器保存
        const pageContentForm = document.getElementById('pageContentForm');
        if (pageContentForm) {
            // 同步TinyMCE内容
            if (typeof tinymce !== 'undefined') {
                tinymce.triggerSave();
            }

            // 显示保存中状态
            const saveButton = document.querySelector('#properties-panel button[onclick="saveComponentForm()"]');
            let originalText = '';
            if (saveButton) {
                originalText = saveButton.innerHTML;
                saveButton.disabled = true;
                saveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>保存中...';
            }

            // 创建表单数据
            const formDataToSubmit = new FormData(pageContentForm);

            // AJAX提交表单
            fetch(pageContentForm.action, {
                method: 'POST',
                body: formDataToSubmit,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.json(); // 解析JSON响应
                }
                throw new Error('保存失败');
            })
            .then(data => {
                // 恢复按钮状态
                if (saveButton) {
                    saveButton.disabled = false;
                    saveButton.innerHTML = originalText;
                }

                if (data.success) {
                    Dialog.notify(data.message || window.PageConfigL10n.saveSuccess || '保存成功', 'success');
                    setTimeout(function(){
                        location.reload();
                    }, 500);
                } else {
                    Dialog.error(data.message || window.PageConfigL10n.saveError || '保存失败');
                }
            })
            .catch(error => {
                console.error('Error saving component:', error);

                // 恢复按钮状态
                if (saveButton) {
                    saveButton.disabled = false;
                    saveButton.innerHTML = originalText;
                }

                Dialog.error(window.PageConfigL10n.saveError || '保存失败');
            });
        }

    } catch (error) {
        console.error('Error saving component form:', error);
        Dialog.error(window.PageConfigL10n.saveError || '保存失败');
    }
}

// 同步所有组件数据到表单字段
function updateAllComponentData() {
    pageData.components.forEach((componentData, index) => {
        const componentId = `component_${index}`;
        const dataTextarea = document.getElementById(`dataJson_${componentId}`);

        if (dataTextarea) {
            // 如果当前编辑的是这个组件，使用最新的表单数据
            if (pageEditor.currentComponentIndex === index && pageEditor.formRenderer) {
                const formData = pageEditor.formRenderer.getFormData();
                dataTextarea.value = JSON.stringify(formData, null, 2);
            }
            // 否则保持原有数据不变
        }

        // 同步可见性状态 - 查找组件卡片中的visibility checkbox
        const componentCard = document.getElementById(`component-card-${index}`);
        if (componentCard) {
            const visibilityCheckbox = componentCard.querySelector(`input[type="checkbox"]`);
            const visibilityHidden = document.querySelector(`input[name="ComponentsData[${index}].IsVisible"][type="hidden"]`);

            if (visibilityCheckbox && visibilityHidden) {
                // 将checkbox的状态同步到hidden field
                visibilityHidden.value = visibilityCheckbox.checked;
            }
        }
    });
}

// 重置组件表单
function resetComponentForm() {
    if (!pageEditor.currentEditingComponent || pageEditor.currentComponentIndex === null) return;
    
    Dialog.confirm(window.PageConfigL10n.resetConfirm || '确定要重置所有更改吗？').then(result => {
        if (result) {
            loadComponentData(pageEditor.currentEditingComponent, pageEditor.currentComponentIndex);
            Dialog.info(window.PageConfigL10n.resetSuccess || '已重置');
        }
    });
}

// 保存简单属性
function saveSimpleProperties() {
    Dialog.notify(window.PageConfigL10n.saveSuccess || '保存成功', 'success');
}

// 暴露全局函数
window.pageEditor = pageEditor;
window.selectComponent = selectComponent;
window.showEmptyPropertiesState = showEmptyPropertiesState;
window.saveComponentForm = saveComponentForm;
window.resetComponentForm = resetComponentForm;
window.saveSimpleProperties = saveSimpleProperties;

// 初始化组件表单
function initializeComponentForms() {
    pageData.components.forEach((componentData, index) => {
        const componentId = `component_${index}`;
        const formContainer = document.getElementById(`form_${componentId}`);
        
        if (formContainer) {
            generateComponentForm(formContainer, componentData, index);
        }
    });
}

// 生成组件表单
async function generateComponentForm(container, componentData, componentIndex) {
    try {
        console.log('Generating form for component:', componentIndex, componentData);
        
        // 获取组件的表单字段配置
        const formFieldsResponse = await fetch(`@Html.MultilingualUrl("GetComponentFormFields", "PageManage")/${componentData.ComponentDefinitionId}/variants/${componentData.TemplateKey}`);
        if (!formFieldsResponse.ok) {
            throw new Error('Failed to load form fields configuration');
        }
        
        const formFieldsData = await formFieldsResponse.json();
        if (!formFieldsData.success) {
            throw new Error(formFieldsData.message || 'Failed to load form fields');
        }
        
        const formFields = formFieldsData.formFields || [];
        const existingData = JSON.parse(componentData.DataJson || '{}');
        
        console.log('Form fields config:', formFields);
        console.log('Existing data:', existingData);
        
        container.innerHTML = '';
        
        // 如果没有表单字段配置，显示提示
        if (formFields.length === 0) {
            container.innerHTML = `
                <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                尚未配置表单字段
                            </h3>
                            <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                                该组件的 variants.json 文件中尚未配置 formFields，请参考设计文档进行配置。
                            </p>
                        </div>
                    </div>
                </div>
            `;
            return;
        }
        
        // 基于配置生成表单字段
        generateFormFieldsFromConfig(container, formFields, existingData, componentIndex);
        
        console.log('Form generated successfully');
    } catch (error) {
        console.error('Error generating component form:', error);
        container.innerHTML = `
            <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            表单生成失败
                        </h3>
                        <p class="mt-1 text-sm text-red-700 dark:text-red-300">
                            ${error.message}
                        </p>
                    </div>
                </div>
            </div>
        `;
    }
}

// 基于配置生成表单字段
function generateFormFieldsFromConfig(container, formFields, existingData, componentIndex) {
    formFields.forEach(fieldConfig => {
        const fieldValue = existingData[fieldConfig.name] || getDefaultFieldValue(fieldConfig);
        
        const fieldContainer = document.createElement('div');
        fieldContainer.className = 'form-field mb-6';
        
        if (fieldConfig.multilingual) {
            // 多语言字段
            fieldContainer.innerHTML = createMultilingualFieldFromConfig(fieldConfig, fieldValue, componentIndex);
        } else {
            // 普通字段
            fieldContainer.innerHTML = createSimpleFieldFromConfig(fieldConfig, fieldValue, componentIndex);
        }
        
        container.appendChild(fieldContainer);
    });
    
    // 初始化多语言标签页
    initializeLanguageTabs(container);
    
    // 初始化TinyMCE编辑器
    initializeTinyMCE(container);
}

// 获取字段默认值
function getDefaultFieldValue(fieldConfig) {
    if (fieldConfig.defaultValue !== undefined && fieldConfig.defaultValue !== null) {
        return fieldConfig.defaultValue;
    }
    
    if (fieldConfig.multilingual) {
        const multilingualValue = {};
        pageData.supportedLanguages.forEach(lang => {
            multilingualValue[lang.Code] = '';
        });
        return multilingualValue;
    }
    
    switch (fieldConfig.type) {
        case 'checkbox':
            return false;
        case 'number':
            return 0;
        default:
            return '';
    }
}

// 基于配置创建多语言字段
function createMultilingualFieldFromConfig(fieldConfig, fieldValue, componentIndex) {
    const fieldId = `field_${componentIndex}_${fieldConfig.name}`;
    const currentLang = pageData.supportedLanguages[0]?.Code || 'zh';
    const label = getCurrentLabel(fieldConfig.labels);
    
    return `
        <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ${label} ${fieldConfig.required ? '<span class="text-red-500">*</span>' : ''}
                <span class="text-primary-600 text-xs ml-1">(@AdminLocalizer["MultilingualContent"])</span>
            </label>
            <div class="multilingual-field" data-field="${fieldConfig.name}">
                <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        ${pageData.supportedLanguages.map((lang, index) => `
                            <button type="button" class="language-tab ${index === 0 ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} dark:text-gray-400 dark:hover:text-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                    data-lang="${lang.Code}" onclick="switchLanguageTab(this, '${fieldId}')">
                                ${lang.Name || lang.Code.toUpperCase()}
                            </button>
                        `).join('')}
                    </nav>
                </div>
                <div class="language-contents">
                    ${pageData.supportedLanguages.map((lang, index) => `
                        <div class="language-content ${index === 0 ? 'block' : 'hidden'}" data-lang="${lang.Code}">
                            ${createInputElementFromConfig(fieldConfig, fieldValue[lang.Code] || '', `${fieldId}_${lang.Code}`, `data-lang="${lang.Code}"`)}
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

// 基于配置创建简单字段
function createSimpleFieldFromConfig(fieldConfig, fieldValue, componentIndex) {
    const fieldId = `field_${componentIndex}_${fieldConfig.name}`;
    const label = getCurrentLabel(fieldConfig.labels);
    
    return `
        <div class="form-group">
            <label for="${fieldId}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ${label} ${fieldConfig.required ? '<span class="text-red-500">*</span>' : ''}
            </label>
            ${createInputElementFromConfig(fieldConfig, fieldValue, fieldId)}
        </div>
    `;
}

// 基于配置创建输入元素
function createInputElementFromConfig(fieldConfig, fieldValue, fieldId, extraAttrs = '') {
    const commonClasses = 'w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500';
    const placeholder = getCurrentPlaceholder(fieldConfig.placeholder);
    const validation = fieldConfig.validation || {};
    
    switch (fieldConfig.type) {
        case 'text':
        case 'email':
        case 'url':
        case 'tel':
            return `
                <input type="${fieldConfig.type}" id="${fieldId}" name="${fieldId}" value="${fieldValue || ''}" 
                       class="${commonClasses}" 
                       placeholder="${placeholder}"
                       ${fieldConfig.required ? 'required' : ''}
                       ${validation.maxLength ? `maxlength="${validation.maxLength}"` : ''}
                       ${validation.minLength ? `minlength="${validation.minLength}"` : ''}
                       ${validation.pattern ? `pattern="${validation.pattern}"` : ''}
                       onchange="updateFieldData()" ${extraAttrs}>
            `;
            
        case 'textarea':
            const rows = fieldConfig.options?.rows || 4;
            return `
                <textarea id="${fieldId}" name="${fieldId}" rows="${rows}" 
                          class="${commonClasses}" 
                          placeholder="${placeholder}"
                          ${fieldConfig.required ? 'required' : ''}
                          ${validation.maxLength ? `maxlength="${validation.maxLength}"` : ''}
                          onchange="updateFieldData()" ${extraAttrs}>${fieldValue || ''}</textarea>
            `;
            
        case 'number':
            return `
                <input type="number" id="${fieldId}" name="${fieldId}" value="${fieldValue || 0}" 
                       class="${commonClasses}" 
                       placeholder="${placeholder}"
                       ${fieldConfig.required ? 'required' : ''}
                       ${validation.min !== undefined ? `min="${validation.min}"` : ''}
                       ${validation.max !== undefined ? `max="${validation.max}"` : ''}
                       ${validation.step !== undefined ? `step="${validation.step}"` : ''}
                       onchange="updateFieldData()" ${extraAttrs}>
            `;
            
        case 'checkbox':
            const label = getCurrentLabel(fieldConfig.labels);
            return `
                <label class="flex items-center">
                    <input type="checkbox" id="${fieldId}" name="${fieldId}" ${fieldValue ? 'checked' : ''} 
                           class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:border-primary-500 focus:ring-primary-500"
                           onchange="updateFieldData()" ${extraAttrs}>
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">${label}</span>
                </label>
            `;
            
        case 'select':
            const options = fieldConfig.options?.items || [];
            return `
                <select id="${fieldId}" name="${fieldId}" class="${commonClasses}" 
                        ${fieldConfig.required ? 'required' : ''}
                        onchange="updateFieldData()" ${extraAttrs}>
                    <option value="">${placeholder}</option>
                    ${options.map(option => `
                        <option value="${option.value}" ${fieldValue === option.value ? 'selected' : ''}>
                            ${getCurrentLabel(option.labels)}
                        </option>
                    `).join('')}
                </select>
            `;
            
        case 'date':
        case 'datetime-local':
        case 'time':
            return `
                <input type="${fieldConfig.type === 'datetime' ? 'datetime-local' : fieldConfig.type}" 
                       id="${fieldId}" name="${fieldId}" value="${fieldValue || ''}" 
                       class="${commonClasses}" 
                       ${fieldConfig.required ? 'required' : ''}
                       ${validation.min ? `min="${validation.min}"` : ''}
                       ${validation.max ? `max="${validation.max}"` : ''}
                       onchange="updateFieldData()" ${extraAttrs}>
            `;
            
        case 'color':
            return `
                <input type="color" id="${fieldId}" name="${fieldId}" value="${fieldValue || '#ffffff'}" 
                       class="w-20 h-10 rounded border-gray-300 dark:border-gray-600"
                       onchange="updateFieldData()" ${extraAttrs}>
            `;
            
        case 'richtext':
            return `
                <textarea id="${fieldId}" name="${fieldId}" 
                          class="tinymce-editor ${commonClasses}" 
                          placeholder="${placeholder}"
                          ${fieldConfig.required ? 'required' : ''}
                          ${validation.maxLength ? `maxlength="${validation.maxLength}"` : ''}
                          onchange="updateFieldData()" ${extraAttrs}>${fieldValue || ''}</textarea>
            `;
            
        default:
            return `
                <input type="text" id="${fieldId}" name="${fieldId}" value="${fieldValue || ''}" 
                       class="${commonClasses}" 
                       placeholder="${placeholder}"
                       onchange="updateFieldData()" ${extraAttrs}>
            `;
    }
}

// 获取当前语言的标签
function getCurrentLabel(labels) {
    if (!labels || typeof labels !== 'object') return '';
    const currentLang = getCultureFromUrl();
    return labels[currentLang] || labels['zh'] || labels['en'] || Object.values(labels)[0] || '';
}

// 获取当前语言的占位符
function getCurrentPlaceholder(placeholders) {
    if (!placeholders || typeof placeholders !== 'object') return '';
    const currentLang = getCultureFromUrl();
    return placeholders[currentLang] || placeholders['zh'] || placeholders['en'] || Object.values(placeholders)[0] || '';
}

// 从URL获取当前语言
function getCultureFromUrl() {
    const pathParts = window.location.pathname.split('/');
    const supportedCodes = pageData.supportedLanguages.map(l => l.Code);
    return supportedCodes.includes(pathParts[1]) ? pathParts[1] : 'zh';
}

// 创建多语言字段
function createMultilingualField(fieldName, fieldValue, componentIndex) {
    const fieldId = `field_${componentIndex}_${fieldName}`;
    
    return `
        <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ${fieldName} <span class="text-primary-600">(@AdminLocalizer["MultilingualContent"])</span>
            </label>
            <div class="multilingual-field" data-field="${fieldName}">
                <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        ${pageData.supportedLanguages.map(langObj => `
                            <button type="button" class="language-tab ${langObj.Code === 'zh' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} dark:text-gray-400 dark:hover:text-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                    data-lang="${langObj.Code}" onclick="switchLanguageTab(this, '${fieldId}')">
                                ${langObj.Name || langObj.Code.toUpperCase()}
                            </button>
                        `).join('')}
                    </nav>
                </div>
                <div class="language-contents">
                    ${pageData.supportedLanguages.map(langObj => `
                        <div class="language-content ${langObj.Code === 'zh' ? 'block' : 'hidden'}" data-lang="${langObj.Code}">
                            ${createInputElement(fieldName, fieldValue[langObj.Code] || '', `${fieldId}_${langObj.Code}`, `data-lang="${langObj.Code}"`)}
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

// 创建简单字段
function createSimpleField(fieldName, fieldValue, componentIndex) {
    const fieldId = `field_${componentIndex}_${fieldName}`;
    
    return `
        <div class="form-group">
            <label for="${fieldId}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ${fieldName}
            </label>
            ${createInputElement(fieldName, fieldValue, fieldId)}
        </div>
    `;
}

// 创建输入元素
function createInputElement(fieldName, fieldValue, fieldId, extraAttrs = '') {
    const commonClasses = 'w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500';
    
    if (typeof fieldValue === 'boolean') {
        return `
            <label class="flex items-center">
                <input type="checkbox" id="${fieldId}" name="${fieldId}" ${fieldValue ? 'checked' : ''} 
                       class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:border-primary-500 focus:ring-primary-500"
                       onchange="updateFieldData()" ${extraAttrs}>
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">${fieldName}</span>
            </label>
        `;
    } else if (typeof fieldValue === 'number') {
        return `
            <input type="number" id="${fieldId}" name="${fieldId}" value="${fieldValue}" 
                   class="${commonClasses}" onchange="updateFieldData()" ${extraAttrs}>
        `;
    } else if (typeof fieldValue === 'string' && (fieldValue.length > 100 || fieldName.toLowerCase().includes('content') || fieldName.toLowerCase().includes('description'))) {
        return `
            <textarea id="${fieldId}" name="${fieldId}" rows="4" 
                      class="${commonClasses}" onchange="updateFieldData()" ${extraAttrs}>${fieldValue}</textarea>
        `;
    } else {
        return `
            <input type="text" id="${fieldId}" name="${fieldId}" value="${fieldValue || ''}" 
                   class="${commonClasses}" onchange="updateFieldData()" ${extraAttrs}>
        `;
    }
}

// 切换语言标签页 - 使用通用系统
function switchLanguageTab(tab, fieldId) {
    const container = tab.closest('.multilingual-field');
    const lang = tab.getAttribute('data-lang');
    
    // 使用通用系统切换语言
    return window.switchLanguageTab(lang, container, {
        buttonClass: 'language-tab',
        contentClass: 'language-content',
        contentIdPrefix: `${fieldId}_`,
        activeClasses: ['border-primary-500', 'text-primary-600'],
        inactiveClasses: ['border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300']
    });
}

// 初始化语言标签页
function initializeLanguageTabs(container) {
    console.log('Initializing language tabs in container:', container);
    
    // 查找所有多语言字段
    const multilingualFields = container.querySelectorAll('.multilingual-field');
    console.log('Found multilingual fields:', multilingualFields.length);
    
    multilingualFields.forEach((field, index) => {
        console.log('Initializing field', index, field);
        
        // 找到第一个语言标签页并激活
        const firstTab = field.querySelector('.language-tab');
        if (firstTab) {
            console.log('Found first tab:', firstTab);
            // 直接调用切换函数而不是点击事件
            const fieldId = field.getAttribute('data-field');
            switchLanguageTab(firstTab, fieldId);
        } else {
            console.log('No language tab found in field');
        }
    });
}

// 更新字段数据
function updateFieldData() {
    pageData.components.forEach((componentData, index) => {
        const componentId = `component_${index}`;
        const formContainer = document.getElementById(`form_${componentId}`);
        const dataTextarea = document.getElementById(`dataJson_${componentId}`);
        
        if (formContainer && dataTextarea) {
            const updatedData = collectFormData(formContainer, componentData.MultilingualFields || []);
            dataTextarea.value = JSON.stringify(updatedData, null, 2);
        }
    });
}

// 收集表单数据
function collectFormData(container, multilingualFields) {
    // 同步TinyMCE内容到textarea
    if (typeof tinymce !== 'undefined') {
        tinymce.triggerSave();
    }
    
    const data = {};
    
    container.querySelectorAll('.form-field').forEach(fieldContainer => {
        const multilingualField = fieldContainer.querySelector('.multilingual-field');
        
        if (multilingualField) {
            // 多语言字段
            const fieldName = multilingualField.getAttribute('data-field');
            data[fieldName] = {};
            
            multilingualField.querySelectorAll('.language-content input, .language-content textarea, .language-content select').forEach(input => {
                const lang = input.getAttribute('data-lang');
                if (lang) {
                    if (input.type === 'checkbox') {
                        data[fieldName][lang] = input.checked;
                    } else if (input.type === 'number') {
                        data[fieldName][lang] = parseFloat(input.value) || 0;
                    } else {
                        // 对于TinyMCE编辑器，获取最新内容
                        if (input.classList.contains('tinymce-editor') && tinymce.get(input.id)) {
                            data[fieldName][lang] = tinymce.get(input.id).getContent();
                        } else {
                            data[fieldName][lang] = input.value || '';
                        }
                    }
                }
            });
        } else {
            // 普通字段
            const input = fieldContainer.querySelector('input, textarea, select');
            if (input) {
                const fieldName = extractFieldName(input.id);
                if (fieldName) {
                    if (input.type === 'checkbox') {
                        data[fieldName] = input.checked;
                    } else if (input.type === 'number') {
                        data[fieldName] = parseFloat(input.value) || 0;
                    } else {
                        // 对于TinyMCE编辑器，获取最新内容
                        if (input.classList.contains('tinymce-editor') && tinymce.get(input.id)) {
                            data[fieldName] = tinymce.get(input.id).getContent();
                        } else {
                            data[fieldName] = input.value || '';
                        }
                    }
                }
            }
        }
    });
    
    return data;
}

// 从input的id中提取字段名
function extractFieldName(inputId) {
    // inputId格式: field_{componentIndex}_{fieldName} 或 field_{componentIndex}_{fieldName}_{lang}
    const parts = inputId.split('_');
    if (parts.length >= 3) {
        return parts.slice(2).join('_').replace(/_[a-z]{2}$/, ''); // 移除语言后缀
    }
    return null;
}

// 切换组件可见性
function toggleComponentVisibility(index, isVisible) {
    // 更新隐藏的表单字段
    const input = document.querySelector(`input[name="ComponentsData[${index}].IsVisible"][type="hidden"]`);
    if (input) {
        input.value = isVisible;
    }

    // 确保checkbox状态与传入的isVisible参数一致
    const checkbox = document.querySelector(`input[name="ComponentsData[${index}].IsVisible"][type="checkbox"]`);
    if (checkbox && checkbox.checked !== isVisible) {
        checkbox.checked = isVisible;
    }

    // 可选：更新组件卡片的视觉状态（如透明度等）
    const componentCard = document.getElementById(`component-card-${index}`);
    if (componentCard) {
        componentCard.dataset.visible = isVisible.toString();

        // 添加视觉反馈（例如降低不可见组件的透明度）
        if (isVisible) {
            componentCard.style.opacity = '1';
        } else {
            componentCard.style.opacity = '0.6';
        }
    }
}

// 切换组件折叠/展开
function toggleComponent(componentId) {
    const content = document.getElementById(componentId);
    const button = content.previousElementSibling.querySelector('button[onclick*="toggleComponent"]');
    const icon = button.querySelector('svg path');
    
    if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.setAttribute('d', 'M19 9l-7 7-7-7');
    } else {
        content.classList.add('hidden');
        icon.setAttribute('d', 'M9 5l7 7-7 7');
    }
}

// 发布内容
async function publishContent() {
    const result = await Dialog.confirm((window.Resources?.Admin?.PublishPageContent || 'Publish Page Content') + '?', window.Resources?.Shared?.Confirm || 'Confirm');
    if (result) {
        // 先保存，再发布
        document.getElementById('pageContentForm').submit();
    }
}

// 表单提交前更新数据
document.getElementById('pageContentForm')?.addEventListener('submit', function() {
    // 同步TinyMCE内容到textarea
    if (typeof tinymce !== 'undefined') {
        tinymce.triggerSave();
    }
    updateFieldData();
});

// 初始化TinyMCE编辑器
function initializeTinyMCE(container) {
    const richTextEditors = container.querySelectorAll('.tinymce-editor');
    
    richTextEditors.forEach(editor => {
        // 避免重复初始化
        if (editor.hasAttribute('data-tinymce-initialized')) {
            return;
        }
        
        editor.setAttribute('data-tinymce-initialized', 'true');
        
        // 从fieldConfig获取配置选项（如果有的话）
        const fieldOptions = getFieldOptionsForEditor(editor);
        const customToolbar = fieldOptions.toolbar || 
            'undo redo | blocks | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help';
        const customHeight = fieldOptions.height || 400;
        
        // 优先使用通用初始化器，保持向后兼容
        if (window.AdminTinyMCE && typeof window.AdminTinyMCE.init === 'function') {
            window.AdminTinyMCE.init(editor, {
                height: customHeight,
                menubar: false,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount'
                ],
                toolbar: customToolbar,
                content_style: `
                    body { 
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                        font-size: 14px; 
                        line-height: 1.4; 
                        color: #333; 
                    }
                `,
                setup: function(editorInstance) {
                    editorInstance.on('change', function() {
                        editorInstance.getElement().dispatchEvent(new Event('change'));
                    });
                },
                images_upload_handler: function (blobInfo, progress) {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = function () {
                            resolve(reader.result);
                        };
                        reader.readAsDataURL(blobInfo.blob());
                    });
                }
            });
        } else {
            // 回退到页面内原始初始化逻辑
            if (typeof tinymce === 'undefined') { return; }
            tinymce.init({
                license_key: 'gpl',
                target: editor,
                height: customHeight,
                menubar: false,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount'
                ],
                toolbar: customToolbar,
                content_style: `
                    body { 
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                        font-size: 14px; 
                        line-height: 1.4; 
                        color: #333; 
                    }
                `,
                setup: function(editor) {
                    editor.on('change', function() {
                        // 触发原始textarea的change事件
                        editor.getElement().dispatchEvent(new Event('change'));
                    });
                },
                language: getCurrentLanguageForTinyMCE(),
                skin: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'oxide-dark' : 'oxide',
                content_css: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'default',
                // 图片上传配置（如果需要）
                images_upload_handler: function (blobInfo, progress) {
                    return new Promise((resolve, reject) => {
                        // 这里可以实现图片上传逻辑
                        // 暂时禁用图片上传，返回base64
                        const reader = new FileReader();
                        reader.onload = function () {
                            resolve(reader.result);
                        };
                        reader.readAsDataURL(blobInfo.blob());
                    });
                }
            });
        }
    });
}

// 获取编辑器的字段配置选项
function getFieldOptionsForEditor(editor) {
    // 这里可以从编辑器的data属性或其他方式获取字段配置
    // 暂时返回空对象，使用默认配置
    return {};
}

// 获取TinyMCE语言设置
function getCurrentLanguageForTinyMCE() {
    const currentLang = getCultureFromUrl();
    const langMap = {
        'zh': 'zh_CN',
        'en': 'en',
        'ja': 'ja'
    };
    return langMap[currentLang] || 'en';
}

// 销毁TinyMCE编辑器
function destroyTinyMCE(container) {
    const richTextEditors = container.querySelectorAll('.tinymce-editor[data-tinymce-initialized]');
    
    richTextEditors.forEach(editor => {
        const editorId = editor.id;
        if (tinymce.get(editorId)) {
            tinymce.get(editorId).destroy();
        }
        editor.removeAttribute('data-tinymce-initialized');
    });
}
</script>

}