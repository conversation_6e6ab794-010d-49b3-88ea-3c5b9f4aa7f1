@model MlSoft.Sites.Web.ViewModels.Admin.PageManageListViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@inject IStringLocalizer<MlSoft.Sites.Web.Resources.AdminResource> AdminLocalizer
@inject IStringLocalizer<MlSoft.Sites.Web.Resources.SharedResource> SharedRes

@{
    ViewData["Title"] = AdminLocalizer["PageContentManage"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="p-4">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                @AdminLocalizer["PageContentManage"]
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
                @AdminLocalizer["ManagePageContentData"]
            </p>
        </div>

    <!-- 工具栏 -->
    <div class="mb-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">

                 <!-- 筛选器 -->
            <div class="flex flex-col sm:flex-row gap-2">
                <form action="@Html.MultilingualUrl("", "PageManage")" method="get" class="flex gap-2">
                    <input type="hidden" name="page" value="1" />
                    <select name="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-40 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">@AdminLocalizer["AllStatus"]</option>
                        <option value="@PageContentStatus.Published" selected="@(Model.FilterStatus == PageContentStatus.Published)">@AdminLocalizer["Published"]</option>
                        <option value="@PageContentStatus.Draft" selected="@(Model.FilterStatus == PageContentStatus.Draft)">@AdminLocalizer["Draft"]</option>
                    </select>
                    <button type="submit" class="text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                        @AdminLocalizer["Filter"]
                    </button>
                </form>
            </div>


                
            </div>
        </div>

        <!-- 内容列表 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            @if (Model.Contents.Any())
            {
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-900">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    @AdminLocalizer["PageName"]
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    @AdminLocalizer["PageContentStatus"]
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    @AdminLocalizer["ComponentCount"]
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    @AdminLocalizer["LastUpdated"]
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">
                                    @AdminLocalizer["Actions"]
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach (var content in Model.Contents)
                            {
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                    <td class="px-6 py-4">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                @(content.PageName.TryGetValue(ViewData["CurrentLanguage"]?.ToString(), out var localeName) ? localeName : content.PageKey)
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                @content.PageKey
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        @if (content.HasContent)
                                        {
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @(content.Status switch {
                                                    PageContentStatus.Draft => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
                                                    PageContentStatus.Review => "bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-300",
                                                    PageContentStatus.Published => "bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-300",
                                                    PageContentStatus.Archived => "bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-300",
                                                    _ => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                                                })">
                                                @(content.Status switch {
                                                    PageContentStatus.Draft => AdminLocalizer["Draft"],
                                                    PageContentStatus.Review => AdminLocalizer["Review"],
                                                    PageContentStatus.Published => AdminLocalizer["Published"],
                                                    PageContentStatus.Archived => AdminLocalizer["Archived"],
                                                    _ => AdminLocalizer["Draft"]
                                                })
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-300">
                                                @AdminLocalizer["NoContentCreated"]
                                            </span>
                                        }
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                                        @content.ComponentCount @AdminLocalizer["ComponentsCount"]
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        @content.UpdatedAt.ToLocalTime().ToString("yyyy-MM-dd HH:mm")
                                        @if (!string.IsNullOrEmpty(content.UpdatedBy))
                                        {
                                            <div class="text-xs">@content.UpdatedBy</div>
                                        }
                                    </td>
                                    <td class="px-6 py-4  text-sm font-medium">

                                        <div class="flex items-center space-x-2">
                                            <!-- 编辑按钮 -->
                                            <a href="@Html.MultilingualUrl("Edit", "PageManage", null, new { id = content.PageConfigurationId })"
                                               class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                                               title="@AdminLocalizer["Edit"]">
                                                <i class="fas fa-edit text-2xl"></i>
                                            </a>

                                             @if (content.HasContent)
                                            {
                                                @if (content.Status == PageContentStatus.Published)
                                                {
                                                    <button type="button" 
                                                        class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300" 
                                                        title="@AdminLocalizer["UnpublishPageContent"]"
                                                         onclick="unpublishContent('@content.Id')">
                                                            <i class="fas fa-eye-slash text-2xl"></i>
                                                        </button>

                                                }
                                                else if (content.Status == PageContentStatus.Draft)
                                                {
                                                      <button type="button" 
                                                        class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300" 
                                                        title="@AdminLocalizer["PublishPageContent"]"
                                                        onclick="publishContent('@content.Id')">
                                                        <i class="fas fa-eye text-2xl"></i>
                                                      </button>
                                                }
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                @if (Model.TotalPages > 1)
                {
                    <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex-1 flex justify-between sm:hidden">
                                @if (Model.HasPreviousPage)
                                {
                                    <a href="@Url.Action("Index", new { page = Model.CurrentPage - 1, pageSize = Model.PageSize, status = Model.FilterStatus, search = Model.SearchKeyword })"
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                                        @SharedRes["Previous"]
                                    </a>
                                }
                                @if (Model.HasNextPage)
                                {
                                    <a href="@Url.Action("Index", new { page = Model.CurrentPage + 1, pageSize = Model.PageSize, status = Model.FilterStatus, search = Model.SearchKeyword })"
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 ml-3">
                                        @SharedRes["Next"]
                                    </a>
                                }
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700 dark:text-gray-300">
                                        @SharedRes["Showing"] <span class="font-medium">@((Model.CurrentPage - 1) * Model.PageSize + 1)</span>
                                        @SharedRes["To"] <span class="font-medium">@Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount)</span>
                                        @SharedRes["Of"] <span class="font-medium">@Model.TotalCount</span> @SharedRes["Results"]
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        @for (int i = 1; i <= Model.TotalPages; i++)
                                        {
                                            @if (i == Model.CurrentPage)
                                            {
                                                <span class="bg-primary-50 border-primary-500 text-primary-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                                    @i
                                                </span>
                                            }
                                            else
                                            {
                                                <a href="@Url.Action("Index", new { page = i, pageSize = Model.PageSize, status = Model.FilterStatus, search = Model.SearchKeyword })"
                                                   class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                                    @i
                                                </a>
                                            }
                                        }
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400 text-lg">
                        @SharedRes["NoData"]
                    </div>
                    <p class="mt-2 text-sm text-gray-400 dark:text-gray-500">
                        @AdminLocalizer["NoComponents"]
                    </p>
                </div>
            }
        </div>
    </div>

@section Scripts {
<script>
// Dialog system is initialized globally in _AdminLayout.cshtml

// 发布内容
async function publishContent(id) {
    const result = await Dialog.confirm((window.Resources?.Admin?.PublishPageContent || 'Publish Page Content') + '?', window.Resources?.Shared?.Confirm || 'Confirm');
    if (result) {
        try {
            const response = await fetch(`@Html.MultilingualUrl("Publish", "PageManage")/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                }
            });
            
            const data = await response.json();
            if (data.success) {
                Dialog.notify(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                Dialog.error(data.message);
            }
        } catch (error) {
            Dialog.error('@AdminLocalizer["PageContentPublishError"]');
        }
    }
}

// 取消发布内容
async function unpublishContent(id) {
    const result = await Dialog.confirm((window.Resources?.Admin?.UnpublishPageContent || 'Unpublish Page Content') + '?', window.Resources?.Shared?.Confirm || 'Confirm');
    if (result) {
        try {
            const response = await fetch(`@Html.MultilingualUrl("Unpublish", "PageManage")/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                }
            });
            
            const data = await response.json();
            if (data.success) {
                Dialog.notify(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                Dialog.error(data.message);
            }
        } catch (error) {
            Dialog.error('@AdminLocalizer["PageContentUnpublishError"]');
        }
    }
}
</script>
}