@model MlSoft.Sites.Web.ViewModels.Admin.RecruitmentIndexViewModel
@{
    ViewData["Title"] = AdminRes["RecruitmentManagement"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="space-y-6">
    <!-- Tab Navigation -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="border-b border-gray-200 dark:border-gray-600 mb-6">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button onclick="switchTab('job-positions')" data-tab="job-positions" class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm text-primary-600 dark:text-primary-400 border-primary-500">
                        @AdminRes["JobPositions"]
                    </button>
                    <button onclick="switchTab('employee-interviews')" data-tab="employee-interviews"
                            class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 border-transparent">
                        @AdminRes["EmployeeInterviews"]
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <!-- Job Positions Tab -->
            <div id="job-positions-tab" class="tab-content">
                <div class="space-y-6">
                    <!-- 操作工具栏 -->
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-4">
                            <button onclick="openJobPositionModal()" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fas fa-plus mr-2"></i>@AdminRes["CreateJobPosition"]
                            </button>
                        </div>
                        <div class="flex space-x-4">
                            <select id="jobTypeFilter" class="border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="">@AdminRes["AllJobTypes"]</option>
                                @foreach (var type in Model.AvailableJobTypes)
                                {
                                    <option value="@((int)type)">@AdminRes[$"JobType_{type}"]</option>
                                }
                            </select>
                            <div class="relative">
                                <input type="text" id="jobSearchInput" placeholder="@AdminRes["SearchJobs"]"
                                       class="w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 职位列表 -->
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                        <ul id="jobPositionsList" class="divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach (var job in Model.Jobs.Items)
                            {
                                <li class="job-item" data-id="@job.Id">
                                    <div class="px-4 py-4 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="ml-4 min-w-0 flex-1">
                                                <div class="flex items-center space-x-2">
                                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                        @job.JobTitle
                                                    </h3>
                                                    @if (job.IsFeatured)
                                                    {
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                                                            <i class="fas fa-star mr-1"></i>@AdminRes["Featured"]
                                                        </span>
                                                    }
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                        @AdminRes[$"JobType_{job.Type}"]
                                                    </span>
                                                </div>
                                                <div class="mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-4">
                                                    <span><i class="fas fa-building mr-1"></i>@job.Department</span>
                                                    <span><i class="fas fa-calendar mr-1"></i>@job.PostDate.ToString("yyyy-MM-dd")</span>
                                                    @if (job.ApplicationDeadline.HasValue)
                                                    {
                                                        <span><i class="fas fa-clock mr-1"></i>@AdminRes["Deadline"]: @job.ApplicationDeadline.Value.ToString("yyyy-MM-dd")</span>
                                                    }
                                                    @if (!string.IsNullOrEmpty(job.SalaryRange))
                                                    {
                                                        <span><i class="fas fa-yen-sign mr-1"></i>@job.SalaryRange</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <button onclick="editJobPosition('@job.Id')"
                                                    class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button onclick="deleteJobPosition('@job.Id')"
                                                    class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            }
                        </ul>
                    </div>

                    <!-- Pagination -->
                    @if (Model.Jobs.Total > 0)
                    {
                        ViewBag.CurrentPageIndex = Model.Jobs.Page;
                        ViewBag.TotalCount = Model.Jobs.Total;
                        ViewBag.PageSize = Model.Jobs.PageSize;
                        ViewBag.PageUrl = new Func<int, string>(pageNum => Url.Action("Index", new { page = pageNum }));

                        @await Html.PartialAsync("_Pagination");
                    }
                </div>
            </div>

            <!-- Employee Interviews Tab -->
            <div id="employee-interviews-tab" class="tab-content hidden">
                <!-- 懒加载占位符 -->
                <div class="loading-placeholder flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">@AdminRes["Loading"]...</span>
                </div>
            </div>

            <!-- Statistics Tab -->
            <div id="statistics-tab" class="tab-content hidden">
                <!-- 懒加载占位符 -->
                <div class="loading-placeholder flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">@AdminRes["Loading"]...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 职位编辑Modal -->
<div id="jobPositionModal" class="fixed inset-0 z-50 overflow-y-auto hidden">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
            <form id="jobPositionForm" onsubmit="submitJobPosition(event)">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="flex items-start">
                        <div class="w-full">
                            <!-- Modal Header -->
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="jobModalTitle">@AdminRes["CreateJobPosition"]</h3>
                                <button type="button" onclick="closeJobPositionModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                            <!-- 多语言标签页 -->
                            <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                                <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                                    @{
                                        var supportedLanguages = (SupportedLanguage[])ViewData["SupportedLanguages"];
                                        var isFirst = true;
                                    }
                                    @foreach (var lang in supportedLanguages)
                                    {
                                        <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#jobPositionModal', {buttonClass: 'job-modal-lang-tab-button', contentClass: 'job-modal-lang-content', contentIdPrefix: 'job-modal-lang-'})"
                                                class="job-modal-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                                data-lang="@lang.Code">
                                            @lang.Emoji @lang.Name
                                        </button>
                                        isFirst = false;
                                    }
                                </nav>
                            </div>

                            <!-- 多语言内容 -->
                            @{
                                isFirst = true;
                            }
                            @foreach (var lang in supportedLanguages)
                            {
                                <div id="<EMAIL>" class="job-modal-lang-content @(isFirst ? "" : "hidden")">
                                    <div class="grid grid-cols-1 gap-4 mb-6">
                                        <div>
                                            <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                @AdminRes["JobTitle"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"].ToString()){
                                                <span class="text-red-500">*</span>
                                            }
                                        </label>
                                        <input type="text" id="<EMAIL>" name="<EMAIL>"
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                               required="@(lang.Code == ViewData["DefaultLanguage"].ToString())" />
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["JobDescription"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                                  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 tinymce-editor"></textarea>
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["Requirements"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                                  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 tinymce-editor"></textarea>
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["Benefits"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="2"
                                                  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["WorkLocation"] (@lang.Name)
                                        </label>
                                        <input type="text" id="<EMAIL>" name="<EMAIL>"
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                    </div>
                                </div>
                            </div>
                            isFirst = false;
                                                        }

                            <!-- 基本信息 -->
                            <div class="grid grid-cols-3 gap-4 mb-4">
                                <div>
                                    <label for="jobType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["JobType"]</label>
                                    <select id="jobType" name="jobType" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                        @foreach (var type in Model.AvailableJobTypes)
                                        {
                                            <option value="@((int)type)">@AdminRes[$"JobType_{type}"]</option>
                                        }
                                    </select>
                                </div>
                                <div>
                                    <label for="employmentType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["EmploymentType"]</label>
                                    <select id="employmentType" name="employmentType" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                        @foreach (var type in Model.AvailableEmploymentTypes)
                                        {
                                            <option value="@((int)type)">@AdminRes[$"EmploymentType_{type}"]</option>
                                        }
                                    </select>
                                </div>
                                <div>
                                    <label for="experienceLevel" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["ExperienceLevel"]</label>
                                    <select id="experienceLevel" name="experienceLevel" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                        @foreach (var level in Model.AvailableExperienceLevels)
                                        {
                                            <option value="@((int)level)">@AdminRes[$"ExperienceLevel_{level}"]</option>
                                        }
                                    </select>
                                </div>
                            </div>

                            <!-- 薪资信息 -->
                            <div class="grid grid-cols-3 gap-4 mb-4">
                                <div>
                                    <label for="salaryMin" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["SalaryMin"]</label>
                                    <input type="number" id="salaryMin" name="salaryMin"
                                           class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                </div>
                                <div>
                                    <label for="salaryMax" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["SalaryMax"]</label>
                                    <input type="number" id="salaryMax" name="salaryMax"
                                           class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                </div>
                                <div>
                                    <label for="currency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Currency"]</label>
                                    <select id="currency" name="currency" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                        <option value="JPY">@AdminRes["Currency_JPY"]</option>
                                        <option value="USD">@AdminRes["Currency_USD"]</option>
                                        <option value="CNY">@AdminRes["Currency_CNY"]</option>
                                        <option value="EUR">@AdminRes["Currency_EUR"]</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 日期信息 -->
                            <div class="grid grid-cols-3 gap-4 mb-4">
                                <div>
                                    <label for="postDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["PostDate"]</label>
                                    <input type="date" id="postDate" name="postDate"
                                           class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                </div>
                                <div>
                                    <label for="applicationDeadline" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["ApplicationDeadline"]</label>
                                    <input type="date" id="applicationDeadline" name="applicationDeadline"
                                           class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                </div>
                                <div>
                                    <label for="workingHours" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["WorkingHours"]</label>
                                    <input type="text" id="workingHours" name="workingHours" placeholder="@AdminRes["WorkingHoursPlaceholder"]"
                                           class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                </div>
                                <div>
                                    <label for="probationPeriod" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["ProbationPeriod"]</label>
                                    <input type="text" id="probationPeriod" name="probationPeriod" placeholder="@AdminRes["ProbationPeriodPlaceholder"]"
                                           class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                </div>
                            </div>


                            <!-- 选项 -->
                            <div class="flex items-center space-x-6 mb-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="isActive" name="isActive" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    <label for="isActive" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">@AdminRes["IsActive"]</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="isFeatured" name="isFeatured" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    <label for="isFeatured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">@AdminRes["IsFeatured"]</label>
                                </div>
                            </div>

                            <input type="hidden" id="jobId" name="jobId" />
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        @AdminRes["Save"]
                    </button>
                    <button type="button" onclick="closeJobPositionModal()"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        @AdminRes["Cancel"]
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="~/js/admin/recruitment.js" asp-append-version="true"></script>
