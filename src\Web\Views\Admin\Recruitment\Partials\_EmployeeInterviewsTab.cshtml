@model MlSoft.Sites.Web.ViewModels.Admin.EmployeeInterviewsTabViewModel

<div class="space-y-6">
    <!-- 操作工具栏 -->
    <div class="flex justify-between items-center">
        <div class="flex space-x-4">
            <button onclick="openEmployeeInterviewModal()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-plus mr-2"></i>@AdminRes["CreateEmployeeInterview"]
            </button>
        </div>
    </div>

    <!-- 员工访谈列表 -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        <ul id="employeeInterviewsList" class="divide-y divide-gray-200 dark:divide-gray-700">
            @foreach (var interview in Model.Interviews)
            {
                <li class="interview-item" data-id="@interview.Id">
                    <div class="px-4 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="ml-4 flex-shrink-0">
                                @if (!string.IsNullOrEmpty(interview.PhotoUrl))
                                {
                                    <img class="h-12 w-12 rounded-full object-cover" src="@interview.PhotoUrl" alt="@interview.EmployeeName">
                                }
                                else
                                {
                                    <div class="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                        <i class="fas fa-user text-gray-400"></i>
                                    </div>
                                }
                            </div>
                            <div class="ml-4 min-w-0 flex-1">
                                <div class="flex items-center space-x-2">
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                        @interview.EmployeeName
                                    </h3>
                                    @if (interview.IsFeatured)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                                            <i class="fas fa-star mr-1"></i>@AdminRes["Featured"]
                                        </span>
                                    }
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                    @AdminRes[$"InterviewType_{interview.InterviewType}"]
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @(interview.Status == "Published" ? "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200" :
                                                                                interview.Status == "Draft" ? "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200" :
                                                                                "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200")">
                                    @AdminRes[$"Status_{interview.Status}"]
                                </span>
                            </div>
                            <div class="mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-4">
                                <span><i class="fas fa-clock mr-1"></i>@interview.YearsOfService @AdminRes["Years"]</span>
                                <span><i class="fas fa-calendar mr-1"></i>@interview.InterviewDate.ToString("yyyy-MM-dd")</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-500 dark:text-gray-400">@AdminRes["Order"]: @interview.DisplayOrder</span>
                        <button onclick="editEmployeeInterview('@interview.Id')"
                                class="text-2xl text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteEmployeeInterview('@interview.Id')"
                                class="text-2xl text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </li>
                        }
        </ul>

        @if (!Model.Interviews.Any())
        {
            <div class="text-center py-12">
                <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600 dark:text-gray-400">@AdminRes["NoInterviewsFound"]</p>
                <button onclick="openEmployeeInterviewModal()"
                        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                    <i class="fas fa-plus mr-2"></i>@AdminRes["CreateFirstInterview"]
                </button>
            </div>
        }
    </div>


</div>

<!-- 员工访谈编辑Modal -->
<div id="employeeInterviewModal" class="fixed inset-0 z-50 overflow-y-auto hidden">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
            <form id="employeeInterviewForm" onsubmit="submitEmployeeInterview(event)">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="flex items-start">
                        <div class="w-full">

                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4" id="interviewModalTitle">
                                    @AdminRes["CreateEmployeeInterview"]
                                </h3>
                                <button type="button" onclick="closeEmployeeInterviewModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                            <!-- 多语言标签页 -->
                            <div class="border-b border-gray-200 dark:border-gray-600 mb-4">
                                <nav class="-mb-px flex space-x-8" aria-label="Language Tabs">
                                    @{
                                        var supportedLanguages = (SupportedLanguage[])ViewData["SupportedLanguages"];
                                        var isFirst = true;
                                    }
                                    @foreach (var lang in supportedLanguages)
                                    {
                                        <button type="button" onclick="window.switchLanguageTab('@lang.Code', '#employeeInterviewModal', {buttonClass: 'interview-modal-lang-tab-button', contentClass: 'interview-modal-lang-content', contentIdPrefix: 'interview-modal-lang-'})"
                                                class="interview-modal-lang-tab-button @(isFirst ? "active border-primary-500 text-primary-600" : "border-transparent text-gray-500 dark:text-gray-400") hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                                                data-lang="@lang.Code">
                                            @lang.Emoji @lang.Name
                                        </button>
                                        isFirst = false;
                                    }
                                </nav>
                            </div>

                            <!-- 多语言内容 -->
                            @{
                                isFirst = true;
                            }
                            @foreach (var lang in supportedLanguages)
                            {
                                <div id="<EMAIL>" class="interview-modal-lang-content @(isFirst ? "" : "hidden")">
                                    <div class="grid grid-cols-2 gap-4 mb-6">
                                        <div>
                                            <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                @AdminRes["EmployeeName"] (@lang.Name) @if(lang.Code == ViewData["DefaultLanguage"].ToString()){
                                                <span class="text-red-500">*</span>
                                            }
                                        </label>
                                        <input type="text" id="<EMAIL>" name="<EMAIL>"
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                               required="@(lang.Code == ViewData["DefaultLanguage"].ToString())" />
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["Position"] (@lang.Name)
                                        </label>
                                        <input type="text" id="<EMAIL>" name="<EMAIL>"
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 gap-4 mb-6">
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["InterviewContent"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="4"
                                                  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 tinymce-editor"></textarea>
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["WorkDescription"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                                  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                                    </div>
                                    <div>
                                        <label for="<EMAIL>" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["CompanyImpression"] (@lang.Name)
                                        </label>
                                        <textarea id="<EMAIL>" name="<EMAIL>" rows="3"
                                                  class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
                                    </div>
                                </div>
                            </div>
                            isFirst = false;
                                                        }

                            <!-- 基本信息 -->
                            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4">

                                <div class="md:col-span-2 lg:col-span-2 grid grid-cols-3 gap-4">

                                    <div>
                                        <label for="departmentId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            @AdminRes["Department"]
                                        </label>
                                        <select id="departmentId" name="departmentId"
                                                class="block whitespace-pre-wrap w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                           <option></option>

                                            @if(Model.Organs != null)
                                            {
                                                foreach(var organ in Model.Organs)
                                                {
                                                    <option value="@organ.Key">@Html.Raw(organ.Value.Replace(" ","&nbsp;"))</option>
                                                }
                                            }

                                           </select>
                                    </div>
                                    <div>
                                        <label for="interviewType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["InterviewType"]</label>
                                        <select id="interviewType" name="interviewType" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                            <option value="0">@AdminRes["InterviewType_NewEmployee"]</option>
                                            <option value="1">@AdminRes["InterviewType_Veteran"]</option>
                                            <option value="2">@AdminRes["InterviewType_Management"]</option>
                                            <option value="3">@AdminRes["InterviewType_Technical"]</option>
                                            <option value="4">@AdminRes["InterviewType_Sales"]</option>
                                            <option value="5">@AdminRes["InterviewType_Other"]</option>
                                        </select>
                                    </div>
                                   
                                    <div>
                                        <label for="yearsOfService" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["YearsOfService"]</label>
                                        <input type="number" id="yearsOfService" name="yearsOfService" min="0" value="1"
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                    </div>
                                    <div>
                                        <label for="interviewDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["InterviewDate"]</label>
                                        <input type="date" id="interviewDate" name="interviewDate"
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                    </div>

                                    <div>
                                        <label for="displayOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["DisplayOrder"]</label>
                                        <input type="number" id="displayOrder" name="displayOrder" min="0" value="0"
                                               class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" />
                                    </div>
                                    <div>
                                        <label for="interviewStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["Status"]</label>
                                        <select id="interviewStatus" name="interviewStatus" class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                            <option value="0">@AdminRes["Status_Draft"]</option>
                                            <option value="1">@AdminRes["Status_Published"]</option>
                                            <option value="2">@AdminRes["Status_Archived"]</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="md:col-span-1 lg:col-span-1">
                                    <div>
                                        <label for="photoUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">@AdminRes["PhotoUrl"]</label>
                                        <div id="employeePhotoUpload"></div>
                                        <input type="hidden" id="photoUrl" name="photoUrl" />
                                    </div>


                                </div>


                            </div>

                            <!-- 选项 -->
                            <div class="flex items-center space-x-6 mb-4 mt-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="isInterviewFeatured" name="isInterviewFeatured" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                                    <label for="isInterviewFeatured" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">@AdminRes["IsFeatured"]</label>
                                </div>
                            </div>

                            <input type="hidden" id="interviewId" name="interviewId" />
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        @AdminRes["Save"]
                    </button>
                    <button type="button" onclick="closeEmployeeInterviewModal()"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        @AdminRes["Cancel"]
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>