@{
    ViewData["Title"] = AdminRes["AccountSettingsTitle"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="space-y-6">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">@AdminRes["AccountSettingsTitle"]</h3>
            
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- 修改密码 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-lock text-primary-600 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-medium text-gray-900">@AdminRes["ChangePasswordTitle"]</h4>
                            <p class="text-sm text-gray-500">@AdminRes["UpdateYourAccountPassword"]</p>
                        </div>
                    </div>
                    <a asp-controller="Account" asp-action="ChangePassword"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-edit mr-2"></i>
                        @AdminRes["ChangePasswordTitle"]
                    </a>
                </div>

                <!-- 修改邮箱 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-envelope text-primary-600 text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-medium text-gray-900">@AdminRes["ChangeEmailTitle"]</h4>
                            <p class="text-sm text-gray-500">@AdminRes["UpdateYourEmailAddress"]</p>
                        </div>
                    </div>
                    <a asp-controller="Account" asp-action="ChangeEmail"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-edit mr-2"></i>
                        @AdminRes["ChangeEmailTitle"]
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>