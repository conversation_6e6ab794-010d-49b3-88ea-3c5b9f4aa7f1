@model MlSoft.Sites.Web.ViewModels.Admin.SiteSettingsViewModel
@{
    ViewData["Title"] = AdminRes["SiteSettingsTitle"];
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="space-y-6">

    <!-- Alert Messages -->
    @if (!string.IsNullOrEmpty((string)TempData["Success"]))
    {
        <div id="success-alert" class="mb-6 p-4 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-600 text-primary-700 dark:text-primary-300 rounded-md">
            <div class="flex">
                <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                @TempData["Success"]
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty((string)TempData["Error"]))
    {
        <div id="error-alert" class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-600 text-red-700 dark:text-red-300 rounded-md">
            <div class="flex">
                <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                @TempData["Error"]
            </div>
        </div>
    }

    <!-- Tab Navigation -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button onclick="switchTab('basic-info')"
                            class="tab-button active border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="basic-info">
                        @AdminRes["BasicInfo"]
                    </button>
                    <button onclick="switchTab('theme-settings')"
                            class="tab-button border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="theme-settings">
                        @AdminRes["ThemeSettings"]
                    </button>
                    <button onclick="switchTab('component-settings')"
                            class="tab-button border-transparent text-gray-500 hover:text-primary-600 hover:border-primary-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="component-settings">
                        @AdminRes["ComponentSettings"]
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <!-- Basic Information Tab -->
            <div id="basic-info-tab" class="tab-content">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">@AdminRes["BasicInfoSettingsTitle"]</h3>

                <form id="basic-info-form" class="space-y-6">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Multi-language Site Names -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">@AdminRes["SiteName"] *</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                @foreach (var lang in (MlSoft.Sites.Model.Configuration.SupportedLanguage[])ViewData["SupportedLanguages"])
                                {
                                    <div>
                                        <label for="<EMAIL>" class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">@lang.Emoji @lang.Name</label>
                                        <input type="text" id="<EMAIL>" name="siteNames[@lang.Code]"
                                               value="@(Model.BasicInfo.SiteNames.GetValueOrDefault(lang.Code, ""))"
                                               class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" required>
                                    </div>
                                }
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                            <div>
                                <!-- Domain -->
                                <div>
                                    <label for="domain" class="block text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["Domain"] *</label>
                                    <input type="text" id="domain" name="domain" value="@Model.BasicInfo.Domain"
                                           class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm" required>
                                </div>

                                <div>
                                    <label for="countCode" class="block mt-4 text-sm font-medium text-gray-700 dark:text-gray-300">@AdminRes["CountCode"]</label>
                                    <textarea rows="5" type="text" id="countCode" name="countCode"
                                              class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm">@Model.BasicInfo.CountCode</textarea>
                                </div>

                            </div>

                            <div>
                                <!-- Logo Upload -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">@AdminRes["Logo"]</label>
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-center w-full h-32 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
                                             onclick="document.getElementById('logoInput').click()">
                                            <div class="text-center" id="logoPreview">
                                                @if (Model.BasicInfo.LogoUrl != null)
                                                {
                                                    <img src="@Model.BasicInfo.LogoUrl" alt="Logo" class="max-h-28 mx-auto rounded">
                                                }
                                                else
                                                {
                                                    <div class="flex flex-col items-center">
                                                        <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                        </svg>
                                                        <p class="text-sm text-gray-500 dark:text-gray-400">@AdminRes["ClickUploadLogo"]</p>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <input type="file" id="logoInput" name="logoFile" accept="image/*" class="hidden" onchange="handleLogoUpload(this)">
                                        <input type="hidden" id="logoFileName" name="logoFileName" value="@Model.BasicInfo.LogoFileName">
                                    </div>
                                </div>

                                <!-- Favicon Upload -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mt-4 mb-2">@AdminRes["Favicon"]</label>
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-center w-full h-32 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
                                             onclick="document.getElementById('faviconInput').click()">
                                            <div class="text-center" id="faviconPreview">
                                                @if (Model.BasicInfo.FaviconUrl != null)
                                                {
                                                    var faviconUrl = $"{Model.BasicInfo.FaviconUrl}?ver={DateTime.UtcNow.Ticks}";
                                                    <img src="@faviconUrl" alt="Favicon" class="max-h-28 mx-auto rounded">
                                                }
                                                else
                                                {
                                                    <div class="flex flex-col items-center">
                                                        <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 711 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                        </svg>
                                                        <p class="text-sm text-gray-500 dark:text-gray-400">@AdminRes["ClickUploadFavicon"]</p>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <input type="file" id="faviconInput" name="faviconFile" accept="image/*" class="hidden" onchange="handleFaviconUpload(this)">
                                        <input type="hidden" id="faviconFileName" name="faviconFileName" value="@Model.BasicInfo.FaviconFileName">
                                        <div id="faviconGenerateButtonContainer" class="@(!string.IsNullOrEmpty(Model.BasicInfo.LogoFileName) ? "" : "hidden")">
                                            <button type="button" onclick="generateFaviconFromLogo()"
                                                    class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                                <i class="fas fa-magic mr-1"></i>@AdminRes["GenerateFavicon"]
                                            </button>
                                        </div>
                                    </div>
                                </div>



                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-save mr-2"></i>
                            @AdminRes["SaveBasicInfo"]
                        </button>
                    </div>
                </form>
            </div>

            <!-- Theme Settings Tab -->
            <div id="theme-settings-tab" class="tab-content hidden">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">@AdminRes["ThemeSettingsTitle"]</h3>

                <!-- Current Theme -->
                @if (Model.ThemeSettings.AvailableThemes.Any(t => t.IsActive))
                {
                    var currentTheme = Model.ThemeSettings.AvailableThemes.First(t => t.IsActive);
                    <div class="mb-6 p-4 bg-primary-50 border border-primary-200 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">@AdminRes["CurrentTheme"]</h3>
                        <div class="flex items-center space-x-4">
                            <img class="w-16 h-16 object-cover rounded border"
                                 src="@currentTheme.PreviewImage" alt="@currentTheme.Name">
                            <div>
                                <p class="font-medium">@currentTheme.Name</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">@currentTheme.Description</p>
                            </div>
                        </div>
                    </div>
                }

                <!-- Available Themes -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach (var theme in Model.ThemeSettings.AvailableThemes)
                    {

                        if (theme.IsActive)
                            continue;

                        <div class="border rounded-lg p-4 @(theme.IsActive ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20" : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500")">
                            <div class="flex items-center space-x-4">
                                <img class="w-32 h-32 object-cover rounded mb-3"
                                     src="@theme.PreviewImage" alt="@theme.Name">
                                <div>
                                    <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-1">@theme.Name</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">@theme.Description</p>
                                    @* <div class="text-xs text-gray-500 dark:text-gray-400 mb-3">
                                        <span><EMAIL></span> • <span>@theme.Author</span>
                                    </div> *@
                                </div>
                            </div>
                            <button onclick="applyTheme('@theme.Id')"
                                    class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fas fa-check mr-2"></i>
                                @AdminRes["ApplyTheme"]
                            </button>
                        </div>
                    }
                </div>
            </div>

            <!-- Component Settings Tab -->
            <div id="component-settings-tab" class="tab-content hidden">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">@AdminRes["GlobalComponentSettings"]</h3>

                <form id="component-settings-form" class="space-y-8">
                    <!-- Header Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">@AdminRes["HeaderComponent"]</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            @foreach (var variant in Model.ComponentSettings.HeaderVariants)
                            {
                                <div class="border rounded-lg p-4 @(Model.ComponentSettings.HeaderVariant == variant.Id ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20" : "border-gray-200 dark:border-gray-600")">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center">
                                            <input type="radio" id="<EMAIL>" name="headerVariant" value="@variant.Id"
                                                   @(Model.ComponentSettings.HeaderVariant == variant.Id ? "checked" : "")
                                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700">
                                            <label for="<EMAIL>" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100">@variant.Name</label>
                                        </div>
                                        @if (Model.ComponentSettings.HeaderVariant == variant.Id)
                                        {
                                            <button type="button" onclick="openComponentDataManagement('Header', '@variant.Id')"
                                                    class="ml-2 px-3 py-1.5 text-xs font-medium text-primary-600 bg-white border border-primary-200 rounded-md hover:bg-primary-50 dark:bg-gray-700 dark:text-primary-400 dark:border-primary-600 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                                <i class="fas fa-cog mr-1"></i>@AdminRes["DataManagement"]
                                            </button>
                                        }
                                    </div>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">@variant.Description</p>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Footer Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">@AdminRes["FooterComponent"]</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            @foreach (var variant in Model.ComponentSettings.FooterVariants)
                            {
                                <div class="border rounded-lg p-4 @(Model.ComponentSettings.FooterVariant == variant.Id ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20" : "border-gray-200 dark:border-gray-600")">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center">
                                            <input type="radio" id="<EMAIL>" name="footerVariant" value="@variant.Id"
                                                   @(Model.ComponentSettings.FooterVariant == variant.Id ? "checked" : "")
                                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700">
                                            <label for="<EMAIL>" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100">@variant.Name</label>
                                        </div>
                                        @if (Model.ComponentSettings.FooterVariant == variant.Id)
                                        {
                                            <button type="button" onclick="openComponentDataManagement('Footer', '@variant.Id')"
                                                    class="ml-2 px-3 py-1.5 text-xs font-medium text-primary-600 bg-white border border-primary-200 rounded-md hover:bg-primary-50 dark:bg-gray-700 dark:text-primary-400 dark:border-primary-600 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                                <i class="fas fa-cog mr-1"></i>@AdminRes["DataManagement"]
                                            </button>
                                        }
                                    </div>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">@variant.Description</p>
                                </div>
                            }
                        </div>
                    </div>


                    <!-- Cookie Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">@AdminRes["CookieComponent"]</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            @foreach (var variant in Model.ComponentSettings.CookieVariants)
                            {
                                <div class="border rounded-lg p-4 @(Model.ComponentSettings.CookieVariant == variant.Id ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20" : "border-gray-200 dark:border-gray-600")">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center">
                                            <input type="radio" id="<EMAIL>" name="cookieVariant" value="@variant.Id"
                                                   @(Model.ComponentSettings.CookieVariant == variant.Id ? "checked" : "")
                                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700">
                                            <label for="<EMAIL>" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100">@variant.Name</label>
                                        </div>
                                        @if (Model.ComponentSettings.CookieVariant == variant.Id)
                                        {
                                            <button type="button" onclick="openComponentDataManagement('CookiePolicy', '@variant.Id')"
                                                    class="ml-2 px-3 py-1.5 text-xs font-medium text-primary-600 bg-white border border-primary-200 rounded-md hover:bg-primary-50 dark:bg-gray-700 dark:text-primary-400 dark:border-primary-600 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                                <i class="fas fa-cog mr-1"></i>@AdminRes["DataManagement"]
                                            </button>
                                        }
                                    </div>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">@variant.Description</p>
                                </div>
                            }
                        </div>
                    </div>



                    <!-- Save Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-save mr-2"></i>
                            @AdminRes["SaveComponentSettings"]
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Component Data Management Modal -->
<div id="componentDataModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white" id="modalTitle">
                    @AdminRes["ComponentDataManagement"]
                </h3>
                <button type="button" onclick="closeComponentDataManagement()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-4">
                <div id="componentFormContainer" class="space-y-6">
                    <!-- Dynamic form will be rendered here -->
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="mt-6 flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button type="button" onclick="closeComponentDataManagement()"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600">
                    @AdminRes["Cancel"]
                </button>
                <button type="button" onclick="saveComponentData()"
                        class="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <i class="fas fa-save mr-2"></i>@AdminRes["Save"]
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/admin/form-field-renderer.js" asp-append-version="true"></script>
    <script src="~/js/admin/form-field-renderer-extensions.js" asp-append-version="true"></script>
}

<script>
    // Tab switching
    // Use global tab switching method
    function switchTab(tabName) {
        MlSoftSites.switchTab(tabName);
        // Update URL hash
        window.history.replaceState(null, null, '#' + tabName);
    }

    // Initialize tab from URL hash or default to first tab
    function initializeTab() {
        const hash = window.location.hash.substring(1); // Remove # from hash
        const validTabs = ['basic-info', 'theme-settings', 'component-settings'];

        if (hash && validTabs.includes(hash)) {
            switchTab(hash);
        } else {
            switchTab('basic-info'); // Default tab
        }
    }

    // Use global loading methods
    function showLoading(message) {
        MlSoftSites.showLoading(message || window.Resources?.Admin?.PleaseWaitProcessing || 'Please wait...');
    }

    function hideLoading() {
        MlSoftSites.hideLoading();
    }

    // Use global alert method - replaced with Dialog system
    function showAlert(message, type = 'success') {
        if (type === 'success') {
            Dialog.notify(message, 'success');
        } else {
            Dialog.alert(message, null, 'error');
        }
    }

    function bindRadioClick() {
        var componentForm = document.getElementById(`component-settings-form`);
        var radios = componentForm.querySelectorAll(`input[type='radio']`);

        radios.forEach(ipt => {
            var id = ipt.getAttribute('id');
            var containerDiv = ipt.parentElement.parentElement;

            // Radio button click handler
            ipt.addEventListener('click', function(){
                updateRadioStyles(this);
            });

            // Container div click handler
            containerDiv.addEventListener('click', function(e) {
                // Don't trigger if clicking directly on radio or label
                if (e.target.type === 'radio' || e.target.tagName === 'LABEL') {
                    return;
                }

                var radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                    updateRadioStyles(radio);
                }
            });

            // Add cursor pointer to container
            containerDiv.style.cursor = 'pointer';
        });
    }

    function updateRadioStyles(radioElement) {
        var groupDiv = radioElement.parentElement.parentElement.parentElement;
        var componentDivs = groupDiv.querySelectorAll(`div.border`);

        componentDivs.forEach(cd => {
            cd.classList.remove('border-primary-500', 'bg-primary-50');
            cd.classList.add('border-gray-200');
        });

        radioElement.parentElement.parentElement.classList.remove('border-gray-200');
        radioElement.parentElement.parentElement.classList.add('border-primary-500', 'bg-primary-50');
    }



    // Apply Theme Function
    async function applyTheme(themeId) {
        try {
            showLoading(window.Resources?.Admin?.ApplyingTheme || 'Applying theme...');

            const response = await fetch('/Admin/SiteSettings/apply-theme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ themeId: themeId })
            });

            const result = await response.json();

            if (result.success) {
                showAlert(result.message, 'success');
                // Reload page after successful theme application to maintain current tab
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showAlert(result.message, 'error');
            }
        } catch (error) {
            console.error('Apply theme error:', error);
            showAlert(window.Resources?.Admin?.ApplyThemeError || 'Failed to apply theme', 'error');
        } finally {
            hideLoading();
        }
    }

    // Auto-hide alerts
    document.addEventListener('DOMContentLoaded', function() {

            // Basic Info Form Handler
        document.getElementById('basic-info-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);

            // 收集多语言站点名称
            const siteNames = {};
            document.querySelectorAll('input[name^="siteNames["]').forEach(input => {
                const langMatch = input.name.match(/siteNames\[(.+?)\]/);
                if (langMatch) {
                    siteNames[langMatch[1]] = input.value;
                }
            });

            const data = {
                siteNames: siteNames,
                domain: formData.get('domain'),
                countCode:formData.get('countCode'),
                logoFileName: document.getElementById('logoFileName').value || null,
                faviconFileName: document.getElementById('faviconFileName').value || null
            };

            try {
                showLoading(window.Resources?.Admin?.SavingBasicInfo || 'Saving basic info...');

                const response = await fetch('/Admin/SiteSettings/basic-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(result.message, 'success');
                    // Reload page after successful basic info save to maintain current tab
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert(result.message, 'error');
                }
            } catch (error) {
                console.error('Save basic info error:', error);
                showAlert(window.Resources?.Admin?.SaveBasicInfoError || 'Failed to save basic info', 'error');
            } finally {
                hideLoading();
            }
        });

        // Component Settings Form Handler
        document.getElementById('component-settings-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
                headerVariant: formData.get('headerVariant'),
                footerVariant: formData.get('footerVariant'),
            };

            try {
                showLoading(window.Resources?.Admin?.SavingComponentSettings || 'Saving component settings...');

                const response = await fetch('/Admin/SiteSettings/component-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(result.message, 'success');
                    // Reload page after successful component settings save to maintain current tab
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert(result.message, 'error');
                }
            } catch (error) {
                console.error('Save component settings error:', error);
                showAlert(window.Resources?.Admin?.SaveComponentSettingsError || 'Failed to save component settings', 'error');
            } finally {
                hideLoading();
            }
        });

        bindRadioClick();

        // Initialize tab based on URL hash
        initializeTab();

        // Handle browser back/forward navigation
        window.addEventListener('hashchange', function() {
            initializeTab();
        });

        const alerts = document.querySelectorAll('[id$="-alert"]');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.classList.add('hidden');
            }, 5000);
        });

        // 图片上传处理函数
        window.handleLogoUpload = function(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                uploadImage(file, 'logo', function(filename) {
                    document.getElementById('logoFileName').value = filename;
                    // 更新预览
                    const preview = document.getElementById('logoPreview');
                    preview.innerHTML = `<img src="/upload/basic/${filename}" alt="Logo" class="max-h-28 mx-auto rounded">`;

                    // 显示favicon生成按钮
                    updateFaviconGenerateButton();
                });
            }
        };

        window.handleFaviconUpload = function(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                uploadImage(file, 'favicon', function(filename) {
                    document.getElementById('faviconFileName').value = filename;
                    // 更新预览
                    const preview = document.getElementById('faviconPreview');
                    preview.innerHTML = `<img src="/upload/basic/${filename}" alt="Favicon" class="max-h-28 mx-auto rounded">`;
                });
            }
        };

        window.generateFaviconFromLogo = async function() {
            const logoFileName = document.getElementById('logoFileName').value;
            if (!logoFileName) {
                showAlert(window.Resources?.Admin?.PleaseUploadLogoFirst || 'Please upload logo first', 'error');
                return;
            }

            try {
                showLoading(window.Resources?.Admin?.GeneratingFaviconFromLogo || 'Generating favicon from logo...');

                const response = await fetch('/Admin/SiteSettings/generate-favicon', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ logoFileName: logoFileName })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('faviconFileName').value = result.faviconFileName;
                    // 更新预览
                    const preview = document.getElementById('faviconPreview');
                    preview.innerHTML = `<img src="/upload/basic/${result.faviconFileName}" alt="Favicon" class="max-h-28 mx-auto rounded">`;
                    showAlert(window.Resources?.Admin?.FaviconGeneratedSuccess || 'Favicon generated successfully', 'success');
                } else {
                    showAlert(result.message, 'error');
                }
            } catch (error) {
                console.error('Generate favicon error:', error);
                showAlert(window.Resources?.Admin?.GenerateFaviconError || 'Failed to generate favicon', 'error');
            } finally {
                hideLoading();
            }
        };

        // Use global upload method
        function uploadImage(file, type, callback) {
            const uploadingMessage = type === 'logo' ?
                (window.Resources?.Admin?.UploadingLogo || 'Uploading logo...') :
                (window.Resources?.Admin?.UploadingFavicon || 'Uploading favicon...');

            MlSoftSites.uploadFile(file, buildMultilingualUrl("upload-image", "SiteSettings"), {
                additionalData: { type: type },
                uploadingMessage: uploadingMessage,
                successMessage: window.Resources?.Admin?.UploadSuccessful || 'Upload successful',
                errorMessage: window.Resources?.Admin?.UploadFailed || 'Upload failed',
                showResult: true
            })
            .then(result => {
                callback(result.fileName);
            })
            .catch(error => {
                console.error('Upload error:', error);
            });
        }

        function updateFaviconGenerateButton() {
            const logoFileName = document.getElementById('logoFileName').value;
            const buttonContainer = document.getElementById('faviconGenerateButtonContainer');

            if (logoFileName) {
                buttonContainer.classList.remove('hidden');
            } else {
                buttonContainer.classList.add('hidden');
            }
        }

    });

    // Component Data Management Functions
    let currentComponentData = {
        componentId: '',
        variantId: '',
        formRenderer: null
    };

    // Open component data management modal
    async function openComponentDataManagement(componentId, variantId) {
        try {
            showLoading(window.Resources?.Admin?.LoadingComponentData || 'Loading component data...');

            // Store current component info
            currentComponentData.componentId = componentId;
            currentComponentData.variantId = variantId;



            // Get component variant info
            const variantUrl = `${getLanguagePrefix()}/api/Components/${componentId}/variants/${variantId}`;
            const variantResponse = await fetch(variantUrl);

            if (!variantResponse.ok) {
                throw new Error(`Failed to load component variant: ${variantResponse.status}`);
            }

            const variantInfo = await variantResponse.json();

            // Get existing component data
            const dataUrl = `${getLanguagePrefix()}/Admin/SiteSettings/get-component-data/${componentId}/${variantId}`;
            let existingData = {};

            try {
                const dataResponse = await fetch(dataUrl);
                if (dataResponse.ok) {
                    const dataResult = await dataResponse.json();
                    if (dataResult.success) {
                        existingData = dataResult.data || {};
                    }
                }
            } catch (error) {
                console.log('No existing data found, using empty object');
            }

            // Render form using existing form renderer
            await renderComponentDataForm(variantInfo, existingData);

            // Show modal
            document.getElementById('componentDataModal').classList.remove('hidden');

        } catch (error) {
            console.error('Error opening component data management:', error);
            Dialog.error(window.Resources?.Admin?.LoadComponentDataError || 'Failed to load component data');
        } finally {
            hideLoading();
        }
    }

    // Render component data form
    async function renderComponentDataForm(variantInfo, existingData) {
        const container = document.getElementById('componentFormContainer');

        if (!variantInfo.formFields || variantInfo.formFields.length === 0) {
            container.innerHTML = `
                <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                ${window.Resources?.Admin?.NoFormFieldsConfigured || 'No form fields configured'}
                            </h3>
                            <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                                ${window.Resources?.Admin?.ComponentFormFieldsNotConfigured || 'This component has no form fields configured in its variants.json file.'}
                            </p>
                        </div>
                    </div>
                </div>
            `;
            return;
        }

        // Create localization object for form renderer (similar to PageManage/Edit.cshtml)
        const formL10n = {
            selectComponentToEdit: window.Resources?.Admin?.SelectComponentToEdit || 'Select Component To Edit',
            componentType: window.Resources?.Admin?.ComponentType || 'Component Type',
            template: window.Resources?.Admin?.Template || 'Template',
            save: window.Resources?.Admin?.Save || 'Save',
            cancel: window.Resources?.Admin?.Cancel || 'Cancel',

            // Form groups
            basicInfo: window.Resources?.Shared?.FormGroups_BasicInfo || 'Basic Info',
            mediaContent: window.Resources?.Shared?.FormGroups_MediaContent || 'Media Content',
            layoutSettings: window.Resources?.Shared?.FormGroups_LayoutSettings || 'Layout Settings',
            resources: window.Resources?.Shared?.FormGroups_Resources || 'Resources',
            seoSettings: window.Resources?.Shared?.FormGroups_SEOSettings || 'SEO Settings',

            // File upload
            selectFile: window.Resources?.Shared?.FileUpload_SelectFile || 'Select File',
            clickToUpload: window.Resources?.Shared?.FileUpload_ClickToUpload || 'Click To Upload',
            orDragDrop: window.Resources?.Shared?.FileUpload_OrDragDrop || 'Or Drag Drop',
            deleteFile: window.Resources?.Shared?.FileUpload_DeleteFile || 'Delete File',
            uploadSuccess: window.Resources?.Shared?.FileUpload_UploadSuccess || 'Upload Success',
            uploadError: window.Resources?.Shared?.FileUpload_UploadError || 'Upload Error',

            defaultGroup: window.Resources?.Shared?.FormGroups_Other || 'Other',
            enterText: window.Resources?.Shared?.EnterText || 'Enter Text'
        };

        // Initialize form renderer
        currentComponentData.formRenderer = new FormFieldRenderer(container, formL10n);

        // Render form fields
        currentComponentData.formRenderer.render(variantInfo.formFields);

        // Load existing data
        if (existingData && Object.keys(existingData).length > 0) {
            currentComponentData.formRenderer.setFormData(existingData);
        }
    }

    // Close component data management modal
    function closeComponentDataManagement() {
        document.getElementById('componentDataModal').classList.add('hidden');

        // Clean up
        const container = document.getElementById('componentFormContainer');
        container.innerHTML = '';
        currentComponentData.formRenderer = null;
    }

    // Save component data
    async function saveComponentData() {
        try {
            if (!currentComponentData.formRenderer) {
                Dialog.error(window.Resources?.Admin?.NoFormDataToSave || 'No form data to save');
                return;
            }

            showLoading(window.Resources?.Admin?.SavingComponentData || 'Saving component data...');

            // Get form data
            const formData = currentComponentData.formRenderer.getFormData();

            // Save to server
            const saveUrl = `${getLanguagePrefix()}/Admin/SiteSettings/save-component-data`;
            const response = await fetch(saveUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    componentId: currentComponentData.componentId,
                    variantId: currentComponentData.variantId,
                    data: formData
                })
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.ComponentDataSaved || 'Component data saved successfully', 'success');
                closeComponentDataManagement();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.SaveComponentDataError || 'Failed to save component data');
            }

        } catch (error) {
            console.error('Error saving component data:', error);
            Dialog.error(window.Resources?.Admin?.SaveComponentDataError || 'Failed to save component data');
        } finally {
            hideLoading();
        }
    }

    // Make functions global
    window.openComponentDataManagement = openComponentDataManagement;
    window.closeComponentDataManagement = closeComponentDataManagement;
    window.saveComponentData = saveComponentData;
</script>