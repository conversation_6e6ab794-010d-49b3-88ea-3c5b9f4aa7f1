@model MlSoft.Sites.Model.Entities.Pages.PageConfiguration
@using Microsoft.AspNetCore.Hosting
@using Microsoft.AspNetCore.Html
@using Microsoft.Extensions.Hosting
@using MlSoft.Sites.Model.Entities.Pages
@using MlSoft.Sites.Web.Extensions
@using System.Text.Json

@{

    Layout = ViewData["Layout"]?.ToString() ?? "_Layout";

  
    var components = ViewData["PageComponents"] as List<PageComponentConfig> ?? new List<PageComponentConfig>();

    // 收集需要预加载的媒体资源
    var preloadMedia = new List<(string url, string type, string asType)>();
    var componentJsonData = ViewData["ComponentJsonData"] as Dictionary<string, JObject>;

    // 只预加载前3个组件的媒体资源（通常包含首屏内容）
    var topComponents = components.Where(c => c.IsVisible).OrderBy(c => c.DisplayOrder).Take(3);

    foreach (var component in topComponents)
    {
        var componentKey = component.ComponentDefinitionId + "_" + component.IdNo;
        var jsonData = componentJsonData?.GetValueOrDefault(componentKey);

        if (jsonData != null)
        {
            // Hero组件媒体预加载
            if (component.ComponentDefinitionId.Contains("Hero"))
            {

                if (jsonData["BackgroundImage"] != null)
                {
                    var imageUrl = jsonData["BackgroundImage"]?.ToString();

                    
                    if (!string.IsNullOrEmpty(imageUrl))
                    {
                        preloadMedia.Add((imageUrl, "image", "image"));
                    }
                }


                if (jsonData["BackgroundVideo"] != null)
                {
                    var videoUrl = jsonData["BackgroundVideo"]?.ToString();

                    if (!string.IsNullOrEmpty(videoUrl))
                    {
                        preloadMedia.Add((videoUrl, "video", "video"));
                    }
                }
            }

            // Media组件媒体预加载  
            if (component.ComponentDefinitionId.Contains("Media") && jsonData["MediaItems"] != null)
            {
                var arrItems = (JArray)jsonData["MediaItems"];

                if (arrItems != null && arrItems.Count() != 0)
                {
                    // 只预加载前2个媒体项
                    foreach (var item in arrItems.Take(2))
                    {
                        var url = item["Url"]?.ToString();
                        if (!string.IsNullOrEmpty(url))
                        {
                            var itemType = item["Type"]?.ToString();
                            var asType = itemType switch
                            {
                                "video" => "video",
                                "image" => "image",
                                _ => "image"
                            };

                            preloadMedia.Add((url, itemType, asType));
                        }
                    }
                }
            }
        }
    }
}

@* 将媒体资源预加载标签添加到 head 部分 *@
@section HeadPreLoad {
    @if (preloadMedia.Any())
    {
        @* 关键资源预加载 *@
        @foreach (var media in preloadMedia.Take(6)) // 限制预加载数量，避免过度预加载
        {
            @if (media.asType == "image")
            {
                <link rel="preload" href="@media.url" as="image">
            }
            else if (media.asType == "video")
            {
                <link rel="preload" href="@media.url" as="video">
            }
        }

        var uniqueDomains = preloadMedia
                 .Select(m => new Uri(m.url, UriKind.RelativeOrAbsolute))
                 .Where(uri => uri.IsAbsoluteUri && !uri.Host.Equals(Context.Request.Host.Host, StringComparison.OrdinalIgnoreCase))
                 .Select(uri => uri.GetLeftPart(UriPartial.Authority))
                 .Distinct()
                 .Take(3);
        
        @* 预连接到可能的CDN域名 *@
        @foreach (var domain in uniqueDomains)
        {
            <link rel="preconnect" href="@domain">
            <link rel="dns-prefetch" href="@domain">
        }
    }
}



<div class="dynamic-page grid grid-cols-1 md:grid-cols-12 gap-4" data-page-key="@ViewData["PageKey"]">
    @{
        var visibleComponents = components.Where(c => c.IsVisible).OrderBy(c => c.DisplayOrder).ToList();
        var isInRow = false; // 标记是否在行容器内
        var previousColumnSpan = 0; // 记录上一个组件的列跨度
    }
    
    @for (int i = 0; i < visibleComponents.Count; i++)
    {
        var component = visibleComponents[i];
        var columnSpan = component.ColumnSpan > 0 ? component.ColumnSpan : 12;
        var isFullWidth = columnSpan == 12;
        
        // 如果当前组件是全宽组件
        if (isFullWidth)
        {
            // 如果之前在行容器内，需要先关闭行容器
            if (isInRow)
            {
                @:</div>
                isInRow = false;
            }
        } 
        else
        {
             // 如果当前组件不是全宽组件
            // 检查是否需要开始新的行容器
            if (!isInRow)
            {
                @:<div class="max-w-7xl mx-auto col-span-12 grid grid-cols-1 md:grid-cols-12 gap-4">
                isInRow = true;
            }
        }

            var columnSpanClass = $"md:col-span-{columnSpan}";

            <div class="page-component @columnSpanClass" data-component="@component.ComponentDefinitionId" data-template="@component.TemplateKey" data-order="@component.DisplayOrder">
                @{
                    var componentKey = component.ComponentDefinitionId + "_" + component.IdNo;
                    var jsonData = componentJsonData?.GetValueOrDefault(componentKey) ?? default;
                    var componentName = component.ComponentDefinitionId.Replace("_component", "");//.ToTitleCase();
                    var templateKey = !string.IsNullOrEmpty(component.TemplateKey) ? component.TemplateKey : "Default";
                    var idNo = component.IdNo;
                }
                
                @if (jsonData!=null)
                {
                    @try
                    {
                      @await Component.InvokeAsync(componentName, new { model = jsonData, variant = templateKey })

                    }
                    catch (Exception ex)
                    {
                        <div class="component-error bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded">
                            <p><strong>Component Error:</strong> @ex.Message</p>
                            <p><small>ComponentId: @component.ComponentDefinitionId, Template: @templateKey</small></p>
                        </div>
                    }
                }
                else
                {
                    <div class="component-error bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 px-4 py-3 rounded">
                        <p><strong>Component Warning:</strong> No data available for @componentName</p>
                        <p><small>ComponentId: @component.ComponentDefinitionId, Template: @templateKey</small></p>
                    </div>
                }
            </div>


        // 更新状态
        previousColumnSpan = columnSpan;
  }
        
        
     

    
    @* 如果最后还在行容器内，需要关闭它 *@
    @if (isInRow)
    {
        @:</div>
    }

</div>