@model MlSoft.Sites.Web.ViewModels.Components.BreadcrumbComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
	// Extract data from ViewModel with null-safe defaults
	var items = Model?.Items ?? new List<BreadcrumbItem>();

	// Create unique IDs for the component
	var uniqueId = JObjectHelper.GenerateId("breadcrumb");

}

<!-- Flowbite Breadcrumb Component -->
<nav class="text-gray-700 dark:bg-gray-800 aria-label="Breadcrumb" id="@uniqueId">
	<div class="mx-auto max-w-7xl p-4">
		<ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">

			<!-- Breadcrumb Items -->
			@for (int i = 0; i < items.Count; i++)
			{
				var item = items[i];
				var isLast = i == items.Count - 1;

				<li>
					<div class="flex items-center">

						@if (i == 0)
						{
							<svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
								<path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
							</svg>


						}
						else
						{
							<svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
								<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
							</svg>
						}




						@if (isLast || string.IsNullOrEmpty(item.Url))
						{
							<span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400" aria-current="page">@item.Text</span>
						}
						else
						{
							<a href="@item.Url" class="ms-1 text-sm font-medium text-gray-700 hover:text-primary-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">@item.Text</a>
						}
					</div>
				</li>
			}
		</ol>

	</div>
</nav>