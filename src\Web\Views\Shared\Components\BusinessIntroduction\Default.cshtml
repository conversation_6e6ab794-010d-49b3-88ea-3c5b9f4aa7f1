@model MlSoft.Sites.Web.ViewModels.Components.BusinessIntroductionComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<FormResource> FormRes

@{
	// Extract data from ViewModel with null-safe defaults
	var businessList = (Model?.BusinessItems == null) ? new List<BusinessItem>() : Model?.BusinessItems?.Take(4).ToList();
	var title = string.IsNullOrEmpty(Model?.TitleText) ? SharedRes["BusinessIntroduction_Title"] : Model?.TitleText;
	var description = Model?.Description;
	var columnsDesktop = businessList?.Count;
	var columnsTablet = 2;
	var columnsMobile =  1;

	var buttonText = SharedRes["Button_ViewDetail"].ToString();

	

	// Generate unique ID for the component
	var uniqueId = JObjectHelper.GenerateId("business-intro");

	// Generate grid classes
	var gridClass = $"grid gap-6 md:grid-cols-{columnsTablet} lg:grid-cols-{columnsDesktop}";
	if (columnsMobile == 1) gridClass = "grid gap-6 md:grid-cols-" + columnsTablet + " lg:grid-cols-" + columnsDesktop;

}

<section id="@uniqueId" class="py-16 lg:py-24 bg-white dark:bg-gray-800">
	<div class="container  max-w-7xl  mx-auto px-4">
		<div class="text-center mb-12">
			<h2 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
				@title
			</h2>
			<p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
				@description
			</p>
		</div>

		<div class="@gridClass">
			@if (businessList?.Count != 0)
			{
				@foreach (var business in businessList)
				{
					<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow p-6 h-full flex flex-col">
						@if (!string.IsNullOrEmpty(business.Icon))
						{
							<div class="mb-4">
								<i class="fas <EMAIL> text-4xl text-primary-600 dark:text-primary-400"></i>
							</div>
						}

						<h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">@business.Title</h3>

						<p class="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed flex-grow">@business.Description</p>

						@if (!string.IsNullOrEmpty(business.Features))
						{
							var listFeatures = business.Features.Split("\n");
							if (listFeatures.Length != 0)
							{
								<ul class="space-y-2 mb-6">
									@foreach (var feature in listFeatures)
									{
										<li class="text-sm text-gray-600 dark:text-gray-300 flex items-center">
											<div class="w-1.5 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full mr-2 flex-shrink-0"></div>
											@feature
										</li>
									}
								</ul>

							}
						}

						@if (!string.IsNullOrEmpty(business.ButtonUrl))
						{
							<div class="mt-auto">
								<a href="@business.ButtonUrl" class="text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900 w-full inline-flex justify-center items-center">
									@(!string.IsNullOrEmpty(business.ButtonText) ? business.ButtonText : buttonText)
								</a>
							</div>
						}
					</div>
				}
			}
		</div>
	</div>
</section>