{"ComponentId": "BusinessIntroduction", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "事业介绍", "en": "Business Introduction", "ja": "事業紹介"}, "Descriptions": {"zh": "展示企业各个业务部门和服务领域的组件", "en": "Component to showcase company business divisions and service areas", "ja": "企業の各事業部門とサービス領域を紹介するコンポーネント"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "collapsed": true, "layout": "inline"}, "validation": {"maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@AdminResource:BusinessInfoTitle", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "collapsed": true, "layout": "inline"}, "validation": {"required": false, "maxLength": 500}}, {"name": "BusinessItems", "type": "repeater", "label": "@SharedResource:BusinessIntroduction_BusinessItems", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "collapsed": true, "order": 11}, "template": {"fields": [{"name": "Icon", "type": "select", "label": "@FormResource:FormFields_Icon", "display": {"layout": "inline", "width": "col-span-12"}, "options": [{"value": "code", "label": "code"}, {"value": "cloud", "label": "cloud"}, {"value": "microchip", "label": "microchip"}, {"value": "shield", "label": "shield"}, {"value": "cog", "label": "cog"}, {"value": "chart", "label": "chart"}, {"value": "users", "label": "users"}, {"value": "globe", "label": "globe"}]}, {"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": true, "maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": true, "maxLength": 300}}, {"name": "Features", "type": "multilingual-textarea", "label": "@SharedResource:BusinessItem_Features", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": false}}, {"name": "ButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_ButtonText", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": false, "maxLength": 50}}, {"name": "ButtonUrl", "type": "text", "label": "@FormResource:FormFields_Link", "display": {"width": "col-span-12"}, "validation": {"required": false, "maxLength": 200}}]}, "validation": {"required": false, "minItems": 1, "maxItems": 8}}]}