@model MlSoft.Sites.Web.ViewModels.Components.CSRSectionComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedR<PERSON>
@inject IStringLocalizer<AdminResource> AdminRes

@{
    // Extract data from ViewModel with null-safe defaults
    var title = string.IsNullOrEmpty(Model?.Title) ? AdminRes["CSRActivities"] : Model?.Title;
    var description = Model?.Description;
    var backgroundClass = Model?.BackgroundColor == "muted" ? "bg-gray-50 dark:bg-gray-900/50" : "bg-white dark:bg-gray-800";
    var columnsDesktop = Model?.ColumnsDesktop ?? 4;
    var columnsTablet = Model?.ColumnsTablet ?? 2;
    var buttonVariant = Model?.ButtonVariant ?? "primary";
    
    var goalsTitle = Model?.GoalsTitle;
    var goalsButtonText = Model?.GoalsButtonText;
    var csrImageAlt = string.IsNullOrEmpty(Model?.CSRImageAlt) ? goalsTitle : Model?.CSRImageAlt;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("csr-section");

    // Generate grid classes
    var gridClass = $"grid gap-6 md:grid-cols-{columnsTablet} lg:grid-cols-{columnsDesktop}";

    // Button classes based on variant
    var buttonClass = buttonVariant switch
    {
        "primary" => "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800",
        "outline" => "text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900",
        _ => "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
    };



    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "/images/placeholder-sustainability.jpg" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-16 lg:py-24 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">@title</h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
                @description
            </p>
        </div>

        @if (Model?.Initiatives?.Any() == true)
        {
            <div class="@gridClass mb-12">
                @foreach (var initiative in Model.Initiatives)
                {
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-6 text-center h-full">
                        @if (!string.IsNullOrEmpty(initiative.Icon))
                        {
                            <div class="mb-4">
                                <i class="fas <EMAIL> text-4xl text-primary-600 dark:text-primary-400"></i>
                            </div>
                        }
                        
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">@initiative.Title</h3>
                        
                        <p class="text-gray-600 dark:text-gray-300 leading-relaxed">@initiative.Description</p>
                    </div>
                }
            </div>
        }

        @if (!string.IsNullOrEmpty(Model?.Goals) || !string.IsNullOrEmpty(Model?.CSRImage))
        {
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-8 lg:p-12">
                <div class="grid lg:grid-cols-2 gap-8 items-center">
                    @if (!string.IsNullOrEmpty(Model?.Goals))
                    {

                        var listGoals = Model?.Goals?.Split("\n");

                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">@goalsTitle</h3>
                            @if (Model?.Goals?.Any() == true)
                            {
                                <ul class="space-y-3 text-gray-600 dark:text-gray-300 mb-6">
                                    @foreach (var goal in listGoals)
                                    {
                                        <li class="flex items-start">
                                            <div class="w-2 h-2 bg-primary-600 dark:bg-primary-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                            @goal
                                        </li>
                                    }
                                </ul>
                            }
                            @if (!string.IsNullOrEmpty(Model?.GoalsButtonUrl))
                            {
                                <a href="@Model.GoalsButtonUrl" class="@buttonClass">
                                    @goalsButtonText
                                </a>
                            }
                            else
                            {
                                <button type="button" class="@buttonClass">
                                    @goalsButtonText
                                </button>
                            }
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(Model?.CSRImage))
                    {
                        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-6">
                            <img src="@ProcessFilePath(Model?.CSRImage)" 
                                 alt="@csrImageAlt" 
                                 class="w-full h-64 object-cover rounded-lg"
                                 loading="lazy" />
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</section>