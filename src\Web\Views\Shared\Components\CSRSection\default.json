{"ComponentId": "CSRSection", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "CSR与可持续发展", "en": "CSR & Sustainability", "ja": "CSR・サステナビリティ"}, "Descriptions": {"zh": "展示企业社会责任和可持续发展举措的组件", "en": "Component to showcase corporate social responsibility and sustainability initiatives", "ja": "企業の社会的責任とサステナビリティの取り組みを紹介するコンポーネント"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "collapsed": true, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "collapsed": true, "layout": "inline"}, "validation": {"required": false, "maxLength": 500}}, {"name": "BackgroundColor", "type": "select", "label": "@FormResource:FormFields_BackgroundColor", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 3}, "options": [{"value": "white", "label": "@FormResource:FormFields_BackgroundColor_White"}, {"value": "muted", "label": "@FormResource:FormFields_BackgroundColor_Muted"}], "validation": {"required": false}}, {"name": "ColumnsDesktop", "type": "select", "label": "@FormResource:FormFields_ColumnsDesktop", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 4}, "options": [{"value": "2", "label": "2"}, {"value": "3", "label": "3"}, {"value": "4", "label": "4"}], "validation": {"required": false}}, {"name": "ColumnsTablet", "type": "select", "label": "@FormResource:FormFields_ColumnsTablet", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 5}, "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}], "validation": {"required": false}}, {"name": "ButtonV<PERSON>t", "type": "select", "label": "@SharedResource:FormGroups_ButtonSettings", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 9}, "options": [{"value": "primary", "label": "@FormResource:ButtonStyle_Primary"}, {"value": "outline", "label": "@FormResource:ButtonStyle_Outline"}], "validation": {"required": false}}, {"name": "Initiatives", "type": "repeater", "label": "@AdminResource:CSRActivities", "display": {"group": "@AdminResource:CSRActivities", "width": "col-span-12", "collapsed": true, "order": 10}, "template": {"fields": [{"name": "Icon", "type": "select", "label": "@FormResource:FormFields_Icon", "display": {"width": "col-span-12"}, "options": [{"value": "leaf", "label": "leaf"}, {"value": "heart", "label": "heart"}, {"value": "recycle", "label": "recycle"}, {"value": "users", "label": "users"}, {"value": "globe", "label": "globe"}, {"value": "shield", "label": "shield"}, {"value": "tree", "label": "tree"}, {"value": "handshake", "label": "handshake"}]}, {"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": true, "maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": true, "maxLength": 300}}]}, "validation": {"required": false, "minItems": 1, "maxItems": 6}}, {"name": "GoalsTitle", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_Goals", "width": "col-span-12", "order": 11, "layout": "inline", "collapsed": true}, "validation": {"required": false, "maxLength": 100}}, {"name": "Goals", "type": "multilingual-textarea", "label": "@SharedResource:FormGroups_Goals", "display": {"group": "@SharedResource:FormGroups_Goals", "width": "col-span-12", "order": 12, "collapsed": true, "layout": "inline"}, "validation": {"required": false}}, {"name": "GoalsButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_ButtonText", "display": {"group": "@SharedResource:FormGroups_Goals", "width": "col-span-12", "order": 13, "collapsed": true, "layout": "inline"}, "validation": {"required": false, "maxLength": 50}}, {"name": "GoalsButtonUrl", "type": "text", "label": "@FormResource:FormFields_ButtonUrl", "display": {"group": "@SharedResource:FormGroups_Goals", "width": "col-span-12", "layout": "inline", "collapsed": true, "order": 14}, "validation": {"required": false, "maxLength": 200}}, {"name": "CSRImage", "type": "image", "label": "@FormResource:FormFields_Image", "display": {"group": "@SharedResource:FormGroups_Goals", "width": "col-span-12", "collapsed": true, "order": 15}, "fileConfig": {"folder": "csr", "types": ["image/*"], "maxSize": "5MB", "multiple": false, "preview": true}, "validation": {"required": false}}, {"name": "CSRImageAlt", "type": "multilingual-text", "label": "@FormResource:FormFields_AltText", "display": {"group": "@SharedResource:FormGroups_Goals", "width": "col-span-12", "collapsed": true, "order": 16, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}]}