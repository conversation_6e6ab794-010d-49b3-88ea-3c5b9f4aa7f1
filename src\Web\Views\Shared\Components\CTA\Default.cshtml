@model MlSoft.Sites.Web.ViewModels.Components.CTAComponentViewModel
@using MlSoft.Sites.Web.Helpers

@{
    // 从ViewModel中获取数据
    var title = Model?.Title ?? "";
    var subtitle = Model?.Subtitle ?? "";
    var description = Model?.Description ?? "";

    // Primary button settings
    var primaryButtonText = Model?.PrimaryButtonText ?? "";
    var primaryButtonUrl = Model?.PrimaryButtonUrl ?? "";
    var primaryButtonStyle = string.IsNullOrEmpty(Model?.PrimaryButtonStyle) ? "primary" : Model?.PrimaryButtonStyle;
    var primaryButtonOpenNewTab = Model?.PrimaryButtonOpenNewTab ?? false;

    // Secondary button settings
    var secondaryButtonText = Model?.SecondaryButtonText ?? "";
    var secondaryButtonUrl = Model?.SecondaryButtonUrl ?? "";
    var secondaryButtonStyle = string.IsNullOrEmpty(Model?.SecondaryButtonStyle) ? "secondary" : Model?.SecondaryButtonStyle;
    var secondaryButtonOpenNewTab = Model?.SecondaryButtonOpenNewTab ?? false;

    // Visual settings
    var backgroundColor = string.IsNullOrEmpty(Model?.BackgroundColor) ? "primary" : Model?.BackgroundColor;
    var backgroundImage = Model?.BackgroundImage ?? "";
    var textColor = string.IsNullOrEmpty(Model?.TextColor) ? "white" : Model?.TextColor;
    var textAlignment = string.IsNullOrEmpty(Model?.TextAlignment) ? "center" : Model?.TextAlignment;
    var size = string.IsNullOrEmpty(Model?.Size) ? "medium" : Model?.Size;

    // Layout settings
    var layout = string.IsNullOrEmpty(Model?.Layout) ? "horizontal" : Model?.Layout;
    var spacing = string.IsNullOrEmpty(Model?.Spacing) ? "normal" : Model?.Spacing;
    var borderRadius = string.IsNullOrEmpty(Model?.BorderRadius) ? "normal" : Model?.BorderRadius;
    var showShadow = Model?.ShowShadow ?? true;

    // Animation settings
    var animationEnabled = Model?.AnimationEnabled ?? true;
    var animationType = string.IsNullOrEmpty(Model?.AnimationType) ? "fadeIn" : Model?.AnimationType;

    // Background overlay settings
    var showOverlay = Model?.ShowOverlay ?? true;
    var overlayOpacity = string.IsNullOrEmpty(Model?.OverlayOpacity) ? "0.7" : Model?.OverlayOpacity;

    // 生成CSS类
    var textAlignClass = JObjectHelper.GetTextAlignmentClass(textAlignment);
    var animationClass = animationEnabled ? "transition-all duration-500 ease-in-out" : "";
    var uniqueId = JObjectHelper.GenerateId("cta");

    // Background color classes
    var backgroundColorClass = backgroundColor switch
    {
        "primary" => "bg-primary-600 dark:bg-primary-700",
        "secondary" => "bg-secondary-600 dark:bg-secondary-700",
        "accent" => "bg-accent-600 dark:bg-accent-700",
        "gray" => "bg-gray-600 dark:bg-gray-700",
        "transparent" => "bg-transparent",
        _ => "bg-primary-600 dark:bg-primary-700"
    };

    // Text color classes
    var textColorClass = textColor switch
    {
        "white" => "text-white",
        "black" => "text-black",
        "primary" => "text-primary-900 dark:text-primary-100",
        "gray" => "text-gray-700 dark:text-gray-300",
        _ => "text-white"
    };

    // Size classes
    var sizeClass = size switch
    {
        "small" => "py-8 px-4",
        "medium" => "py-12 px-6",
        "large" => "py-16 px-8",
        _ => "py-12 px-6"
    };

    // Spacing classes
    var spacingClass = spacing switch
    {
        "tight" => "space-y-4",
        "normal" => "space-y-6",
        "loose" => "space-y-8",
        _ => "space-y-6"
    };

    // Border radius classes
    var borderRadiusClass = borderRadius switch
    {
        "none" => "rounded-none",
        "small" => "rounded-md",
        "normal" => "rounded-lg",
        "large" => "rounded-xl",
        "full" => "rounded-full",
        _ => "rounded-lg"
    };

    // Shadow classes
    var shadowClass = showShadow ? "shadow-xl dark:shadow-2xl" : "";

    // Layout classes
    var layoutClass = layout == "vertical" ? "flex-col" : "flex-col lg:flex-row lg:items-center lg:justify-between";

    // Overlay opacity classes
    var overlayOpacityClass = overlayOpacity switch
    {
        "0.1" => "bg-opacity-10",
        "0.2" => "bg-opacity-20",
        "0.3" => "bg-opacity-30",
        "0.4" => "bg-opacity-40",
        "0.5" => "bg-opacity-50",
        "0.6" => "bg-opacity-60",
        "0.7" => "bg-opacity-70",
        "0.8" => "bg-opacity-80",
        "0.9" => "bg-opacity-90",
        _ => "bg-opacity-70"
    };

    var hasBackground = !string.IsNullOrEmpty(backgroundImage);

    string GetButtonClass(string type) => JObjectHelper.GetButtonClass(type);
    string ProcessFilePath(string filePath) => string.IsNullOrEmpty(filePath) ? "" : filePath.StartsWith("/") ? filePath : $"/{filePath}";
    string GetTargetAttribute(bool openNewTab) => openNewTab ? "target=\"_blank\" rel=\"noopener noreferrer\"" : "";
}

<section class="cta-section relative @backgroundColorClass @borderRadiusClass @shadowClass @sizeClass @animationClass overflow-hidden"
         id="@uniqueId"
         data-animation="@animationType">

    @* 背景图片层 *@
    @if (!string.IsNullOrEmpty(backgroundImage))
    {
        <div class="absolute inset-0 z-0">
            <img src="@ProcessFilePath(backgroundImage)"
                 alt=""
                 class="w-full h-full object-cover @(animationEnabled ? "transition-transform duration-700 hover:scale-105" : "")"
                 loading="lazy" />
        </div>
    }

    @* 遮罩层 *@
    @if (showOverlay && hasBackground)
    {
        <div class="absolute inset-0 bg-black @overlayOpacityClass z-5"></div>
    }

    @* 内容层 *@
    <div class="relative z-10 max-w-6xl mx-auto">
        <div class="flex @layoutClass @spacingClass @textAlignClass">

            @* 文本内容区域 *@
            <div class="@(layout == "horizontal" ? "lg:flex-1" : "w-full") @spacingClass">
                @if (!string.IsNullOrEmpty(title))
                {
                    <h2 class="text-2xl md:text-4xl font-bold @textColorClass mb-4 @(animationEnabled ? "transition-all duration-500" : "")">
                        @title
                    </h2>
                }

                @if (!string.IsNullOrEmpty(subtitle))
                {
                    <h3 class="text-lg md:text-xl @(hasBackground ? "text-gray-200" : textColorClass == "text-white" ? "text-gray-200" : "text-gray-600 dark:text-gray-300") mb-4 @(animationEnabled ? "transition-all duration-500" : "")">
                        @subtitle
                    </h3>
                }

                @if (!string.IsNullOrEmpty(description))
                {
                    <p class="text-base md:text-lg @(hasBackground ? "text-gray-300" : textColorClass == "text-white" ? "text-gray-300" : "text-gray-600 dark:text-gray-400") @(animationEnabled ? "transition-all duration-500" : "")">
                        @description
                    </p>
                }
            </div>

            @* 按钮区域 *@
            @if (!string.IsNullOrEmpty(primaryButtonText) || !string.IsNullOrEmpty(secondaryButtonText))
            {
                <div class="@(layout == "horizontal" ? "lg:flex-shrink-0 lg:ml-8" : "w-full")">
                    <div class="flex flex-col sm:flex-row gap-4 @(textAlignment == "center" ? "justify-center" : textAlignment == "right" ? "justify-end" : layout == "horizontal" ? "justify-end" : "justify-start") items-center">

                        @if (!string.IsNullOrEmpty(primaryButtonText))
                        {
                            @if (!string.IsNullOrEmpty(primaryButtonUrl))
                            {
                                <a href="@primaryButtonUrl"
                                   @Html.Raw(GetTargetAttribute(primaryButtonOpenNewTab))
                                   class="@GetButtonClass(primaryButtonStyle) @(animationEnabled ? "hover:scale-105 hover:shadow-lg transform transition-all duration-300" : "")">
                                    @primaryButtonText
                                </a>
                            }
                            else
                            {
                                <button class="@GetButtonClass(primaryButtonStyle) @(animationEnabled ? "hover:scale-105 hover:shadow-lg transform transition-all duration-300" : "")">
                                    @primaryButtonText
                                </button>
                            }
                        }

                        @if (!string.IsNullOrEmpty(secondaryButtonText))
                        {
                            @if (!string.IsNullOrEmpty(secondaryButtonUrl))
                            {
                                <a href="@secondaryButtonUrl"
                                   @Html.Raw(GetTargetAttribute(secondaryButtonOpenNewTab))
                                   class="@GetButtonClass(secondaryButtonStyle) @(animationEnabled ? "hover:scale-105 transform transition-all duration-300" : "")">
                                    @secondaryButtonText
                                </a>
                            }
                            else
                            {
                                <button class="@GetButtonClass(secondaryButtonStyle) @(animationEnabled ? "hover:scale-105 transform transition-all duration-300" : "")">
                                    @secondaryButtonText
                                </button>
                            }
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</section>

@* 自定义动画CSS *@
@if (animationEnabled)
{
    <style>
        @@keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @@keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @@keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @@keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .cta-section[data-animation="fadeIn"] {
            animation: fadeIn 0.8s ease-out forwards;
        }

        .cta-section[data-animation="slideInLeft"] {
            animation: slideInLeft 0.8s ease-out forwards;
        }

        .cta-section[data-animation="slideInRight"] {
            animation: slideInRight 0.8s ease-out forwards;
        }

        .cta-section[data-animation="scaleIn"] {
            animation: scaleIn 0.8s ease-out forwards;
        }

        @@media (prefers-reduced-motion: reduce) {
            .cta-section {
                animation: none !important;
            }
            .cta-section * {
                transition: none !important;
                transform: none !important;
            }
        }
    </style>
}