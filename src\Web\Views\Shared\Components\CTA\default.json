{"ComponentId": "CTA", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "默认行动号召", "en": "Default Call to Action", "ja": "デフォルトCTA"}, "Descriptions": {"zh": "标准行动号召区域布局", "en": "Standard call to action section layout", "ja": "標準CTAセクションレイアウト"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@SharedResource:FormFields_TitleHelpText"}, "validation": {"required": true, "maxLength": 100, "minLength": 1}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@SharedResource:FormFields_Subtitle", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true, "helpText": "@SharedResource:FormFields_SubtitleHelpText"}, "validation": {"maxLength": 200}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 3, "rows": 3, "layout": "inline", "collapsed": true, "helpText": "@SharedResource:FormFields_DescriptionHelpText"}, "validation": {"maxLength": 500}}, {"name": "PrimaryButtonText", "type": "multilingual-text", "label": "@FormResource:PrimaryButtonText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 1, "layout": "inline", "helpText": "@FormResource:PrimaryButtonTextHelpText"}, "validation": {"maxLength": 50}}, {"name": "PrimaryButtonUrl", "type": "text", "label": "@FormResource:PrimaryButtonLink", "display": {"collapsed": true, "layout": "inline", "group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-4", "order": 2, "helpText": "@FormResource:PrimaryButtonLinkHelpText"}}, {"name": "PrimaryButtonStyle", "type": "select", "label": "@FormResource:FormFields_ButtonStyle", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-4", "order": 3, "layout": "inline", "collapsed": true}, "options": [{"value": "primary", "label": "@FormResource:ButtonStyle_Primary"}, {"value": "secondary", "label": "@FormResource:ButtonStyle_Secondary"}, {"value": "outline", "label": "@FormResource:ButtonStyle_Outline"}, {"value": "ghost", "label": "@FormResource:ButtonStyle_Ghost"}], "defaultValue": "primary"}, {"name": "PrimaryButtonOpenNewTab", "type": "checkbox", "label": "@FormResource:FormFields_OpenInNewTab", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-4", "order": 4, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_OpenInNewTabHelpText"}, "defaultValue": false}, {"name": "SecondaryButtonText", "type": "multilingual-text", "label": "@FormResource:SecondaryButtonText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 5, "layout": "inline", "helpText": "@FormResource:SecondaryButtonTextHelpText"}, "validation": {"maxLength": 50}}, {"name": "SecondaryButtonUrl", "type": "text", "label": "@FormResource:FormFields_ButtonUrl", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-4", "order": 6, "collapsed": true, "layout": "inline", "helpText": "@FormResource:SecondaryButtonLinkHelpText"}}, {"name": "SecondaryButtonStyle", "type": "select", "label": "@FormResource:ButtonStyle_Secondary", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-4", "order": 7, "layout": "inline", "collapsed": true}, "options": [{"value": "primary", "label": "@FormResource:ButtonStyle_Primary"}, {"value": "secondary", "label": "@FormResource:ButtonStyle_Secondary"}, {"value": "outline", "label": "@FormResource:ButtonStyle_Outline"}, {"value": "ghost", "label": "@FormResource:ButtonStyle_Ghost"}], "defaultValue": "secondary"}, {"name": "SecondaryButtonOpenNewTab", "type": "checkbox", "label": "@FormResource:FormFields_OpenInNewTab", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-4", "order": 8, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_OpenInNewTabHelpText"}, "defaultValue": false}, {"name": "BackgroundImage", "type": "image", "label": "@SharedResource:FormFields_BackgroundImage", "display": {"group": "@SharedResource:FormGroups_MediaContent", "width": "col-span-12", "order": 1, "collapsed": true, "helpText": "@SharedResource:FormFields_BackgroundImageHelpText"}, "fileConfig": {"folder": "cta", "types": ["image/*"], "maxSize": "5MB", "multiple": false, "preview": true}}, {"name": "BackgroundColor", "type": "select", "label": "@FormResource:FormFields_BackgroundColor", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_BackgroundColorHelpText"}, "options": [{"value": "primary", "label": "@FormResource:FormFields_BackgroundColor_Primary"}, {"value": "secondary", "label": "@FormResource:FormFields_BackgroundColor_Secondary"}, {"value": "accent", "label": "@FormResource:FormFields_BackgroundColor_Accent"}, {"value": "gray", "label": "@FormResource:FormFields_BackgroundColor_Gray"}, {"value": "transparent", "label": "@FormResource:FormFields_BackgroundColor_Transparent"}], "defaultValue": "primary"}, {"name": "TextColor", "type": "select", "label": "@FormResource:FormFields_TextColor", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 2, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_TextColor"}, "options": [{"value": "white", "label": "@FormResource:WhiteText"}, {"value": "black", "label": "@FormResource:BlackText"}, {"value": "primary", "label": "@FormResource:PrimaryText"}, {"value": "gray", "label": "@FormResource:GrayText"}], "defaultValue": "white"}, {"name": "TextAlignment", "type": "select", "label": "@FormResource:TextAlignment", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 3, "layout": "inline", "collapsed": true, "helpText": "@FormResource:TextAlignmentHelpText"}, "options": [{"value": "left", "label": "@FormResource:AlignLeft"}, {"value": "center", "label": "@FormResource:AlignCenter"}, {"value": "right", "label": "@FormResource:AlignRight"}], "defaultValue": "center"}, {"name": "Size", "type": "select", "label": "@FormResource:SectionSize", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 4, "layout": "inline", "collapsed": true}, "options": [{"value": "small", "label": "@FormResource:SmallSize"}, {"value": "medium", "label": "@FormResource:MediumSize"}, {"value": "large", "label": "@FormResource:LargeSize"}], "defaultValue": "medium"}, {"name": "Layout", "type": "select", "label": "@FormResource:FormFields_Layout", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 5, "layout": "inline", "collapsed": true}, "options": [{"value": "horizontal", "label": "@FormResource:FormFields_HorizontalLayout"}, {"value": "vertical", "label": "@FormResource:FormFields_VerticalLayout"}], "defaultValue": "vertical"}, {"name": "Spacing", "type": "select", "label": "@FormResource:FormFields_SpacingSettings", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 6, "layout": "inline", "collapsed": true}, "options": [{"value": "tight", "label": "@FormResource:TightSpacing"}, {"value": "normal", "label": "@FormResource:NormalSpacing"}, {"value": "loose", "label": "@FormResource:LooseSpacing"}], "defaultValue": "normal"}, {"name": "BorderRadius", "type": "select", "label": "@FormResource:FormFields_BorderRadius", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 7, "layout": "inline", "collapsed": true}, "options": [{"value": "none", "label": "@FormResource:NoBorderRadius"}, {"value": "small", "label": "@FormResource:SmallBorderRadius"}, {"value": "normal", "label": "@FormResource:NormalBorderRadius"}, {"value": "large", "label": "@FormResource:LargeBorderRadius"}, {"value": "full", "label": "@FormResource:FullBorderRadius"}], "defaultValue": "normal"}, {"name": "ShowShadow", "type": "checkbox", "label": "@FormResource:FormFields_ShowShadow", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 8, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_ShowShadowHelpText"}, "defaultValue": true}, {"name": "ShowOverlay", "type": "checkbox", "label": "@FormResource:ShowOverlay", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 9, "layout": "inline", "collapsed": true, "helpText": "@FormResource:ShowOverlayHelpText"}, "defaultValue": true}, {"name": "OverlayOpacity", "type": "select", "label": "@FormResource:OverlayOpacity", "display": {"layout": "inline", "group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 10, "collapsed": true, "helpText": "@FormResource:OverlayOpacityHelpText"}, "options": [{"value": "0.1", "label": "10%"}, {"value": "0.2", "label": "20%"}, {"value": "0.3", "label": "30%"}, {"value": "0.4", "label": "40%"}, {"value": "0.5", "label": "50%"}, {"value": "0.6", "label": "60%"}, {"value": "0.7", "label": "70%"}, {"value": "0.8", "label": "80%"}, {"value": "0.9", "label": "90%"}], "defaultValue": "0.7"}, {"name": "AnimationEnabled", "type": "checkbox", "label": "@FormResource:FormFields_AnimationEnabled", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 11, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "AnimationType", "type": "select", "label": "@FormResource:FormFields_AnimationType", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-3", "order": 12, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_AnimationTypeHelpText"}, "options": [{"value": "fadeIn", "label": "@FormResource:FadeInAnimation"}, {"value": "slideInLeft", "label": "@FormResource:SlideInLeftAnimation"}, {"value": "slideInRight", "label": "@FormResource:SlideInRightAnimation"}, {"value": "scaleIn", "label": "@FormResource:ScaleInAnimation"}], "defaultValue": "fadeIn"}]}