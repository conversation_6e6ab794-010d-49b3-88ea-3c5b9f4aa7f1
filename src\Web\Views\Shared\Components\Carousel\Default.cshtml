@model MlSoft.Sites.Web.ViewModels.Components.CarouselComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var items = Model?.Items ?? new List<CarouselItem>();
    var settings = Model?.Settings ?? new CarouselSettings();
    var carouselId = Model?.Id ?? JObjectHelper.GenerateId("carousel");
    
    // Generate CSS classes based on properties
    var heightClass = GetHeightClass(settings.Height, settings.CustomHeight);
    
    // Helper methods for dynamic class generation
    string GetHeightClass(string? height, string? customHeight)
    {
        if (!string.IsNullOrEmpty(customHeight))
            return $"h-[{customHeight}]";
            
        return height?.ToLower() switch
        {
            "small" => "h-56 md:h-64",
            "medium" => "h-64 md:h-80",
            "large" => "h-80 md:h-96",
            "full" => "h-screen",
            _ => "h-56 md:h-96"
        };
    }

    
    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

@if (items.Any())
{
    <div id="carousel-@carouselId" 
         class="relative w-full" 
         data-carousel="slide"
         data-carousel-type="banner"
         data-carousel-interval="@settings.AutoPlayInterval"
         @(settings.AutoPlay ? "data-carousel-auto" : "")
         @(settings.InfiniteLoop ? "data-carousel-loop" : "")>
        
        <!-- Carousel wrapper -->
        <div class="relative @heightClass overflow-hidden rounded-lg">
            @for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                var isActive = i == 0;
                
                <div class="@(isActive ? "block" : "hidden") <EMAIL> ease-in-out transition-opacity"
                     data-carousel-item="@(isActive ? "active" : "")">
                     
                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                    {
                        <img src="@ProcessFilePath(item.ImageUrl)" 
                             alt="@(item.Alt ?? item.Title ?? $"Slide {i + 1}")" 
                             class="absolute block w-full h-full object-cover"
                             loading="@(i == 0 ? "eager" : "lazy")"
                             @(i == 0 ? "fetchpriority=\"high\"" : "") />
                    }
                    
                    <!-- Content overlay -->
                    @if (settings.ShowCaptions && (!string.IsNullOrEmpty(item.Title) || !string.IsNullOrEmpty(item.Content)))
                    {
                        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/50 to-transparent dark:from-gray-900/70">
                            <div class="@GetCaptionPositionClass(settings.CaptionPosition) text-white">
                                @if (!string.IsNullOrEmpty(item.Title))
                                {
                                    <h3 class="mb-3 text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight text-white drop-shadow-lg">
                                        @item.Title
                                    </h3>
                                }
                                
                                @if (!string.IsNullOrEmpty(item.Subtitle))
                                {
                                    <h4 class="mb-4 text-lg md:text-xl lg:text-2xl font-medium text-gray-100 drop-shadow-md">
                                        @item.Subtitle
                                    </h4>
                                }
                                
                                @if (!string.IsNullOrEmpty(item.Content))
                                {
                                    <p class="mb-4 text-base md:text-lg leading-relaxed text-gray-200 drop-shadow-sm max-w-2xl">
                                        @item.Content
                                    </p>
                                }                               
                                
                            </div>
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(item.LinkUrl))
                    {
                        <a href="@ProcessFilePath(item.LinkUrl)" 
                           class="absolute inset-0 z-10"
                           @(item.OpenInNewTab ? "target=\"_blank\" rel=\"noopener noreferrer\"" : "")
                           aria-label="@(item.Title ?? $"Go to slide {i + 1}")">
                            <span class="sr-only">@(item.Title ?? $"Go to slide {i + 1}")</span>
                        </a>
                    }
                </div>
            }
        </div>
        
        <!-- Slider indicators -->
        @if (settings.ShowIndicators && items.Count > 1)
        {
            <div class="absolute z-30 flex space-x-3 -translate-x-1/2 bottom-5 left-1/2">
                @for (int i = 0; i < items.Count; i++)
                {
                    <button type="button" 
                            class="w-3 h-3 rounded-full transition-all duration-300 @(i == 0 ? "bg-white dark:bg-gray-200" : "bg-white/50 dark:bg-gray-400/50 hover:bg-white/75 dark:hover:bg-gray-300/75")" 
                            aria-current="@(i == 0 ? "true" : "false")" 
                            aria-label="@SharedRes["Carousel_SlideIndicator"] @(i + 1)" 
                            data-carousel-slide-to="@i"></button>
                }
            </div>
        }
        
        <!-- Slider controls -->
        @if (settings.ShowNavigation && items.Count > 1)
        {
            <button type="button" 
                    class="absolute top-0 start-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" 
                    data-carousel-prev
                    aria-label="@SharedRes["Carousel_Previous"]">
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none transition-all duration-300">
                    <svg class="w-4 h-4 text-white dark:text-gray-200 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                    </svg>
                    <span class="sr-only">@SharedRes["Carousel_Previous"]</span>
                </span>
            </button>
            
            <button type="button" 
                    class="absolute top-0 end-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" 
                    data-carousel-next
                    aria-label="@SharedRes["Carousel_Next"]">
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none transition-all duration-300">
                    <svg class="w-4 h-4 text-white dark:text-gray-200 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span class="sr-only">@SharedRes["Carousel_Next"]</span>
                </span>
            </button>
        }
    </div>
}

@functions {
    string GetButtonClasses(string? style)
    {
        return style?.ToLower() switch
        {
            "secondary" => "text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-all duration-300 dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900",
            "outline" => "text-white border border-white hover:bg-white hover:text-primary-700 focus:ring-4 focus:outline-none focus:ring-white font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-all duration-300 dark:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-300 dark:hover:text-gray-900 dark:focus:ring-gray-800",
            _ => "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-all duration-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
        };
    }
    
    string GetCaptionPositionClass(string? position)
    {
        return position?.ToLower() switch
        {
            "top" => "absolute top-5 left-5 right-5 md:top-8 md:left-8 md:right-8",
            "center" => "absolute inset-0 flex flex-col items-center justify-center text-center px-5 md:px-8",
            "overlay" => "absolute inset-5 flex flex-col items-start justify-end md:inset-8",
            _ => "absolute bottom-5 left-5 right-5 md:bottom-8 md:left-8 md:right-8"
        };
    }
}