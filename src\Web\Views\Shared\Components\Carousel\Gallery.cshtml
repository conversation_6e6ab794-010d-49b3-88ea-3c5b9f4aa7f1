@model MlSoft.Sites.Web.ViewModels.Components.CarouselComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var items = Model?.Items ?? new List<CarouselItem>();
    var settings = Model?.Settings ?? new CarouselSettings();
    var carouselId = Model?.Id ?? JObjectHelper.GenerateId("carousel");
    
    // Gallery specific settings
    var heightClass = GetHeightClass(settings.Height, settings.CustomHeight);
    
    string GetHeightClass(string? height, string? customHeight)
    {
        if (!string.IsNullOrEmpty(customHeight))
            return $"h-[{customHeight}]";
            
        return height?.ToLower() switch
        {
            "small" => "h-48 md:h-56",
            "medium" => "h-56 md:h-64",
            "large" => "h-64 md:h-80",
            "full" => "h-screen",
            _ => "h-56 md:h-64"
        };
    }
    
    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

@if (items.Any())
{
    <div id="carousel-@carouselId" 
         class="relative w-full" 
         data-carousel="slide"
         data-carousel-type="gallery"
         data-carousel-interval="@settings.AutoPlayInterval"
         @(settings.AutoPlay ? "data-carousel-auto" : "")
         @(settings.InfiniteLoop ? "data-carousel-loop" : "")>
        
        <!-- Carousel wrapper -->
        <div class="relative @heightClass overflow-hidden rounded-lg bg-white dark:bg-gray-800">
            @for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                var isActive = i == 0;
                
                <div class="@(isActive ? "block" : "hidden") <EMAIL> ease-in-out" 
                     data-carousel-item="@(isActive ? "active" : "")">
                     
                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                    {
                        <img src="@ProcessFilePath(item.ImageUrl)" 
                             alt="@(item.Alt ?? item.Title ?? $"Gallery image {i + 1}")" 
                             class="absolute block w-full h-full object-contain p-4"
                             loading="@(i == 0 ? "eager" : "lazy")"
                             @(i == 0 ? "fetchpriority=\"high\"" : "") />
                    }
                    
                    <!-- Simple caption for gallery -->
                    @if (!string.IsNullOrEmpty(item.Title))
                    {
                        <div class="absolute bottom-0 left-0 right-0 bg-white/90 dark:bg-gray-800/90 p-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 text-center">
                                @item.Title
                            </h3>
                            @if (!string.IsNullOrEmpty(item.Subtitle))
                            {
                                <p class="text-sm text-gray-600 dark:text-gray-400 text-center mt-1">
                                    @item.Subtitle
                                </p>
                            }
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(item.LinkUrl))
                    {
                        <a href="@ProcessFilePath(item.LinkUrl)" 
                           class="absolute inset-0 z-10"
                           @(item.OpenInNewTab ? "target=\"_blank\" rel=\"noopener noreferrer\"" : "")
                           aria-label="@(item.Title ?? $"View gallery image {i + 1}")">
                            <span class="sr-only">@(item.Title ?? $"View gallery image {i + 1}")</span>
                        </a>
                    }
                </div>
            }
        </div>
        
        <!-- Slider indicators -->
        @if (settings.ShowIndicators && items.Count > 1)
        {
            <div class="absolute z-30 flex space-x-3 -translate-x-1/2 bottom-5 left-1/2">
                @for (int i = 0; i < items.Count; i++)
                {
                    <button type="button" 
                            class="w-3 h-3 rounded-full transition-all duration-300 @(i == 0 ? "bg-primary-600 dark:bg-primary-500" : "bg-gray-400 dark:bg-gray-500 hover:bg-primary-400 dark:hover:bg-primary-400")" 
                            aria-current="@(i == 0 ? "true" : "false")" 
                            aria-label="@SharedRes["Carousel_SlideIndicator"] @(i + 1)" 
                            data-carousel-slide-to="@i"></button>
                }
            </div>
        }
        
        <!-- Slider controls -->
        @if (settings.ShowNavigation && items.Count > 1)
        {
            <button type="button" 
                    class="absolute top-0 start-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" 
                    data-carousel-prev
                    aria-label="@SharedRes["Carousel_Previous"]">
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/80 dark:bg-gray-800/80 group-hover:bg-white dark:group-hover:bg-gray-700 group-focus:ring-4 group-focus:ring-primary-300 dark:group-focus:ring-primary-800 group-focus:outline-none transition-all duration-300">
                    <svg class="w-4 h-4 text-gray-800 dark:text-gray-200 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                    </svg>
                    <span class="sr-only">@SharedRes["Carousel_Previous"]</span>
                </span>
            </button>
            
            <button type="button" 
                    class="absolute top-0 end-0 z-30 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none" 
                    data-carousel-next
                    aria-label="@SharedRes["Carousel_Next"]">
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-white/80 dark:bg-gray-800/80 group-hover:bg-white dark:group-hover:bg-gray-700 group-focus:ring-4 group-focus:ring-primary-300 dark:group-focus:ring-primary-800 group-focus:outline-none transition-all duration-300">
                    <svg class="w-4 h-4 text-gray-800 dark:text-gray-200 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span class="sr-only">@SharedRes["Carousel_Next"]</span>
                </span>
            </button>
        }
        
        <!-- Image counter for gallery -->
        <div class="absolute top-4 right-4 z-30 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
            <span id="current-slide-@carouselId">1</span> / @items.Count
        </div>
    </div>
    

}