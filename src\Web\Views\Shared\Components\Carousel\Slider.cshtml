@model MlSoft.Sites.Web.ViewModels.Components.CarouselComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var items = Model?.Items ?? new List<CarouselItem>();
    var settings = Model?.Settings ?? new CarouselSettings();
    var carouselId = Model?.Id ?? JObjectHelper.GenerateId("carousel");
    
    // Slider specific settings - compact height
    var heightClass = GetHeightClass(settings.Height, settings.CustomHeight);
    
    string GetHeightClass(string? height, string? customHeight)
    {
        if (!string.IsNullOrEmpty(customHeight))
            return $"h-[{customHeight}]";
            
        return height?.ToLower() switch
        {
            "small" => "h-32 md:h-40",
            "medium" => "h-40 md:h-48",
            "large" => "h-48 md:h-56",
            _ => "h-40 md:h-48"
        };
    }
    
    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

@if (items.Any())
{
    <div id="carousel-@carouselId" 
         class="relative w-full" 
         data-carousel="slide"
         data-carousel-type="slider"
         data-carousel-interval="@settings.AutoPlayInterval"
         @(settings.AutoPlay ? "data-carousel-auto" : "")
         @(settings.InfiniteLoop ? "data-carousel-loop" : "")>
        
        <!-- Carousel wrapper -->
        <div class="relative @heightClass overflow-hidden rounded-lg shadow-lg">
            @for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                var isActive = i == 0;
                
                <div class="@(isActive ? "block" : "hidden") <EMAIL> ease-in-out" 
                     data-carousel-item="@(isActive ? "active" : "")">
                     
                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                    {
                        <img src="@ProcessFilePath(item.ImageUrl)" 
                             alt="@(item.Alt ?? item.Title ?? $"Slide {i + 1}")" 
                             class="absolute block w-full h-full object-cover"
                             loading="@(i == 0 ? "eager" : "lazy")"
                             @(i == 0 ? "fetchpriority=\"high\"" : "") />
                    }
                    
                    <!-- Minimal overlay for slider -->
                    @if (!string.IsNullOrEmpty(item.Title) || !string.IsNullOrEmpty(item.Content))
                    {
                        <div class="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent">
                            <div class="absolute bottom-4 left-4 right-4 text-white">
                                @if (!string.IsNullOrEmpty(item.Title))
                                {
                                    <h3 class="text-lg md:text-xl font-semibold text-white dark:text-white mb-1">
                                        @item.Title
                                    </h3>
                                }
                                
                                @if (!string.IsNullOrEmpty(item.Content))
                                {
                                    <p class="text-sm text-gray-200 dark:text-gray-300 line-clamp-2">
                                        @item.Content
                                    </p>
                                }
                            </div>
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(item.LinkUrl))
                    {
                        <a href="@ProcessFilePath(item.LinkUrl)" 
                           class="absolute inset-0 z-10"
                           @(item.OpenInNewTab ? "target=\"_blank\" rel=\"noopener noreferrer\"" : "")
                           aria-label="@(item.Title ?? $"Go to slide {i + 1}")">
                            <span class="sr-only">@(item.Title ?? $"Go to slide {i + 1}")</span>
                        </a>
                    }
                </div>
            }
        </div>
        
        <!-- Compact indicators -->
        @if (settings.ShowIndicators && items.Count > 1)
        {
            <div class="absolute z-30 flex space-x-2 -translate-x-1/2 bottom-2 left-1/2">
                @for (int i = 0; i < items.Count; i++)
                {
                    <button type="button" 
                            class="w-2 h-2 rounded-full transition-all duration-300 @(i == 0 ? "bg-white dark:bg-gray-200" : "bg-white/50 dark:bg-gray-400/50 hover:bg-white/75 dark:hover:bg-gray-300/75")" 
                            aria-current="@(i == 0 ? "true" : "false")" 
                            aria-label="@SharedRes["Carousel_SlideIndicator"] @(i + 1)" 
                            data-carousel-slide-to="@i"></button>
                }
            </div>
        }
        
        <!-- Compact navigation controls -->
        @if (settings.ShowNavigation && items.Count > 1)
        {
            <button type="button" 
                    class="absolute top-1/2 start-2 z-30 -translate-y-1/2 cursor-pointer group focus:outline-none" 
                    data-carousel-prev
                    aria-label="@SharedRes["Carousel_Previous"]">
                <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-2 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none transition-all duration-300">
                    <svg class="w-3 h-3 text-white dark:text-gray-200 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                    </svg>
                    <span class="sr-only">@SharedRes["Carousel_Previous"]</span>
                </span>
            </button>
            
            <button type="button" 
                    class="absolute top-1/2 end-2 z-30 -translate-y-1/2 cursor-pointer group focus:outline-none" 
                    data-carousel-next
                    aria-label="@SharedRes["Carousel_Next"]">
                <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-white/30 dark:bg-gray-800/30 group-hover:bg-white/50 dark:group-hover:bg-gray-800/60 group-focus:ring-2 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none transition-all duration-300">
                    <svg class="w-3 h-3 text-white dark:text-gray-200 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span class="sr-only">@SharedRes["Carousel_Next"]</span>
                </span>
            </button>
        }
    </div>
}