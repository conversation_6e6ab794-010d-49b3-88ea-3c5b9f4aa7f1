{"ComponentId": "Carousel", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "默认轮播图", "en": "<PERSON><PERSON><PERSON>", "ja": "デフォルトカルーセル"}, "Descriptions": {"zh": "标准轮播图组件，支持图片和内容展示，可配置自动播放、导航控件等功能", "en": "Standard carousel component with image and content display, configurable autoplay, navigation controls and more", "ja": "画像とコンテンツ表示をサポートする標準カルーセルコンポーネント、自動再生、ナビゲーションコントロールなどが設定可能"}, "formFields": [{"name": "Items", "type": "repeater", "label": "@FormResource:FormFields_CarouselItems", "display": {"group": "@FormResource:FormGroups_Content", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_CarouselItemsHelpText"}, "validation": {"required": true, "minItems": 1, "maxItems": 10}, "template": {"fields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"width": "col-span-12", "layout": "inline", "order": 1}, "validation": {"maxLength": 100}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@SharedResource:FormFields_Subtitle", "display": {"width": "col-span-12", "layout": "inline", "order": 2}, "validation": {"maxLength": 150}}, {"name": "Content", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Content", "display": {"width": "col-span-12", "rows": 2, "layout": "inline", "order": 3}, "validation": {"maxLength": 500}}, {"name": "ImageUrl", "type": "image", "label": "@FormResource:FormFields_Image", "display": {"width": "col-span-12", "order": 4}, "fileConfig": {"folder": "carousel", "types": ["image/*"], "maxSize": "5MB", "multiple": false, "preview": true}, "validation": {"required": true}}, {"name": "LinkUrl", "type": "text", "label": "@FormResource:FormFields_LinkUrl", "display": {"width": "col-span-12", "layout": "inline", "order": 6}, "validation": {"maxLength": 500}}, {"name": "OpenInNewTab", "type": "checkbox", "label": "@FormResource:FormFields_OpenInNewTab", "display": {"width": "col-span-12", "layout": "inline", "order": 7}}]}}, {"name": "Settings.AutoPlay", "type": "checkbox", "label": "@FormResource:FormFields_AutoPlay", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "order": 10, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_AutoPlayHelpText"}, "defaultValue": true}, {"name": "Settings.AutoPlayInterval", "type": "number", "label": "@FormResource:FormFields_AutoPlayInterval", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "order": 11, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_AutoPlayIntervalHelpText"}, "validation": {"min": 1000, "max": 30000}, "defaultValue": 5000}, {"name": "Settings.ShowIndicators", "type": "checkbox", "label": "@FormResource:FormFields_ShowIndicators", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "order": 12}, "defaultValue": true}, {"name": "Settings.ShowNavigation", "type": "checkbox", "label": "@FormResource:FormFields_ShowNavigation", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "order": 13}, "defaultValue": true}, {"name": "Settings.InfiniteLoop", "type": "checkbox", "label": "@FormResource:FormFields_InfiniteLoop", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "order": 15}, "defaultValue": true}, {"name": "Settings.TransitionDuration", "type": "number", "label": "@FormResource:FormFields_TransitionDuration", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "order": 17, "helpText": "@FormResource:FormFields_TransitionDurationHelpText"}, "validation": {"min": 200, "max": 2000}, "defaultValue": 600}, {"name": "Settings.Height", "type": "select", "label": "@FormResource:FormFields_Height", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 20}, "options": [{"value": "auto", "label": "@FormResource:Height_Auto"}, {"value": "small", "label": "@FormResource:Height_Small"}, {"value": "medium", "label": "@FormResource:Height_Medium"}, {"value": "large", "label": "@FormResource:Height_Large"}, {"value": "full", "label": "@FormResource:Height_Full"}, {"value": "custom", "label": "@FormResource:Height_Custom"}], "defaultValue": "auto"}, {"name": "Settings.CustomHeight", "type": "text", "label": "@FormResource:FormFields_CustomHeight", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 21, "helpText": "@FormResource:FormFields_CustomHeightHelpText", "conditional": {"field": "Settings.Height", "value": "custom"}}, "validation": {"maxLength": 20}}, {"name": "Settings.ShowCaptions", "type": "checkbox", "label": "@FormResource:FormFields_ShowCaptions", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 22}, "defaultValue": true}, {"name": "Settings.CaptionPosition", "type": "select", "label": "@FormResource:FormFields_CaptionPosition", "display": {"layout": "inline", "collapsed": true, "group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 23, "conditional": {"field": "Settings.ShowCaptions", "value": true}}, "options": [{"value": "bottom", "label": "@FormResource:CaptionPosition_Bottom"}, {"value": "top", "label": "@FormResource:CaptionPosition_Top"}, {"value": "center", "label": "@FormResource:CaptionPosition_Center"}, {"value": "overlay", "label": "@FormResource:CaptionPosition_Overlay"}], "defaultValue": "bottom"}]}