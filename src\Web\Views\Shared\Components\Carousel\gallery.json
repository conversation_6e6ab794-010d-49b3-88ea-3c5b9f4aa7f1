{"ComponentId": "Carousel", "Id": "Gallery", "Names": {"zh": "图库轮播", "en": "Gallery Carousel", "ja": "ギャラリーカルーセル"}, "Descriptions": {"zh": "图库风格的轮播组件，适合展示产品图片或作品集，图片居中显示", "en": "Gallery-style carousel component, perfect for showcasing product images or portfolios with centered image display", "ja": "製品画像やポートフォリオの展示に最適な、画像を中央に表示するギャラリースタイルのカルーセルコンポーネント"}, "formFields": [{"name": "Items", "type": "repeater", "label": "@FormResource:FormFields_GalleryItems", "display": {"group": "@FormResource:FormGroups_Content", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_GalleryItemsHelpText"}, "validation": {"required": true, "minItems": 1, "maxItems": 20}, "template": {"fields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true, "order": 1}, "validation": {"maxLength": 100}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@SharedResource:FormFields_Subtitle", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true, "order": 2}, "validation": {"maxLength": 150}}, {"name": "ImageUrl", "type": "image", "label": "@FormResource:FormFields_Image", "display": {"width": "col-span-12", "order": 3}, "fileConfig": {"folder": "gallery", "types": ["image/*"], "maxSize": "10MB", "multiple": false, "preview": true}, "validation": {"required": true}}, {"name": "LinkUrl", "type": "text", "label": "@FormResource:FormFields_LinkUrl", "display": {"width": "col-span-12", "order": 5}, "validation": {"maxLength": 500}}, {"name": "OpenInNewTab", "type": "checkbox", "label": "@FormResource:FormFields_OpenInNewTab", "display": {"width": "col-span-12", "order": 6}}]}}, {"name": "Settings.AutoPlay", "type": "checkbox", "label": "@FormResource:FormFields_AutoPlay", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 10}, "defaultValue": false}, {"name": "Settings.AutoPlayInterval", "type": "number", "label": "@FormResource:FormFields_AutoPlayInterval", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 11, "conditional": {"field": "Settings.AutoPlay", "value": true}}, "validation": {"min": 2000, "max": 30000}, "defaultValue": 8000}, {"name": "Settings.ShowIndicators", "type": "checkbox", "label": "@FormResource:FormFields_ShowIndicators", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 12}, "defaultValue": true}, {"name": "Settings.ShowNavigation", "type": "checkbox", "label": "@FormResource:FormFields_ShowNavigation", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 13}, "defaultValue": true}, {"name": "Settings.Height", "type": "select", "label": "@FormResource:FormFields_Height", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 20}, "options": [{"value": "small", "label": "@FormResource:Height_Small"}, {"value": "medium", "label": "@FormResource:Height_Medium"}, {"value": "large", "label": "@FormResource:Height_Large"}, {"value": "custom", "label": "@FormResource:Height_Custom"}], "defaultValue": "medium"}, {"name": "Settings.CustomHeight", "type": "text", "label": "@FormResource:FormFields_CustomHeight", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 21, "helpText": "@FormResource:FormFields_CustomHeightHelpText", "conditional": {"field": "Settings.Height", "value": "custom"}}, "validation": {"maxLength": 20}}]}