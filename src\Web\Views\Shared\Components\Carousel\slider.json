{"ComponentId": "Carousel", "Id": "Slide<PERSON>", "Names": {"zh": "紧凑滑块", "en": "Compact Slider", "ja": "コンパクトスライダー"}, "Descriptions": {"zh": "紧凑型滑块组件，适合在页面中嵌入小型图片轮播，占用空间较小", "en": "Compact slider component, perfect for embedding small image carousels within pages with minimal space usage", "ja": "ページ内に小さな画像カルーセルを埋め込むのに最適な、最小限のスペース使用量のコンパクトスライダーコンポーネント"}, "formFields": [{"name": "Items", "type": "repeater", "label": "@FormResource:FormFields_SliderItems", "display": {"group": "@FormResource:FormGroups_Content", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_SliderItemsHelpText"}, "validation": {"required": true, "minItems": 2, "maxItems": 8}, "template": {"fields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true, "order": 1}, "validation": {"maxLength": 80}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@FormResource:FormFields_Subtitle", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true, "order": 2}, "validation": {"maxLength": 120}}, {"name": "Content", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Content", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true, "order": 3}, "validation": {"maxLength": 200}}, {"name": "ImageUrl", "type": "image", "label": "@FormResource:FormFields_Image", "display": {"width": "col-span-12", "order": 4}, "fileConfig": {"folder": "slider", "types": ["image/*"], "maxSize": "3MB", "multiple": false, "preview": true}, "validation": {"required": true}}, {"name": "LinkUrl", "type": "text", "label": "@FormResource:FormFields_LinkUrl", "display": {"width": "col-span-12", "order": 6}, "validation": {"maxLength": 500}}, {"name": "OpenInNewTab", "type": "checkbox", "label": "@FormResource:FormFields_OpenInNewTab", "display": {"width": "col-span-12", "order": 7}}]}}, {"name": "Settings.AutoPlay", "type": "checkbox", "label": "@FormResource:FormFields_AutoPlay", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 10}, "defaultValue": true}, {"name": "Settings.AutoPlayInterval", "type": "number", "label": "@FormResource:FormFields_AutoPlayInterval", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 11, "conditional": {"field": "Settings.AutoPlay", "value": true}}, "validation": {"min": 2000, "max": 15000}, "defaultValue": 4000}, {"name": "Settings.ShowIndicators", "type": "checkbox", "label": "@FormResource:FormFields_ShowIndicators", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 12}, "defaultValue": true}, {"name": "Settings.ShowNavigation", "type": "checkbox", "label": "@FormResource:FormFields_ShowNavigation", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 13}, "defaultValue": true}, {"name": "Settings.InfiniteLoop", "type": "checkbox", "label": "@FormResource:FormFields_InfiniteLoop", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 15}, "defaultValue": true}, {"name": "Settings.ShowCaptions", "type": "checkbox", "label": "@FormResource:FormFields_ShowCaptions", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 16}, "defaultValue": true}, {"name": "Settings.CaptionPosition", "type": "select", "label": "@FormResource:FormFields_CaptionPosition", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 17, "conditional": {"field": "Settings.ShowCaptions", "value": true}}, "options": [{"value": "bottom", "label": "@FormResource:CaptionPosition_Bottom"}, {"value": "top", "label": "@FormResource:CaptionPosition_Top"}, {"value": "center", "label": "@FormResource:CaptionPosition_Center"}, {"value": "overlay", "label": "@FormResource:CaptionPosition_Overlay"}], "defaultValue": "bottom"}, {"name": "Settings.Height", "type": "select", "label": "@FormResource:FormFields_Height", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 20}, "options": [{"value": "small", "label": "@FormResource:Height_Small"}, {"value": "medium", "label": "@FormResource:Height_Medium"}, {"value": "large", "label": "@FormResource:Height_Large"}, {"value": "custom", "label": "@FormResource:Height_Custom"}], "defaultValue": "medium"}, {"name": "Settings.CustomHeight", "type": "text", "label": "@FormResource:FormFields_CustomHeight", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 21, "helpText": "@FormResource:FormFields_CustomHeightHelpText", "conditional": {"field": "Settings.Height", "value": "custom"}}, "validation": {"maxLength": 20}}]}