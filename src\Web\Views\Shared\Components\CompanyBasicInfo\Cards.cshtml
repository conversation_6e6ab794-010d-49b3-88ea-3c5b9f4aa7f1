@model MlSoft.Sites.Web.ViewModels.Components.CompanyBasicInfoComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedR<PERSON>
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";

    // Get company data
    var company = Model?.CompanyData;
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var companyLocale = company?.Locale?.ContainsKey(culture) == true ? company.Locale[culture] : null;
    var contactInfo = company?.ContactInfo;
    var contactLocale = contactInfo?.Locale?.ContainsKey(culture) == true ? contactInfo.Locale[culture] : null;
    var presidentLocale = president?.Locale?.Contains<PERSON>ey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("company-basic-info-cards");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    // Helper function to get employee scale display text
    string GetEmployeeScaleText(MlSoft.Sites.Model.Entities.Enums.EmployeeScale? scale) => scale switch
    {
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Small => culture == "ja" ? "1-50名" : culture == "en" ? "1-50 employees" : "1-50人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Medium => culture == "ja" ? "51-300名" : culture == "en" ? "51-300 employees" : "51-300人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Large => culture == "ja" ? "301-1000名" : culture == "en" ? "301-1000 employees" : "301-1000人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Enterprise => culture == "ja" ? "1000名以上" : culture == "en" ? "1000+ employees" : "1000人以上",
        _ => ""
    };
}

<section id="@uniqueId" class="py-12 @containerClass">
    <div class="container max-w-7xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["CompanyBasicInfo_Title"])
                </h2>
                <div class="h-1 w-16 bg-primary-600"></div>
            </div>
        }

        @if (!string.IsNullOrEmpty(companyLocale?.CompanyName))
        {
            <div class="text-center mb-8">
                <h3 class="text-2xl font-semibold text-primary-600 dark:text-primary-400">@companyLocale.CompanyName</h3>
            </div>
        }

        <div class="grid gap-8 lg:grid-cols-2">
            <!-- Basic Company Info Card -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">@FormRes["CompanyBasicInfo_BasicInfo"]</h3>
                </div>
                <div class="p-6 space-y-4">
                    @if (Model?.ShowEstablishedDate == true && company?.EstablishedDate != null)
                    {
                        var dateFormat = culture == "ja" ? "yyyy年M月d日" : culture == "en" ? "MMMM d, yyyy" : "yyyy年M月d日";
                        <div class="border-b border-gray-200 dark:border-gray-700 pb-3">
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_EstablishedDate"]</dt>
                            <dd class="text-gray-900 dark:text-white">@company.EstablishedDate.ToString(dateFormat)</dd>
                        </div>
                    }
                    
                    @if (Model?.ShowCapital == true && company?.Capital != null)
                    {
                        var capitalText = company.Currency switch
                        {
                            "JPY" => $"{company.Capital:N0}円",
                            "USD" => $"${company.Capital:N0}",
                            "CNY" => $"¥{company.Capital:N0}",
                            _ => $"{company.Capital:N0} {company.Currency}"
                        };
                        <div class="border-b border-gray-200 dark:border-gray-700 pb-3">
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_Capital"]</dt>
                            <dd class="text-gray-900 dark:text-white">@capitalText</dd>
                        </div>
                    }
                    
                    @if (Model?.ShowEmployeeScale == true && company?.EmployeeScale != null)
                    {
                        var employeeText = GetEmployeeScaleText(company.EmployeeScale);
                        if (!string.IsNullOrEmpty(employeeText))
                        {
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-3">
                                <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_EmployeeCount"]</dt>
                                <dd class="text-gray-900 dark:text-white">@employeeText</dd>
                            </div>
                        }
                    }
                    
                    @if (Model?.ShowPresident == true && !string.IsNullOrEmpty(presidentLocale?.Name))
                    {
                        <div class="border-b border-gray-200 dark:border-gray-700 pb-3">
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_President"]</dt>
                            <dd class="text-gray-900 dark:text-white">@presidentLocale.Name</dd>
                        </div>
                    }
                    
                    @if (Model?.ShowRegistrationNumber == true && !string.IsNullOrEmpty(company?.RegistrationNumber))
                    {
                        <div class="pb-3">
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_RegistrationNumber"]</dt>
                            <dd class="text-gray-900 dark:text-white">@company.RegistrationNumber</dd>
                        </div>
                    }
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">@FormRes["CompanyBasicInfo_ContactInfo"]</h3>
                </div>
                <div class="p-6 space-y-4">
                    @if (Model?.ShowAddress == true && !string.IsNullOrEmpty(contactLocale?.Address))
                    {
                        <div>
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">@FormRes["CompanyBasicInfo_HeadOffice"]</dt>
                            <dd class="text-gray-900 dark:text-white leading-relaxed whitespace-pre-line">@contactLocale.Address</dd>
                        </div>
                    }
                    
                    @if (Model?.ShowPostalCode == true && !string.IsNullOrEmpty(contactInfo?.PostalCode))
                    {
                        <div class="pt-4">
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_PostalCode"]</dt>
                            <dd class="text-gray-900 dark:text-white">@contactInfo.PostalCode</dd>
                        </div>
                    }
                    
                    @if (Model?.ShowPhone == true && !string.IsNullOrEmpty(contactInfo?.Phone))
                    {
                        <div class="pt-4">
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@SharedRes["Contact_Phone"]</dt>
                            <dd class="text-gray-900 dark:text-white">@contactInfo.Phone</dd>
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(contactInfo?.Fax))
                    {
                        <div>
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_Fax"]</dt>
                            <dd class="text-gray-900 dark:text-white">@contactInfo.Fax</dd>
                        </div>
                    }
                    
                    @if (Model?.ShowEmail == true && !string.IsNullOrEmpty(contactInfo?.Email))
                    {
                        <div>
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@SharedRes["Contact_Email"]</dt>
                            <dd class="text-gray-900 dark:text-white">
                                <a href="mailto:@contactInfo.Email" class="text-primary-600 dark:text-primary-400 hover:underline">@contactInfo.Email</a>
                            </dd>
                        </div>
                    }
                    
                    @if (Model?.ShowWebsite == true && !string.IsNullOrEmpty(contactInfo?.Website))
                    {
                        <div>
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@FormRes["CompanyBasicInfo_Website"]</dt>
                            <dd class="text-gray-900 dark:text-white">
                                <a href="@contactInfo.Website" target="_blank" class="text-primary-600 dark:text-primary-400 hover:underline">@contactInfo.Website</a>
                            </dd>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</section>