# 公司基本信息组件 (CompanyBasicInfo Component)

## 概述

`CompanyBasicInfo` 组件是一个专门用于展示企业基本信息的组件，包括成立时间、资本金、员工规模、总部地址等关键信息。该组件按照主流企业网站的设计标准开发，支持多种布局样式和显示配置。

**重要特性：数据与配置分离**
- **数据来源**：组件数据直接从 `Company` 实体中读取，确保信息的一致性和准确性
- **配置功能**：组件配置仅控制显示样式、布局和字段可见性，不涉及具体数据内容
- **统一管理**：公司信息通过专门的公司信息管理功能进行维护

## 功能特性

### 数据字段（从Company实体读取）
- **公司名称** - 从 `CompanyLocaleFields.CompanyName` 读取
- **成立时间** - 从 `Company.EstablishedDate` 读取，支持多语言日期格式
- **资本金** - 从 `Company.Capital` 和 `Company.Currency` 读取，自动格式化显示
- **员工规模** - 从 `Company.EmployeeScale` 枚举读取，自动转换为文字描述
- **代表取締役社長** - 从 `Executive` 实体读取（`IsPresident=true`），显示社长姓名
- **工商注册号** - 从 `Company.RegistrationNumber` 读取
- **总部地址** - 从 `ContactInfo.Locale[culture].Address` 读取
- **邮编** - 从 `ContactInfo.PostalCode` 读取
- **联系电话** - 从 `ContactInfo.Phone` 读取
- **邮箱地址** - 从 `ContactInfo.Email` 读取
- **官方网站** - 从 `ContactInfo.Website` 读取

### 显示控制配置
- **字段可见性控制** - 可单独控制每个字段的显示/隐藏
- **布局样式选择** - 支持多种布局模式
- **标题和边框设置** - 可自定义组件外观

### 布局样式
1. **Default (默认布局)** - 网格布局，适合大多数场景
2. **TwoColumns (双列布局)** - 双列展示，适合信息较多的情况
3. **Cards (卡片布局)** - 分卡片展示基本信息和联系信息，视觉层次清晰
4. **Minimal (简洁布局)** - 紧凑的列表式布局，适合空间有限的场景
5. **Timeline (时间线布局)** - 时间线式展示，视觉效果突出，适合强调企业发展

### 显示设置
- **显示标题** - 可选择是否显示组件标题
- **自定义标题** - 可自定义标题文字
- **显示边框** - 可控制是否显示边框
- **背景样式** - 白色/灰色/透明背景可选

## 技术实现

### 文件结构
```
src/Web/
├── ViewModels/Components/
│   └── CompanyBasicInfoComponentViewModel.cs
├── ViewComponents/
│   └── CompanyBasicInfoViewComponent.cs
└── Views/Shared/Components/CompanyBasicInfo/
    ├── Default.cshtml          # 默认网格布局
    ├── default.json
    ├── TwoColumns.cshtml       # 双列布局
    ├── twocolumns.json
    ├── Cards.cshtml            # 卡片布局
    ├── cards.json
    ├── Minimal.cshtml          # 简洁布局
    ├── minimal.json
    ├── Timeline.cshtml         # 时间线布局
    └── timeline.json
```

### 多语言支持
组件完全支持多语言，包含以下语言：
- **中文 (zh)** - 默认语言
- **英文 (en)** - 英语支持
- **日文 (ja)** - 日语支持

所有文本资源已添加到 `FormResource.resx` 文件中。

### 响应式设计
- 使用 Tailwind CSS 实现响应式布局
- 支持移动端、平板和桌面端
- 自动适配不同屏幕尺寸

### 主题支持
- 完全支持深色/浅色主题切换
- 使用主题变量 (`primary-*`) 而非硬编码颜色
- 符合项目的主题系统规范

## 使用方法

### 在页面中使用组件

```html
<!-- 默认布局 -->
@await Component.InvokeAsync("CompanyBasicInfo", new { model = componentData, variant = "Default" })

<!-- 双列布局 -->
@await Component.InvokeAsync("CompanyBasicInfo", new { model = componentData, variant = "TwoColumns" })

<!-- 卡片布局 -->
@await Component.InvokeAsync("CompanyBasicInfo", new { model = componentData, variant = "Cards" })

<!-- 简洁布局 -->
@await Component.InvokeAsync("CompanyBasicInfo", new { model = componentData, variant = "Minimal" })

<!-- 时间线布局 -->
@await Component.InvokeAsync("CompanyBasicInfo", new { model = componentData, variant = "Timeline" })
```

### 配置模型示例

```csharp
var componentConfig = new CompanyBasicInfoComponentViewModel
{
    // 显示设置
    Layout = "grid",
    ShowTitle = true,
    TitleText = "会社概要", // 可选，为空时使用默认标题
    ShowBorder = true,
    BackgroundStyle = "white",
    
    // 字段显示控制
    ShowEstablishedDate = true,
    ShowCapital = true,
    ShowEmployeeScale = true,
    ShowPresident = true,
    ShowAddress = true,
    ShowPostalCode = true,
    ShowPhone = true,
    ShowEmail = true,
    ShowWebsite = true,
    ShowRegistrationNumber = false, // 默认隐藏工商注册号
    
    // CompanyData 会自动从 CompanyService 获取
};
```

### 数据架构说明

组件采用**数据与配置分离**的架构：

1. **数据层**：通过 `CompanyService.GetCompany()` 获取公司实体数据
2. **配置层**：通过组件配置控制显示样式和字段可见性
3. **展示层**：根据配置动态渲染公司数据

这种架构的优势：
- **数据一致性**：所有使用公司信息的地方都从同一数据源读取
- **维护简便**：公司信息变更只需在一处修改
- **灵活展示**：不同页面可以有不同的显示配置
- **多语言支持**：自动根据当前语言显示对应内容

## 设计特点

### 符合日本企业网站标准
- 采用日本企业网站常见的信息展示方式
- 信息层次清晰，易于阅读
- 专业、简洁的视觉设计

### 化学行业适配
- 支持显示行业特有信息（如ISO认证、安全资质等）
- 可通过自定义字段添加行业特定内容
- 符合化学行业企业的信息披露需求

### 无障碍访问
- 使用语义化HTML标签
- 包含适当的ARIA标签
- 支持键盘导航
- 图标配有文字说明

## 配置选项

### 布局选项
- `grid` - 网格布局（默认）
- `list` - 列表布局
- `card` - 卡片布局

### 背景样式
- `white` - 白色背景（默认）
- `gray` - 灰色背景
- `transparent` - 透明背景

### 图标选项
组件支持多种FontAwesome图标：
- `calendar` - 日历（成立时间）
- `coins` - 硬币（资本金）
- `user` - 用户（代表人）
- `users` - 用户组（员工数）
- `briefcase` - 公文包（业务类型）
- `map-pin` - 地图标记（地址）
- `phone` - 电话
- `envelope` - 邮件
- `globe` - 网站
- `building` - 建筑
- `certificate` - 证书
- `award` - 奖项
- `handshake` - 握手
- `chart-line` - 图表
- `industry` - 工业
- `shield-alt` - 盾牌

## 更新记录

### v1.2.0 (2024-09-24)
- 新增代表取締役社長字段 - 从Executive实体读取社长信息
- 新增邮编字段 - 从ContactInfo.PostalCode读取
- 完善所有变体的字段显示控制
- 优化多语言资源支持

### v1.1.0 (2024-09-24)
- 新增Cards变体 - 卡片式布局，分别展示基本信息和联系信息
- 新增Minimal变体 - 简洁列表式布局，适合空间有限场景
- 新增Timeline变体 - 时间线式布局，视觉效果突出
- 优化数据与配置分离架构
- 完善多语言资源支持

### v1.0.0 (2024-09-24)
- 初始版本发布
- 支持基本信息展示
- 实现Default和TwoColumns两种布局
- 完整的多语言支持
- 响应式设计和主题支持
- 数据从Company实体读取

## 注意事项

1. **资源文件更新** - 新增的资源键已添加到FormResource.resx及其多语言版本
2. **组件注册** - 已在components.json中注册新组件
3. **主题兼容** - 使用主题变量确保与所有主题兼容
4. **性能优化** - 组件采用延迟加载和条件渲染优化性能

## 扩展建议

1. **更多布局** - 可以添加更多布局变体（如Timeline时间线布局）
2. **动画效果** - 可以添加进入动画和交互效果
3. **数据源集成** - 可以与公司信息管理系统集成
4. **导出功能** - 可以添加PDF导出或打印功能

这个组件为企业网站提供了专业、灵活的公司信息展示解决方案，特别适合日本化学行业企业的需求。