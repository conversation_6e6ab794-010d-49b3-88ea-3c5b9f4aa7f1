@model MlSoft.Sites.Web.ViewModels.Components.CompanyBasicInfoComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var showBorder = Model?.ShowBorder ?? true;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";

    // Get company data
    var company = Model?.CompanyData;
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var companyLocale = company?.Locale?.ContainsKey(culture) == true ? company.Locale[culture] : null;
    var contactInfo = company?.ContactInfo;
    var contactLocale = contactInfo?.Locale?.ContainsKey(culture) == true ? contactInfo.Locale[culture] : null;
    var presidentLocale = president?.Locale?.ContainsKey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("company-basic-info-minimal");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    var cardClass = showBorder ? 
        "border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm" : 
        "rounded-lg";

    // Helper function to get employee scale display text
    string GetEmployeeScaleText(MlSoft.Sites.Model.Entities.Enums.EmployeeScale? scale) => scale switch
    {
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Small => culture == "ja" ? "1-50名" : culture == "en" ? "1-50 employees" : "1-50人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Medium => culture == "ja" ? "51-300名" : culture == "en" ? "51-300 employees" : "51-300人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Large => culture == "ja" ? "301-1000名" : culture == "en" ? "301-1000 employees" : "301-1000人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Enterprise => culture == "ja" ? "1000名以上" : culture == "en" ? "1000+ employees" : "1000人以上",
        _ => ""
    };

    // Prepare basic info items from company data
    var basicInfoItems = new List<(string Label, string Value)>();
    
    // 成立时间
    if (Model?.ShowEstablishedDate == true && company?.EstablishedDate != null)
    {
        var dateFormat = culture == "ja" ? "yyyy年M月d日" : culture == "en" ? "MMMM d, yyyy" : "yyyy年M月d日";
        basicInfoItems.Add((FormRes["CompanyBasicInfo_EstablishedDate"], company.EstablishedDate.ToString(dateFormat)));
    }
    
    // 资本金
    if (Model?.ShowCapital == true && company?.Capital != null)
    {
        var capitalText = company.Currency switch
        {
            "JPY" => $"{company.Capital:N0}円",
            "USD" => $"${company.Capital:N0}",
            "CNY" => $"¥{company.Capital:N0}",
            _ => $"{company.Capital:N0} {company.Currency}"
        };
        basicInfoItems.Add((FormRes["CompanyBasicInfo_Capital"], capitalText));
    }
    
    // 员工规模
    if (Model?.ShowEmployeeScale == true && company?.EmployeeScale != null)
    {
        var employeeText = GetEmployeeScaleText(company.EmployeeScale);
        if (!string.IsNullOrEmpty(employeeText))
            basicInfoItems.Add((FormRes["CompanyBasicInfo_EmployeeCount"], employeeText));
    }
    
    // 代表取締役社長
    if (Model?.ShowPresident == true && !string.IsNullOrEmpty(presidentLocale?.Name))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_President"], presidentLocale.Name));
    
    // 工商注册号
    if (Model?.ShowRegistrationNumber == true && !string.IsNullOrEmpty(company?.RegistrationNumber))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_RegistrationNumber"], company.RegistrationNumber));
    
    // 地址
    if (Model?.ShowAddress == true && !string.IsNullOrEmpty(contactLocale?.Address))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_HeadOffice"], contactLocale.Address));
    
    // 邮编
    if (Model?.ShowPostalCode == true && !string.IsNullOrEmpty(contactInfo?.PostalCode))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_PostalCode"], contactInfo.PostalCode));
    
    // 电话
    if (Model?.ShowPhone == true && !string.IsNullOrEmpty(contactInfo?.Phone))
        basicInfoItems.Add((SharedRes["Contact_Phone"], contactInfo.Phone));
    
    // 邮箱
    if (Model?.ShowEmail == true && !string.IsNullOrEmpty(contactInfo?.Email))
        basicInfoItems.Add((SharedRes["Contact_Email"], contactInfo.Email));
    
    // 网站
    if (Model?.ShowWebsite == true && !string.IsNullOrEmpty(contactInfo?.Website))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_Website"], contactInfo.Website));
}

<section id="@uniqueId" class="py-8 @containerClass">
    <div class="container max-w-4xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["CompanyBasicInfo_Title"])
                </h2>
            </div>
        }

        @if (!string.IsNullOrEmpty(companyLocale?.CompanyName))
        {
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-primary-600 dark:text-primary-400">@companyLocale.CompanyName</h3>
            </div>
        }

        @if (basicInfoItems.Any())
        {
            <div class="@cardClass p-6">
                <dl class="space-y-3">
                    @foreach (var item in basicInfoItems)
                    {
                        <div class="flex flex-col sm:flex-row sm:items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                            <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 sm:w-1/3 mb-1 sm:mb-0">@item.Label</dt>
                            <dd class="text-sm text-gray-900 dark:text-white sm:w-2/3 break-words">
                                @if (item.Label == SharedRes["Contact_Email"] && item.Value.Contains("@"))
                                {
                                    <a href="mailto:@item.Value" class="text-primary-600 dark:text-primary-400 hover:underline">@item.Value</a>
                                }
                                else if (item.Label == FormRes["CompanyBasicInfo_Website"] && (item.Value.StartsWith("http") || item.Value.StartsWith("www")))
                                {
                                    <a href="@item.Value" target="_blank" class="text-primary-600 dark:text-primary-400 hover:underline">@item.Value</a>
                                }
                                else
                                {
                                    @item.Value
                                }
                            </dd>
                        </div>
                    }
                </dl>
            </div>
        }
    </div>
</section>