@model MlSoft.Sites.Web.ViewModels.Components.CompanyBasicInfoComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";

    // Get company data
    var company = Model?.CompanyData;
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var companyLocale = company?.Locale?.ContainsKey(culture) == true ? company.Locale[culture] : null;
    var contactInfo = company?.ContactInfo;
    var contactLocale = contactInfo?.Locale?.ContainsKey(culture) == true ? contactInfo.Locale[culture] : null;
    var presidentLocale = president?.Locale?.Contains<PERSON>ey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("company-basic-info-timeline");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    // Helper function to get employee scale display text
    string GetEmployeeScaleText(MlSoft.Sites.Model.Entities.Enums.EmployeeScale? scale) => scale switch
    {
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Small => culture == "ja" ? "1-50名" : culture == "en" ? "1-50 employees" : "1-50人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Medium => culture == "ja" ? "51-300名" : culture == "en" ? "51-300 employees" : "51-300人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Large => culture == "ja" ? "301-1000名" : culture == "en" ? "301-1000 employees" : "301-1000人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Enterprise => culture == "ja" ? "1000名以上" : culture == "en" ? "1000+ employees" : "1000人以上",
        _ => ""
    };

    // Prepare timeline items from company data
    var timelineItems = new List<(string Icon, string Label, string Value, string Color)>();
    
    // 成立时间
    if (Model?.ShowEstablishedDate == true && company?.EstablishedDate != null)
    {
        var dateFormat = culture == "ja" ? "yyyy年M月d日" : culture == "en" ? "MMMM d, yyyy" : "yyyy年M月d日";
        timelineItems.Add(("calendar", FormRes["CompanyBasicInfo_EstablishedDate"], company.EstablishedDate.ToString(dateFormat), "bg-blue-500"));
    }
    
    // 资本金
    if (Model?.ShowCapital == true && company?.Capital != null)
    {
        var capitalText = company.Currency switch
        {
            "JPY" => $"{company.Capital:N0}円",
            "USD" => $"${company.Capital:N0}",
            "CNY" => $"¥{company.Capital:N0}",
            _ => $"{company.Capital:N0} {company.Currency}"
        };
        timelineItems.Add(("coins", FormRes["CompanyBasicInfo_Capital"], capitalText, "bg-green-500"));
    }
    
    // 员工规模
    if (Model?.ShowEmployeeScale == true && company?.EmployeeScale != null)
    {
        var employeeText = GetEmployeeScaleText(company.EmployeeScale);
        if (!string.IsNullOrEmpty(employeeText))
            timelineItems.Add(("users", FormRes["CompanyBasicInfo_EmployeeCount"], employeeText, "bg-purple-500"));
    }
    
    // 代表取締役社長
    if (Model?.ShowPresident == true && !string.IsNullOrEmpty(presidentLocale?.Name))
        timelineItems.Add(("user-tie", FormRes["CompanyBasicInfo_President"], presidentLocale.Name, "bg-orange-500"));
    
    // 地址
    if (Model?.ShowAddress == true && !string.IsNullOrEmpty(contactLocale?.Address))
        timelineItems.Add(("map-pin", FormRes["CompanyBasicInfo_HeadOffice"], contactLocale.Address, "bg-red-500"));
    
    // 邮编
    if (Model?.ShowPostalCode == true && !string.IsNullOrEmpty(contactInfo?.PostalCode))
        timelineItems.Add(("mail-bulk", FormRes["CompanyBasicInfo_PostalCode"], contactInfo.PostalCode, "bg-pink-500"));
    
    // 联系方式
    if (Model?.ShowPhone == true && !string.IsNullOrEmpty(contactInfo?.Phone))
        timelineItems.Add(("phone", SharedRes["Contact_Phone"], contactInfo.Phone, "bg-yellow-500"));
    
    if (Model?.ShowEmail == true && !string.IsNullOrEmpty(contactInfo?.Email))
        timelineItems.Add(("envelope", SharedRes["Contact_Email"], contactInfo.Email, "bg-indigo-500"));
    
    if (Model?.ShowWebsite == true && !string.IsNullOrEmpty(contactInfo?.Website))
        timelineItems.Add(("globe", FormRes["CompanyBasicInfo_Website"], contactInfo.Website, "bg-teal-500"));
}

<section id="@uniqueId" class="py-12 @containerClass">
    <div class="container max-w-4xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["CompanyBasicInfo_Title"])
                </h2>
                @if (!string.IsNullOrEmpty(companyLocale?.CompanyName))
                {
                    <p class="text-xl text-primary-600 dark:text-primary-400 font-semibold">@companyLocale.CompanyName</p>
                }
            </div>
        }

        @if (timelineItems.Any())
        {
            <div class="relative">
                <!-- Timeline line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
                
                <div class="space-y-8">
                    @for (int i = 0; i < timelineItems.Count; i++)
                    {
                        var item = timelineItems[i];
                        var isLast = i == timelineItems.Count - 1;
                        
                        <div class="relative flex items-start">
                            <!-- Timeline dot -->
                            <div class="flex-shrink-0 w-16 h-16 @item.Color rounded-full flex items-center justify-center shadow-lg z-10">
                                <i class="fas <EMAIL> text-white text-lg"></i>
                            </div>
                            
                            <!-- Content -->
                            <div class="ml-6 flex-1">
                                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">@item.Label</h3>
                                    <div class="text-gray-700 dark:text-gray-300">
                                        @if (item.Label == SharedRes["Contact_Email"] && item.Value.Contains("@"))
                                        {
                                            <a href="mailto:@item.Value" class="text-primary-600 dark:text-primary-400 hover:underline">@item.Value</a>
                                        }
                                        else if (item.Label == FormRes["CompanyBasicInfo_Website"] && (item.Value.StartsWith("http") || item.Value.StartsWith("www")))
                                        {
                                            <a href="@item.Value" target="_blank" class="text-primary-600 dark:text-primary-400 hover:underline">@item.Value</a>
                                        }
                                        else if (item.Label == FormRes["CompanyBasicInfo_HeadOffice"])
                                        {
                                            <div class="whitespace-pre-line">@item.Value</div>
                                        }
                                        else
                                        {
                                            @item.Value
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</section>