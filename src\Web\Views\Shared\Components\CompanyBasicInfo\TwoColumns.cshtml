@model MlSoft.Sites.Web.ViewModels.Components.CompanyBasicInfoComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var showBorder = Model?.ShowBorder ?? true;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";

    // Get company data
    var company = Model?.CompanyData;
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var companyLocale = company?.Locale?.ContainsKey(culture) == true ? company.Locale[culture] : null;
    var contactInfo = company?.ContactInfo;
    var contactLocale = contactInfo?.Locale?.ContainsKey(culture) == true ? contactInfo.Locale[culture] : null;
    var presidentLocale = president?.Locale?.ContainsKey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("company-basic-info-two-cols");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    var cardClass = showBorder ? 
        "border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm" : 
        "rounded-lg";

    // Helper function to get employee scale display text
    string GetEmployeeScaleText(MlSoft.Sites.Model.Entities.Enums.EmployeeScale? scale) => scale switch
    {
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Small => culture == "ja" ? "1-50名" : culture == "en" ? "1-50 employees" : "1-50人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Medium => culture == "ja" ? "51-300名" : culture == "en" ? "51-300 employees" : "51-300人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Large => culture == "ja" ? "301-1000名" : culture == "en" ? "301-1000 employees" : "301-1000人",
        MlSoft.Sites.Model.Entities.Enums.EmployeeScale.Enterprise => culture == "ja" ? "1000名以上" : culture == "en" ? "1000+ employees" : "1000人以上",
        _ => ""
    };

    // Prepare basic info items from company data
    var basicInfoItems = new List<(string Label, string Value, string Icon)>();
    
    // 成立时间
    if (Model?.ShowEstablishedDate == true && company?.EstablishedDate != null)
    {
        var dateFormat = culture == "ja" ? "yyyy年M月d日" : culture == "en" ? "MMMM d, yyyy" : "yyyy年M月d日";
        basicInfoItems.Add((FormRes["CompanyBasicInfo_EstablishedDate"], company.EstablishedDate.ToString(dateFormat), "calendar"));
    }
    
    // 资本金
    if (Model?.ShowCapital == true && company?.Capital != null)
    {
        var capitalText = company.Currency switch
        {
            "JPY" => $"{company.Capital:N0}円",
            "USD" => $"${company.Capital:N0}",
            "CNY" => $"¥{company.Capital:N0}",
            _ => $"{company.Capital:N0} {company.Currency}"
        };
        basicInfoItems.Add((FormRes["CompanyBasicInfo_Capital"], capitalText, "coins"));
    }
    
    // 员工规模
    if (Model?.ShowEmployeeScale == true && company?.EmployeeScale != null)
    {
        var employeeText = GetEmployeeScaleText(company.EmployeeScale);
        if (!string.IsNullOrEmpty(employeeText))
            basicInfoItems.Add((FormRes["CompanyBasicInfo_EmployeeCount"], employeeText, "users"));
    }
    
    // 代表取締役社長
    if (Model?.ShowPresident == true && !string.IsNullOrEmpty(presidentLocale?.Name))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_President"], presidentLocale.Name, "user-tie"));
    
    // 工商注册号
    if (Model?.ShowRegistrationNumber == true && !string.IsNullOrEmpty(company?.RegistrationNumber))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_RegistrationNumber"], company.RegistrationNumber, "certificate"));
    
    // 地址
    if (Model?.ShowAddress == true && !string.IsNullOrEmpty(contactLocale?.Address))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_HeadOffice"], contactLocale.Address, "map-pin"));
    
    // 邮编
    if (Model?.ShowPostalCode == true && !string.IsNullOrEmpty(contactInfo?.PostalCode))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_PostalCode"], contactInfo.PostalCode, "mail-bulk"));
    
    // 电话
    if (Model?.ShowPhone == true && !string.IsNullOrEmpty(contactInfo?.Phone))
        basicInfoItems.Add((SharedRes["Contact_Phone"], contactInfo.Phone, "phone"));
    
    // 邮箱
    if (Model?.ShowEmail == true && !string.IsNullOrEmpty(contactInfo?.Email))
        basicInfoItems.Add((SharedRes["Contact_Email"], contactInfo.Email, "envelope"));
    
    // 网站
    if (Model?.ShowWebsite == true && !string.IsNullOrEmpty(contactInfo?.Website))
        basicInfoItems.Add((FormRes["CompanyBasicInfo_Website"], contactInfo.Website, "globe"));

    // Split items into two columns
    var leftColumnItems = basicInfoItems.Take((basicInfoItems.Count + 1) / 2).ToList();
    var rightColumnItems = basicInfoItems.Skip(leftColumnItems.Count).ToList();
}

<section id="@uniqueId" class="py-12 @containerClass">
    <div class="container max-w-4xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="text-center mb-8">
                <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["CompanyBasicInfo_Title"])
                </h2>
            </div>
        }

        @if (!string.IsNullOrEmpty(companyLocale?.CompanyName))
        {
            <div class="text-center mb-8">
                <h3 class="text-xl font-semibold text-primary-600 dark:text-primary-400">@companyLocale.CompanyName</h3>
            </div>
        }

        @if (basicInfoItems.Any())
        {
            <div class="@cardClass p-8">
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Left Column -->
                    <div class="space-y-6">
                        @foreach (var item in leftColumnItems)
                        {
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                                    <i class="fas <EMAIL> text-primary-600 dark:text-primary-400"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@item.Label</dt>
                                    <dd class="text-base text-gray-900 dark:text-white break-words">@item.Value</dd>
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-6">
                        @foreach (var item in rightColumnItems)
                        {
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                                    <i class="fas <EMAIL> text-primary-600 dark:text-primary-400"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <dt class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">@item.Label</dt>
                                    <dd class="text-base text-gray-900 dark:text-white break-words">@item.Value</dd>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</section>