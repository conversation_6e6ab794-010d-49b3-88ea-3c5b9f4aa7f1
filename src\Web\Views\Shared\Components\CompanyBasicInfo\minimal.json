{"ComponentId": "CompanyBasicInfo", "Id": "Minimal", "Names": {"zh": "简洁布局", "en": "Minimal Layout", "ja": "ミニマルレイアウト"}, "Descriptions": {"zh": "简洁的列表式布局，信息紧凑，适合空间有限的场景", "en": "Clean list-style layout with compact information, suitable for space-limited scenarios", "ja": "シンプルなリストスタイルのレイアウト、コンパクトな情報表示、スペースが限られた場面に適している"}, "formFields": [{"name": "ShowEstablishedDate", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowEstablishedDate", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 1, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowCapital", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowCapital", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 2, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowEmployeeScale", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowEmployeeScale", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 3, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowPresident", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowPresident", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 4, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowAddress", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowAddress", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 5, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowPostalCode", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowPostalCode", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 6, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowPhone", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowPhone", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 7, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowEmail", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowEmail", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 8, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowWebsite", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowWebsite", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 9, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowRegistrationNumber", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowRegistrationNumber", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 10, "layout": "inline", "collapsed": true}, "defaultValue": false}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowTitle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-4", "order": 31, "layout": "inline"}, "defaultValue": true}, {"name": "ShowBorder", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowBorder", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-4", "order": 32, "layout": "inline"}, "defaultValue": true}, {"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:CompanyBasicInfo_TitleText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-12", "order": 33, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:CompanyBasicInfo_BackgroundStyle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-4", "order": 34, "layout": "inline"}, "options": [{"value": "white", "label": "@FormResource:Background_White"}, {"value": "gray", "label": "@FormResource:<PERSON>_<PERSON>"}, {"value": "transparent", "label": "@FormResource:Background_Transparent"}], "defaultValue": "white"}]}