{"ComponentId": "CompanyBasicInfo", "Id": "Timeline", "Names": {"zh": "时间线布局", "en": "Timeline Layout", "ja": "タイムラインレイアウト"}, "Descriptions": {"zh": "以时间线形式展示公司信息，视觉效果突出，适合强调企业发展历程", "en": "Display company information in timeline format with prominent visual effects, suitable for emphasizing corporate development", "ja": "タイムライン形式で会社情報を表示、視覚効果が際立ち、企業の発展過程を強調するのに適している"}, "formFields": [{"name": "ShowEstablishedDate", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowEstablishedDate", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 1}, "defaultValue": true}, {"name": "ShowCapital", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowCapital", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 2}, "defaultValue": true}, {"name": "ShowEmployeeScale", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowEmployeeScale", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 3}, "defaultValue": true}, {"name": "ShowPresident", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowPresident", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 4}, "defaultValue": true}, {"name": "ShowAddress", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowAddress", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 5}, "defaultValue": true}, {"name": "ShowPostalCode", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowPostalCode", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 6}, "defaultValue": true}, {"name": "ShowPhone", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowPhone", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 7}, "defaultValue": true}, {"name": "ShowEmail", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowEmail", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 8}, "defaultValue": true}, {"name": "ShowWebsite", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowWebsite", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 9}, "defaultValue": true}, {"name": "ShowRegistrationNumber", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowRegistrationNumber", "display": {"group": "@FormResource:FormGroups_FieldDisplay", "width": "col-span-4", "order": 10}, "defaultValue": false}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:CompanyBasicInfo_ShowTitle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-6", "order": 31}, "defaultValue": true}, {"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:CompanyBasicInfo_TitleText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-12", "order": 33, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:CompanyBasicInfo_BackgroundStyle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-6", "order": 34}, "options": [{"value": "white", "label": "@FormResource:Background_White"}, {"value": "gray", "label": "@FormResource:<PERSON>_<PERSON>"}, {"value": "transparent", "label": "@FormResource:Background_Transparent"}], "defaultValue": "white"}]}