@model MlSoft.Sites.Web.ViewModels.Components.CompanyHistoryComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Model.Entities.Enums
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<AdminResource> AdminRes

@{
	// Extract data from ViewModel with null-safe defaults
	var layout = Model?.Layout ?? "timeline";
	var showTitle = Model?.ShowTitle ?? true;
	var titleText =  Model?.TitleText;
	var descriptionText = Model?.Description ?? "";
	var backgroundStyle = Model?.BackgroundStyle ?? "white";
	var showEventImages = Model?.ShowEventImages ?? true;

	var showYearLabels = Model?.ShowYearLabels ?? true;
	var enableAnimation = Model?.EnableAnimation ?? true;
	var borderRadius = Model?.BorderRadius ?? "md";
	var spacing = Model?.Spacing ?? "normal";
	var historyData = Model?.HistoryData ?? new List<MlSoft.Sites.Model.Entities.History.CompanyHistory>();

	// Generate CSS classes
	var backgroundClass = backgroundStyle switch
	{
		"gray" => "bg-gray-50 dark:bg-gray-900",
		"transparent" => "bg-transparent",
		_ => "bg-white dark:bg-gray-800"
	};

	var spacingClass = spacing switch
	{
		"compact" => "space-y-4",
		"loose" => "space-y-12",
		_ => "space-y-8"
	};

	var borderRadiusClass = borderRadius switch
	{
		"sm" => "rounded-sm",
		"lg" => "rounded-lg",
		"xl" => "rounded-xl",
		"full" => "rounded-full",
		_ => "rounded-md"
	};

	var uniqueId = JObjectHelper.GenerateId("company-history");



	// Get current culture for multilingual content
	var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";

	// Helper method to get localized content
	string GetLocalizedContent(Dictionary<string, MlSoft.Sites.Model.Entities.LocaleFields.CompanyHistoryLocaleFields> locale, string field)
	{
		if (locale != null && locale.ContainsKey(culture))
		{
			var localeData = locale[culture];
			return field switch
			{
				"title" => localeData?.EventTitle ?? "",
				"description" => localeData?.EventDescription ?? "",
				_ => ""
			};
		}
		return "";
	}

	// Helper method to format event type
	string GetEventTypeText(HistoryEventType eventType)
	{
		return eventType switch
		{
			HistoryEventType.Establishment => AdminRes["Establishment"],
			HistoryEventType.Expansion => AdminRes["Expansion"],
			HistoryEventType.ProductLaunch => AdminRes["ProductLaunch"],
			HistoryEventType.Acquisition => AdminRes["Acquisition"],
			HistoryEventType.Partnership => AdminRes["Partnership"],
			HistoryEventType.Award => AdminRes["Award"],
			HistoryEventType.Milestone => AdminRes["Milestone"],
			_ => AdminRes["Other"]
		};
	}
}

<div class="company-history-component @backgroundClass" id="@uniqueId">
	<div class="container  max-w-7xl  mx-auto px-4 py-8">

		@if (showTitle)
		{
			<div class="mb-8">
				<h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">@titleText</h2>
				<div class="h-1 w-16 bg-primary-600"></div>
				<p class="text-gray-600 dark:text-gray-400 mt-4">
					@SharedRes["CompanyHistoryDescription"]
				</p>
			</div>
		}

		@if (layout == "timeline")
		{
			<div class="timeline-container @spacingClass">

				<div class="space-y-8">
					@foreach (var historyEvent in historyData.OrderByDescending(h => h.EventDate))
					{
						<div class="bg-white border border-gray-200 @borderRadiusClass shadow dark:bg-gray-800 dark:border-gray-700
		                                                @(enableAnimation ? "transform transition-all duration-300 hover:shadow-lg hover:scale-105" : "")">
							<div class="p-6">
								<div class="flex gap-6">
									@if (showYearLabels)
									{
										<div class="flex-shrink-0 w-24 text-center">
											<div class="bg-primary-600 text-white px-4 py-2 @borderRadiusClass dark:bg-primary-500">
												<div class="text-lg font-bold">@historyEvent.EventDate.Year</div>
												@if (historyEvent.EventDate.Month != 1 || historyEvent.EventDate.Day != 1)
												{
													<div class="text-xs">@historyEvent.EventDate.ToString("MMMM")</div>
												}
											</div>
										</div>
									}
									<div class="flex-1">
										@if (showEventImages && !string.IsNullOrEmpty(historyEvent.ImageUrl))
										{
											<div class="mb-4">
												<img src="@historyEvent.ImageUrl" alt="@GetLocalizedContent(historyEvent.Locale, "title")"
													 class="w-full h-32 object-cover @borderRadiusClass" loading="lazy">
											</div>
										}
										<div class="flex items-start justify-between mb-2">
											<h3 class="text-xl font-semibold text-gray-900 dark:text-white">
												@GetLocalizedContent(historyEvent.Locale, "title")
											</h3>
											<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300">
												@GetEventTypeText(historyEvent.EventType)
											</span>
										</div>
										<p class="text-gray-700 dark:text-gray-300 leading-relaxed">
											@GetLocalizedContent(historyEvent.Locale, "description")
										</p>
									</div>
								</div>
							</div>
						</div>
					}
				</div>
			</div>
		}
		else if (layout == "cards")
		{
			<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				@foreach (var historyEvent in historyData.OrderByDescending(h => h.EventDate))
				{
					<div class="bg-white border border-gray-200 @borderRadiusClass shadow dark:bg-gray-800 dark:border-gray-700
		                                @(enableAnimation ? "transform transition-all duration-300 hover:shadow-lg hover:scale-105" : "")">
						@if (showEventImages && !string.IsNullOrEmpty(historyEvent.ImageUrl))
						{
							<img src="@historyEvent.ImageUrl" alt="@GetLocalizedContent(historyEvent.Locale, "title")"
								 class="w-full h-48 object-cover @borderRadiusClass" loading="lazy">
						}
						<div class="p-5">
							<div class="flex items-center justify-between mb-3">
								<span class="text-sm font-medium text-primary-600 dark:text-primary-400">
									@historyEvent.EventDate.ToString("yyyy-MM")
								</span>
								<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
									@GetEventTypeText(historyEvent.EventType)
								</span>
							</div>
							<h3 class="mb-2 text-lg font-bold tracking-tight text-gray-900 dark:text-white">
								@GetLocalizedContent(historyEvent.Locale, "title")
							</h3>
							<p class="mb-3 font-normal text-gray-700 dark:text-gray-400 text-sm">
								@GetLocalizedContent(historyEvent.Locale, "description")
							</p>
						</div>
					</div>
				}
			</div>
		}

		@if (!historyData.Any())
		{
			<div class="text-center py-12">
				<div class="text-gray-500 dark:text-gray-400">
					<svg class="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<p class="text-lg">@SharedRes["NoHistoryDataAvailable"]</p>
				</div>
			</div>
		}
	</div>
</div>

@if (enableAnimation)
{
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			// Initialize intersection observer for animations
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						entry.target.classList.add('animate-fade-in-up');
					}
				});
			}, { threshold: 0.1 });

			const historyCards = document.querySelectorAll('#@uniqueId .bg-white, #@uniqueId .border');
			historyCards.forEach(card => observer.observe(card));
		});
	</script>
}