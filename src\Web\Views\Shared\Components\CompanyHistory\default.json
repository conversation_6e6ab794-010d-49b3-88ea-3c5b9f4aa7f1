{"ComponentId": "CompanyHistory", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "企业沿革 - 时间轴", "en": "Company History - Timeline", "ja": "企業沿革 - タイムライン"}, "Descriptions": {"zh": "以时间轴形式展示企业发展历程和重要事件", "en": "Display company development history and important events in timeline format", "ja": "企業の発展歴史と重要な出来事をタイムライン形式で表示"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "collapsed": true, "layout": "inline"}, "validation": {"maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "collapsed": true, "layout": "inline"}, "validation": {"maxLength": 100}}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:ShowTitle", "defaultValue": true, "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 2}}, {"name": "Layout", "type": "select", "label": "@FormResource:FormFields_Layout", "options": [{"value": "timeline", "label": "@FormResource:Layout_List"}, {"value": "cards", "label": "@FormResource:Layout_Card"}], "defaultValue": "timeline", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 3}}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:CompanyBasicInfo_BackgroundStyle", "options": [{"value": "white", "label": "@FormResource:Background_White"}, {"value": "gray", "label": "@FormResource:<PERSON>_<PERSON>"}, {"value": "transparent", "label": "@FormResource:Background_Transparent"}], "defaultValue": "white", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 4}}, {"name": "ShowEventImages", "type": "checkbox", "label": "@FormResource:FormFields_ShowImages", "defaultValue": false, "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 7}}, {"name": "EnableAnimation", "type": "checkbox", "label": "@FormResource:EnableAnimation", "defaultValue": false, "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 13}}, {"name": "BorderRadius", "type": "select", "label": "@FormResource:FormFields_BorderRadius", "options": [{"value": "sm", "label": "@FormResource:SmallBorderRadius"}, {"value": "md", "label": "@FormResource:NormalBorderRadius"}, {"value": "lg", "label": "@FormResource:LargeBorderRadius"}], "defaultValue": "md", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 17}}, {"name": "Spacing", "type": "select", "label": "@FormResource:FormFields_SpacingSettings", "options": [{"value": "compact", "label": "@FormResource:TightSpacing"}, {"value": "normal", "label": "@FormResource:NormalSpacing"}, {"value": "loose", "label": "@FormResource:LooseSpacing"}], "defaultValue": "normal", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 18}}]}