@model MlSoft.Sites.Web.ViewModels.Components.CompanyLocationsComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Model.Entities.Enums
@inject IStringLocalizer<AdminResource> AdminRes

@{
    var locations = Model?.Locations ?? new List<MlSoft.Sites.Model.Entities.Company.CompanyLocation>();

    var showTitle = Model?.ShowTitle ?? true;
    var title = Model?.Title ?? "";
    var subtitle = Model?.Subtitle ?? "";
    var layout = Model?.Layout ?? "grid";
    var showMap = Model?.ShowMap ?? true;
    var showContactInfo = Model?.ShowContactInfo ?? true;
    var showPrimaryBadge = Model?.ShowPrimaryBadge ?? true;
    var itemsPerRow = Model?.ItemsPerRow ?? 3;
    var backgroundColor = Model?.BackgroundColor ?? "transparent";

    // Get current language for localization
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";

    var uniqueId = JObjectHelper.GenerateId("companylocations");

    var gridClass = itemsPerRow switch
    {
        1 => "grid-cols-1",
        2 => "grid-cols-1 md:grid-cols-2",
        3 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
        4 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
        _ => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
    };

    var backgroundClass = backgroundColor switch
    {
        "white" => "bg-white dark:bg-gray-800",
        "gray" => "bg-gray-50 dark:bg-gray-900",
        _ => ""
    };

    string GetLocationTypeLabel(LocationType type) => type switch
    {
        LocationType.Headquarters => AdminRes["Headquarters"],
        LocationType.Branch => AdminRes["Branch"],
        LocationType.Factory => AdminRes["Factory"],
        LocationType.Office => AdminRes["Office"],
        LocationType.Laboratory => AdminRes["Laboratory"],
        LocationType.Warehouse => AdminRes["Warehouse"],
        _ => AdminRes["Office"]
    };

    string GetLocationTypeIcon(LocationType type) => type switch
    {
        LocationType.Headquarters => "fa-building",
        LocationType.Branch => "fa-code-branch",
        LocationType.Factory => "fa-industry",
        LocationType.Office => "fa-briefcase",
        LocationType.Laboratory => "fa-flask",
        LocationType.Warehouse => "fa-warehouse",
        _ => "fa-map-marker-alt"
    };
}

<section id="@uniqueId" class="py-8 sm:py-16 @backgroundClass">
    <style>
        #@uniqueId .map-embed {
            position: relative;
            width: 100%;
            aspect-ratio: 16 / 9;
            overflow: hidden;
            border-radius: 0.5rem;
        }

        #@uniqueId .map-embed iframe,
        #@uniqueId .map-embed > * {
            position: absolute;
            inset: 0;
            width: 100% !important;
            height: 100% !important;
            border: 0;
        }
    </style>

    <div class="max-w-screen-xl mx-auto px-4 lg:px-6">
        @if (showTitle && !string.IsNullOrEmpty(title))
        {
            <div class="max-w-screen-md mb-8">
                <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">
                    @title
                </h2>
                @if (!string.IsNullOrEmpty(subtitle))
                {
                    <p class="text-gray-500 sm:text-xl dark:text-gray-400">
                        @subtitle
                    </p>
                }
            </div>
        }



        @if (locations.Any())
        {
            <div class="grid @gridClass gap-6">
                @foreach (var location in locations.OrderBy(l => l.DisplayOrder))
                {
                    var locationLocale = location.Locale?.ContainsKey(culture) == true ? location.Locale[culture] : location.Locale?.FirstOrDefault().Value;
                    var locationName = locationLocale?.LocationName ?? "";
                    var contactLocale = location.ContactInfo?.Locale?.ContainsKey(culture) == true ? location.ContactInfo.Locale[culture] : location.ContactInfo?.Locale?.FirstOrDefault().Value;

                    <div class="bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 hover:shadow-lg transition-shadow duration-300">
                        <div class="p-6">
                            <div class="flex items-start justify-between mb-8">
                                <div class="flex items-start gap-3">
                                    <div class="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                                        <i class="fas @GetLocationTypeIcon(location.Type) text-primary-600 dark:text-primary-300 text-lg"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-1">
                                            @locationName
                                        </h3>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                                            @GetLocationTypeLabel(location.Type)
                                        </span>
                                    </div>
                                </div>
                                @if (showPrimaryBadge && location.IsPrimary)
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                        <i class="fas fa-star text-xs mr-1"></i>
                                        @AdminRes["PrimaryLocation"]
                                    </span>
                                }
                            </div>

                            @if (showContactInfo && location.ContactInfo != null)
                            {
                                <div class="space-y-8 gap-8">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        @* Address full width *@
                                        @if (!string.IsNullOrEmpty(contactLocale?.Address))
                                        {
                                            <div class="flex items-start gap-3 text-gray-700 dark:text-gray-300">
                                                <i class="fas fa-map-marker-alt text-primary-600 dark:text-primary-400 mt-0.5 flex-shrink-0"></i>
                                                <div>
                                                    @if (!string.IsNullOrEmpty(location.ContactInfo?.PostalCode))
                                                    {
                                                        <div class="text-gray-600 dark:text-gray-400">〒 @location.ContactInfo?.PostalCode</div>
                                                    }
                                                    <div>@contactLocale.Address</div>
                                                </div>
                                            </div>
                                        }

                                        @* TEL *@
                                        @if (!string.IsNullOrEmpty(location.ContactInfo.Phone))
                                        {
                                            <div class="flex items-start gap-3 text-gray-700 dark:text-gray-300">
                                                <i class="fas fa-phone text-primary-600 dark:text-primary-400 mt-0.5 flex-shrink-0"></i>
                                                <div>
                                                    <div class="text-xs uppercase tracking-wide text-gray-500 dark:text-gray-400">TEL</div>
                                                    <a href="tel:@location.ContactInfo.Phone" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                                        @location.ContactInfo.Phone
                                                    </a>
                                                </div>
                                            </div>
                                        }

                                        @* EMAIL *@
                                        @if (!string.IsNullOrEmpty(location.ContactInfo.Email))
                                        {
                                            <div class="flex items-start gap-3 text-gray-700 dark:text-gray-300">
                                                <i class="fas fa-envelope text-primary-600 dark:text-primary-400 mt-0.5 flex-shrink-0"></i>
                                                <div>
                                                    <div class="text-xs uppercase tracking-wide text-gray-500 dark:text-gray-400">EMAIL</div>
                                                    <a href="mailto:@location.ContactInfo.Email" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors break-all">
                                                        @location.ContactInfo.Email
                                                    </a>
                                                </div>
                                            </div>
                                        }

                                        @* Business Hours *@
                                        @if (!string.IsNullOrEmpty(contactLocale?.BusinessHours))
                                        {
                                            <div class="flex items-start gap-3 text-gray-700 dark:text-gray-300">
                                                <i class="fas fa-clock text-primary-600 dark:text-primary-400 mt-0.5 flex-shrink-0"></i>
                                                <div>
                                                    <div class="text-xs tracking-wide text-gray-500 dark:text-gray-400">営業時間</div>
                                                    <div>@contactLocale.BusinessHours</div>
                                                </div>
                                            </div>
                                        }

                                        @* Access Info optional (full width) *@
                                        @if (!string.IsNullOrEmpty(contactLocale?.AccessInfo))
                                        {
                                            <div class="md:col-span-2 flex items-start gap-3 text-gray-700 dark:text-gray-300">
                                                <i class="fas fa-route text-primary-600 dark:text-primary-400 mt-0.5 flex-shrink-0"></i>
                                                <div>
                                                    <div class="text-xs tracking-wide text-gray-500 dark:text-gray-400">アクセス</div>
                                                    <div>@contactLocale.AccessInfo</div>
                                                </div>
                                            </div>
                                        }
                                    </div>

                                    @if (showMap && !string.IsNullOrEmpty(location.ContactInfo.MapLink))
                                    {
                                        <div class="map-embed mt-2">
                                            @Html.Raw(location.ContactInfo.MapLink)
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-map-marker-alt text-2xl text-gray-400 dark:text-gray-500"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">@AdminRes["NoLocationRecords"]</h3>
                <p class="text-gray-500 dark:text-gray-400">@AdminRes["NoDataAvailable"]</p>
            </div>
        }
    </div>
</section>