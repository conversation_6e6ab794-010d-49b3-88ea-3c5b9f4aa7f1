@model MlSoft.Sites.Web.ViewModels.Components.CompanyOverviewComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedR<PERSON>
@inject IStringLocalizer<AdminResource> AdminR<PERSON>
@inject IStringLocalizer<FormResource> FormRes

@{
	// Extract data from ViewModel with null-safe defaults
	var description = Model?.Description;
	var philosophyContent = Model?.PhilosophyContent;
	var companyImageAlt = Model?.CompanyImageAlt;

	// Generate unique ID for the component
	var uniqueId = JObjectHelper.GenerateId("company-overview");

	string ProcessFilePath(string? filePath) =>
		string.IsNullOrEmpty(filePath) ? "/images/placeholder-office.jpg" :
		filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-16 lg:py-24 bg-gray-50 dark:bg-gray-900/50">
	<div class="container max-w-7xl mx-auto px-4">
		<div class="text-center mb-12">
			<h2 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">@SharedRes["CompanyProfile_Title"]</h2>
			<p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
				@Html.Raw(description)
			</p>
		</div>

		@if (Model?.Stats?.Any() == true)
		{

			var maxList = Model.Stats.Take(4).ToList();

			<div class="grid grid-cols-2 lg:<EMAIL> gap-6 mb-12">
				@foreach (var stat in maxList)
				{
					<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-6 text-center">
						@if (!string.IsNullOrEmpty(stat.Icon))
						{
							<i class="fa <EMAIL> text-2xl text-primary-600 dark:text-primary-400 mb-3"></i>
						}
						<div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">@stat.Value</div>
						<div class="text-sm text-gray-600 dark:text-gray-300">@stat.Label</div>
					</div>
				}
			</div>
		}

		@if (!string.IsNullOrWhiteSpace(Model?.CompanyImage) || !string.IsNullOrEmpty(philosophyContent))
		{
			<div class="grid lg:grid-cols-2 gap-12 items-center">
				@if (!string.IsNullOrEmpty(philosophyContent))
				{
					<div>
						<h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">@AdminRes["Philosophy"]</h3>
						<p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
							@Html.Raw(philosophyContent)
						</p>
						@if (!string.IsNullOrEmpty(Model?.PhilosophyButtonUrl))
						{
							<div class="py-6">
								<a href="@Model.PhilosophyButtonUrl" class="text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900">
									@SharedRes["Button_ViewDetail"]
								</a>
							</div>
						}
					</div>
				}

				@if (!string.IsNullOrWhiteSpace(Model?.CompanyImage))
				{
					<div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-8">
						<img src="@ProcessFilePath(Model?.CompanyImage)"
							 alt="@companyImageAlt"
							 class="w-full h-64 object-cover rounded-lg"
							 loading="lazy" />
					</div>
				}
			</div>
		}
	</div>
</section>