{"ComponentId": "CompanyOverview", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "公司概况", "en": "Company Overview", "ja": "会社概要"}, "Descriptions": {"zh": "展示公司基本信息、统计数据和经营理念的组件", "en": "Component to showcase company basic information, statistics and philosophy", "ja": "会社の基本情報、統計データ、経営理念を紹介するコンポーネント"}, "formFields": [{"name": "Description", "type": "multilingual-textarea", "label": "@AdminResource:CompanyDescription", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline"}, "validation": {"required": false, "maxLength": 500}}, {"name": "Stats", "type": "repeater", "label": "@FormResource:CompanyOverview_Stats", "display": {"collapsed": true, "group": "@FormResource:FormGroups_Statistics", "width": "col-span-12", "order": 8}, "template": {"fields": [{"name": "Icon", "type": "select", "label": "@FormResource:StatItem_Icon", "display": {"width": "col-span-12"}, "options": [{"value": "building", "label": "building"}, {"value": "users", "label": "users"}, {"value": "globe", "label": "globe"}, {"value": "award", "label": "award"}, {"value": "calendar", "label": "calendar"}, {"value": "chart", "label": "chart"}, {"value": "image", "label": "image"}, {"value": "bolt", "label": "bolt"}, {"value": "droplet", "label": "droplet"}, {"value": "link", "label": "link"}, {"value": "heart", "label": "heart"}, {"value": "thumbs-up", "label": "thumbs-up"}, {"value": "house", "label": "house"}]}, {"name": "Label", "type": "multilingual-text", "label": "@FormResource:StatItem_Label", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": true, "maxLength": 50}}, {"name": "Value", "type": "multilingual-text", "label": "@FormResource:StatItem_Value", "display": {"width": "col-span-12", "layout": "inline"}, "validation": {"required": true, "maxLength": 50}}]}, "validation": {"required": false, "minItems": 1, "maxItems": 6}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "multilingual-textarea", "label": "@AdminResource:Philosophy", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 10, "layout": "inline"}, "validation": {"required": false, "maxLength": 1000}}, {"name": "PhilosophyButtonUrl", "type": "text", "collapsed": true, "label": "@FormResource:FormFields_Link", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 12}, "validation": {"required": false, "maxLength": 200}}, {"name": "CompanyImage", "type": "image", "collapsed": true, "label": "@AdminResource:Image", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 13}, "fileConfig": {"folder": "company", "types": ["image/*"], "maxSize": "5MB", "multiple": false, "preview": true}, "validation": {"required": false}}, {"name": "CompanyImageAlt", "type": "multilingual-text", "label": "@FormResource:FormFields_AltText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 14, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}]}