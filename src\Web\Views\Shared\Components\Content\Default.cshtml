@model MlSoft.Sites.Web.ViewModels.Components.ContentComponentViewModel
@using MlSoft.Sites.Web.Helpers

@{
	// 生成CSS类
	var animationClass = Model.AnimationEnabled ? "transition-all duration-700 ease-in-out" : "";
	var uniqueId = JObjectHelper.GenerateId("content");
}

<section class="content-component py-16 md:py-24 @animationClass" id="@uniqueId">
	<div class="max-w-7xl mx-auto p-4">
		@if (!string.IsNullOrEmpty(Model.Title) || !string.IsNullOrEmpty(Model.Subtitle))
		{
			<div class="content-header text-center mb-12 @(Model.AnimationEnabled ? "animate-fade-in-up" : "")">
				@if (!string.IsNullOrEmpty(Model.Title))
				{
					<h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4 @(Model.AnimationEnabled ? "transition-all duration-700 delay-200" : "")">@Model.Title</h2>
				}
				@if (!string.IsNullOrEmpty(Model.Subtitle))
				{
					<h3 class="text-xl text-gray-600 dark:text-gray-300 @(Model.AnimationEnabled ? "transition-all duration-700 delay-300" : "")">@Model.Subtitle</h3>
				}
			</div>
		}

		@if (!string.IsNullOrEmpty(Model.Content))
		{
			<div class="content-body prose prose-lg max-w-none mb-12 @(Model.AnimationEnabled ? "animate-fade-in-up transition-all duration-700 delay-400" : "")">
				@Html.Raw(Model.Content)
			</div>
		}

		@if (Model.ContentBlocks.Any())
		{
			<div class="content-blocks grid gap-8 @(Model.Layout == "list" ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3") @(Model.AnimationEnabled ? "animate-fade-in-up transition-all duration-700 delay-500" : "")">
				@foreach (var block in Model.ContentBlocks)
				{
					<div class="content-block bg-white dark:bg-gray-800 rounded-lg shadow-lg hover:shadow-xl p-6 @(Model.AnimationEnabled ? "transition-all duration-300 hover:transform hover:scale-105" : "")">
						@if (!string.IsNullOrEmpty(block.Image))
						{
							<div class="block-image mb-4">
								<img src="@JObjectHelper.ProcessFilePath(block.Image)" alt="@block.Title" class="w-full h-48 object-cover rounded-lg @(Model.AnimationEnabled ? "transition-transform duration-300" : "")" />
							</div>
						}

						<div class="block-content">
							@if (!string.IsNullOrEmpty(block.Title))
							{
								<h4 class="block-title text-xl font-semibold text-gray-900 dark:text-white mb-3 @(Model.AnimationEnabled ? "transition-colors duration-300" : "")">@block.Title</h4>
							}

							@if (!string.IsNullOrEmpty(block.Content))
							{
								<div class="block-text text-gray-600 dark:text-gray-300 mb-4 @(Model.AnimationEnabled ? "transition-colors duration-300" : "")">
									@Html.Raw(block.Content)
								</div>
							}

							@if (!string.IsNullOrEmpty(block.Link))
							{
								<a href="@block.Link" class="block-link inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium @(Model.AnimationEnabled ? "transition-colors duration-300" : "")">
									@(!string.IsNullOrEmpty(block.LinkText) ? block.LinkText : "")
									<svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
									</svg>
								</a>
							}
						</div>
					</div>
				}
			</div>
		}

		@if (Model.ShowDivider)
		{
			<hr class="content-divider border-gray-200 dark:border-gray-700 my-12 @(Model.AnimationEnabled ? "transition-colors duration-300" : "")" />
		}
	</div>
</section>

@* 自定义动画CSS *@
@if (Model.AnimationEnabled)
{
	<style>
		@@keyframes fadeInUp {
			from {
				opacity: 0;
				transform: translateY(30px);
			}

			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		.animate-fade-in-up {
			animation: fadeInUp 1s ease-out forwards;
		}
	</style>
}