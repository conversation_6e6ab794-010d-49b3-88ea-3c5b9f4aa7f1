{"ComponentId": "Content", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "内容组件", "en": "Content", "ja": "コンテンツ"}, "Descriptions": {"zh": "标准内容区域布局，支持多语言富文本内容", "en": "Standard content area layout with multilingual rich text support", "ja": "多言語リッチテキストサポート付きの標準コンテンツエリアレイアウト"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@SharedResource:FormFields_TitleHelpText"}, "validation": {"maxLength": 200}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@SharedResource:FormFields_Subtitle", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "collapsed": true, "layout": "inline", "helpText": "@SharedResource:FormFields_SubtitleHelpText"}, "validation": {"maxLength": 300}}, {"name": "Content", "type": "multilingual-richtext", "label": "@FormResource:FormFields_Content", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 3, "collapsed": true, "helpText": "@FormResource:FormFields_ContentHelpText"}, "validation": {"required": true}, "editorConfig": {"toolbar": "undo redo | blocks | bold italic underline strikethrough | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image media table | code codesample | removeformat | fullscreen", "height": 400, "plugins": ["lists", "link", "image", "media", "table", "code", "codesample", "fullscreen", "wordcount"], "menubar": false, "branding": false}}, {"name": "ContentBlocks", "type": "array", "label": "@FormResource:FormFields_ContentBlocks", "display": {"group": "@FormResource:FormGroups_ContentBlocks", "width": "col-span-12", "order": 1, "collapsed": true, "helpText": "@FormResource:FormFields_ContentBlocksHelpText"}, "minItems": 0, "maxItems": 6, "template": {"fields": [{"name": "Title", "type": "multilingual-text", "label": "@FormResource:FormFields_BlockTitle", "display": {"layout": "inline"}, "validation": {"required": true, "maxLength": 100}}, {"name": "Content", "type": "multilingual-richtext", "label": "@FormResource:FormFields_BlockContent", "editorConfig": {"toolbar": "bold italic underline | bullist numlist | link", "height": 200, "plugins": ["lists", "link"]}}, {"name": "Image", "type": "image", "label": "@FormResource:FormFields_Image", "fileConfig": {"folder": "content/blocks", "types": ["image/*"], "maxSize": "2MB", "preview": true}}, {"name": "Link", "type": "url", "label": "@FormResource:FormFields_Link"}, {"name": "LinkText", "type": "multilingual-text", "label": "@FormResource:FormFields_LinkText", "display": {"layout": "inline"}, "validation": {"maxLength": 50}}]}}, {"name": "Layout", "type": "select", "label": "@FormResource:FormFields_Layout", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 1, "helpText": "@FormResource:FormFields_LayoutHelpText"}, "options": [{"value": "grid", "label": "@FormResource:FormFields_GridLayout"}, {"value": "list", "label": "@FormResource:FormFields_ListLayout"}], "defaultValue": "grid"}, {"name": "AnimationEnabled", "type": "checkbox", "label": "@FormResource:FormFields_AnimationEnabled", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 2, "layout": "inline", "helpText": "@FormResource:FormFields_AnimationEnabledHelpText"}, "defaultValue": true}, {"name": "ShowDivider", "type": "checkbox", "label": "@FormResource:FormFields_ShowDivider", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 3, "layout": "inline", "helpText": "@FormResource:FormFields_ShowDividerHelpText"}, "defaultValue": false}]}