@model MlSoft.Sites.Web.ViewModels.Components.CookiePolicyComponentViewModel
@using MlSoft.Sites.Web.Helpers
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var title = string.IsNullOrEmpty(Model?.Title) ? SharedRes["CookiePolicy_Title"] : Model?.Title;
    var message = string.IsNullOrEmpty(Model?.Message) ? SharedRes["CookiePolicy_Message"] : Model?.Message;
    var acceptButtonText = string.IsNullOrEmpty(Model?.AcceptButtonText) ? SharedRes["CookiePolicy_Accept"] : Model?.AcceptButtonText;
    var declineButtonText = string.IsNullOrEmpty(Model?.DeclineButtonText) ? SharedRes["CookiePolicy_Decline"] : Model?.DeclineButtonText;
    var learnMoreText = string.IsNullOrEmpty(Model?.LearnMoreText) ? SharedRes["CookiePolicy_LearnMore"] : Model?.LearnMoreText;
    var learnMoreUrl = string.IsNullOrEmpty(Model?.LearnMoreUrl) ? "/privacy-policy" : Model?.LearnMoreUrl;
    
    // Display settings
    var position = Model?.Position ?? "bottom";
    var showDeclineButton = Model?.ShowDeclineButton ?? false;
    var showLearnMoreLink = Model?.ShowLearnMoreLink ?? true;
    var backgroundColor = Model?.BackgroundColor ?? "dark";
    var autoHide = Model?.AutoHide ?? false;
    var autoHideDelay = Model?.AutoHideDelay ?? 5000;
    
    // Generate CSS classes based on properties
    var positionClass = position == "top" ? "top-0" : "bottom-0";
    var backgroundClass = backgroundColor == "light" 
        ? "bg-white dark:bg-gray-100 text-gray-900 dark:text-gray-800 border-gray-200 dark:border-gray-300" 
        : "bg-gray-900 dark:bg-gray-800 text-white dark:text-gray-100 border-gray-700 dark:border-gray-600";
    
    // Create unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("cookie-policy");
}

<!-- Cookie Policy Banner -->
<div id="@uniqueId" class="fixed @positionClass left-0 right-0 z-50 @backgroundClass border-t border-b shadow-lg transform translate-y-full opacity-0 transition-all duration-300 ease-in-out" style="display: none;">
    <div class="max-w-7xl mx-auto px-4 py-4">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <!-- Content -->
            <div class="flex-1">
                @if (!string.IsNullOrEmpty(title))
                {
                    <h3 class="text-lg font-semibold mb-2">@title</h3>
                }
                <p class="text-sm opacity-90 mb-3 sm:mb-0">
                    @message
                    @if (showLearnMoreLink && !string.IsNullOrEmpty(learnMoreUrl))
                    {
                        <a href="@learnMoreUrl" class="underline hover:no-underline text-primary-400 hover:text-primary-300 ml-1">@learnMoreText</a>
                    }
                </p>
            </div>
            
            <!-- Buttons -->
            <div class="flex flex-col sm:flex-row gap-2 min-w-fit">
                @if (showDeclineButton)
                {
                    <button type="button" onclick="handleCookieDecline('@uniqueId')" 
                            class="px-4 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        @declineButtonText
                    </button>
                }
                <button type="button" onclick="handleCookieAccept('@uniqueId')" 
                        class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-300 rounded-lg dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 transition-colors duration-200">
                    @acceptButtonText
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const cookieBanner = document.getElementById('@uniqueId');
    const cookieAccepted = localStorage.getItem('cookiePolicy');
    
    // Show banner if cookie policy not accepted
    if (!cookieAccepted && cookieBanner) {
        setTimeout(() => {
            cookieBanner.style.display = 'block';
            setTimeout(() => {
                cookieBanner.classList.remove('translate-y-full', 'opacity-0');
                cookieBanner.classList.add('translate-y-0', 'opacity-100');
            }, 100);
        }, 1000); // Delay showing banner by 1 second
        
        // Auto-hide if enabled
        @if (autoHide)
        {
            <text>
            setTimeout(() => {
                handleCookieAccept('@uniqueId');
            }, @autoHideDelay);
            </text>
        }
    }
});

// Handle cookie acceptance
window.handleCookieAccept = function(bannerId) {
    localStorage.setItem('cookiePolicy', 'accepted');
    localStorage.setItem('cookiePolicyDate', new Date().toISOString());
    hideCookieBanner(bannerId);
    
    // Optional: Trigger analytics or other tracking
    if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
            'analytics_storage': 'granted'
        });
    }
};

// Handle cookie decline
window.handleCookieDecline = function(bannerId) {
    localStorage.setItem('cookiePolicy', 'declined');
    localStorage.setItem('cookiePolicyDate', new Date().toISOString());
    hideCookieBanner(bannerId);
    
    // Optional: Disable analytics or other tracking
    if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
            'analytics_storage': 'denied'
        });
    }
};

// Hide cookie banner with animation
function hideCookieBanner(bannerId) {
    const banner = document.getElementById(bannerId);
    if (banner) {
        banner.classList.add('translate-y-full', 'opacity-0');
        banner.classList.remove('translate-y-0', 'opacity-100');
        setTimeout(() => {
            banner.style.display = 'none';
        }, 300);
    }
}
</script>