# Cookie Policy Component

A simple, non-intrusive cookie policy banner component that appears at the bottom of the website for first-time visitors.

## Features

- **Simple & Clean**: Minimal design that doesn't interfere with user experience
- **Multilingual Support**: Supports Chinese, English, and Japanese
- **Customizable**: Configurable position, colors, and behavior
- **GDPR Compliant**: Optional decline button for compliance
- **Auto-hide Option**: Can automatically accept after specified time
- **Local Storage**: Remembers user's choice to avoid repeated prompts
- **Theme Support**: Automatically adapts to light/dark mode
- **Responsive**: Works on all device sizes

## Usage

### In Layout File (_Layout.cshtml)

Add the component at the end of your layout file, just before the closing `</body>` tag:

```html
<!-- Cookie Policy Banner -->
@await Component.InvokeAsync("CookiePolicy", new { variant = "Default" })
```

### With Custom Configuration

```html
@{
    var cookieConfig = new {
        Title = "Cookie Notice",
        Message = "We use cookies to enhance your experience.",
        Position = "bottom",
        ShowDeclineButton = true,
        BackgroundColor = "dark"
    };
}

@await Component.InvokeAsync("CookiePolicy", new { model = cookieConfig, variant = "Default" })
```

## Configuration Options

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `Title` | string | "Cookie Policy" | Banner title (optional) |
| `Message` | string | Default message | Cookie policy explanation text |
| `AcceptButtonText` | string | "Accept" | Accept button text |
| `DeclineButtonText` | string | "Decline" | Decline button text |
| `LearnMoreText` | string | "Learn More" | Learn more link text |
| `LearnMoreUrl` | string | "/privacy-policy" | URL for detailed policy |
| `Position` | string | "bottom" | Banner position ("bottom" or "top") |
| `ShowDeclineButton` | bool | false | Show decline option |
| `ShowLearnMoreLink` | bool | true | Show learn more link |
| `BackgroundColor` | string | "dark" | Theme ("dark" or "light") |
| `AutoHide` | bool | false | Auto-accept after delay |
| `AutoHideDelay` | int | 5000 | Auto-hide delay in milliseconds |

## Behavior

1. **First Visit**: Banner appears 1 second after page load
2. **User Choice**: Stores decision in localStorage
3. **Subsequent Visits**: Banner doesn't appear if choice was made
4. **Analytics Integration**: Automatically updates Google Analytics consent if available

## Styling

The component uses:
- **Flowbite components** for consistent UI
- **Theme variables** (primary-600, primary-700) for colors
- **Dark mode support** with automatic switching
- **Responsive design** with mobile-friendly layout

## JavaScript Functions

The component provides global JavaScript functions:

- `handleCookieAccept(bannerId)`: Accept cookies and hide banner
- `handleCookieDecline(bannerId)`: Decline cookies and hide banner
- `hideCookieBanner(bannerId)`: Hide banner with animation

## Browser Support

- Modern browsers with localStorage support
- Graceful degradation for older browsers
- No external dependencies required

## Compliance

- **GDPR Ready**: Optional decline button
- **CCPA Compatible**: Clear opt-out mechanism
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Performance**: Minimal impact on page load time