{"ComponentId": "<PERSON>ie<PERSON><PERSON><PERSON>", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "Cookie政策横幅", "en": "<PERSON>ie <PERSON>", "ja": "Cookieポリシーバナー"}, "Descriptions": {"zh": "网站底部Cookie政策提示横幅", "en": "Cookie policy notification banner at the bottom of the website", "ja": "ウェブサイト下部のCookieポリシー通知バナー"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_CookiePolicyTitleHelpText"}, "validation": {"maxLength": 100}}, {"name": "Message", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Content", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_CookiePolicyMessageHelpText"}, "validation": {"required": true, "maxLength": 500}}, {"name": "AcceptButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_AcceptButtonText", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 3, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_AcceptButtonTextHelpText"}, "validation": {"maxLength": 50}}, {"name": "DeclineButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_DeclineButtonText", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 4, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_DeclineButtonTextHelpText"}, "validation": {"maxLength": 50}}, {"name": "LearnMoreText", "type": "multilingual-text", "label": "@FormResource:FormFields_LearnMoreText", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 5, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_LearnMoreTextHelpText"}, "validation": {"maxLength": 50}}, {"name": "LearnMoreUrl", "type": "text", "label": "@FormResource:FormFields_LearnMoreUrl", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "collapsed": true, "order": 6, "helpText": "@FormResource:FormFields_LearnMoreUrlHelpText"}, "validation": {"maxLength": 200}}, {"name": "Position", "type": "select", "label": "@FormResource:FormFields_Position", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 1, "helpText": "@FormResource:FormFields_PositionHelpText"}, "options": [{"value": "bottom", "label": "@FormResource:FormFields_PositionBottom"}, {"value": "top", "label": "@FormResource:FormFields_PositionTop"}], "defaultValue": "bottom"}, {"name": "BackgroundColor", "type": "select", "label": "@FormResource:FormFields_BackgroundColor", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 2, "helpText": "@FormResource:FormFields_BackgroundColorHelpText"}, "options": [{"value": "dark", "label": "@FormResource:FormFields_BackgroundColorDark"}, {"value": "light", "label": "@FormResource:FormFields_BackgroundColorLight"}], "defaultValue": "dark"}, {"name": "ShowDeclineButton", "type": "checkbox", "label": "@FormResource:FormFields_ShowDeclineButton", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 3, "helpText": "@FormResource:FormFields_ShowDeclineButtonHelpText"}, "defaultValue": false}, {"name": "ShowLearnMoreLink", "type": "checkbox", "label": "@FormResource:FormFields_ShowLearnMoreLink", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 4, "helpText": "@FormResource:FormFields_ShowLearnMoreLinkHelpText"}, "defaultValue": true}, {"name": "AutoHide", "type": "checkbox", "label": "@FormResource:FormFields_AutoHide", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "layout": "inline", "collapsed": true, "order": 1, "helpText": "@FormResource:FormFields_AutoHideHelpText"}, "defaultValue": false}, {"name": "AutoHideDelay", "type": "number", "label": "@FormResource:FormFields_AutoHideDelay", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 2, "collapsed": true, "layout": "inline", "helpText": "@FormResource:FormFields_AutoHideDelayHelpText"}, "validation": {"min": 1000, "max": 30000}, "defaultValue": 5000}]}