@model MlSoft.Sites.Web.ViewModels.Components.CustomerCaseShowcaseComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract data from ViewModel with null-safe defaults
    var customerCases = Model?.CustomerCases ?? new List<CustomerCase>();
    var title = string.IsNullOrEmpty(Model?.TitleText) ? SharedRes["CustomerCases_Title"] : Model?.TitleText;
    var description = Model?.Description;

    var columnsDesktop = Math.Min(Model?.ColumnsDesktop ?? 4, customerCases.Count);
    var columnsTablet = Math.Min(Model?.ColumnsTablet ?? 2, customerCases.Count);
    var columnsMobile = Math.Min(Model?.ColumnsMobile ?? 1, customerCases.Count);

    var showCompanyLogos = Model?.ShowCompanyLogos ?? true;
    var backgroundStyle = Model?.BackgroundStyle ?? "light";

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("customer-cases-cards");

    // Generate grid classes
    var gridClass = $"grid gap-6 md:grid-cols-{columnsTablet} lg:grid-cols-{columnsDesktop}";

    // Background classes
    var backgroundClass = backgroundStyle switch
    {
        "dark" => "bg-gray-900 dark:bg-gray-950",
        "gradient" => "bg-gradient-to-br from-primary-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",
        _ => "bg-gray-50 dark:bg-gray-900"
    };

    var textColorClass = backgroundStyle == "dark" ? "text-white" : "text-gray-900 dark:text-white";
    var subtextColorClass = backgroundStyle == "dark" ? "text-gray-300" : "text-gray-600 dark:text-gray-300";

    // File path processing helper
    string ProcessFilePath(string filePath) =>
        string.IsNullOrEmpty(filePath) ? "" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-16 lg:py-20 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        <!-- Header Section -->
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold @textColorClass mb-4">
                @title
            </h2>

            @if (!string.IsNullOrEmpty(description))
            {
                <p class="text-lg @subtextColorClass max-w-2xl mx-auto leading-relaxed">
                    @description
                </p>
            }
        </div>

        <!-- Simple Cards Grid -->
        @if (customerCases.Count > 0)
        {
            <div class="@gridClass">
                @foreach (var customerCase in customerCases)
                {
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 text-center group">
                        @if (showCompanyLogos && !string.IsNullOrEmpty(customerCase.CompanyLogo))
                        {
                            <div class="mb-4">
                                <img src="@ProcessFilePath(customerCase.CompanyLogo)"
                                     alt="@customerCase.CompanyName"
                                     class="w-16 h-16 object-contain mx-auto rounded grayscale group-hover:grayscale-0 transition-all duration-300"
                                     loading="lazy" />
                            </div>
                        }

                        <h3 class="font-semibold text-lg text-gray-900 dark:text-white mb-2">
                            @customerCase.CompanyName
                        </h3>

                        @if (!string.IsNullOrEmpty(customerCase.Industry))
                        {
                            <span class="inline-block px-3 py-1 text-sm font-medium rounded-full bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 mb-3">
                                @customerCase.Industry
                            </span>
                        }

                        @if (!string.IsNullOrEmpty(customerCase.ProjectDescription))
                        {
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                                @customerCase.ProjectDescription
                            </p>
                        }

                        @if (!string.IsNullOrEmpty(customerCase.TestimonialText))
                        {
                            <blockquote class="text-gray-500 dark:text-gray-400 italic text-sm mb-4 line-clamp-3">
                                "@customerCase.TestimonialText"
                            </blockquote>
                        }

                        <!-- Single Key Metric -->
                        @if (customerCase.Metrics?.Count > 0)
                        {
                            var keyMetric = customerCase.Metrics.First();
                            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                                <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                                    @keyMetric.Value@(string.IsNullOrEmpty(keyMetric.Unit) ? "" : keyMetric.Unit)
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    @keyMetric.Label
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(customerCase.CaseStudyUrl))
                        {
                            <div class="mt-4">
                                <a href="@customerCase.CaseStudyUrl"
                                   class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm transition-colors">
                                    @SharedRes["LearnMore"] →
                                </a>
                            </div>
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-12">
                <i class="fas fa-building text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    @SharedRes["NoCasesAvailable"]
                </h3>
                <p class="text-gray-500 dark:text-gray-400">
                    @SharedRes["NoCasesDescription"]
                </p>
            </div>
        }
    </div>
</section>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>