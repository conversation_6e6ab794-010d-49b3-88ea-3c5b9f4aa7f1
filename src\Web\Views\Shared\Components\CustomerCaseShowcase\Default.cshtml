@model MlSoft.Sites.Web.ViewModels.Components.CustomerCaseShowcaseComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract data from ViewModel with null-safe defaults
    var customerCases = Model?.CustomerCases ?? new List<CustomerCase>();
    var title = string.IsNullOrEmpty(Model?.TitleText) ? SharedRes["CustomerCases_Title"] : Model?.TitleText;
    var subtitle = string.IsNullOrEmpty(Model?.SubtitleText) ? SharedRes["CustomerCases_Subtitle"] : Model?.SubtitleText;
    var description = Model?.Description;

    var columnsDesktop = Math.Min(Model?.ColumnsDesktop ?? 3, customerCases.Count);
    var columnsTablet = Math.Min(Model?.ColumnsTablet ?? 2, customerCases.Count);
    var columnsMobile = Math.Min(Model?.ColumnsMobile ?? 1, customerCases.Count);

    var showIndustryFilter = Model?.ShowIndustryFilter ?? true;
    var showTestimonials = Model?.ShowTestimonials ?? true;
    var showCompanyLogos = Model?.ShowCompanyLogos ?? true;
    var backgroundStyle = Model?.BackgroundStyle ?? "light";
    var enableCarousel = Model?.EnableCarousel ?? false;

    var showCtaButton = Model?.ShowCtaButton ?? false;
    var ctaButtonText = Model?.CtaButtonText ?? SharedRes["ViewAllCases"];
    var ctaButtonUrl = Model?.CtaButtonUrl ?? "#";

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("customer-cases");

    // Generate grid classes
    var gridClass = $"grid gap-8 md:grid-cols-{columnsTablet} lg:grid-cols-{columnsDesktop}";

    // Background classes
    var backgroundClass = backgroundStyle switch
    {
        "dark" => "bg-gray-900 dark:bg-gray-950",
        "gradient" => "bg-gradient-to-br from-primary-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",
        _ => "bg-white dark:bg-gray-800"
    };

    var textColorClass = backgroundStyle == "dark" ? "text-white" : "text-gray-900 dark:text-white";
    var subtextColorClass = backgroundStyle == "dark" ? "text-gray-300" : "text-gray-600 dark:text-gray-300";

    // Get unique industries for filter
    var industries = customerCases.Where(c => !string.IsNullOrEmpty(c.Industry))
                                 .Select(c => c.Industry)
                                 .Distinct()
                                 .OrderBy(i => i)
                                 .ToList();

    // File path processing helper
    string ProcessFilePath(string filePath) =>
        string.IsNullOrEmpty(filePath) ? "" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-16 lg:py-24 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        <!-- Header Section -->
        <div class="text-center mb-12">
            @if (!string.IsNullOrEmpty(subtitle))
            {
                <p class="text-sm font-semibold uppercase tracking-wider @subtextColorClass mb-2">
                    @subtitle
                </p>
            }

            <h2 class="text-3xl lg:text-4xl font-bold @textColorClass mb-4">
                @title
            </h2>

            @if (!string.IsNullOrEmpty(description))
            {
                <p class="text-lg @subtextColorClass max-w-3xl mx-auto leading-relaxed">
                    @description
                </p>
            }
        </div>

        <!-- Industry Filter -->
        @if (showIndustryFilter && industries.Count > 1)
        {
            <div class="flex flex-wrap justify-center gap-2 mb-12" role="tablist" aria-label="@SharedRes["IndustryFilter"]">
                <button class="industry-filter-btn active px-6 py-2 text-sm font-medium rounded-full transition-colors bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                        data-industry="all"
                        role="tab"
                        aria-selected="true">
                    @SharedRes["AllIndustries"]
                </button>
                @foreach (var industry in industries)
                {
                    <button class="industry-filter-btn px-6 py-2 text-sm font-medium rounded-full transition-colors bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                            data-industry="@industry"
                            role="tab"
                            aria-selected="false">
                        @industry
                    </button>
                }
            </div>
        }

        <!-- Cases Grid -->
        @if (customerCases.Count > 0)
        {
            <div class="@gridClass" id="cases-container">
                @foreach (var customerCase in customerCases)
                {
                    <article class="case-item bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
                             data-industry="@customerCase.Industry"
                             @(customerCase.IsFeatured ? "data-featured=\"true\"" : "")>

                        <!-- Featured Image -->
                        @if (!string.IsNullOrEmpty(customerCase.FeaturedImage))
                        {
                            <div class="aspect-video overflow-hidden">
                                <img src="@ProcessFilePath(customerCase.FeaturedImage)"
                                     alt="@customerCase.ProjectTitle"
                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                     loading="lazy" />
                            </div>
                        }

                        <div class="p-6">
                            <!-- Company Info -->
                            <div class="flex items-center mb-4">
                                @if (showCompanyLogos && !string.IsNullOrEmpty(customerCase.CompanyLogo))
                                {
                                    <img src="@ProcessFilePath(customerCase.CompanyLogo)"
                                         alt="@customerCase.CompanyName"
                                         class="w-12 h-12 object-contain mr-3 rounded"
                                         loading="lazy" />
                                }
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">
                                        @customerCase.CompanyName
                                    </h3>
                                    @if (!string.IsNullOrEmpty(customerCase.Industry))
                                    {
                                        <span class="text-sm text-primary-600 dark:text-primary-400 font-medium">
                                            @customerCase.Industry
                                        </span>
                                    }
                                </div>
                            </div>

                            <!-- Project Title -->
                            @if (!string.IsNullOrEmpty(customerCase.ProjectTitle))
                            {
                                <h4 class="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                    @customerCase.ProjectTitle
                                </h4>
                            }

                            <!-- Project Description -->
                            @if (!string.IsNullOrEmpty(customerCase.ProjectDescription))
                            {
                                <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                                    @customerCase.ProjectDescription
                                </p>
                            }

                            <!-- Metrics -->
                            @if (customerCase.Metrics?.Count > 0)
                            {
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    @foreach (var metric in customerCase.Metrics.Take(4))
                                    {
                                        <div class="text-center">
                                            @if (!string.IsNullOrEmpty(metric.Icon))
                                            {
                                                <i class="fas <EMAIL> text-primary-600 dark:text-primary-400 mb-1"></i>
                                            }
                                            <div class="font-bold text-gray-900 dark:text-white">
                                                @metric.Value@(string.IsNullOrEmpty(metric.Unit) ? "" : metric.Unit)
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                @metric.Label
                                            </div>
                                        </div>
                                    }
                                </div>
                            }

                            <!-- Testimonial -->
                            @if (showTestimonials && !string.IsNullOrEmpty(customerCase.TestimonialText))
                            {
                                <blockquote class="border-l-4 border-primary-600 pl-4 mb-4">
                                    <p class="text-gray-600 dark:text-gray-300 italic text-sm mb-2">
                                        "@customerCase.TestimonialText"
                                    </p>
                                    @if (!string.IsNullOrEmpty(customerCase.TestimonialAuthor))
                                    {
                                        <footer class="text-xs text-gray-500 dark:text-gray-400">
                                            — @customerCase.TestimonialAuthor
                                            @if (!string.IsNullOrEmpty(customerCase.TestimonialPosition))
                                            {
                                                <span>, @customerCase.TestimonialPosition</span>
                                            }
                                        </footer>
                                    }
                                </blockquote>
                            }

                            <!-- Technologies -->
                            @if (customerCase.Technologies?.Count > 0)
                            {
                                <div class="flex flex-wrap gap-2 mb-4">
                                    @foreach (var tech in customerCase.Technologies.Take(4))
                                    {
                                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                            @tech
                                        </span>
                                    }
                                </div>
                            }

                            <!-- CTA Link -->
                            @if (!string.IsNullOrEmpty(customerCase.CaseStudyUrl))
                            {
                                <a href="@customerCase.CaseStudyUrl"
                                   class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm transition-colors">
                                    @SharedRes["ViewCaseStudy"]
                                    <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            }
                        </div>
                    </article>
                }
            </div>

            <!-- Load More/View All Button -->
            @if (showCtaButton)
            {
                <div class="text-center mt-12">
                    <a href="@ctaButtonUrl"
                       class="inline-flex items-center px-8 py-3 text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors">
                        @ctaButtonText
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            }
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-12">
                <i class="fas fa-briefcase text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    @SharedRes["NoCasesAvailable"]
                </h3>
                <p class="text-gray-500 dark:text-gray-400">
                    @SharedRes["NoCasesDescription"]
                </p>
            </div>
        }
    </div>
</section>

@if (showIndustryFilter && industries.Count > 1)
{
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Industry filter functionality
        const filterButtons = document.querySelectorAll('.industry-filter-btn');
        const caseItems = document.querySelectorAll('.case-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const industry = this.dataset.industry;

                // Update button states
                filterButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-primary-600', 'text-white', 'hover:bg-primary-700');
                    btn.classList.add('bg-gray-100', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300', 'hover:bg-gray-200', 'dark:hover:bg-gray-600');
                    btn.setAttribute('aria-selected', 'false');
                });

                this.classList.add('active', 'bg-primary-600', 'text-white', 'hover:bg-primary-700');
                this.classList.remove('bg-gray-100', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300', 'hover:bg-gray-200', 'dark:hover:bg-gray-600');
                this.setAttribute('aria-selected', 'true');

                // Filter case items
                caseItems.forEach(item => {
                    if (industry === 'all' || item.dataset.industry === industry) {
                        item.style.display = 'block';
                        item.classList.remove('hidden');
                    } else {
                        item.style.display = 'none';
                        item.classList.add('hidden');
                    }
                });
            });
        });
    });
    </script>
}

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>