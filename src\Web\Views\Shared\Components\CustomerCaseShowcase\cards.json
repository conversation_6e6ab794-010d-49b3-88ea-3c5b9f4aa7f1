{"ComponentId": "CustomerCaseShowcase", "Id": "Cards", "Names": {"zh": "客户案例卡片", "en": "Customer Case Cards", "ja": "顧客事例カード"}, "Descriptions": {"zh": "简洁的客户案例卡片展示，突出关键信息和数据指标", "en": "Clean customer case cards display highlighting key information and metrics", "ja": "主要情報と指標を強調した、すっきりとした顧客事例カード表示"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 500}}, {"name": "ColumnsDesktop", "type": "select", "label": "@FormResource:FormFields_ColumnsDesktop", "display": {"group": "@FormResource:FormGroups_Layout", "width": "col-span-4", "order": 10, "layout": "inline", "collapsed": true}, "options": [{"value": "3", "label": "3"}, {"value": "4", "label": "4"}, {"value": "5", "label": "5"}], "validation": {"required": false}}, {"name": "ColumnsTablet", "type": "select", "label": "@FormResource:FormFields_ColumnsTablet", "display": {"group": "@FormResource:FormGroups_Layout", "width": "col-span-4", "order": 11, "layout": "inline", "collapsed": true}, "options": [{"value": "2", "label": "2"}, {"value": "3", "label": "3"}], "validation": {"required": false}}, {"name": "ColumnsMobile", "type": "select", "label": "@FormResource:FormFields_ColumnsMobile", "display": {"group": "@FormResource:FormGroups_Layout", "width": "col-span-4", "order": 12, "layout": "inline", "collapsed": true}, "options": [{"value": "1", "label": "1"}], "validation": {"required": false}}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:CompanyBasicInfo_BackgroundStyle", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 20, "layout": "inline", "collapsed": true}, "options": [{"value": "light", "label": "@FormResource:BreadcrumbBackgroundStyleLight"}, {"value": "dark", "label": "@FormResource:BreadcrumbBackgroundStyleDark"}, {"value": "gradient", "label": "@FormResource:<PERSON>_<PERSON>"}], "validation": {"required": false}}, {"name": "ShowCompanyLogos", "type": "checkbox", "label": "@FormResource:CustomerCases_ShowCompanyLogos", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 21, "layout": "inline", "collapsed": true}}, {"name": "CustomerCases", "type": "repeater", "label": "@FormResource:CustomerCases", "display": {"group": "@FormResource:FormGroups_Content", "width": "col-span-12", "collapsed": true, "order": 30}, "template": {"fields": [{"name": "CompanyName", "type": "multilingual-text", "label": "@FormResource:CompanyName", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": true, "maxLength": 100}}, {"name": "Industry", "type": "multilingual-text", "label": "@FormResource:Industry", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 50}}, {"name": "CompanyLogo", "type": "image", "label": "@FormResource:CompanyLogo", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "fileConfig": {"folder": "customer-cases", "types": ["image/*"], "maxSize": "2MB", "multiple": false, "preview": true}}, {"name": "ProjectDescription", "type": "multilingual-textarea", "label": "@FormResource:ProjectDescription", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": true, "maxLength": 300}}, {"name": "TestimonialText", "type": "multilingual-textarea", "label": "@FormResource:TestimonialText", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 200}}, {"name": "CaseStudyUrl", "type": "text", "label": "@FormResource:CaseStudyUrl", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 200}}]}, "validation": {"required": false, "minItems": 1, "maxItems": 20}}]}