{"ComponentId": "CustomerCaseShowcase", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "客户案例展示", "en": "Customer Case Showcase", "ja": "顧客事例紹介"}, "Descriptions": {"zh": "展示企业成功案例和客户证言的组件，支持行业筛选和详细信息展示", "en": "Component to showcase company success stories and customer testimonials with industry filtering and detailed information display", "ja": "業界フィルタリングと詳細情報表示による企業の成功事例と顧客の声を紹介するコンポーネント"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 100}}, {"name": "SubtitleText", "type": "multilingual-text", "label": "@FormResource:FormFields_Subtitle", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 3, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 500}}, {"name": "ColumnsDesktop", "type": "select", "label": "@FormResource:FormFields_ColumnsDesktop", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 10}, "options": [{"value": "2", "label": "2"}, {"value": "3", "label": "3"}, {"value": "4", "label": "4"}], "validation": {"required": false}}, {"name": "ColumnsTablet", "type": "select", "label": "@FormResource:FormFields_ColumnsTablet", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 11}, "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}, {"value": "3", "label": "3"}], "validation": {"required": false}}, {"name": "ColumnsMobile", "type": "select", "label": "@FormResource:FormFields_ColumnsMobile", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 12}, "options": [{"value": "1", "label": "1"}], "validation": {"required": false}}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:CompanyBasicInfo_BackgroundStyle", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 20}, "options": [{"value": "light", "label": "@FormResource:BreadcrumbBackgroundStyleLight"}, {"value": "dark", "label": "@FormResource:BreadcrumbBackgroundStyleDark"}, {"value": "gradient", "label": "@FormResource:<PERSON>_<PERSON>"}], "validation": {"required": false}}, {"name": "ShowIndustryFilter", "type": "checkbox", "label": "@FormResource:CustomerCases_ShowIndustryFilter", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 21}}, {"name": "ShowTestimonials", "type": "checkbox", "label": "@FormResource:CustomerCases_ShowTestimonials", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 22}}, {"name": "ShowCompanyLogos", "type": "checkbox", "label": "@FormResource:CustomerCases_ShowCompanyLogos", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 23}}, {"name": "ShowCtaButton", "type": "checkbox", "label": "@FormResource:ShowCtaButton", "display": {"group": "@FormResource:FormGroups_CTA", "width": "col-span-12", "layout": "inline", "collapsed": true, "order": 30}}, {"name": "CtaButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_ButtonText", "display": {"group": "@FormResource:FormGroups_CTA", "width": "col-span-12", "order": 31, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 50}}, {"name": "CtaButtonUrl", "type": "text", "label": "@FormResource:FormFields_ButtonUrl", "display": {"group": "@FormResource:FormGroups_CTA", "width": "col-span-12", "layout": "inline", "collapsed": true, "order": 32}, "validation": {"maxLength": 200}}, {"name": "CustomerCases", "type": "repeater", "label": "@FormResource:CustomerCases", "display": {"group": "@FormResource:FormGroups_Content", "width": "col-span-12", "collapsed": true, "order": 40}, "template": {"fields": [{"name": "CompanyName", "type": "multilingual-text", "label": "@FormResource:CompanyName", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": true, "maxLength": 100}}, {"name": "Industry", "type": "multilingual-text", "label": "@FormResource:Industry", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 50}}, {"name": "CompanyLogo", "type": "image", "label": "@FormResource:CompanyLogo", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "fileConfig": {"folder": "customer-cases", "types": ["image/*"], "maxSize": "2MB", "multiple": false, "preview": true}}, {"name": "FeaturedImage", "type": "image", "label": "@FormResource:FeaturedImage", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "fileConfig": {"folder": "customer-cases", "types": ["image/*"], "maxSize": "5MB", "multiple": false, "preview": true}}, {"name": "ProjectTitle", "type": "multilingual-text", "label": "@FormResource:ProjectTitle", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": true, "maxLength": 150}}, {"name": "ProjectDescription", "type": "multilingual-textarea", "label": "@FormResource:ProjectDescription", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": true, "maxLength": 500}}, {"name": "TestimonialText", "type": "multilingual-textarea", "label": "@FormResource:TestimonialText", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 300}}, {"name": "Testimonial<PERSON><PERSON><PERSON>", "type": "multilingual-text", "label": "@FormResource:TestimonialAuthor", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 100}}, {"name": "TestimonialPosition", "type": "multilingual-text", "label": "@FormResource:TestimonialPosition", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 100}}, {"name": "CaseStudyUrl", "type": "text", "label": "@FormResource:CaseStudyUrl", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"maxLength": 200}}, {"name": "IsFeatured", "type": "checkbox", "label": "@FormResource:IsFeatured", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}}]}, "validation": {"required": false, "minItems": 1, "maxItems": 12}}]}