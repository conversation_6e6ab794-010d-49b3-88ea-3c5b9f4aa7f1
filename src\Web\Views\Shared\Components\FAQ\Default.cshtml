@model MlSoft.Sites.Web.ViewModels.Components.FAQComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var title = string.IsNullOrEmpty(Model?.Title) ? "" : Model?.Title;
    var subtitle = string.IsNullOrEmpty(Model?.Subtitle) ? "" : Model?.Subtitle;
    var description = string.IsNullOrEmpty(Model?.Description) ? "" : Model?.Description;
    var layout = Model?.Layout ?? "accordion";
    var showSearch = Model?.ShowSearch ?? true;
    var showCategories = Model?.ShowCategories ?? false;
    var animationEnabled = Model?.AnimationEnabled ?? true;
    var allowMultipleOpen = Model?.AllowMultipleOpen ?? false;
    var accentColor = Model?.AccentColor ?? "primary";

    // Generate unique IDs for the component
    var uniqueId = JObjectHelper.GenerateId("faq");
    var searchId = $"search_{uniqueId}";
    var categoryId = $"category_{uniqueId}";

    // Group FAQs by category if needed
    var faqItems = Model?.FAQItems ?? new List<FAQItem>();
    var categories = showCategories ? faqItems.Where(f => !string.IsNullOrEmpty(f.Category)).Select(f => f.Category).Distinct().ToList() : new List<string>();
    var popularFAQs = faqItems.Where(f => f.IsPopular).OrderBy(f => f.Order).ToList();

    // Animation classes
    var animationClass = animationEnabled ? "transition-all duration-300 ease-in-out" : "";
}

<section class="faq-component py-16 md:py-24 bg-white dark:bg-gray-900 @animationClass" id="@uniqueId">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        @* Header Section *@
        @if (!string.IsNullOrEmpty(title) || !string.IsNullOrEmpty(subtitle) || !string.IsNullOrEmpty(description))
        {
            <div class="text-center mb-12 @(animationEnabled ? "animate-fade-in-up" : "")">
                @if (!string.IsNullOrEmpty(title))
                {
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">@title</h2>
                }
                @if (!string.IsNullOrEmpty(subtitle))
                {
                    <h3 class="text-xl text-primary-600 dark:text-primary-400 font-medium mb-4">@subtitle</h3>
                }
                @if (!string.IsNullOrEmpty(description))
                {
                    <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">@description</p>
                }
            </div>
        }

        @* Search and Filter Section *@
        @if (showSearch || showCategories)
        {
            <div class="mb-8 @(animationEnabled ? "animate-fade-in-up" : "")" style="animation-delay: 0.2s;">
                <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                    
                    @* Search Box *@
                    @if (showSearch)
                    {
                        <div class="relative flex-1 max-w-md">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" id="@searchId" 
                                   class="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @animationClass"
                                   placeholder="@SharedRes["SearchFAQs"]" />
                        </div>
                    }

                    @* Category Filter *@
                    @if (showCategories && categories.Any())
                    {
                        <div class="flex flex-wrap gap-2">
                            <button type="button" class="category-filter active px-4 py-2 text-sm font-medium rounded-full bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-800 @animationClass" data-category="all">
                                @SharedRes["AllCategories"]
                            </button>
                            @foreach (var category in categories)
                            {
                                <button type="button" class="category-filter px-4 py-2 text-sm font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 @animationClass" data-category="@category">
                                    @category
                                </button>
                            }
                        </div>
                    }
                </div>
            </div>
        }

        @* Popular FAQs Section *@
        @if (popularFAQs.Any())
        {
            <div class="mb-12 @(animationEnabled ? "animate-fade-in-up" : "")" style="animation-delay: 0.3s;">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-5 h-5 text-primary-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    @SharedRes["PopularQuestions"]
                </h3>
                <div class="grid gap-3 sm:grid-cols-2">
                    @foreach (var faq in popularFAQs.Take(4))
                    {
                        <button type="button" class="popular-faq-item text-left p-4 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 @animationClass" data-target="#<EMAIL>()">
                            <span class="text-sm font-medium text-primary-700 dark:text-primary-300">@faq.Question</span>
                        </button>
                    }
                </div>
            </div>
        }

        @* FAQ Items *@
        @if (faqItems.Any())
        {
            <div class="faq-container @(animationEnabled ? "animate-fade-in-up" : "")" style="animation-delay: 0.4s;">
                @if (layout == "accordion")
                {
                    <div class="space-y-4" data-accordion="@(allowMultipleOpen ? "open" : "collapse")" data-active-classes="bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300" data-inactive-classes="text-gray-700 dark:text-gray-300">
                        @foreach (var faq in faqItems.OrderBy(f => f.Order))
                        {
                            var itemId = $"faq-item-{faq.GetHashCode()}";
                            var headingId = $"faq-heading-{faq.GetHashCode()}";
                            var bodyId = $"faq-body-{faq.GetHashCode()}";
                            
                            <div class="faq-item border border-gray-200 dark:border-gray-700 rounded-lg @animationClass" id="@itemId" data-category="@(faq.Category ?? "")">
                                <h3 id="@headingId">
                                    <button type="button" class="faq-question flex items-center justify-between w-full p-5 font-medium text-left text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border-0 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-800 @animationClass" 
                                            data-accordion-target="#@bodyId" aria-expanded="false" aria-controls="@bodyId">
                                        <span class="flex-1">@faq.Question</span>
                                        <svg class="w-3 h-3 rotate-180 shrink-0 transition-transform duration-200" aria-hidden="true" fill="none" viewBox="0 0 10 6">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
                                        </svg>
                                    </button>
                                </h3>
                                <div id="@bodyId" class="hidden" aria-labelledby="@headingId">
                                    <div class="faq-answer p-5 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                                        <div class="prose prose-sm max-w-none text-gray-600 dark:text-gray-300">
                                            @Html.Raw(faq.Answer)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    @* List Layout *@
                    <div class="space-y-6">
                        @foreach (var faq in faqItems.OrderBy(f => f.Order))
                        {
                            <div class="faq-item bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm hover:shadow-md @animationClass" data-category="@(faq.Category ?? "")">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">@faq.Question</h3>
                                <div class="prose prose-sm max-w-none text-gray-600 dark:text-gray-300">
                                    @Html.Raw(faq.Answer)
                                </div>
                            </div>
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">@SharedRes["NoFAQsFound"]</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">@SharedRes["NoFAQsFoundDescription"]</p>
            </div>
        }
    </div>
</section>

@* Custom Animations and JavaScript *@
@if (animationEnabled)
{
    <style>
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .faq-item.hidden-by-search {
            display: none !important;
        }

        .faq-item.hidden-by-category {
            display: none !important;
        }
    </style>
}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const faqContainer = document.getElementById('@uniqueId');
    if (!faqContainer) return;

    const searchInput = document.getElementById('@searchId');
    const categoryFilters = faqContainer.querySelectorAll('.category-filter');
    const faqItems = faqContainer.querySelectorAll('.faq-item');
    const popularItems = faqContainer.querySelectorAll('.popular-faq-item');

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question span, h3')?.textContent?.toLowerCase() || '';
                const answer = item.querySelector('.faq-answer, .prose')?.textContent?.toLowerCase() || '';
                
                if (searchTerm === '' || question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.classList.remove('hidden-by-search');
                } else {
                    item.classList.add('hidden-by-search');
                }
            });
        });
    }

    // Category filter functionality
    categoryFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            const selectedCategory = this.getAttribute('data-category');
            
            // Update active state
            categoryFilters.forEach(f => f.classList.remove('active', 'bg-primary-100', 'dark:bg-primary-900', 'text-primary-700', 'dark:text-primary-300'));
            categoryFilters.forEach(f => f.classList.add('bg-gray-100', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300'));
            
            this.classList.add('active', 'bg-primary-100', 'dark:bg-primary-900', 'text-primary-700', 'dark:text-primary-300');
            this.classList.remove('bg-gray-100', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            
            // Filter items
            faqItems.forEach(item => {
                const itemCategory = item.getAttribute('data-category') || '';
                
                if (selectedCategory === 'all' || itemCategory === selectedCategory) {
                    item.classList.remove('hidden-by-category');
                } else {
                    item.classList.add('hidden-by-category');
                }
            });
        });
    });

    // Popular FAQ click handlers
    popularItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetSelector = this.getAttribute('data-target');
            const targetElement = document.querySelector(targetSelector);
            
            if (targetElement) {
                // Clear search and category filters
                if (searchInput) searchInput.value = '';
                faqItems.forEach(item => {
                    item.classList.remove('hidden-by-search', 'hidden-by-category');
                });
                
                // Reset category filter to "all"
                const allCategoryFilter = faqContainer.querySelector('.category-filter[data-category="all"]');
                if (allCategoryFilter) {
                    allCategoryFilter.click();
                }
                
                // Scroll to target and open if accordion
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                // Open accordion item if needed
                const accordionButton = targetElement.querySelector('.faq-question');
                const accordionBody = targetElement.querySelector('[id^="faq-body-"]');
                
                if (accordionButton && accordionBody && accordionBody.classList.contains('hidden')) {
                    accordionButton.click();
                }
            }
        });
    });

    // Initialize Flowbite accordion if layout is accordion
    @if (layout == "accordion")
    {
        <text>
        if (window.Accordion) {
            const accordionElement = faqContainer.querySelector('[data-accordion]');
            if (accordionElement) {
                new Accordion(accordionElement, {
                    alwaysOpen: @(allowMultipleOpen ? "true" : "false"),
                    activeClasses: 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300',
                    inactiveClasses: 'text-gray-700 dark:text-gray-300'
                });
            }
        }
        </text>
    }
});
</script>