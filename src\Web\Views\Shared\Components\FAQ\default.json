{"ComponentId": "FAQ", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "常见问题", "en": "FAQ", "ja": "よくある質問"}, "Descriptions": {"zh": "常见问题解答组件，支持搜索、分类筛选和手风琴展开", "en": "Frequently Asked Questions component with search, category filtering and accordion layout", "ja": "検索、カテゴリフィルタリング、アコーディオンレイアウト対応のよくある質問コンポーネント"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@FormResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_TitleHelpText"}, "validation": {"maxLength": 200}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@FormResource:FormFields_Subtitle", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_SubtitleHelpText"}, "validation": {"maxLength": 300}}, {"name": "Description", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 3, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_DescriptionHelpText"}, "validation": {"maxLength": 500}}, {"name": "FAQItems", "type": "array", "label": "@FormResource:FormFields_FAQItems", "display": {"group": "@FormResource:FormGroups_FAQContent", "width": "col-span-12", "order": 1, "collapsed": true, "helpText": "@FormResource:FormFields_FAQItemsHelpText"}, "minItems": 1, "maxItems": 50, "template": {"fields": [{"name": "Question", "type": "multilingual-text", "label": "@FormResource:FormFields_Question", "display": {"layout": "inline", "width": "col-span-12"}, "validation": {"required": true, "maxLength": 300}}, {"name": "Answer", "type": "multilingual-richtext", "label": "@FormResource:FormFields_Answer", "display": {"layout": "inline", "width": "col-span-12"}, "validation": {"required": true}, "editorConfig": {"toolbar": "bold italic underline | bullist numlist | link | removeformat", "height": 200, "plugins": ["lists", "link"], "menubar": false, "branding": false}}, {"name": "Category", "type": "multilingual-text", "label": "@FormResource:FormFields_Category", "display": {"layout": "inline", "width": "col-span-12", "collapsed": true}, "validation": {"maxLength": 100}}, {"name": "Order", "type": "number", "label": "@FormResource:FormFields_Order", "display": {"layout": "inline", "width": "col-span-4", "collapsed": true}, "defaultValue": 0, "validation": {"min": 0, "max": 9999}}, {"name": "IsPopular", "type": "checkbox", "label": "@FormResource:FormFields_IsPopular", "display": {"layout": "inline", "width": "col-span-4", "collapsed": true}, "defaultValue": false}]}}, {"name": "Layout", "type": "select", "label": "@FormResource:FormFields_Layout", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_FAQLayoutHelpText"}, "options": [{"value": "accordion", "label": "@FormResource:FormFields_AccordionLayout"}, {"value": "list", "label": "@FormResource:FormFields_ListLayout"}], "defaultValue": "accordion"}, {"name": "ShowSearch", "type": "checkbox", "label": "@FormResource:FormFields_ShowSearch", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 2, "layout": "inline", "helpText": "@FormResource:FormFields_ShowSearchHelpText"}, "defaultValue": true}, {"name": "ShowCategories", "type": "checkbox", "label": "@FormResource:FormFields_ShowCategories", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 3, "layout": "inline", "helpText": "@FormResource:FormFields_ShowCategoriesHelpText"}, "defaultValue": false}, {"name": "AnimationEnabled", "type": "checkbox", "label": "@FormResource:FormFields_AnimationEnabled", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 4, "layout": "inline", "helpText": "@FormResource:FormFields_AnimationEnabledHelpText"}, "defaultValue": true}, {"name": "AllowMultipleOpen", "type": "checkbox", "label": "@FormResource:FormFields_AllowMultipleOpen", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 5, "layout": "inline", "helpText": "@FormResource:FormFields_AllowMultipleOpenHelpText"}, "defaultValue": false}, {"name": "AccentColor", "type": "select", "label": "@FormResource:FormFields_AccentColor", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 6, "layout": "inline", "helpText": "@FormResource:FormFields_AccentColorHelpText"}, "options": [{"value": "primary", "label": "@FormResource:FormFields_PrimaryColor"}, {"value": "secondary", "label": "@FormResource:FormFields_SecondaryColor"}, {"value": "success", "label": "@FormResource:FormFields_SuccessColor"}, {"value": "info", "label": "@FormResource:FormFields_InfoColor"}], "defaultValue": "primary"}]}