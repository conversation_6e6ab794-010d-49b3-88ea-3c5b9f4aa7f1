@model MlSoft.Sites.Web.ViewModels.Components.FooterComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@using Microsoft.AspNetCore.Hosting
@using Microsoft.Extensions.DependencyInjection
@using Microsoft.Extensions.Hosting

@{
    // 获取当前语言
    var currentLanguage = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var defaultLanguage = ViewData["DefaultLanguage"]?.ToString() ?? "zh";

    var culturePrefix = currentLanguage == defaultLanguage ? "" : $"/{currentLanguage}";

    // 使用FooterComponentViewModel属性直接获取数据
    var logo = Model?.Logo ?? "";
    var companyName = Model?.CompanyName ?? "";
    var companyDescription = Model?.CompanyDescription ?? "";
    var copyright = Model?.Copyright ?? "";
    var showAdminLinks = Model?.ShowAdminLinks ?? true;

    // 获取分区和社交链接数据
    var sections = Model?.Sections ?? new List<FooterSection>();
    var socialLinks = Model?.SocialLinks ?? new List<SocialLink>();
}

<!-- Footer -->
<footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
    <div class="mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8">
        <div class="md:flex md:justify-between">
            <div class="mb-6 md:mb-0">
                <a href="@($"{culturePrefix}/")" class="flex items-center">
                    @if (!string.IsNullOrEmpty(logo))
                    {
                        <img src="@logo" alt="@companyName" class="h-8 w-auto mr-3" />
                    }
                    <span class="self-center text-xl font-semibold whitespace-nowrap text-gray-700 dark:text-gray-100">
                        @companyName
                    </span>
                </a>
                @if (!string.IsNullOrEmpty(companyDescription))
                {
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">@companyDescription</p>
                }
            </div>

            <div class="grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3">
                @if (sections.Any())
                {
                    @foreach (var section in sections)
                    {
                        <div>
                            @if (!string.IsNullOrEmpty(section.Title))
                            {
                                <h2 class="mb-6 text-sm font-semibold uppercase text-gray-900 dark:text-gray-100">@(section.Title)</h2>
                            }
                            @if (section.Links?.Any() == true)
                            {
                                <ul class="font-medium">
                                    @foreach (var link in section.Links)
                                    {
                                        <li class="mb-4">
                                            <a href="@(link.Url ?? "#")"
                                               class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:underline"
                                               @(link.OpenInNewTab ? "target=\"_blank\"" : "")>
                                                @link.Text
                                            </a>
                                        </li>
                                    }
                                </ul>
                            }
                        </div>
                    }
                }
            </div>
        </div>
        <hr class="my-6 sm:mx-auto lg:my-8 border-gray-200 dark:border-gray-700" />
        <div class="sm:flex sm:items-center sm:justify-between">
            <span class="text-sm sm:text-center text-gray-600 dark:text-gray-400">
                @if (!string.IsNullOrEmpty(copyright))
                {
                    @Html.Raw(copyright)
                }
                else
                {
                    @Html.Raw($"© {DateTime.Now.Year} <a href=\"{culturePrefix}/\" class=\"text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:underline\">{companyName}</a>. All Rights Reserved.")
                }

                @if (showAdminLinks || Context.RequestServices.GetRequiredService<IWebHostEnvironment>().IsDevelopment())
                {
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <a asp-area="" asp-controller="Admin" asp-action="Dashboard"
                           class="py-2 px-3 rounded md:border-0 md:p-0 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-500 hover:underline">
                            @AdminRes["BackendManagement"]
                        </a>
                    }
                    else
                    {
                        <a asp-area="" asp-controller="Account" asp-action="Login"
                           class="py-2 px-3 rounded md:border-0 md:p-0 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:underline">
                            @AccountRes["AdminLogin"]
                        </a>
                    }
                }
            </span>
            <div class="flex mt-4 sm:justify-center sm:mt-0">
                @if (socialLinks.Any())
                {
                    @foreach (var social in socialLinks)
                    {
                        <a href="@(social.Url ?? "#")" class="mr-4 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400" target="_blank">
                            @if (!string.IsNullOrEmpty(social.Icon))
                            {
                                <i class="@social.Icon"></i>
                            }
                            else
                            {
                                <span class="sr-only">@social.Platform</span>
                                @social.Platform
                            }
                        </a>
                    }
                }
            </div>
        </div>
    </div>
</footer>