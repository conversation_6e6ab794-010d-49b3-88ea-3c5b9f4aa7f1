{"ComponentId": "Footer", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "默认页脚", "en": "<PERSON><PERSON><PERSON>", "ja": "デフォルトフッター"}, "Descriptions": {"zh": "标准页脚布局", "en": "Standard footer layout", "ja": "標準フッターレイアウト"}, "formFields": [{"name": "CompanyName", "type": "multilingual-text", "label": "@FormResource:FormFields_CompanyName", "display": {"group": "@FormResource:FormGroups_CompanyInfo", "width": "col-span-12", "order": 1, "collapsed": true, "helpText": "@FormResource:FormFields_CompanyNameHelpText", "layout": "inline"}}, {"name": "CompanyDescription", "type": "multilingual-textarea", "label": "@FormResource:FormFields_CompanyDescription", "display": {"group": "@FormResource:FormGroups_CompanyInfo", "width": "col-span-12", "order": 2, "collapsed": true, "layout": "inline", "helpText": "@FormResource:FormFields_CompanyDescriptionHelpText", "rows": 3}, "validation": {"maxLength": 500}}, {"name": "Logo", "type": "image", "label": "@SharedResource:FormFields_Logo", "display": {"group": "@FormResource:FormGroups_CompanyInfo", "width": "col-span-12", "order": 3, "helpText": "@SharedResource:FormFields_LogoHelpText", "collapsed": true}, "fileConfig": {"folder": "footer-logos", "types": ["image/jpeg", "image/png", "image/webp"], "maxSize": "2MB", "multiple": false, "preview": true}}, {"name": "Copyright", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Copyright", "display": {"group": "@FormResource:FormGroups_CompanyInfo", "width": "col-span-12", "layout": "inline", "order": 4, "collapsed": true, "helpText": "@FormResource:FormFields_CopyrightHelpText"}}, {"name": "Sections", "type": "repeater", "label": "@FormResource:FormFields_FooterSections", "display": {"group": "@FormResource:FormGroups_Navigation", "width": "col-span-12", "order": 1, "helpText": "@FormResource:FormFields_FooterSectionsHelpText", "collapsed": true}, "minItems": 0, "maxItems": 6, "template": {"fields": [{"name": "Title", "type": "multilingual-text", "label": "@FormResource:FormFields_SectionTitle", "display": {"width": "col-span-12", "order": 1, "layout": "inline", "helpText": "@FormResource:FormFields_SectionTitleHelpText"}}, {"name": "Links", "type": "repeater", "label": "@FormResource:FormFields_SectionLinks", "display": {"width": "col-span-12", "order": 2, "helpText": "@FormResource:FormFields_SectionLinksHelpText", "collapsed": true}, "minItems": 0, "maxItems": 10, "template": {"fields": [{"name": "Text", "type": "multilingual-text", "label": "@FormResource:FormFields_LinkText", "display": {"width": "col-span-4", "order": 1, "layout": "inline", "helpText": "@FormResource:FormFields_LinkTextHelpText"}, "validation": {"required": true, "maxLength": 50}}, {"name": "Url", "type": "text", "label": "@FormResource:FormFields_LinkUrl", "display": {"width": "col-span-6", "layout": "inline", "order": 2, "helpText": "@FormResource:FormFields_LinkUrlHelpText"}, "validation": {"required": true, "maxLength": 200}}, {"name": "OpenInNewTab", "type": "checkbox", "label": "@FormResource:FormFields_OpenInNewTab", "display": {"width": "col-span-2", "order": 3, "layout": "inline", "helpText": "@FormResource:FormFields_OpenInNewTabHelpText"}, "defaultValue": false}]}}]}}, {"name": "ContactInfo", "type": "group", "label": "@FormResource:FormFields_ContactInfo", "display": {"group": "@FormResource:FormGroups_ContactInfo", "width": "col-span-12", "order": 7, "layout": "inline", "helpText": "@FormResource:FormFields_ContactInfoHelpText", "collapsed": true}, "fields": [{"name": "Phone", "type": "text", "label": "@FormResource:FormFields_Phone", "display": {"width": "col-span-4", "order": 1, "layout": "inline", "helpText": "@FormResource:FormFields_PhoneHelpText"}, "validation": {"maxLength": 50}}, {"name": "Email", "type": "email", "label": "@FormResource:FormFields_Email", "display": {"width": "col-span-4", "order": 2, "layout": "inline", "helpText": "@FormResource:FormFields_EmailHelpText"}, "validation": {"maxLength": 100}}, {"name": "Address", "type": "text", "label": "@FormResource:FormFields_Address", "display": {"layout": "inline", "width": "col-span-4", "order": 3, "helpText": "@FormResource:FormFields_AddressHelpText"}}]}, {"name": "SocialLinks", "type": "repeater", "label": "@FormResource:FormFields_SocialLinks", "display": {"group": "@FormResource:FormGroups_SocialMedia", "width": "col-span-12", "order": 1, "helpText": "@FormResource:FormFields_SocialLinksHelpText", "collapsed": true}, "minItems": 0, "maxItems": 10, "template": {"fields": [{"name": "Platform", "type": "select", "label": "@FormResource:FormFields_SocialPlatform", "display": {"width": "col-span-4", "order": 1, "layout": "inline", "helpText": "@FormResource:FormFields_SocialPlatformHelpText"}, "validation": {"required": true}, "options": [{"value": "", "label": "@FormResource:FormFields_PleaseSelect"}, {"value": "facebook", "label": "Facebook"}, {"value": "twitter", "label": "Twitter"}, {"value": "instagram", "label": "Instagram"}, {"value": "linkedin", "label": "LinkedIn"}, {"value": "youtube", "label": "YouTube"}, {"value": "github", "label": "GitHub"}, {"value": "other", "label": "FormFields_Other"}]}, {"name": "Url", "type": "text", "label": "@FormResource:FormFields_SocialUrl", "display": {"width": "col-span-4", "layout": "inline", "order": 2, "helpText": "@FormResource:FormFields_SocialUrlHelpText"}, "validation": {"required": true, "maxLength": 200}}, {"name": "Icon", "type": "text", "label": "@FormResource:FormFields_SocialIcon", "display": {"width": "col-span-4", "layout": "inline", "order": 3, "helpText": "@FormResource:FormFields_SocialIconHelpText"}, "validation": {"maxLength": 50}}]}}, {"name": "ShowAdminLinks", "type": "checkbox", "label": "@FormResource:FormFields_ShowAdminLinks", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-6", "order": 1, "collapsed": true, "helpText": "@FormResource:FormFields_ShowAdminLinksHelpText"}, "defaultValue": false}]}