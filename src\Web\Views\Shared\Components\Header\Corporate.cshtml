@model MlSoft.Sites.Web.ViewModels.Components.HeaderComponentViewModel

<header class="corporate-header bg-primary-900 text-white shadow-lg">
    <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
            <!-- Brand Section -->
            <div class="flex items-center space-x-4">
                @if (!string.IsNullOrEmpty(Model.Logo))
                {
                    <img src="@Model.Logo" alt="@Model.CompanyName" class="h-12 w-auto" />
                }
                @if (!string.IsNullOrEmpty(Model.CompanyName))
                {
                    <div class="text-xl font-bold">@Model.CompanyName</div>
                }
            </div>

            <!-- Right Section: Contact Info, Dark Mode Toggle and Language Selector -->
            <div class="flex items-center space-x-4">
                <!-- Contact Info (Corporate Style) -->
                @if (Model.ContactInfo != null)
                {
                    <div class="hidden md:flex items-center space-x-6 text-sm">
                        @if (!string.IsNullOrEmpty(Model.ContactInfo.Phone))
                        {
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                </svg>
                                <span>@Model.ContactInfo.Phone</span>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.ContactInfo.Email))
                        {
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                </svg>
                                <span>@Model.ContactInfo.Email</span>
                            </div>
                        }
                    </div>
                }

                <!-- Dark Mode Toggle -->
                <button id="darkModeToggle" class="p-2 rounded-lg bg-white/20 hover:bg-white/30 dark:bg-black/20 dark:hover:bg-black/30 transition-colors duration-200" aria-label="切换暗黑模式">
                    <!-- Sun Icon (Light Mode) -->
                    <svg id="sunIcon" class="w-5 h-5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                    </svg>
                    <!-- Moon Icon (Dark Mode) -->
                    <svg id="moonIcon" class="w-5 h-5 text-blue-300 hidden" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                </button>

                <!-- Language Selector -->
                @if (Model.ShowLanguageSelector)
                {
                    <div class="language-selector">
                        <language-switcher supported-languages="@(ViewData["SupportedLanguages"] as SupportedLanguage[])" 
                                         css-class="corporate-language-switcher" 
                                         show-flags="true" 
                                         show-names="true" 
                                         dropdown="true" />
                    </div>
                }
            </div>
        </div>
    </div>
</header>