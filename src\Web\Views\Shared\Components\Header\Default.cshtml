@model MlSoft.Sites.Web.ViewModels.Components.HeaderComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
	// 获取当前语言
	var currentLanguage = ViewData["CurrentLanguage"]?.ToString();
	var defaultLanguage = ViewData["DefaultLanguage"]?.ToString();

	var culturePrefix = currentLanguage == defaultLanguage ? "" : $"/{currentLanguage}";

	// 使用HeaderComponentViewModel数据
	var logo = Model?.Logo ?? "";
	var companyName = Model?.CompanyName ?? "";
	var phone = Model?.ContactInfo?.Phone ?? "";
	var email = Model?.ContactInfo?.Email ?? "";
	var address = Model?.ContactInfo?.Address ?? "";

	var showLanguageSelector = Model?.ShowLanguageSelector ?? true;
	var showSearchBox = Model?.ShowSearchBox ?? true;
	var showDarkModeToggle = true; // 可以从配置中获取
	var mobileMenuButtonText = SharedRes["OpenMobileMenu"];

	// 获取菜单项数据
	var menuItems = Model?.MenuItems ?? new List<NavigationItem>();
}

<div class="sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60">
	<!-- Header/Logo Section -->
	<div class="max-w-screen-xl mx-auto px-4 py-3">
		<div class="flex items-center justify-between">
			<!-- Logo and Company Name -->
			<a href="@($"{culturePrefix}/")" class="flex items-center space-x-3" title="@companyName">
				@if (!string.IsNullOrEmpty(logo))
				{
					<img src="@logo" alt="@companyName" class="h-10 w-auto" />
				}
				@if (Model.ShowCompanyName)
				{
					<span class="self-center text-2xl font-semibold whitespace-nowrap text-gray-700 dark:text-gray-100">
						@companyName
					</span>
				}
			</a>

			<!-- Contact Info, Dark Mode Toggle and Language Selector -->
			<div class="hidden md:flex items-center space-x-6">
				@if (!string.IsNullOrEmpty(phone))
				{
					<span class="text-sm text-gray-600 dark:text-gray-400">@phone</span>
				}

				@if (!string.IsNullOrEmpty(email))
				{
					<span class="text-sm text-gray-600 dark:text-gray-400">@email</span>
				}

				@if (showDarkModeToggle)
				{
					<!-- Dark Mode Toggle -->
					<button id="darkModeToggle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200" aria-label="">
						<!-- Sun Icon (Light Mode) -->
						<svg id="sunIcon" class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
						</svg>
						<!-- Moon Icon (Dark Mode) -->
						<svg id="moonIcon" class="w-5 h-5 text-primary-600 hidden" fill="currentColor" viewBox="0 0 20 20">
							<path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
						</svg>
					</button>
				}

				@if (showLanguageSelector)
				{
					var supportedLangs = ViewData["SupportedLanguages"] as SupportedLanguage[];
					@if (supportedLangs != null && supportedLangs.Length > 1)
					{
						<language-switcher css-class="language-switcher inline-flex"
										   show-flags="false"
										   show-names="true"
										   dropdown="true" />
					}
				}
			</div>
		</div>
	</div>

	<!-- Navigation -->
	<nav class="border-t border-gray-50 dark:border-gray-600">
		<div class="max-w-7xl flex flex-wrap items-center justify-between mx-auto p-4">
			<!-- Mobile menu button -->
			<button data-collapse-toggle="navbar-default" type="button"
					class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 dark:text-gray-400 rounded-lg md:hidden hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-600"
					aria-controls="navbar-default" aria-expanded="false">
				<span class="sr-only">@mobileMenuButtonText</span>
				<svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
					<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h15M1 7h15M1 13h15" />
				</svg>
			</button>

			<!-- Navigation menu -->
			<div class="hidden w-full md:block md:w-auto" id="navbar-default">
				<ul class="font-medium flex flex-col  md:p-0  md:flex-row md:space-x-8 rtl:space-x-reverse ">
					@if (menuItems != null && menuItems.Any())
					{
						@foreach (var item in menuItems.OrderBy(x => x.Sort))
						{
							var itemId = $"menu_{Guid.NewGuid().ToString("N")[..8]}";

							<li>
								@if (item.SubItems != null && item.SubItems.Any())
								{
									<button id="dropdownNavbarLink-@itemId" data-dropdown-toggle="dropdown-@itemId"
											class="flex items-center justify-between w-full py-2 px-3 text-gray-700 dark:text-gray-100 rounded hover:bg-gray-100 dark:hover:bg-gray-700 md:hover:bg-transparent md:dark:hover:bg-transparent md:border-0 md:hover:text-primary-700 md:dark:hover:text-primary-400 md:p-0 md:w-auto">
										@item.Text
										<svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
											<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4" />
										</svg>
									</button>
									<!-- Dropdown menu -->
									<div id="dropdown-@itemId" class="z-10 hidden font-normal bg-white dark:bg-gray-700 divide-y divide-gray-100 dark:divide-gray-600 rounded-lg shadow w-44">
										<ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownNavbarLink-@itemId">
											@foreach (var subItem in item.SubItems.OrderBy(x => x.Sort))
											{
												<li>
													<a href="@(subItem.Url ?? "#")"
													   class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 hover:text-primary-700 dark:hover:text-primary-400"
													   @(subItem.IsExternal ? "target=\"_blank\"" : "")>
														@if (!string.IsNullOrEmpty(subItem.Icon))
														{
															<i class="@subItem.Icon mr-2"></i>
														}
														@subItem.Text
													</a>
												</li>
											}
										</ul>
									</div>
								}
								else
								{
									<a href="@(item.Url ?? "#")"
									   class="block py-2 px-3 text-gray-700 dark:text-gray-100 rounded hover:bg-gray-100 dark:hover:bg-gray-700 md:hover:bg-transparent md:dark:hover:bg-transparent md:border-0 md:hover:text-primary-700 md:dark:hover:text-primary-400 md:p-0 @(item.IsActive ? "text-primary-700 dark:text-primary-400" : "")"
									   @(item.IsExternal ? "target=\"_blank\"" : "")>
										@if (!string.IsNullOrEmpty(item.Icon))
										{
											<i class="@item.Icon mr-2"></i>
										}
										@item.Text
									</a>
								}
							</li>
						}
					}
				</ul>
			</div>
		</div>
	</nav>

</div>