@model MlSoft.Sites.Web.ViewModels.Components.HeaderComponentViewModel

<header class="minimal-header bg-white border-b border-gray-200">
    <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-center relative">
            <!-- Centered Brand -->
            <div class="flex items-center space-x-3">
                @if (!string.IsNullOrEmpty(Model.Logo))
                {
                    <img src="@Model.Logo" alt="@Model.CompanyName" class="h-8 w-auto" />
                }
                @if (!string.IsNullOrEmpty(Model.CompanyName))
                {
                    <div class="text-lg font-light text-gray-800">@Model.CompanyName</div>
                }
            </div>

            <!-- Right Section: Dark Mode Toggle and Language Selector -->
            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-3">
                <!-- Dark Mode Toggle -->
                <button id="darkModeToggle" class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200" aria-label="切换暗黑模式">
                    <!-- Sun Icon (Light Mode) -->
                    <svg id="sunIcon" class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                    </svg>
                    <!-- Moon Icon (Dark Mode) -->
                    <svg id="moonIcon" class="w-4 h-4 text-blue-600 hidden" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                </button>
                
                <!-- Minimal Language Selector -->
                @if (Model.ShowLanguageSelector)
                {
                    <div>
                        <language-switcher supported-languages="@(ViewData["SupportedLanguages"] as SupportedLanguage[])" 
                                         css-class="minimal-language-switcher" 
                                         show-flags="false" 
                                         show-names="true" 
                                         dropdown="true" />
                    </div>
                }
            </div>
        </div>
    </div>
</header>