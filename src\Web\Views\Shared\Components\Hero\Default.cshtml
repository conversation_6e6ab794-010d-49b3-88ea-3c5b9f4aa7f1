@model MlSoft.Sites.Web.ViewModels.Components.HeroComponentViewModel
@using MlSoft.Sites.Web.Helpers

@{
    // 从ViewModel中获取数据
    var title = Model?.Title ?? "";
    var subtitle = Model?.Subtitle ?? "";
    var description = Model?.Description ?? "";
    var backgroundImage = Model?.BackgroundImage ?? "";
    var backgroundVideo = Model?.BackgroundVideo ?? "";
    var showOverlay = Model?.ShowOverlay ?? true;
    var overlayOpacity = string.IsNullOrEmpty(Model?.OverlayOpacity) ? "0.5" : Model?.OverlayOpacity;
    var textAlignment = string.IsNullOrEmpty(Model?.TextAlignment) ? "center" : Model?.TextAlignment;
    var height = string.IsNullOrEmpty(Model?.Height) ? "large" : Model?.Height;
    var animationEnabled = Model?.AnimationEnabled ?? true;
    var parallaxEnabled = Model?.ParallaxEnabled ?? false;
    var parallaxSpeed = Model?.ParallaxSpeed ?? 0.5f;
    var showScrollIndicator = Model?.ShowScrollIndicator ?? false;
    var primaryButtonText = Model?.PrimaryButtonText ?? "";
    var primaryButtonUrl = Model?.PrimaryButtonUrl ?? "";
    var secondaryButtonText = Model?.SecondaryButtonText ?? "";
    var secondaryButtonUrl = Model?.SecondaryButtonUrl ?? "";

    // 生成CSS类
    var heightClass = JObjectHelper.GetHeightClass(height);
    var textAlignClass = JObjectHelper.GetTextAlignmentClass(textAlignment);
    var animationClass = animationEnabled ? "transition-all duration-700 ease-in-out" : "";
    var isEnabledParallax = parallaxEnabled ? "true" : "false";

    var opacityClass = overlayOpacity switch
    {
        "0.1" => "bg-opacity-10",
        "0.2" => "bg-opacity-20",
        "0.3" => "bg-opacity-30",
        "0.4" => "bg-opacity-40",
        "0.5" => "bg-opacity-50",
        "0.6" => "bg-opacity-60",
        "0.7" => "bg-opacity-70",
        "0.8" => "bg-opacity-80",
        "0.9" => "bg-opacity-90",
        _ => "bg-opacity-50"
    };

    var hasBackground = !string.IsNullOrEmpty(backgroundImage) || !string.IsNullOrEmpty(backgroundVideo);
    var uniqueId = JObjectHelper.GenerateId("hero");



    string GetButtonClass(string type) => JObjectHelper.GetButtonClass(type);

    string ProcessFilePath(string filePath) => string.IsNullOrEmpty(filePath) ? "" : filePath.StartsWith("/") ? filePath : $"/{filePath}";

}

<section class="hero-section relative @heightClass bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800 py-16 md:py-24 flex items-center @animationClass"
         id="@uniqueId"
         data-parallax="@isEnabledParallax"
         data-speed="@parallaxSpeed.ToString("F1")">

    @* 背景图片层 *@
    @if (!string.IsNullOrEmpty(backgroundImage))
    {
        <div class="absolute inset-0 z-0 overflow-hidden">
            <img src="@ProcessFilePath(backgroundImage)"
                 alt=""
                 class="w-full h-full object-cover @(animationEnabled ? "transition-transform duration-1000 ease-out hover:scale-105" : "") @(parallaxEnabled ? "parallax-background" : "")"
                 data-speed="@parallaxSpeed.ToString("F1")"
                 loading="eager"
                 fetchpriority="high" />
        </div>
    }

    @* 背景视频层 *@
    @if (!string.IsNullOrEmpty(backgroundVideo))
    {
        <div class="absolute inset-0 z-0 overflow-hidden">
            <video class="w-full h-full object-cover @(parallaxEnabled ? "parallax-background" : "")"
                   autoplay muted loop
                   data-speed="@parallaxSpeed.ToString("F1")">
                <source src="@ProcessFilePath(backgroundVideo)" type="video/mp4">
            </video>
        </div>
    }

    @* 遮罩层 *@
    @if (showOverlay && hasBackground)
    {
        <div class="absolute inset-0 bg-black @opacityClass dark:@opacityClass z-5 @(animationEnabled ? "transition-opacity duration-500" : "")"></div>
    }

    @* 内容层 *@
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div class="@textAlignClass @(animationEnabled ? "animate-fade-in-up" : "")">
            @if (!string.IsNullOrEmpty(title))
            {
                <h1 class="text-4xl md:text-6xl font-bold @(hasBackground ? "text-white" : "text-primary-900 dark:text-white") mb-8 @(animationEnabled ? "transition-all duration-700 delay-200" : "")">
                    @title
                </h1>
            }

            @if (!string.IsNullOrEmpty(subtitle))
            {
                <h2 class="text-xl @(hasBackground ? "text-gray-200" : "text-primary-700 dark:text-gray-300") mb-8 @(textAlignment == "center" ? "mx-auto" : "") @(animationEnabled ? "transition-all duration-700 delay-400" : "")">
                    @subtitle
                </h2>
            }

            @if (!string.IsNullOrEmpty(description))
            {
                <p class="@(hasBackground ? "text-gray-300" : "text-primary-600 dark:text-gray-400") mb-8 @(textAlignment == "center" ? "mx-auto" : "") @(animationEnabled ? "transition-all duration-700 delay-500" : "") max-w-3xl">
                    @description
                </p>
            }

            @if (!string.IsNullOrEmpty(primaryButtonText) || !string.IsNullOrEmpty(secondaryButtonText))
            {
                <div class="flex flex-col sm:flex-row gap-4 @(textAlignment == "center" ? "justify-center" : textAlignment == "right" ? "justify-end" : "justify-start") items-center @(animationEnabled ? "transition-all duration-700 delay-700" : "")">
                    @if (!string.IsNullOrEmpty(primaryButtonText))
                    {
                        @if(!string.IsNullOrEmpty(primaryButtonUrl))
                        {
                             <a href="@primaryButtonUrl"
                               class="@GetButtonClass("primary") @(animationEnabled ? "hover:scale-105 hover:shadow-xl" : "")">
                                @primaryButtonText
                            </a>
                        }
                        else
                        {
                             <button
                               class="@GetButtonClass("primary") @(animationEnabled ? "hover:scale-105 hover:shadow-xl" : "")">
                                @primaryButtonText
                            </button>
                        }

                    }

                    @if (!string.IsNullOrEmpty(secondaryButtonText))
                    {
                        @if(!string.IsNullOrEmpty(secondaryButtonUrl))
                        {
                             <a href="@secondaryButtonUrl"
                           class="@GetButtonClass("outline") bg-white bg-opacity-90 dark:bg-gray-800 dark:bg-opacity-90 @(animationEnabled ? "hover:scale-105" : "")">
                            @secondaryButtonText
                        </a>
                        } else
                        {
                             <button  class="@GetButtonClass("outline") bg-white bg-opacity-90 dark:bg-gray-800 dark:bg-opacity-90 @(animationEnabled ? "hover:scale-105" : "")">
                                @secondaryButtonText
                            </button>
                        }


                    }
                </div>
            }
        </div>
    </div>

    @* 滚动指示器 *@
    @if (showScrollIndicator)
    {
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center z-10">
            <div class="@(animationEnabled ? "animate-bounce" : "")">
                <svg class="w-6 h-6 @(hasBackground ? "text-white" : "text-primary-600 dark:text-white") mx-auto @(animationEnabled ? "transition-colors duration-300" : "")"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </div>
        </div>
    }

</section>

@* 自定义动画CSS *@
@if (animationEnabled || parallaxEnabled)
{
    <style>
        @if (animationEnabled)
        {
        <text>
        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .animate-fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
        }
        </text>
        }

        @if (parallaxEnabled)
        {
        <text>
        [data-parallax="true"] {
            overflow: hidden;
        }
        .parallax-background {
            will-change: transform;
            transform-style: preserve-3d;
            backface-visibility: hidden;
            min-height: 120%;
            min-width: 100%;
            position: relative;
        }
        @@media (prefers-reduced-motion: reduce) {
            .parallax-background {
                transform: none !important;
            }
        }
        @@media (max-width: 768px) {
            [data-parallax="true"] .parallax-background {
                transform: none !important;
                top: 0;
                min-height: 100%;
            }
        }
        </text>
        }
    </style>
}