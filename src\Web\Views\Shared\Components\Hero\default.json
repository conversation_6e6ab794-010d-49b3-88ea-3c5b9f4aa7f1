{"ComponentId": "Hero", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "默认主视觉", "en": "Default <PERSON>", "ja": "デフォルトヒーロー"}, "Descriptions": {"zh": "标准主视觉区域布局", "en": "Standard hero section layout", "ja": "標準ヒーローセクションレイアウト"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@SharedResource:FormFields_TitleHelpText"}, "validation": {"required": true, "maxLength": 100, "minLength": 1}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@SharedResource:FormFields_Subtitle", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true, "helpText": "@SharedResource:FormFields_SubtitleHelpText"}, "validation": {"maxLength": 200}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 3, "rows": 2, "layout": "inline", "collapsed": true, "helpText": "@SharedResource:FormFields_DescriptionHelpText"}, "validation": {"maxLength": 500}}, {"name": "BackgroundImage", "type": "image", "label": "@SharedResource:FormFields_BackgroundImage", "display": {"group": "@SharedResource:FormGroups_MediaContent", "width": "col-span-12", "order": 1, "collapsed": true, "helpText": "@SharedResource:FormFields_BackgroundImageHelpText"}, "fileConfig": {"folder": "hero", "types": ["image/*"], "maxSize": "5MB", "multiple": false, "preview": true}}, {"name": "BackgroundVideo", "type": "video", "label": "@SharedResource:FormFields_BackgroundVideo", "display": {"group": "@SharedResource:FormGroups_MediaContent", "width": "col-span-12", "order": 2, "collapsed": true, "helpText": "@FormResource:BackgroundVideoHelpText"}, "fileConfig": {"folder": "hero/videos", "types": ["video/mp4", "video/webm"], "maxSize": "50MB", "multiple": false, "preview": true}}, {"name": "PrimaryButtonText", "type": "multilingual-text", "label": "@FormResource:PrimaryButtonText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 1, "layout": "inline", "helpText": "@FormResource:PrimaryButtonTextHelpText"}, "validation": {"maxLength": 50}}, {"name": "PrimaryButtonUrl", "type": "text", "label": "@FormResource:PrimaryButtonLink", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 2, "helpText": "@FormResource:PrimaryButtonLinkHelpText"}, "validation": {"pattern": "^(https?://|/)"}}, {"name": "SecondaryButtonText", "type": "multilingual-text", "label": "@FormResource:SecondaryButtonText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 3, "layout": "inline", "helpText": "@FormResource:SecondaryButtonTextHelpText"}, "validation": {"maxLength": 50}}, {"name": "SecondaryButtonUrl", "type": "text", "label": "@FormResource:SecondaryButtonLink", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 4, "collapsed": true, "helpText": "@FormResource:SecondaryButtonLinkHelpText"}, "validation": {"pattern": "^(https?://|/)"}}, {"name": "TextAlignment", "type": "select", "label": "@FormResource:TextAlignment", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:TextAlignmentHelpText"}, "options": [{"value": "left", "label": "@FormResource:AlignLeft"}, {"value": "center", "label": "@FormResource:AlignCenter"}, {"value": "right", "label": "@FormResource:AlignRight"}], "defaultValue": "center"}, {"name": "Height", "type": "select", "label": "@FormResource:SectionHeight", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 2, "layout": "inline", "collapsed": true, "helpText": "@FormResource:SectionHeightHelpText"}, "options": [{"value": "auto", "label": "@FormResource:AutoHeight"}, {"value": "screen", "label": "@FormResource:FullScreenHeight"}, {"value": "large", "label": "@FormResource:LargeSize"}, {"value": "medium", "label": "@FormResource:MediumSize"}], "defaultValue": "large"}, {"name": "OverlayOpacity", "type": "number", "label": "@FormResource:OverlayOpacity", "display": {"layout": "inline", "group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 3, "collapsed": true, "helpText": "@FormResource:OverlayOpacityHelpText"}, "validation": {"min": 0, "max": 100}, "defaultValue": 40}, {"name": "ShowOverlay", "type": "checkbox", "label": "@FormResource:ShowOverlay", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 4, "layout": "inline", "collapsed": true, "helpText": "@FormResource:ShowOverlayHelpText"}, "defaultValue": true}, {"name": "AnimationEnabled", "type": "checkbox", "label": "@FormResource:EnableAnimation", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 5, "layout": "inline", "collapsed": true, "helpText": "@FormResource:EnableAnimationHelpText"}, "defaultValue": true}, {"name": "ParallaxEnabled", "type": "checkbox", "label": "@FormResource:EnableParallax", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "layout": "inline", "order": 6, "collapsed": true, "helpText": "@FormResource:EnableParallaxHelpText"}, "defaultValue": false}, {"name": "ShowScrollIndicator", "type": "checkbox", "label": "@FormResource:ShowScrollIndicator", "display": {"group": "@SharedResource:FormGroups_LayoutSettings", "width": "col-span-4", "order": 7, "layout": "inline", "collapsed": true, "helpText": "@FormResource:ShowScrollIndicatorHelpText"}, "defaultValue": false}]}