@model MlSoft.Sites.Web.ViewModels.Components.HowToDoComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract data from ViewModel with null-safe defaults
    var steps = Model?.Steps ?? new List<ProcessStep>();
    var title = string.IsNullOrEmpty(Model?.TitleText) ? SharedRes["HowToDo_Title"] : Model?.TitleText;
    var description = Model?.Description;

    var columnsDesktop = Math.Min(Model?.ColumnsDesktop ?? 3, Math.Max(1, steps.Count));
    var columnsTablet = Math.Min(Model?.ColumnsTablet ?? 2, Math.Max(1, steps.Count));
    var columnsMobile = Model?.ColumnsMobile ?? 1;

    var showStepNumbers = Model?.ShowStepNumbers ?? true;
    var numberStyle = Model?.NumberStyle ?? "circle";
    var backgroundStyle = Model?.BackgroundStyle ?? "light";

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("how-to-do-cards");

    // Generate grid classes
    var gridClass = $"grid gap-6 md:grid-cols-{columnsTablet} lg:grid-cols-{columnsDesktop}";

    // Background classes
    var backgroundClass = backgroundStyle switch
    {
        "dark" => "bg-gray-900 dark:bg-gray-950",
        "gradient" => "bg-gradient-to-br from-primary-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",
        _ => "bg-white dark:bg-gray-800"
    };

    var textColorClass = backgroundStyle == "dark" ? "text-white" : "text-gray-900 dark:text-white";
    var subtextColorClass = backgroundStyle == "dark" ? "text-gray-300" : "text-gray-600 dark:text-gray-300";

    // Number style classes
    var numberStyleClass = numberStyle switch
    {
        "square" => "bg-primary-600 text-white rounded-lg",
        "plain" => "text-primary-600 font-bold border-2 border-primary-600 rounded-lg",
        _ => "bg-primary-600 text-white rounded-full"
    };
}

<section id="@uniqueId" class="py-16 lg:py-20 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        <!-- Header Section -->
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold @textColorClass mb-4">
                @title
            </h2>

            @if (!string.IsNullOrEmpty(description))
            {
                <p class="text-lg @subtextColorClass max-w-2xl mx-auto leading-relaxed">
                    @description
                </p>
            }
        </div>

        <!-- Steps Cards -->
        @if (steps.Count > 0)
        {
            <div class="@gridClass max-w-5xl mx-auto">
                @foreach (var step in steps.OrderBy(s => s.Order))
                {
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-6 text-center group">

                        <!-- Step Number -->
                        @if (showStepNumbers)
                        {
                            <div class="w-16 h-16 @numberStyleClass flex items-center justify-center text-xl font-bold mx-auto mb-4">
                                @(step.StepNumber ?? step.Order.ToString("00"))
                            </div>
                        }

                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">
                            @step.Title
                        </h3>

                        <p class="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                            @step.Description
                        </p>

                        @if (!string.IsNullOrEmpty(step.ActionText) && !string.IsNullOrEmpty(step.ActionUrl))
                        {
                            <div class="mt-4">
                                <a href="@step.ActionUrl"
                                   class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm transition-colors">
                                    @step.ActionText →
                                </a>
                            </div>
                        }

                        <!-- Step connector for desktop -->
                        @if (step.Order < steps.Count)
                        {
                            <div class="hidden lg:block absolute -right-3 top-1/2 transform -translate-y-1/2 text-primary-400 dark:text-primary-600">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-12">
                <i class="fas fa-list-ol text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium @textColorClass mb-2">
                    @SharedRes["NoStepsAvailable"]
                </h3>
                <p class="@subtextColorClass">
                    @SharedRes["NoStepsDescription"]
                </p>
            </div>
        }
    </div>
</section>