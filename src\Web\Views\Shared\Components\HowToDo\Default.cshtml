@model MlSoft.Sites.Web.ViewModels.Components.HowToDoComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract data from ViewModel with null-safe defaults
    var steps = Model?.Steps ?? new List<ProcessStep>();
    var title = string.IsNullOrEmpty(Model?.TitleText) ? SharedRes["HowToDo_Title"] : Model?.TitleText;
    var subtitle = string.IsNullOrEmpty(Model?.SubtitleText) ? SharedRes["HowToDo_Subtitle"] : Model?.SubtitleText;
    var description = Model?.Description;

    var layout = Model?.Layout ?? "grid";
    var columnsDesktop = Math.Min(Model?.ColumnsDesktop ?? 2, Math.Max(1, steps.Count));
    var columnsTablet = Math.Min(Model?.ColumnsTablet ?? 2, Math.Max(1, steps.Count));
    var columnsMobile = Model?.ColumnsMobile ?? 1;

    var showStepNumbers = Model?.ShowStepNumbers ?? true;
    var showStepIcons = Model?.ShowStepIcons ?? false;
    var showConnectors = Model?.ShowConnectors ?? true;
    var numberStyle = Model?.NumberStyle ?? "circle";
    var animationEnabled = Model?.AnimationEnabled ?? true;
    var backgroundStyle = Model?.BackgroundStyle ?? "light";

    var showCtaButton = Model?.ShowCtaButton ?? false;
    var ctaButtonText = Model?.CtaButtonText ?? SharedRes["GetStarted"];
    var ctaButtonUrl = Model?.CtaButtonUrl ?? "#";

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("how-to-do");

    // Generate grid classes
    var gridClass = layout == "timeline" ? "space-y-8" : $"grid gap-8 md:grid-cols-{columnsTablet} lg:grid-cols-{columnsDesktop}";
    if (layout == "list") gridClass = "space-y-6";

    // Background classes
    var backgroundClass = backgroundStyle switch
    {
        "dark" => "bg-gray-900 dark:bg-gray-950",
        "gradient" => "bg-gradient-to-br from-primary-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",
        _ => "bg-gray-50 dark:bg-gray-900"
    };

    var textColorClass = backgroundStyle == "dark" ? "text-white" : "text-gray-900 dark:text-white";
    var subtextColorClass = backgroundStyle == "dark" ? "text-gray-300" : "text-gray-600 dark:text-gray-300";

    // Number style classes
    var numberStyleClass = numberStyle switch
    {
        "square" => "bg-primary-600 text-white rounded-md",
        "plain" => "text-primary-600 font-bold",
        _ => "bg-primary-600 text-white rounded-full"
    };

    // File path processing helper
    string ProcessFilePath(string filePath) =>
        string.IsNullOrEmpty(filePath) ? "" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-16 lg:py-24 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        <!-- Header Section -->
        <div class="text-center mb-12">
            @if (!string.IsNullOrEmpty(subtitle))
            {
                <p class="text-sm font-semibold uppercase tracking-wider @subtextColorClass mb-2">
                    @subtitle
                </p>
            }

            <h2 class="text-3xl lg:text-4xl font-bold @textColorClass mb-4">
                @title
            </h2>

            @if (!string.IsNullOrEmpty(description))
            {
                <p class="text-lg @subtextColorClass max-w-3xl mx-auto leading-relaxed">
                    @description
                </p>
            }
        </div>

        <!-- Steps Content -->
        @if (steps.Count > 0)
        {
            <div class="max-w-6xl mx-auto">
                @if (layout == "timeline")
                {
                    <!-- Timeline Layout -->
                    <div class="relative">
                        <!-- Timeline line -->
                        @if (showConnectors && steps.Count > 1)
                        {
                            <div class="absolute left-8 top-16 bottom-16 w-0.5 bg-primary-200 dark:bg-primary-800 hidden md:block"></div>
                        }

                        <div class="@gridClass">
                            @foreach (var step in steps.OrderBy(s => s.Order))
                            {
                                <div class="relative flex items-start gap-6 @(animationEnabled ? "opacity-0 animate-fade-in-up" : "")"
                                     style="@(animationEnabled ? $"animation-delay: {step.Order * 100}ms" : "")">

                                    <!-- Step Number/Icon -->
                                    <div class="flex-shrink-0 z-10">
                                        @if (showStepNumbers)
                                        {
                                            <div class="w-16 h-16 @numberStyleClass flex items-center justify-center text-lg font-bold shadow-lg">
                                                @(step.StepNumber ?? step.Order.ToString("00"))
                                            </div>
                                        }
                                        else if (showStepIcons && !string.IsNullOrEmpty(step.Icon))
                                        {
                                            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                                <i class="fas <EMAIL> text-2xl text-primary-600 dark:text-primary-400"></i>
                                            </div>
                                        }
                                    </div>

                                    <!-- Step Content -->
                                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 flex-1 @(step.IsHighlighted ? "ring-2 ring-primary-500" : "")">
                                        @if (!string.IsNullOrEmpty(step.Image))
                                        {
                                            <div class="mb-4">
                                                <img src="@ProcessFilePath(step.Image)"
                                                     alt="@step.Title"
                                                     class="w-full h-48 object-cover rounded-lg"
                                                     loading="lazy" />
                                            </div>
                                        }

                                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                            @step.Title
                                        </h3>

                                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                                            @step.Description
                                        </p>

                                        @if (!string.IsNullOrEmpty(step.DetailText))
                                        {
                                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                                @step.DetailText
                                            </p>
                                        }

                                        @if (!string.IsNullOrEmpty(step.ActionText) && !string.IsNullOrEmpty(step.ActionUrl))
                                        {
                                            <a href="@step.ActionUrl"
                                               class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">
                                                @step.ActionText
                                                <i class="fas fa-arrow-right ml-2"></i>
                                            </a>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
                else
                {
                    <!-- Grid/List Layout -->
                    <div class="@gridClass">
                        @foreach (var step in steps.OrderBy(s => s.Order))
                        {
                            <div class="relative @(animationEnabled ? "opacity-0 animate-fade-in-up" : "")"
                                 style="@(animationEnabled ? $"animation-delay: {step.Order * 100}ms" : "")">

                                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 h-full group @(step.IsHighlighted ? "ring-2 ring-primary-500" : "")">

                                    <!-- Step Header -->
                                    <div class="flex items-center mb-4">
                                        @if (showStepNumbers)
                                        {
                                            <div class="w-12 h-12 @numberStyleClass flex items-center justify-center text-lg font-bold mr-4 flex-shrink-0">
                                                @(step.StepNumber ?? step.Order.ToString("00"))
                                            </div>
                                        }
                                        else if (showStepIcons && !string.IsNullOrEmpty(step.Icon))
                                        {
                                            <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                                <i class="fas <EMAIL> text-xl text-primary-600 dark:text-primary-400"></i>
                                            </div>
                                        }

                                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                                            @step.Title
                                        </h3>
                                    </div>

                                    @if (!string.IsNullOrEmpty(step.Image))
                                    {
                                        <div class="mb-4">
                                            <img src="@ProcessFilePath(step.Image)"
                                                 alt="@step.Title"
                                                 class="w-full h-48 object-cover rounded-lg"
                                                 loading="lazy" />
                                        </div>
                                    }

                                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                                        @step.Description
                                    </p>

                                    @if (!string.IsNullOrEmpty(step.DetailText))
                                    {
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                            @step.DetailText
                                        </p>
                                    }

                                    @if (!string.IsNullOrEmpty(step.ActionText) && !string.IsNullOrEmpty(step.ActionUrl))
                                    {
                                        <div class="mt-auto pt-4">
                                            <a href="@step.ActionUrl"
                                               class="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors group-hover:translate-x-1 transition-transform">
                                                @step.ActionText
                                                <i class="fas fa-arrow-right ml-2"></i>
                                            </a>
                                        </div>
                                    }

                                    <!-- Connector for grid layout -->
                                    @if (showConnectors && layout == "grid" && step.Order < steps.Count)
                                    {
                                        <div class="hidden lg:block absolute -right-4 top-1/2 transform -translate-y-1/2 text-primary-400 dark:text-primary-600">
                                            <i class="fas fa-arrow-right text-xl"></i>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                }

                <!-- CTA Section -->
                @if (showCtaButton)
                {
                    <div class="text-center mt-12">
                        <a href="@ctaButtonUrl"
                           class="inline-flex items-center px-8 py-3 text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-300 hover:shadow-lg">
                            @ctaButtonText
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                }
            </div>
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-12">
                <i class="fas fa-list-ol text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium @textColorClass mb-2">
                    @SharedRes["NoStepsAvailable"]
                </h3>
                <p class="@subtextColorClass">
                    @SharedRes["NoStepsDescription"]
                </p>
            </div>
        }
    </div>
</section>

@if (animationEnabled)
{
    <style>
    .animate-fade-in-up {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s ease-out forwards;
    }

    @@keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    </style>
}