@model MlSoft.Sites.Web.ViewModels.Components.MediaComponentViewModel

<section class="media-component">
    <div class="container">
        @if (!string.IsNullOrEmpty(Model.Title) || !string.IsNullOrEmpty(Model.Description))
        {
            <div class="media-header">
                @if (!string.IsNullOrEmpty(Model.Title))
                {
                    <h2 class="media-title">@Model.Title</h2>
                }
                @if (!string.IsNullOrEmpty(Model.Description))
                {
                    <p class="media-description">@Model.Description</p>
                }
            </div>
        }

        @if (Model.MediaItems.Any())
        {
            <div class="media-container <EMAIL>">
                @foreach (var item in Model.MediaItems)
                {
                    <div class="media-item <EMAIL>">
                        @switch (item.Type)
                        {
                            case "video":
                                <div class="video-wrapper">
                                    <video class="media-video" 
                                           @(Model.ShowControls ? "controls" : "")
                                           @(Model.AutoPlay ? "autoplay muted" : "")
                                           @(item.Width.HasValue ? $"width=\"{item.Width}\"" : "")
                                           @(item.Height.HasValue ? $"height=\"{item.Height}\"" : "")>
                                        <source src="@item.Url" type="video/mp4">
                                        お使いのブラウザは動画タグをサポートしていません。
                                    </video>
                                    @if (!string.IsNullOrEmpty(item.Caption))
                                    {
                                        <div class="media-caption">@item.Caption</div>
                                    }
                                </div>
                                break;

                            case "audio":
                                <div class="audio-wrapper">
                                    <audio class="media-audio" 
                                           @(Model.ShowControls ? "controls" : "")
                                           @(Model.AutoPlay ? "autoplay" : "")>
                                        <source src="@item.Url" type="audio/mpeg">
                                        お使いのブラウザは音声タグをサポートしていません。
                                    </audio>
                                    @if (!string.IsNullOrEmpty(item.Title))
                                    {
                                        <div class="audio-title">@item.Title</div>
                                    }
                                    @if (!string.IsNullOrEmpty(item.Description))
                                    {
                                        <div class="audio-description">@item.Description</div>
                                    }
                                </div>
                                break;

                            default: // image
                                <div class="image-wrapper">
                                    <img src="@item.Url" 
                                         alt="@(!string.IsNullOrEmpty(item.Alt) ? item.Alt : item.Title)"
                                         class="media-image"
                                         @(item.Width.HasValue ? $"width=\"{item.Width}\"" : "")
                                         @(item.Height.HasValue ? $"height=\"{item.Height}\"" : "") />
                                    
                                    @if (!string.IsNullOrEmpty(item.Title) || !string.IsNullOrEmpty(item.Caption))
                                    {
                                        <div class="image-info">
                                            @if (!string.IsNullOrEmpty(item.Title))
                                            {
                                                <div class="image-title">@item.Title</div>
                                            }
                                            @if (!string.IsNullOrEmpty(item.Caption))
                                            {
                                                <div class="image-caption">@item.Caption</div>
                                            }
                                        </div>
                                    }
                                </div>
                                break;
                        }

                        @if (!string.IsNullOrEmpty(item.Description) && item.Type == "image")
                        {
                            <div class="media-description">@item.Description</div>
                        }
                    </div>
                }
            </div>

            @if (Model.Layout == "carousel" && Model.ShowThumbnails)
            {
                <div class="media-thumbnails">
                    @foreach (var item in Model.MediaItems.Where(i => !string.IsNullOrEmpty(i.ThumbnailUrl)))
                    {
                        <div class="thumbnail-item">
                            <img src="@item.ThumbnailUrl" alt="@item.Title" class="thumbnail-image" />
                        </div>
                    }
                </div>
            }
        }
    </div>
</section>