@model MlSoft.Sites.Web.ViewModels.Components.MessageComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Web.Resources
@inject SharedResource SharedRes
@inject AdminResource AdminRes

@{
    // 获取当前语言
    var currentLanguage = ViewData["CurrentLanguage"]?.ToString() ?? ViewData["DefaultLanguage"]?.ToString() ?? "en";

    // 使用 Model 的配置属性，提供默认值
    var title = string.IsNullOrEmpty(Model?.Title) ? SharedRes["FormFields_Title"] : Model?.Title;
    var subtitle = string.IsNullOrEmpty(Model?.Subtitle) ? "" : Model?.Subtitle;
    var description = string.IsNullOrEmpty(Model?.Description) ? "" : Model?.Description;
    var showTitle = Model?.ShowTitle ?? true;
    var showSubtitle = Model?.ShowSubtitle ?? false;
    var showDescription = Model?.ShowDescription ?? true;
    var textAlignment = string.IsNullOrEmpty(Model?.TextAlignment) ? "center" : Model?.TextAlignment;
    var backgroundColor = string.IsNullOrEmpty(Model?.BackgroundColor) ? "gray-50" : Model?.BackgroundColor;
    var formWidth = string.IsNullOrEmpty(Model?.FormWidth) ? "large" : Model?.FormWidth;
    var submitButtonText = string.IsNullOrEmpty(Model?.SubmitButtonText) ? SharedRes["Contact_Submit"] : Model?.SubmitButtonText;
    var requiredFieldsNote = string.IsNullOrEmpty(Model?.RequiredFieldsNote) ? SharedRes["Contact_RequiredFieldsNote"] : Model?.RequiredFieldsNote;
    var privacyNotice = string.IsNullOrEmpty(Model?.PrivacyNotice) ? SharedRes["Contact_PrivacyNotice"] : Model?.PrivacyNotice;

    // 生成CSS类
    var textAlignClass = JObjectHelper.GetTextAlignmentClass(textAlignment);
    var backgroundClass = $"bg-{backgroundColor}";
    var formWidthClass = formWidth switch
    {
        "small" => "max-w-md",
        "medium" => "max-w-2xl",
        "large" => "max-w-4xl",
        "full" => "max-w-full",
        _ => "max-w-2xl"
    };

    var uniqueId = JObjectHelper.GenerateId("message");
}

<section class="message-section @backgroundClass dark:bg-gray-900 py-16" id="@uniqueId">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        @* 标题区域 *@
        @if (showTitle || showSubtitle || showDescription)
        {
            <div class="@textAlignClass mb-12">
                @if (showTitle && !string.IsNullOrEmpty(title))
                {
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        @title
                    </h2>
                }

                @if (showSubtitle && !string.IsNullOrEmpty(subtitle))
                {
                    <h3 class="text-xl text-gray-700 dark:text-gray-300 mb-6">
                        @subtitle
                    </h3>
                }

                @if (showDescription && !string.IsNullOrEmpty(description))
                {
                    <p class="text-gray-600 dark:text-gray-400 @(textAlignment == "center" ? "mx-auto" : "") max-w-3xl">
                        @description
                    </p>
                }
            </div>
        }

        @* 联系表单 *@
        <div class="@formWidthClass mx-auto">
            <form id="contact-form" method="post" action="@Html.MultilingualUrl("Submit","Contact",area:"")" class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8">
                @Html.AntiForgeryToken()

                @* 必填项提示 *@
                <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
                    @requiredFieldsNote
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @* 姓名 *@
                    <div class="md:col-span-1">
                        <label for="contactName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @SharedRes["Contact_Name"] <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="contactName"
                               name="ContactName"
                               required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                               placeholder="@SharedRes["Contact_NamePlaceholder"]">
                    </div>

                    @* 邮箱 *@
                    <div class="md:col-span-1">
                        <label for="contactEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @SharedRes["Contact_Email"] <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="contactEmail"
                               name="ContactEmail"
                               required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                               placeholder="@SharedRes["Contact_EmailPlaceholder"]">
                    </div>

                    @* 电话 *@
                    <div class="md:col-span-1">
                        <label for="contactPhone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @SharedRes["Contact_Phone"]
                        </label>
                        <input type="tel"
                               id="contactPhone"
                               name="ContactPhone"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                               placeholder="@SharedRes["Contact_PhonePlaceholder"]">
                    </div>

                    @* 公司名 *@
                    <div class="md:col-span-1">
                        <label for="companyName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @SharedRes["Contact_Company"]
                        </label>
                        <input type="text"
                               id="companyName"
                               name="CompanyName"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                               placeholder="@SharedRes["Contact_CompanyPlaceholder"]">
                    </div>

                    @* 职位 *@
                    <div class="md:col-span-1">
                        <label for="jobTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @SharedRes["Contact_Position"]
                        </label>
                        <input type="text"
                               id="jobTitle"
                               name="JobTitle"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                               placeholder="@SharedRes["Contact_PositionPlaceholder"]">
                    </div>

                    @* 询问类型 *@
                    <div class="md:col-span-1">
                        <label for="messageType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @SharedRes["Contact_MessageType"] <span class="text-red-500">*</span>
                        </label>
                        <select id="messageType"
                                name="Type"
                                required
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200">
                            <option value="">@SharedRes["Contact_PleaseSelect"]</option>
                            <option value="0">@AdminRes["GeneralInquiry"]</option>
                            <option value="1">@AdminRes["ProductInquiry"]</option>
                            <option value="2">@AdminRes["ServiceInquiry"]</option>
                            <option value="3">@AdminRes["TechnicalSupport"]</option>
                            <option value="4">@AdminRes["BusinessCooperation"]</option>
                            <option value="5">@AdminRes["PartnershipInquiry"]</option>
                            <option value="6">@AdminRes["MediaInquiry"]</option>
                            <option value="7">@AdminRes["CareerInquiry"]</option>
                            <option value="8">@AdminRes["Complaint"]</option>
                            <option value="9">@AdminRes["Other"]</option>
                        </select>
                    </div>
                </div>

                @* 留言内容 *@
                <div class="mt-6">
                    <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        @SharedRes["Contact_MessageContent"] <span class="text-red-500">*</span>
                    </label>
                    <textarea id="message"
                              name="Message"
                              rows="6"
                              required
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200 resize-vertical"
                              placeholder="@SharedRes["Contact_MessagePlaceholder"]"></textarea>
                </div>

                @* 隐藏字段 *@
                <input type="hidden" name="SourcePage" value="@Context.Request.Path">
                <input type="hidden" name="ReferrerUrl" value="@Context.Request.Headers.Referer">

                @* 隐私声明 *@
                <div class="mt-6 text-sm text-gray-600 dark:text-gray-400">
                    @privacyNotice
                </div>

                @* 提交按钮 *@
                <div class="mt-8 flex justify-center">
                    <button type="submit"
                            class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-md shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                        @submitButtonText
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

@* 表单提交脚本 *@
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('contact-form');
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // 禁用提交按钮，防止重复提交
            submitButton.disabled = true;
            submitButton.textContent = '@Html.Raw(SharedRes["Contact_Submitting"])';

            const formData = new FormData(form);

            fetch("@Html.MultilingualUrl("Submit", "Contact", area: "")", {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 成功提示 - 使用主题颜色
                    form.innerHTML = `
                        <div class="text-center py-12">
                            <div class="text-primary-600 dark:text-primary-400 text-4xl mb-4">✓</div>
                            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">@SharedRes["Contact_SubmitSuccess"]</h3>
                            <p class="text-gray-600 dark:text-gray-400">@SharedRes["Contact_SubmitSuccessMessage"]</p>
                        </div>
                    `;
                } else {
                    Dialog.error(data.message || '@SharedRes["Contact_SubmitError"]');
                           // 恢复提交按钮
                    submitButton.disabled = false;
                    submitButton.textContent = originalButtonText;
                }
            })
            .catch(error => {
             

                // 使用Dialog系统显示错误
                if (typeof Dialog !== 'undefined' && Dialog.error) {
                    Dialog.error(data.message || '@SharedRes["Contact_SubmitError"]');
                } else {
                    alert('@SharedRes["Contact_SubmitError"]');
                }

                // 恢复提交按钮
                submitButton.disabled = false;
                submitButton.textContent = originalButtonText;
            });
        });
    });
</script>