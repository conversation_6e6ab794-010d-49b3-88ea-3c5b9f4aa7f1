@model MlSoft.Sites.Web.ViewModels.Components.NewsListComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var title = string.IsNullOrEmpty(Model?.Title) ? SharedRes["NewsList_Title"] : Model?.Title;
    var description = string.IsNullOrEmpty(Model?.Description) ? SharedRes["NewsList_Description"] : Model?.Description;
    var backgroundClass = Model?.BackgroundColor == "muted" ? "bg-gray-50 dark:bg-gray-900/50" : "bg-white dark:bg-gray-800";
    var layout = Model?.Layout ?? "list";
    var gridColumns = Model?.GridColumns ?? 2;
    var showImages = Model?.ShowImages ?? true;
    var showCategories = Model?.ShowCategories ?? true;
    var showDates = Model?.ShowDates ?? true;
    var showExcerpts = Model?.ShowExcerpts ?? true;
    var animationEnabled = Model?.AnimationEnabled ?? true;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("news-list");

    // Get category badge class
    string GetCategoryBadgeClass(string? categoryColor) => categoryColor?.ToLower() switch
    {
        "primary" => "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300",
        "secondary" => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
        "success" => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
        "warning" => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
        "info" => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
        _ => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
    };

    // Get grid layout classes
    string GetGridClass(int columns) => columns switch
    {
        1 => "grid-cols-1",
        2 => "grid-cols-1 md:grid-cols-2",
        3 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
        4 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
        _ => "grid-cols-1 md:grid-cols-2"
    };

    var gridClass = GetGridClass(gridColumns);
    var animationClass = animationEnabled ? "animate-fade-in-up" : "";
}

<section id="@uniqueId" class="py-8 lg:py-12 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        @if (!string.IsNullOrEmpty(title) || !string.IsNullOrEmpty(description))
        {
            <div class="text-center mb-8">
                @if (!string.IsNullOrEmpty(title))
                {
                    <h2 class="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-4">@title</h2>
                }
                @if (!string.IsNullOrEmpty(description))
                {
                    <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">@description</p>
                }
            </div>
        }

        @if (Model?.NewsItems?.Any() == true)
        {
            @if (layout == "grid")
            {
                <!-- Grid Layout -->
                <div class="grid @gridClass gap-6">
                    @foreach (var item in Model.NewsItems)
                    {
                        <article class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden @animationClass">
                            @if (showImages && !string.IsNullOrEmpty(item.ThumbnailUrl))
                            {
                                <div class="aspect-w-16 aspect-h-9">
                                    <img src="@item.ThumbnailUrl" alt="@item.Title" class="w-full h-48 object-cover" loading="lazy" />
                                </div>
                            }
                            <div class="p-6">
                                <div class="flex items-center gap-3 mb-3">
                                    @if (showDates && !string.IsNullOrEmpty(item.Date))
                                    {
                                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 8h12v8H4V8z"/>
                                            </svg>
                                            @item.Date
                                        </div>
                                    }
                                    @if (showCategories && !string.IsNullOrEmpty(item.Category))
                                    {
                                        <span class="@GetCategoryBadgeClass(item.CategoryColor) text-xs font-medium px-2.5 py-0.5 rounded">
                                            @item.Category
                                        </span>
                                    }
                                    @if (item.IsFeatured)
                                    {
                                        <span class="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 text-xs font-medium px-2.5 py-0.5 rounded">
                                            @SharedRes["Featured"]
                                        </span>
                                    }
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                    @if (!string.IsNullOrEmpty(item.Url))
                                    {
                                        <a href="@item.Url" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">@item.Title</a>
                                    }
                                    else
                                    {
                                        @item.Title
                                    }
                                </h3>
                                @if (showExcerpts && !string.IsNullOrEmpty(item.Excerpt))
                                {
                                    <p class="text-gray-600 dark:text-gray-300 text-sm leading-relaxed line-clamp-3 mb-3">@item.Excerpt</p>
                                }
                                @if (item.ViewCount > 0)
                                {
                                    <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                        </svg>
                                        @item.ViewCount @SharedRes["Views"]
                                    </div>
                                }
                            </div>
                        </article>
                    }
                </div>
            }
            else
            {
                <!-- List Layout -->
                <div class="space-y-6">
                    @foreach (var item in Model.NewsItems)
                    {
                        <article class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 @animationClass">
                            <div class="p-6">
                                <div class="flex flex-col md:flex-row gap-6">
                                    @if (showImages && !string.IsNullOrEmpty(item.ThumbnailUrl))
                                    {
                                        <div class="md:w-48 flex-shrink-0">
                                            <img src="@item.ThumbnailUrl" alt="@item.Title" class="w-full h-32 md:h-24 object-cover rounded-lg" loading="lazy" />
                                        </div>
                                    }
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-3">
                                            @if (showDates && !string.IsNullOrEmpty(item.Date))
                                            {
                                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 8h12v8H4V8z"/>
                                                    </svg>
                                                    @item.Date
                                                </div>
                                            }
                                            @if (showCategories && !string.IsNullOrEmpty(item.Category))
                                            {
                                                <span class="@GetCategoryBadgeClass(item.CategoryColor) text-xs font-medium px-2.5 py-0.5 rounded">
                                                    @item.Category
                                                </span>
                                            }
                                            @if (item.IsFeatured)
                                            {
                                                <span class="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 text-xs font-medium px-2.5 py-0.5 rounded">
                                                    @SharedRes["Featured"]
                                                </span>
                                            }
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            @if (!string.IsNullOrEmpty(item.Url))
                                            {
                                                <a href="@item.Url" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">@item.Title</a>
                                            }
                                            else
                                            {
                                                @item.Title
                                            }
                                        </h3>
                                        @if (showExcerpts && !string.IsNullOrEmpty(item.Excerpt))
                                        {
                                            <p class="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-3">@item.Excerpt</p>
                                        }
                                        @if (item.ViewCount > 0)
                                        {
                                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                                </svg>
                                                @item.ViewCount @SharedRes["Views"]
                                            </div>
                                        }
                                    </div>
                                    @if (!string.IsNullOrEmpty(item.Url))
                                    {
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                            </svg>
                                        </div>
                                    }
                                </div>
                            </div>
                        </article>
                    }
                </div>
            }
        }
        else
        {
            <div class="text-center py-12">
                <div class="text-gray-500 dark:text-gray-400">
                    <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                    </svg>
                    <p class="text-lg">@SharedRes["NoNewsAvailable"]</p>
                </div>
            </div>
        }
    </div>
</section>

@if (animationEnabled)
{
    <style>
        .animate-fade-in-up {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        .animate-fade-in-up:nth-child(1) { animation-delay: 0.1s; }
        .animate-fade-in-up:nth-child(2) { animation-delay: 0.2s; }
        .animate-fade-in-up:nth-child(3) { animation-delay: 0.3s; }
        .animate-fade-in-up:nth-child(4) { animation-delay: 0.4s; }
        .animate-fade-in-up:nth-child(5) { animation-delay: 0.5s; }
        
        @@keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
}
