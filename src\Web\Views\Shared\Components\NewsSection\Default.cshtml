@model MlSoft.Sites.Web.ViewModels.Components.NewsSectionComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var title = string.IsNullOrEmpty(Model?.Title) ? SharedRes["NewsSection_Title"] : Model?.Title;
    var description = string.IsNullOrEmpty(Model?.Description) ? SharedRes["NewsSection_Description"] : Model?.Description;
    var backgroundClass = Model?.BackgroundColor == "muted" ? "bg-gray-50 dark:bg-gray-900/50" : "bg-white dark:bg-gray-800";
    var maxItems = Model?.MaxItems ?? 4;
    var showViewAllButton = Model?.ShowViewAllButton ?? true;
    var showCategories = Model?.ShowCategories ?? true;
    var showDates = Model?.ShowDates ?? true;
    var showExcerpts = Model?.ShowExcerpts ?? true;
    var viewAllButtonText = string.IsNullOrEmpty(Model?.ViewAllButtonText) ? SharedRes["Button_ViewAll"] : Model?.ViewAllButtonText;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("news-section");

    // Get category badge class
    string GetCategoryBadgeClass(string? categoryColor) => categoryColor?.ToLower() switch
    {
        "primary" => "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300",
        "secondary" => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
        "success" => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
        "warning" => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
        "danger" => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
        "info" => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
        _ => "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
    };

    // Take only the specified number of items
    var displayItems = Model?.NewsItems?.Take(maxItems).ToList() ?? new List<NewsItem>();
}

<section id="@uniqueId" class="py-16 lg:py-24 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        <div class="flex items-center justify-between mb-12">
            <div>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">@title</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">@description</p>
            </div>
            @if (showViewAllButton)
            {
                <div class="hidden md:flex">
                    @if (!string.IsNullOrEmpty(Model?.ViewAllButtonUrl))
                    {
                        <a href="@Model.ViewAllButtonUrl" class="text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900">
                            @viewAllButtonText
                            <svg class="w-4 h-4 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                            </svg>
                        </a>
                    }
                    else
                    {
                        <button type="button" class="text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900">
                            @viewAllButtonText
                            <svg class="w-4 h-4 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                            </svg>
                        </button>
                    }
                </div>
            }
        </div>

        @if (displayItems.Any())
        {
            <div class="grid gap-6">
                @foreach (var item in displayItems)
                {
                    var newsItemClass = !string.IsNullOrEmpty(item.Url) ? "hover:shadow-md transition-shadow cursor-pointer" : "";
                    
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow @newsItemClass">
                        <div class="p-6">
                            <div class="flex flex-col md:flex-row md:items-center gap-4">
                                <div class="flex items-center gap-4 md:w-64 flex-shrink-0">
                                    @if (showDates && !string.IsNullOrEmpty(item.Date))
                                    {
                                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                            <svg class="w-4 h-4 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                                            </svg>
                                            @item.Date
                                        </div>
                                    }
                                    @if (showCategories && !string.IsNullOrEmpty(item.Category))
                                    {
                                        <span class="@GetCategoryBadgeClass(item.CategoryColor) text-xs font-medium px-2.5 py-0.5 rounded">
                                            @item.Category
                                        </span>
                                    }
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">@item.Title</h3>
                                    @if (showExcerpts && !string.IsNullOrEmpty(item.Excerpt))
                                    {
                                        <p class="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">@item.Excerpt</p>
                                    }
                                </div>
                                @if (!string.IsNullOrEmpty(item.Url))
                                {
                                    <svg class="w-5 h-5 text-gray-400 dark:text-gray-500 hidden md:block" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                                    </svg>
                                }
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(item.Url))
                        {
                            <a href="@item.Url" class="absolute inset-0" aria-label="@item.Title"></a>
                        }
                    </div>
                }
            </div>
        }

        @if (showViewAllButton)
        {
            <div class="text-center mt-8 md:hidden">
                @if (!string.IsNullOrEmpty(Model?.ViewAllButtonUrl))
                {
                    <a href="@Model.ViewAllButtonUrl" class="text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900">
                        @viewAllButtonText
                        <svg class="w-4 h-4 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                        </svg>
                    </a>
                }
                else
                {
                    <button type="button" class="text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900">
                        @viewAllButtonText
                        <svg class="w-4 h-4 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                        </svg>
                    </button>
                }
            </div>
        }
    </div>
</section>

<style>
    .relative { position: relative; }
    .absolute { position: absolute; }
    .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
</style>