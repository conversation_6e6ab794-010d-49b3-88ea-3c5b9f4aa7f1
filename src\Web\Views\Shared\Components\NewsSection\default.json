{"ComponentId": "NewsSection", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "新闻资讯", "en": "News Section", "ja": "ニュース・お知らせ"}, "Descriptions": {"zh": "展示最新企业新闻和公告的组件", "en": "Component to showcase latest company news and announcements", "ja": "最新の企業ニュースとお知らせを表示するコンポーネント"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "collapsed": true, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "collapsed": true, "layout": "inline"}, "validation": {"required": false, "maxLength": 200}}, {"name": "BackgroundColor", "type": "select", "label": "@FormResource:FormFields_BackgroundColor", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "order": 3, "layout": "inline", "collapsed": true}, "options": [{"value": "white", "label": "@FormResource:FormFields_BackgroundColor_White"}, {"value": "muted", "label": "@FormResource:FormFields_BackgroundColor_Muted"}], "validation": {"required": false}}, {"name": "MaxItems", "type": "number", "label": "@FormResource:FormFields_MaxItems", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "order": 4, "layout": "inline", "collapsed": true}, "validation": {"required": false, "min": 1, "max": 10}, "defaultValue": 4}, {"name": "ShowViewAllButton", "type": "checkbox", "label": "@FormResource:FormFields_ShowViewAllButton", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "order": 5, "layout": "inline", "collapsed": true}, "validation": {"required": false}, "defaultValue": true}, {"name": "ShowCategories", "type": "checkbox", "label": "@FormResource:FormFields_ShowCategories", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "order": 6, "layout": "inline", "collapsed": true}, "validation": {"required": false}, "defaultValue": true}, {"name": "ShowDates", "type": "checkbox", "label": "@FormResource:FormFields_ShowDates", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "order": 7, "layout": "inline", "collapsed": true}, "validation": {"required": false}, "defaultValue": true}, {"name": "ShowExcerpts", "type": "checkbox", "label": "@FormResource:FormFields_ShowExcerpts", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "order": 8, "layout": "inline", "collapsed": true}, "validation": {"required": false}, "defaultValue": true}, {"name": "ViewAllButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_ViewAllButtonText", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 9, "layout": "inline", "collapsed": true}, "validation": {"required": false, "maxLength": 50}}, {"name": "ViewAllButtonUrl", "type": "text", "label": "@FormResource:FormFields_ViewAllButtonUrl", "display": {"group": "@SharedResource:FormGroups_ButtonSettings", "width": "col-span-12", "order": 10, "layout": "inline", "collapsed": true}, "validation": {"required": false, "maxLength": 200}}]}