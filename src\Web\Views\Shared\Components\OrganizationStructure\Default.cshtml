@model MlSoft.Sites.Web.ViewModels.Components.OrganizationStructureComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<AdminResource> AdminRes
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment


@{
    var title = Model?.Title;
    var subtitle = Model?.Subtitle;
    var uniqueId = JObjectHelper.GenerateId("org-chart");
}

<style>
    /* 基础重置 */
    #@uniqueId .org-tree,
    #@uniqueId .org-tree ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    /* 主容器 - 金字塔式布局 */
    #@uniqueId .org-tree {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        position: relative;
    }

    #@uniqueId .org-tree-wrapper {
        width: 100%;
        overflow-x: auto;
        overflow-y: visible;
        display: flex;
        justify-content: center;
        padding: 2rem 1rem;
        min-height: 300px;
    }

    /* 每一层的容器 */
    #@uniqueId .org-tree ul {
        display: flex;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: flex-start;
        margin-top: 2rem;
        position: relative;
        width: auto;
        gap: 1rem;
    }

    #@uniqueId .org-tree > li > ul {
        margin-top: 2rem;
    }

    /* 节点样式 */
    #@uniqueId .org-tree li {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width:80px;
    }

   

    /* 子节点的水平连接线 */
    #@uniqueId .org-tree ul::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
        border-radius: 2px;
        z-index: 5;
    }

    .dark #@uniqueId .org-tree ul::before {
        background: linear-gradient(90deg, #60a5fa 0%, #93c5fd 100%);
    }

     /* 水平线中点向上的连接线（与父节点连成“T”字） */
     #@uniqueId .org-tree li > ul::after {
         content: '';
         position: absolute;
         top: -2rem;
         left: 50%;
         transform: translateX(-50%);
         width: 3px;
         height: 2rem;
         background: linear-gradient(180deg, #3b82f6 0%, #60a5fa 100%);
         border-radius: 2px;
         z-index: 8;
     }
     .dark #@uniqueId .org-tree li > ul::after {
         background: linear-gradient(180deg, #60a5fa 0%, #93c5fd 100%);
     }

    /* 单个子节点时隐藏水平线 */
    #@uniqueId .org-tree ul:has(> li:only-child)::before {
        display: none;
    }

    /* 子节点向上的垂直连接线 */
    #@uniqueId .org-tree ul > li::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 3px;
        height: 2rem;
        background: linear-gradient(180deg, #3b82f6 0%, #60a5fa 100%);
        border-radius: 2px;
        z-index: 8;
    }
    .dark #@uniqueId .org-tree ul > li::before {
        background: linear-gradient(180deg, #60a5fa 0%, #93c5fd 100%);
    }

    /* 顶层节点不需要向上的连接线 */
    #@uniqueId .org-tree > li::before {
        display: none;
    }

    /* 节点卡片需要向下偏移，为连接线留出空间 */
    #@uniqueId .org-tree ul > li {
        padding-top: 2rem;
    }

    /* 响应式设计 */
    @@media (max-width: 768px) {
        #@uniqueId .org-tree ul {
            margin-top: 3rem;
            gap: 2rem;
            flex-wrap: wrap;
        }

        #@uniqueId .org-tree li {
            min-width: 160px;
        }

        #@uniqueId .org-tree-wrapper {
            padding: 1rem 0.5rem;
        }

        #@uniqueId .org-tree > li > ul {
            padding-top: 2rem;
        }

        #@uniqueId .org-tree li:has(> ul)::after {
            height: 3rem;
        }
    }

    @@media (max-width: 480px) {
        #@uniqueId .org-tree li {
            min-width: 140px;
        }

        #@uniqueId .org-tree ul {
            gap: 1rem;
            margin-top: 2.5rem;
        }
    }
</style>

<section id="@uniqueId" class="bg-white dark:bg-gray-900 py-8 px-4 mx-auto max-w-screen-xl" style="--background-color: #fff; --background-color-dark: #111827;">
    <div class="mx-auto max-w-screen-md text-center mb-8 lg:mb-12">
        @if (!string.IsNullOrEmpty(title))
        {
            <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">@title</h2>
        }
        @if (!string.IsNullOrEmpty(subtitle))
        {
            <p class="mb-5 font-light text-gray-500 sm:text-xl dark:text-gray-400">@subtitle</p>
        }
    </div>

    <div class="org-tree-wrapper">
        @if (Model?.Departments != null && Model.Departments.Any())
        {
            <ul class="org-tree">
                @{
                    ViewData["Level"] = 0;
                }
                @foreach (var node in Model.Departments)
                {
                    <li>
                        <partial name="~/Views/Shared/Components/OrganizationStructure/_OrganizationStructureNode.cshtml" model="node" />
                    </li>
                }
            </ul>
        }
        else
        {
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-sitemap text-2xl text-gray-400 dark:text-gray-500"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">@AdminRes["NoOrganizationData"]</h3>
                <p class="text-gray-500 dark:text-gray-400">@AdminRes["NoDataAvailable"]</p>
            </div>
        }
    </div>
</section>
