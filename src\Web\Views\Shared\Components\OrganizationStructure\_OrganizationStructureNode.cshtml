@model MlSoft.Sites.Web.ViewModels.Components.OrganizationStructureNode
@using MlSoft.Sites.Model.Extensions
@using MlSoft.Sites.Model.Entities.LocaleFields

@{
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var description = Model.Department.GetLocalizedProperty(culture, (OrganizationLocaleFields l) => l.Description);
}

@{

    var hasChildren = Model.Children?.Any() == true;
    var isLeafNode = !hasChildren;
    var level = ViewData.ContainsKey("Level") ? (int)ViewData["Level"]! : 0;



    var boxWidth = level == 0 ? "w-64" : $"w-24";

    // 根据层级决定样式
    var cardClasses = level switch {
        0 => "node-container executive-node bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-300 dark:from-blue-900/50 dark:to-indigo-900/50 dark:border-blue-600",
        1 => "node-container manager-node bg-gradient-to-br from-green-50 to-emerald-100 border-2 border-green-300 dark:from-green-900/50 dark:to-emerald-900/50 dark:border-green-600",
        _ => "node-container employee-node bg-gradient-to-br from-gray-50 to-slate-100 border-2 border-gray-300 dark:from-gray-800/50 dark:to-slate-800/50 dark:border-gray-600"
    };

    var textClasses = level switch {
        0 => "text-blue-900 dark:text-blue-100",
        1 => "text-green-900 dark:text-green-100",
        _ => "text-gray-900 dark:text-gray-100"
    };

    var badgeClasses = level switch {
        0 => "bg-blue-500 text-white",
        1 => "bg-green-500 text-white",
        _ => "bg-gray-500 text-white"
    };
}

<div class="@cardClasses rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 @boxWidth text-center relative overflow-hidden">
    <!-- 层级标识角标 -->
    @* @if (level <= 1)
    {
        <div class="absolute top-2 right-2 @badgeClasses text-xs px-2 py-1 rounded-full font-medium shadow-sm">
            @if (level == 0) { <text>高管</text> }
            else if (level == 1) { <text>部门</text> }
        </div>
    } *@

    <!-- 装饰性图标 -->
    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r @(level == 0 ? "from-blue-400 to-indigo-500" : level == 1 ? "from-green-400 to-emerald-500" : "from-gray-400 to-slate-500")"></div>

    <div class="p-5 pt-6">
        <!-- 部门图标 -->
        <div class="w-12 h-12 mx-auto mb-3 rounded-full @(level == 0 ? "bg-blue-100 dark:bg-blue-800/50" : level == 1 ? "bg-green-100 dark:bg-green-800/50" : "bg-gray-100 dark:bg-gray-700/50") flex items-center justify-center">
            <i class="fas @(level == 0 ? "fa-crown text-blue-600 dark:text-blue-300" : level == 1 ? "fa-users text-green-600 dark:text-green-300" : "fa-user text-gray-600 dark:text-gray-300") text-lg"></i>
        </div>

        <!-- 部门名称 -->
        <h3 class="text-lg font-bold @textClasses mb-2 leading-tight">
            @Model.Department.GetLocalizedProperty(culture, (OrganizationLocaleFields l) => l.DepartmentName)
        </h3>

        <!-- 部门描述 -->
        @if(!string.IsNullOrWhiteSpace(description))
        {
            <p class="text-sm text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">
                @description
            </p>
        }

        @* <!-- 统计信息 -->
        @if (hasChildren)
        {
            <div class="flex justify-center items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                <span class="flex items-center">
                    <i class="fas fa-sitemap mr-1"></i>
                    @Model.Children.Count
                </span>
            </div>
        }
        else
        {
            <div class="flex justify-center">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                    <i class="fas fa-leaf mr-1"></i>
                    执行部门
                </span>
            </div>
        } *@
    </div>

    <!-- 悬停效果的光晕 -->
    <div class="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
</div>

@if (Model.Children != null && Model.Children.Any())
{
    <ul>
        @{
            ViewData["Level"] = level + 1;
        }
        @foreach (var child in Model.Children)
        {
            <li>
                <partial name="~/Views/Shared/Components/OrganizationStructure/_OrganizationStructureNode.cshtml" model="child" />
            </li>
        }
    </ul>
}
