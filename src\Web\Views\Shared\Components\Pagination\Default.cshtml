@model MlSoft.Sites.Web.ViewModels.Components.PaginationComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedRes

@{
	// 从ViewModel获取数据，设置默认值
	var currentPageIndex = Model?.CurrentPageIndex ?? 1;
	var pageSize = Model?.PageSize ?? 10;
	var totalCount = Model?.TotalCount ?? 0;
	var info = Model?.Info ?? "";
	var diffCount = Model?.DiffCount ?? 2;

	// 显示控制选项
	var showFirstLast = Model?.ShowFirstLast ?? true;
	var showPrevNext = Model?.ShowPrevNext ?? true;
	var showPageNumbers = Model?.ShowPageNumbers ?? true;
	var showInfo = false;
	var showTotalCount = Model?.ShowTotalCount ?? true;
	var size = Model?.Size ?? "md";

	// 计算分页数据
	var totalPages = pageSize > 0 ? (int)Math.Ceiling(totalCount / (double)pageSize) : 0;
	var startPage = Math.Max(1, currentPageIndex - diffCount);
	var endPage = Math.Min(totalPages, currentPageIndex + diffCount);

	// 如果总页数小于等于显示范围，显示所有页码
	if (totalPages <= (diffCount * 2 + 1))
	{
		startPage = 1;
		endPage = totalPages;
	}

	// 尺寸相关的CSS类
	var sizeClasses = size switch
	{
		"sm" => "text-xs px-2 py-1",
		"lg" => "text-base px-4 py-3",
		_ => "text-sm px-3 py-2"
	};

	var containerClasses = size switch
	{
		"sm" => "gap-1 p-2",
		"lg" => "gap-2 p-6",
		_ => "gap-2 p-4"
	};

	// URL生成函数
	string GeneratePageUrl(int pageNumber)
	{
		return Model?.GeneratePageUrl(pageNumber) ?? $"?page={pageNumber}";
	}
}

@if (totalPages > 1 || showInfo)
{
	<div class="container max-w-7xl mx-auto px-4">

		<div class="flex flex-col sm:flex-row justify-between items-center @containerClasses bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">

			@if (showTotalCount && totalCount > 0)
			{
				<!-- 总数信息 -->
				<div class="flex items-center gap-2 @sizeClasses text-gray-600 dark:text-gray-300">
					<span>@SharedRes["Pagination_Total"]:</span>
					<span class="font-semibold text-primary-600 dark:text-primary-400">@totalCount.ToString("N0")</span>
					<span>@SharedRes["Pagination_Records"]</span>
				</div>
			}

			@if (totalPages > 1)
			{
				<!-- 分页导航 -->
				<nav class="flex items-center space-x-1" aria-label="@(SharedRes["Pagination_Navigation"])" role="navigation">

					@if (showFirstLast)
					{
						<!-- 首页按钮 -->
						@if (currentPageIndex == 1)
						{
							<button type="button"
									class="@sizeClasses text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded-lg cursor-not-allowed focus:outline-none"
									disabled
									aria-label="@(SharedRes["Pagination_First"])">
								««
							</button>
						}
						else
						{
							<a href="@GeneratePageUrl(1)"
							   class="@sizeClasses text-gray-500 bg-white hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white rounded-lg border border-gray-300 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-800"
							   title="@(SharedRes["Pagination_First"])"
							   aria-label="@(SharedRes["Pagination_First"])">
								««
							</a>
						}
					}

					@if (showPrevNext)
					{
						<!-- 上一页按钮 -->
						@if (currentPageIndex == 1)
						{
							<button type="button"
									class="@sizeClasses text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded-lg cursor-not-allowed focus:outline-none"
									disabled
									aria-label="@(SharedRes["Pagination_Prev"])">
								«
							</button>
						}
						else
						{
							<a href="@GeneratePageUrl(currentPageIndex - 1)"
							   class="@sizeClasses text-gray-500 bg-white hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white rounded-lg border border-gray-300 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-800"
							   title="@(SharedRes["Pagination_Prev"])"
							   aria-label="@(SharedRes["Pagination_Prev"])">
								«
							</a>
						}
					}

					@if (showPageNumbers)
					{
						<!-- 省略号（开始） -->
						@if (startPage > 1)
						{
							<span class="@sizeClasses text-gray-400 dark:text-gray-500">...</span>
						}

						<!-- 页码按钮 -->
						@for (int i = startPage; i <= endPage; i++)
						{
							@if (currentPageIndex == i)
							{
								<button type="button"
										class="@sizeClasses text-primary-600 bg-primary-50 hover:bg-primary-100 hover:text-primary-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-lg border border-primary-300 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-800"
										aria-label="@($"{SharedRes["Pagination_Page"]}{i}")"
										aria-current="page">
									@i
								</button>
							}
							else
							{
								<a href="@GeneratePageUrl(i)"
								   class="@sizeClasses text-gray-500 bg-white hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white rounded-lg border border-gray-300 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-800"
								   title="@($"{SharedRes["Pagination_Page"]}{i}")"
								   aria-label="@($"{SharedRes["Pagination_Page"]}{i}")">
									@i
								</a>
							}
						}

						<!-- 省略号（结束） -->
						@if (endPage < totalPages)
						{
							<span class="@sizeClasses text-gray-400 dark:text-gray-500">...</span>
						}
					}

					@if (showPrevNext)
					{
						<!-- 下一页按钮 -->
						@if (currentPageIndex == totalPages)
						{
							<button type="button"
									class="@sizeClasses text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded-lg cursor-not-allowed focus:outline-none"
									disabled
									aria-label="@(SharedRes["Pagination_Next"])">
								»
							</button>
						}
						else
						{
							<a href="@GeneratePageUrl(currentPageIndex + 1)"
							   class="@sizeClasses text-gray-500 bg-white hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white rounded-lg border border-gray-300 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-800"
							   title="@(SharedRes["Pagination_Next"])"
							   aria-label="@(SharedRes["Pagination_Next"])">
								»
							</a>
						}
					}

					@if (showFirstLast)
					{
						<!-- 末页按钮 -->
						@if (currentPageIndex == totalPages)
						{
							<button type="button"
									class="@sizeClasses text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded-lg cursor-not-allowed focus:outline-none"
									disabled
									aria-label="@(SharedRes["Pagination_Last"])">
								»»
							</button>
						}
						else
						{
							<a href="@GeneratePageUrl(totalPages)"
							   class="@sizeClasses text-gray-500 bg-white hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white rounded-lg border border-gray-300 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:focus:ring-primary-800"
							   title="@(SharedRes["Pagination_Last"])"
							   aria-label="@(SharedRes["Pagination_Last"])">
								»»
							</a>
						}
					}
				</nav>
			}

			@if (showInfo && (!string.IsNullOrEmpty(info) || totalPages > 0))
			{
				<div class="flex items-center gap-3 @sizeClasses">
					<!-- 自定义信息 -->
					@if (!string.IsNullOrEmpty(info))
					{
						<span class="text-amber-600 dark:text-amber-400">@info</span>
					}

					<!-- 页码信息 -->
					@if (totalPages > 0)
					{
						<span class="text-gray-600 dark:text-gray-400">
							@SharedRes["Pagination_Page"] @currentPageIndex @SharedRes["Pagination_Of"] @totalPages @SharedRes["Pagination_Pages"]
						</span>
					}
				</div>
			}
		</div>

	</div>
}