@model MlSoft.Sites.Web.ViewModels.Components.PresidentMessageComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedR<PERSON>
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var showPhoto = Model?.ShowPhoto ?? true;
    var showPosition = Model?.ShowPosition ?? true;
    var showBiography = Model?.ShowBiography ?? false;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";
    var photoSize = Model?.PhotoSize ?? "medium";

    // Get president data
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var presidentLocale = president?.Locale?.ContainsKey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("president-message-card");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    var photoSizeClass = photoSize switch
    {
        "small" => "w-20 h-20",
        "large" => "w-36 h-36",
        _ => "w-28 h-28"
    };

    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "/images/placeholder-avatar.jpg" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-12 @containerClass">
    <div class="container max-w-7xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["PresidentMessage_Title"])
                </h2>
                <div class="h-1 w-16 bg-primary-600 mx-auto"></div>
            </div>
        }

        @if (president != null && presidentLocale != null)
        {
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                <!-- Header Card -->
                <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-6">
                    <div class="flex flex-col sm:flex-row items-center gap-6">
                        @if (showPhoto && !string.IsNullOrEmpty(president.PhotoUrl))
                        {
                            <div class="flex-shrink-0">
                                <img src="@ProcessFilePath(president.PhotoUrl)" 
                                     alt="@presidentLocale.Name" 
                                     class="@photoSizeClass rounded-full object-cover border-4 border-white shadow-lg" 
                                     loading="lazy" />
                            </div>
                        }
                        
                        <div class="text-center sm:text-left">
                            <h3 class="text-2xl font-bold text-white mb-2">@presidentLocale.Name</h3>
                            @if (showPosition && !string.IsNullOrEmpty(presidentLocale.Position))
                            {
                                <p class="text-lg text-primary-100 font-medium">@presidentLocale.Position</p>
                            }
                        </div>
                    </div>
                </div>

                <!-- Message Content -->
                @if (!string.IsNullOrEmpty(presidentLocale.Message))
                {
                    <div class="p-8">
                        <div class="relative">
                            <!-- Quote Icon -->
                            <div class="absolute -top-2 -left-2 text-primary-200 dark:text-primary-800">
                                <i class="fas fa-quote-left text-4xl"></i>
                            </div>
                            
                            <div class="pl-8 prose prose-lg max-w-none dark:prose-invert">
                                <div class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line italic">
                                    @Html.Raw(presidentLocale.Message)
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (showBiography && !string.IsNullOrEmpty(presidentLocale.Biography))
                {
                    <div class="px-8 pb-8">
                        <div class="pt-6 border-t border-gray-200 dark:border-gray-700">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-user-graduate text-primary-600 mr-2"></i>
                                @FormRes["PresidentMessage_Biography"]
                            </h4>
                            <div class="text-gray-600 dark:text-gray-400 leading-relaxed whitespace-pre-line">
                                @Html.Raw(presidentLocale.Biography)
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <!-- No President Data Available -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    <i class="fas fa-user-tie text-4xl mb-4"></i>
                    <p>@FormRes["PresidentMessage_NoData"]</p>
                </div>
            </div>
        }
    </div>
</section>