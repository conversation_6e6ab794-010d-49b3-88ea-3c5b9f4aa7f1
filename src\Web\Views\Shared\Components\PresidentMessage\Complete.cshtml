@model MlSoft.Sites.Web.ViewModels.Components.PresidentMessageComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedR<PERSON>
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var showPhoto = Model?.ShowPhoto ?? true;
    var showPosition = Model?.ShowPosition ?? true;
    var showBiography = Model?.ShowBiography ?? false;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";

    // Get president data
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var presidentLocale = president?.Locale?.ContainsKey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("president-message-complete");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "/images/placeholder-avatar.jpg" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";

    // Get current date for signature
    var currentDate = DateTime.Now.ToString(culture == "ja" ? "yyyy年M月" : culture == "en" ? "MMMM yyyy" : "yyyy年M月");
}

<section id="@uniqueId" class="py-8 @containerClass">
    <div class="container max-w-7xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["PresidentMessage_Title"])
                </h1>
                <div class="h-1 w-16 bg-primary-600"></div>
            </div>
        }

        @if (president != null && presidentLocale != null)
        {
            <!-- CEO Profile Section -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm mb-8">
                <div class="p-8">
                    <div class="grid gap-8 lg:grid-cols-3">
                        <!-- CEO Photo -->
                        @if (showPhoto && !string.IsNullOrEmpty(president.PhotoUrl))
                        {
                            <div class="lg:col-span-1">
                                <div class="text-center">
                                    <div class="w-48 h-60 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                                        <img src="@ProcessFilePath(president.PhotoUrl)" 
                                             alt="@presidentLocale.Name" 
                                             class="w-full h-full object-cover" 
                                             loading="lazy" />
                                    </div>
                                    <div class="text-center">
                                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-1">@presidentLocale.Name</h2>
                                        @if (showPosition && !string.IsNullOrEmpty(presidentLocale.Position))
                                        {
                                            <p class="text-gray-600 dark:text-gray-300 text-sm">@presidentLocale.Position</p>
                                        }
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- CEO Message Content -->
                        <div class="@(showPhoto && !string.IsNullOrEmpty(president.PhotoUrl) ? "lg:col-span-2" : "lg:col-span-3")">
                            @if (!string.IsNullOrEmpty(presidentLocale.Message))
                            {
                                <div class="space-y-6 text-gray-700 dark:text-gray-300 leading-relaxed">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">@FormRes["PresidentMessage_ToStakeholders"]</h3>
                                        <div class="whitespace-pre-line">
                                            @Html.Raw(presidentLocale.Message)
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

           
            @if (showBiography && !string.IsNullOrEmpty(presidentLocale.Biography))
            {
                <!-- Biography Section -->
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm mt-8">
                    <div class="p-8">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-user-graduate text-primary-600 mr-3"></i>
                            @FormRes["PresidentMessage_Biography"]
                        </h3>
                        <div class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">
                            @Html.Raw(presidentLocale.Biography)
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <!-- No President Data Available -->
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-8 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    <i class="fas fa-user-tie text-4xl mb-4"></i>
                    <p>@FormRes["PresidentMessage_NoData"]</p>
                </div>
            </div>
        }
    </div>
</section>