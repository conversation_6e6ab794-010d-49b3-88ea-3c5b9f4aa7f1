@model MlSoft.Sites.Web.ViewModels.Components.PresidentMessageComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedRes
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var layout = Model?.Layout ?? "default";
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var showPhoto = Model?.ShowPhoto ?? true;
    var showPosition = Model?.ShowPosition ?? true;
    var showBiography = Model?.ShowBiography ?? false;
    var showBorder = Model?.ShowBorder ?? true;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";
    var photoPosition = Model?.PhotoPosition ?? "left";
    var photoSize = Model?.PhotoSize ?? "medium";

    // Get president data
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var presidentLocale = president?.Locale?.ContainsKey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("president-message");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    var cardClass = showBorder ? 
        "border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm" : 
        "rounded-lg";

    var photoSizeClass = photoSize switch
    {
        "small" => "w-24 h-24",
        "large" => "w-40 h-40",
        _ => "w-32 h-32"
    };

    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "/images/placeholder-avatar.jpg" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-12 @containerClass">
    <div class="container max-w-7xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["PresidentMessage_Title"])
                </h2>
                <div class="h-1 w-16 bg-primary-600 mx-auto"></div>
            </div>
        }

        @if (president != null && presidentLocale != null)
        {
            <div class="@cardClass p-8">
                @if (photoPosition == "top")
                {
                    <!-- Top Photo Layout -->
                    <div class="text-center mb-6">
                        @if (showPhoto && !string.IsNullOrEmpty(president.PhotoUrl))
                        {
                            <div class="inline-block mb-4">
                                <img src="@ProcessFilePath(president.PhotoUrl)" 
                                     alt="@presidentLocale.Name" 
                                     class="@photoSizeClass rounded-full object-cover mx-auto shadow-lg" 
                                     loading="lazy" />
                            </div>
                        }
                        
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">@presidentLocale.Name</h3>
                            @if (showPosition && !string.IsNullOrEmpty(presidentLocale.Position))
                            {
                                <p class="text-lg text-primary-600 dark:text-primary-400 font-medium">@presidentLocale.Position</p>
                            }
                        </div>
                    </div>
                }
                else
                {
                    <!-- Side Photo Layout -->
                    <div class="flex flex-col @(photoPosition == "right" ? "lg:flex-row-reverse" : "lg:flex-row") gap-8 items-start">
                        @if (showPhoto && !string.IsNullOrEmpty(president.PhotoUrl))
                        {
                            <div class="flex-shrink-0 mx-auto lg:mx-0">
                                <img src="@ProcessFilePath(president.PhotoUrl)" 
                                     alt="@presidentLocale.Name" 
                                     class="@photoSizeClass rounded-full object-cover shadow-lg" 
                                     loading="lazy" />
                            </div>
                        }
                        
                        <div class="flex-1 text-center lg:text-left">
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">@presidentLocale.Name</h3>
                            @if (showPosition && !string.IsNullOrEmpty(presidentLocale.Position))
                            {
                                <p class="text-lg text-primary-600 dark:text-primary-400 font-medium mb-4">@presidentLocale.Position</p>
                            }
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(presidentLocale.Message))
                {
                    <div class="mt-6 prose prose-lg max-w-none dark:prose-invert">
                        <div class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">
                            @Html.Raw(presidentLocale.Message)
                        </div>
                    </div>
                }

                @if (showBiography && !string.IsNullOrEmpty(presidentLocale.Biography))
                {
                    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">@FormRes["PresidentMessage_Biography"]</h4>
                        <div class="text-gray-600 dark:text-gray-400 leading-relaxed whitespace-pre-line">
                            @Html.Raw(presidentLocale.Biography)
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <!-- No President Data Available -->
            <div class="@cardClass p-8 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    <i class="fas fa-user-tie text-4xl mb-4"></i>
                    <p>@FormRes["PresidentMessage_NoData"]</p>
                </div>
            </div>
        }
    </div>
</section>