@model MlSoft.Sites.Web.ViewModels.Components.PresidentMessageComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedR<PERSON>
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var showPhoto = Model?.ShowPhoto ?? true;
    var showPosition = Model?.ShowPosition ?? true;
    var showBiography = Model?.ShowBiography ?? false;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";

    // Get president data
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var presidentLocale = president?.Locale?.ContainsKey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("president-message-featured");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "/images/placeholder-avatar.jpg" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-16 @containerClass">
    <div class="container max-w-7xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["PresidentMessage_Title"])
                </h2>
                <div class="h-1 w-20 bg-primary-600 mx-auto"></div>
            </div>
        }

        @if (president != null && presidentLocale != null)
        {
            <div class="relative">
                <!-- Background Pattern -->
                <div class="absolute inset-0 bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 rounded-2xl transform rotate-1"></div>
                
                <!-- Main Content -->
                <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden">
                    <div class="grid lg:grid-cols-2 gap-0">
                        <!-- Left Side - Photo and Info -->
                        <div class="bg-gradient-to-br from-primary-600 to-primary-700 p-8 lg:p-12 flex flex-col justify-center">
                            <div class="text-center lg:text-left">
                                @if (showPhoto && !string.IsNullOrEmpty(president.PhotoUrl))
                                {
                                    <div class="mb-6 flex justify-center lg:justify-start">
                                        <img src="@ProcessFilePath(president.PhotoUrl)" 
                                             alt="@presidentLocale.Name" 
                                             class="w-32 h-32 lg:w-40 lg:h-40 rounded-full object-cover border-4 border-white shadow-2xl" 
                                             loading="lazy" />
                                    </div>
                                }
                                
                                <h3 class="text-3xl lg:text-4xl font-bold text-white mb-3">@presidentLocale.Name</h3>
                                @if (showPosition && !string.IsNullOrEmpty(presidentLocale.Position))
                                {
                                    <p class="text-xl text-primary-100 font-medium mb-6">@presidentLocale.Position</p>
                                }
                                
                                <!-- Decorative Element -->
                                <div class="hidden lg:block">
                                    <div class="w-16 h-1 bg-white/30 mb-4"></div>
                                    <p class="text-primary-100 text-sm">@FormRes["PresidentMessage_MessageFrom"]</p>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side - Message Content -->
                        <div class="p-8 lg:p-12">
                            @if (!string.IsNullOrEmpty(presidentLocale.Message))
                            {
                                <div class="relative">
                                    <!-- Large Quote -->
                                    <div class="absolute -top-4 -left-4 text-primary-100 dark:text-primary-800">
                                        <i class="fas fa-quote-left text-6xl"></i>
                                    </div>
                                    
                                    <div class="relative z-10 pl-8">
                                        <div class="prose prose-lg max-w-none dark:prose-invert">
                                            <div class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line text-lg">
                                                @Html.Raw(presidentLocale.Message)
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            @if (showBiography && !string.IsNullOrEmpty(presidentLocale.Biography))
                            {
                                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                        <i class="fas fa-user-graduate text-primary-600 mr-3"></i>
                                        @FormRes["PresidentMessage_Biography"]
                                    </h4>
                                    <div class="text-gray-600 dark:text-gray-400 leading-relaxed whitespace-pre-line">
                                        @Html.Raw(presidentLocale.Biography)
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- No President Data Available -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-12 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    <i class="fas fa-user-tie text-6xl mb-6"></i>
                    <p class="text-lg">@FormRes["PresidentMessage_NoData"]</p>
                </div>
            </div>
        }
    </div>
</section>