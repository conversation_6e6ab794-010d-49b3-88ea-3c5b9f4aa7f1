@model MlSoft.Sites.Web.ViewModels.Components.PresidentMessageComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@inject IStringLocalizer<SharedResource> SharedR<PERSON>
@inject IStringLocalizer<AdminResource> AdminRes
@inject IStringLocalizer<FormResource> FormRes

@{
    // Extract display settings from ViewModel
    var showTitle = Model?.ShowTitle ?? true;
    var titleText = Model?.TitleText;
    var showPhoto = Model?.ShowPhoto ?? true;
    var showPosition = Model?.ShowPosition ?? true;
    var showBorder = Model?.ShowBorder ?? true;
    var backgroundStyle = Model?.BackgroundStyle ?? "white";

    // Get president data
    var president = Model?.PresidentData;
    var culture = ViewData["CurrentLanguage"]?.ToString() ?? "zh";
    var presidentLocale = president?.Locale?.ContainsKey(culture) == true ? president.Locale[culture] : null;

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("president-message-minimal");

    // CSS classes based on settings
    var containerClass = backgroundStyle switch
    {
        "gray" => "bg-gray-50 dark:bg-gray-900/50",
        "transparent" => "bg-transparent",
        _ => "bg-white dark:bg-gray-800"
    };

    var cardClass = showBorder ? 
        "border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm" : 
        "rounded-lg";

    string ProcessFilePath(string? filePath) =>
        string.IsNullOrEmpty(filePath) ? "/images/placeholder-avatar.jpg" :
        filePath.StartsWith("/") ? filePath : $"/{filePath}";
}

<section id="@uniqueId" class="py-8 @containerClass">
    <div class="container max-w-7xl mx-auto px-4">
        @if (showTitle)
        {
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                    @(!string.IsNullOrEmpty(titleText) ? titleText : FormRes["PresidentMessage_Title"])
                </h2>
            </div>
        }

        @if (president != null && presidentLocale != null)
        {
            <div class="@cardClass p-6">
                <!-- President Info -->
                <div class="flex items-center gap-4 mb-6">
                    @if (showPhoto && !string.IsNullOrEmpty(president.PhotoUrl))
                    {
                        <div class="flex-shrink-0">
                            <img src="@ProcessFilePath(president.PhotoUrl)" 
                                 alt="@presidentLocale.Name" 
                                 class="w-16 h-16 rounded-full object-cover" 
                                 loading="lazy" />
                        </div>
                    }
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">@presidentLocale.Name</h3>
                        @if (showPosition && !string.IsNullOrEmpty(presidentLocale.Position))
                        {
                            <p class="text-sm text-primary-600 dark:text-primary-400">@presidentLocale.Position</p>
                        }
                    </div>
                </div>

                <!-- Message -->
                @if (!string.IsNullOrEmpty(presidentLocale.Message))
                {
                    <div class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">
                        @Html.Raw(presidentLocale.Message)
                    </div>
                }
            </div>
        }
        else
        {
            <!-- No President Data Available -->
            <div class="@cardClass p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400">
                    <i class="fas fa-user-tie text-2xl mb-2"></i>
                    <p class="text-sm">@FormRes["PresidentMessage_NoData"]</p>
                </div>
            </div>
        }
    </div>
</section>