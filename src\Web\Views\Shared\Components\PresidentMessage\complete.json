{"ComponentId": "President<PERSON><PERSON><PERSON>", "Id": "Complete", "Names": {"zh": "完整页面布局", "en": "Complete Page Layout", "ja": "完全ページレイアウト"}, "Descriptions": {"zh": "完整的社長メッセージ页面布局，包含社长简介、愿景战略、承诺价值观和结束致辞", "en": "Complete president message page layout including profile, vision & strategy, commitments & values, and closing message", "ja": "社長プロフィール、ビジョン・戦略、コミットメント・価値観、締めのメッセージを含む完全な社長メッセージページレイアウト"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:PresidentMessage_TitleText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "layout": "inline", "order": 1}, "validation": {"required": false, "maxLength": 100}}, {"name": "ShowPhoto", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON>age_ShowPhoto", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 1}, "defaultValue": true}, {"name": "ShowPosition", "type": "checkbox", "label": "@FormResource:PresidentM<PERSON>age_ShowPosition", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 2}, "defaultValue": true}, {"name": "ShowBiography", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON>age_ShowBiography", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "collapsed": true, "layout": "inline", "order": 3}, "defaultValue": false}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON><PERSON>_ShowTitle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 10}, "defaultValue": true}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:President<PERSON><PERSON><PERSON>_BackgroundStyle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 12}, "options": [{"value": "white", "label": "@FormResource:Background_White"}, {"value": "gray", "label": "@FormResource:<PERSON>_<PERSON>"}, {"value": "transparent", "label": "@FormResource:Background_Transparent"}], "defaultValue": "white"}]}