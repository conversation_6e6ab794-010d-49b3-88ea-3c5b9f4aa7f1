{"ComponentId": "President<PERSON><PERSON><PERSON>", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "标准布局", "en": "Standard Layout", "ja": "標準レイアウト"}, "Descriptions": {"zh": "标准的社長メッセージ布局，支持多种照片位置和显示选项", "en": "Standard president message layout with multiple photo positions and display options", "ja": "複数の写真位置と表示オプションをサポートする標準的な社長メッセージレイアウト"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:PresidentMessage_TitleText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}, {"name": "ShowPhoto", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON>age_ShowPhoto", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 1}, "defaultValue": true}, {"name": "ShowPosition", "type": "checkbox", "label": "@FormResource:PresidentM<PERSON>age_ShowPosition", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 2}, "defaultValue": true}, {"name": "ShowBiography", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON>age_ShowBiography", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 3}, "defaultValue": false}, {"name": "PhotoPosition", "type": "select", "label": "@FormResource:PresidentMessage_PhotoPosition", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 4}, "options": [{"value": "left", "label": "@FormResource:PhotoPosition_Left"}, {"value": "right", "label": "@FormResource:PhotoPosition_Right"}, {"value": "top", "label": "@FormResource:PhotoPosition_Top"}], "defaultValue": "left"}, {"name": "PhotoSize", "type": "select", "label": "@FormResource:PresidentMessage_PhotoSize", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 5}, "options": [{"value": "small", "label": "@FormResource:PhotoSize_Small"}, {"value": "medium", "label": "@FormResource:PhotoSize_Medium"}, {"value": "large", "label": "@FormResource:PhotoSize_Large"}], "defaultValue": "medium"}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON><PERSON>_ShowTitle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 10}, "defaultValue": true}, {"name": "ShowBorder", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON><PERSON>_ShowBorder", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 11}, "defaultValue": true}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:President<PERSON><PERSON><PERSON>_BackgroundStyle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 13}, "options": [{"value": "white", "label": "@FormResource:Background_White"}, {"value": "gray", "label": "@FormResource:<PERSON>_<PERSON>"}, {"value": "transparent", "label": "@FormResource:Background_Transparent"}], "defaultValue": "white"}]}