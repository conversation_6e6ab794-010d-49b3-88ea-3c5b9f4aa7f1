{"ComponentId": "President<PERSON><PERSON><PERSON>", "Id": "Featured", "Names": {"zh": "特色布局", "en": "Featured Layout", "ja": "フィーチャーレイアウト"}, "Descriptions": {"zh": "大型特色社長メッセージ布局，适合首页或重要页面的突出展示", "en": "Large featured president message layout, suitable for homepage or important page highlights", "ja": "ホームページや重要なページのハイライトに適した大型フィーチャー社長メッセージレイアウト"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:PresidentMessage_TitleText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}, {"name": "ShowPhoto", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON>age_ShowPhoto", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 1}, "defaultValue": true}, {"name": "ShowPosition", "type": "checkbox", "label": "@FormResource:PresidentM<PERSON>age_ShowPosition", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 2}, "defaultValue": true}, {"name": "ShowBiography", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON>age_ShowBiography", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 3}, "defaultValue": false}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON><PERSON>_ShowTitle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 10}, "defaultValue": true}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:President<PERSON><PERSON><PERSON>_BackgroundStyle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 12}, "options": [{"value": "white", "label": "@FormResource:Background_White"}, {"value": "gray", "label": "@FormResource:<PERSON>_<PERSON>"}, {"value": "transparent", "label": "@FormResource:Background_Transparent"}], "defaultValue": "white"}]}