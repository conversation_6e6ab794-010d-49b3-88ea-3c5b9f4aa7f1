{"ComponentId": "President<PERSON><PERSON><PERSON>", "Id": "Minimal", "Names": {"zh": "简洁布局", "en": "Minimal Layout", "ja": "ミニマルレイアウト"}, "Descriptions": {"zh": "简洁紧凑的社長メッセージ布局，适合空间有限的场景", "en": "Clean and compact president message layout, suitable for space-limited scenarios", "ja": "スペースが限られた場面に適したクリーンでコンパクトな社長メッセージレイアウト"}, "formFields": [{"name": "TitleText", "type": "multilingual-text", "label": "@FormResource:PresidentMessage_TitleText", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline"}, "validation": {"required": false, "maxLength": 100}}, {"name": "ShowPhoto", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON>age_ShowPhoto", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 1}, "defaultValue": true}, {"name": "ShowPosition", "type": "checkbox", "label": "@FormResource:PresidentM<PERSON>age_ShowPosition", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "collapsed": true, "order": 2}, "defaultValue": true}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON><PERSON>_ShowTitle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 10}, "defaultValue": true}, {"name": "ShowBorder", "type": "checkbox", "label": "@FormResource:President<PERSON><PERSON><PERSON>_ShowBorder", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 11}, "defaultValue": true}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:President<PERSON><PERSON><PERSON>_BackgroundStyle", "display": {"collapsed": true, "group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-3", "layout": "inline", "order": 13}, "options": [{"value": "white", "label": "@FormResource:Background_White"}, {"value": "gray", "label": "@FormResource:<PERSON>_<PERSON>"}, {"value": "transparent", "label": "@FormResource:Background_Transparent"}], "defaultValue": "white"}]}