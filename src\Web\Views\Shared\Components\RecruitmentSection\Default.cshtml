@model MlSoft.Sites.Web.ViewModels.Components.RecruitmentSectionComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
    // Extract data from ViewModel with null-safe defaults
    var title = string.IsNullOrEmpty(Model?.Title) ? SharedRes["RecruitmentSection_Title"] : Model?.Title;
    var description = string.IsNullOrEmpty(Model?.Description) ? SharedRes["RecruitmentSection_Description"] : Model?.Description;
    var backgroundClass = Model?.BackgroundColor == "muted" ? "bg-gray-50 dark:bg-gray-900/50" : "bg-white dark:bg-gray-800";


    var buttonVariant = Model?.ButtonVariant ?? "outline";

    // Generate unique ID for the component
    var uniqueId = JObjectHelper.GenerateId("recruitment-section");

    // Button classes based on variant
    var buttonClass = buttonVariant switch
    {
        "primary" => "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800",
        "outline" => "text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900",
        _ => "text-primary-700 hover:text-white border border-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-900"
    };


}

<section id="@uniqueId" class="py-16 lg:py-24 @backgroundClass">
    <div class="container max-w-7xl mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">@title</h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
                @description
            </p>
        </div>

        @if (Model?.Categories?.Any() == true)
        {

            <div class="grid lg:grid-cols-3 gap-8 mb-12">
                @foreach (var category in Model.Categories)
                {
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-6 text-center">
                        @if (!string.IsNullOrEmpty(category.Icon))
                        {
                            <div class="mb-4">
                                <i class="fas <EMAIL> text-4xl text-primary-600 dark:text-primary-400"></i>
                            </div>
                        }
                        
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">@category.Title</h3>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">@category.Description</p>
                        
                        @if (!string.IsNullOrEmpty(category.ButtonUrl))
                        {
                            <a href="@category.ButtonUrl" class="@buttonClass w-full inline-flex justify-center items-center">
                                @(!string.IsNullOrEmpty(category.ButtonText) ? category.ButtonText : "詳細を見る")
                            </a>
                        }
                        else
                        {
                            <button type="button" class="@buttonClass w-full">
                                @(!string.IsNullOrEmpty(category.ButtonText) ? category.ButtonText : "詳細を見る")
                            </button>
                        }
                    </div>
                }
            </div>
        }

        
    </div>
</section>