{"ComponentId": "RecruitmentSection", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "招聘信息", "en": "Recruitment Section", "ja": "採用情報"}, "Descriptions": {"zh": "展示企业招聘信息和职位的组件", "en": "Component to showcase company recruitment information and job positions", "ja": "企業の採用情報と求人を表示するコンポーネント"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true}, "validation": {"required": false, "maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true}, "validation": {"required": false, "maxLength": 500}}, {"name": "BackgroundColor", "type": "select", "label": "@FormResource:FormFields_BackgroundColor", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-6", "layout": "inline", "collapsed": true, "order": 3}, "options": [{"value": "white", "label": "@FormResource:FormFields_BackgroundColor_White"}, {"value": "muted", "label": "@FormResource:FormFields_BackgroundColor_Muted"}], "validation": {"required": false}}, {"name": "ButtonV<PERSON>t", "type": "select", "label": "@SharedResource:FormGroups_ButtonSettings", "display": {"group": "@SharedResource:FormGroups_DisplaySettings", "width": "col-span-6", "layout": "inline", "collapsed": true, "order": 7}, "options": [{"value": "primary", "label": "@FormResource:ButtonStyle_Primary"}, {"value": "outline", "label": "@FormResource:ButtonStyle_Outline"}], "validation": {"required": false}}, {"name": "Categories", "type": "repeater", "label": "@FormResource:FormFields_RecruitmentSection_Categories", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "collapsed": true, "order": 8}, "template": {"fields": [{"name": "Title", "type": "multilingual-text", "label": "@SharedResource:FormFields_Title", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": true, "maxLength": 100}}, {"name": "Description", "type": "multilingual-textarea", "label": "@SharedResource:FormFields_Description", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": true, "maxLength": 300}}, {"name": "Icon", "type": "select", "label": "@FormResource:FormFields_Icon", "display": {"width": "col-span-4", "layout": "inline", "collapsed": true}, "options": [{"value": "graduation-cap", "label": "graduation-cap"}, {"value": "briefcase", "label": "briefcase"}, {"value": "users", "label": "users"}, {"value": "user-tie", "label": "user-tie"}, {"value": "handshake", "label": "handshake"}, {"value": "chart-line", "label": "chart-line"}]}, {"name": "ButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_ButtonText", "display": {"width": "col-span-12", "layout": "inline", "collapsed": true}, "validation": {"required": false, "maxLength": 50}}, {"name": "ButtonUrl", "type": "text", "label": "@FormResource:FormFields_ButtonUrl", "display": {"width": "col-span-4", "layout": "inline", "collapsed": true}, "validation": {"required": false, "maxLength": 200}}]}, "validation": {"required": false, "minItems": 1, "maxItems": 4}}]}