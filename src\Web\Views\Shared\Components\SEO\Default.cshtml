@model MlSoft.Sites.Web.ViewModels.Components.SEOComponentViewModel
@using Newtonsoft.Json

@{
    var jsonSettings = new JsonSerializerSettings
    {
        ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
        NullValueHandling = NullValueHandling.Ignore,
        Formatting = Formatting.None
    };
}

@if (!string.IsNullOrEmpty(Model.Title))
{
    <title>@Model.Title</title>
}

@if (!string.IsNullOrEmpty(Model.Description))
{
    <meta name="description" content="@Model.Description" />
}

@if (!string.IsNullOrEmpty(Model.Keywords))
{
    <meta name="keywords" content="@Model.Keywords" />
}

@if (!string.IsNullOrEmpty(Model.CanonicalUrl))
{
    <link rel="canonical" href="@Model.CanonicalUrl" />
}

@if (Model.HreflangUrls.Any())
{
    @foreach (var hreflang in Model.HreflangUrls)
    {
        <link rel="alternate" hreflang="@hreflang.Key" href="@hreflang.Value" />
    }
}

@if (!string.IsNullOrEmpty(Model.OgTitle))
{
    <meta property="og:title" content="@Model.OgTitle" />
}

@if (!string.IsNullOrEmpty(Model.OgDescription))
{
    <meta property="og:description" content="@Model.OgDescription" />
}

@if (!string.IsNullOrEmpty(Model.OgImage))
{
    <meta property="og:image" content="@Model.OgImage" />
}

@if (!string.IsNullOrEmpty(Model.OgUrl))
{
    <meta property="og:url" content="@Model.OgUrl" />
}

@if (!string.IsNullOrEmpty(Model.OgType))
{
    <meta property="og:type" content="@Model.OgType" />
}

@if (!string.IsNullOrEmpty(Model.OgLocale))
{
    <meta property="og:locale" content="@Model.OgLocale" />
}

@if (!string.IsNullOrEmpty(Model.SiteName))
{
    <meta property="og:site_name" content="@Model.SiteName" />
}

@* Twitter Card tags *@
@if (!string.IsNullOrEmpty(Model.TwitterCard))
{
    <meta name="twitter:card" content="@Model.TwitterCard" />
}

@if (!string.IsNullOrEmpty(Model.TwitterSite))
{
    <meta name="twitter:site" content="@Model.TwitterSite" />
}

@if (!string.IsNullOrEmpty(Model.TwitterCreator))
{
    <meta name="twitter:creator" content="@Model.TwitterCreator" />
}

@if (!string.IsNullOrEmpty(Model.TwitterTitle))
{
    <meta name="twitter:title" content="@Model.TwitterTitle" />
}

@if (!string.IsNullOrEmpty(Model.TwitterDescription))
{
    <meta name="twitter:description" content="@Model.TwitterDescription" />
}

@if (!string.IsNullOrEmpty(Model.TwitterImage))
{
    <meta name="twitter:image" content="@Model.TwitterImage" />
}

@if (Model.EnableSchema && Model.SchemaMarkups.Any())
{
    @foreach (var schema in Model.SchemaMarkups)
    {
        <script type="application/ld+json">
@Html.Raw(JsonConvert.SerializeObject(schema.Data, jsonSettings))
        </script>
    }
}