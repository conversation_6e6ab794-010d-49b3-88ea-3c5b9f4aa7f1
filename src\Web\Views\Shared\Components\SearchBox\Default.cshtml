@model MlSoft.Sites.Web.ViewModels.Components.SearchBoxComponentViewModel
@using MlSoft.Sites.Web.Helpers
@using MlSoft.Sites.Model.Configuration
@using MlSoft.Sites.Web.ViewModels.Components
@inject IStringLocalizer<SharedResource> SharedRes

@{
	// Extract data from ViewModel with null-safe defaults
	var title = string.IsNullOrEmpty(Model?.Title) ? "" : Model?.Title;
	var subtitle = string.IsNullOrEmpty(Model?.Subtitle) ? "" : Model?.Subtitle;
	var description = string.IsNullOrEmpty(Model?.Description) ? "" : Model?.Description;
	var layout = Model?.Layout ?? "horizontal";
	var showTitle = Model?.ShowTitle ?? true;
	var showSearchButton = Model?.ShowSearchButton ?? true;
	var showResetButton = Model?.ShowResetButton ?? true;
	var animationEnabled = Model?.AnimationEnabled ?? true;
	var searchButtonText = string.IsNullOrEmpty(Model?.SearchButtonText) ? SharedRes["Search"] : Model?.SearchButtonText;
	var resetButtonText = string.IsNullOrEmpty(Model?.ResetButtonText) ? SharedRes["Reset"] : Model?.ResetButtonText;
	var searchAction = Model?.SearchAction;
	var searchMethod = Model?.SearchMethod ?? "GET";
	var backgroundStyle = Model?.BackgroundStyle ?? "light";
	var borderRadius = Model?.BorderRadius ?? "medium";
	var showShadow = Model?.ShowShadow ?? true;
	var searchFields = Model?.SearchFields ?? new List<SearchField>();

	// Generate unique IDs for the component
	var uniqueId = JObjectHelper.GenerateId("searchbox");
	var formId = $"form_{uniqueId}";

	// Generate CSS classes based on properties (Flowbite/Tailwind defaults)
	var animationClass = animationEnabled ? "transition-all duration-300" : "";
	var layoutClass = layout == "horizontal" ? "flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4" : "flex flex-col space-y-4";
	var backgroundClass = GetBackgroundClass(backgroundStyle);
	var borderRadiusClass = GetBorderRadiusClass(borderRadius);
	var shadowClass = showShadow ? "shadow-lg" : "";

	// Helper methods for dynamic class generation
	string GetBackgroundClass(string style) => style switch
	{
		"light" => "bg-white dark:bg-gray-800",
		"dark" => "bg-gray-900 dark:bg-gray-900",
		"transparent" => "bg-transparent",
		_ => "bg-white dark:bg-gray-800"
	};

	string GetBorderRadiusClass(string radius) => radius switch
	{
		"small" => "rounded",
		"medium" => "rounded-lg",
		"large" => "rounded-xl",
		_ => "rounded-lg"
	};

	string GetFieldWidthClass(string width) => width switch
	{
		"full" => "w-full",
		"half" => "w-full md:w-1/2",
		"third" => "w-full md:w-1/3",
		_ => "w-full md:flex-1"
	};
}

	<div id="@uniqueId" class="search-box-component @backgroundClass @borderRadiusClass @shadowClass p-6 @animationClass">
	@if (showTitle && (!string.IsNullOrEmpty(title) || !string.IsNullOrEmpty(subtitle) || !string.IsNullOrEmpty(description)))
	{
		<div class="mb-6">
			@if (!string.IsNullOrEmpty(title))
			{
				<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">@title</h2>
			}
			@if (!string.IsNullOrEmpty(subtitle))
			{
				<h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">@subtitle</h3>
			}
			@if (!string.IsNullOrEmpty(description))
			{
				<p class="text-gray-600 dark:text-gray-400">@description</p>
			}
		</div>
	}

	<form id="@formId" action="@searchAction" method="@searchMethod" class="search-form">
		<div class="@layoutClass">
			@foreach (var field in searchFields.OrderBy(f => f.Order))
			{
				<div class="search-field @GetFieldWidthClass(field.Width ?? "auto")">
					@if (!string.IsNullOrEmpty(field.Label))
					{
						<label for="@field.Name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
							@field.Label
							@if (field.Required)
							{
								<span class="text-red-500 ml-1">*</span>
							}
						</label>
					}

					@if (field.Type == "select")
					{
						<select id="@field.Name" name="@field.Name"
								class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 @animationClass"
								@(field.Required ? "required" : "")>
							@if (!string.IsNullOrEmpty(field.Placeholder))
							{
								<option value="">@field.Placeholder</option>
							}
							@foreach (var fo in field.Options)
							{
								if (fo.Selected || fo.Value == field.DefaultValue)
								{
									<option value="@fo.Value" selected>
										@fo.Label
									</option>
								}
								else
								{
									<option value="@fo.Value">
										@fo.Label
									</option>
								}

							}
						</select>
					}
					else
					{
						<div class="relative">
							<input type="@field.Type" id="@field.Name" name="@field.Name"
								   value="@field.DefaultValue"
								   placeholder="@field.Placeholder"
								   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 pr-10 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 @animationClass"
								   @(field.Required ? "required" : "") />
							@if (field.Type == "text" && field.Name?.ToLower().Contains("search") == true)
							{
								<div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
									<svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
									</svg>
								</div>
							}
						</div>
					}
				</div>
			}

			@if (showSearchButton || showResetButton)
			{
				<div class="search-actions flex space-x-3 @(layout == "horizontal" ? "md:mt-0 mt-4 md:items-end" : "mt-4")">
					@if (showSearchButton)
					{
						<button type="submit"
								class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800 @animationClass">
							<svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
							</svg>
							@searchButtonText
						</button>
					}
					@if (showResetButton)
					{
						<button type="button" onclick="resetSearchForm('@formId')"
								class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600 @animationClass">
							<svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
							</svg>
							@resetButtonText
						</button>
					}
				</div>
			}
		</div>
	</form>
</div>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const searchBox = document.getElementById('@uniqueId');
		if (!searchBox) return;

		// Add entrance animation
		searchBox.style.opacity = '0';
		searchBox.style.transform = 'translateY(20px)';
		setTimeout(() => {
			searchBox.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
			searchBox.style.opacity = '1';
			searchBox.style.transform = 'translateY(0)';
		}, 100);

		// Reset form function with enhanced animation
		window.resetSearchForm = function(formId) {
			const form = document.getElementById(formId);
			if (form) {
				// Add reset animation
				form.style.transform = 'scale(0.98)';
				setTimeout(() => {
					form.reset();
					form.style.transform = 'scale(1)';
					form.style.transition = 'transform 0.2s ease-out';
				}, 100);

				// Reset select elements to their placeholder option
				const selects = form.querySelectorAll('select');
				selects.forEach(select => {
					if (select.options.length > 0 && select.options[0].value === '') {
						select.selectedIndex = 0;
					}
				});

				// Trigger change events for any listeners
				const inputs = form.querySelectorAll('input, select');
				inputs.forEach(input => {
					input.dispatchEvent(new Event('change', { bubbles: true }));
					// Add reset animation to fields
					input.style.transform = 'scale(0.95)';
					setTimeout(() => {
						input.style.transform = 'scale(1)';
						input.style.transition = 'transform 0.2s ease-out';
					}, 50);
				});
			}
		};

		// Enhanced form submission with validation and animations
		const form = searchBox.querySelector('form');
		if (form) {
			form.addEventListener('submit', function(e) {
				const requiredFields = form.querySelectorAll('[required]');
				let isValid = true;

				requiredFields.forEach(field => {
					if (!field.value.trim()) {
						isValid = false;
						// Enhanced error animation
						field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500', 'animate-pulse');
						field.classList.remove('border-gray-200', 'focus:border-blue-500', 'focus:ring-blue-500');
						
						// Shake animation
						field.style.animation = 'shake 0.5s ease-in-out';
						setTimeout(() => {
							field.style.animation = '';
						}, 500);
					} else {
						field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500', 'animate-pulse');
						field.classList.add('border-gray-200', 'focus:border-blue-500', 'focus:ring-blue-500');
					}
				});

				if (!isValid) {
					e.preventDefault();
					// Show validation message using Dialog system if available
					if (window.Dialog) {
						Dialog.alert('@SharedRes["PleaseCompleteRequiredFields"]', '@SharedRes["ValidationError"]');
					}
				} else {
					// Success animation
					const submitBtn = form.querySelector('button[type="submit"]');
					if (submitBtn) {
						submitBtn.style.transform = 'scale(0.95)';
						setTimeout(() => {
							submitBtn.style.transform = 'scale(1)';
						}, 150);
					}
				}
			});

			// Enhanced input interactions
			const inputs = form.querySelectorAll('input, select');
			inputs.forEach(input => {
				// Focus animation
				input.addEventListener('focus', function() {
					this.style.transform = 'scale(1.02)';
					this.style.transition = 'transform 0.2s ease-out';
				});

				// Blur animation
				input.addEventListener('blur', function() {
					this.style.transform = 'scale(1)';
				});

				// Input validation with real-time feedback
				input.addEventListener('input', function() {
					if (this.hasAttribute('required') && this.value.trim()) {
						this.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500', 'animate-pulse');
						this.classList.add('border-green-500', 'focus:border-green-500', 'focus:ring-green-500');
						
						// Success pulse animation
						this.style.animation = 'successPulse 0.6s ease-out';
						setTimeout(() => {
							this.style.animation = '';
							this.classList.remove('border-green-500', 'focus:border-green-500', 'focus:ring-green-500');
							this.classList.add('border-gray-200', 'focus:border-blue-500', 'focus:ring-blue-500');
						}, 600);
					}
				});
			});

			// Button hover effects
			const buttons = form.querySelectorAll('button');
			buttons.forEach(button => {
				button.addEventListener('mouseenter', function() {
					this.style.transform = 'translateY(-2px) scale(1.02)';
					this.style.transition = 'all 0.2s ease-out';
				});

				button.addEventListener('mouseleave', function() {
					this.style.transform = 'translateY(0) scale(1)';
				});
			});
		}
	});

	// Add custom CSS animations
	const style = document.createElement('style');
	style.textContent = `
		@@keyframes shake {
			0%, 100% { transform: translateX(0); }
			10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
			20%, 40%, 60%, 80% { transform: translateX(5px); }
		}
		
		@@keyframes successPulse {
			0% { transform: scale(1); }
			50% { transform: scale(1.05); }
			100% { transform: scale(1); }
		}
		
		.search-box-component {
			backdrop-filter: blur(10px);
		}
		
		.search-field:hover .group-hover\\:text-blue-600 {
			color: #2563eb;
		}
		
		.group\\/input:hover .group-hover\\/input\\:text-blue-500 {
			color: #3b82f6;
		}
	`;
	document.head.appendChild(style);
</script>
