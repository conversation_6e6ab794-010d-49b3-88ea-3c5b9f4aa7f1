{"ComponentId": "SearchBox", "Id": "<PERSON><PERSON><PERSON>", "Names": {"zh": "搜索框", "en": "Search Box", "ja": "検索ボックス"}, "Descriptions": {"zh": "可配置的搜索框组件，支持多种输入类型和下拉选择", "en": "Configurable search box component with multiple input types and dropdown selections", "ja": "複数の入力タイプとドロップダウン選択をサポートする設定可能な検索ボックスコンポーネント"}, "formFields": [{"name": "Title", "type": "multilingual-text", "label": "@FormResource:FormFields_Title", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_TitleHelpText"}, "validation": {"maxLength": 200}}, {"name": "Subtitle", "type": "multilingual-text", "label": "@FormResource:FormFields_Subtitle", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 2, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_SubtitleHelpText"}, "validation": {"maxLength": 300}}, {"name": "Description", "type": "multilingual-textarea", "label": "@FormResource:FormFields_Description", "display": {"group": "@SharedResource:FormGroups_BasicInfo", "width": "col-span-12", "order": 3, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_DescriptionHelpText"}, "validation": {"maxLength": 500}}, {"name": "SearchFields", "type": "repeater", "label": "@FormResource:FormFields_SearchFields", "display": {"group": "@FormResource:FormGroups_CustomFields", "width": "col-span-12", "order": 1, "collapsed": true, "helpText": "@FormResource:FormFields_SearchFieldsHelpText"}, "minItems": 1, "maxItems": 10, "template": {"fields": [{"name": "Name", "type": "text", "label": "@FormResource:FormFields_FieldName", "display": {"layout": "inline", "width": "col-span-6"}, "validation": {"required": true, "maxLength": 50}}, {"name": "Label", "type": "multilingual-text", "label": "@FormResource:CustomField_Label", "display": {"layout": "inline", "width": "col-span-6"}, "validation": {"maxLength": 100}}, {"name": "Type", "type": "select", "label": "@FormResource:FormFields_FieldType", "display": {"layout": "inline", "width": "col-span-4"}, "options": [{"value": "text", "label": "@FormResource:FormFields_TextInput"}, {"value": "select", "label": "@FormResource:FormFields_SelectDropdown"}], "defaultValue": "text"}, {"name": "Placeholder", "type": "multilingual-text", "label": "@FormResource:FormFields_Placeholder", "display": {"layout": "inline", "width": "col-span-4"}, "validation": {"maxLength": 100}}, {"name": "<PERSON><PERSON><PERSON>", "type": "select", "label": "@FormResource:FormFields_FieldWidth", "display": {"layout": "inline", "width": "col-span-4"}, "options": [{"value": "auto", "label": "@FormResource:FormFields_WidthAuto"}, {"value": "full", "label": "@FormResource:FormFields_WidthFull"}, {"value": "half", "label": "@FormResource:FormFields_WidthHalf"}, {"value": "third", "label": "@FormResource:FormFields_WidthThird"}], "defaultValue": "auto"}, {"name": "Required", "type": "checkbox", "label": "@FormResource:Form<PERSON>ields_Required", "display": {"layout": "inline", "width": "col-span-3"}, "defaultValue": false}, {"name": "Order", "type": "number", "label": "@FormResource:FormFields_Order", "display": {"layout": "inline", "width": "col-span-3"}, "defaultValue": 0, "validation": {"min": 0, "max": 99}}, {"name": "Options", "type": "repeater", "label": "@FormResource:FormFields_SelectOptions", "display": {"layout": "inline", "width": "col-span-12", "collapsed": true, "helpText": "@FormResource:FormFields_SelectOptionsHelpText"}, "minItems": 0, "maxItems": 20, "template": {"fields": [{"name": "Value", "type": "text", "label": "@FormResource:FormFields_OptionValue", "display": {"layout": "inline", "width": "col-span-6"}, "validation": {"required": true, "maxLength": 100}}, {"name": "Label", "type": "multilingual-text", "label": "@FormResource:FormFields_OptionLabel", "display": {"layout": "inline", "width": "col-span-6"}, "validation": {"required": true, "maxLength": 100}}]}}]}}, {"name": "Layout", "type": "select", "label": "@FormResource:FormFields_Layout", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 1, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_LayoutHelpText"}, "options": [{"value": "horizontal", "label": "@FormResource:FormFields_HorizontalLayout"}, {"value": "vertical", "label": "@FormResource:FormFields_VerticalLayout"}], "defaultValue": "horizontal"}, {"name": "BackgroundStyle", "type": "select", "label": "@FormResource:CompanyBasicInfo_BackgroundStyle", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 2, "layout": "inline", "collapsed": true}, "options": [{"value": "light", "label": "@FormResource:BreadcrumbBackgroundStyleLight"}, {"value": "dark", "label": "@FormResource:BreadcrumbBackgroundStyleDark"}, {"value": "transparent", "label": "@FormResource:FormFields_BackgroundColor_Transparent"}], "defaultValue": "light"}, {"name": "BorderRadius", "type": "select", "label": "@FormResource:FormFields_BorderRadius", "display": {"group": "@FormResource:FormGroups_Appearance", "width": "col-span-4", "order": 3, "layout": "inline", "collapsed": true}, "options": [{"value": "small", "label": "@FormResource:SmallBorderRadius"}, {"value": "medium", "label": "@FormResource:NormalBorderRadius"}, {"value": "large", "label": "@FormResource:LargeBorderRadius"}], "defaultValue": "medium"}, {"name": "ShowTitle", "type": "checkbox", "label": "@FormResource:ShowTitle", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-3", "order": 1, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowSearchButton", "type": "checkbox", "label": "@FormResource:FormFields_ShowSearchButton", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-3", "order": 2, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowResetButton", "type": "checkbox", "label": "@FormResource:FormFields_ShowResetButton", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-3", "order": 3, "layout": "inline", "collapsed": true}, "defaultValue": true}, {"name": "ShowShadow", "type": "checkbox", "label": "@FormResource:FormFields_ShowShadow", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-3", "order": 4, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_ShowShadowHelpText"}, "defaultValue": true}, {"name": "AnimationEnabled", "type": "checkbox", "label": "@FormResource:FormFields_AnimationEnabled", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-3", "order": 5, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_AnimationEnabledHelpText"}, "defaultValue": true}, {"name": "SearchButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_SearchButtonText", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-12", "order": 6, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 50}}, {"name": "ResetButtonText", "type": "multilingual-text", "label": "@FormResource:FormFields_ResetButtonText", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-12", "order": 7, "layout": "inline", "collapsed": true}, "validation": {"maxLength": 50}}, {"name": "SearchAction", "type": "text", "label": "@FormResource:FormFields_SearchAction", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-12", "order": 8, "layout": "inline", "collapsed": true, "helpText": "@FormResource:FormFields_SearchActionHelpText"}, "defaultValue": "/search", "validation": {"maxLength": 200}}, {"name": "SearchMethod", "type": "select", "label": "@FormResource:FormFields_SearchMethod", "display": {"group": "@FormResource:FormGroups_Settings", "width": "col-span-4", "order": 9, "layout": "inline", "collapsed": true}, "options": [{"value": "GET", "label": "GET"}, {"value": "POST", "label": "POST"}], "defaultValue": "GET"}]}