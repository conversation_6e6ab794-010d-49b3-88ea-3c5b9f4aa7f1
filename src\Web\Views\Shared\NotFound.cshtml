@using MlSoft.Sites.Web.Resources
@inject SharedResource SharedRes
@{
    ViewData["Title"] = SharedRes["Error404_Title"];
}

<div class="py-16 bg-white dark:bg-gray-900 flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- 404 Number -->
        <div>
            <h1 class="text-9xl font-bold text-primary-600 dark:text-primary-400">
                @SharedRes["Error404_Heading"]
            </h1>
        </div>
        
        <!-- Error Message -->
        <div class="space-y-4">
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                @SharedRes["Error404_Message"]
            </h2>
        </div>
        
        <!-- Back to Home Button -->
        <div class="mt-8">
            <a href="/" 
               class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-900 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                @SharedRes["Error404_BackToHome"]
            </a>
        </div>
    </div>
</div>