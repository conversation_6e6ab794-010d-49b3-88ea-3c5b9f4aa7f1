@using Newtonsoft.Json.Linq;

<!DOCTYPE html>
<html lang="@ViewData["CurrentLanguage"]">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - @AdminRes["BackendManagement"]</title>
    <link rel="stylesheet" href="~/css/output.css" asp-append-version="true">

    <!-- Language-specific Fonts -->
    @{
        var currentLanguage = ViewData["CurrentLanguage"]?.ToString() ?? System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
    }

    @if (currentLanguage == "ja")
    {
        <link rel="stylesheet" href="~/css/noto-sans-jp.css" asp-append-version="true" />
    }
    else
    {
        <link rel="stylesheet" href="~/css/noto-sans.css" asp-append-version="true" />
    }

    <link href="~/lib/font-awesome/font-awesome-7.0.1-local.css" rel="stylesheet" asp-append-version="true">
    
    <!-- Dark Mode Detection Script - Must run before page renders -->
    <script>
        // Load dark mode initialization if available, fallback to inline
        if (window.DarkModeInit) {
            DarkModeInit.init();
        } else {
            // Fallback inline initialization
            (function() {
                const savedTheme = localStorage.getItem('theme');
                const systemPrefersDark = window.matchMedia && 
                    window.matchMedia('(prefers-color-scheme: dark)').matches;
                const shouldUseDark = savedTheme === 'dark' || 
                    (!savedTheme && systemPrefersDark);
                if (shouldUseDark) {
                    document.documentElement.classList.add('dark');
                }
                window.__INITIAL_THEME__ = shouldUseDark ? 'dark' : 'light';
            })();
        }
    </script>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="min-h-screen flex">
        <!-- 侧边栏 -->
        <div class="bg-gray-800 dark:bg-gray-900 text-white w-64 min-h-screen px-4 py-8 sticky-sidebar">
            <div class="flex items-center space-x-2 mb-8">
                <i class="fas fa-cog text-2xl text-primary-400"></i>
                <h2 class="text-xl font-semibold">@AdminRes["BackendManagement"]</h2>
            </div>
            
            <nav class="space-y-2">
                <a asp-controller="Admin" asp-action="Dashboard" 
                   class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                    <i class="fas fa-tachometer-alt fa-fw"></i>
                    <span>@AdminRes["Dashboard"]</span>
                </a>
                
                <!-- 网站管理 -->
                <div class="pt-4">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">@AdminRes["WebsiteManage"]</h3>

                    <a asp-controller="Admin" asp-action="CompanyInfo"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-building fa-fw"></i>
                        <span>@AdminRes["CompanyInfoTitle"]</span>
                    </a>

                    <a asp-controller="Admin" asp-action="BusinessInfo"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-industry fa-fw"></i>
                        <span>@AdminRes["BusinessInfoTitle"]</span>
                    </a>

                    <a asp-controller="Admin" asp-action="AdminNews"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-newspaper fa-fw"></i>
                        <span>@AdminRes["NewsManagement"]</span>
                    </a>

                    <a asp-controller="Admin" asp-action="Recruitment"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-users fa-fw"></i>
                        <span>@AdminRes["RecruitmentManagement"]</span>
                    </a>
                    <a asp-controller="Admin" asp-action="AdminMessage"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-regular fa-message fa-fw"></i>
                        <span>@AdminRes["MessageManagement"]</span>
                    </a>

                    <a asp-controller="Admin" asp-action="PageManage"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-file-alt fa-fw"></i>
                        <span>@AdminRes["PageContentManage"]</span>
                    </a>
                    

                 </div>


                <!-- 系统设置 -->
                <div class="pt-4">
                     <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">@AdminRes["SystemSettings"]</h3>

                   

                     <a asp-controller="Admin" asp-action="SiteSettings" 
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-cog fa-fw"></i>
                        <span>@AdminRes["SiteSettings"]</span>
                    </a>
                    <a asp-controller="Admin" asp-action="Settings" 
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-tools fa-fw"></i>
                        <span>@AdminRes["AccountSettings"]</span>
                    </a>

                    <a asp-controller="Admin" asp-action="PageConfiguration"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-file-alt fa-fw"></i>
                        <span>@AdminRes["PageConfigurationTitle"]</span>
                    </a>

                </div>

                <!-- 其他 -->
                <div class="pt-4">
                    <a asp-controller="Home" asp-action="Index" target="_blank"
                       class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white">
                        <i class="fas fa-home fa-fw"></i>
                        <span>@AdminRes["FrontendPage"]</span>
                    </a>
                    <form asp-controller="Account" asp-action="Logout" method="post" class="inline">
                        <button type="submit" 
                                class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 text-gray-300 hover:text-white w-full text-left">
                            <i class="fas fa-sign-out-alt fa-fw"></i>
                            <span>@AdminRes["Logout"]</span>
                        </button>
                    </form>
                </div>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部栏 -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h1 class="text-2xl font-semibold text-gray-800 dark:text-white">@ViewData["Title"]</h1>
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-gray-600 dark:text-gray-300">
                            @AdminRes["WelcomeAdmin"]
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-6">
                @RenderBody()
            </main>
        </div>
    </div>



    <script>
         window.currentLanguagePrefix = "@ViewData["CurrentLanguage"].ToString()";
       // 获取当前语言前缀（从URL或全局变量中获取）
        function getCurrentLanguagePrefix() {
              return window.currentLanguagePrefix;
        }



    </script>
        <!-- Sortable.js for drag and drop -->
    <script src="~/lib/sortablejs/Sortable.min.js" asp-append-version="true"></script>
    <script src="~/js/vendor/flowbite.min.js" asp-append-version="true"></script>
    <script src="~/tinymce/tinymce.min.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/admin.js" asp-append-version="true"></script>

    <!-- 粘性侧边栏样式 -->
    <style>
        .sticky-sidebar {
            position: sticky;
            top: 0;
            height: 100vh;
            overflow-y: auto;
            align-self: flex-start;
        }

        /* 自定义滚动条样式 */
        .sticky-sidebar::-webkit-scrollbar {
            width: 4px;
        }

    </style>




    <!-- 多语言资源文件 -->
    <script src="~/js/resources/shared.@(ViewData["CurrentLanguage"]).js" asp-append-version="true"></script>
    <script src="~/js/resources/admin.@(ViewData["CurrentLanguage"]).js" asp-append-version="true"></script>
    <script src="~/js/resources/form.@(ViewData["CurrentLanguage"]).js" asp-append-version="true"></script>
    <script src="~/js/resources/account.@(ViewData["CurrentLanguage"]).js" asp-append-version="true"></script>

    <!-- 初始化 Dialog 系统 -->
    <script>


 // 设置支持的语言
        window.SupportedLanguages = @Html.Raw(Json.Serialize(
            ((SupportedLanguage[])ViewData["SupportedLanguages"]).Select(lang => new {
                code = lang.Code,
                name = lang.Name,
                emoji = lang.Emoji
            })
        ));

        // 集中初始化 Dialog 系统，使用全局资源
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Dialog !== 'undefined') {
                Dialog.init({
                    alert: window.Resources?.Shared?.Alert || 'Alert',
                    warning: window.Resources?.Shared?.Warning || 'Warning',
                    error: window.Resources?.Shared?.Error || 'Error',
                    information: window.Resources?.Shared?.Information || 'Information',
                    question: window.Resources?.Shared?.Question || 'Question',
                    confirm: window.Resources?.Shared?.Confirm || 'Confirm',
                    ok: window.Resources?.Shared?.OK || 'OK',
                    cancel: window.Resources?.Shared?.Cancel || 'Cancel',
                    yes: window.Resources?.Shared?.Yes || 'Yes',
                    no: window.Resources?.Shared?.No || 'No'
                });
            }
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>