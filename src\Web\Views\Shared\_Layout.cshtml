﻿@using MlSoft.Sites.Web.ViewModels.Components
<!DOCTYPE html>
<html lang="@ViewData["CurrentLanguage"]">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- SEO Component -->
    @* @await Component.InvokeAsync("SEO", new { 
        pageConfigurationId = ViewData["PageId"] as string,
        title = ViewData["Title"] as string
    }) *@
    
    <!-- Tailwind CSS (Local Build with Active Theme) -->
    <link rel="stylesheet" href="~/css/output.css" asp-append-version="true" />
    

    <link href="~/lib/font-awesome/font-awesome-7.0.1-local.css" rel="stylesheet" asp-append-version="true">
    <!-- Language-specific Fonts -->
    @{
        var currentLanguage = ViewData["CurrentLanguage"]?.ToString() ?? System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
    }
    
    @if (currentLanguage == "ja")
    {
        <link rel="stylesheet" href="~/css/noto-sans-jp.css" asp-append-version="true" />
    }
    else
    {
        <link rel="stylesheet" href="~/css/noto-sans.css" asp-append-version="true" />
    }
    
    <!-- Dark Mode Detection Script - Must run before page renders -->
    <script>
        // Load dark mode initialization if available, fallback to inline
        if (window.DarkModeInit) {
            DarkModeInit.init();
        } else {
            // Fallback inline initialization
            (function() {
                const savedTheme = localStorage.getItem('theme');
                const systemPrefersDark = window.matchMedia && 
                    window.matchMedia('(prefers-color-scheme: dark)').matches;
                const shouldUseDark = savedTheme === 'dark' || 
                    (!savedTheme && systemPrefersDark);
                if (shouldUseDark) {
                    document.documentElement.classList.add('dark');
                }
                window.__INITIAL_THEME__ = shouldUseDark ? 'dark' : 'light';
            })();
        }
    </script>
    
    @* 页面特定的 head 内容（如预加载标签） *@
    @await RenderSectionAsync("HeadPreLoad", required: false)
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header Component -->
    @await Component.InvokeAsync("Header")


    <!-- Main Content -->
    <main>
        @RenderBody()
    </main>


    <!-- Footer Component -->
    @await Component.InvokeAsync("Footer")
    @await Component.InvokeAsync("CookiePolicy")

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-8 right-8 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible z-50" aria-label="Back to top">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- Flowbite JavaScript -->
    <script src="~/js/vendor/flowbite.min.js" asp-append-version="true"></script>
    
    <!-- Custom JavaScript -->
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>