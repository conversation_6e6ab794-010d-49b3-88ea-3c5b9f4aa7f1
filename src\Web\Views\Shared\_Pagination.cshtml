@{
    // 分页参数处理和验证
    int CurrentPageIndex = ViewBag.CurrentPageIndex ?? 1;
    int PageSize = ViewBag.PageSize ?? 10;
    int TotalCount = ViewBag.TotalCount ?? 0;
    string Info = ViewBag.Info ?? "";

    // 页面URL生成函数（需要在使用组件的页面中定义）
    Func<int, string> PageUrl = ViewBag.PageUrl ?? new Func<int, string>(page => $"?page={page}");

    int diffCount = 2; // 当前页前后显示的页码数量


    // 分页显示控制
    bool ShowFirstLast = ViewBag.ShowFirstLast ?? true;
    bool ShowPrevNext = ViewBag.ShowPrevNext ?? true;
    bool ShowPageNumbers = ViewBag.ShowPageNumbers ?? true;
    bool ShowInfo = ViewBag.ShowInfo ?? true;
    bool ShowTotalCount = ViewBag.ShowTotalCount ?? true;

    int TotalPages = (int)Math.Ceiling(TotalCount / (double)PageSize);

    // 计算显示范围
    var startPage = Math.Max(1, CurrentPageIndex - diffCount);
    var endPage = Math.Min(TotalPages, CurrentPageIndex + diffCount);

    // 如果总页数小于等于显示范围，显示所有页码
    if (TotalPages <= (diffCount * 2 + 1))
    {
        startPage = 1;
        endPage = TotalPages;
    }
}

@if (TotalPages > 1 || ShowInfo)
{
    <div class="flex flex-col sm:flex-row justify-between items-center gap-4 mt-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">

        @if (ShowTotalCount && TotalCount > 0)
        {
            <!-- 总数信息 -->
            <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                <span>@SharedRes["Pagination_Total"]:</span>
                <span class="font-semibold text-blue-600 dark:text-blue-400">@TotalCount.ToString("N0")</span>
                <span>@SharedRes["Pagination_Records"]</span>
            </div>
        }

        @if (TotalPages > 1)
        {
            <!-- 分页导航 -->
            <nav class="flex items-center space-x-1" aria-label="@(SharedRes["Pagination_Navigation"])">

                @if (ShowFirstLast)
                {
                    <!-- 首页按钮 -->
                    @if (CurrentPageIndex == 1)
                    {
                        <button class="px-3 py-2 text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded cursor-not-allowed"
                                disabled
                                aria-label="@(SharedRes["Pagination_First"])">
                            ««
                        </button>
                    }
                    else
                    {
                        <a href="@PageUrl(1)"
                           class="px-3 py-2 text-gray-700 bg-white dark:bg-gray-800 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                           title="@(SharedRes["Pagination_First"])"
                           aria-label="@(SharedRes["Pagination_First"])">
                            ««
                        </a>
                    }
                }

                @if (ShowPrevNext)
                {
                    <!-- 上一页按钮 -->
                    @if (CurrentPageIndex == 1)
                    {
                        <button class="px-3 py-2 text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded cursor-not-allowed"
                                disabled
                                aria-label="@(SharedRes["Pagination_Prev"])">
                            «
                        </button>
                    }
                    else
                    {
                        <a href="@PageUrl(CurrentPageIndex - 1)"
                           class="px-3 py-2 text-gray-700 bg-white dark:bg-gray-800 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                           title="@(SharedRes["Pagination_Prev"])"
                           aria-label="@(SharedRes["Pagination_Prev"])">
                            «
                        </a>
                    }
                }

                @if (ShowPageNumbers)
                {
                    <!-- 省略号（开始） -->
                    @if (startPage > 1)
                    {
                        <span class="px-2 text-gray-400 dark:text-gray-500">...</span>
                    }

                    <!-- 页码按钮 -->
                    @for (int i = startPage; i <= endPage; i++)
                    {
                        @if (CurrentPageIndex == i)
                        {
                            <button class="px-3 py-2 text-white bg-blue-600 dark:bg-blue-500 rounded font-medium"
                                    aria-label="@($"{SharedRes["Pagination_Page"]}{i}")"
                                    aria-current="page">
                                @i
                            </button>
                        }
                        else
                        {
                            <a href="@PageUrl(i)"
                               class="px-3 py-2 text-gray-700 bg-white dark:bg-gray-800 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                               title="@($"{SharedRes["Pagination_Page"]}{i}")"
                               aria-label="@($"{SharedRes["Pagination_Page"]}{i}")">
                                @i
                            </a>
                        }
                    }

                    <!-- 省略号（结束） -->
                    @if (endPage < TotalPages)
                    {
                        <span class="px-2 text-gray-400 dark:text-gray-500">...</span>
                    }
                }

                @if (ShowPrevNext)
                {
                    <!-- 下一页按钮 -->
                    @if (CurrentPageIndex == TotalPages)
                    {
                        <button class="px-3 py-2 text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded cursor-not-allowed"
                                disabled
                                aria-label="@(SharedRes["Pagination_Next"])">
                            »
                        </button>
                    }
                    else
                    {
                        <a href="@PageUrl(CurrentPageIndex + 1)"
                           class="px-3 py-2 text-gray-700 bg-white dark:bg-gray-800 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                           title="@(SharedRes["Pagination_Next"])"
                           aria-label="@(SharedRes["Pagination_Next"])">
                            »
                        </a>
                    }
                }

                @if (ShowFirstLast)
                {
                    <!-- 末页按钮 -->
                    @if (CurrentPageIndex == TotalPages)
                    {
                        <button class="px-3 py-2 text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded cursor-not-allowed"
                                disabled
                                aria-label="@(SharedRes["Pagination_Last"])">
                            »»
                        </button>
                    }
                    else
                    {
                        <a href="@PageUrl(TotalPages)"
                           class="px-3 py-2 text-gray-700 bg-white dark:bg-gray-800 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                           title="@(SharedRes["Pagination_Last"])"
                           aria-label="@(SharedRes["Pagination_Last"])">
                            »»
                        </a>
                    }
                }
            </nav>
        }

        @if (ShowInfo && (!string.IsNullOrEmpty(Info) || TotalPages > 0))
        {
            <div class="flex items-center gap-3 text-sm">
                <!-- 自定义信息 -->
                @if (!string.IsNullOrEmpty(Info))
                {
                    <span class="text-amber-600 dark:text-amber-400">@Info</span>
                }

                <!-- 页码信息 -->
                @if (TotalPages > 0)
                {
                    <span class="text-gray-600 dark:text-gray-400">
                        @SharedRes["Pagination_Page"] @CurrentPageIndex @SharedRes["Pagination_Of"] @TotalPages @SharedRes["Pagination_Pages"]
                    </span>
                }
            </div>
        }
    </div>
}