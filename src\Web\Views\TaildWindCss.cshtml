﻿@*
    TailwindCSS 动态样式类收集文件
    此文件用于收集项目中动态拼接的CSS样式类，确保TailwindCSS编译时能够识别这些类
*@

<!-- Grid Column Span Classes -->
<div class="md:col-span-1"></div>
<div class="md:col-span-2"></div>
<div class="md:col-span-3"></div>
<div class="md:col-span-4"></div>
<div class="md:col-span-5"></div>
<div class="md:col-span-6"></div>
<div class="md:col-span-7"></div>
<div class="md:col-span-8"></div>
<div class="md:col-span-9"></div>
<div class="md:col-span-10"></div>
<div class="md:col-span-11"></div>
<div class="md:col-span-12"></div>

<!-- Additional responsive column span classes -->
<div class="sm:col-span-1 sm:col-span-2 sm:col-span-3 sm:col-span-4 sm:col-span-5 sm:col-span-6"></div>
<div class="lg:col-span-1 lg:col-span-2 lg:col-span-3 lg:col-span-4 lg:col-span-5 lg:col-span-6 lg:col-span-7 lg:col-span-8 lg:col-span-9 lg:col-span-10 lg:col-span-11 lg:col-span-12"></div>
<div class="xl:col-span-1 xl:col-span-2 xl:col-span-3 xl:col-span-4 xl:col-span-5 xl:col-span-6 xl:col-span-7 xl:col-span-8 xl:col-span-9 xl:col-span-10 xl:col-span-11 xl:col-span-12"></div>
<div class="2xl:col-span-1 2xl:col-span-2 2xl:col-span-3 2xl:col-span-4 2xl:col-span-5 2xl:col-span-6 2xl:col-span-7 2xl:col-span-8 2xl:col-span-9 2xl:col-span-10 2xl:col-span-11 2xl:col-span-12"></div>

<!-- Height Classes (100px to 800px, step 100) -->
<div class="h-100px"></div>
<div class="h-200px"></div>
<div class="h-300px"></div>
<div class="h-400px"></div>
<div class="h-500px"></div>
<div class="h-600px"></div>
<div class="h-700px"></div>
<div class="h-800px"></div>
