{"name": "web", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build-css": "pnpm exec tailwindcss -i ./wwwroot/css/input.css -o ./wwwroot/css/output.css --watch", "build-css-prod": "pnpm exec tailwindcss -i ./wwwroot/css/input.css -o ./wwwroot/css/output.css --minify", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}, "dependencies": {"flowbite": "^3.1.2"}}