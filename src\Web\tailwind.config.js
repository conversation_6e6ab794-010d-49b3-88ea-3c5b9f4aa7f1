/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./Views/**/*.cshtml",
        "./Views/*.cshtml",
        "./Helpers/*.cs",
        "./Views/**/*.json",
        "./wwwroot/**/*.js",
        "./node_modules/flowbite/**/*.js"
    ],
    darkMode: 'class',
    theme: {
        extend: {
            fontFamily: {
                'sans': ['Noto Sans', 'Noto Sans JP', 'ui-sans-serif', 'system-ui', 'sans-serif'],
            },
            colors: {
                'primary': {
                    50: 'var(--color-primary-50)',
                    100: 'var(--color-primary-100)',
                    200: 'var(--color-primary-200)',
                    300: 'var(--color-primary-300)',
                    400: 'var(--color-primary-400)',
                    500: 'var(--color-primary-500)',
                    600: 'var(--color-primary-600)',
                    700: 'var(--color-primary-700)',
                    800: 'var(--color-primary-800)',
                    900: 'var(--color-primary-900)',
                    950: 'var(--color-primary-950)',
                },
                'secondary': {
                    500: 'var(--color-secondary-500)',
                },
                'accent': {
                    500: 'var(--color-accent-500)',
                },
                'success': 'var(--color-success)',
                'warning': 'var(--color-warning)',
                'error': 'var(--color-error)',
                'info': 'var(--color-info)',
            }
        },
    },
    plugins: [
        require('flowbite/plugin')
    ],
}