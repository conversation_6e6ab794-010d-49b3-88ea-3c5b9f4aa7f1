/* Business Blue Theme - 商务蓝主题 */
:root {
  /* Primary Colors - 主色调 */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;
  
  /* Secondary Colors - 辅助色 */
  --color-secondary-500: #6b7280;
  --color-accent-500: #f59e0b;
  
  /* Semantic Colors - 语义色彩 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Light mode backgrounds and text */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border-color: #e5e7eb;
  
  /* Component Colors - 组件专用颜色 */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-hover: var(--color-primary-700);
  --button-primary-focus: var(--color-primary-500);
  --button-outline-border: var(--color-primary-600);
  --button-outline-text: var(--color-primary-600);
  --button-outline-hover-bg: var(--color-primary-50);
  
  --card-border: var(--color-primary-100);
  --card-shadow: rgba(37, 99, 235, 0.1);
  --link-color: var(--color-primary-600);
  --link-hover: var(--color-primary-700);
  
  /* Badge Colors - 徽章颜色 */
  --badge-success-bg: #dcfce7;
  --badge-success-text: #166534;
  --badge-error-bg: #fecaca;
  --badge-error-text: #991b1b;
  --badge-primary-bg: var(--color-primary-100);
  --badge-primary-text: var(--color-primary-800);

  /* Language Switcher Colors - 语言选择器颜色 */
  --language-switcher-button-bg: #f9fafb;
  --language-switcher-button-text: #6b7280;
  --language-switcher-button-hover-bg: #f3f4f6;
  --language-switcher-button-hover-text: #374151;
  --language-switcher-button-border: #e5e7eb;
  --language-switcher-button-focus-ring: rgba(37, 99, 235, 0.3);
  
  --language-switcher-dropdown-bg: #ffffff;
  --language-switcher-dropdown-border: #e5e7eb;
  --language-switcher-dropdown-shadow: rgba(0, 0, 0, 0.1);
  
  --language-switcher-item-text: #6b7280;
  --language-switcher-item-hover-bg: #f3f4f6;
  --language-switcher-item-hover-text: #374151;
  --language-switcher-item-active-bg: var(--color-primary-100);
  --language-switcher-item-active-text: var(--color-primary-800);
}

/* Dark mode overrides */
.dark {
  /* Dark mode primary colors - adjusted for better contrast */
  --color-primary-50: #1e3a8a;
  --color-primary-100: #1e40af;
  --color-primary-200: #1d4ed8;
  --color-primary-300: #2563eb;
  --color-primary-400: #3b82f6;
  --color-primary-500: #60a5fa;
  --color-primary-600: #93c5fd;
  --color-primary-700: #bfdbfe;
  --color-primary-800: #dbeafe;
  --color-primary-900: #eff6ff;
  --color-primary-950: #f8fafc;
  
  /* Dark mode backgrounds and text */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-color: #475569;
  
  /* Dark mode semantic colors */
  --color-success: #22c55e;
  --color-warning: #fbbf24;
  --color-error: #f87171;
  --color-info: #60a5fa;
  
  /* Dark mode component colors */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-hover: var(--color-primary-700);
  --button-primary-focus: var(--color-primary-500);
  --button-outline-border: var(--color-primary-600);
  --button-outline-text: var(--color-primary-600);
  --button-outline-hover-bg: rgba(147, 197, 253, 0.1);
  
  --card-border: var(--border-color);
  --card-shadow: rgba(0, 0, 0, 0.3);
  --link-color: var(--color-primary-400);
  --link-hover: var(--color-primary-300);
  
  /* Dark mode badge colors */
  --badge-success-bg: rgba(34, 197, 94, 0.2);
  --badge-success-text: #4ade80;
  --badge-error-bg: rgba(248, 113, 113, 0.2);
  --badge-error-text: #fca5a5;
  --badge-primary-bg: rgba(147, 197, 253, 0.2);
  --badge-primary-text: var(--color-primary-300);
  
  /* Dark mode language switcher colors */
  --language-switcher-button-bg: var(--bg-tertiary);
  --language-switcher-button-text: var(--text-secondary);
  --language-switcher-button-hover-bg: #475569;
  --language-switcher-button-hover-text: var(--text-primary);
  --language-switcher-button-border: var(--border-color);
  --language-switcher-button-focus-ring: rgba(147, 197, 253, 0.3);
  
  --language-switcher-dropdown-bg: var(--bg-secondary);
  --language-switcher-dropdown-border: var(--border-color);
  --language-switcher-dropdown-shadow: rgba(0, 0, 0, 0.5);
  
  --language-switcher-item-text: var(--text-secondary);
  --language-switcher-item-hover-bg: var(--bg-tertiary);
  --language-switcher-item-hover-text: var(--text-primary);
  --language-switcher-item-active-bg: rgba(147, 197, 253, 0.2);
  --language-switcher-item-active-text: var(--color-primary-300);
}

/* Theme-specific component styles */
.btn-theme-primary {
  background-color: var(--button-primary-bg);
  color: white;
  transition: background-color 0.2s ease;
}

.btn-theme-primary:hover {
  background-color: var(--button-primary-hover);
}

.btn-theme-primary:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
}

.dark .btn-theme-primary:focus {
  box-shadow: 0 0 0 3px rgba(147, 197, 253, 0.3);
}

.btn-theme-outline {
  border: 1px solid var(--button-outline-border);
  color: var(--button-outline-text);
  background-color: transparent;
  transition: all 0.2s ease;
}

.btn-theme-outline:hover {
  background-color: var(--button-outline-hover-bg);
}

.card-theme {
  border-color: var(--card-border);
  box-shadow: 0 1px 3px var(--card-shadow);
  background-color: var(--bg-primary);
}

.badge-theme-success {
  background-color: var(--badge-success-bg);
  color: var(--badge-success-text);
}

.badge-theme-error {
  background-color: var(--badge-error-bg);
  color: var(--badge-error-text);
}

.badge-theme-primary {
  background-color: var(--badge-primary-bg);
  color: var(--badge-primary-text);
}

.link-theme {
  color: var(--link-color);
}

.link-theme:hover {
  color: var(--link-hover);
}

/* Language Switcher Theme Styles - 语言选择器主题样式 */
.language-switcher-theme-button {
  background-color: var(--language-switcher-button-bg);
  color: var(--language-switcher-button-text);
  border-color: var(--language-switcher-button-border);
  transition: all 0.2s ease;
}

.language-switcher-theme-button:hover {
  background-color: var(--language-switcher-button-hover-bg);
  color: var(--language-switcher-button-hover-text);
}

.language-switcher-theme-button:focus {
  box-shadow: 0 0 0 3px var(--language-switcher-button-focus-ring);
}

.language-switcher-theme-dropdown {
  background-color: var(--language-switcher-dropdown-bg);
  border-color: var(--language-switcher-dropdown-border);
  box-shadow: 0 10px 15px -3px var(--language-switcher-dropdown-shadow);
}

.language-switcher-theme-item {
  color: var(--language-switcher-item-text);
  transition: all 0.2s ease;
}

.language-switcher-theme-item:hover {
  background-color: var(--language-switcher-item-hover-bg);
  color: var(--language-switcher-item-hover-text);
}

.language-switcher-theme-item.active {
  background-color: var(--language-switcher-item-active-bg);
  color: var(--language-switcher-item-active-text);
}

/* Dark mode general styles */
.dark body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.dark .bg-white {
  background-color: var(--bg-primary) !important;
}

.dark .bg-gray-50 {
  background-color: var(--bg-secondary) !important;
}

.dark .bg-gray-100 {
  background-color: var(--bg-tertiary) !important;
}

.dark .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .text-gray-500 {
  color: var(--text-tertiary) !important;
}

.dark .border-gray-200 {
  border-color: var(--border-color) !important;
}

.dark .border-gray-300 {
  border-color: var(--border-color) !important;
}