<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <radialGradient id="orangeGradient" cx="30%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#fff7ed"/>
      <stop offset="100%" style="stop-color:#ffedd5"/>
    </radialGradient>
  </defs>

  <rect width="200" height="200" fill="url(#orangeGradient)"/>

  <!-- Central sun/heart shape -->
  <circle cx="100" cy="100" r="45" fill="#f97316"/>
  <circle cx="100" cy="100" r="35" fill="#fb923c" opacity="0.9"/>
  <circle cx="100" cy="100" r="25" fill="#fdba74" opacity="0.8"/>

  <!-- Radiating warm rays -->
  <path d="M 100 20 L 105 50 L 95 50 Z" fill="#ea580c"/>
  <path d="M 100 180 L 105 150 L 95 150 Z" fill="#ea580c"/>
  <path d="M 20 100 L 50 105 L 50 95 Z" fill="#ea580c"/>
  <path d="M 180 100 L 150 105 L 150 95 Z" fill="#ea580c"/>

  <!-- Diagonal rays -->
  <path d="M 41.5 41.5 L 65 60 L 60 65 Z" fill="#c2410c"/>
  <path d="M 158.5 158.5 L 135 140 L 140 135 Z" fill="#c2410c"/>
  <path d="M 158.5 41.5 L 140 65 L 135 60 Z" fill="#c2410c"/>
  <path d="M 41.5 158.5 L 60 135 L 65 140 Z" fill="#c2410c"/>

  <!-- Warm embrace curves -->
  <path d="M 70 70 Q 100 50, 130 70 Q 100 90, 70 70" fill="none" stroke="#f97316" stroke-width="3" opacity="0.6"/>
  <path d="M 70 130 Q 100 150, 130 130 Q 100 110, 70 130" fill="none" stroke="#f97316" stroke-width="3" opacity="0.6"/>

  <!-- Small warm circles -->
  <circle cx="45" cy="60" r="6" fill="#fed7aa" opacity="0.8"/>
  <circle cx="155" cy="60" r="8" fill="#fdba74" opacity="0.7"/>
  <circle cx="45" cy="140" r="7" fill="#fed7aa" opacity="0.8"/>
  <circle cx="155" cy="140" r="5" fill="#fb923c" opacity="0.9"/>

  <!-- Hearts for warmth -->
  <path d="M 165 25 C 165 20, 170 15, 175 20 C 180 15, 185 20, 185 25 C 185 35, 175 40, 175 40 C 175 40, 165 35, 165 25 Z" fill="#f97316" opacity="0.7"/>
  <path d="M 15 170 C 15 167, 18 164, 21 167 C 24 164, 27 167, 27 170 C 27 176, 21 180, 21 180 C 21 180, 15 176, 15 170 Z" fill="#ea580c" opacity="0.8"/>
</svg>