(function () {
	if (!window.AdminTinyMCE) {
		window.AdminTinyMCE = {};
	}

	/**
	 * 初始化 TinyMCE（通用）
	 * @param {HTMLElement|string} textareaOrSelector 目标元素或选择器
	 * @param {object} config 可选配置
	 * @param {string} langCode 可选，字段目标语言（仅用于日志），UI 使用页面语言
	 */
	window.AdminTinyMCE.init = function (textareaOrSelector, config, langCode) {
		if (typeof tinymce === 'undefined') {
			console.warn('TinyMCE is not loaded');
			return;
		}

		var targetEl = null;
		var selector = null;

		if (typeof textareaOrSelector === 'string') {
			selector = textareaOrSelector;
		} else if (textareaOrSelector && textareaOrSelector.nodeType === 1) {
			targetEl = textareaOrSelector;
			if (!targetEl.id) {
				// 生成安全 id，供 TinyMCE 使用
				targetEl.id = ('tmce_' + (targetEl.name || 'editor_' + Date.now())).replace(/[\[\]\.\s]/g, '_');
			}
		} else {
			console.warn('AdminTinyMCE.init: invalid target');
			return;
		}

		var currentLang = window.currentLanguage || document.documentElement.lang || 'zh';
		var isDarkMode = document.documentElement.classList.contains('dark') || document.body.classList.contains('dark');

		// 调试语言信息（与原实现保持一致）
		try {
			console.log('TinyMCE Language Detection Debug:', {
				langCode: langCode,
				windowCurrentLanguage: window.currentLanguage,
				documentLang: document.documentElement.lang,
				finalCurrentLang: currentLang,
				targetId: targetEl ? targetEl.id : selector,
				note: 'Editor UI language uses page language, not field target language'
			});
		} catch (_) { }

		var defaultConfig = {
			license_key: 'gpl',
			height: config && typeof config.height !== 'undefined' ? config.height : 200,
			menubar: config && typeof config.menubar !== 'undefined' ? config.menubar : false,
			plugins: (config && config.plugins) || ['lists', 'link', 'image', 'code'],
			toolbar: (config && config.toolbar) || 'undo redo | formatselect | bold italic underline | bullist numlist | link image | code',
			branding: config && typeof config.branding !== 'undefined' ? config.branding : false,
			language: currentLang === 'zh' ? 'zh_CN' : (currentLang === 'ja' ? 'ja' : 'en'),
			language_url: currentLang === 'zh' ? '/tinymce/langs/zh_CN.js' : (currentLang === 'ja' ? '/tinymce/langs/ja.js' : undefined),
			block_formats: currentLang === 'zh'
				? '段落=p; 标题 1=h1; 标题 2=h2; 标题 3=h3; 标题 4=h4; 标题 5=h5; 标题 6=h6; 预格式化=pre'
				: (currentLang === 'ja'
					? '段落=p; 見出し 1=h1; 見出し 2=h2; 見出し 3=h3; 見出し 4=h4; 見出し 5=h5; 見出し 6=h6; 整形済みテキスト=pre'
					: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre'),
			content_style: 'body {\n\tfont-family: -apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,\"Noto Sans\",sans-serif;\n\tfont-size: 14px;\n\tline-height: 1.5;\n\tbackground-color: ' + (isDarkMode ? '#374151' : '#ffffff') + ';\n\tcolor: ' + (isDarkMode ? '#f3f4f6' : '#1f2937') + ';\n}',
			skin: isDarkMode ? 'oxide-dark' : 'oxide',
			content_css: isDarkMode ? 'dark' : 'default'
		};

		var finalConfig = Object.assign({}, defaultConfig, config || {});

		if (targetEl) {
			finalConfig.target = targetEl;
			// 确保 selector 不与 target 同时存在
			delete finalConfig.selector;
		} else if (selector) {
			finalConfig.selector = selector;
		}

		try {
			// 如果以 target 方式初始化，且存在同 id 的实例，先销毁
			if (targetEl && typeof tinymce.get === 'function') {
				var existing = tinymce.get(targetEl.id);
				if (existing) {
					existing.remove();
				}
			}
			tinymce.init(finalConfig);
		} catch (e) {
			console.error('AdminTinyMCE.init failed:', e);
		}
	};

	/**
	 * 批量初始化 TinyMCE
	 * @param {string} selector 选择器（默认 textarea.tinymce-editor）
	 * @param {object} config 可选配置
	 */
	window.AdminTinyMCE.initAll = function (selector, config) {
		var sel = selector || 'textarea.tinymce-editor';
		if (typeof tinymce === 'undefined') {
			console.warn('TinyMCE is not loaded');
			return;
		}
		window.AdminTinyMCE.init(sel, config || {});
	};


	if (!window.AdminUpload) {
		window.AdminUpload = {};
	}

	/**
	 * 绑定文件上传行为到指定的 input[type="file"]
	 * @param {HTMLInputElement} fileInput
	 * @param {object} options
	 * @param {HTMLElement} options.fileListEl 文件列表容器
	 * @param {object} options.fileConfig {types, multiple, folder, maxSize, preview}
	 * @param {function} options.getResource i18n 函数 (key, fallback, ns)
	 */
	window.AdminUpload.bind = function (fileInput, options) {
		var opts = options || {};
		var fileListEl = opts.fileListEl;
		var fileConfig = opts.fileConfig || {};
		var getRes = typeof opts.getResource === 'function' ? opts.getResource : function (k, fb) { return fb || k; };
		var hiddenSelector = opts.hiddenInputSelector;

		if (!fileInput || !fileListEl) {
			console.warn('AdminUpload.bind: invalid arguments');
			return;
		}

		var alreadyBound = fileInput.dataset && fileInput.dataset.uploadBound === '1';
		if (!alreadyBound) {
			var dropZone = fileInput.parentElement ? fileInput.parentElement.querySelector('label') : null;

			fileInput.addEventListener('change', function (e) {
				var files = Array.prototype.slice.call(e.target.files || []);
				files.forEach(function (f) { uploadSingleFile(f); });
			});

			if (dropZone) {
				dropZone.addEventListener('dragover', function (e) {
					e.preventDefault();
					dropZone.classList.add('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
				});
				dropZone.addEventListener('dragleave', function (e) {
					e.preventDefault();
					dropZone.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
				});
				dropZone.addEventListener('drop', function (e) {
					e.preventDefault();
					dropZone.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
					var files = Array.prototype.slice.call(e.dataTransfer && e.dataTransfer.files || []);
					files.forEach(function (f) { uploadSingleFile(f); });
				});
			}

			fileInput.dataset.uploadBound = '1';
		}

		function uploadSingleFile(file) {
			var progressEl = createProgressItem(file.name);
			fileListEl.appendChild(progressEl);

			var formData = new FormData();
			formData.append('file', file);
			formData.append('folder', fileConfig.folder || 'default');
			formData.append('allowedTypes', (fileConfig.types || []).join(','));
			formData.append('maxSize', fileConfig.maxSize || '10MB');

			fetch('/api/FileUpload/upload', { method: 'POST', body: formData })
				.then(function (res) { if (!res.ok) return res.json().then(function (e) { throw new Error(e && e.error || 'Upload failed'); }); return res.json(); })
				.then(function (result) {
					fileListEl.removeChild(progressEl);
					var item = createFileItem(result, fileConfig);
					fileListEl.appendChild(item);
				})
				.catch(function (err) {
					try { fileListEl.removeChild(progressEl); } catch (_) { }
					console.error('File upload error:', err);
					if (typeof Dialog !== 'undefined' && Dialog.error) {
						Dialog.error((getRes('FileUpload_UploadError', 'Upload failed', 'shared')) + ': ' + file.name);
					}
				});
		}

		function createProgressItem(fileName) {
			var item = document.createElement('div');
			item.className = 'flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700';
			item.innerHTML = '' +
				'<div class="flex-1">' +
					'<p class="text-sm font-medium text-gray-900 dark:text-white">' + fileName + '</p>' +
					'<div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700 mt-1">' +
						'<div class="bg-primary-600 h-2 rounded-full animate-pulse" style="width: 45%"></div>' +
					'</div>' +
				'</div>' +
				'<div class="ml-3">' +
					'<div class="animate-spin w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full"></div>' +
				'</div>';
			return item;
		}

		function createFileItem(fileInfo, cfg) {
			var item = document.createElement('div');
			item.className = 'flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm';
			item.dataset.filePath = fileInfo.filePath;

			var icon = getFileIcon(fileInfo.contentType || '');
			var previewBtn = (cfg && cfg.preview && isPreviewable(fileInfo.contentType || ''))
				? ('<button type="button" class="p-1 text-primary-600 hover:text-primary-700 dark:text-primary-400" onclick="window.open(\'' + fileInfo.filePath + '\', \'_blank\')" title="' + getRes('FileUpload_Preview', 'Preview', 'shared') + '"><i class="fas fa-eye"></i></button>')
				: '';

			item.innerHTML = '' +
				'<div class="flex-shrink-0 mr-3">' +
					'<i class="' + icon + ' text-2xl text-gray-500"></i>' +
				'</div>' +
				'<div class="flex-1 min-w-0">' +
					'<p class="text-sm font-medium text-gray-900 dark:text-white truncate">' + (fileInfo.originalName || '') + '</p>' +
				'</div>' +
				'<div class="flex items-center space-x-2">' +
					previewBtn +
					'<button type="button" class="p-1 text-red-600 hover:text-red-700 dark:text-red-400" data-action="delete" title="' + getRes('FileUpload_DeleteFile', 'Delete file', 'shared') + '"><i class="fas fa-trash"></i></button>' +
				'</div>';

			var delBtn = item.querySelector('[data-action="delete"]');
			delBtn.addEventListener('click', function () { doDelete(fileInfo.filePath, item); });
			return item;
		}

		// Expose a helper to render initial files using the same UI
		if (!window.AdminUpload.renderInitialFiles) {
			window.AdminUpload.renderInitialFiles = function (fileListElParam, fileConfigParam, files) {
				try {
					var targetList = fileListElParam || fileListEl;
					var cfg = fileConfigParam || fileConfig;
					if (!targetList || !Array.isArray(files)) return;
					files.forEach(function (f) {
						var info = (typeof f === 'string')
							? { filePath: f, originalName: (f.split('/').pop() || 'uploaded-file'), fileSize: 0, contentType: (cfg && cfg.types && cfg.types[0]) || '' }
							: f;
						// Skip if already present
						if (targetList.querySelector('[data-file-path="' + info.filePath + '"]')) return;
						var item = createFileItem(info, cfg);
						targetList.appendChild(item);
					});
				} catch (_) { }
			};
		}

		function doDelete(filePath, fileElement) {
			var proceed = Promise.resolve(true);
			if (typeof Dialog !== 'undefined' && Dialog.confirm) {
				proceed = Dialog.confirm(getRes('FileUpload_DeleteConfirm', 'Are you sure you want to delete this file?', 'shared'), getRes('Confirm', 'Confirm', 'shared'));
			}
			proceed.then(function (ok) {
				if (!ok) return;
				return fetch('/api/FileUpload/delete?filePath=' + encodeURIComponent(filePath), { method: 'DELETE' })
					.then(function (res) { if (!res.ok) return res.json().then(function (e) { throw new Error(e && e.error || 'Delete failed'); }); })
					.then(function () {
						fileElement.remove();
						if (typeof Dialog !== 'undefined' && Dialog.notify) {
							Dialog.notify(getRes('FileUpload_DeleteSuccess', 'File deleted successfully', 'shared'), 'success');
						}
					})
					.catch(function (err) {
						console.error('File delete error:', err);
						if (typeof Dialog !== 'undefined' && Dialog.error) {
							Dialog.error((getRes('FileUpload_DeleteError', 'Delete failed', 'shared')) + ': ' + err.message);
						}
					});
			});
		}

		function getFileIcon(contentType) {
			if ((contentType || '').indexOf('image/') === 0) return 'fas fa-image text-primary-500';
			if ((contentType || '').indexOf('video/') === 0) return 'fas fa-video text-primary-600';
			if ((contentType || '').indexOf('pdf') !== -1) return 'fas fa-file-pdf text-error-500';
			if ((contentType || '').indexOf('word') !== -1 || (contentType || '').indexOf('document') !== -1) return 'fas fa-file-word text-primary-700';
			return 'fas fa-file text-gray-500';
		}

		function isPreviewable(contentType) {
			return (contentType || '').indexOf('image/') === 0 || (contentType || '').indexOf('pdf') !== -1 || (contentType || '').indexOf('video/') === 0;
		}

		function formatFileSize(bytes) {
			if (!bytes) return '0 Bytes';
			var k = 1024;
			var sizes = ['Bytes', 'KB', 'MB', 'GB'];
			var i = Math.floor(Math.log(bytes) / Math.log(k));
			return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
		}

		// Prefill from options.initialFiles or hidden input on every bind call (idempotent)
		try {
			var initialFiles = [];
			if (Array.isArray(opts.initialFiles)) initialFiles = opts.initialFiles;
			else if (typeof opts.initialFiles === 'function') initialFiles = opts.initialFiles() || [];
			if ((!initialFiles || initialFiles.length === 0) && hiddenSelector) {
				var hiddenEl = document.querySelector(hiddenSelector);
				if (hiddenEl && hiddenEl.value) initialFiles = [hiddenEl.value];
			}
			if (initialFiles && initialFiles.length > 0 && window.AdminUpload.renderInitialFiles) {
				window.AdminUpload.renderInitialFiles(fileListEl, fileConfig, initialFiles);
			}
		} catch (_) { }

		// Sync hidden input value from the first file item
		try {
			if (hiddenSelector && !fileInput.dataset.hiddenSyncAttached) {
				var hiddenElForSync = document.querySelector(hiddenSelector);
				if (hiddenElForSync) {
					var syncHidden = function () {
						var first = fileListEl.querySelector('[data-file-path]');
						hiddenElForSync.value = first ? first.getAttribute('data-file-path') : '';
					};
					var observer = new MutationObserver(syncHidden);
					observer.observe(fileListEl, { childList: true });
					syncHidden();
					fileInput.dataset.hiddenSyncAttached = '1';
				}
			}
		} catch (_) { }
	};


	/**
	 * Simple Image Upload Component
     * @param {Object} config - Configuration object
     * @param {string} config.container - Container selector or element where the uploader will be rendered
     * @param {string} config.name - Input name attribute (default: 'imageFile')
     * @param {string} config.value - Initial image URL
     * @param {string} config.fileName - Initial file name (stored in a hidden input)
	 * @param {string} config.maxWidth - Maximum width of the image
	 * @param {string} config.maxHeight - Maximum height of the image
     * @param {string} config.accept - Accept attribute for file input (default: 'image/*')
     * @param {string} config.uploadText - Upload button text (default: 'Click to upload')
     * @param {function} config.onChange - Callback when image is selected (receives {file, previewUrl, fileName})
	 * @param {function} config.onUploaded - Callback when image is uploaded (receives {file, previewUrl, fileName, result})
     * @param {function} config.onError - Callback when image upload fails (receives {error})
     */
    window.SimpleImageUpload = function (config) {
        // Default configuration
        const defaults = {
            name: 'imageFile',
            value: '',
            fileName: '',
            maxWidth: 1024,
            maxHeight: 1024,
            accept: 'image/*',
            uploadText: 'Click to upload',
            // Built-in upload options
            autoUpload: false,
            uploadUrl: '/api/FileUpload/upload',
            folder: 'default',
            allowedTypes: 'image/*',
            maxSize: '10MB',
            hiddenInputSelector: null,
            onUploaded: function() {
                if (typeof Dialog !== 'undefined' && Dialog.notify) {
                    Dialog.notify((window.Resources?.Shared?.FileUpload_UploadSuccess) || 'Upload success', 'success');
                }
            },
            onError: function(err) {
                console.error('Executive photo upload error:', err);
                if (typeof Dialog !== 'undefined' && Dialog.error) {
                    Dialog.error((window.Resources?.Shared?.FileUpload_UploadError) || 'Upload failed');
                }
			},
			onChange: function(file, previewUrl, fileName) {

			}
        };

        // Merge defaults with user config
        const options = { ...defaults, ...config };
        
        // Get container element
        let container = typeof options.container === 'string' 
            ? document.querySelector(options.container) 
            : options.container;
            
        if (!container) {
            console.error('SimpleImageUpload: Container element not found');
            return null;
        }

        // Generate unique IDs
        const id = 'siu_' + Math.random().toString(36).substr(2, 9);
        const fileInputId = id + '_input';
        const previewId = id + '_preview';
        const fileNameInputId = id + '_filename';

        // Create HTML structure
        container.innerHTML = `
            <div class="space-y-3">
                <div class="flex items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                     id="${id}_dropzone">
                    <div class="text-center" id="${previewId}">
                        ${options.value ? 
                            `<img src="${options.value}" alt="Preview" class="max-h-28 mx-auto rounded">` : 
                            `<div class="flex flex-col items-center">
                                <svg class="w-8 h-8 mb-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="text-sm text-gray-500">${options.uploadText}</p>
                            </div>`
                        }
                    </div>
                </div>
                <input type="file" id="${fileInputId}" name="${options.name}" 
                       accept="${options.accept}" class="hidden">
                <input type="hidden" id="${fileNameInputId}" name="${options.name}FileName" 
                       value="${options.fileName || ''}">
            </div>
        `;

        // Get elements
        const dropzone = document.getElementById(`${id}_dropzone`);
        const fileInput = document.getElementById(fileInputId);
        const previewEl = document.getElementById(previewId);
        const fileNameInput = document.getElementById(fileNameInputId);

        // Handle file selection
        const handleFileSelect = (file) => {
            if (!file) return;

            const reader = new FileReader();
            
            reader.onload = function(e) {
                const previewUrl = e.target.result;
                const fileName = file.name;
                
                // Update preview
                previewEl.innerHTML = `<img src="${previewUrl}" alt="Preview" class="max-h-28 mx-auto rounded">`;
                
                // Update hidden input
                fileNameInput.value = fileName;
                
                // Call onChange callback if provided
                if (typeof options.onChange === 'function') {
                    options.onChange({ file, previewUrl, fileName });
                }

                // Auto upload if enabled
                if (options.autoUpload && options.uploadUrl) {
                    try {
                        const formData = new FormData();
                        formData.append('file', file);
                        formData.append('folder', options.folder || 'default');
                        formData.append('allowedTypes', options.allowedTypes || 'image/*');
                        formData.append('maxSize', options.maxSize || '10MB');
                        formData.append('maxWidth', options.maxWidth || '0');
                        formData.append('maxHeight', options.maxHeight || '0');

                        fetch(options.uploadUrl, { method: 'POST', body: formData })
                            .then(function (res) { if (!res.ok) return res.json().then(function (e) { throw new Error((e && e.error) || 'Upload failed'); }); return res.json(); })
                            .then(function (result) {
                                const uploadedPath = result && (result.filePath || result.url || '');
                                // Sync to hidden input if provided
                                if (options.hiddenInputSelector) {
                                    const hiddenEl = document.querySelector(options.hiddenInputSelector);
                                    if (hiddenEl) hiddenEl.value = uploadedPath || '';
                                }
                                if (typeof options.onUploaded === 'function') {
                                    options.onUploaded({ file, previewUrl, fileName, result });
                                }
                            })
                            .catch(function (err) {
                                if (typeof options.onError === 'function') options.onError(err);
                                else console.error('SimpleImageUpload upload error:', err);
                            });
                    } catch (err) {
                        if (typeof options.onError === 'function') options.onError(err);
                        else console.error('SimpleImageUpload upload init error:', err);
                    }
                }
            };
            
            reader.readAsDataURL(file);
        };

        // Click handler for dropzone
        dropzone.addEventListener('click', () => fileInput.click());

        // Handle drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropzone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropzone.classList.add('border-blue-500', 'bg-blue-50');
        }

        function unhighlight() {
            dropzone.classList.remove('border-blue-500', 'bg-blue-50');
        }

        // Handle dropped files
        dropzone.addEventListener('drop', (e) => {
            const dt = e.dataTransfer;
            const files = dt.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // Handle file input change
        fileInput.addEventListener('change', (e) => {
            if (fileInput.files && fileInput.files[0]) {
                handleFileSelect(fileInput.files[0]);
            }
        });

        // Public methods
        return {
            // Get current file
            getFile: () => fileInput.files[0],
            
            // Get current file name
            getFileName: () => fileNameInput.value,
            
            // Clear the upload
            clear: () => {
                fileInput.value = '';
                fileNameInput.value = '';
                previewEl.innerHTML = `
                    <div class="flex flex-col items-center">
                        <svg class="w-8 h-8 mb-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <p class="text-sm text-gray-500">${options.uploadText}</p>
                    </div>
                `;
            }
		};
	};	

})();


function getLanguagePrefix() {
    return window.currentLanguagePrefix ? `/${window.currentLanguagePrefix}` : '';
}

/**
 * 生成多语言URL
 */
function buildMultilingualUrl(action, controller) {
    const langPrefix = getLanguagePrefix();
    return `${langPrefix}/Admin/${controller}/${action}`;
}

// ==================== 通用多语言切换系统 ====================

/**
 * 内部多语言切换函数（避免递归调用）
 * @param {string} lang - 语言代码
 * @param {string|HTMLElement} context - 上下文选择器或DOM元素
 * @param {Object} options - 配置选项
 */
window.switchLanguageTabInternal = function(lang, context = null, options = {}) {
    // 默认配置
    const config = {
        buttonClass: 'lang-tab-button',
        contentClass: 'lang-content',
        activeClasses: ['active', 'border-primary-500', 'text-primary-600'],
        inactiveClasses: ['border-transparent', 'text-gray-500', 'dark:text-gray-400'],
        contentIdPrefix: 'lang-',
        ...options
    };

    // 确定上下文
    let contextElement = null;
    
    if (typeof context === 'string') {
        contextElement = document.querySelector(context);
    } else if (context instanceof HTMLElement) {
        contextElement = context;
    } else {
        // 自动检测上下文
        contextElement = findLanguageContext();
    }

    if (!contextElement) {
        console.warn('Language tab context not found for language:', lang);
        return false;
    }

    // 切换按钮状态
    switchLanguageButtons(contextElement, lang, config);
    
    // 切换内容显示
    switchLanguageContent(contextElement, lang, config);
    
    return true;
};

/**
 * 公共多语言切换函数
 * @param {string} lang - 语言代码
 * @param {string|HTMLElement} context - 上下文选择器或DOM元素
 * @param {Object} options - 配置选项
 */
window.switchLanguageTab = function(lang, context = null, options = {}) {
    return window.switchLanguageTabInternal(lang, context, options);
};

/**
 * 自动检测多语言上下文
 */
function findLanguageContext() {
    // 按优先级检查可能的上下文
    const possibleContexts = [
        // 模态框
        '#historyModal',
        '#companyInfoModal', 
        '#contactInfoModal',
        '#locationModal',
        '#csrActivityModal',
        '#financialReportModal',
        '#shareholderMeetingModal',
        '#executiveModal',
        '#organizationModal',
        '#businessDivisionModal',
        '#productServiceModal',
        '#employeeInterviewModal',
        '#jobPositionModal',
        '#newsModal',
        '#mapLinkModal',
        // 标签页内容
        '.tab-content.active',
        '.tab-pane.active',
        // 当前可见的容器
        '.modal:not(.hidden)',
        '.tab-content:not(.hidden)',
        // 页面主体
        'main',
        '#main-content',
        'body'
    ];
    
    // 支持的语言按钮类名
    const languageButtonSelectors = [
        '.lang-tab-button[data-lang]',
        '.executive-lang-tab-button[data-lang]',
        '.organization-lang-tab-button[data-lang]',
        '.company-info-lang-tab-button[data-lang]',
        '.contact-lang-tab-button[data-lang]',
        '.location-lang-tab-button[data-lang]',
        '.csr-lang-tab-button[data-lang]',
        '.financial-lang-tab-button[data-lang]',
        '.meeting-lang-tab-button[data-lang]',
        '.interview-modal-lang-tab-button[data-lang]',
        '.job-modal-lang-tab-button[data-lang]',
        '.product-lang-tab-button[data-lang]',
        '.language-tab[data-lang]'
    ];
    
    for (const selector of possibleContexts) {
        const element = document.querySelector(selector);
        if (element) {
            // 检查是否包含任何类型的语言按钮
            for (const buttonSelector of languageButtonSelectors) {
                if (element.querySelector(buttonSelector)) {
                    return element;
                }
            }
        }
    }
    
    return null;
}

/**
 * 切换语言按钮状态
 */
function switchLanguageButtons(context, lang, config) {
    // 移除所有按钮的激活状态
    context.querySelectorAll(`.${config.buttonClass}`).forEach(btn => {
        btn.classList.remove(...config.activeClasses);
        btn.classList.add(...config.inactiveClasses);
    });

    // 激活选中的按钮
    const activeBtn = context.querySelector(`.${config.buttonClass}[data-lang="${lang}"]`);
    if (activeBtn) {
        activeBtn.classList.add(...config.activeClasses);
        activeBtn.classList.remove(...config.inactiveClasses);
    }
}

/**
 * 切换语言内容显示
 */
function switchLanguageContent(context, lang, config) {
    // 隐藏所有语言内容
    context.querySelectorAll(`.${config.contentClass}`).forEach(content => {
        content.classList.add('hidden');
    });

    // 显示选中的语言内容
    const activeContent = context.querySelector(`#${config.contentIdPrefix}${lang}`);
    if (activeContent) {
        activeContent.classList.remove('hidden');
        
        // 触发内容显示事件
        activeContent.dispatchEvent(new CustomEvent('languageContentShown', {
            detail: { language: lang, context: context }
        }));
    }
}

/**
 * 为特定上下文创建专用的语言切换函数
 * @param {string} contextSelector - 上下文选择器
 * @param {Object} options - 配置选项
 */
window.createLanguageSwitcher = function(contextSelector, options = {}) {
    return function(lang) {
        return window.switchLanguageTab(lang, contextSelector, options);
    };
};

/**
 * 获取当前激活的语言
 * @param {string|HTMLElement} context - 上下文选择器或DOM元素
 */
window.getCurrentLanguage = function(context = null) {
    const contextElement = context ? 
        (typeof context === 'string' ? document.querySelector(context) : context) : 
        findLanguageContext();
    
    if (!contextElement) return null;
    
    const activeButton = contextElement.querySelector('.lang-tab-button.active, .lang-tab-button[class*="active"]');
    return activeButton ? activeButton.getAttribute('data-lang') : null;
};

/**
 * 设置默认语言
 * @param {string} lang - 语言代码
 * @param {string|HTMLElement} context - 上下文选择器或DOM元素
 */
window.setDefaultLanguage = function(lang, context = null) {
    const contextElement = context ? 
        (typeof context === 'string' ? document.querySelector(context) : context) : 
        findLanguageContext();
    
    if (!contextElement) return false;
    
    // 检查该语言是否存在
    const langButton = contextElement.querySelector(`.lang-tab-button[data-lang="${lang}"]`);
    if (!langButton) return false;
    
    // 切换到该语言
    return window.switchLanguageTab(lang, contextElement);
};

/**
 * 初始化页面中的所有多语言切换
 */
window.initLanguageSwitchers = function() {
    // 为所有语言按钮添加点击事件
    document.addEventListener('click', function(e) {
        if (e.target.matches('.lang-tab-button[data-lang]')) {
            e.preventDefault();
            const lang = e.target.getAttribute('data-lang');
            if (lang) {
                window.switchLanguageTab(lang);
            }
        }
    });
    
    // 为所有onclick包含switchLanguageTab的按钮添加事件
    document.addEventListener('click', function(e) {
        if (e.target.onclick && e.target.onclick.toString().includes('switchLanguageTab')) {
            e.preventDefault();
            const onclickStr = e.target.onclick.toString();
            const match = onclickStr.match(/switchLanguageTab\(['"]([^'"]+)['"]\)/);
            if (match) {
                const lang = match[1];
                window.switchLanguageTab(lang);
            }
        }
    });
};

// ==================== 通用多语言数据收集 ====================

/**
 * 通用多语言数据收集方法
 * @param {string|Array} fieldNames - 要收集的字段名，可以是字符串或字符串数组
 * @param {Object} options - 配置选项
 * @param {string} options.formSelector - 表单选择器，默认为当前表单
 * @param {string} options.prefix - 字段名前缀，默认为空
 * @param {string} options.suffix - 字段名后缀，默认为空
 * @param {Array} options.supportedLanguages - 支持的语言列表，默认使用 window.SupportedLanguages
 * @param {Function} options.valueProcessor - 值处理函数，用于对收集到的值进行处理
 * @returns {Object} 多语言数据对象
 */
window.collectMultilingualData = function(fieldNames, options = {}) {
    const config = {
        formSelector: options.formSelector || 'form',
        prefix: options.prefix || '',
        suffix: options.suffix || '',
        supportedLanguages: options.supportedLanguages || (window.SupportedLanguages || []),
        valueProcessor: options.valueProcessor || ((value) => value || ''),
        ...options
    };

    // 确保 fieldNames 是数组
    const fields = Array.isArray(fieldNames) ? fieldNames : [fieldNames];
    
    // 获取表单元素
    const form = typeof config.formSelector === 'string' 
        ? document.querySelector(config.formSelector) 
        : config.formSelector;
    
    if (!form) {
        console.warn('collectMultilingualData: Form not found');
        return {};
    }

    // 在收集数据前，先保存TinyMCE编辑器的内容
    if (typeof tinymce !== 'undefined') {
        tinymce.triggerSave();
    }

    const result = {};

    // 如果提供了支持的语言列表，使用该列表
    if (config.supportedLanguages && config.supportedLanguages.length > 0) {
        config.supportedLanguages.forEach(lang => {
            const langCode = typeof lang === 'string' ? lang : lang.code;
            result[langCode] = {};
            
            fields.forEach(fieldName => {
                const fullFieldName = `${config.prefix}${fieldName}${config.suffix}_${langCode}`;
                const input = form.querySelector(`[name="${fullFieldName}"]`);
                let value = '';
                
                if (input) {
                    // 如果是TinyMCE编辑器，尝试从TinyMCE实例获取内容
                    if (input.classList.contains('tinymce-editor') && typeof tinymce !== 'undefined') {
                        const editor = tinymce.get(input.id);
                        value = editor ? editor.getContent() : input.value;
                    } else {
                        value = input.value;
                    }
                }
                
                result[langCode][fieldName] = config.valueProcessor(value);
            });
        });
    } else {
        // 如果没有提供语言列表，则通过DOM扫描所有匹配的字段
        fields.forEach(fieldName => {
            result[fieldName] = {};
            
            // 查找所有匹配的输入字段
            const pattern = new RegExp(`^${config.prefix}${fieldName}${config.suffix}_(.+)$`);
            form.querySelectorAll(`[name^="${config.prefix}${fieldName}${config.suffix}_"]`).forEach(input => {
                const match = input.name.match(pattern);
                if (match) {
                    const langCode = match[1];
                    let value = '';
                    
                    // 如果是TinyMCE编辑器，尝试从TinyMCE实例获取内容
                    if (input.classList.contains('tinymce-editor') && typeof tinymce !== 'undefined') {
                        const editor = tinymce.get(input.id);
                        value = editor ? editor.getContent() : input.value;
                    } else {
                        value = input.value;
                    }
                    
                    result[fieldName][langCode] = config.valueProcessor(value);
                }
            });
        });
    }

    return result;
};

/**
 * 收集多语言数据（简化版本，使用 FormData）
 * @param {FormData} formData - FormData 对象
 * @param {string|Array} fieldNames - 要收集的字段名
 * @param {Array} supportedLanguages - 支持的语言列表
 * @param {Function} valueProcessor - 值处理函数
 * @returns {Object} 多语言数据对象
 */
window.collectMultilingualDataFromFormData = function(formData, fieldNames, supportedLanguages, valueProcessor) {
    const fields = Array.isArray(fieldNames) ? fieldNames : [fieldNames];
    const languages = supportedLanguages || (window.SupportedLanguages || []);
    const processor = valueProcessor || ((value) => value || '');
    
    const result = {};
    
    languages.forEach(lang => {
        const langCode = typeof lang === 'string' ? lang : lang.code;
        result[langCode] = {};
        
        fields.forEach(fieldName => {
            const value = formData.get(`${fieldName}_${langCode}`) || '';
            result[langCode][fieldName] = processor(value);
        });
    });
    
    return result;
};

/**
 * 收集多语言数据（支持方括号语法，如 fieldName[langCode]）
 * @param {string|Array} fieldNames - 要收集的字段名
 * @param {Object} options - 配置选项
 * @param {string} options.formSelector - 表单选择器，默认为当前表单
 * @param {string} options.inputType - 输入类型，'input' 或 'textarea'，默认为 'input'
 * @param {Function} options.valueProcessor - 值处理函数
 * @returns {Object} 多语言数据对象，结构为 {fieldName: {langCode: value}}
 */
window.collectMultilingualDataWithBrackets = function(fieldNames, options = {}) {
    const config = {
        formSelector: options.formSelector || 'form',
        inputType: options.inputType || 'input',
        valueProcessor: options.valueProcessor || ((value) => value || ''),
        ...options
    };

    // 确保 fieldNames 是数组
    const fields = Array.isArray(fieldNames) ? fieldNames : [fieldNames];
    
    // 获取表单元素
    const form = typeof config.formSelector === 'string' 
        ? document.querySelector(config.formSelector) 
        : config.formSelector;
    
    if (!form) {
        console.warn('collectMultilingualDataWithBrackets: Form not found');
        return {};
    }

    // 在收集数据前，先保存TinyMCE编辑器的内容
    if (typeof tinymce !== 'undefined') {
        tinymce.triggerSave();
    }

    const result = {};

    fields.forEach(fieldName => {
        result[fieldName] = {};
        
        // 查找所有匹配的输入字段
        const selector = `${config.inputType}[name^="${fieldName}["]`;
        form.querySelectorAll(selector).forEach(input => {
            const match = input.name.match(new RegExp(`${fieldName}\\[(.+?)\\]`));
            if (match) {
                const langCode = match[1];
                let value = '';
                
                // 如果是TinyMCE编辑器，尝试从TinyMCE实例获取内容
                if (input.classList.contains('tinymce-editor') && typeof tinymce !== 'undefined') {
                    const editor = tinymce.get(input.id);
                    value = editor ? editor.getContent() : input.value;
                } else {
                    value = input.value;
                }
                
                result[fieldName][langCode] = config.valueProcessor(value);
            }
        });
    });

    return result;
};

/**
 * 处理TinyMCE表单验证问题
 * 在表单提交前，临时显示隐藏的TinyMCE textarea元素，让HTML5验证能够聚焦
 * @param {HTMLFormElement} form - 表单元素
 */
window.handleTinyMCEFormValidation = function(form) {
    if (!form || typeof tinymce === 'undefined') return;
    
    // 查找所有TinyMCE编辑器的textarea
    const tinymceTextareas = form.querySelectorAll('textarea.tinymce-editor');
    
    tinymceTextareas.forEach(textarea => {
        const editor = tinymce.get(textarea.id);
        if (editor) {
            // 保存TinyMCE内容到textarea
            editor.save();
            
            // 临时显示textarea以允许验证聚焦
            const originalDisplay = textarea.style.display;
            const originalVisibility = textarea.style.visibility;
            const originalPosition = textarea.style.position;
            const originalLeft = textarea.style.left;
            const originalTop = textarea.style.top;
            const originalWidth = textarea.style.width;
            const originalHeight = textarea.style.height;
            
            // 临时显示textarea（但保持不可见）
            textarea.style.display = 'block';
            textarea.style.visibility = 'hidden';
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px';
            textarea.style.top = '-9999px';
            textarea.style.width = '1px';
            textarea.style.height = '1px';
            textarea.removeAttribute('aria-hidden');
            
            // 在下一个事件循环中恢复样式
            setTimeout(() => {
                textarea.style.display = originalDisplay;
                textarea.style.visibility = originalVisibility;
                textarea.style.position = originalPosition;
                textarea.style.left = originalLeft;
                textarea.style.top = originalTop;
                textarea.style.width = originalWidth;
                textarea.style.height = originalHeight;
                textarea.setAttribute('aria-hidden', 'true');
            }, 0);
        }
    });
};

/**
 * 为表单添加TinyMCE验证处理
 * @param {string|HTMLFormElement} formSelector - 表单选择器或表单元素
 */
window.enableTinyMCEFormValidation = function(formSelector) {
    const form = typeof formSelector === 'string' 
        ? document.querySelector(formSelector) 
        : formSelector;
    
    if (!form) return;
    
    // 在表单提交前处理TinyMCE验证
    form.addEventListener('submit', function(e) {
        // 先保存所有TinyMCE内容
        if (typeof tinymce !== 'undefined') {
            tinymce.triggerSave();
        }
        
        // 处理TinyMCE验证
        window.handleTinyMCEFormValidation(form);
    });
};

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    window.initLanguageSwitchers();
    
    // 自动为所有表单启用TinyMCE验证处理
    document.querySelectorAll('form').forEach(form => {
        window.enableTinyMCEFormValidation(form);
    });
});
