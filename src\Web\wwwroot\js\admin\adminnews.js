/**
 * AdminNews管理页面 JavaScript
 * 基于 businessinfo.js 的表单提交机制重构
 */


// 使用全局加载方法
function showLoading(message = window.Resources?.Admin?.PleaseWaitProcessing || 'Please wait...') {
    MlSoftSites.showLoading(message);
}

function hideLoading() {
    MlSoftSites.hideLoading();
}

// ==================== 新闻管理函数 ====================

// 打开新闻编辑模态框
window.openCreateModal = function () {
    resetNewsForm();
    document.getElementById('modalTitle').textContent = window.Resources?.Admin?.CreateNews || 'Create News';
    document.getElementById('saveButtonText').textContent = window.Resources?.Admin?.Create || 'Create';
    showNewsModal();
};

// 编辑新闻
window.editNews = function (newsId) {
    loadNewsData(newsId);
};

// 显示新闻模态框
function showNewsModal() {
    const modal = document.getElementById('newsModal');
    if (modal) {
        modal.classList.remove('hidden');
        // 初始化TinyMCE
        initializeTinyMCE();
        // 初始化缩略图上传
        if (typeof window.initNewsThumbnailUpload === 'function') {
            window.initNewsThumbnailUpload();
        }
    }
}

// 重置新闻表单
function resetNewsForm() {
    const form = document.getElementById('newsForm');
    if (form) {
        form.reset();

        // 重置隐藏字段
        document.getElementById('newsId').value = '';
        document.getElementById('Tags').value = '';
        const thumbHidden = document.getElementById('ThumbnailUrl');
        if (thumbHidden) thumbHidden.value = '';

        // 重置发布日期为当前时间
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        document.getElementById('PublishDate').value = now.toISOString().slice(0, 16);

        // 清除验证错误
        clearFieldErrors();

        // 重置多语言内容
        const supportedLanguages = window.SupportedLanguages || [];
        supportedLanguages.forEach(lang => {
            const titleField = document.querySelector(`input[name="Locale[${lang.code}].Title"]`);
            const summaryField = document.querySelector(`textarea[name="Locale[${lang.code}].Summary"]`);
            const contentField = document.querySelector(`textarea[name="Locale[${lang.code}].Content"]`);

            if (titleField) titleField.value = '';
            if (summaryField) summaryField.value = '';
            if (contentField) {
                contentField.value = '';
                // 重置TinyMCE内容
                const editor = (typeof tinymce !== 'undefined') ? tinymce.get(contentField.name) : null;
                if (editor && editor.initialized) {
                    editor.setContent('');
                }
            }
        });

        // 切换到第一个语言标签
        if (supportedLanguages.length > 0) {
            window.switchLanguageTab(supportedLanguages[0].code, '#newsModal', {
                buttonClass: 'language-tab',
                contentClass: 'language-content',
                contentIdPrefix: 'news-lang-'
            });
        }
    }
}

// 加载新闻数据
async function loadNewsData(newsId) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl(`edit/${newsId}`, 'AdminNews'), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
                populateNewsForm(result.data);
                document.getElementById('modalTitle').textContent = window.Resources?.Admin?.EditNews || 'Edit News';
                document.getElementById('saveButtonText').textContent = window.Resources?.Admin?.Update || 'Update';
                showNewsModal();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.LoadRecordError || 'Failed to load record');
            }
        } else {
            throw new Error('Failed to load news data');
        }
    } catch (error) {
        console.error('Load news error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load record');
    } finally {
        hideLoading();
    }
}

// 填充新闻表单
function populateNewsForm(news) {
    // 基本信息
    document.getElementById('newsId').value = news.id || '';
    document.getElementById('Type').value = news.type;
    document.getElementById('Priority').value = news.priority || 1;
    document.getElementById('Status').value = news.status;
    document.getElementById('Source').value = news.source;
    document.getElementById('ExternalUrl').value = news.externalUrl || '';
    document.getElementById('IsFeatured').checked = news.isFeatured || false;
    document.getElementById('ThumbnailUrl').value = news.thumbnailUrl || '';

    // 重新初始化缩略图上传，显示现有图片
    if (typeof window.initNewsThumbnailUpload === 'function') {
        window.initNewsThumbnailUpload();
    }

    // 发布日期
    if (news.publishDate) {
        const date = new Date(news.publishDate);
        date.setMinutes(date.getMinutes() - date.getTimezoneOffset());
        document.getElementById('PublishDate').value = date.toISOString().slice(0, 16);
    }

    // 标签
    if (news.tags && news.tags.length > 0) {
        document.getElementById('tagsInput').value = news.tags.join(', ');
        document.getElementById('Tags').value = JSON.stringify(news.tags);
    } else {
        document.getElementById('tagsInput').value = '';
        document.getElementById('Tags').value = '';
    }

    // 多语言内容
    if (news.locale) {
        Object.keys(news.locale).forEach(lang => {
            const localeData = news.locale[lang];
            if (localeData) {
                const titleField = document.querySelector(`input[name="Locale[${lang}].Title"]`);
                const summaryField = document.querySelector(`textarea[name="Locale[${lang}].Summary"]`);
                const contentField = document.querySelector(`textarea[name="Locale[${lang}].Content"]`);

                if (titleField) titleField.value = localeData.title || '';
                if (summaryField) summaryField.value = localeData.summary || '';
                if (contentField) {
                    contentField.value = localeData.content || '';

                    // 设置TinyMCE内容
                    setTimeout(() => {
                        const editor = (typeof tinymce !== 'undefined') ? tinymce.get(contentField.name) : null;
                        if (editor && editor.initialized) {
                            editor.setContent(localeData.content || '');
                        }
                    }, 100);
                }
            }
        });
    }
}

// 初始化新闻缩略图上传（单图）
if (typeof window.initNewsThumbnailUpload !== 'function') {
    window.initNewsThumbnailUpload = function () {
        try {
            var container = document.getElementById('thumbnailUpload');
            if (!container || !window.SimpleImageUpload) return;

            // 读取已有值，供预览
            var existingUrl = (document.getElementById('ThumbnailUrl') || {}).value || '';

            // 渲染上传控件（每次调用会重绘容器）
            window.__newsThumbnailUploader = window.SimpleImageUpload({
                container: container,
                name: 'newsThumbnail',
                value: existingUrl,
                uploadText: (window.Resources?.Shared?.FileUpload_ClickToUpload || 'Click to upload'),
                autoUpload: true,
                maxWidth: 800,
                maxHeight: 800,
                folder: 'news',
                allowedTypes: 'image/*',
                maxSize: '10MB',
                hiddenInputSelector: '#ThumbnailUrl'
            });
        } catch (e) {
            console.warn('Init news thumbnail upload failed', e);
        }
    };
}

// 初始化TinyMCE富文本编辑器
function initializeTinyMCE() {
    try {
        if (window.AdminTinyMCE && typeof window.AdminTinyMCE.initAll === 'function') {
            // 使用统一的AdminTinyMCE初始化方法
            window.AdminTinyMCE.initAll('#newsModal textarea[name*="Content"]', {
                menubar: false,
                plugins: ['lists', 'link', 'image', 'code', 'table', 'paste'],
                toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code | removeformat',
                height: 300,
                branding: false,
                paste_data_images: true,
                automatic_uploads: true,
                file_picker_types: 'image'
            });
        } else if (typeof tinymce !== 'undefined') {
            // 销毁现有的编辑器实例
            const modal = document.getElementById('newsModal');
            if (modal) {
                const editors = modal.querySelectorAll('textarea[name*="Content"]');
                editors.forEach(function (el) {
                    const inst = tinymce.get(el.id || el.name);
                    if (inst) {
                        inst.remove();
                    }
                });
            }

            // 初始化新的编辑器实例（回退方案）
            tinymce.init({
                selector: '#newsModal textarea[name*="Content"]',
                height: 300,
                menubar: false,
                plugins: [
                    'lists', 'link', 'image', 'code', 'table', 'paste'
                ],
                toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code | removeformat',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif; font-size: 14px }',
                language: window.currentLanguage || 'zh_CN',
                branding: false,
                paste_data_images: true,
                automatic_uploads: true,
                file_picker_types: 'image',
                setup: function (editor) {
                    editor.on('change', function () {
                        editor.save();
                    });
                }
            });
        } else {
            console.warn('Neither AdminTinyMCE nor TinyMCE is available');
        }
    } catch (e) {
        console.warn('Init TinyMCE for news modal failed', e);
    }
}

// 保存新闻
window.saveNews = async function () {
    try {
        console.log('=== 开始保存新闻 ===');
        const form = document.getElementById('newsForm');
        if (!form) {
            console.error('表单不存在');
            return;
        }

        console.log('表单找到，开始验证...');

        // 手动验证多语言字段，只验证当前可见的标签页
        let isValid = true;
        let firstErrorField = null;

        // 获取支持的语言列表
        const supportedLanguages = window.SupportedLanguages || [];

        // 验证每种语言的必填字段
        supportedLanguages.forEach(lang => {
            const titleField = document.querySelector(`input[name="Locale[${lang.code}].Title"]`);
            const contentField = document.querySelector(`textarea[name="Locale[${lang.code}].Content"]`);

            // 验证标题
            if (titleField && (!titleField.value || titleField.value.trim() === '')) {
                isValid = false;
                if (!firstErrorField) {
                    firstErrorField = { field: titleField, lang: lang.code, message: window.Resources?.Admin?.TitleRequired || 'Title is required' };
                }
                showFieldError(titleField, window.Resources?.Admin?.TitleRequired || 'Title is required');
            }

            // 验证内容
            if (contentField) {
                // 获取TinyMCE内容或textarea内容
                let content = '';
                const editor = (typeof tinymce !== 'undefined') ? tinymce.get(contentField.id || contentField.name) : null;
                if (editor && editor.initialized) {
                    content = editor.getContent();
                } else {
                    content = contentField.value;
                }

                if (!content || content.trim() === '' || content.trim() === '<p></p>') {
                    isValid = false;
                    if (!firstErrorField) {
                        firstErrorField = { field: contentField, lang: lang.code, message: window.Resources?.Admin?.ContentRequired || 'Content is required' };
                    }
                    showFieldError(contentField, window.Resources?.Admin?.ContentRequired || 'Content is required');
                }
            }
        });

        // 如果有验证错误，切换到第一个错误字段所在的标签页
        if (!isValid && firstErrorField) {
            // 使用全局语言切换函数
            if (window.switchLanguageTab) {
                window.switchLanguageTab(firstErrorField.lang, '#newsModal', {
                    buttonClass: 'language-tab',
                    contentClass: 'language-content',
                    contentIdPrefix: 'news-lang-'
                });
            }

            // 聚焦到错误字段
            setTimeout(() => {
                firstErrorField.field.focus();
            }, 100);

            Dialog.error(firstErrorField.message);
            return;
        }

        // 检查其他标准字段的HTML5验证
        const invalidField = Array.from(form.elements).find(field => {
            if (!field.name || field.type === 'hidden') return false;
            return !field.checkValidity();
        });

        if (invalidField) {
            invalidField.reportValidity();
            return;
        }

        showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

        // 同步 TinyMCE 内容到 textarea
        console.log('同步 TinyMCE 内容...');
        if (typeof tinymce !== 'undefined') {
            try {
                tinymce.triggerSave();
                console.log('TinyMCE triggerSave 成功');
            } catch (e) {
                console.warn('TinyMCE triggerSave 失败:', e);
            }
        }

        // 构建数据对象
        console.log('开始构建数据对象...');
        const formData = new FormData(form);

        // 枚举值映射 - 转换为整数值
        const getEnumValue = (enumName, stringValue) => {
            const enums = {
                NewsType: { CompanyNews: 0, IndustryNews: 1, ProductNews: 2, EventNews: 3, Announcement: 4 },
                NewsStatus: { Draft: 0, Review: 1, Published: 2 },
                NewsSource: { Internal: 0, External: 1, PressRelease: 2, MediaReport: 3, Partner: 4 }
            };
            return enums[enumName][stringValue] ?? 0;
        };

        const newsData = {
            Id: formData.get('Id') || '',
            Type: parseInt(formData.get('Type')) || 0,
            PublishDate: formData.get('PublishDate') || new Date().toISOString(),
            Priority: parseInt(formData.get('Priority')) || 1,
            Status: parseInt(formData.get('Status')),
            Source: parseInt(formData.get('Source')),
            ExternalUrl: formData.get('ExternalUrl') || '',
            IsFeatured: document.getElementById('IsFeatured').checked,
            ThumbnailUrl: formData.get('ThumbnailUrl') || '',
            Tags: [],
            Locale: {}
        };

        console.log('基本数据收集完成:', {
            Id: newsData.Id,
            Type: newsData.Type,
            Status: newsData.Status,
            IsFeatured: newsData.IsFeatured
        });

        // 处理标签
        const tagsInput = formData.get('Tags');
        if (tagsInput) {
            try {
                newsData.Tags = JSON.parse(tagsInput);
            } catch (e) {
                newsData.Tags = tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
            }
        }

        // 收集多语言数据 - 手动收集，因为表单使用 Locale[langCode].Field 格式
        console.log('开始收集多语言数据...');
        console.log('支持的语言:', supportedLanguages.map(l => l.code));

        supportedLanguages.forEach(lang => {
            console.log(`处理语言: ${lang.code}`);
            const title = formData.get(`Locale[${lang.code}].Title`);
            const summary = formData.get(`Locale[${lang.code}].Summary`);
            let content = formData.get(`Locale[${lang.code}].Content`);

            console.log(`${lang.code} 表单数据:`, { title, summary, contentLength: content?.length });

            // 获取TinyMCE内容
            const contentField = document.querySelector(`textarea[name="Locale[${lang.code}].Content"]`);
            if (contentField) {
                const editor = (typeof tinymce !== 'undefined') ? tinymce.get(contentField.id || contentField.name) : null;
                if (editor && editor.initialized) {
                    content = editor.getContent();
                    console.log(`${lang.code} TinyMCE内容长度:`, content?.length);
                }
            }

            // 只有当至少有一个字段有内容时才添加该语言的数据
            if (title || summary || content) {
                newsData.Locale[lang.code] = {
                    Title: title || '',
                    Summary: summary || '',
                    Content: content || ''
                };
                console.log(`${lang.code} 数据已添加`);
            } else {
                console.log(`${lang.code} 没有数据，跳过`);
            }
        });

        console.log('多语言数据收集完成:', Object.keys(newsData.Locale));

        // 发送请求
        console.log('准备发送请求...');
        console.log('最终数据:', newsData);

        const url = newsData.Id
            ? buildMultilingualUrl(`edit/${newsData.Id}`, 'AdminNews')
            : buildMultilingualUrl('create', 'AdminNews');

        console.log('请求URL:', url);

        // 获取防伪令牌
        const antiForgeryToken = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...(antiForgeryToken && { 'RequestVerificationToken': antiForgeryToken })
            },
            body: JSON.stringify(newsData)
        });

        console.log('响应状态:', response.status);
        console.log('响应头:', Object.fromEntries(response.headers.entries()));

        const result = await response.json();
        console.log('响应结果:', result);

        if (result.success) {
            Dialog.notify(result.message || window.Resources?.Admin?.SaveSuccess || 'Saved successfully', 'success');
            closeNewsModal();
            // 重新加载页面
            window.location.reload();
        } else {
            console.error('保存失败:', result.message);
            if (result.errors && result.errors.length > 0) {
                console.error('验证错误:', result.errors);
                result.errors.forEach((error, index) => {
                    console.error(`错误 ${index + 1}:`, error);
                });
            }
            Dialog.error(result.message || window.Resources?.Admin?.SaveError || 'Save failed');
        }
    } catch (error) {
        console.error('Save news error:', error);
        Dialog.error(window.Resources?.Admin?.SaveError || 'Save failed');
    } finally {
        hideLoading();
    }
};

// 显示字段错误
function showFieldError(field, message) {
    // 清除之前的错误
    const existingError = field.parentNode.querySelector('.validation-error');
    if (existingError) {
        existingError.remove();
    }

    // 添加错误样式
    field.classList.add('border-red-500');

    // 添加错误消息
    const errorDiv = document.createElement('div');
    errorDiv.className = 'validation-error text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

// 清除字段错误
function clearFieldErrors() {
    document.querySelectorAll('.border-red-500').forEach(field => {
        field.classList.remove('border-red-500');
    });
    document.querySelectorAll('.validation-error').forEach(error => {
        error.remove();
    });
}

// 删除新闻
window.deleteNews = async function (newsId) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete || 'Are you sure?');
    if (confirmed) {
        try {
            showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

            const response = await fetch(buildMultilingualUrl(`delete/${newsId}`, 'AdminNews'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteSuccess || 'Deleted successfully', 'success');
                // 重新加载页面
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete news error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
};

// 切换推荐状态
window.toggleFeatured = async function (newsId) {
    try {
        showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

        const response = await fetch(buildMultilingualUrl(`toggle-featured/${newsId}`, 'AdminNews'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success) {
            Dialog.notify(result.message || window.Resources?.Admin?.UpdateSuccess || 'Updated successfully', 'success');
            // 重新加载页面
            window.location.reload();
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.UpdateError || 'Update failed');
        }
    } catch (error) {
        console.error('Toggle featured error:', error);
        Dialog.error(window.Resources?.Admin?.UpdateError || 'Update failed');
    } finally {
        hideLoading();
    }
};

// 切换发布状态
window.togglePublish = async function (newsId) {
    try {
        showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

        const response = await fetch(buildMultilingualUrl(`toggle-publish/${newsId}`, 'AdminNews'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success) {
            Dialog.notify(result.message || window.Resources?.Admin?.UpdateSuccess || 'Updated successfully', 'success');
            // 重新加载页面
            window.location.reload();
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.UpdateError || 'Update failed');
        }
    } catch (error) {
        console.error('Toggle publish error:', error);
        Dialog.error(window.Resources?.Admin?.UpdateError || 'Update failed');
    } finally {
        hideLoading();
    }
};

// 关闭新闻模态框
window.closeNewsModal = function () {
    const modal = document.getElementById('newsModal');
    if (modal) {
        // 销毁 TinyMCE 实例，避免重复初始化与内存泄露
        try {
            if (typeof tinymce !== 'undefined') {
                const editors = modal.querySelectorAll('textarea[name*="Content"]');
                editors.forEach(function (el) {
                    const inst = tinymce.get(el.id || el.name);
                    if (inst) {
                        inst.remove();
                    }
                });
            }
        } catch (_) { }

        // 清除验证错误
        clearFieldErrors();

        // 隐藏模态框
        modal.classList.add('hidden');
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function () {
    // 确保全局Dialog可用
    if (typeof Dialog === 'undefined') {
        console.warn('Dialog library not found');
        window.Dialog = {
            error: function (msg) { alert('Error: ' + msg); },
            notify: function (msg) { alert(msg); },
            confirm: function (msg) { return confirm(msg); }
        };
    }

    // 绑定筛选表单提交事件
    const filterForm = document.querySelector('form[method="get"]');
    if (filterForm) {
        filterForm.addEventListener('submit', function (e) {
            showLoading(window.Resources?.Admin?.Searching || 'Searching...');
        });
    }

    // 为筛选按钮添加加载状态
    const searchButton = document.querySelector('button[type="submit"]');
    if (searchButton) {
        searchButton.addEventListener('click', function () {
            showLoading(window.Resources?.Admin?.Searching || 'Searching...');
        });
    }
});