/**
 * BusinessInfo管理页面 JavaScript
 * 支持多标签页面懒加载和业务信息管理功能
 */

// Tab加载状态追踪
const loadedTabs = new Set(['business-divisions']); // 第一个标签已加载


// Tab switching with lazy loading
async function switchTab(tabName) {
    // Update URL hash
    window.history.replaceState(null, null, '#' + tabName);

    // 检查是否已加载
    if (loadedTabs.has(tabName)) {
        // 直接使用全局方法切换
        MlSoftSites.switchTab(tabName);
        return;
    }

    // 对于未加载的标签页，显示加载状态并调用API
    if (tabName !== 'business-divisions') {
        await loadTabContent(tabName);
    }

    // 切换显示
    MlSoftSites.switchTab(tabName);
}

// 懒加载标签页内容
async function loadTabContent(tabName) {
    const tabContainer = document.getElementById(`${tabName}-tab`);
    if (!tabContainer) return;

    // 显示加载状态
    showTabLoading(tabContainer);

    try {
        const response = await fetch(buildMultilingualUrl(`tab/${tabName}`, 'BusinessInfo'), {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        const result = await response.json();

        if (result.success) {
            // 插入HTML内容
            tabContainer.innerHTML = result.html;

            // 标记为已加载
            loadedTabs.add(tabName);

            // 初始化该标签页的JavaScript功能
            initializeTabScripts(tabName);

        } else {
            showTabError(tabContainer, result.message);
        }
    } catch (error) {
        console.error('Error loading tab:', error);
        showTabError(tabContainer, window.Resources?.Admin?.LoadTabError || 'Failed to load tab content');
    }
}

// 显示标签页加载状态
function showTabLoading(tabContainer) {
    tabContainer.innerHTML = `
        <div class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-3 text-gray-600 dark:text-gray-400">${window.Resources?.Admin?.Loading || 'Loading'}...</span>
        </div>
    `;
}

// 显示标签页错误状态
function showTabError(tabContainer, message) {
    tabContainer.innerHTML = `
        <div class="text-center py-12">
            <div class="text-red-600 dark:text-red-400 mb-4">
                <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
                <p class="text-lg">${message}</p>
            </div>
            <button onclick="reloadTab('${tabContainer.id.replace('-tab', '')}')"
                    class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-redo mr-2"></i>
                ${window.Resources?.Admin?.Retry || 'Retry'}
            </button>
        </div>
    `;
}

// 重新加载标签页
window.reloadTab = async function(tabName) {
    loadedTabs.delete(tabName);
    await loadTabContent(tabName);
};

// 初始化标签页特定的JavaScript功能
function initializeTabScripts(tabName) {
    switch(tabName) {
        case 'product-services':
            // 初始化产品服务相关的脚本
            initializeProductServiceScripts();
            break;
    }
}

// 初始化产品服务脚本
function initializeProductServiceScripts() {
    // 这里可以添加产品服务特定的初始化代码
    console.log('Product services tab initialized');
}

// Initialize tab from URL hash or default to first tab
function initializeTab() {
    const hash = window.location.hash.substring(1);
    const validTabs = ['business-divisions', 'product-services'];

    if (hash && validTabs.includes(hash)) {
        switchTab(hash);
    } else {
        switchTab('business-divisions');
    }
}

// Use global loading methods
function showLoading(message = window.Resources?.Admin?.PleaseWaitProcessing || 'Please wait...') {
    MlSoftSites.showLoading(message);
}

function hideLoading() {
    MlSoftSites.hideLoading();
}

// ==================== 业务部门管理函数 ====================

// 打开业务部门编辑模态框
window.openBusinessDivisionModal = function(divisionId = null) {
    if (divisionId) {
        loadBusinessDivision(divisionId);
    } else {
        resetBusinessDivisionForm();
    }
    
    // 显示模态框
    const modal = document.getElementById('businessDivisionModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
};

// 编辑业务部门
window.editBusinessDivision = function(divisionId) {
    openBusinessDivisionModal(divisionId);
};

// 加载业务部门数据
async function loadBusinessDivision(divisionId) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl(`business-division/${divisionId}`, 'BusinessInfo'), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateBusinessDivisionForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.RecordNotFound || 'Record not found');
        }
    } catch (error) {
        console.error('Load business division error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load record');
    } finally {
        hideLoading();
    }
}

// 填充业务部门表单
function populateBusinessDivisionForm(division) {
    document.getElementById('divisionId').value = division.id || '';
    document.getElementById('displayOrder').value = division.displayOrder || 0;
    document.getElementById('isActive').checked = division.isActive;
    // document.getElementById('imageUrl').value = division.imageUrl || '';
    // document.getElementById('iconUrl').value = division.iconUrl || '';

    // 填充多语言字段
    if (division.locale) {
        Object.keys(division.locale).forEach(lang => {
            const localeData = division.locale[lang];
            if (localeData) {
                const divisionNameField = document.getElementById(`divisionName_${lang}`);
                const descriptionField = document.getElementById(`description_${lang}`);
                const servicesField = document.getElementById(`services_${lang}`);

                if (divisionNameField) divisionNameField.value = localeData.divisionName || '';
                if (descriptionField) descriptionField.value = localeData.description || '';
                if (servicesField) servicesField.value = localeData.services || '';
            }
        });
    }
}

// 重置业务部门表单
function resetBusinessDivisionForm() {
    document.getElementById('divisionId').value = '';
    document.getElementById('displayOrder').value = 0;
    document.getElementById('isActive').checked = true;
    // document.getElementById('imageUrl').value = '';
    // document.getElementById('iconUrl').value = '';

    // 重置多语言字段
    const supportedLanguages = window.SupportedLanguages || [];
    supportedLanguages.forEach(lang => {
        const divisionNameField = document.getElementById(`divisionName_${lang.code}`);
        const descriptionField = document.getElementById(`description_${lang.code}`);
        const servicesField = document.getElementById(`services_${lang.code}`);

        if (divisionNameField) divisionNameField.value = '';
        if (descriptionField) descriptionField.value = '';
        if (servicesField) servicesField.value = '';
    });
}

// 保存业务部门
window.saveBusinessDivision = async function() {
    try {
        const form = document.getElementById('businessDivisionForm');

        // 手动验证多语言字段，只验证当前可见的标签页
        let isValid = true;

        // 获取当前活动的语言标签页
        const activeTab = document.querySelector('.division-lang-tab-button.active');
        if (activeTab) {
            const activeLang = activeTab.getAttribute('data-lang');
            const activeContent = document.getElementById(`division-lang-${activeLang}`);

            if (activeContent && !activeContent.classList.contains('hidden')) {
                // 验证当前语言标签页中的必填字段
                const requiredFields = activeContent.querySelectorAll('[required]');
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.focus();
                        if (typeof Dialog !== 'undefined' && Dialog.error) {
                            Dialog.error(window.Resources?.Admin?.PleaseCompleteRequiredFields || '请填写必填字段');
                        }
                        return;
                    }
                });
            }
        }

        if (!isValid) {
            return;
        }

        // 检查其他标准字段的HTML5验证（排除隐藏的多语言字段）
        const visibleFields = Array.from(form.elements).filter(field => {
            if (!field.name) return false;

            // 如果是多语言字段，检查是否在可见的标签页中
            if (field.name.includes('_')) {
                const parent = field.closest('.division-lang-content');
                return parent && !parent.classList.contains('hidden');
            }

            // 其他字段正常检查
            return true;
        });

        const invalidField = visibleFields.find(field => !field.checkValidity());
        if (invalidField) {
            invalidField.reportValidity();
            return;
        }

        showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

        const formData = new FormData(form);
        const divisionData = {
            id: formData.get('divisionId') || '',
            displayOrder: parseInt(formData.get('displayOrder')) || 0,
            isActive: formData.get('isActive') === 'on',
            // imageUrl: formData.get('imageUrl') || '',
            // iconUrl: formData.get('iconUrl') || '',
            locale: {}
        };

        // 收集多语言数据
        divisionData.locale = window.collectMultilingualDataFromFormData(
            formData, 
            ['divisionName', 'description', 'services'], 
            window.SupportedLanguages
        );

        const response = await fetch(buildMultilingualUrl('business-division', 'BusinessInfo'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(divisionData)
        });

        const result = await response.json();

        if (result.success) {
            Dialog.notify(result.message || window.Resources?.Admin?.SaveSuccess || 'Saved successfully', 'success');
            closeBusinessDivisionModal();
            // 重新加载页面或更新表格
            window.location.reload();
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.SaveError || 'Save failed');
        }
    } catch (error) {
        console.error('Save business division error:', error);
        Dialog.error(window.Resources?.Admin?.SaveError || 'Save failed');
    } finally {
        hideLoading();
    }
};

// 删除业务部门
window.deleteBusinessDivision = async function(divisionId) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete || 'Are you sure?');
    if (confirmed) {
        try {
            showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

            const response = await fetch(buildMultilingualUrl(`business-division/${divisionId}`, 'BusinessInfo'), {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteSuccess || 'Deleted successfully', 'success');
                // 重新加载页面或更新表格
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete business division error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
};

// 关闭业务部门模态框
window.closeBusinessDivisionModal = function() {
    const modal = document.getElementById('businessDivisionModal');
    if (modal) {
        modal.classList.add('hidden');
    }
};



// ==================== 产品服务管理函数 ====================

// 初始化产品服务脚本
function initializeProductServiceScripts() {
    loadBusinessDivisions();
}

// 加载业务部门列表
async function loadBusinessDivisions() {
    try {
        const response = await fetch(buildMultilingualUrl('business-divisions', 'BusinessInfo'), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            const divisionFilter = document.getElementById('divisionFilter');
            if (divisionFilter) {
                divisionFilter.innerHTML = '<option value="">'+ window.Resources.Admin.AllDivisions +'</option>';
                
                result.data.forEach(division => {
                    const option = document.createElement('option');
                    option.value = division.id;
                    option.textContent = division.name;
                    divisionFilter.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('Load business divisions error:', error);
    }
}

// 筛选产品服务
window.filterProductServices = function() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const divisionFilter = document.getElementById('divisionFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('.product-service-row');
    
    rows.forEach(row => {
        const category = row.dataset.category;
        const division = row.dataset.division;
        const search = row.dataset.search;
        
        let show = true;
        
        if (categoryFilter && category !== categoryFilter) {
            show = false;
        }
        
        if (divisionFilter && division !== divisionFilter) {
            show = false;
        }
        
        if (searchInput && !search.includes(searchInput)) {
            show = false;
        }
        
        row.style.display = show ? '' : 'none';
    });
};

// 打开产品服务编辑模态框
window.openProductServiceModal = function(productId = null) {
    if (productId) {
        loadProductService(productId);
    } else {
        resetProductServiceForm();
    }
    
    // 显示模态框
    const modal = document.getElementById('productServiceModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
    // 初始化产品图片上传绑定
    initializeProductImagesUpload();
    // 初始化产品文档上传绑定
    initializeProductDocumentsUpload();
    // 初始化 TinyMCE（仅限该模态框内）
    try {
        if (window.AdminTinyMCE) {
            window.AdminTinyMCE.initAll('#productServiceModal .tinymce-editor', {
                menubar: false,
                plugins: ['lists'],
                toolbar: 'undo redo | formatselect | bullist numlist | alignleft aligncenter alignright | removeformat',
                height: 220
            });
        }
    } catch (e) { console.warn('Init TinyMCE for product service modal failed', e); }
};

// 编辑产品服务
window.editProductService = function(productId) {
    openProductServiceModal(productId);
};

// 加载产品服务数据
async function loadProductService(productId) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl(`product-service/${productId}`, 'BusinessInfo'), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateProductServiceForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.RecordNotFound || 'Record not found');
        }
    } catch (error) {
        console.error('Load product service error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load record');
    } finally {
        hideLoading();
    }
}

// 填充产品服务表单
function populateProductServiceForm(product) {
    document.getElementById('productId').value = product.id || '';
    document.getElementById('businessDivisionId').value = product.businessDivisionId || '';
    document.getElementById('category').value = product.category || 0;
    document.getElementById('price').value = product.price || '';
    document.getElementById('currency').value = product.currency || '';
    document.getElementById('displayOrder').value = product.displayOrder || 0;
    document.getElementById('isActive').checked = product.isActive;

    // 填充多语言字段
    if (product.locale) {
        Object.keys(product.locale).forEach(lang => {
            const localeData = product.locale[lang];
            if (localeData) {
                const productNameField = document.getElementById(`productName_${lang}`);
                const descriptionField = document.getElementById(`description_${lang}`);
                const featuresField = document.getElementById(`features_${lang}`);
                const specificationsField = document.getElementById(`specifications_${lang}`);

                if (productNameField) productNameField.value = localeData.productName || '';
                // 如果 TinyMCE 已初始化，使用 setContent；否则直接赋值
                if (descriptionField) {
                    const value = (localeData.description == null) ? '' : String(localeData.description);
                    const ed = (typeof tinymce !== 'undefined') ? tinymce.get(descriptionField.id) : null;
                    if (ed) {
                        if (ed.initialized) { ed.setContent(value); }
                        else { ed.once('init', function () { try { ed.setContent(value); } catch (_) {} }); }
                    } else {
                        descriptionField.value = value;
                    }
                }
                if (featuresField) {
                    const value = (localeData.features == null) ? '' : String(localeData.features);
                    const ed = (typeof tinymce !== 'undefined') ? tinymce.get(featuresField.id) : null;
                    if (ed) {
                        if (ed.initialized) { ed.setContent(value); }
                        else { ed.once('init', function () { try { ed.setContent(value); } catch (_) {} }); }
                    } else {
                        featuresField.value = value;
                    }
                }
                if (specificationsField) {
                    const value = (localeData.specifications == null) ? '' : String(localeData.specifications);
                    const ed = (typeof tinymce !== 'undefined') ? tinymce.get(specificationsField.id) : null;
                    if (ed) {
                        if (ed.initialized) { ed.setContent(value); }
                        else { ed.once('init', function () { try { ed.setContent(value); } catch (_) {} }); }
                    } else {
                        specificationsField.value = value;
                    }
                }
            }
        });
    }

    // 渲染已存在的产品图片
    try {
        const listEl = document.getElementById('productImagesFileList');
        if (listEl) listEl.innerHTML = '';
        if (product.imageUrls && product.imageUrls.length > 0 && window.AdminUpload && window.AdminUpload.renderInitialFiles) {
            const infos = product.imageUrls.map(url => ({ filePath: url, originalName: url.split('/').pop() || 'image.jpg', contentType: 'image/jpeg' }));
            window.AdminUpload.renderInitialFiles(listEl, {
                types: ['image/*'],
                multiple: true,
                folder: 'product-images',
                maxSize: '10MB',
                preview: true
            }, infos);
            const hidden = document.getElementById('productImagesFileUrl');
            if (hidden) hidden.value = product.imageUrls.join(',');
        }
    } catch (_) { }

    // 渲染已存在的产品文档
    try {
        const docListEl = document.getElementById('productDocumentsFileList');
        if (docListEl) docListEl.innerHTML = '';
        if (product.documents && product.documents.length > 0 && window.AdminUpload && window.AdminUpload.renderInitialFiles) {
            const infos = product.documents.map(d => ({
                filePath: d.fileUrl,
                originalName: d.fileUrl.split('/').pop() || 'document',
                contentType: d.fileType || 'application/octet-stream'
            }));
            window.AdminUpload.renderInitialFiles(docListEl, {
                types: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', 'application/pdf'],
                multiple: true,
                folder: 'product-documents',
                maxSize: '50MB',
                preview: true
            }, infos);
        }
    } catch (_) { }
}

// 重置产品服务表单
function resetProductServiceForm() {
    document.getElementById('productId').value = '';
    document.getElementById('businessDivisionId').value = '';
    document.getElementById('category').value = 0;
    document.getElementById('price').value = '';
    document.getElementById('currency').value = '';
    document.getElementById('displayOrder').value = 0;
    document.getElementById('isActive').checked = true;

    // 重置多语言字段
    const supportedLanguages = window.SupportedLanguages || [];
    supportedLanguages.forEach(lang => {
        const productNameField = document.getElementById(`productName_${lang.code}`);
        const descriptionField = document.getElementById(`description_${lang.code}`);
        const featuresField = document.getElementById(`features_${lang.code}`);
        const specificationsField = document.getElementById(`specifications_${lang.code}`);

        if (productNameField) productNameField.value = '';
        if (descriptionField) {
            const ed = (typeof tinymce !== 'undefined') ? tinymce.get(descriptionField.id) : null;
            if (ed) { if (ed.initialized) ed.setContent(''); else ed.once('init', function(){ try { ed.setContent(''); } catch(_){} }); }
            else { descriptionField.value = ''; }
        }
        if (featuresField) {
            const ed = (typeof tinymce !== 'undefined') ? tinymce.get(featuresField.id) : null;
            if (ed) { if (ed.initialized) ed.setContent(''); else ed.once('init', function(){ try { ed.setContent(''); } catch(_){} }); }
            else { featuresField.value = ''; }
        }
        if (specificationsField) {
            const ed = (typeof tinymce !== 'undefined') ? tinymce.get(specificationsField.id) : null;
            if (ed) { if (ed.initialized) ed.setContent(''); else ed.once('init', function(){ try { ed.setContent(''); } catch(_){} }); }
            else { specificationsField.value = ''; }
        }
    });

    // 重置图片区域
    const listEl = document.getElementById('productImagesFileList');
    if (listEl) listEl.innerHTML = '';
    const hidden = document.getElementById('productImagesFileUrl');
    if (hidden) hidden.value = '';

    // 重置文档区域
    const docListEl = document.getElementById('productDocumentsFileList');
    if (docListEl) docListEl.innerHTML = '';
}

// 保存产品服务
window.saveProductService = async function() {
    try {
        const form = document.getElementById('productServiceForm');

        // 手动验证多语言字段，只验证当前可见的标签页
        let isValid = true;

        // 获取当前活动的语言标签页
        const activeTab = document.querySelector('.product-lang-tab-button.active');
        if (activeTab) {
            const activeLang = activeTab.getAttribute('data-lang');
            const activeContent = document.getElementById(`product-lang-${activeLang}`);

            if (activeContent && !activeContent.classList.contains('hidden')) {
                // 验证当前语言标签页中的必填字段
                const requiredFields = activeContent.querySelectorAll('[required]');
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.focus();
                        if (typeof Dialog !== 'undefined' && Dialog.error) {
                            Dialog.error(window.Resources?.Admin?.PleaseCompleteRequiredFields || '请填写必填字段');
                        }
                        return;
                    }
                });
            }
        }

        if (!isValid) {
            return;
        }

        // 检查其他标准字段的HTML5验证（排除隐藏的多语言字段）
        const visibleFields = Array.from(form.elements).filter(field => {
            if (!field.name) return false;

            // 如果是多语言字段，检查是否在可见的标签页中
            if (field.name.includes('_')) {
                const parent = field.closest('.product-lang-content');
                return parent && !parent.classList.contains('hidden');
            }

            // 其他字段正常检查
            return true;
        });

        const invalidField = visibleFields.find(field => !field.checkValidity());
        if (invalidField) {
            invalidField.reportValidity();
            return;
        }

        showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

        // 同步 TinyMCE 内容到 textarea
        if (typeof tinymce !== 'undefined') {
            try { tinymce.triggerSave(); } catch (_) {}
        }

        const formData = new FormData(form);
        // 收集图片URL（从渲染列表收集，支持多图片）
        const productImageUrls = (function () {
            const list = document.getElementById('productImagesFileList');
            if (!list) return [];
            return Array.from(list.querySelectorAll('[data-file-path]'))
                .map(el => el.getAttribute('data-file-path'))
                .filter(u => u && u.trim());
        })();
        // 收集文档（最少先收集文件URL与基础元数据）
        const productDocuments = (function () {
            const list = document.getElementById('productDocumentsFileList');
            if (!list) return [];
            return Array.from(list.querySelectorAll('[data-file-path]')).map(el => {
                const url = el.getAttribute('data-file-path');
                const name = url ? (url.split('/').pop() || '') : '';
                // 通过文件扩展名推断类型
                const ext = (name.split('.').pop() || '').toLowerCase();
                const typeMap = { pdf: 'application/pdf', doc: 'application/msword', docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', xls: 'application/vnd.ms-excel', xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', ppt: 'application/vnd.ms-powerpoint', pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' };
                const fileType = typeMap[ext] || 'application/octet-stream';
                return {
                    locale: {},
                    fileUrl: url || '',
                    fileType: fileType,
                    fileSize: 0,
                    uploadDate: new Date().toISOString()
                };
            });
        })();
        const productData = {
            id: formData.get('productId') || '',
            businessDivisionId: formData.get('businessDivisionId') || '',
            category: parseInt(formData.get('category')) || 0,
            price: parseFloat(formData.get('price')) || null,
            currency: formData.get('currency') || '',
            displayOrder: parseInt(formData.get('displayOrder')) || 0,
            isActive: formData.get('isActive') === 'on',
            imageUrls: productImageUrls,
            documents: productDocuments,
            locale: {}
        };

        // 收集多语言数据
        productData.locale = window.collectMultilingualDataFromFormData(
            formData, 
            ['productName', 'description', 'features', 'specifications'], 
            window.SupportedLanguages
        );

        const response = await fetch(buildMultilingualUrl('product-service', 'BusinessInfo'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(productData)
        });

        const result = await response.json();

        if (result.success) {
            Dialog.notify(result.message || window.Resources?.Admin?.SaveSuccess || 'Saved successfully', 'success');
            closeProductServiceModal();
            // 重新加载页面或更新表格
            window.location.reload();
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.SaveError || 'Save failed');
        }
    } catch (error) {
        console.error('Save product service error:', error);
        Dialog.error(window.Resources?.Admin?.SaveError || 'Save failed');
    } finally {
        hideLoading();
    }
};

// 删除产品服务
window.deleteProductService = async function(productId) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete || 'Are you sure?');
    if (confirmed) {
        try {
            showLoading(window.Resources?.Admin?.PleaseWaitProcessing || 'Processing...');

            const response = await fetch(buildMultilingualUrl(`product-service/${productId}`, 'BusinessInfo'), {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteSuccess || 'Deleted successfully', 'success');
                // 重新加载页面或更新表格
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete product service error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
};

// 关闭产品服务模态框
window.closeProductServiceModal = function() {
    const modal = document.getElementById('productServiceModal');
    if (modal) {
        // 销毁 TinyMCE 实例，避免重复初始化与内存泄露
        try {
            if (typeof tinymce !== 'undefined') {
                const editors = modal.querySelectorAll('.tinymce-editor');
                editors.forEach(function (el) {
                    var inst = tinymce.get(el.id);
                    if (inst) { inst.remove(); }
                });
            }
        } catch (_) { }
        modal.classList.add('hidden');
    }
};

// 产品服务语言标签页切换 - 使用通用系统
window.switchProductLanguageTab = function(langCode) {
    return window.switchLanguageTab(langCode, '#productServiceModal', {
        buttonClass: 'product-lang-tab-button',
        contentClass: 'product-lang-content',
        contentIdPrefix: 'product-lang-',
        activeClasses: ['active', 'text-primary-600', 'dark:text-primary-400', 'border-primary-500'],
        inactiveClasses: ['text-gray-500', 'dark:text-gray-400', 'hover:text-gray-700', 'dark:hover:text-gray-300', 'hover:border-gray-300', 'dark:hover:border-gray-600', 'border-transparent']
    });
};

// 加载业务部门到下拉选择框
async function loadBusinessDivisionsForModal() {
    try {
        const response = await fetch(buildMultilingualUrl('business-divisions', 'BusinessInfo'), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            const businessDivisionSelect = document.getElementById('businessDivisionId');
            if (businessDivisionSelect) {
                businessDivisionSelect.innerHTML = '<option value="">'+ window.Resources?.Admin?.SelectBusinessDivision +'</option>';
                
                result.data.forEach(division => {
                    const option = document.createElement('option');
                    option.value = division.id;
                    option.textContent = division.name;
                    businessDivisionSelect.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('Load business divisions for modal error:', error);
    }
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页
    initializeTab();

    // 处理浏览器前进后退
    window.addEventListener('hashchange', function() {
        initializeTab();
    });

    // 页面加载时初始化业务部门下拉框
    loadBusinessDivisionsForModal();

    // 初始化产品图片上传绑定（页面级，避免未打开模态也绑定失败）
    initializeProductImagesUpload();
    // 初始化产品文档上传绑定
    initializeProductDocumentsUpload();
});

// 初始化产品图片上传（统一使用 AdminUpload）
function initializeProductImagesUpload() {
    try {
        const input = document.getElementById('productImageFile');
        const listEl = document.getElementById('productImagesFileList');
        const hidden = document.getElementById('productImagesFileUrl');
        if (input && listEl) {
            window.AdminUpload && window.AdminUpload.bind(input, {
                fileListEl: listEl,
                fileConfig: {
                    types: ['image/*'],
                    multiple: true,
                    folder: 'product-images',
                    maxSize: '10MB',
                    preview: true
                },
                getResource: function(key, fb) { return (window.Resources?.Shared?.[key]) || fb || key; },
                hiddenInputSelector: hidden ? '#productImagesFileUrl' : undefined
            });
        }
    } catch (e) { console.warn('initializeProductImagesUpload failed', e); }
}

// 初始化产品文档上传（统一使用 AdminUpload）
function initializeProductDocumentsUpload() {
    try {
        const input = document.getElementById('productDocumentFiles');
        const listEl = document.getElementById('productDocumentsFileList');
        if (input && listEl) {
            window.AdminUpload && window.AdminUpload.bind(input, {
                fileListEl: listEl,
                fileConfig: {
                    types: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', 'application/pdf'],
                    multiple: true,
                    folder: 'product-documents',
                    maxSize: '50MB',
                    preview: true
                },
                getResource: function(key, fb) { return (window.Resources?.Shared?.[key]) || fb || key; }
            });
        }
    } catch (e) { console.warn('initializeProductDocumentsUpload failed', e); }
}
