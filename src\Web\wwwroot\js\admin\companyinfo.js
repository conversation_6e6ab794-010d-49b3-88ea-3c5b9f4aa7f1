/**
 * CompanyInfo管理页面 JavaScript
 * 支持多标签页面懒加载和企业历史管理功能
 */

// Tab加载状态追踪
const loadedTabs = new Set(['basic-info']); // 第一个标签已加载



// Tab switching with lazy loading
async function switchTab(tabName) {
    // Update URL hash
    window.history.replaceState(null, null, '#' + tabName);

    // 检查是否已加载
    if (loadedTabs.has(tabName)) {
        // 直接使用全局方法切换
        MlSoftSites.switchTab(tabName);
        return;
    }

    // 对于未加载的标签页，显示加载状态并调用API
    if (tabName !== 'basic-info') {
        await loadTabContent(tabName);
    }

    // 切换显示
    MlSoftSites.switchTab(tabName);
}

// 懒加载标签页内容
async function loadTabContent(tabName) {
    const tabContainer = document.getElementById(`${tabName}-tab`);
    if (!tabContainer) return;

    // 显示加载状态
    showTabLoading(tabContainer);

    try {
        const response = await fetch(buildMultilingualUrl(`tab/${tabName}`, 'CompanyInfo'), {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        const result = await response.json();

        if (result.success) {
            // 插入HTML内容
            tabContainer.innerHTML = result.html;

            // 标记为已加载
            loadedTabs.add(tabName);

            // 初始化该标签页的JavaScript功能
            initializeTabScripts(tabName);

        } else {
            showTabError(tabContainer, result.message);
        }
    } catch (error) {
        console.error('Error loading tab:', error);
        showTabError(tabContainer, window.Resources?.Admin?.LoadTabError || 'Failed to load tab content');
    }
}

// 显示标签页加载状态
function showTabLoading(tabContainer) {
    tabContainer.innerHTML = `
        <div class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-3 text-gray-600 dark:text-gray-400">${window.Resources?.Admin?.Loading || 'Loading'}...</span>
        </div>
    `;
}

// 显示标签页错误状态
function showTabError(tabContainer, message) {
    tabContainer.innerHTML = `
        <div class="text-center py-12">
            <div class="text-red-600 dark:text-red-400 mb-4">
                <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
                <p class="text-lg">${message}</p>
            </div>
            <button onclick="reloadTab('${tabContainer.id.replace('-tab', '')}')"
                    class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-redo mr-2"></i>
                ${window.Resources?.Admin?.Retry || 'Retry'}
            </button>
        </div>
    `;
}

// 重新加载标签页
window.reloadTab = async function(tabName) {
    loadedTabs.delete(tabName);
    await loadTabContent(tabName);
};

// 初始化标签页特定的JavaScript功能
function initializeTabScripts(tabName) {
    switch(tabName) {
        case 'company-history':
            bindCompanyHistoryTabForm();
            if (typeof window.__initEventImageUpload === 'function') {
                window.__initEventImageUpload();
            }
            break;
        case 'executive-organization':
          
            initializeExecutiveOrganizationFunctions();
            bindExecutiveForm();
            bindOrganizationForm();
            break;
        case 'contact-info':
            // Initialize contact info functions
            initializeContactInfoFunctions();
            break;
        case 'csr-activities':
            initializeCSRActivitiesFunctions();
            break;
        case 'investor-relations':
            initializeInvestorRelationsFunctions();
            break;
    }
}

function bindCompanyHistoryTabForm() {
    // Handle history form submission (guard when form not present)
    const historyForm = document.getElementById('historyForm');
    if (historyForm) historyForm.addEventListener('submit', async function (e) {
        e.preventDefault();

        const formData = new FormData(this);
        const historyData = {
            Id: document.getElementById('historyId').value || '',
            EventDate: formData.get('eventDate'),
            EventType: parseInt(formData.get('eventType')),
            DisplayOrder: parseInt(formData.get('displayOrder')) || 0,
            ImageUrl: formData.get('imageUrl') || '',
            IsActive: document.getElementById('isActive').checked,
            EventTitles: {
                zh: formData.get('eventTitle_zh') || '',
                en: formData.get('eventTitle_en') || '',
                ja: formData.get('eventTitle_ja') || ''
            },
            EventDescriptions: {
                zh: formData.get('eventDescription_zh') || '',
                en: formData.get('eventDescription_en') || '',
                ja: formData.get('eventDescription_ja') || ''
            }
        };

        // Validation
        if (!historyData.EventTitles.zh.trim()) {
            Dialog.error(window.Resources?.Admin?.EventTitleZhRequired || 'Chinese event title is required');
            switchLanguageTab('zh');
            document.getElementById('eventTitle_zh').focus();
            return;
        }

        showLoading();

        try {
            const response = await fetch(buildMultilingualUrl('company-history', 'CompanyInfo'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(historyData)
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.SaveHistorySuccess || 'History record saved successfully');
                closeHistoryModal();
                // Reload the page to update the table
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.SaveHistoryError || 'Failed to save history record');
            }
        } catch (error) {
            console.error('Save history error:', error);
            Dialog.error(window.Resources?.Admin?.SaveHistoryError || 'Failed to save history record');
        } finally {
            hideLoading();
        }
    });
}

// Initialize tab from URL hash or default to first tab
function initializeTab() {
    const hash = window.location.hash.substring(1);
    const validTabs = ['basic-info', 'company-history', 'executive-organization', 'contact-info', 'csr-activities', 'investor-relations'];

    if (hash && validTabs.includes(hash)) {
        switchTab(hash);
    } else {
        switchTab('basic-info');
    }
}

// Use global loading methods
function showLoading(message = window.Resources?.Admin?.PleaseWaitProcessing || 'Please wait...') {
    MlSoftSites.showLoading(message);
}

function hideLoading() {
    MlSoftSites.hideLoading();
}

// 企业历史管理函数
// Ensure uploader initializer exists globally (scripts inside injected HTML won't run)
if (typeof window.__initEventImageUpload !== 'function') {
    window.__initEventImageUpload = function () {
        var container = document.getElementById('eventImageUpload');
        if (!container || !window.SimpleImageUpload) return;

        var hidden = document.getElementById('imageUrl');
        var initialUrl = hidden && hidden.value ? hidden.value : '';

        window.__eventImageUploader = window.SimpleImageUpload({
            container: container,
            name: 'eventImage',
            value: initialUrl,
            uploadText: (window.Resources?.Shared?.FileUpload_ClickToUpload || 'Click to upload'),
            autoUpload: true,
            maxWidth: 500,
            maxHeight: 500,
            folder: 'company',
            allowedTypes: 'image/*',
            maxSize: '10MB',
            hiddenInputSelector: '#imageUrl'
        });
    };
}

if (typeof window.__initExecutivePhotoUpload !== 'function') {
    window.__initExecutivePhotoUpload = function () {
        var container = document.getElementById('executivePhotoUpload');
        if (!container || !window.SimpleImageUpload) return;

        // Read existing value for preview
        var existingUrl = (document.getElementById('executivePhotoUrl') || {}).value || '';

        // Render uploader
        var uploader = window.SimpleImageUpload({
            container: container,
            name: 'executivePhoto',
            value: existingUrl,
            uploadText: (window.Resources && (window.Resources.Admin?.SelectPhoto || 'Click to upload')),
            autoUpload: true,        
            maxWidth: 500,
            maxHeight: 500,
            folder: 'executives',
            allowedTypes: 'image/*',
            maxSize: '10MB',
            hiddenInputSelector: '#executivePhotoUrl'
        });

        // Store for potential future use
        window.__executivePhotoUploader = uploader;
    };
}


window.openHistoryModal = function() {
    clearHistoryForm();
    document.getElementById('historyModalTitle').textContent = window.Resources?.Admin?.AddHistoryEvent || 'Add History Event';
    document.getElementById('historyModal').classList.remove('hidden');
    // Lazy init event image upload when modal opens
    if (typeof window.__initEventImageUpload === 'function') {
        window.__initEventImageUpload();
    }
    // Set default to first language (within history modal)
    const firstLang = document.querySelector('.lang-tab-button[data-lang]');
    if (firstLang) {
        switchLanguageTab(firstLang.getAttribute('data-lang'));
    }
};

window.closeHistoryModal = function() {
    document.getElementById('historyModal').classList.add('hidden');
    clearHistoryForm();
};

window.editHistory = function(id) {
    // 从已加载的企业历史标签页中获取数据
    const companyHistoryTab = document.getElementById('company-history-tab');
    if (!companyHistoryTab || !loadedTabs.has('company-history')) {
        Dialog.error(window.Resources?.Admin?.HistoryTabNotLoaded || '请先加载企业历史标签页');
        return;
    }

    // 从表格中查找对应的历史记录行
    const historyRow = companyHistoryTab.querySelector(`tr[data-history-id="${id}"]`);
    if (!historyRow) {
        Dialog.error(window.Resources?.Admin?.HistoryNotFound || 'History record not found');
        return;
    }

    // 通过 API 获取完整的历史记录数据
    fetchHistoryForEdit(id);
};

async function fetchHistoryForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingHistoryData || '正在加载历史记录数据...');

        const response = await fetch(buildMultilingualUrl(`company-history/${id}`, 'CompanyInfo'), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateHistoryForm(result.data);
            // Update modal title and show
            document.getElementById('historyModalTitle').textContent = window.Resources?.Admin?.EditHistoryEvent || 'Edit History Event';
            document.getElementById('historyModal').classList.remove('hidden');
          
            // Set default to first language (within history modal)
            const firstLang = document.querySelector('.lang-tab-button[data-lang]');
            if (firstLang) {
                switchLanguageTab(firstLang.getAttribute('data-lang'));
            }
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.HistoryNotFound || 'History record not found');
        }
    } catch (error) {
        console.error('Fetch history error:', error);
        Dialog.error(window.Resources?.Admin?.LoadHistoryError || 'Failed to load history record');
    } finally {
        hideLoading();
    }
}

function populateHistoryForm(history) {
    // Populate the form - 使用camelCase访问JSON属性
    document.getElementById('historyId').value = history.id;
    document.getElementById('eventDate').value = history.eventDate.split('T')[0];
    document.getElementById('eventType').value = history.eventType.toString();
    document.getElementById('displayOrder').value = history.displayOrder || 0;
    document.getElementById('imageUrl').value = history.imageUrl || '';
    document.getElementById('isActive').checked = history.isActive;

    // Ensure uploader is initialized (no custom prefill; rely on built-in list)
    if (typeof window.__initEventImageUpload === 'function') {
        window.__initEventImageUpload();
    }

    // Populate multilingual fields - 使用camelCase访问JSON属性
    if (history.eventTitles) {
        document.getElementById('eventTitle_zh').value = history.eventTitles.zh || '';
        document.getElementById('eventTitle_en').value = history.eventTitles.en || '';
        document.getElementById('eventTitle_ja').value = history.eventTitles.ja || '';
    }

    if (history.eventDescriptions) {
        document.getElementById('eventDescription_zh').value = history.eventDescriptions.zh || '';
        document.getElementById('eventDescription_en').value = history.eventDescriptions.en || '';
        document.getElementById('eventDescription_ja').value = history.eventDescriptions.ja || '';
    }
}

window.deleteHistory = async function(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete);
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(buildMultilingualUrl(`company-history/${id}`, 'CompanyInfo'), {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteHistorySuccess || 'History record deleted successfully');
                // Reload the page to update the table
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteHistoryError || 'Failed to delete history record');
            }
        } catch (error) {
            console.error('Delete history error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteHistoryError || 'Failed to delete history record');
        } finally {
            hideLoading();
        }
    }
};


function bindExecutiveForm() {
    // Executive Form Submission
    const executiveForm = document.getElementById('executiveForm');
    if (executiveForm) {
        executiveForm.addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData(this);
            const executiveData = {
                Id: document.getElementById('executiveId').value || '',
                PhotoUrl: document.getElementById('executivePhotoUrl').value || '',
                DisplayOrder: parseInt(document.getElementById('executiveDisplayOrder').value) || 0,
                IsPresident: document.getElementById('executiveIsPresident').checked,
                Names: {
                    zh: document.getElementById('executiveName_zh').value || '',
                    en: document.getElementById('executiveName_en').value || '',
                    ja: document.getElementById('executiveName_ja').value || ''
                },
                Positions: {
                    zh: document.getElementById('executivePosition_zh').value || '',
                    en: document.getElementById('executivePosition_en').value || '',
                    ja: document.getElementById('executivePosition_ja').value || ''
                },
                Biographies: {
                    zh: document.getElementById('executiveBiography_zh').value || '',
                    en: document.getElementById('executiveBiography_en').value || '',
                    ja: document.getElementById('executiveBiography_ja').value || ''
                },
                Messages: {
                    zh: document.getElementById('executiveMessage_zh').value || '',
                    en: document.getElementById('executiveMessage_en').value || '',
                    ja: document.getElementById('executiveMessage_ja').value || ''
                }
            };

            // Validate required fields
            if (!executiveData.Names.zh.trim()) {
                Dialog.error(window.Resources?.Admin?.ExecutiveNameRequired || 'Executive name is required');
                document.getElementById('executiveName_zh').focus();
                return;
            }

            showLoading();

            try {
                const response = await fetch(buildMultilingualUrl('executive', 'CompanyInfo'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(executiveData)
                });

                const result = await response.json();

                if (result.success) {
                    Dialog.notify(result.message || window.Resources?.Admin?.SaveExecutiveSuccess || 'Executive saved successfully');
                    closeExecutiveModal();
                    window.location.reload();
                } else {
                    Dialog.error(result.message || window.Resources?.Admin?.SaveExecutiveError || 'Failed to save executive');
                }
            } catch (error) {
                console.error('Save executive error:', error);
                Dialog.error(window.Resources?.Admin?.SaveExecutiveError || 'Failed to save executive');
            } finally {
                hideLoading();
            }
        });
    }

}

function bindOrganizationForm() {
    // Organization Form Submission
    const organizationForm = document.getElementById('organizationForm');
    if (organizationForm) {
        organizationForm.addEventListener('submit', async function (e) {
            e.preventDefault();

            const organizationData = {
                Id: document.getElementById('organizationId').value || '',
                ParentDepartmentId: document.getElementById('organizationParentDepartment').value || null,
                Level: parseInt(document.getElementById('organizationLevel').value) || 0,
                DisplayOrder: parseInt(document.getElementById('organizationDisplayOrder').value) || 0,
                DepartmentNames: {
                    zh: document.getElementById('departmentName_zh').value || '',
                    en: document.getElementById('departmentName_en').value || '',
                    ja: document.getElementById('departmentName_ja').value || ''
                },
                DepartmentDescriptions: {
                    zh: document.getElementById('departmentDescription_zh').value || '',
                    en: document.getElementById('departmentDescription_en').value || '',
                    ja: document.getElementById('departmentDescription_ja').value || ''
                }
            };

            // Validate required fields
            if (!organizationData.DepartmentNames.zh.trim()) {
                Dialog.error(window.Resources?.Admin?.DepartmentNameRequired || 'Department name is required');
                document.getElementById('departmentName_zh').focus();
                return;
            }

            showLoading();

            try {
                const response = await fetch(buildMultilingualUrl('organization', 'CompanyInfo'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(organizationData)
                });

                const result = await response.json();

                if (result.success) {
                    Dialog.notify(result.message || window.Resources?.Admin?.SaveOrganizationSuccess || 'Organization saved successfully');
                    closeOrganizationModal();
                    window.location.reload();
                } else {
                    Dialog.error(result.message || window.Resources?.Admin?.SaveOrganizationError || 'Failed to save organization');
                }
            } catch (error) {
                console.error('Save organization error:', error);
                Dialog.error(window.Resources?.Admin?.SaveOrganizationError || 'Failed to save organization');
            } finally {
                hideLoading();
            }
        });
    }
}

// Executive language tab switching - 使用通用系统
window.switchExecutiveLanguageTab = function(lang) {
    return window.switchLanguageTab(lang, '#executiveModal', {
        buttonClass: 'executive-lang-tab-button',
        contentClass: 'executive-lang-content',
        contentIdPrefix: 'executive-lang-'
    });
};

// Organization language tab switching - 使用通用系统
window.switchOrganizationLanguageTab = function(lang) {
    return window.switchLanguageTab(lang, '#organizationModal', {
        buttonClass: 'organization-lang-tab-button',
        contentClass: 'organization-lang-content',
        contentIdPrefix: 'organization-lang-'
    });
};


window.clearHistoryForm = function() {
    document.getElementById('historyForm').reset();
    document.getElementById('historyId').value = '';
    document.getElementById('imageUrl').value = '';
    document.getElementById('isActive').checked = true;
};

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // Dialog system is initialized globally in _AdminLayout.cshtml

    // Enable TinyMCE form validation for all forms
    document.querySelectorAll('form').forEach(form => {
        if (window.enableTinyMCEFormValidation) {
            window.enableTinyMCEFormValidation(form);
        }
    });

    // Basic Info Form Handler
    document.getElementById('basic-info-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        // 确保TinyMCE内容同步到textarea（在验证之前）
        if (typeof tinymce !== 'undefined') {
            tinymce.triggerSave();
        }

        // 手动验证必填的TinyMCE字段
        let isValid = true;
        const defaultLang = 'zh'; // 假设默认语言是中文

        // 检查必填的公司描述字段
        const companyDescTextarea = document.getElementById(`companyDescription_${defaultLang}`);
        if (companyDescTextarea && companyDescTextarea.getAttribute('data-required') === 'True') {
            const content = companyDescTextarea.value.trim();
            if (!content || content === '<p><br></p>' || content === '<p></p>') {
                isValid = false;
                // 显示TinyMCE编辑器并聚焦
                const editor = tinymce.get(`companyDescription_${defaultLang}`);
                if (editor) {
                    editor.focus();
                    // 显示错误消息
                    if (typeof Dialog !== 'undefined' && Dialog.error) {
                        Dialog.error(window.Resources?.Admin?.CompanyDescriptionRequired || '请填写公司简介');
                    }
                }
                return;
            }
        }

        // 如果手动验证通过，检查其他标准HTML5验证
        if (isValid && !this.checkValidity()) {
            this.reportValidity();
            return;
        }

        const formData = new FormData(e.target);

        // 收集多语言字段
        const companyNames = {};
        const companyDescriptions = {};
        const philosophy = {};

        document.querySelectorAll('input[name^="companyNames["]').forEach(input => {
            const langMatch = input.name.match(/companyNames\[(.+?)\]/);
            if (langMatch) {
                companyNames[langMatch[1]] = input.value;
            }
        });

        document.querySelectorAll('textarea[name^="companyDescriptions["]').forEach(textarea => {
            const langMatch = textarea.name.match(/companyDescriptions\[(.+?)\]/);
            if (langMatch) {
                companyDescriptions[langMatch[1]] = textarea.value;
            }
        });

        document.querySelectorAll('textarea[name^="philosophy["]').forEach(textarea => {
            const langMatch = textarea.name.match(/philosophy\[(.+?)\]/);
            if (langMatch) {
                philosophy[langMatch[1]] = textarea.value;
            }
        });

        const data = {
            companyNames: companyNames,
            companyDescriptions: companyDescriptions,
            philosophy: philosophy,
            establishedDate: formData.get('establishedDate'),
            registrationNumber: formData.get('registrationNumber'),
            capital: formData.get('capital') ? parseFloat(formData.get('capital')) : null,
            currency: formData.get('currency') || null,
            employeeScale: formData.get('employeeScale') ? parseInt(formData.get('employeeScale')) : null,
            isActive: true
        };

        try {
            showLoading(window.Resources?.Admin?.SavingBasicInfo || 'Saving basic info...');

            const response = await fetch(buildMultilingualUrl('basic-info', 'CompanyInfo'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                Dialog.alert(result.message, null, 'error');
            }
        } catch (error) {
            console.error('Save basic info error:', error);
            Dialog.alert(window.Resources?.Admin?.SaveBasicInfoError || 'Save failed', null, 'error');
        } finally {
            hideLoading();
        }
    });




    

    // Initialize tab based on URL hash
    initializeTab();

    // Handle browser back/forward navigation
    window.addEventListener('hashchange', function() {
        initializeTab();
    });

    // Auto-hide alerts
    const alerts = document.querySelectorAll('[id$="-alert"]');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.classList.add('hidden');
        }, 5000);
    });
});
function openExecutiveModal(id = null) {
    const modal = document.getElementById('executiveModal');
    const form = document.getElementById('executiveForm');
    const title = document.getElementById('executiveModalTitle');

    // Reset form
    form.reset();
    document.getElementById('executiveId').value = '';

    // Reset photo uploader and initialize SimpleImageUpload
    resetExecutivePhotoPreview();

    // Initialize SimpleImageUpload for photo upload
    if (window.__initExecutivePhotoUpload) {
        window.__initExecutivePhotoUpload();
    }

    // Initialize first language tab
    const firstLangBtn = document.querySelector('.executive-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#executiveModal', {
            buttonClass: 'executive-lang-tab-button',
            contentClass: 'executive-lang-content',
            contentIdPrefix: 'executive-lang-'
        });
    }

    if (id) {
        title.textContent = window.Resources?.Admin?.Edit || 'Edit Executive';
        loadExecutiveForEdit(id);
    } else {
        title.textContent = window.Resources?.Admin?.AddExecutive || 'Add Executive';
    }

    modal.classList.remove('hidden');
}

function closeExecutiveModal() {
    const modal = document.getElementById('executiveModal');
    modal.classList.add('hidden');
}

function resetExecutivePhotoPreview() {
    const photoUrl = document.getElementById('executivePhotoUrl');
    if (photoUrl) photoUrl.value = '';
    const container = document.getElementById('executivePhotoUpload');
    if (container) container.innerHTML = '';
}

// Note: previewExecutivePhoto function removed as it's now handled by AdminUpload system

async function loadExecutiveForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl('executive', 'CompanyInfo') + '/' + id, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateExecutiveForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.RecordNotFound || 'Record not found');
        }
    } catch (error) {
        console.error('Load executive error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load record');
    } finally {
        hideLoading();
    }
}

function populateExecutiveForm(executive) {
    document.getElementById('executiveId').value = executive.id;
    document.getElementById('executivePhotoUrl').value = executive.photoUrl || '';
    document.getElementById('executiveDisplayOrder').value = executive.displayOrder || 0;
    document.getElementById('executiveIsPresident').checked = executive.isPresident;

    // Initialize uploader first (will use hidden url as initial value)
    if (window.__initExecutivePhotoUpload) {
        window.__initExecutivePhotoUpload();
    }

    // Populate multilingual fields
    if (executive.names) {
        document.getElementById('executiveName_zh').value = executive.names.zh || '';
        document.getElementById('executiveName_en').value = executive.names.en || '';
        document.getElementById('executiveName_ja').value = executive.names.ja || '';
    }

    if (executive.positions) {
        document.getElementById('executivePosition_zh').value = executive.positions.zh || '';
        document.getElementById('executivePosition_en').value = executive.positions.en || '';
        document.getElementById('executivePosition_ja').value = executive.positions.ja || '';
    }

    if (executive.biographies) {
        document.getElementById('executiveBiography_zh').value = executive.biographies.zh || '';
        document.getElementById('executiveBiography_en').value = executive.biographies.en || '';
        document.getElementById('executiveBiography_ja').value = executive.biographies.ja || '';
    }

    if (executive.messages) {
        document.getElementById('executiveMessage_zh').value = executive.messages.zh || '';
        document.getElementById('executiveMessage_en').value = executive.messages.en || '';
        document.getElementById('executiveMessage_ja').value = executive.messages.ja || '';
    }
}

// Executive CRUD Operations
async function editExecutive(id) {
    openExecutiveModal(id);
}

async function deleteExecutive(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete);
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(buildMultilingualUrl('executive', 'CompanyInfo') + '/' + id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteExecutiveSuccess || 'Executive deleted successfully');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteExecutiveError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete executive error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteExecutiveError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
}

// Organization Management Functions
function openOrganizationModal(id = null) {
    const modal = document.getElementById('organizationModal');
    const form = document.getElementById('organizationForm');
    const title = document.getElementById('organizationModalTitle');

    // Reset form
    form.reset();
    document.getElementById('organizationId').value = '';

    // Populate parent department options
    populateParentDepartmentOptions();

    // Initialize first language tab
    const firstLangBtn = document.querySelector('.organization-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#organizationModal', {
            buttonClass: 'organization-lang-tab-button',
            contentClass: 'organization-lang-content',
            contentIdPrefix: 'organization-lang-'
        });
    }

    if (id) {
        title.textContent = window.Resources?.Admin?.Edit || 'Edit Department';
        loadOrganizationForEdit(id);
    } else {
        title.textContent = window.Resources?.Admin?.AddDepartment || 'Add Department';
    }

    modal.classList.remove('hidden');
}

function closeOrganizationModal() {
    const modal = document.getElementById('organizationModal');
    modal.classList.add('hidden');
}

function populateParentDepartmentOptions() {
    const select = document.getElementById('organizationParentDepartment');

    // Clear existing options (except root option)
    const rootOption = select.firstElementChild;
    select.innerHTML = '';
    select.appendChild(rootOption);

    // Get current organizations from the table
    const organizationRows = document.querySelectorAll('[data-organization-id]');
    organizationRows.forEach(row => {
        const id = row.getAttribute('data-organization-id');
        const nameCell = row.querySelector('td:first-child .text-sm');
        if (nameCell) {
            const option = document.createElement('option');
            option.value = id;
            option.textContent = nameCell.textContent.trim();
            select.appendChild(option);
        }
    });
}

async function loadOrganizationForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl('organization', 'CompanyInfo') + '/' + id, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateOrganizationForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.RecordNotFound || 'Record not found');
        }
    } catch (error) {
        console.error('Load organization error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load record');
    } finally {
        hideLoading();
    }
}

function populateOrganizationForm(organization) {
    document.getElementById('organizationId').value = organization.id;
    document.getElementById('organizationParentDepartment').value = organization.parentDepartmentId || '';
    document.getElementById('organizationLevel').value = organization.level || 0;
    document.getElementById('organizationDisplayOrder').value = organization.displayOrder || 0;

    // Populate multilingual fields
    if (organization.departmentNames) {
        document.getElementById('departmentName_zh').value = organization.departmentNames.zh || '';
        document.getElementById('departmentName_en').value = organization.departmentNames.en || '';
        document.getElementById('departmentName_ja').value = organization.departmentNames.ja || '';
    }

    if (organization.departmentDescriptions) {
        document.getElementById('departmentDescription_zh').value = organization.departmentDescriptions.zh || '';
        document.getElementById('departmentDescription_en').value = organization.departmentDescriptions.en || '';
        document.getElementById('departmentDescription_ja').value = organization.departmentDescriptions.ja || '';
    }
}

// Organization CRUD Operations
async function editOrganization(id) {
    openOrganizationModal(id);
}

async function deleteOrganization(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete );
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(buildMultilingualUrl('organization', 'CompanyInfo') + '/' + id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteOrganizationSuccess || 'Organization deleted successfully');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteOrganizationError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete organization error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteOrganizationError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
}

// Form submission handlers - Add these to the DOMContentLoaded event handler


function loadMap(){
    // Map link live preview
    try {
        var mapInput = document.getElementById('mapLink');
        var mapPreview = document.getElementById('mapPreivew');

        if (mapInput && mapPreview) {
            var renderMapPreview = function () {
                var raw = (mapInput.value || '').trim();
                if (!raw) {
                    mapPreview.innerHTML = '';
                    return;
                }

                var embedUrl = raw.indexOf("<iframe") !== -1 ? raw : '<iframe src="' + raw + '" class="w-full h-64 rounded-md border border-gray-200 dark:border-gray-700" referrerpolicy="no-referrer-when-downgrade" loading="lazy" allowfullscreen></iframe>';

                // Render iframe
                mapPreview.innerHTML = '' +
                    '<div class="mt-3">' + embedUrl +
                    '</div>';
            };

            var debounce = function (fn, wait) {
                var t = null;
                return function () {
                    clearTimeout(t);
                    t = setTimeout(fn, wait);
                };
            };

            var debouncedRender = debounce(renderMapPreview, 300);
            mapInput.addEventListener('input', debouncedRender);
            mapInput.addEventListener('change', renderMapPreview);

            // Initial render if prefilled
            if ((mapInput.value || '').trim()) {
                renderMapPreview();
            }
        }
    } catch (e) { /* noop */ }
}

// Contact Info Management Functions
function initializeContactInfoFunctions() {
    // Initialize contact info form handler when tab loads
    console.log('Contact info functions initialized');

    // Contact Info Form Submission
    const contactInfoForm = document.getElementById('contactInfoForm');
    if (contactInfoForm) {
        contactInfoForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const contactData = {
                phone: formData.get('phone') || '',
                fax: formData.get('fax') || '',
                email: formData.get('email') || '',
                website: formData.get('website') || '',
                postalCode: formData.get('postalCode') || '',
                mapLink: formData.get('mapLink') || '',
                // latitude: formData.get('latitude') ? parseFloat(formData.get('latitude')) : null,
                // longitude: formData.get('longitude') ? parseFloat(formData.get('longitude')) : null,
                addresses: {},
                businessHours: {},
                accessInfo: {}
            };

            // Collect multilingual fields
            document.querySelectorAll('[name^="address_"]').forEach(input => {
                const langMatch = input.name.match(/address_(.+)/);
                if (langMatch) {
                    contactData.addresses[langMatch[1]] = input.value;
                }
            });

            document.querySelectorAll('[name^="businessHours_"]').forEach(input => {
                const langMatch = input.name.match(/businessHours_(.+)/);
                if (langMatch) {
                    contactData.businessHours[langMatch[1]] = input.value;
                }
            });

            document.querySelectorAll('[name^="accessInfo_"]').forEach(input => {
                const langMatch = input.name.match(/accessInfo_(.+)/);
                if (langMatch) {
                    contactData.accessInfo[langMatch[1]] = input.value;
                }
            });

            showLoading();

            try {
                const response = await fetch(buildMultilingualUrl('contact-info', 'CompanyInfo'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(contactData)
                });

                const result = await response.json();

                if (result.success) {
                    Dialog.notify(result.message || window.Resources?.Admin?.SaveContactInfoSuccess || 'Contact info saved successfully');
                    closeContactInfoModal();
                    window.location.reload();
                } else {
                    Dialog.error(result.message || window.Resources?.Admin?.SaveContactInfoError || 'Failed to save contact info');
                }
            } catch (error) {
                console.error('Save contact info error:', error);
                Dialog.error(window.Resources?.Admin?.SaveContactInfoError || 'Failed to save contact info');
            } finally {
                hideLoading();
            }
        });
    }

    

    // Location Form Submission
    const locationForm = document.getElementById('locationForm');
    if (locationForm) {
        locationForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const locationData = {
                id: document.getElementById('locationId').value || '',
                type: parseInt(formData.get('type')),
                displayOrder: parseInt(formData.get('displayOrder')) || 0,
                isPrimary: document.getElementById('locationIsPrimary').checked,
                isActive: document.getElementById('locationIsActive').checked,
                names: {},
                contactInfo: {
                    phone: document.getElementById('locationPhone').value || '',
                    fax: document.getElementById('locationFax').value || '',
                    email: document.getElementById('locationEmail').value || '',
                    postalCode: document.getElementById('locationPostalCode').value || '',
                    mapLink: document.getElementById('locationMapLink').value || '',
                    // latitude: document.getElementById('locationLatitude').value ? parseFloat(document.getElementById('locationLatitude').value) : null,
                    // longitude: document.getElementById('locationLongitude').value ? parseFloat(document.getElementById('locationLongitude').value) : null,
                    addresses: {},
                    businessHours: {},
                    accessInfo: {}
                }
            };

            // Collect location names
            document.querySelectorAll('[name^="names["]').forEach(input => {
                const langMatch = input.name.match(/names\[(.+?)\]/);
                if (langMatch) {
                    locationData.names[langMatch[1]] = input.value;
                }
            });

            // Collect contact info multilingual fields
            document.querySelectorAll('[name^="contactInfo.address_"]').forEach(input => {
                const langMatch = input.name.match(/contactInfo\.address_(.+)/);
                if (langMatch) {
                    locationData.contactInfo.addresses[langMatch[1]] = input.value;
                }
            });

            document.querySelectorAll('[name^="contactInfo.businessHours_"]').forEach(input => {
                const langMatch = input.name.match(/contactInfo\.businessHours_(.+)/);
                if (langMatch) {
                    locationData.contactInfo.businessHours[langMatch[1]] = input.value;
                }
            });

            document.querySelectorAll('[name^="contactInfo.accessInfo_"]').forEach(input => {
                const langMatch = input.name.match(/contactInfo\.accessInfo_(.+)/);
                if (langMatch) {
                    locationData.contactInfo.accessInfo[langMatch[1]] = input.value;
                }
            });

            // Validate required fields
            if (!locationData.names.zh || !locationData.names.zh.trim()) {
                Dialog.error(window.Resources?.Admin?.LocationNameRequired || 'Location name is required');
                document.getElementById('locationName-zh').focus();
                return;
            }

            showLoading();

            try {
                const response = await fetch(buildMultilingualUrl('location', 'CompanyInfo'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(locationData)
                });

                const result = await response.json();

                if (result.success) {
                    Dialog.notify(result.message || window.Resources?.Admin?.SaveLocationSuccess || 'Location saved successfully');
                    closeLocationModal();
                    window.location.reload();
                } else {
                    Dialog.error(result.message || window.Resources?.Admin?.SaveLocationError || 'Failed to save location');
                }
            } catch (error) {
                console.error('Save location error:', error);
                Dialog.error(window.Resources?.Admin?.SaveLocationError || 'Failed to save location');
            } finally {
                hideLoading();
            }
        });
    }
}

// Contact Info Modal Functions
window.openContactInfoModal = function() {
    const modal = document.getElementById('contactInfoModal');
    const title = document.getElementById('contactInfoModalTitle');

    // Load current contact info data
    loadCurrentContactInfo();

    title.textContent = window.Resources?.Admin?.EditContactInfo || 'Edit Contact Info';
    modal.classList.remove('hidden');

    // Set default to first language
    const firstLang = document.querySelector('.contact-lang-tab-button[data-lang]');
    if (firstLang) {
        window.switchLanguageTab(firstLang.getAttribute('data-lang'), '#contactInfoModal', {
            buttonClass: 'contact-lang-tab-button',
            contentClass: 'contact-lang-content',
            contentIdPrefix: 'contact-lang-'
        });
    }
};

window.closeContactInfoModal = function() {
    const modal = document.getElementById('contactInfoModal');
    modal.classList.add('hidden');
    clearContactInfoForm();
};

async function loadCurrentContactInfo() {
    try {
        showLoading(window.Resources?.Admin?.LoadingContactInfo || 'Loading contact info...');

        // Get contact info from current tab data (if loaded)
        const contactTab = document.getElementById('contact-info-tab');
        if (!contactTab || !loadedTabs.has('contact-info')) {
            // Load tab first if not loaded
            await loadTabContent('contact-info');
        }

        // Extract data from the loaded tab and populate form
        populateContactInfoFromTab();
       
    } catch (error) {
        console.error('Load contact info error:', error);
        Dialog.error(window.Resources?.Admin?.LoadContactInfoError || 'Failed to load contact info');
    } finally {
        hideLoading();
    }
}

async function populateContactInfoFromTab() {
    try {
        // 从API获取完整的联系信息数据
        const response = await fetch(buildMultilingualUrl('contact-info', 'CompanyInfo'), {
            method: 'GET',
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        const result = await response.json();

        if (result.success && result.data) {
            console.log('Contact info data received:', result.data);
            
            // 检查数据结构，API返回的是basicContactInfo（小写）
            const contactData = result.data.basicContactInfo || result.data.BasicContactInfo || result.data;
            
            if (!contactData) {
                console.warn('No contact data found in response');
                return;
            }
            
            // 填充基本联系信息字段（API返回的字段名是小写）
            document.getElementById('contactPhone').value = contactData.phone  || '';
            document.getElementById('contactFax').value = contactData.fax || '';
            document.getElementById('contactEmail').value = contactData.email  || '';
            document.getElementById('contactWebsite').value = contactData.website || '';
            document.getElementById('contactPostalCode').value = contactData.postalCode || '';
            document.getElementById('mapLink').value = contactData.mapLink  || '';
            loadMap();

            // 填充多语言字段（API返回的字段名是小写）
            const addresses = contactData.addresses || contactData.Addresses;
            if (addresses) {
                Object.keys(addresses).forEach(langCode => {
                    const addressInput = document.getElementById(`contactAddress_${langCode}`);
                    if (addressInput) {
                        addressInput.value = addresses[langCode] || '';
                    }
                });
            }

            const businessHours = contactData.businessHours || contactData.BusinessHours;
            if (businessHours) {
                Object.keys(businessHours).forEach(langCode => {
                    const businessHoursInput = document.getElementById(`contactBusinessHours_${langCode}`);
                    if (businessHoursInput) {
                        businessHoursInput.value = businessHours[langCode] || '';
                    }
                });
            }

            const accessInfo = contactData.accessInfo || contactData.AccessInfo;
            if (accessInfo) {
                Object.keys(accessInfo).forEach(langCode => {
                    const accessInfoInput = document.getElementById(`contactAccessInfo_${langCode}`);
                    if (accessInfoInput) {
                        accessInfoInput.value = accessInfo[langCode] || '';
                    }
                });
            }
        } else {
            console.warn('Failed to load contact info data:', result.message);
        }
    } catch (error) {
        console.error('Error loading contact info data:', error);
    }
}

function clearContactInfoForm() {
    const form = document.getElementById('contactInfoForm');
    if (form) {
        form.reset();
    }
}

// Map Link Modal Functions
window.openMapLinkModal = function(mapLink) {
    const modal = document.getElementById('mapLinkModal');
    const mapLinkDisplay = document.getElementById('mapLinkDisplay');
    const mapPreview = document.getElementById('mapPreview');
    
    if (mapLinkDisplay) {
        mapLinkDisplay.value = mapLink;
    }
    
    // 设置iframe的src为地图链接
    if (mapPreview) {
        if(mapLink.indexOf("<iframe") !== -1) {
            mapPreview.outerHTML = mapLink;
        } else {
            mapPreview.src = mapLink;
        }
    }
    
    modal.classList.remove('hidden');
};

window.closeMapLinkModal = function() {
    const modal = document.getElementById('mapLinkModal');
    const mapPreview = document.getElementById('mapPreview');
    
    modal.classList.add('hidden');
    
    // 清空iframe的src以停止加载
    if (mapPreview) {
        mapPreview.src = '';
    }
};

window.copyMapLink = function() {
    const mapLinkDisplay = document.getElementById('mapLinkDisplay');
    if (mapLinkDisplay) {
        mapLinkDisplay.select();
        mapLinkDisplay.setSelectionRange(0, 99999); // 对于移动设备
        
        try {
            document.execCommand('copy');
            Dialog.notify(window.Resources?.Admin?.MapLinkCopied || 'Map link copied to clipboard');
        } catch (err) {
            // 如果execCommand失败，尝试使用现代API
            if (navigator.clipboard) {
                navigator.clipboard.writeText(mapLinkDisplay.value).then(() => {
                    Dialog.notify(window.Resources?.Admin?.MapLinkCopied || 'Map link copied to clipboard');
                }).catch(() => {
                    Dialog.error(window.Resources?.Admin?.CopyFailed || 'Failed to copy map link');
                });
            } else {
                Dialog.error(window.Resources?.Admin?.CopyNotSupported || 'Copy not supported in this browser');
            }
        }
    }
};

window.openMapInNewTab = function() {
    const mapLinkDisplay = document.getElementById('mapLinkDisplay');
    if (mapLinkDisplay && mapLinkDisplay.value) {
        var mapUrl = mapLinkDisplay.value;

        if (mapUrl) {
            // 判断是否是iframe代码
            if (mapUrl.indexOf('<iframe') !== -1) {
                // 提取iframe的src属性
                var srcMatch = mapUrl.match(/src\s*=\s*["']([^"']+)["']/i);
                if (srcMatch && srcMatch[1]) {
                    mapUrl = srcMatch[1];
                }
            }
            
            window.open(mapUrl, '_blank');
        }
    }
};

// Company info language tab switching - 使用通用系统
window.switchCompanyInfoLanguageTab = function(lang) {
    const result = window.switchLanguageTab(lang, '#company-info-section', {
        buttonClass: 'company-info-lang-tab-button',
        contentClass: 'company-info-lang-content',
        contentIdPrefix: 'company-info-lang-'
    });
    
    if (result) {
        // Re-initialize TinyMCE for newly visible textareas
        setTimeout(() => {
            if (window.AdminTinyMCE) {
                const activeContent = document.getElementById(`company-info-lang-${lang}`);
                if (activeContent) {
                    const newTextareas = activeContent.querySelectorAll('.tinymce-editor');
                    newTextareas.forEach(textarea => {
                        if (!tinymce.get(textarea.id)) {
                            AdminTinyMCE.init(textarea, {
                                menubar: false,
                                height: 200
                            });
                        }
                    });
                }
            }
        }, 100);
    }
    
    return result;
};

// Contact language tab switching - 使用通用系统
window.switchContactLanguageTab = function(lang) {
    return window.switchLanguageTab(lang, '#contactInfoModal', {
        buttonClass: 'contact-lang-tab-button',
        contentClass: 'contact-lang-content',
        contentIdPrefix: 'contact-lang-'
    });
};

// Location Management Functions
window.openLocationModal = function(id = null) {
    const modal = document.getElementById('locationModal');
    const form = document.getElementById('locationForm');
    const title = document.getElementById('locationModalTitle');

    // Reset form
    form.reset();
    document.getElementById('locationId').value = '';

    // Initialize first language tab
    const firstLangBtn = document.querySelector('.location-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#locationModal', {
            buttonClass: 'location-lang-tab-button',
            contentClass: 'location-lang-content',
            contentIdPrefix: 'location-lang-'
        });
    }

    if (id) {
        title.textContent = window.Resources?.Admin?.EditLocation || 'Edit Location';
        loadLocationForEdit(id);
    } else {
        title.textContent = window.Resources?.Admin?.AddLocation || 'Add Location';
    }

    modal.classList.remove('hidden');
};

window.closeLocationModal = function() {
    const modal = document.getElementById('locationModal');
    modal.classList.add('hidden');
};

async function loadLocationForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl('location', 'CompanyInfo') + '/' + id, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateLocationForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.LocationNotFound || 'Location not found');
        }
    } catch (error) {
        console.error('Load location error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load location');
    } finally {
        hideLoading();
    }
}

function populateLocationForm(location) {
    document.getElementById('locationId').value = location.id;
    document.getElementById('locationType').value = location.type.toString();
    document.getElementById('locationDisplayOrder').value = location.displayOrder || 0;
    document.getElementById('locationIsPrimary').checked = location.isPrimary;
    document.getElementById('locationIsActive').checked = location.isActive;

    // Populate location names
    if (location.names) {
        Object.keys(location.names).forEach(lang => {
            const nameInput = document.getElementById(`locationName-${lang}`);
            if (nameInput) {
                nameInput.value = location.names[lang] || '';
            }
        });
    }

    // Populate contact info
    if (location.contactInfo) {
        document.getElementById('locationPhone').value = location.contactInfo.phone || '';
        document.getElementById('locationFax').value = location.contactInfo.fax || '';
        document.getElementById('locationEmail').value = location.contactInfo.email || '';
        document.getElementById('locationPostalCode').value = location.contactInfo.postalCode || '';
        document.getElementById('locationMapLink').value = location.contactInfo.mapLink || '';
        // document.getElementById('locationLatitude').value = location.contactInfo.latitude || '';
        // document.getElementById('locationLongitude').value = location.contactInfo.longitude || '';

        // Populate multilingual contact info
        if (location.contactInfo.addresses) {
            Object.keys(location.contactInfo.addresses).forEach(lang => {
                const addressInput = document.getElementById(`locationAddress_${lang}`);
                if (addressInput) {
                    addressInput.value = location.contactInfo.addresses[lang] || '';
                }
            });
        }

        if (location.contactInfo.businessHours) {
            Object.keys(location.contactInfo.businessHours).forEach(lang => {
                const hoursInput = document.getElementById(`locationBusinessHours_${lang}`);
                if (hoursInput) {
                    hoursInput.value = location.contactInfo.businessHours[lang] || '';
                }
            });
        }

        if (location.contactInfo.accessInfo) {
            Object.keys(location.contactInfo.accessInfo).forEach(lang => {
                const accessInput = document.getElementById(`locationAccessInfo_${lang}`);
                if (accessInput) {
                    accessInput.value = location.contactInfo.accessInfo[lang] || '';
                }
            });
        }
    }
}

// Location language tab switching - 使用通用系统
window.switchLocationLanguageTab = function(lang) {
    return window.switchLanguageTab(lang, '#locationModal', {
        buttonClass: 'location-lang-tab-button',
        contentClass: 'location-lang-content',
        contentIdPrefix: 'location-lang-'
    });
};

// Location CRUD Operations
window.editLocation = function(id) {
    openLocationModal(id);
};

window.deleteLocation = async function(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete);
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(buildMultilingualUrl('location', 'CompanyInfo') + '/' + id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteSuccess || 'Location deleted successfully');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete location error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
};

// Helper function to get image URLs from file list
function getCSRActivityImageUrls() {
    const imageFileList = document.getElementById('csrActivityImageFileList');
    if (!imageFileList) return [];
    
    const fileItems = imageFileList.querySelectorAll('[data-file-path]');
    return Array.from(fileItems).map(item => item.getAttribute('data-file-path')).filter(url => url && url.trim());
}

// Populate file uploads with existing data
function populateCSRActivityFileUploads(activity) {
    // Clear existing file lists
    const reportFileList = document.getElementById('csrActivityReportFileList');
    const imageFileList = document.getElementById('csrActivityImageFileList');
    
    if (reportFileList) reportFileList.innerHTML = '';
    if (imageFileList) imageFileList.innerHTML = '';
    
    // Populate report file
    if (activity.reportFileUrl && reportFileList) {
        const reportFileInfo = {
            filePath: activity.reportFileUrl,
            originalName: activity.reportFileUrl.split('/').pop() || 'report.pdf',
            contentType: 'application/pdf'
        };
        
        if (window.AdminUpload && window.AdminUpload.renderInitialFiles) {
            window.AdminUpload.renderInitialFiles(reportFileList, {
                types: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
                multiple: false,
                folder: 'csr-reports',
                maxSize: '50MB',
                preview: true
            }, [reportFileInfo]);
        }
        
        // Update hidden input
        const reportFileUrlInput = document.getElementById('csrActivityReportFileUrl');
        if (reportFileUrlInput) {
            reportFileUrlInput.value = activity.reportFileUrl;
        }
    }
    
    // Populate image files
    if (activity.imageUrls && activity.imageUrls.length > 0 && imageFileList) {
        const imageFileInfos = activity.imageUrls.map(url => ({
            filePath: url,
            originalName: url.split('/').pop() || 'image.jpg',
            contentType: 'image/jpeg'
        }));
        
        if (window.AdminUpload && window.AdminUpload.renderInitialFiles) {
            window.AdminUpload.renderInitialFiles(imageFileList, {
                types: ['image/*'],
                multiple: true,
                folder: 'csr-images',
                maxSize: '10MB',
                preview: true
            }, imageFileInfos);
        }
        
        // Update hidden input
        const imageUrlsInput = document.getElementById('csrActivityImageUrls');
        if (imageUrlsInput) {
            imageUrlsInput.value = activity.imageUrls.join(',');
        }
    }
}

// Initialize file uploads for CSR Activities
function initializeCSRActivityFileUploads() {
    // Initialize report file upload
    const reportFileInput = document.getElementById('csrActivityReportFile');
    const reportFileList = document.getElementById('csrActivityReportFileList');
    const reportFileUrlInput = document.getElementById('csrActivityReportFileUrl');
    
    if (reportFileInput && reportFileList && reportFileUrlInput) {
        window.AdminUpload.bind(reportFileInput, {
            fileListEl: reportFileList,
            fileConfig: {
                types: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
                multiple: false,
                folder: 'csr-reports',
                maxSize: '50MB',
                preview: true
            },
            getResource: function(key, fallback) {
                return window.Resources?.Shared?.[key] || fallback || key;
            },
            hiddenInputSelector: '#csrActivityReportFileUrl'
        });
    }

    // Initialize image files upload
    const imageFileInput = document.getElementById('csrActivityImageFiles');
    const imageFileList = document.getElementById('csrActivityImageFileList');
    const imageUrlsInput = document.getElementById('csrActivityImageUrls');
    
    if (imageFileInput && imageFileList && imageUrlsInput) {
        window.AdminUpload.bind(imageFileInput, {
            fileListEl: imageFileList,
            fileConfig: {
                types: ['image/*'],
                multiple: true,
                folder: 'csr-images',
                maxSize: '10MB',
                preview: true
            },
            getResource: function(key, fallback) {
                return window.Resources?.Shared?.[key] || fallback || key;
            },
            hiddenInputSelector: '#csrActivityImageUrls'
        });
    }
}

// CSR Activities Management Functions
function initializeCSRActivitiesFunctions() {
    console.log('CSR Activities functions initialized');

    // Initialize file uploads for CSR Activities
    initializeCSRActivityFileUploads();

    // CSR Activity Form Submission
    const csrActivityForm = document.getElementById('csrActivityForm');
    if (csrActivityForm) {
        csrActivityForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const csrActivityData = {
                id: document.getElementById('csrActivityId').value || '',
                category: parseInt(formData.get('category')),
                startDate: formData.get('startDate'),
                endDate: formData.get('endDate') || null,
                imageUrls: getCSRActivityImageUrls(),
                reportFileUrl: document.getElementById('csrActivityReportFileUrl').value || '',
                isActive: document.getElementById('csrActivityIsActive').checked,
                titles: {},
                descriptions: {},
                summaries: {}
            };

            // Collect multilingual fields
            document.querySelectorAll('[name^="title_"]').forEach(input => {
                const langMatch = input.name.match(/title_(.+)/);
                if (langMatch) {
                    csrActivityData.titles[langMatch[1]] = input.value;
                }
            });

            document.querySelectorAll('[name^="description_"]').forEach(textarea => {
                const langMatch = textarea.name.match(/description_(.+)/);
                if (langMatch) {
                    csrActivityData.descriptions[langMatch[1]] = textarea.value;
                }
            });

            document.querySelectorAll('[name^="summary_"]').forEach(textarea => {
                const langMatch = textarea.name.match(/summary_(.+)/);
                if (langMatch) {
                    csrActivityData.summaries[langMatch[1]] = textarea.value;
                }
            });

            // Validate required fields
            if (!csrActivityData.titles.zh || !csrActivityData.titles.zh.trim()) {
                Dialog.error(window.Resources?.Admin?.CSRActivityTitleRequired || 'CSR Activity title is required');
                document.getElementById('csrActivityTitle_zh').focus();
                return;
            }

            showLoading();

            try {
                const response = await fetch(buildMultilingualUrl('csr-activity', 'CompanyInfo'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(csrActivityData)
                });

                const result = await response.json();

                if (result.success) {
                    Dialog.notify(result.message || window.Resources?.Admin?.SaveCSRActivitySuccess || 'CSR Activity saved successfully');
                    closeCSRActivityModal();
                    window.location.reload();
                } else {
                    Dialog.error(result.message || window.Resources?.Admin?.SaveCSRActivityError || 'Failed to save CSR Activity');
                }
            } catch (error) {
                console.error('Save CSR Activity error:', error);
                Dialog.error(window.Resources?.Admin?.SaveCSRActivityError || 'Failed to save CSR Activity');
            } finally {
                hideLoading();
            }
        });
    }
}

// CSR Activity Modal Functions
window.openCSRActivityModal = function(id = null) {
    const modal = document.getElementById('csrActivityModal');
    const form = document.getElementById('csrActivityForm');
    const title = document.getElementById('csrActivityModalTitle');

    // Check if modal elements exist
    if (!modal || !form || !title) {
        console.warn('CSR Activity modal elements not found. Modal may not be loaded yet.');
        return;
    }

    // Reset form
    form.reset();
    const idInput = document.getElementById('csrActivityId');
    if (idInput) {
        idInput.value = '';
    }



    // Initialize file uploads
    initializeCSRActivityFileUploads();

    // Clear file uploads
    const reportFileList = document.getElementById('csrActivityReportFileList');
    const imageFileList = document.getElementById('csrActivityImageFileList');
    if (reportFileList) reportFileList.innerHTML = '';
    if (imageFileList) imageFileList.innerHTML = '';

    // Initialize first language tab
    const firstLangBtn = document.querySelector('.csr-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#csrActivityModal', {
            buttonClass: 'csr-lang-tab-button',
            contentClass: 'csr-lang-content',
            contentIdPrefix: 'csr-lang-'
        });
    }

    if (id) {
        title.textContent = window.Resources?.Admin?.EditCSRActivity || 'Edit CSR Activity';
        loadCSRActivityForEdit(id);
    } else {
        title.textContent = window.Resources?.Admin?.AddCSRActivity || 'Add CSR Activity';
    }

    modal.classList.remove('hidden');
};

window.closeCSRActivityModal = function() {
    const modal = document.getElementById('csrActivityModal');
    if (modal) {
        modal.classList.add('hidden');
    }
};

async function loadCSRActivityForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl('csr-activity', 'CompanyInfo') + '/' + id, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateCSRActivityForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.CSRActivityNotFound || 'CSR Activity not found');
        }
    } catch (error) {
        console.error('Load CSR Activity error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load CSR Activity');
    } finally {
        hideLoading();
    }
}

function populateCSRActivityForm(activity) {
    document.getElementById('csrActivityId').value = activity.id;
    document.getElementById('csrActivityCategory').value = activity.category.toString();
    document.getElementById('csrActivityStartDate').value = activity.startDate.split('T')[0];
    document.getElementById('csrActivityEndDate').value = activity.endDate ? activity.endDate.split('T')[0] : '';
    document.getElementById('csrActivityIsActive').checked = activity.isActive;

    // Populate file uploads
    populateCSRActivityFileUploads(activity);

    // Populate multilingual fields
    if (activity.titles) {
        Object.keys(activity.titles).forEach(lang => {
            const titleInput = document.getElementById(`csrActivityTitle_${lang}`);
            if (titleInput) {
                titleInput.value = activity.titles[lang] || '';
            }
        });
    }

    if (activity.descriptions) {
        Object.keys(activity.descriptions).forEach(lang => {
            const descInput = document.getElementById(`csrActivityDescription_${lang}`);
            if (descInput) {
                descInput.value = activity.descriptions[lang] || '';
            }
        });
    }

    if (activity.summaries) {
        Object.keys(activity.summaries).forEach(lang => {
            const summaryInput = document.getElementById(`csrActivitySummary_${lang}`);
            if (summaryInput) {
                summaryInput.value = activity.summaries[lang] || '';
            }
        });
    }
}

// CSR language tab switching - 使用通用系统
window.switchCSRLanguageTab = function(lang) {
    return window.switchLanguageTab(lang, '#csrActivityModal', {
        buttonClass: 'csr-lang-tab-button',
        contentClass: 'csr-lang-content',
        contentIdPrefix: 'csr-lang-'
    });
};

// CSR Activity CRUD Operations
window.editCSRActivity = function(id) {
    openCSRActivityModal(id);
};

window.deleteCSRActivity = async function(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete);
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(buildMultilingualUrl('csr-activity', 'CompanyInfo') + '/' + id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteCSRActivitySuccess || 'CSR Activity deleted successfully');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteCSRActivityError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete CSR Activity error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteCSRActivityError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
};

// Investor Relations Management Functions
function initializeInvestorRelationsFunctions() {
    console.log('Investor Relations functions initialized');

    // Financial Report Form Submission
    const financialReportForm = document.getElementById('financialReportForm');
    if (financialReportForm) {
        financialReportForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const reportData = {
                id: document.getElementById('financialReportId').value || '',
                type: parseInt(formData.get('type')),
                period: parseInt(formData.get('period')),
                year: parseInt(formData.get('year')),
                quarter: formData.get('quarter') ? parseInt(formData.get('quarter')) : null,
                publishDate: formData.get('publishDate'),
                reportFileUrl: formData.get('reportFileUrl') || '',
                revenue: formData.get('revenue') ? parseFloat(formData.get('revenue')) : null,
                netIncome: formData.get('netIncome') ? parseFloat(formData.get('netIncome')) : null,
                totalAssets: formData.get('totalAssets') ? parseFloat(formData.get('totalAssets')) : null,
                currency: formData.get('currency') || 'JPY',
                isPublished: document.getElementById('financialReportIsPublished').checked,
                titles: {},
                summaries: {},
                analyses: {}
            };

            // Collect multilingual fields
            document.querySelectorAll('[name^="reportTitle_"]').forEach(input => {
                const langMatch = input.name.match(/reportTitle_(.+)/);
                if (langMatch) {
                    reportData.titles[langMatch[1]] = input.value;
                }
            });

            document.querySelectorAll('[name^="reportSummary_"]').forEach(textarea => {
                const langMatch = textarea.name.match(/reportSummary_(.+)/);
                if (langMatch) {
                    reportData.summaries[langMatch[1]] = textarea.value;
                }
            });

            document.querySelectorAll('[name^="reportAnalysis_"]').forEach(textarea => {
                const langMatch = textarea.name.match(/reportAnalysis_(.+)/);
                if (langMatch) {
                    reportData.analyses[langMatch[1]] = textarea.value;
                }
            });

            // Validate required fields
            if (!reportData.titles.zh || !reportData.titles.zh.trim()) {
                Dialog.error(window.Resources?.Admin?.FinancialReportTitleRequired || 'Financial report title is required');
                document.getElementById('financialReportTitle_zh').focus();
                return;
            }

            showLoading();

            try {
                const response = await fetch(buildMultilingualUrl('financial-report', 'CompanyInfo'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(reportData)
                });

                const result = await response.json();

                if (result.success) {
                    Dialog.notify(result.message || window.Resources?.Admin?.SaveFinancialReportSuccess || 'Financial report saved successfully');
                    closeFinancialReportModal();
                    window.location.reload();
                } else {
                    Dialog.error(result.message || window.Resources?.Admin?.SaveFinancialReportError || 'Failed to save financial report');
                }
            } catch (error) {
                console.error('Save financial report error:', error);
                Dialog.error(window.Resources?.Admin?.SaveFinancialReportError || 'Failed to save financial report');
            } finally {
                hideLoading();
            }
        });
    }

    // Shareholder Meeting Form Submission
    const shareholderMeetingForm = document.getElementById('shareholderMeetingForm');
    if (shareholderMeetingForm) {
        shareholderMeetingForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const meetingData = {
                id: document.getElementById('shareholderMeetingId').value || '',
                meetingDate: formData.get('meetingDate'),
                status: parseInt(formData.get('status')),
                titles: {},
                descriptions: {},
                agendas: {},
                documents: []
            };

            // Collect multilingual fields
            document.querySelectorAll('[name^="meetingTitle_"]').forEach(input => {
                const langMatch = input.name.match(/meetingTitle_(.+)/);
                if (langMatch) {
                    meetingData.titles[langMatch[1]] = input.value;
                }
            });

            document.querySelectorAll('[name^="meetingDescription_"]').forEach(textarea => {
                const langMatch = textarea.name.match(/meetingDescription_(.+)/);
                if (langMatch) {
                    meetingData.descriptions[langMatch[1]] = textarea.value;
                }
            });

            document.querySelectorAll('[name^="meetingAgenda_"]').forEach(textarea => {
                const langMatch = textarea.name.match(/meetingAgenda_(.+)/);
                if (langMatch) {
                    meetingData.agendas[langMatch[1]] = textarea.value;
                }
            });

            // Validate required fields
            if (!meetingData.titles.zh || !meetingData.titles.zh.trim()) {
                Dialog.error(window.Resources?.Admin?.ShareholderMeetingTitleRequired || 'Shareholder meeting title is required');
                document.getElementById('shareholderMeetingTitle_zh').focus();
                return;
            }

            showLoading();

            try {
                const response = await fetch(buildMultilingualUrl('shareholder-meeting', 'CompanyInfo'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(meetingData)
                });

                const result = await response.json();

                if (result.success) {
                    Dialog.notify(result.message || window.Resources?.Admin?.SaveShareholderMeetingSuccess || 'Shareholder meeting saved successfully');
                    closeShareholderMeetingModal();
                    window.location.reload();
                } else {
                    Dialog.error(result.message || window.Resources?.Admin?.SaveShareholderMeetingError || 'Failed to save shareholder meeting');
                }
            } catch (error) {
                console.error('Save shareholder meeting error:', error);
                Dialog.error(window.Resources?.Admin?.SaveShareholderMeetingError || 'Failed to save shareholder meeting');
            } finally {
                hideLoading();
            }
        });
    }
}

// Financial Report Modal Functions
window.openFinancialReportModal = function(id = null) {
    const modal = document.getElementById('financialReportModal');
    const form = document.getElementById('financialReportForm');
    const title = document.getElementById('financialReportModalTitle');

    // Check if modal elements exist
    if (!modal || !form || !title) {
        console.warn('Financial Report modal elements not found. Modal may not be loaded yet.');
        return;
    }

    // Reset form
    form.reset();
    const idInput = document.getElementById('financialReportId');
    if (idInput) {
        idInput.value = '';
    }

    // Clear file lists
    const reportFileList = document.getElementById('financialReportFileList');
    if (reportFileList) reportFileList.innerHTML = '';

    // Initialize file uploads
    initializeFinancialReportFileUploads();

    // Initialize first language tab
    const firstLangBtn = document.querySelector('.financial-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#financialReportModal', {
            buttonClass: 'financial-lang-tab-button',
            contentClass: 'financial-lang-content',
            contentIdPrefix: 'financial-lang-'
        });
    }

    if (id) {
        title.textContent = window.Resources?.Admin?.EditFinancialReport || 'Edit Financial Report';
        loadFinancialReportForEdit(id);
    } else {
        title.textContent = window.Resources?.Admin?.AddFinancialReport || 'Add Financial Report';
    }

    modal.classList.remove('hidden');
};

window.closeFinancialReportModal = function() {
    const modal = document.getElementById('financialReportModal');
    if (modal) {
        modal.classList.add('hidden');
    }
};

async function loadFinancialReportForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl('financial-report', 'CompanyInfo') + '/' + id, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateFinancialReportForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.FinancialReportNotFound || 'Financial report not found');
        }
    } catch (error) {
        console.error('Load financial report error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load financial report');
    } finally {
        hideLoading();
    }
}

function populateFinancialReportForm(report) {
    document.getElementById('financialReportId').value = report.id;
    document.getElementById('financialReportType').value = report.type.toString();
    document.getElementById('financialReportYear').value = report.year;
    document.getElementById('financialReportQuarter').value = report.quarter || '';
    document.getElementById('financialReportPublishDate').value = report.publishDate.split('T')[0];
    document.getElementById('financialReportFileUrl').value = report.reportFileUrl || '';
    
    // Handle file upload display
    if (report.reportFileUrl) {
        // Clear existing file list
        const fileList = document.getElementById('financialReportFileList');
        if (fileList) {
            fileList.innerHTML = '';
        }
        
        // Render initial file if exists
        if (window.AdminUpload && window.AdminUpload.renderInitialFiles) {
            const fileConfig = {
                types: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
                multiple: false,
                folder: 'financial-reports',
                maxSize: '50MB',
                preview: true
            };
            window.AdminUpload.renderInitialFiles(fileList, fileConfig, [report.reportFileUrl]);
        }
    }
    document.getElementById('financialReportRevenue').value = report.revenue || '';
    document.getElementById('financialReportNetIncome').value = report.netIncome || '';
    document.getElementById('financialReportTotalAssets').value = report.totalAssets || '';
    document.getElementById('financialReportCurrency').value = report.currency || 'JPY';
    document.getElementById('financialReportIsPublished').checked = report.isPublished;

    // Populate multilingual fields
    if (report.titles) {
        Object.keys(report.titles).forEach(lang => {
            const titleInput = document.getElementById(`financialReportTitle_${lang}`);
            if (titleInput) {
                titleInput.value = report.titles[lang] || '';
            }
        });
    }

    if (report.summaries) {
        Object.keys(report.summaries).forEach(lang => {
            const summaryInput = document.getElementById(`financialReportSummary_${lang}`);
            if (summaryInput) {
                summaryInput.value = report.summaries[lang] || '';
            }
        });
    }

    if (report.analyses) {
        Object.keys(report.analyses).forEach(lang => {
            const analysisInput = document.getElementById(`financialReportAnalysis_${lang}`);
            if (analysisInput) {
                analysisInput.value = report.analyses[lang] || '';
            }
        });
    }
}

// Financial language tab switching - 使用通用系统
window.switchFinancialLanguageTab = function(lang) {
    return window.switchLanguageTab(lang, '#financialReportModal', {
        buttonClass: 'financial-lang-tab-button',
        contentClass: 'financial-lang-content',
        contentIdPrefix: 'financial-lang-'
    });
};

// Financial Report CRUD Operations
window.editFinancialReport = function(id) {
    openFinancialReportModal(id);
};

window.deleteFinancialReport = async function(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete);
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(buildMultilingualUrl('financial-report', 'CompanyInfo') + '/' + id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteFinancialReportSuccess || 'Financial report deleted successfully');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteFinancialReportError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete financial report error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteFinancialReportError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }
};

// Shareholder Meeting Modal Functions
window.openShareholderMeetingModal = function(id = null) {
    const modal = document.getElementById('shareholderMeetingModal');
    const form = document.getElementById('shareholderMeetingForm');
    const title = document.getElementById('shareholderMeetingModalTitle');

    // Check if modal elements exist
    if (!modal || !form || !title) {
        console.warn('Shareholder Meeting modal elements not found. Modal may not be loaded yet.');
        return;
    }

    // Reset form
    form.reset();
    const idInput = document.getElementById('shareholderMeetingId');
    if (idInput) {
        idInput.value = '';
    }

    // Initialize first language tab
    const firstLangBtn = document.querySelector('.meeting-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#shareholderMeetingModal', {
            buttonClass: 'meeting-lang-tab-button',
            contentClass: 'meeting-lang-content',
            contentIdPrefix: 'meeting-lang-'
        });
    }

    if (id) {
        title.textContent = window.Resources?.Admin?.EditShareholderMeeting || 'Edit Shareholder Meeting';
        loadShareholderMeetingForEdit(id);
    } else {
        title.textContent = window.Resources?.Admin?.AddShareholderMeeting || 'Add Shareholder Meeting';
    }

    modal.classList.remove('hidden');
};

window.closeShareholderMeetingModal = function() {
    const modal = document.getElementById('shareholderMeetingModal');
    if (modal) {
        modal.classList.add('hidden');
    }
};

async function loadShareholderMeetingForEdit(id) {
    try {
        showLoading(window.Resources?.Admin?.LoadingData || 'Loading data...');

        const response = await fetch(buildMultilingualUrl('shareholder-meeting', 'CompanyInfo') + '/' + id, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateShareholderMeetingForm(result.data);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.ShareholderMeetingNotFound || 'Shareholder meeting not found');
        }
    } catch (error) {
        console.error('Load shareholder meeting error:', error);
        Dialog.error(window.Resources?.Admin?.LoadRecordError || 'Failed to load shareholder meeting');
    } finally {
        hideLoading();
    }
}

function populateShareholderMeetingForm(meeting) {
    document.getElementById('shareholderMeetingId').value = meeting.id;
    document.getElementById('shareholderMeetingDate').value = meeting.meetingDate.split('T')[0];
    document.getElementById('shareholderMeetingStatus').value = meeting.status.toString();

    // Populate multilingual fields
    if (meeting.titles) {
        Object.keys(meeting.titles).forEach(lang => {
            const titleInput = document.getElementById(`shareholderMeetingTitle_${lang}`);
            if (titleInput) {
                titleInput.value = meeting.titles[lang] || '';
            }
        });
    }

    if (meeting.descriptions) {
        Object.keys(meeting.descriptions).forEach(lang => {
            const descInput = document.getElementById(`shareholderMeetingDescription_${lang}`);
            if (descInput) {
                descInput.value = meeting.descriptions[lang] || '';
            }
        });
    }

    if (meeting.agendas) {
        Object.keys(meeting.agendas).forEach(lang => {
            const agendaInput = document.getElementById(`shareholderMeetingAgenda_${lang}`);
            if (agendaInput) {
                agendaInput.value = meeting.agendas[lang] || '';
            }
        });
    }
}

// Meeting language tab switching - 使用通用系统
window.switchMeetingLanguageTab = function(lang) {
    return window.switchLanguageTab(lang, '#shareholderMeetingModal', {
        buttonClass: 'meeting-lang-tab-button',
        contentClass: 'meeting-lang-content',
        contentIdPrefix: 'meeting-lang-'
    });
};

// Shareholder Meeting CRUD Operations
window.editShareholderMeeting = function(id) {
    openShareholderMeetingModal(id);
};

window.deleteShareholderMeeting = async function(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete);
    if (confirmed) {
        showLoading();
        try {
            const response = await fetch(buildMultilingualUrl('shareholder-meeting', 'CompanyInfo') + '/' + id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteShareholderMeetingSuccess || 'Shareholder meeting deleted successfully');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteShareholderMeetingError || 'Delete failed');
            }
        } catch (error) {
            console.error('Delete shareholder meeting error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteShareholderMeetingError || 'Delete failed');
        } finally {
            hideLoading();
        }
    }

    // Initialize Financial Report File Upload
    initializeFinancialReportFileUploads();
};

// Initialize Financial Report File Upload
function initializeFinancialReportFileUploads() {
    // Initialize report file upload
    const reportFileInput = document.getElementById('financialReportFile');
    const reportFileList = document.getElementById('financialReportFileList');
    const reportFileUrlInput = document.getElementById('financialReportFileUrl');
    
    if (reportFileInput && reportFileList && reportFileUrlInput) {
        window.AdminUpload.bind(reportFileInput, {
            fileListEl: reportFileList,
            fileConfig: {
                types: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
                multiple: false,
                folder: 'financial-reports',
                maxSize: '50MB',
                preview: true
            },
            getResource: function(key, fallback) {
                return window.Resources?.Shared?.[key] || fallback || key;
            },
            hiddenInputSelector: '#financialReportFileUrl'
        });
    }
}

// Executive and Organization Management Functions
function initializeExecutiveOrganizationFunctions() {
    console.log('Executive and Organization functions initialized');

    // This function is called when the executive-organization tab is loaded
    // The executive and organization forms are already initialized in the global scope
    // when the tab content is loaded via AJAX
}