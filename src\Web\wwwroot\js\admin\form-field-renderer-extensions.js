/**
 * FormFieldRenderer扩展方法
 * 包含缺少的字段类型实现 - 只包含新功能，避免重复定义
 */

// 确保FormFieldRenderer类已经加载
if (typeof FormFieldRenderer === 'undefined') {
    console.error('FormFieldRenderer class not found. Make sure form-field-renderer.js is loaded first.');
} else {
    console.log('FormField<PERSON>enderer found, applying extensions...');
}

// 保存原始方法的引用
const originalCreateFieldComponent = FormFieldRenderer.prototype.createFieldComponent;

// 扩展FormFieldRenderer类
Object.assign(FormFieldRenderer.prototype, {

    /**
     * 覆盖原有的createFieldComponent方法以支持新的字段类型
     */
    createFieldComponent(field) {
        console.log(`createFieldComponent called for ${field.name} (${field.type}):`, field);

        try {
            // 首先检查是否是我们支持的扩展字段类型
            switch (field.type) {
                case 'group':
                    console.log(`Creating group input for ${field.name}, layout:`, field.display?.layout);
                    console.log(`Group fields:`, field.fields?.map(f => ({ name: f.name, layout: f.display?.layout, width: f.display?.width })));
                    return this.createGroupInput(field);
                case 'repeater':
                case 'array':
                    console.log(`Creating repeater input for ${field.name}`, field);
                    return this.createRepeaterInput(field);
                case 'multilingual-richtext':
                    console.log(`Creating multilingual richtext for ${field.name}`);
                    return this.createMultilingualRichtext(field);
                case 'icon':
                    console.log(`Creating icon picker for ${field.name}`);
                    return this.createIconPicker(field);
                case 'url':
                    console.log(`Creating URL input for ${field.name}`);
                    return this.createUrlInput(field);
                default:
                    // 对于其他字段类型，调用原始方法
                    if (originalCreateFieldComponent) {
                        return originalCreateFieldComponent.call(this, field);
                    } else {
                        throw new Error(`Unsupported field type: ${field.type}`);
                    }
            }
        } catch (error) {
            console.error(`Error creating field component for ${field.name} (${field.type}):`, error);

            // 创建错误展示组件
            const errorWrapper = document.createElement('div');
            errorWrapper.className = 'p-4 border border-red-300 rounded-lg bg-red-50 dark:bg-red-900/20';
            errorWrapper.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-400">
                            Field Render Error
                        </h3>
                        <p class="text-xs text-red-700 dark:text-red-300 mt-1">
                            Field: <code>${field.name}</code> (Type: <code>${field.type}</code>)<br>
                            Error: ${error.message}
                        </p>
                    </div>
                </div>
            `;
            return errorWrapper;
        }
    },

    /**
     * 创建分组字段
     */
    createGroupInput(field) {
        const wrapper = document.createElement('div');
        wrapper.className = 'space-y-4 p-4 border border-gray-200 rounded-lg dark:border-gray-700';

        // 创建分组标题
        const groupTitle = document.createElement('h4');
        groupTitle.className = 'text-md font-medium text-gray-900 dark:text-white mb-4';
        groupTitle.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            groupTitle.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(groupTitle);

        // 创建子字段容器
        const fieldsContainer = document.createElement('div');

        // 检查是否有inline布局的字段
        const hasInlineFields = field.fields && field.fields.some(subField =>
            subField.display?.layout === 'inline'
        );

        console.log(`Group ${field.name} - hasInlineFields:`, hasInlineFields);
        console.log(`Group ${field.name} - field.fields:`, field.fields?.map(f => ({
            name: f.name,
            layout: f.display?.layout,
            width: f.display?.width,
            displayObj: f.display
        })));

        // 根据是否有inline字段来设置网格布局
        if (hasInlineFields) {
            // 如果所有字段都有相同的宽度且是简单的等分布局，使用简化的网格
            const allFieldsHaveSameWidth = field.fields && field.fields.every(subField =>
                subField.display?.width !== 'col-span-12' && subField.display?.layout === 'inline'
            );

            var cols = field.fields.length > 4 ?4: field.fields.length;

            if (allFieldsHaveSameWidth) {
                fieldsContainer.className = `grid grid-cols-${cols} gap-4  p-5`; // N等分布局
            } else {
                fieldsContainer.className = 'grid grid-cols-12 gap-4  p-5'; // 使用12列网格支持col-span-*
            }
        } else {
            fieldsContainer.className = 'grid grid-cols-1 md:grid-cols-2 gap-4 p-5'; // 默认布局
        }

        console.log(`Group field container layout for ${field.name}: ${fieldsContainer.className}`);

        // 渲染子字段
        if (field.fields && Array.isArray(field.fields)) {
            field.fields.forEach(subField => {
                // 更新子字段名称以包含父字段名称
                const updatedSubField = {
                    ...subField,
                    name: `${field.name}.${subField.name}`
                };

                try {
                    const subFieldElement = this.createFieldComponent(updatedSubField);
                    const subFieldContainer = document.createElement('div');

                    // 检查是否使用了简化的3列布局
                    const isSimpleThreeColLayout = fieldsContainer.className.includes('grid-cols-3');

                    let assignedWidth;
                    if (isSimpleThreeColLayout) {
                        // 在3列布局中，每个字段自然占用一列，不需要col-span
                        assignedWidth = '';
                    } else {
                        // 在12列布局中，使用配置的宽度
                        assignedWidth = updatedSubField.display?.width || 'col-span-12';
                    }

                    subFieldContainer.className = assignedWidth.trim();
                    console.log(`Group ${field.name} - subfield ${updatedSubField.name} assigned width: ${assignedWidth} (3-col layout: ${isSimpleThreeColLayout})`);
                    subFieldContainer.appendChild(subFieldElement);
                    fieldsContainer.appendChild(subFieldContainer);
                } catch (error) {
                    console.error(`Error rendering sub-field ${updatedSubField.name}:`, error);
                }
            });
        }

        wrapper.appendChild(fieldsContainer);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-2 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        return wrapper;
    },

    /**
     * 创建重复字段（数组字段）
     */
    createRepeaterInput(field) {
        const wrapper = document.createElement('div');
        wrapper.className = 'space-y-4';
        wrapper.dataset.repeaterField = field.name;

        console.log(`Creating repeater input for ${field.name}:`, field);

        // 创建字段标题
        const fieldTitle = document.createElement('div');
        fieldTitle.className = 'flex justify-between items-center';

        const title = document.createElement('h4');
        title.className = 'text-md font-medium text-gray-900 dark:text-white';
        title.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            title.innerHTML += ' <span class="text-red-500">*</span>';
        }

        fieldTitle.appendChild(title);

        // 添加按钮 - 重要：需要捕获完整的field对象
        const addButton = document.createElement('button');
        addButton.type = 'button';
        addButton.className = 'inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800';
        addButton.innerHTML = '<i class="fas fa-plus mr-2"></i>' + (this.getResource('AddItem', 'Add Item', 'form'));

        // 确保传递完整的field对象，包括template
        const completeField = {
            ...field,
            // 确保所有可能的字段配置都传递过去
            template: field.template || (field.itemFields ? { fields: field.itemFields } : null),
            fields: field.fields || field.itemFields,
            itemFields: field.itemFields,
            templateFields: field.templateFields,
            minItems: field.minItems || field.arrayConfig?.minItems || 0,
            maxItems: field.maxItems || field.arrayConfig?.maxItems || 99
        };

        addButton.onclick = () => {
            console.log(`Add button clicked for ${field.name}, passing field:`, completeField);
            this.addRepeaterItem(completeField, itemsContainer);
        };

        fieldTitle.appendChild(addButton);
        wrapper.appendChild(fieldTitle);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mb-4 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        // 创建项目容器
        const itemsContainer = document.createElement('div');
        itemsContainer.className = 'space-y-3';
        itemsContainer.dataset.repeaterItems = field.name;
        wrapper.appendChild(itemsContainer);

        // 添加最小项目数量
        const minItems = completeField.minItems || 0;
        for (let i = 0; i < minItems; i++) {
            console.log(`Adding initial item ${i} for ${field.name}`);
            this.addRepeaterItem(completeField, itemsContainer);
        }

        return wrapper;
    },

    /**
     * 添加重复项目
     */
    addRepeaterItem(field, container) {
        const maxItems = field.maxItems || 99;
        const currentItems = container.children.length;

        console.log(`Adding repeater item for field: ${field.name}, current items: ${currentItems}`);
        console.log(`Full field object:`, field);
        console.log(`Field template:`, field.template);
        console.log(`Template fields:`, field.template?.fields);

        if (currentItems >= maxItems) {
            if (typeof Dialog !== 'undefined') {
                Dialog.warning(this.getResource('MaxItemsReached', 'Maximum items reached', 'form'));
            }
            return;
        }

        const itemWrapper = document.createElement('div');
        itemWrapper.className = 'relative p-4 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800 mb-3';

        // 创建删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.type = 'button';
        deleteButton.className = 'absolute top-2 right-2 p-1 text-red-600 hover:text-red-700 dark:text-red-400';
        deleteButton.innerHTML = '<i class="fas fa-times"></i>';
        deleteButton.onclick = () => {
            const minItems = field.minItems || 0;
            if (container.children.length > minItems) {
                itemWrapper.remove();
                this.updateRepeaterIndices(field, container);
            } else if (typeof Dialog !== 'undefined') {
                Dialog.warning(this.getResource('MinItemsRequired', 'Minimum items required', 'form'));
            }
        };
        itemWrapper.appendChild(deleteButton);

        // 创建折叠/展开按钮
        const collapseButton = document.createElement('button');
        collapseButton.type = 'button';
        collapseButton.className = 'absolute top-2 right-8 p-1 text-gray-600 hover:text-gray-700 dark:text-gray-400';
        collapseButton.innerHTML = '<i class="fas fa-chevron-up"></i>';
        itemWrapper.appendChild(collapseButton);

        // 创建项目标题
        const itemTitle = document.createElement('div');
        itemTitle.className = 'mb-3  pb-2 border-b border-gray-300 dark:border-gray-600 cursor-pointer';
        const titleText = document.createElement('h6');
        titleText.className = 'text-sm font-medium text-gray-700 dark:text-gray-300';
        titleText.textContent = `${this.resolveResourceKey(field.label) || field.name} #${currentItems + 1}`;
        itemTitle.appendChild(titleText);
        itemWrapper.appendChild(itemTitle);

        // 创建字段容器
        const fieldsContainer = document.createElement('div');

        console.log(`Template fields for ${field.name}:`, field.template?.fields);

        // 检查多种可能的模板字段位置
        let templateFields = null;
        if (field.template && field.template.fields) {
            templateFields = field.template.fields;
            console.log(`Found template.fields:`, templateFields);
            // 打印每个字段的详细信息
            templateFields.forEach((tf, idx) => {
                console.log(`Template field ${idx}:`, {
                    name: tf.name,
                    type: tf.type,
                    layout: tf.layout,
                    allProps: Object.keys(tf)
                });
            });
        } else if (field.itemFields) {
            templateFields = field.itemFields;
            console.log(`Found itemFields:`, templateFields);
        } else if (field.fields) {
            templateFields = field.fields;
            console.log(`Found direct fields:`, templateFields);
        } else if (field.templateFields) {
            templateFields = field.templateFields;
            console.log(`Found templateFields:`, templateFields);
        } else if (field.arrayConfig && field.arrayConfig.itemFields) {
            templateFields = field.arrayConfig.itemFields;
            console.log(`Found arrayConfig.itemFields:`, templateFields);
        }

        // 检查是否有inline布局的字段
        const hasInlineFields = templateFields && templateFields.some(field =>
            field.display?.layout === 'inline'
        );

        // 根据是否有inline字段来设置网格布局
        if (hasInlineFields) {
            // 如果所有字段都有相同的宽度且是简单的等分布局，使用简化的网格
            const allFieldsHaveSameWidth = templateFields && templateFields.every(field =>
                field.display?.width === 'col-span-4' && field.display?.layout === 'inline'
            );

            if (allFieldsHaveSameWidth && templateFields.length === 3) {
                fieldsContainer.className = 'grid grid-cols-3 gap-4'; // 3等分布局
            } else {
                fieldsContainer.className = 'grid grid-cols-12 gap-4'; // 使用12列网格支持col-span-*
            }
        } else {
            fieldsContainer.className = 'grid grid-cols-1 gap-4'; // 默认单列布局
        }

        console.log(`Field container layout for ${field.name}: ${fieldsContainer.className}`);
        console.log(`Has inline fields:`, hasInlineFields);

        // 渲染模板字段
        if (templateFields && Array.isArray(templateFields) && templateFields.length > 0) {
            const itemIndex = currentItems;
            console.log(`Rendering ${templateFields.length} template fields for item ${itemIndex}`);

            templateFields.forEach((templateField, fieldIndex) => {
                console.log(`Rendering template field: ${templateField.name} (${templateField.type}) for item ${itemIndex}, layout: ${templateField.display?.layout}`);

                // 更新字段名称以包含数组索引
                const updatedField = {
                    ...templateField,
                    // 确保关键属性在扩展后设置，避免被覆盖
                    name: `${field.name}[${itemIndex}].${templateField.name}`,
                    parentRepeater: field.name,
                    itemIndex: itemIndex,
                    fieldIndex: fieldIndex
                };

                // 确保layout属性正确传递
                if (templateField.layout) {
                    updatedField.layout = templateField.layout;
                }

                console.log(`Updated field for ${updatedField.name}:`, {
                    type: updatedField.type,
                    layout: updatedField.layout,
                    originalLayout: templateField.layout
                });

                try {
                    // 获取字段元素
                    let fieldElement;

                    // 对于基本字段类型，我们需要直接调用 createFieldComponent 以避免双重容器包装
                    if (['text', 'email', 'number', 'textarea', 'select', 'checkbox', 'radio', 'file', 'image', 'video', 'richtext', 'color', 'date', 'datetime'].includes(templateField.type)) {
                        // 直接创建字段组件，不包装容器
                        fieldElement = originalCreateFieldComponent.call(this, updatedField);
                    } else {
                        // 对于自定义字段类型，使用 renderNestedField
                        fieldElement = this.renderNestedField(updatedField);
                    }

                    // 创建字段容器并应用正确的CSS类
                    const fieldContainer = document.createElement('div');

                    // 检查是否使用了简化的3列布局
                    const isSimpleThreeColLayout = fieldsContainer.className.includes('grid-cols-3');

                    let width;
                    if (isSimpleThreeColLayout) {
                        // 在3列布局中，每个字段自然占用一列，不需要col-span
                        width = '';
                    } else {
                        // 在12列布局中，使用配置的宽度
                        width = updatedField.display?.width || 'col-span-12';
                    }

                    fieldContainer.className = `${width} mb-4`.trim();
                    fieldContainer.dataset.fieldName = updatedField.name;
                    fieldContainer.dataset.fieldType = updatedField.type;

                    fieldContainer.appendChild(fieldElement);
                    fieldsContainer.appendChild(fieldContainer);

                    console.log(`Successfully rendered field: ${updatedField.name} with width: ${width}`);
                } catch (error) {
                    console.error(`Error rendering repeater field ${updatedField.name}:`, error);
                    // 创建错误提示
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'text-red-500 text-sm p-2 border border-red-300 rounded col-span-full';
                    errorDiv.innerHTML = `
                        <strong>Field Error:</strong> ${templateField.name} (${templateField.type})<br>
                        <small>${error.message}</small>
                    `;
                    fieldsContainer.appendChild(errorDiv);
                }
            });
        } else {
            console.warn(`No template fields found for repeater: ${field.name}`);
            console.log(`Field keys:`, Object.keys(field));
            console.log(`Template keys:`, field.template ? Object.keys(field.template) : 'No template');

            // 创建详细的调试信息
            const debugDiv = document.createElement('div');
            debugDiv.className = 'text-orange-600 text-sm p-3 border border-orange-300 rounded bg-orange-50';
            debugDiv.innerHTML = `
                <strong>Debug Info:</strong> No template fields configured for repeater: ${field.name}<br>
                <small>Field keys: ${Object.keys(field).join(', ')}</small><br>
                <small>Template: ${field.template ? JSON.stringify(field.template, null, 2) : 'undefined'}</small>
            `;
            fieldsContainer.appendChild(debugDiv);
        }

        // 添加折叠功能
        let isCollapsed = true;
        const toggleCollapse = () => {
            isCollapsed = !isCollapsed;
            if (isCollapsed) {
                fieldsContainer.style.display = 'none';
                collapseButton.innerHTML = '<i class="fas fa-chevron-down"></i>';
                itemWrapper.classList.add('collapsed');
            } else {
                fieldsContainer.style.display = '';
                collapseButton.innerHTML = '<i class="fas fa-chevron-up"></i>';
                itemWrapper.classList.remove('collapsed');
            }
        };

        // 绑定折叠按钮和标题点击事件
        collapseButton.onclick = toggleCollapse;
        itemTitle.onclick = toggleCollapse;

        itemWrapper.appendChild(fieldsContainer);
        container.appendChild(itemWrapper);

        console.log(`Added repeater item for ${field.name}, total items now: ${container.children.length}`);

        this.updateRepeaterIndices(field, container);
    },

    /**
     * 渲染嵌套字段（专门处理repeater内的字段）
     */
    renderNestedField(field) {
        console.log(`renderNestedField called for: ${field.name} (${field.type})`);

        try {
            let result;
            switch (field.type) {
                case 'group':
                    console.log(`Creating nested group for: ${field.name}`);
                    result = this.createNestedGroupInput(field);
                    break;
                case 'repeater':
                    console.log(`Creating nested repeater for: ${field.name}`);
                    result = this.createNestedRepeaterInput(field);
                    break;
                case 'multilingual-text':
                    console.log(`Creating multilingual input for: ${field.name}`);
                    result = this.createNestedMultilingualInput(field);
                    break;
                case 'multilingual-textarea':
                    console.log(`Creating multilingual textarea for: ${field.name}`);
                    result = this.createNestedMultilingualTextarea(field);
                    break;
                case 'multilingual-richtext':
                    console.log(`Creating multilingual richtext for: ${field.name}`);
                    result = this.createNestedMultilingualRichtext(field);
                    break;
                case 'icon':
                    console.log(`Creating nested icon picker for: ${field.name}`);
                    result = this.createIconPicker(field);
                    break;
                case 'url':
                    console.log(`Creating nested URL input for: ${field.name}`);
                    result = this.createUrlInput(field);
                    break;
                default:
                    // 对于其他字段类型，调用原始的字段创建方法
                    if (originalCreateFieldComponent) {
                        result = originalCreateFieldComponent.call(this, field);
                    } else {
                        throw new Error(`Unsupported nested field type: ${field.type}`);
                    }
                    break;
            }

            if (result && result instanceof HTMLElement) {
                console.log(`Successfully created element for: ${field.name}`, result);
            } else {
                console.warn(`Invalid result for field: ${field.name}`, result);
            }

            return result;
        } catch (error) {
            console.error(`Error in renderNestedField for ${field.name}:`, error);
            throw error;
        }
    },

    /**
     * 创建嵌套的分组字段
     */
    createNestedGroupInput(field) {
        const wrapper = document.createElement('div');
        wrapper.className = 'space-y-4 p-3 border border-gray-300 rounded-md dark:border-gray-600 bg-white dark:bg-gray-800';

        // 创建分组标题
        const groupTitle = document.createElement('h5');
        groupTitle.className = 'text-sm font-medium text-gray-800 dark:text-white mb-3';
        groupTitle.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            groupTitle.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(groupTitle);

        // 创建子字段容器
        const fieldsContainer = document.createElement('div');

        // 检查是否有inline布局的字段
        const hasInlineFields = field.fields && field.fields.some(subField =>
            subField.display?.layout === 'inline'
        );

        // 根据是否有inline字段来设置网格布局
        if (hasInlineFields) {
            fieldsContainer.className = 'grid grid-cols-12 gap-3'; // 使用12列网格支持col-span-*
        } else {
            fieldsContainer.className = 'grid grid-cols-1 gap-3'; // 默认单列布局
        }

        console.log(`Group field container layout for ${field.name}: ${fieldsContainer.className}`);

        // 渲染子字段
        if (field.fields && Array.isArray(field.fields)) {
            field.fields.forEach(subField => {
                // 更新子字段名称
                const updatedSubField = {
                    ...subField,
                    name: `${field.name}.${subField.name}`
                };

                try {
                    const subFieldElement = this.renderNestedField(updatedSubField);
                    const subFieldContainer = document.createElement('div');
                    subFieldContainer.className = updatedSubField.display?.width || 'col-span-1';
                    subFieldContainer.appendChild(subFieldElement);
                    fieldsContainer.appendChild(subFieldContainer);
                } catch (error) {
                    console.error(`Error rendering nested sub-field ${updatedSubField.name}:`, error);
                }
            });
        }

        wrapper.appendChild(fieldsContainer);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-2 text-xs text-gray-500 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        return wrapper;
    },

    /**
     * 创建嵌套的重复字段
     */
    createNestedRepeaterInput(field) {
        const wrapper = document.createElement('div');
        wrapper.className = 'space-y-3';
        wrapper.dataset.repeaterField = field.name;

        // 创建字段标题
        const fieldTitle = document.createElement('div');
        fieldTitle.className = 'flex justify-between items-center';

        const title = document.createElement('h5');
        title.className = 'text-sm font-medium text-gray-800 dark:text-white';
        title.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            title.innerHTML += ' <span class="text-red-500">*</span>';
        }

        fieldTitle.appendChild(title);

        // 添加按钮（更小的样式适合嵌套）
        const addButton = document.createElement('button');
        addButton.type = 'button';
        addButton.className = 'inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-primary-500 rounded hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-300';
        addButton.innerHTML = '<i class="fas fa-plus mr-1"></i>' + (this.getResource('Add', 'Add', 'form'));
        addButton.onclick = () => this.addRepeaterItem(field, itemsContainer);

        fieldTitle.appendChild(addButton);
        wrapper.appendChild(fieldTitle);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mb-3 text-xs text-gray-500 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        // 创建项目容器
        const itemsContainer = document.createElement('div');
        itemsContainer.className = 'space-y-2';
        itemsContainer.dataset.repeaterItems = field.name;
        wrapper.appendChild(itemsContainer);

        // 添加最小项目数量
        const minItems = field.minItems || 0;
        for (let i = 0; i < minItems; i++) {
            this.addRepeaterItem(field, itemsContainer);
        }

        return wrapper;
    },

    /**
     * 创建嵌套的多语言输入字段
     */
    createNestedMultilingualInput(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-xs font-medium text-gray-700 dark:text-gray-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(label);

        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: 'Chinese', emoji: '🇨🇳' },
            { code: 'en', name: 'English', emoji: '🇺🇸' },
            { code: 'ja', name: 'Japanese', emoji: '🇯🇵' }
        ];

        // 检查布局模式
        console.log('Nested field layout for', field.name, ':', field.display?.layout);
        if (field.display?.layout === 'inline') {
            // 内联模式：一行显示多个语言
            console.log('Using inline layout for nested field', field.name);
            const grid = document.createElement('div');
            const gridCols = languages.length === 3 ? 'grid-cols-3' :
                            languages.length === 2 ? 'grid-cols-2' : 'grid-cols-1';
            grid.className = `grid ${gridCols} gap-2`;

            languages.forEach(lang => {
                const langWrapper = document.createElement('div');

                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-xs font-medium text-gray-600 dark:text-gray-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const input = document.createElement('input');
                input.type = 'text';
                input.name = `${field.name}[${lang.code}]`;
                input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded focus:ring-primary-500 focus:border-primary-500 block w-full p-2 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                input.placeholder = `${lang.name}...`;

                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    input.value = field.defaultValue[lang.code];
                }

                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(input);
                grid.appendChild(langWrapper);
            });

            wrapper.appendChild(grid);
        } else {
            // 使用紧凑的堆叠布局（适合嵌套环境）
            console.log('Using stacked layout for nested field', field.name);
            languages.forEach(lang => {
                const langWrapper = document.createElement('div');
                langWrapper.className = 'mb-2';

                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-xs text-gray-600 dark:text-gray-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const input = document.createElement('input');
                input.type = 'text';
                input.name = `${field.name}[${lang.code}]`;
                input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded focus:ring-primary-500 focus:border-primary-500 block w-full p-2 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                input.placeholder = `${lang.name}...`;

                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    input.value = field.defaultValue[lang.code];
                }

                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(input);
                wrapper.appendChild(langWrapper);
            });
        }

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-xs text-gray-500 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        return wrapper;
    },

    /**
     * 创建嵌套的多语言富文本编辑器
     */
    createNestedMultilingualRichtext(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-xs font-medium text-gray-700 dark:text-gray-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(label);

        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: 'Chinese', emoji: '🇨🇳' },
            { code: 'en', name: 'English', emoji: '🇺🇸' },
            { code: 'ja', name: 'Japanese', emoji: '🇯🇵' }
        ];

        // 创建Tab标签页结构
        const tabContainer = document.createElement('div');
        tabContainer.className = 'mb-2';

        // 创建Tab头部
        const tabNav = document.createElement('div');
        tabNav.className = 'flex border-b border-gray-200 dark:border-gray-600';

        // 创建Tab内容容器
        const tabContent = document.createElement('div');
        tabContent.className = 'mt-2';

        languages.forEach((lang, index) => {
            // 创建Tab标签
            const tabButton = document.createElement('button');
            tabButton.type = 'button';
            tabButton.className = `px-3 py-2 text-xs font-medium border-b-2 ${
                index === 0
                    ? 'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500'
                    : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`;
            tabButton.textContent = `${lang.emoji} ${lang.name}`;
            tabButton.dataset.tab = lang.code;
            tabButton.dataset.fieldName = field.name;

            // Tab点击事件
            tabButton.onclick = () => {
                // 更新tab状态
                tabNav.querySelectorAll('button').forEach(btn => {
                    btn.className = btn.className.replace(
                        'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500',
                        'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    );
                });
                tabButton.className = tabButton.className.replace(
                    'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
                    'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500'
                );

                // 显示对应的内容区域
                tabContent.querySelectorAll('.lang-content').forEach(content => {
                    content.style.display = 'none';
                });
                const activeContent = tabContent.querySelector(`[data-lang="${lang.code}"]`);
                if (activeContent) {
                    activeContent.style.display = 'block';
                }
            };

            tabNav.appendChild(tabButton);

            // 创建对应的内容区域
            const langContent = document.createElement('div');
            langContent.className = 'lang-content';
            langContent.dataset.lang = lang.code;
            langContent.style.display = index === 0 ? 'block' : 'none';

            // 创建富文本编辑器
            const textarea = document.createElement('textarea');
            textarea.name = `${field.name}[${lang.code}]`;
            // Fix invalid selector characters for TinyMCE
            const safeId = `${field.name}_${lang.code}`.replace(/[\[\]\.]/g, '_');
            textarea.id = safeId;
            textarea.className = 'block p-2 w-full text-xs text-gray-900 bg-gray-50 rounded border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
            textarea.rows = field.editorConfig?.height ? Math.round(field.editorConfig.height / 20) : 8;
            textarea.placeholder = `${lang.name}...`;

            // 设置默认值（只在创建时设置）
            if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                textarea.value = field.defaultValue[lang.code];
            }

            langContent.appendChild(textarea);
            tabContent.appendChild(langContent);

            // 初始化TinyMCE编辑器（延迟初始化以确保DOM就绪）
            setTimeout(() => {
                this.initializeTinyMCE(textarea, field.editorConfig, lang.code);
            }, 100);
        });

        tabContainer.appendChild(tabNav);
        tabContainer.appendChild(tabContent);
        wrapper.appendChild(tabContainer);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-xs text-gray-500 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        return wrapper;
    },

    /**
     * 初始化TinyMCE编辑器
     */
    initializeTinyMCE(textarea, config, langCode) {
        // 优先使用通用的 AdminTinyMCE 初始化
        if (typeof window !== 'undefined' && window.AdminTinyMCE && typeof window.AdminTinyMCE.init === 'function') {
            window.AdminTinyMCE.init(textarea, config, langCode);
            return;
        }

        // 最小化的后备方案（保持兼容性）
        if (typeof tinymce === 'undefined') {
            console.warn('TinyMCE is not loaded');
            return;
        }
        const editorId = textarea.id || (textarea.name ? textarea.name.replace(/[\[\]\.\s]/g, '_') : 'editor_' + Date.now());
        if (!textarea.id) {
            textarea.id = editorId;
        }
        try {
            tinymce.init(Object.assign({ selector: `#${editorId}` }, config || {}));
        } catch (error) {
            console.error(`Failed to initialize TinyMCE for ${editorId}:`, error);
        }
    },
    createNestedMultilingualTextarea(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-xs font-medium text-gray-700 dark:text-gray-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(label);

        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: 'Chinese', emoji: '🇨🇳' },
            { code: 'en', name: 'English', emoji: '🇺🇸' },
            { code: 'ja', name: 'Japanese', emoji: '🇯🇵' }
        ];


        // 检查布局模式
        console.log('Nested field layout for', field.name, ':', field.display?.layout);
        if (field.display?.layout === 'inline') {
            // 内联模式：一行显示多个语言
            console.log('Using inline layout for nested field', field.name);
            const grid = document.createElement('div');
            const gridCols = languages.length === 3 ? 'grid-cols-3' :
                            languages.length === 2 ? 'grid-cols-2' : 'grid-cols-1';
            grid.className = `grid ${gridCols} gap-2`;

            languages.forEach(lang => {
                const langWrapper = document.createElement('div');

                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-xs font-medium text-gray-600 dark:text-gray-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const textarea = document.createElement('textarea');
                textarea.name = `${field.name}[${lang.code}]`;
                textarea.className = 'block p-2 w-full text-xs text-gray-900 bg-gray-50 rounded border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                textarea.rows = field.display?.rows || 2;
                textarea.placeholder = `${this.getResource('EnterText', 'Enter text', 'shared')}...`;
    
                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    textarea.value = field.defaultValue[lang.code];
                }
    
                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(textarea);
                grid.appendChild(langWrapper);
            });

            wrapper.appendChild(grid);
        } else {
            // 使用紧凑的堆叠布局（适合嵌套环境）
            console.log('Using stacked layout for nested field', field.name);
            languages.forEach(lang => {
                const langWrapper = document.createElement('div');
                langWrapper.className = 'mb-2';

                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-xs text-gray-600 dark:text-gray-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const textarea = document.createElement('textarea');
                textarea.name = `${field.name}[${lang.code}]`;
                textarea.className = 'block p-2 w-full text-xs text-gray-900 bg-gray-50 rounded border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                textarea.rows = field.display?.rows || 2;
                textarea.placeholder = `${this.getResource('EnterText', 'Enter text', 'shared')}...`;

                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    textarea.value = field.defaultValue[lang.code];
                }

                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(textarea);
                wrapper.appendChild(langWrapper);
            });
        }

       
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-xs text-gray-500 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        return wrapper;
    },

    /**
     * 创建图标选择器字段
     */
    createIconPicker(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-xs font-medium text-gray-700 dark:text-gray-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(label);

        // 创建输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.name = field.name;
        input.id = `field_${field.name}`;
        input.className = 'block p-2 w-full text-xs text-gray-900 bg-gray-50 rounded border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
        input.placeholder = 'fas fa-star, fas fa-heart, etc.';

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            input.value = field.defaultValue;
        }

        // 设置验证属性
        if (field.validation?.required) {
            input.required = true;
        }
        if (field.validation?.maxLength) {
            input.maxLength = field.validation.maxLength;
        }

        wrapper.appendChild(input);

        // 添加图标预览
        const iconPreview = document.createElement('div');
        iconPreview.className = 'mt-2 p-2 text-center border border-gray-200 rounded dark:border-gray-600';
        iconPreview.innerHTML = '<i class="fas fa-question-circle text-gray-400"></i> <span class="text-xs text-gray-500">图标预览</span>';

        // 实时预览图标
        input.addEventListener('input', () => {
            const iconClass = input.value.trim();
            if (iconClass) {
                iconPreview.innerHTML = `<i class="${iconClass} text-primary-600"></i> <span class="text-xs text-gray-500">${iconClass}</span>`;
            } else {
                iconPreview.innerHTML = '<i class="fas fa-question-circle text-gray-400"></i> <span class="text-xs text-gray-500">图标预览</span>';
            }
        });

        wrapper.appendChild(iconPreview);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-xs text-gray-500 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        } else {
            // 添加默认帮助文本
            const defaultHelp = document.createElement('p');
            defaultHelp.className = 'mt-1 text-xs text-gray-500 dark:text-gray-400';
            defaultHelp.textContent = '输入FontAwesome图标类名，如：fas fa-star';
            wrapper.appendChild(defaultHelp);
        }

        return wrapper;
    },

    /**
     * 创建URL输入字段
     */
    createUrlInput(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-xs font-medium text-gray-700 dark:text-gray-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(label);

        // 创建输入框
        const input = document.createElement('input');
        input.type = 'url';
        input.name = field.name;
        input.id = `field_${field.name}`;
        input.className = 'block p-2 w-full text-xs text-gray-900 bg-gray-50 rounded border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
        input.placeholder = 'https://example.com';

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            input.value = field.defaultValue;
        }

        // 设置验证属性
        if (field.validation?.required) {
            input.required = true;
        }
        if (field.validation?.maxLength) {
            input.maxLength = field.validation.maxLength;
        }

        wrapper.appendChild(input);

        // 添加链接预览
        const linkPreview = document.createElement('div');
        linkPreview.className = 'mt-2 text-xs text-gray-500 dark:text-gray-400';
        linkPreview.style.display = 'none';

        // 实时验证和预览URL
        input.addEventListener('input', () => {
            const url = input.value.trim();
            if (url) {
                try {
                    new URL(url);
                    input.setCustomValidity('');
                    linkPreview.innerHTML = `<i class="fas fa-external-link-alt mr-1"></i> <a href="${url}" target="_blank" class="text-primary-600 hover:underline">${url}</a>`;
                    linkPreview.style.display = 'block';
                } catch (e) {
                    input.setCustomValidity('请输入有效的URL地址');
                    linkPreview.style.display = 'none';
                }
            } else {
                input.setCustomValidity('');
                linkPreview.style.display = 'none';
            }
        });

        wrapper.appendChild(linkPreview);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-xs text-gray-500 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        return wrapper;
    },

    /**
     * 更新重复项目的索引
     */
    updateRepeaterIndices(field, container) {
        Array.from(container.children).forEach((itemWrapper, index) => {
            // 更新所有输入字段的name属性
            const inputs = itemWrapper.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.name && input.name.includes(field.name)) {
                    // 替换旧的索引为新的索引
                    input.name = input.name.replace(/\[\d+\]/, `[${index}]`);
                    input.id = input.id ? input.id.replace(/\[\d+\]/, `[${index}]`) : input.id;
                }
            });

            // 更新关联的label
            const labels = itemWrapper.querySelectorAll('label');
            labels.forEach(label => {
                if (label.getAttribute('for')) {
                    label.setAttribute('for', label.getAttribute('for').replace(/\[\d+\]/, `[${index}]`));
                }
            });
        });
    },

    /**
     * 获取多语言富文本字段值
     */
    getMultilingualRichtextValue(container, fieldName) {
        console.log(`Getting multilingual richtext value for ${fieldName}`);
        const values = {};
        const textareas = container.querySelectorAll(`textarea[name^="${fieldName}["]`);

        console.log(`Found ${textareas.length} textareas for ${fieldName}`);

        textareas.forEach(textarea => {
            const match = textarea.name.match(/\[(\w+)\]$/);
            if (match) {
                const lang = match[1];
                console.log(`Processing language: ${lang} for textarea: ${textarea.id}`);

                // 检查是否有TinyMCE编辑器实例
                if (typeof tinymce !== 'undefined') {
                    const editor = tinymce.get(textarea.id);
                    if (editor) {
                        values[lang] = editor.getContent();
                        console.log(`Got TinyMCE content for ${lang}:`, values[lang]);
                    } else {
                        values[lang] = textarea.value;
                        console.log(`Got textarea value for ${lang}:`, values[lang]);
                    }
                } else {
                    values[lang] = textarea.value;
                    console.log(`No TinyMCE, got textarea value for ${lang}:`, values[lang]);
                }
            }
        });

        console.log(`Final multilingual values for ${fieldName}:`, values);
        return Object.keys(values).length > 0 ? values : '';
    },

    /**
     * 设置多语言富文本字段值
     */
    setMultilingualRichtextValue(container, fieldName, values) {
        if (typeof values === 'object' && values !== null) {
            Object.entries(values).forEach(([lang, value]) => {
                const textarea = container.querySelector(`textarea[name="${fieldName}[${lang}]"]`);
                if (textarea) {
                    // 检查是否有TinyMCE编辑器实例
                    if (typeof tinymce !== 'undefined') {
                        const editor = tinymce.get(textarea.id);
                        if (editor) {
                            editor.setContent(value || '');
                        } else {
                            textarea.value = value || '';
                        }
                    } else {
                        textarea.value = value || '';
                    }
                }
            });
        }
    },

    /**
     * 获取数组字段值
     */
    getArrayValue(container, fieldName, field = null, parentFieldName = null, parentItemIndex = null) {
        console.log(`Getting array value for ${fieldName}`, field, `parent: ${parentFieldName}[${parentItemIndex}]`);
        const items = [];

        // 构建正确的选择器
        let searchSelector;
        if (parentFieldName && parentItemIndex !== null) {
            // 这是嵌套的repeater，需要查找完整路径
            searchSelector = `[data-repeater-items="${parentFieldName}[${parentItemIndex}].${fieldName}"]`;
        } else {
            // 这是顶级repeater
            searchSelector = `[data-repeater-items="${fieldName}"]`;
        }

        console.log(`Searching for repeater container with selector: ${searchSelector}`);
        const itemsContainer = container.querySelector(searchSelector);

        if (!itemsContainer) {
            console.warn(`Array items container not found for ${fieldName} with selector ${searchSelector}`);
            console.log(`Available containers in field:`, container.querySelectorAll('[data-repeater-items]'));
            // 尝试查找所有可能的匹配
            const allContainers = container.querySelectorAll('[data-repeater-items]');
            allContainers.forEach(cont => {
                console.log(`Available container: ${cont.getAttribute('data-repeater-items')}`);
            });
            return items;
        }

        console.log(`Found items container with ${itemsContainer.children.length} children`);

        // 获取字段定义信息
        let templateFields = null;
        if (field) {
            if (field.template && field.template.fields) {
                templateFields = field.template.fields;
            } else if (field.itemFields) {
                templateFields = field.itemFields;
            } else if (field.fields) {
                templateFields = field.fields;
            }
        }
        console.log(`Template fields for ${fieldName}:`, templateFields);

        const itemWrappers = itemsContainer.children;
        for (let i = 0; i < itemWrappers.length; i++) {
            const itemWrapper = itemWrappers[i];
            const itemData = {};

            console.log(`Processing item ${i}`);

            // 确定当前的父字段名
            const currentParentFieldName = parentFieldName ? `${parentFieldName}[${parentItemIndex}].${fieldName}` : fieldName;

            // 如果有字段定义，根据字段定义来收集数据
            if (templateFields && Array.isArray(templateFields)) {
                templateFields.forEach(templateField => {
                    const fieldName = templateField.name;
                    const fieldType = templateField.type;

                    console.log(`Processing template field: ${fieldName} (${fieldType})`);

                    switch (fieldType) {
                        case 'repeater':
                        case 'array':
                            // 处理嵌套的重复字段
                            // fieldName 是template field的原名（如 SubItems）
                            // 需要传递正确的parentFieldName和itemIndex
                            itemData[fieldName] = this.getArrayValue(itemWrapper, fieldName, templateField, currentParentFieldName, i);
                            console.log(`Nested repeater result for ${fieldName}:`, itemData[fieldName]);
                            break;
                        case 'group':
                            // 处理组字段
                            itemData[fieldName] = this.getGroupValue(itemWrapper, fieldName, templateField);
                            console.log(`Group result for ${fieldName}:`, itemData[fieldName]);
                            break;
                        case 'multilingual-text':
                        case 'multilingual-textarea':
                        case 'multilingual-richtext':
                            // 处理多语言字段
                            itemData[fieldName] = this.getMultilingualFieldValue(itemWrapper, fieldName, fieldType, i);
                            console.log(`Multilingual field result for ${fieldName}:`, itemData[fieldName]);
                            break;
                        default:
                            // 处理普通字段
                            itemData[fieldName] = this.getBasicFieldValue(itemWrapper, fieldName, fieldType, i);
                            console.log(`Basic field result for ${fieldName}:`, itemData[fieldName]);
                            break;
                    }
                });
            } else {
                // 如果没有字段定义，使用原来的方法
                const inputs = itemWrapper.querySelectorAll('input, select, textarea');
                console.log(`Found ${inputs.length} inputs in item ${i} (no template fields)`);

                inputs.forEach(input => {
                    const name = input.name;
                    console.log(`Processing input: ${name}`);

                    if (name && name.includes(`[${i}].`)) {
                        const fieldMatch = name.match(/\[(\d+)\]\.(.+)$/);
                        if (fieldMatch) {
                            const fieldName = fieldMatch[2];

                            // 处理多语言字段
                            if (fieldName.includes('[') && fieldName.includes(']')) {
                                const langMatch = fieldName.match(/^(.+)\[(\w+)\]$/);
                                if (langMatch) {
                                    const baseFieldName = langMatch[1];
                                    const lang = langMatch[2];

                                    if (!itemData[baseFieldName]) {
                                        itemData[baseFieldName] = {};
                                    }

                                    // 对于富文本字段，检查TinyMCE编辑器
                                    if (input.tagName.toLowerCase() === 'textarea' && typeof tinymce !== 'undefined' && tinymce.get(input.id)) {
                                        if (typeof tinymce !== 'undefined') {
                                            const editor = tinymce.get(input.id);
                                            itemData[baseFieldName][lang] = editor ? editor.getContent() : input.value;
                                        } else {
                                            itemData[baseFieldName][lang] = input.value;
                                        }
                                    } else {
                                        itemData[baseFieldName][lang] = input.value;
                                    }

                                    console.log(`Set multilingual field ${baseFieldName}[${lang}] = ${itemData[baseFieldName][lang]}`);
                                }
                            } else {
                                // 普通字段
                                if (input.type === 'checkbox') {
                                    itemData[fieldName] = input.checked;
                                } else if (input.type === 'file') {
                                    // 对于文件字段，从文件列表容器中获取已上传的文件路径
                                    // 尝试多种方式找到文件列表容器
                                    let fileListContainer = null;

                                    // 方法1: 基于完整字段名查找
                                    const fullFieldName = name.replace(/[\[\]]/g, '_');
                                    fileListContainer = itemWrapper.querySelector(`#filelist_${fullFieldName}`);

                                    // 方法2: 基于input名称查找
                                    if (!fileListContainer) {
                                        const inputName = input.name.replace(/[\[\]]/g, '_');
                                        fileListContainer = itemWrapper.querySelector(`#filelist_${inputName}`);
                                    }

                                    // 方法3: 查找最近的文件列表容器
                                    if (!fileListContainer) {
                                        fileListContainer = input.closest('.file-upload-wrapper')?.querySelector('[id^="filelist_"]');
                                    }

                                    // 方法4: 在item容器中查找包含字段名的文件列表
                                    if (!fileListContainer) {
                                        const allFileLists = itemWrapper.querySelectorAll('[id^="filelist_"]');
                                        for (const list of allFileLists) {
                                            if (list.id.includes(fieldName.replace(/[\[\]]/g, '_'))) {
                                                fileListContainer = list;
                                                break;
                                            }
                                        }
                                    }

                                    if (fileListContainer) {
                                        const fileItems = fileListContainer.querySelectorAll('[data-file-path]');
                                        if (fileItems.length > 0) {
                                            // 如果有上传的文件，使用第一个文件的路径
                                            itemData[fieldName] = fileItems[0].dataset.filePath;
                                            console.log(`Set file field ${fieldName} = ${itemData[fieldName]} (from uploaded file, container: ${fileListContainer.id})`);
                                        } else {
                                            itemData[fieldName] = '';
                                            console.log(`Set file field ${fieldName} = empty (no uploaded files in container: ${fileListContainer.id})`);
                                        }
                                    } else {
                                        itemData[fieldName] = '';
                                        console.log(`Set file field ${fieldName} = empty (no file list container found for input: ${name})`);
                                    }
                                } else {
                                    itemData[fieldName] = input.value;
                                    console.log(`Set field ${fieldName} = ${itemData[fieldName]}`);
                                }
                            }
                        }
                    }
                });
            }

            console.log(`Item ${i} data:`, itemData);
            items.push(itemData);
        }

        console.log(`Final array data for ${fieldName}:`, items);
        return items;
    },

    /**
     * 获取多语言字段值
     */
    getMultilingualFieldValue(container, fieldName, fieldType, itemIndex) {
        const result = {};
        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: 'Chinese', emoji: '🇨🇳' },
            { code: 'en', name: 'English', emoji: '🇺🇸' },
            { code: 'ja', name: 'Japanese', emoji: '🇯🇵' }
        ];

        languages.forEach((lang, index) => {
            let value = '';
            const inputSelector = `[name*="[${itemIndex}].${fieldName}[${lang.code}]"]`;
            const input = container.querySelector(inputSelector);

            if (input) {
                // 检查是否是富文本编辑器
                if (fieldType === 'multilingual-richtext' && input.tagName.toLowerCase() === 'textarea' && typeof tinymce !== 'undefined') {
                    const editor = tinymce.get(input.id);
                    value = editor ? editor.getContent() : input.value;
                } else {
                    value = input.value || '';
                }
            }

            result[lang.code] = value;
        });

        return result;
    },

    /**
     * 获取基本字段值
     */
    getBasicFieldValue(container, fieldName, fieldType, itemIndex) {
        const inputSelector = `[name*="[${itemIndex}].${fieldName}"]`;
        const input = container.querySelector(inputSelector);

        if (!input) {
            console.warn(`Input not found for field: ${fieldName} in item ${itemIndex}`);
            return fieldType === 'checkbox' ? false : '';
        }

        switch (fieldType) {
            case 'checkbox':
                return input.checked;
            case 'file':
            case 'image':
            case 'video':
                // 处理文件字段
                return this.getFileFieldValue(container, input, fieldName);
            default:
                return input.value || '';
        }
    },

    /**
     * 获取文件字段值
     */
    getFileFieldValue(container, input, fieldName) {
        // 尝试多种方式找到文件列表容器
        let fileListContainer = null;
        const inputName = input.name.replace(/[\[\]]/g, '_');

        // 方法1: 基于input名称查找
        fileListContainer = container.querySelector(`#filelist_${inputName}`);

        // 方法2: 查找最近的文件列表容器
        if (!fileListContainer) {
            fileListContainer = input.closest('.file-upload-wrapper')?.querySelector('[id^="filelist_"]');
        }

        // 方法3: 在容器中查找包含字段名的文件列表
        if (!fileListContainer) {
            const allFileLists = container.querySelectorAll('[id^="filelist_"]');
            for (const list of allFileLists) {
                if (list.id.includes(fieldName.replace(/[\[\]]/g, '_'))) {
                    fileListContainer = list;
                    break;
                }
            }
        }

        if (fileListContainer) {
            const fileItems = fileListContainer.querySelectorAll('[data-file-path]');
            if (fileItems.length > 0) {
                return fileItems[0].dataset.filePath;
            }
        }

        return '';
    },

    /**
     * 设置数组字段值
     */
    setArrayValue(container, fieldName, values, field, parentFieldName = null, parentItemIndex = null) {
        if (!Array.isArray(values)) {
            console.warn(`Array value is not an array for ${fieldName}:`, values);
            return;
        }

        // 构建正确的选择器 - 与getArrayValue一致
        let searchSelector;
        if (parentFieldName && parentItemIndex !== null) {
            // 这是嵌套的repeater，需要查找完整路径
            searchSelector = `[data-repeater-items="${parentFieldName}[${parentItemIndex}].${fieldName}"]`;
        } else {
            // 这是顶级repeater
            searchSelector = `[data-repeater-items="${fieldName}"]`;
        }

        console.log(`Setting array value for ${fieldName} with selector: ${searchSelector}`, values);
        const itemsContainer = container.querySelector(searchSelector);
        if (!itemsContainer) {
            console.warn(`Array items container not found for ${fieldName} with selector ${searchSelector}`);
            // 显示可用的容器用于调试
            const allContainers = container.querySelectorAll('[data-repeater-items]');
            allContainers.forEach(cont => {
                console.log(`Available container: ${cont.getAttribute('data-repeater-items')}`);
            });
            return;
        }

        // 清空现有项目
        itemsContainer.innerHTML = '';

        // 创建新项目
        values.forEach((itemData, index) => {
            this.addRepeaterItem(field, itemsContainer);

            // 设置项目数据
            const itemWrapper = itemsContainer.children[index];
            if (itemWrapper) {
                // 获取字段定义信息
                let templateFields = null;
                if (field) {
                    if (field.template && field.template.fields) {
                        templateFields = field.template.fields;
                    } else if (field.itemFields) {
                        templateFields = field.itemFields;
                    } else if (field.fields) {
                        templateFields = field.fields;
                    }
                }

                // 确定当前的父字段名
                const currentParentFieldName = parentFieldName ? `${parentFieldName}[${parentItemIndex}].${fieldName}` : fieldName;

                Object.entries(itemData).forEach(([subFieldName, subFieldValue]) => {
                    // 检查是否是嵌套的repeater字段
                    const fieldDef = templateFields?.find(f => f.name === subFieldName);

                    if (fieldDef && (fieldDef.type === 'repeater' || fieldDef.type === 'array') && Array.isArray(subFieldValue)) {
                        // 这是嵌套的repeater字段，递归调用setArrayValue
                        console.log(`Setting nested array value for ${subFieldName}:`, subFieldValue);
                        console.log(`Using field definition:`, fieldDef);
                        console.log(`Parent context: ${currentParentFieldName}[${index}]`);
                        this.setArrayValue(itemWrapper, subFieldName, subFieldValue, fieldDef, currentParentFieldName, index);
                    } else if (typeof subFieldValue === 'object' && subFieldValue !== null && !Array.isArray(subFieldValue)) {
                        // 多语言字段
                        Object.entries(subFieldValue).forEach(([lang, langValue]) => {
                            const input = itemWrapper.querySelector(`[name="${fieldName}[${index}].${subFieldName}[${lang}]"]`);
                            if (input) {
                                if (input.tagName.toLowerCase() === 'textarea' && typeof tinymce !== 'undefined' && tinymce.get(input.id)) {
                                    // 富文本字段，延迟设置值以确保编辑器已初始化
                                    setTimeout(() => {
                                        if (typeof tinymce !== 'undefined') {
                                            const editor = tinymce.get(input.id);
                                            if (editor) {
                                                editor.setContent(langValue || '');
                                            } else {
                                                input.value = langValue || '';
                                            }
                                        } else {
                                            input.value = langValue || '';
                                        }
                                    }, 500);
                                } else {
                                    input.value = langValue || '';
                                }
                            }
                        });
                    } else {
                        // 普通字段
                        const input = itemWrapper.querySelector(`[name="${fieldName}[${index}].${subFieldName}"]`);
                        if (input) {
                            if (input.type === 'checkbox') {
                                input.checked = !!subFieldValue;
                            } else if (input.type === 'file') {
                                // 文件字段需要重建文件显示列表
                                const fileUploadWrapper = input.closest('.file-upload-wrapper');
                                if (fileUploadWrapper && subFieldValue) {
                                    const fileListContainer = fileUploadWrapper.querySelector('[id^="filelist_"]');
                                    if (fileListContainer) {
                                        // 清空现有文件列表
                                        fileListContainer.innerHTML = '';

                                        // 处理单个文件路径字符串
                                        if (typeof subFieldValue === 'string' && subFieldValue.trim()) {
                                            const fileInfo = {
                                                filePath: subFieldValue,
                                                originalName: subFieldValue.split('/').pop() || 'uploaded-file',
                                                fileSize: 0,
                                                contentType: this.getFileIcon ? 'image/webp' : 'application/octet-stream'
                                            };

                                            const fieldConfig = this.findFieldConfig(field, subFieldName) || { fileConfig: {} };
                                            const fileItem = this.createFileItem(fileInfo, fieldConfig);
                                            fileListContainer.appendChild(fileItem);
                                            console.log(`Restored file display for ${subFieldName}:`, subFieldValue);
                                        }
                                    }
                                }
                            } else {
                                input.value = subFieldValue || '';
                            }
                        }
                    }
                });
            }
        });
    },

    /**
     * 在字段配置中查找特定子字段的配置
     */
    findFieldConfig(parentField, subFieldName) {
        if (!parentField || !parentField.template || !parentField.template.fields) {
            return null;
        }
        return parentField.template.fields.find(field => field.name === subFieldName);
    },

    /**
     * 获取分组字段值
     */
    getGroupValue(container, fieldName, field = null) {
        console.log(`Getting group value for ${fieldName}`);
        const groupData = {};

        // 查找所有属于该分组的输入字段
        const inputs = container.querySelectorAll(`[name^="${fieldName}."]`);
        console.log(`Found ${inputs.length} inputs for group ${fieldName}`);

        inputs.forEach(input => {
            const name = input.name;
            const fieldMatch = name.match(`^${fieldName}\.(.+)$`);

            if (fieldMatch) {
                const subFieldName = fieldMatch[1];

                // 处理多语言字段
                if (subFieldName.includes('[') && subFieldName.includes(']')) {
                    const langMatch = subFieldName.match(/^(.+)\[(\w+)\]$/);
                    if (langMatch) {
                        const baseFieldName = langMatch[1];
                        const lang = langMatch[2];

                        if (!groupData[baseFieldName]) {
                            groupData[baseFieldName] = {};
                        }

                        // 对于富文本字段，检查TinyMCE编辑器
                        if (input.tagName.toLowerCase() === 'textarea' && typeof tinymce !== 'undefined') {
                            const editor = tinymce.get(input.id);
                            groupData[baseFieldName][lang] = editor ? editor.getContent() : input.value;
                        } else {
                            groupData[baseFieldName][lang] = input.value;
                        }

                        console.log(`Set group multilingual field ${baseFieldName}[${lang}] = ${groupData[baseFieldName][lang]}`);
                    }
                } else {
                    // 普通字段
                    if (input.type === 'checkbox') {
                        groupData[subFieldName] = input.checked;
                    } else if (input.type === 'file') {
                        // 对于文件字段，从文件列表容器中获取已上传的文件路径
                        const fileListContainer = container.querySelector(`#filelist_${name.replace(/[\[\]\.]/g, '_')}`);
                        if (fileListContainer) {
                            const fileItems = fileListContainer.querySelectorAll('[data-file-path]');
                            if (fileItems.length > 0) {
                                groupData[subFieldName] = fileItems[0].dataset.filePath;
                            } else {
                                groupData[subFieldName] = '';
                            }
                        } else {
                            groupData[subFieldName] = '';
                        }
                    } else {
                        groupData[subFieldName] = input.value;
                    }

                    console.log(`Set group field ${subFieldName} = ${groupData[subFieldName]}`);
                }
            }
        });

        console.log(`Final group data for ${fieldName}:`, groupData);
        return groupData;
    },

    /**
     * 设置分组字段值
     */
    setGroupValue(container, fieldName, values, field) {
        console.log(`Setting group value for ${fieldName}:`, values);

        if (typeof values !== 'object' || values === null) {
            console.warn(`Group value is not an object for ${fieldName}:`, values);
            return;
        }

        Object.entries(values).forEach(([subFieldName, subFieldValue]) => {
            if (typeof subFieldValue === 'object' && subFieldValue !== null) {
                // 多语言字段
                Object.entries(subFieldValue).forEach(([lang, langValue]) => {
                    const input = container.querySelector(`[name="${fieldName}.${subFieldName}[${lang}]"]`);
                    if (input) {
                        if (input.tagName.toLowerCase() === 'textarea' && typeof tinymce !== 'undefined') {
                            // 富文本字段，延迟设置值以确保编辑器已初始化
                            setTimeout(() => {
                                const editor = tinymce.get(input.id);
                                if (editor) {
                                    editor.setContent(langValue || '');
                                } else {
                                    input.value = langValue || '';
                                }
                            }, 500);
                        } else {
                            input.value = langValue || '';
                        }
                        console.log(`Set group multilingual field ${subFieldName}[${lang}] = ${langValue}`);
                    }
                });
            } else {
                // 普通字段
                const input = container.querySelector(`[name="${fieldName}.${subFieldName}"]`);
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = !!subFieldValue;
                    } else if (input.type === 'file' && subFieldValue) {
                        // 文件字段需要重建文件显示列表
                        const fileUploadWrapper = input.closest('.file-upload-wrapper');
                        if (fileUploadWrapper) {
                            const fileListContainer = fileUploadWrapper.querySelector('[id^="filelist_"]');
                            if (fileListContainer) {
                                // 清空现有文件列表
                                fileListContainer.innerHTML = '';

                                // 处理单个文件路径字符串
                                if (typeof subFieldValue === 'string' && subFieldValue.trim()) {
                                    const fileInfo = {
                                        filePath: subFieldValue,
                                        originalName: subFieldValue.split('/').pop() || 'uploaded-file',
                                        fileSize: 0,
                                        contentType: this.getFileIcon ? 'image/webp' : 'application/octet-stream'
                                    };

                                    const fieldConfig = this.findGroupFieldConfig(field, subFieldName) || { fileConfig: {} };
                                    const fileItem = this.createFileItem(fileInfo, fieldConfig);
                                    fileListContainer.appendChild(fileItem);
                                    console.log(`Restored group file display for ${subFieldName}:`, subFieldValue);
                                }
                            }
                        }
                    } else {
                        input.value = subFieldValue || '';
                    }
                    console.log(`Set group field ${subFieldName} = ${subFieldValue}`);
                }
            }
        });
    },

    /**
     * 在分组字段配置中查找特定子字段的配置
     */
    findGroupFieldConfig(parentField, subFieldName) {
        if (!parentField || !parentField.fields) {
            return null;
        }
        return parentField.fields.find(field => field.name === subFieldName);
    },

    /**
     * 创建多语言富文本编辑器（顶级字段）
     */
    createMultilingualRichtext(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;

        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        wrapper.appendChild(label);

        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: 'Chinese', emoji: '🇨🇳' },
            { code: 'en', name: 'English', emoji: '🇺🇸' },
            { code: 'ja', name: 'Japanese', emoji: '🇯🇵' }
        ];

        // 创建Tab标签页结构
        const tabContainer = document.createElement('div');
        tabContainer.className = 'mb-4';

        // 创建Tab头部
        const tabNav = document.createElement('div');
        tabNav.className = 'flex border-b border-gray-200 dark:border-gray-600';

        // 创建Tab内容容器
        const tabContent = document.createElement('div');
        tabContent.className = 'mt-2';

        languages.forEach((lang, index) => {
            // 创建Tab标签
            const tabButton = document.createElement('button');
            tabButton.type = 'button';
            tabButton.className = `px-4 py-2 text-sm font-medium border-b-2 ${
                index === 0
                    ? 'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500'
                    : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`;
            tabButton.textContent = `${lang.emoji} ${lang.name}`;
            tabButton.dataset.tab = lang.code;
            tabButton.dataset.fieldName = field.name;

            // Tab点击事件
            tabButton.onclick = () => {
                // 更新tab状态
                tabNav.querySelectorAll('button').forEach(btn => {
                    btn.className = btn.className.replace(
                        'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500',
                        'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    );
                });
                tabButton.className = tabButton.className.replace(
                    'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
                    'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500'
                );

                // 显示对应的内容区域
                tabContent.querySelectorAll('.lang-content').forEach(content => {
                    content.style.display = 'none';
                });
                const activeContent = tabContent.querySelector(`[data-lang="${lang.code}"]`);
                if (activeContent) {
                    activeContent.style.display = 'block';
                }
            };

            tabNav.appendChild(tabButton);

            // 创建对应的内容区域
            const langContent = document.createElement('div');
            langContent.className = 'lang-content';
            langContent.dataset.lang = lang.code;
            langContent.style.display = index === 0 ? 'block' : 'none';

            // 创建富文本编辑器
            const textarea = document.createElement('textarea');
            textarea.name = `${field.name}[${lang.code}]`;
            // Fix invalid selector characters for TinyMCE
            const safeId = `${field.name}_${lang.code}`.replace(/[\[\]\.]/g, '_');
            textarea.id = safeId;
            textarea.className = 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
            textarea.rows = field.editorConfig?.height ? Math.round(field.editorConfig.height / 20) : 10;
            textarea.placeholder = `${this.getResource('EnterText', 'Enter text', 'shared')} (${lang.name})...`;

            // 设置默认值（只在创建时设置）
            if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                textarea.value = field.defaultValue[lang.code];
            }

            langContent.appendChild(textarea);
            tabContent.appendChild(langContent);

            // 初始化TinyMCE编辑器（延迟初始化以确保DOM就绪）
            setTimeout(() => {
                this.initializeTinyMCE(textarea, field.editorConfig, lang.code);
            }, 100);
        });

        tabContainer.appendChild(tabNav);
        tabContainer.appendChild(tabContent);
        wrapper.appendChild(tabContainer);

        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-2 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        return wrapper;
    }
});

// 验证扩展是否正确应用
if (typeof FormFieldRenderer !== 'undefined') {
    console.log('FormFieldRenderer extensions loaded successfully');
    console.log('Extensions applied to prototype');

    // 保存原始的数据处理方法并创建扩展版本
    const originalCollectFormData = FormFieldRenderer.prototype.collectFormData;
    const originalGetFormData = FormFieldRenderer.prototype.getFormData;
    const originalSetFormData = FormFieldRenderer.prototype.setFormData;

    // 创建扩展的getFormData方法（这是主要的数据收集方法）
    FormFieldRenderer.prototype.getFormData = function() {
        console.log('Extended getFormData called');

        // 先调用原始方法获取基础数据
        let data = {};
        if (originalGetFormData) {
            try {
                data = originalGetFormData.call(this);
            } catch (error) {
                console.warn('Error calling original getFormData:', error);
            }
        }

        // 然后处理扩展字段类型
        this.fieldInstances.forEach((instance, fieldName) => {
            const field = instance.field;
            const container = instance.container;

            try {
                console.log(`Processing field: ${fieldName} (${field.type})`);

                switch (field.type) {
                    case 'multilingual-richtext':
                        const multiResult = this.getMultilingualRichtextValue(container, fieldName);
                        console.log(`Multilingual richtext result for ${fieldName}:`, multiResult);
                        data[fieldName] = multiResult;
                        break;
                    case 'array':
                    case 'repeater':
                        const arrayResult = this.getArrayValue(container, fieldName, field, null, null);
                        console.log(`Array result for ${fieldName}:`, arrayResult);
                        data[fieldName] = arrayResult;
                        break;
                    case 'group':
                        const groupResult = this.getGroupValue(container, fieldName, field);
                        console.log(`Group result for ${fieldName}:`, groupResult);
                        data[fieldName] = groupResult;
                        break;
                    case 'icon':
                    case 'url':
                        // 处理图标和URL字段类型
                        const basicInput = container.querySelector(`[name="${fieldName}"]`);
                        if (basicInput) {
                            data[fieldName] = basicInput.value || '';
                            console.log(`${field.type} field result for ${fieldName}:`, data[fieldName]);
                        } else {
                            console.warn(`No input found for ${field.type} field: ${fieldName}`);
                            data[fieldName] = '';
                        }
                        break;
                    case 'file':
                    case 'image':
                    case 'video':
                        // 对于文件字段，检查是否需要返回单个路径还是文件数组
                        const fileValue = this.getFileValue ? this.getFileValue(container) : [];
                        if (Array.isArray(fileValue) && fileValue.length > 0) {
                            // 如果是单文件字段（如Image），返回第一个文件的路径
                            if (field.fileConfig && !field.fileConfig.multiple) {
                                data[fieldName] = fileValue[0].path;
                                console.log(`Single file field result for ${fieldName}:`, data[fieldName]);
                            } else {
                                // 如果是多文件字段，返回文件数组
                                data[fieldName] = fileValue;
                                console.log(`Multi-file field result for ${fieldName}:`, data[fieldName]);
                            }
                        } else {
                            data[fieldName] = field.fileConfig && field.fileConfig.multiple ? [] : '';
                            console.log(`Empty file field result for ${fieldName}:`, data[fieldName]);
                        }
                        break;
                    default:
                        // 对于其他字段类型，如果还没有被处理，尝试基本处理
                        if (!(fieldName in data)) {
                            const input = container.querySelector(`[name="${fieldName}"]`);
                            if (input) {
                                let value;
                                if (input.type === 'checkbox') {
                                    value = input.checked;
                                } else {
                                    value = input.value || '';
                                }
                                console.log(`Basic field result for ${fieldName}:`, value);
                                data[fieldName] = value;
                            } else {
                                console.warn(`No input found for field: ${fieldName}`);
                                data[fieldName] = '';
                            }
                        }
                        break;
                }
            } catch (error) {
                console.error(`Error collecting data for field ${fieldName}:`, error);
                data[fieldName] = '';
            }
        });

        console.log('Final collected data:', data);
        return data;
    };

    // 创建扩展的collectFormData方法（如果存在的话）
    if (originalCollectFormData) {
        FormFieldRenderer.prototype.collectFormData = function() {
            console.log('Extended collectFormData called - delegating to getFormData');
            return this.getFormData();
        };
    }

    // 创建扩展的setFormData方法
    FormFieldRenderer.prototype.setFormData = function(data) {
        console.log('Extended setFormData called with:', data);

        if (!data || typeof data !== 'object') {
            console.warn('Invalid data provided to setFormData:', data);
            return;
        }

        // 先调用原始的setFormData方法处理基础字段
        if (originalSetFormData) {
            try {
                originalSetFormData.call(this, data);
            } catch (error) {
                console.warn('Error calling original setFormData:', error);
            }
        }

        // 然后处理扩展字段类型
        Object.entries(data).forEach(([fieldName, value]) => {
            const instance = this.fieldInstances.get(fieldName);
            if (!instance) {
                console.warn(`Field instance not found for: ${fieldName}`);
                return;
            }

            const field = instance.field;
            const container = instance.container;

            try {
                switch (field.type) {
                    case 'multilingual-richtext':
                        this.setMultilingualRichtextValue(container, fieldName, value);
                        break;
                    case 'array':
                    case 'repeater':
                        this.setArrayValue(container, fieldName, value, field, null, null);
                        break;
                    case 'group':
                        this.setGroupValue(container, fieldName, value, field);
                        break;
                    case 'icon':
                    case 'url':
                        // 处理图标和URL字段类型
                        const basicInput = container.querySelector(`[name="${fieldName}"]`);
                        if (basicInput) {
                            basicInput.value = value || '';
                            console.log(`Set ${field.type} field ${fieldName} to:`, value);
                        } else {
                            console.warn(`No input found for ${field.type} field: ${fieldName}`);
                        }
                        break;
                    case 'file':
                    case 'image':
                    case 'video':
                        // 处理文件字段设置
                        if (typeof value === 'string' && value) {
                            // 如果是单个路径字符串，转换为文件对象格式
                            const fileData = [{
                                path: value,
                                name: value.split('/').pop() || 'uploaded-file'
                            }];
                            this.setFileValue(container, fileData, field);
                            console.log(`Set file field ${fieldName} to single file:`, value);
                        } else if (Array.isArray(value)) {
                            // 如果是文件数组
                            this.setFileValue(container, value, field);
                            console.log(`Set file field ${fieldName} to file array:`, value);
                        } else {
                            // 清空文件字段
                            this.setFileValue(container, [], field);
                            console.log(`Clear file field ${fieldName}`);
                        }
                        break;
                    // 其他字段类型已由原始方法处理
                }
            } catch (error) {
                console.error(`Error setting data for field ${fieldName}:`, error);
            }
        });
    };

    // 确认方法覆盖
    console.log('createFieldComponent method exists:', typeof FormFieldRenderer.prototype.createFieldComponent === 'function');
    console.log('createMultilingualRichtext method exists:', typeof FormFieldRenderer.prototype.createMultilingualRichtext === 'function');
    console.log('createRepeaterInput method exists:', typeof FormFieldRenderer.prototype.createRepeaterInput === 'function');

    // 测试一个实例
    const testRenderer = new FormFieldRenderer();
    console.log('createGroupInput method exists:', typeof testRenderer.createGroupInput === 'function');
    console.log('createRepeaterInput method exists:', typeof testRenderer.createRepeaterInput === 'function');
    console.log('createMultilingualRichtext method exists:', typeof testRenderer.createMultilingualRichtext === 'function');
    console.log('getArrayValue method exists:', typeof testRenderer.getArrayValue === 'function');
    console.log('Original createFieldComponent preserved:', typeof originalCreateFieldComponent === 'function');

    // 测试新字段类型处理
    console.log('Testing field type support...');
    try {
        const testMultilingualField = { type: 'multilingual-richtext', name: 'test_multilingual', label: 'Test' };
        const multiResult = testRenderer.createFieldComponent(testMultilingualField);
        console.log('Multilingual richtext field test successful:', multiResult instanceof HTMLElement);

        const testArrayField = { type: 'array', name: 'test_array', label: 'Test Array', template: { fields: [] } };
        const arrayResult = testRenderer.createFieldComponent(testArrayField);
        console.log('Array field test successful:', arrayResult instanceof HTMLElement);
    } catch (error) {
        console.error('Field type test failed:', error);
    }
} else {
    console.error('FormFieldRenderer not available after extensions');
}