/**
 * 表单字段渲染引擎
 * 负责根据配置动态生成表单UI
 */
class FormFieldRenderer {
    constructor(container, localization = {}) {
        this.container = container;
        this.l10n = localization;
        this.fieldGroups = new Map();
        this.fieldInstances = new Map();
    }

    /**
     * 获取本地化资源
     * @param {string} key - 资源键
     * @param {string} defaultValue - 默认值
     * @param {string} resourceType - 资源类型 (shared, form, admin, account)
     * @returns {string} - 本地化后的文本
     */
    getResource(key, defaultValue = key, resourceType = 'shared') {
        let globalResource;
        let localResource;

        switch (resourceType.toLowerCase()) {
            case 'form':
                globalResource = window.Resources?.Form;
                localResource = this.l10n.formResource || this.l10n.FormResource;
                break;
            case 'admin':
                globalResource = window.Resources?.Admin;
                localResource = this.l10n.adminResource || this.l10n.AdminResource;
                break;
            case 'account':
                globalResource = window.Resources?.Account;
                localResource = this.l10n.accountResource || this.l10n.AccountResource;
                break;
            case 'shared':
            default:
                globalResource = window.Resources?.Shared;
                localResource = this.l10n.sharedResource || this.l10n.SharedResource || this.l10n;
                break;
        }

        return globalResource?.[key] || localResource?.[key] || this.l10n[key] || defaultValue;
    }

    /**
     * 解析资源键
     * @param {string} text - 可能包含资源键的文本
     * @returns {string} - 解析后的文本
     */
    resolveResourceKey(text) {
        if (!text || typeof text !== 'string') return text;

        // 匹配 @ResourceName:KeyName 格式
        const resourceKeyPattern = /@(\w+):(\w+)/g;

        // 如果文本包含 @ 符号，使用标准资源键解析
        if (text.includes('@')) {
            return text.replace(resourceKeyPattern, (match, resourceName, keyName) => {
                // 根据资源名称获取对应的本地化对象
                let resourceObject;
                switch (resourceName) {
                    case 'FormResource':
                        // 优先使用全局Resources对象，再使用传入的l10n对象
                        resourceObject = window.Resources?.Form || this.l10n.formResource || this.l10n.FormResource || {};
                        break;
                    case 'SharedResource':
                        // 优先使用全局Resources对象，再使用传入的l10n对象
                        resourceObject = window.Resources?.Shared || this.l10n.sharedResource || this.l10n.SharedResource || this.l10n;
                        break;
                    case 'AdminResource':
                        // 支持AdminResource资源
                        resourceObject = window.Resources?.Admin || this.l10n.adminResource || this.l10n.AdminResource || {};
                        break;
                    case 'AccountResource':
                        // 支持AccountResource资源
                        resourceObject = window.Resources?.Account || this.l10n.accountResource || this.l10n.AccountResource || {};
                        break;
                    default:
                        resourceObject = this.l10n[resourceName] || {};
                        break;
                }

                // 返回找到的值，如果找不到则返回原始匹配的内容
                return resourceObject[keyName] || match;
            });
        } else {
            // 对于简单键名（如 "FormFields_CompanyName"），尝试从各个资源对象中查找
            // 优先顺序：Form -> Shared -> Admin -> Account
            const formResource = window.Resources?.Form || this.l10n.formResource || this.l10n.FormResource || {};
            const sharedResource = window.Resources?.Shared || this.l10n.sharedResource || this.l10n.SharedResource || this.l10n || {};
            const adminResource = window.Resources?.Admin || this.l10n.adminResource || this.l10n.AdminResource || {};
            const accountResource = window.Resources?.Account || this.l10n.accountResource || this.l10n.AccountResource || {};

            return formResource[text] || sharedResource[text] || adminResource[text] || accountResource[text] || text;
        }
    }
    
    /**
     * 渲染完整的表单
     */
    render(formFields) {
        this.container.innerHTML = '';
        
        // 按分组组织字段
        const groups = this.groupFields(formFields);
        
        // 渲染每个分组
        groups.forEach((fields, groupName) => {
            this.renderGroup(groupName, fields);
        });
    }
    
    /**
     * 按分组组织字段
     */
    groupFields(fields) {
        const groups = new Map();
        
        fields
            .sort((a, b) => (a.display?.order || 0) - (b.display?.order || 0))
            .forEach(field => {
                const groupName = field.display?.group || this.getResource('FormGroups_Other', 'Other', 'admin') || 'Other';
        const resolvedGroupName = this.resolveResourceKey(groupName);
                if (!groups.has(groupName)) {
                    groups.set(groupName, []);
                }
                groups.get(groupName).push(field);
            });
        
        return groups;
    }
    
    /**
     * 渲染分组
     */
    renderGroup(groupName, fields) {
        // 创建手风琴组件
        const accordion = document.createElement('div');
        accordion.className = 'border border-gray-200 rounded-lg mb-4 dark:border-gray-700';
        accordion.dataset.group = groupName;
        
        // 检查是否有字段默认折叠
        const isCollapsed = fields.some(f => f.display?.collapsed ?? true);
        
        // 创建手风琴头部
        const header = this.createGroupHeader(groupName, isCollapsed);
        accordion.appendChild(header);
        
        // 创建内容区域
        const content = this.createGroupContent(fields, isCollapsed);
        accordion.appendChild(content);
        
        this.container.appendChild(accordion);
        this.fieldGroups.set(groupName, { accordion, content, fields });
    }
    
    /**
     * 创建分组头部
     */
    createGroupHeader(groupName, isCollapsed) {
        const resolvedGroupName = this.resolveResourceKey(groupName);
        const header = document.createElement('h3');
        header.innerHTML = `
            <button type="button"
                    class="flex items-center justify-between w-full p-5 font-medium text-left text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-primary-200 dark:focus:ring-primary-800"
                    aria-expanded="${!isCollapsed}"
                    data-group-toggle="${groupName}">
                <span class="flex items-center">
                    <i class="fas fa-folder mr-3 text-primary-500"></i>
                    ${resolvedGroupName}
                </span>
                <svg class="w-3 h-3 transition-transform ${isCollapsed ? '' : 'rotate-180'} shrink-0" 
                     fill="none" viewBox="0 0 10 6">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" 
                          stroke-width="2" d="M9 5 5 1 1 5"/>
                </svg>
            </button>
        `;
        
        // 绑定切换事件
        const button = header.querySelector('button');
        button.addEventListener('click', () => this.toggleGroup(groupName));
        
        return header;
    }
    
    /**
     * 创建分组内容
     */
    createGroupContent(fields, isCollapsed) {
        const content = document.createElement('div');
        content.className = `transition-all duration-300 ${isCollapsed ? 'hidden' : ''}`;
        content.dataset.groupContent = fields[0]?.display?.group || '';

        // 创建网格容器
        const grid = document.createElement('div');

        // 检查是否有inline布局的字段
        const hasInlineFields = fields.some(field =>
            field.display?.layout === 'inline'
        );

        // 检查是否使用3列布局
        let isThreeColumnLayout = false;

        // 根据是否有inline字段来设置网格布局
        if (hasInlineFields) {
            // 检查是否所有inline字段都使用col-span-4，可以使用简化的3列布局
            const inlineFields = fields.filter(field => field.display?.layout === 'inline');
            const allFieldsHaveSameWidth = inlineFields.every(field =>
                field.display?.width !== 'col-span-12'
            );

            var cols = inlineFields.length > 4 ?4: inlineFields.length;

            if (allFieldsHaveSameWidth) {
                grid.className = `grid grid-cols-${cols} gap-4 p-5`; // N等分布局
                isThreeColumnLayout = true;
            } else {
                grid.className = 'grid grid-cols-12 gap-4 p-5'; // 使用12列网格支持col-span-*
            }
        } else {
            grid.className = 'grid grid-cols-1 gap-4 p-5'; // 默认单列布局
        }

        // 渲染字段
        fields.forEach(field => {
            const fieldElement = this.renderField(field, isThreeColumnLayout);
            grid.appendChild(fieldElement);
        });

        content.appendChild(grid);
        return content;
    }
    
    /**
     * 切换分组展开/折叠状态
     */
    toggleGroup(groupName) {
        const group = this.fieldGroups.get(groupName);
        if (!group) return;
        
        const content = group.content;
        const button = group.accordion.querySelector('[data-group-toggle]');
        const icon = button.querySelector('svg');
        
        const isHidden = content.classList.contains('hidden');
        
        if (isHidden) {
            // 展开
            content.classList.remove('hidden');
            button.setAttribute('aria-expanded', 'true');
            icon.classList.add('rotate-180');
        } else {
            // 折叠
            content.classList.add('hidden');
            button.setAttribute('aria-expanded', 'false');
            icon.classList.remove('rotate-180');
        }
    }
    
    /**
     * 渲染单个字段
     */
    renderField(field, isThreeColumnLayout = false) {
        const fieldContainer = document.createElement('div');

        // 在3列布局中，忽略col-span设置，让字段自然占用一列
        let fieldWidth;
        if (isThreeColumnLayout && field.display?.layout === 'inline') {
            fieldWidth = ''; // 在3列布局中，每个字段自然占用一列
        } else {
            fieldWidth = field.display?.width || 'col-span-12';
        }

        fieldContainer.className = `${fieldWidth} mb-4`.trim();
        fieldContainer.dataset.fieldName = field.name;
        fieldContainer.dataset.fieldType = field.type;

        try {
            // 根据字段类型创建对应的组件
            const fieldComponent = this.createFieldComponent(field);
            fieldContainer.appendChild(fieldComponent);

            // 存储字段实例
            this.fieldInstances.set(field.name, {
                field,
                container: fieldContainer,
                component: fieldComponent
            });
        } catch (error) {
            console.error(`Error rendering field ${field.name}:`, error);
            // 渲染错误提示
            fieldContainer.innerHTML = `
                <div class="text-error-500 text-sm p-3 border border-error-300 rounded-md bg-error-50 dark:bg-error-900/20">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    ${this.getResource('FieldRenderError', 'Field render failed', 'admin')}: ${field.name}
                </div>
            `;
        }

        return fieldContainer;
    }
    
    /**
     * 创建字段组件
     */
    createFieldComponent(field) {
        switch (field.type) {
            case 'text':
            case 'email':
            case 'number':
                return this.createTextInput(field);
            case 'textarea':
                return this.createTextareaInput(field);
            case 'multilingual-text':
                return this.createMultilingualInput(field);
            case 'multilingual-textarea':
                return this.createMultilingualTextarea(field);
            case 'select':
                return this.createSelectInput(field);
            case 'checkbox':
                return this.createCheckboxInput(field);
            case 'radio':
                return this.createRadioInput(field);
            case 'file':
            case 'image':
            case 'video':
                return this.createFileUpload(field);
            case 'richtext':
                return this.createRichTextEditor(field);
            case 'color':
                return this.createColorPicker(field);
            case 'date':
            case 'datetime':
                return this.createDatePicker(field);
            default:
                throw new Error(`Unsupported field type: ${field.type}`);
        }
    }
    
    /**
     * 创建文本输入字段
     */
    createTextInput(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        label.setAttribute('for', `field_${field.name}`);

        // 创建输入框
        const input = document.createElement('input');
        input.type = field.type === 'email' ? 'email' : field.type === 'number' ? 'number' : 'text';
        input.id = `field_${field.name}`;
        input.name = field.name;
        input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            input.value = field.defaultValue;
        }
        
        // 设置验证属性
        if (field.validation?.required) {
            input.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        if (field.validation?.maxLength) {
            input.maxLength = field.validation.maxLength;
        }
        if (field.validation?.minLength) {
            input.minLength = field.validation.minLength;
        }
        if (field.validation?.pattern) {
            input.pattern = field.validation.pattern;
        }
        
        wrapper.appendChild(label);
        wrapper.appendChild(input);
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建多行文本输入
     */
    createTextareaInput(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        label.setAttribute('for', `field_${field.name}`);

        // 创建文本域
        const textarea = document.createElement('textarea');
        textarea.id = `field_${field.name}`;
        textarea.name = field.name;
        textarea.className = 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
        textarea.rows = field.display?.rows || 4;

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            textarea.value = field.defaultValue;
        }
        
        if (field.validation?.required) {
            textarea.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        wrapper.appendChild(textarea);
        
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建多语言输入字段
     */
    createMultilingualInput(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        
        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        
        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: this.getResource('Chinese', 'Chinese', 'shared'), emoji: '🇨🇳' },
            { code: 'en', name: this.getResource('English', 'English', 'shared'), emoji: '🇺🇸' },
            { code: 'ja', name: this.getResource('Japanese', 'Japanese', 'shared'), emoji: '🇯🇵' }
        ];
        
        // 根据布局模式创建输入框
        console.log('Field layout for', field.name, ':', field.display?.layout);
        if (field.display?.layout === 'inline') {
            // 内联模式：一行显示多个语言
            console.log('Using inline layout for', field.name);
            const grid = document.createElement('div');
            // 使用固定的网格类名以确保 Tailwind 生成这些类
            const gridCols = languages.length === 3 ? 'grid-cols-3' : 
                            languages.length === 2 ? 'grid-cols-2' : 'grid-cols-1';
            grid.className = `grid ${gridCols} gap-2`;
            
            languages.forEach(lang => {
                const langWrapper = document.createElement('div');
                
                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-xs font-medium text-primary-700 dark:text-primary-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const input = document.createElement('input');
                input.type = 'text';
                input.name = `${field.name}[${lang.code}]`;
                input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                input.placeholder = `${lang.name}...`;

                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    input.value = field.defaultValue[lang.code];
                }
                
                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(input);
                grid.appendChild(langWrapper);
            });
            
            wrapper.appendChild(grid);
        } else {
            // 堆叠模式：每个语言占一行
            console.log('Using stacked layout for', field.name, 'layout value:', field.display?.layout);
            languages.forEach(lang => {
                const langWrapper = document.createElement('div');
                langWrapper.className = 'mb-3';
                
                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-sm font-medium text-primary-700 dark:text-primary-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const input = document.createElement('input');
                input.type = 'text';
                input.name = `${field.name}[${lang.code}]`;
                input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                input.placeholder = `${this.getResource('EnterText', 'Enter text', 'admin')}...`;

                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    input.value = field.defaultValue[lang.code];
                }
                
                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(input);
                wrapper.appendChild(langWrapper);
            });
        }
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建多语言文本域
     */
    createMultilingualTextarea(field) {
        const wrapper = document.createElement('div');

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        
        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        
        // 获取支持的语言
        const languages = window.SupportedLanguages || [
            { code: 'zh', name: this.getResource('Chinese', 'Chinese', 'shared'), emoji: '🇨🇳' },
            { code: 'en', name: this.getResource('English', 'English', 'shared'), emoji: '🇺🇸' },
            { code: 'ja', name: this.getResource('Japanese', 'Japanese', 'shared'), emoji: '🇯🇵' }
        ];
        
        // 根据布局模式创建文本域
        console.log('Field layout for', field.name, ':', field.display?.layout);
        if (field.display?.layout === 'inline') {
            // 内联模式：一行显示多个语言
            console.log('Using inline layout for', field.name);
            const grid = document.createElement('div');
            // 使用固定的网格类名以确保 Tailwind 生成这些类
            const gridCols = languages.length === 3 ? 'grid-cols-3' :
                            languages.length === 2 ? 'grid-cols-2' : 'grid-cols-1';
            grid.className = `grid ${gridCols} gap-2`;

            languages.forEach(lang => {
                const langWrapper = document.createElement('div');

                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-xs font-medium text-primary-700 dark:text-primary-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const textarea = document.createElement('textarea');
                textarea.name = `${field.name}[${lang.code}]`;
                textarea.className = 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                textarea.rows = field.display?.rows || 3;
                textarea.placeholder = `${lang.name}...`;

                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    textarea.value = field.defaultValue[lang.code];
                }

                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(textarea);
                grid.appendChild(langWrapper);
            });

            wrapper.appendChild(grid);
        } else {
            // 堆叠模式：每个语言占一行
            console.log('Using stacked layout for', field.name, 'layout value:', field.display?.layout);
            languages.forEach(lang => {
                const langWrapper = document.createElement('div');
                langWrapper.className = 'mb-3';

                const langLabel = document.createElement('label');
                langLabel.className = 'block mb-1 text-sm font-medium text-primary-700 dark:text-primary-400';
                langLabel.textContent = `${lang.emoji} ${lang.name}`;

                const textarea = document.createElement('textarea');
                textarea.name = `${field.name}[${lang.code}]`;
                textarea.className = 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
                textarea.rows = field.display?.rows || 3;
                textarea.placeholder = `${this.getResource('EnterText', 'Enter text', 'admin')}...`;

                // 设置默认值（只在创建时设置）
                if (field.defaultValue && typeof field.defaultValue === 'object' && field.defaultValue[lang.code] !== undefined) {
                    textarea.value = field.defaultValue[lang.code];
                }

                langWrapper.appendChild(langLabel);
                langWrapper.appendChild(textarea);
                wrapper.appendChild(langWrapper);
            });
        }
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 创建文件上传组件
     */
    createFileUpload(field) {
        const wrapper = document.createElement('div');
        wrapper.className = 'file-upload-wrapper';
        wrapper.__formRenderer = this; // Store reference for delete functionality

        // 创建标签
        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        
        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        
        // 创建文件上传区域
        const uploadArea = document.createElement('div');
        uploadArea.className = 'flex items-center justify-center w-full';
        
        const dropZone = document.createElement('label');
        dropZone.className = 'flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600 transition-colors';
        dropZone.setAttribute('for', `file_${field.name}`);
        
        // 上传区域内容
        dropZone.innerHTML = `
            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span class="font-semibold">${this.getResource('FileUpload_ClickToUpload', 'Click to upload', 'admin')}</span>
                    ${this.getResource('FileUpload_OrDragDrop', 'or drag and drop files here', 'admin')}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    ${this.getFileTypeDescription(field.fileConfig)}
                </p>
            </div>
        `;
        
        // 隐藏的文件输入
        const fileInput = document.createElement('input');
        fileInput.id = `file_${field.name}`;
        fileInput.type = 'file';
        fileInput.name = field.name;
        fileInput.className = 'hidden';
        
        // 设置文件限制
        if (field.fileConfig?.types) {
            fileInput.accept = field.fileConfig.types.join(',');
        }
        if (field.fileConfig?.multiple) {
            fileInput.multiple = true;
        }
        
        uploadArea.appendChild(dropZone);
        uploadArea.appendChild(fileInput);
        wrapper.appendChild(uploadArea);
        
        // 文件列表容器
        const fileList = document.createElement('div');
        fileList.className = 'mt-4 space-y-2';
        fileList.id = `filelist_${field.name}`;
        wrapper.appendChild(fileList);
        
        // 绑定事件（优先使用全局通用 AdminUpload）
        if (window.AdminUpload && typeof window.AdminUpload.bind === 'function') {
            window.AdminUpload.bind(fileInput, {
                fileListEl: fileList,
                fileConfig: field.fileConfig || {},
                getResource: this.getResource ? this.getResource.bind(this) : undefined
            });
        } else {
            this.attachFileUploadEvents(fileInput, field, fileList);
        }
        
        // 添加帮助文本
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
    
    /**
     * 获取文件类型描述
     */
    getFileTypeDescription(fileConfig) {
        if (!fileConfig?.types || fileConfig.types.length === 0) {
            return this.getResource('FileUpload_AllFiles', 'All file types', 'admin');
        }
        
        const typeDescriptions = {
            'image/*': this.getResource('FileType_Image', 'Image', 'admin'),
            'video/*': this.getResource('FileType_Video', 'Video', 'admin'),
            'application/pdf': this.getResource('FileType_PDF', 'PDF', 'admin'),
            'application/msword': this.getResource('FileType_Word', 'Word Document', 'admin'),
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': this.getResource('FileType_Word', 'Word Document', 'admin')
        };
        
        const descriptions = fileConfig.types.map(type => 
            typeDescriptions[type] || type
        );
        
        const maxSize = fileConfig.maxSize ? `, ${this.getResource('FileUpload_MaxSize', 'Max', 'admin')}${fileConfig.maxSize}` : '';
        
        return `${descriptions.join(', ')}${maxSize}`;
    }
    
    /**
     * 绑定文件上传事件
     */
    attachFileUploadEvents(fileInput, field, fileList) {
        const dropZone = fileInput.parentElement.querySelector('label');
        
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files, field, fileList);
        });
        
        // 拖拽事件
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
        });
        
        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900/20');
            this.handleFileSelection(e.dataTransfer.files, field, fileList);
        });
    }
    
    /**
     * 处理文件选择
     */
    async handleFileSelection(files, field, fileList) {
        const fileArray = Array.from(files);
        
        for (const file of fileArray) {
            try {
                await this.uploadFile(file, field, fileList);
            } catch (error) {
                console.error('File upload error:', error);
                if (typeof Dialog !== 'undefined') {
                    Dialog.error(`${this.getResource('FileUpload_UploadError', 'Upload failed', 'admin')}: ${file.name}`);
                }
            }
        }
    }
    
    /**
     * 上传单个文件
     */
    async uploadFile(file, field, fileList) {
        // 显示上传进度
        const progressItem = this.createProgressItem(file.name);
        fileList.appendChild(progressItem);
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('folder', field.fileConfig?.folder || 'default');
            formData.append('allowedTypes', (field.fileConfig?.types || []).join(','));
            formData.append('maxSize', field.fileConfig?.maxSize || '10MB');
            
            const response = await fetch('/api/FileUpload/upload', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || this.getResource('FileUpload_UploadError', 'Upload failed', 'admin'));
            }
            
            const result = await response.json();
            
            // 移除进度条，显示成功的文件项
            fileList.removeChild(progressItem);
            const fileItem = this.createFileItem(result, field);
            fileList.appendChild(fileItem);
            
        } catch (error) {
            // 移除进度条，显示错误
            fileList.removeChild(progressItem);
            throw error;
        }
    }
    
    /**
     * 创建上传进度项
     */
    createProgressItem(fileName) {
        const item = document.createElement('div');
        item.className = 'flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700';
        
        item.innerHTML = `
            <div class="flex-1">
                <p class="text-sm font-medium text-gray-900 dark:text-white">${fileName}</p>
                <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700 mt-1">
                    <div class="bg-primary-600 h-2 rounded-full animate-pulse" style="width: 45%"></div>
                </div>
            </div>
            <div class="ml-3">
                <div class="animate-spin w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full"></div>
            </div>
        `;
        
        return item;
    }
    
    /**
     * 创建文件项
     */
    createFileItem(fileInfo, field) {
        const item = document.createElement('div');
        item.className = 'flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm';
        item.dataset.filePath = fileInfo.filePath;
        
        // 根据文件类型显示不同图标
        const icon = this.getFileIcon(fileInfo.contentType);
        
        item.innerHTML = `
            <div class="flex-shrink-0 mr-3">
                <i class="${icon} text-2xl text-gray-500"></i>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    ${fileInfo.originalName}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    ${this.formatFileSize(fileInfo.fileSize)}
                </p>
            </div>
            <div class="flex items-center space-x-2">
                ${field.fileConfig?.preview && this.isPreviewable(fileInfo.contentType) ? 
                    `<button type="button" class="p-1 text-primary-600 hover:text-primary-700 dark:text-primary-400" 
                             onclick="window.open('${fileInfo.filePath}', '_blank')" 
                             title="${this.getResource('FileUpload_Preview', 'Preview', 'admin')}">
                        <i class="fas fa-eye"></i>
                    </button>` : ''
                }
                <button type="button" class="p-1 text-red-600 hover:text-red-700 dark:text-red-400" 
                        onclick="this.closest('.file-upload-wrapper').__formRenderer.deleteFile('${fileInfo.filePath}', this.closest('[data-file-path]'))"
                        title="${this.getResource('FileUpload_DeleteFile', 'Delete file', 'admin')}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        return item;
    }
    
    /**
     * 获取文件图标
     */
    getFileIcon(contentType) {
        if (contentType.startsWith('image/')) return 'fas fa-image text-primary-500';
        if (contentType.startsWith('video/')) return 'fas fa-video text-primary-600';
        if (contentType.includes('pdf')) return 'fas fa-file-pdf text-error-500';
        if (contentType.includes('word') || contentType.includes('document')) return 'fas fa-file-word text-primary-700';
        return 'fas fa-file text-gray-500';
    }
    
    /**
     * 是否可预览
     */
    isPreviewable(contentType) {
        return contentType.startsWith('image/') || 
               contentType.includes('pdf') || 
               contentType.startsWith('video/');
    }
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 删除文件
     */
    async deleteFile(filePath, fileElement) {
        try {
            if (typeof Dialog !== 'undefined') {
                const result = await Dialog.confirm(
                    this.getResource('FileUpload_DeleteConfirm', 'Are you sure you want to delete this file?', 'admin'),
                    this.getResource('Confirm', 'Confirm', 'admin')
                );
                
                if (!result) return;
            }
            
            const response = await fetch(`/api/FileUpload/delete?filePath=${encodeURIComponent(filePath)}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || this.getResource('FileUpload_DeleteError', 'Delete failed', 'admin'));
            }
            
            // 从DOM中移除文件项
            fileElement.remove();
            
            if (typeof Dialog !== 'undefined') {
                Dialog.notify(this.getResource('FileUpload_DeleteSuccess', 'File deleted successfully', 'admin'), 'success');
            }
            
        } catch (error) {
            console.error('File delete error:', error);
            if (typeof Dialog !== 'undefined') {
                Dialog.error(`${this.getResource('FileUpload_DeleteError', 'Delete failed', 'admin')}: ${error.message}`);
            }
        }
    }
    
    /**
     * 获取表单数据
     */
    getFormData() {
        const data = {};
        
        this.fieldInstances.forEach((instance, fieldName) => {
            const field = instance.field;
            const container = instance.container;
            
            try {
                switch (field.type) {
                    case 'multilingual-text':
                    case 'multilingual-textarea':
                        data[fieldName] = this.getMultilingualValue(container, fieldName);
                        break;
                    case 'file':
                    case 'image':
                    case 'video':
                        data[fieldName] = this.getFileValue(container);
                        break;
                    case 'checkbox':
                        const chkbox = container.querySelector(`[name="${fieldName}"]`);
                        data[fieldName] = chkbox.checked;
                        break;
                    default:
                        const input = container.querySelector(`[name="${fieldName}"]`);
                        data[fieldName] = input ? input.value : '';
                }
            } catch (error) {
                console.error(`Error getting value for field ${fieldName}:`, error);
                data[fieldName] = '';
            }
        });
        
        return data;
    }
    
    /**
     * 获取多语言字段值
     */
    getMultilingualValue(container, fieldName) {
        const values = {};
        const inputs = container.querySelectorAll(`[name^="${fieldName}["]`);
        
        inputs.forEach(input => {
            const match = input.name.match(/\[(\w+)\]$/);
            if (match) {
                values[match[1]] = input.value;
            }
        });
        
        return values;
    }
    
    /**
     * 获取文件字段值
     */
    getFileValue(container) {
        const fileItems = container.querySelectorAll('[data-file-path]');
        const files = [];
        
        fileItems.forEach(item => {
            files.push({
                path: item.dataset.filePath,
                name: item.querySelector('.truncate').textContent.replace(/\n/gi, '').trim()
            });
        });
        
        return files;
    }
    
    /**
     * 设置表单数据
     */
    setFormData(data) {
        Object.entries(data).forEach(([fieldName, value]) => {
            const instance = this.fieldInstances.get(fieldName);
            if (!instance) return;
            
            const field = instance.field;
            const container = instance.container;
            
            try {
                switch (field.type) {
                    case 'multilingual-text':
                    case 'multilingual-textarea':
                        this.setMultilingualValue(container, fieldName, value);
                        break;
                    case 'file':
                    case 'image': 
                    case 'video':
                        this.setFileValue(container, value, field);
                        break;
                    case 'checkbox':
                        const chkbox = container.querySelector(`[name="${fieldName}"]`);
                        chkbox.checked = value;
                        break;
                    default:
                        const input = container.querySelector(`[name="${fieldName}"]`);
                        if (input) input.value = value || '';
                }
            } catch (error) {
                console.error(`Error setting value for field ${fieldName}:`, error);
            }
        });
    }
    
    /**
     * 设置多语言字段值
     */
    setMultilingualValue(container, fieldName, values) {
        if (typeof values === 'object' && values !== null) {
            Object.entries(values).forEach(([lang, value]) => {
                const input = container.querySelector(`[name="${fieldName}[${lang}]"]`);
                if (input) input.value = value || '';
            });
        }
    }
    
    /**
     * 设置文件字段值
     */
    setFileValue(container, files, field) {
        const fileList = container.querySelector(`[id^="filelist_"]`);
        if (!fileList || !Array.isArray(files)) return;
        
        // 清空现有文件
        fileList.innerHTML = '';
        
        // 添加文件项
        files.forEach(file => {
            if (file.path) {
                const fileInfo = {
                    filePath: file.path,
                    originalName: file.name || file.path.split('/').pop(),
                    fileSize: file.size || 0,
                    contentType: file.type || 'application/octet-stream'
                };
                
                const fileItem = this.createFileItem(fileInfo, field);
                fileList.appendChild(fileItem);
            }
        });
    }

    /**
     * 创建复选框输入
     */
    createCheckboxInput(field) {
        const wrapper = document.createElement('div');

        const label = document.createElement('label');
        label.className = 'flex items-center';

        const input = document.createElement('input');
        input.type = 'checkbox';
        input.id = `field_${field.name}`;
        input.name = field.name;
        input.className = 'sr-only peer';

        if (field.validation?.required) {
            input.required = true;
        }

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            input.checked = !!field.defaultValue;
        }

        const checkbox = document.createElement('div');
        checkbox.className = 'relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[\'\'] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600';

        const labelText = document.createElement('span');
        labelText.className = 'ml-3 text-sm font-medium text-primary-800 dark:text-primary-300';
        labelText.textContent = this.resolveResourceKey(field.label) || field.name;
        
        if (field.validation?.required) {
            labelText.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        label.appendChild(input);
        label.appendChild(checkbox);
        label.appendChild(labelText);
        wrapper.appendChild(label);
        
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }

    /**
     * 创建选择框输入
     */
    createSelectInput(field) {
        const wrapper = document.createElement('div');

        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        label.setAttribute('for', `field_${field.name}`);

        const select = document.createElement('select');
        select.id = `field_${field.name}`;
        select.name = field.name;
        select.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';

        if (field.validation?.required) {
            select.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            select.value = field.defaultValue;
        }
        
        // 添加选项
        if (field.options && Array.isArray(field.options)) {
            // 添加空选项
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = this.getResource('PleaseSelect', 'Please select...', 'admin');
            select.appendChild(emptyOption);
            
            field.options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value || option;
                optionElement.textContent = this.resolveResourceKey(option.label) || option.value || option;
                select.appendChild(optionElement);
            });
        }
        
        wrapper.appendChild(label);
        wrapper.appendChild(select);
        
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }

    /**
     * 创建单选按钮输入
     */
    createRadioInput(field) {
        const wrapper = document.createElement('div');

        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        
        if (field.validation?.required) {
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        
        wrapper.appendChild(label);
        
        const radioGroup = document.createElement('div');
        radioGroup.className = 'space-y-2';
        
        if (field.options && Array.isArray(field.options)) {
            field.options.forEach((option, index) => {
                const radioWrapper = document.createElement('div');
                radioWrapper.className = 'flex items-center';
                
                const input = document.createElement('input');
                input.type = 'radio';
                input.id = `field_${field.name}_${index}`;
                input.name = field.name;
                input.value = option.value || option;
                input.className = 'w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600';

                if (field.validation?.required) {
                    input.required = true;
                }

                // 设置默认值（只在创建时设置）
                if (field.defaultValue === (option.value || option)) {
                    input.checked = true;
                }
                
                const radioLabel = document.createElement('label');
                radioLabel.setAttribute('for', `field_${field.name}_${index}`);
                radioLabel.className = 'ml-2 text-sm font-medium text-primary-800 dark:text-primary-300';
                radioLabel.textContent = this.resolveResourceKey(option.label) || option.value || option;
                
                radioWrapper.appendChild(input);
                radioWrapper.appendChild(radioLabel);
                radioGroup.appendChild(radioWrapper);
            });
        }
        
        wrapper.appendChild(radioGroup);
        
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }

    /**
     * 创建富文本编辑器
     */
    createRichTextEditor(field) {
        const wrapper = document.createElement('div');

        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        label.setAttribute('for', `field_${field.name}`);

        const textarea = document.createElement('textarea');
        textarea.id = `field_${field.name}`;
        textarea.name = field.name;
        textarea.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
        textarea.rows = field.display?.rows || 6;

        if (field.validation?.required) {
            textarea.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            textarea.value = field.defaultValue;
        }

        wrapper.appendChild(label);
        wrapper.appendChild(textarea);

        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }

        // 延迟初始化TinyMCE编辑器
        setTimeout(() => {
            this.initializeTinyMCE(textarea, field.editorConfig);
        }, 100);

        return wrapper;
    }

    /**
     * 初始化TinyMCE编辑器
     */
    initializeTinyMCE(textarea, config) {
        // 优先使用通用的 AdminTinyMCE 初始化
        if (typeof window !== 'undefined' && window.AdminTinyMCE && typeof window.AdminTinyMCE.init === 'function') {
            window.AdminTinyMCE.init(textarea, config);
            return;
        }

        // 最小化的后备方案（保持兼容性）
        if (typeof tinymce === 'undefined') {
            console.warn('TinyMCE is not loaded');
            return;
        }
        const editorId = textarea.id || (textarea.name ? textarea.name.replace(/[\[\]\.\s]/g, '_') : 'editor_' + Date.now());
        if (!textarea.id) {
            textarea.id = editorId;
        }
        try {
            tinymce.init(Object.assign({ selector: `#${editorId}` }, config || {}));
        } catch (error) {
            console.error(`Failed to initialize TinyMCE for ${editorId}:`, error);
        }
    }

    /**
     * 创建颜色选择器
     */
    createColorPicker(field) {
        const wrapper = document.createElement('div');

        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        label.setAttribute('for', `field_${field.name}`);

        const input = document.createElement('input');
        input.type = 'color';
        input.id = `field_${field.name}`;
        input.name = field.name;
        input.className = 'p-1 h-10 w-20 bg-white border border-gray-300 rounded-lg cursor-pointer focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-500 dark:focus:border-primary-500';

        if (field.validation?.required) {
            input.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            input.value = field.defaultValue;
        }
        
        wrapper.appendChild(label);
        wrapper.appendChild(input);
        
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }

    /**
     * 创建日期选择器
     */
    createDatePicker(field) {
        const wrapper = document.createElement('div');

        const label = document.createElement('label');
        label.className = 'block mb-2 text-sm font-medium text-primary-800 dark:text-primary-300';
        label.textContent = this.resolveResourceKey(field.label) || field.name;
        label.setAttribute('for', `field_${field.name}`);

        const input = document.createElement('input');
        input.type = field.type === 'datetime' ? 'datetime-local' : field.type;
        input.id = `field_${field.name}`;
        input.name = field.name;
        input.className = 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 placeholder-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500';
        
        if (field.validation?.required) {
            input.required = true;
            label.innerHTML += ' <span class="text-red-500">*</span>';
        }
        if (field.validation?.min) {
            input.min = field.validation.min;
        }
        if (field.validation?.max) {
            input.max = field.validation.max;
        }

        // 设置默认值（只在创建时设置）
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
            input.value = field.defaultValue;
        }
        
        wrapper.appendChild(label);
        wrapper.appendChild(input);
        
        if (field.display?.helpText) {
            const helpText = document.createElement('p');
            helpText.className = 'mt-1 text-sm text-gray-600 dark:text-gray-400';
            helpText.textContent = this.resolveResourceKey(field.display.helpText);
            wrapper.appendChild(helpText);
        }
        
        return wrapper;
    }
}