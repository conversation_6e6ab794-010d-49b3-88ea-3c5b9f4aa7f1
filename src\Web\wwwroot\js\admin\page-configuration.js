/**
 * 页面配置管理 JavaScript
 * 支持页面编辑器的交互功能
 */

// 全局变量
let componentIndex = 0;
let availableComponents = [];
let lastPlaceholderPosition = null; // 防止重复创建占位符


// 构建带语言前缀的API URL
function buildApiUrl(path) {
    const languagePrefix = getCurrentLanguagePrefix();
    const cleanPath = path.startsWith('/') ? path : '/' + path;

    if (languagePrefix) {
        return `/${languagePrefix}${cleanPath}`;
    }

    return cleanPath;
}

// 本地化辅助函数
function getLocalizedText(key, fallback = '') {
    return window.PageConfigL10n && window.PageConfigL10n[key] ? window.PageConfigL10n[key] : fallback;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializePageConfiguration();
});

/**
 * 初始化页面配置功能
 */
function initializePageConfiguration() {
    // 初始化现有组件
    initializeExistingComponents();

    // 初始化表单验证
    initializeFormValidation();

    // 初始化图片质量滑块更新
    initializeImageQualitySlider();

    // 初始化组件列表拖放区域
    setupComponentListDropZone();

    // 初始化表单提交处理
    initializeFormSubmission();
}

/**
 * 初始化现有组件
 */
function initializeExistingComponents() {
    const componentList = document.getElementById('component-list');
    if (componentList) {
        const componentElements = componentList.querySelectorAll('.page-component');
        componentIndex = componentElements.length;

        // 为每个现有组件添加事件监听器
        componentElements.forEach((element, index) => {
            attachComponentEvents(element, index);
        });
    }

    // 如果没有组件，显示空状态消息
    updateEmptyState();
}

/**
 * 为组件附加事件监听器
 */
function attachComponentEvents(componentElement, index) {
    // 编辑按钮
    const editBtn = componentElement.querySelector('.component-edit-btn');
    if (editBtn) {
        editBtn.addEventListener('click', () => {
            console.log('Edit component:', componentElement.dataset.componentId);
            openComponentEditor(componentElement);
        });
    }

    // 组件点击选择（用于右侧属性面板）
    const componentPreview = componentElement.querySelector('.p-6');
    if (componentPreview) {
        componentPreview.addEventListener('click', (e) => {
            // 避免与按钮点击冲突
            if (e.target.closest('button')) return;

            selectComponent(componentElement);
        });
    }

    // 可见性切换按钮
    const visibilityBtn = componentElement.querySelector('.component-visibility-btn');
    if (visibilityBtn) {
        visibilityBtn.addEventListener('click', () => {
            toggleComponentVisibility(componentElement);
        });
    }

    // 删除按钮 - 统一处理所有删除按钮
    const deleteButtons = componentElement.querySelectorAll('.component-delete-btn, .remove-btn');
    deleteButtons.forEach(deleteBtn => {
        // 检查是否已经绑定过事件，避免重复绑定
        if (!deleteBtn.hasAttribute('data-event-bound')) {
            deleteBtn.setAttribute('data-event-bound', 'true');
            deleteBtn.addEventListener('click', async (e) => {
                e.stopPropagation(); // 防止事件冒泡
                const result = await Dialog.confirm(getLocalizedText('deleteComponentConfirm', '确定要删除这个组件吗？'));
                if (result) {
                    removeComponent(componentElement);
                }
            });
        }
    });

    // 拖拽手柄（用于组件重排序）
    const dragHandle = componentElement.querySelector('.component-drag-handle');
    if (dragHandle && !dragHandle.hasAttribute('data-drag-bound')) {
        dragHandle.setAttribute('data-drag-bound', 'true');
        setupComponentReorderDrag(componentElement, dragHandle);
    }

    // 保留原有的上移下移按钮支持（如果存在）
    const moveUpBtn = componentElement.querySelector('.move-up-btn');
    if (moveUpBtn && !moveUpBtn.hasAttribute('data-event-bound')) {
        moveUpBtn.setAttribute('data-event-bound', 'true');
        moveUpBtn.addEventListener('click', () => moveComponentUp(componentElement));
    }

    const moveDownBtn = componentElement.querySelector('.move-down-btn');
    if (moveDownBtn && !moveDownBtn.hasAttribute('data-event-bound')) {
        moveDownBtn.setAttribute('data-event-bound', 'true');
        moveDownBtn.addEventListener('click', () => moveComponentDown(componentElement));
    }
}

/**
 * 选择组件并更新属性面板
 */
function selectComponent(componentElement) {
    // 移除其他组件的选中状态
    document.querySelectorAll('.page-component').forEach(comp => {
        comp.classList.remove('border-primary-600', 'bg-primary-50', 'dark:bg-primary-100');
    });

    // 添加选中状态
    componentElement.classList.add('border-primary-600', 'bg-primary-50', 'dark:bg-primary-100');

    // 更新右侧属性面板
    updatePropertiesPanel(componentElement);
}

/**
 * 更新属性面板内容
 */
function updatePropertiesPanel(componentElement) {
    const propertiesPanel = document.getElementById('properties-panel');
    if (!propertiesPanel) return;

    const componentId = componentElement.dataset.componentId;
    const template = componentElement.dataset.template || 'Default';
    const visible = componentElement.dataset.visible === 'true';
    const displayOrder = componentElement.dataset.displayOrder || 0;
    const columnSpan = componentElement.dataset.columnSpan || '12';

    // 获取组件参数
    const parametersPre = componentElement.querySelector('pre');
    const parametersJson = parametersPre ? parametersPre.textContent : '{}';

    const panelHtml = `
        <div class="space-y-4">
            <!-- 组件信息 -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">${getLocalizedText('componentInfo', '组件信息')}</h3>
                <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                    <div><span class="font-medium">${getLocalizedText('componentType', '类型')}:</span> ${componentId}</div>
                    <div><span class="font-medium">${getLocalizedText('template', '模板')}:</span> ${template}</div>
                    <div><span class="font-medium">${getLocalizedText('displayOrder', '顺序')}:</span> ${displayOrder}</div>
                    <div class="column-span-info"><span class="font-medium">${getLocalizedText('columnSpan', '列跨度')}:</span> ${columnSpan}</div>
                    <div><span class="font-medium">${getLocalizedText('status', '状态')}:</span> ${visible ? getLocalizedText('visible', '可见') : getLocalizedText('hidden', '隐藏')}</div>
                </div>
            </div>
            
            <!-- 快速操作 -->
            <div class="space-y-2">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">${getLocalizedText('quickActions', '快速操作')}</h3>
                
                <!-- 可见性切换 -->
                <label class="flex items-center space-x-2 p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                    <input type="checkbox" ${visible ? 'checked' : ''} 
                           class="component-visibility-toggle text-primary-600" 
                           data-component-id="${componentId}">
                    <span class="text-sm text-gray-700 dark:text-gray-300">${getLocalizedText('componentVisible', '组件可见')}</span>
                </label>
                
                <!-- 模板选择 -->
                <div>
                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">${getLocalizedText('template', '模板')}</label>
                    <select id="component-template-select" class="component-template-select w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white" 
                            data-component-id="${componentId}">
                        <option value="Default" ${template === 'Default' ? 'selected' : ''}>${getLocalizedText('loadingTemplates', '正在加载模板...')}</option>
                    </select>
                </div>
                
                <!-- 列跨度选择 -->
                <div>
                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">${getLocalizedText('columnSpan', '列跨度')}</label>
                    <select id="component-column-span-select" class="component-column-span-select w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white" 
                            data-component-id="${componentId}">
                        <option value="12" ${columnSpan === '12' ? 'selected' : ''}>12</option>
                        <option value="11" ${columnSpan === '11' ? 'selected' : ''}>11</option>
                        <option value="10" ${columnSpan === '10' ? 'selected' : ''}>10</option>
                        <option value="9" ${columnSpan === '9' ? 'selected' : ''}>9</option>
                        <option value="8" ${columnSpan === '8' ? 'selected' : ''}>8</option>
                        <option value="7" ${columnSpan === '7' ? 'selected' : ''}>7</option>
                        <option value="6" ${columnSpan === '6' ? 'selected' : ''}>6</option>
                        <option value="5" ${columnSpan === '5' ? 'selected' : ''}>5</option>
                        <option value="4" ${columnSpan === '4' ? 'selected' : ''}>4</option>
                        <option value="3" ${columnSpan === '3' ? 'selected' : ''}>3</option>
                        <option value="2" ${columnSpan === '2' ? 'selected' : ''}>2</option>
                        <option value="1" ${columnSpan === '1' ? 'selected' : ''}>1</option>
                    </select>
                </div>
            </div>
            
            <!-- 参数预览 -->
            <div>
                <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">${getLocalizedText('componentParameters', '组件参数')}</h3>
                <div class="bg-gray-100 dark:bg-gray-700 rounded p-2 max-h-32 overflow-y-auto">
                    <pre class="text-xs text-gray-800 dark:text-gray-200 whitespace-pre-wrap">${parametersJson}</pre>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="space-y-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                <button type="button" class="edit-component-btn w-full px-3 py-2 text-sm bg-primary-600 hover:bg-primary-700 text-white rounded-md" 
                        data-component-id="${componentId}">
                    <i class="fas fa-edit mr-1"></i> ${getLocalizedText('editComponent', '编辑组件')}
                </button>
                <button type="button" class="delete-component-btn w-full px-3 py-2 text-sm bg-red-600 hover:bg-red-700 text-white rounded-md" 
                        data-component-id="${componentId}">
                    <i class="fas fa-trash mr-1"></i> ${getLocalizedText('deleteComponent', '删除组件')}
                </button>
            </div>
        </div>
    `;

    propertiesPanel.innerHTML = panelHtml;

    // 加载模板选项
    loadComponentTemplates(componentId, template);

    // 绑定属性面板事件
    bindPropertiesPanelEvents(componentElement);
}

/**
 * 加载组件模板选项
 */
function loadComponentTemplates(componentId, currentTemplate) {
    const templateSelect = document.getElementById('component-template-select');
    if (!templateSelect) return;

    // 构建API URL（带语言前缀）
    const url = buildApiUrl(`/Admin/PageConfiguration/GetComponentTemplates/${componentId}`);

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.templates) {
                // 清空现有选项
                templateSelect.innerHTML = '';

                // 添加模板选项
                data.templates.forEach(template => {
                    const option = document.createElement('option');
                    option.value = template.id;
                    option.textContent = template.name;
                    option.title = template.description;

                    // 设置当前选中的模板
                    if (template.id === currentTemplate) {
                        option.selected = true;
                    }

                    templateSelect.appendChild(option);
                });

                console.log(`Loaded ${data.templates.length} templates for component: ${componentId}`);
            } else {
                console.warn('Failed to load templates:', data.message);
                // 显示默认选项
                templateSelect.innerHTML = '<option value="Default">Default</option>';
            }
        })
        .catch(error => {
            console.error('Error loading component templates:', error);
            // 显示默认选项
            templateSelect.innerHTML = '<option value="Default">Default</option>';
        });
}

/**
 * 绑定属性面板事件
 */
function bindPropertiesPanelEvents(componentElement) {
    const propertiesPanel = document.getElementById('properties-panel');
    if (!propertiesPanel) return;

    // 可见性切换
    const visibilityToggle = propertiesPanel.querySelector('.component-visibility-toggle');
    if (visibilityToggle) {
        visibilityToggle.addEventListener('change', (e) => {
            componentElement.dataset.visible = e.target.checked.toString();
            toggleComponentVisibility(componentElement);

            // 更新状态显示
            updatePropertiesPanel(componentElement);

            // 更新JSON隐藏字段
            updateComponentsJsonField();
        });
    }

    // 模板选择
    const templateSelect = propertiesPanel.querySelector('.component-template-select');
    if (templateSelect) {
        templateSelect.addEventListener('change', (e) => {
            const newTemplate = e.target.value;
            componentElement.dataset.template = newTemplate;

            // 更新组件显示
            const templateSpan = componentElement.querySelector('.ml-2.text-xs');
            if (templateSpan) {
                templateSpan.textContent = `(${newTemplate})`;
            }

            // 更新属性面板
            updatePropertiesPanel(componentElement);

            // 更新JSON隐藏字段
            updateComponentsJsonField();
        });
    }

    // 列跨度选择
    const columnSpanSelect = propertiesPanel.querySelector('.component-column-span-select');
    if (columnSpanSelect) {
        columnSpanSelect.addEventListener('change', (e) => {
            const newColumnSpan = e.target.value;
            componentElement.dataset.columnSpan = newColumnSpan;

            // 更新组件显示（在组件信息中显示列跨度）
            const componentInfo = componentElement.querySelector('.bg-gray-50.dark\\:bg-gray-700');
            if (componentInfo) {
                const columnSpanDiv = componentInfo.querySelector('.column-span-info');
                if (columnSpanDiv) {
                    columnSpanDiv.textContent = `${getLocalizedText('columnSpan', '列跨度')}: ${newColumnSpan}`;
                }
            }

            // 更新JSON隐藏字段
            updateComponentsJsonField();
        });
    }

    // 编辑按钮
    const editBtn = propertiesPanel.querySelector('.edit-component-btn');
    if (editBtn) {
        editBtn.addEventListener('click', () => {
            openComponentEditor(componentElement);
        });
    }

    // 删除按钮
    const deleteBtn = propertiesPanel.querySelector('.delete-component-btn');
    if (deleteBtn && !deleteBtn.hasAttribute('data-event-bound')) {
        deleteBtn.setAttribute('data-event-bound', 'true');
        deleteBtn.addEventListener('click', async (e) => {
            e.stopPropagation(); // 防止事件冒泡
            const result = await Dialog.confirm(getLocalizedText('deleteComponentConfirm', '确定要删除这个组件吗？'));
            if (result) {
                removeComponent(componentElement);
                clearPropertiesPanel();
            }
        });
    }
}

/**
 * 清空属性面板
 */
function clearPropertiesPanel() {
    const propertiesPanel = document.getElementById('properties-panel');
    if (!propertiesPanel) return;

    propertiesPanel.innerHTML = `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            <i class="fas fa-hand-pointer text-2xl mb-2"></i>
            <p class="text-sm">${getLocalizedText('selectComponentToEdit', '选择组件进行编辑')}</p>
        </div>
    `;
}

/**
 * 设置组件重排序拖拽功能 - 简化版本
 */
function setupComponentReorderDrag(componentElement, dragHandle) {
    if (!dragHandle || !componentElement) return;

    dragHandle.draggable = true;
    dragHandle.style.cursor = 'move';

    dragHandle.addEventListener('dragstart', (e) => {
        // 标记正在拖拽的元素
        componentElement.classList.add('dragging');
        componentElement.style.opacity = '0.5';

        // 设置拖拽数据
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', componentElement.outerHTML);
        e.dataTransfer.setData('text/plain', componentElement.dataset.componentId);

        console.log('开始拖拽组件:', componentElement.dataset.componentId);

        // 立即设置拖放区域监听器
        setupDragEvents();
    });

    dragHandle.addEventListener('dragend', (e) => {
        console.log('拖拽结束事件触发');

        // 延迟清理，确保drop事件先执行
        setTimeout(() => {
            // 恢复样式（如果drop事件没有处理）
            const draggingElement = document.querySelector('.dragging');
            if (draggingElement) {
                draggingElement.classList.remove('dragging');
                draggingElement.style.opacity = '';
                console.log('恢复拖拽元素样式');
            }

            // 清理所有占位符
            document.querySelectorAll('.drop-placeholder').forEach(p => p.remove());

            // 重置位置记录
            lastPlaceholderPosition = null;

            // 移除临时事件监听器
            removeDragEvents();

            console.log('拖拽完全结束');
        }, 10); // 稍微增加延迟确保drop事件完成
    });
}

/**
 * 动态设置拖放事件 - 在拖拽开始时绑定
 */
function setupDragEvents() {
    console.log('设置拖放事件监听器');
    const componentList = document.getElementById('component-list');
    if (!componentList) return;

    // 移除可能存在的旧监听器
    removeDragEvents();

    // 绑定事件
    componentList.addEventListener('dragover', handleDragOver, true);
    componentList.addEventListener('drop', handleDrop, true);
    componentList.addEventListener('dragleave', handleDragLeave, true);

    // 标记已绑定
    componentList.dataset.dragEventsActive = 'true';
}

/**
 * 移除拖放事件监听器
 */
function removeDragEvents() {
    const componentList = document.getElementById('component-list');
    if (!componentList || componentList.dataset.dragEventsActive !== 'true') return;

    console.log('移除拖放事件监听器');
    componentList.removeEventListener('dragover', handleDragOver, true);
    componentList.removeEventListener('drop', handleDrop, true);
    componentList.removeEventListener('dragleave', handleDragLeave, true);

    delete componentList.dataset.dragEventsActive;
}

/**
 * 初始化组件列表的拖放功能 - 简化版本
 */
function setupComponentListDropZone() {
    const componentList = document.getElementById('component-list');
    if (!componentList) return;

    console.log('组件拖放区域已初始化（动态绑定模式）');
}

/**
 * 处理拖拽悬停 - 优化版本，减少重复操作
 */
function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'move';

    console.log('dragover 事件触发');

    // 只处理组件重排序
    const draggingElement = document.querySelector('.dragging');
    if (!draggingElement) {
        console.log('没有找到拖拽元素');
        return;
    }

    console.log('处理拖拽悬停，拖拽元素:', draggingElement.dataset.componentId);

    const componentList = document.getElementById('component-list');
    const afterElement = getDragAfterElement(componentList, e.clientY);

    // 计算当前位置标识
    const currentPosition = afterElement ? afterElement.dataset.componentId : 'end';

    console.log('目标位置:', currentPosition);

    // 如果位置没有变化，不重新创建占位符
    if (lastPlaceholderPosition === currentPosition) {
        return;
    }

    lastPlaceholderPosition = currentPosition;

    // 移除现有占位符
    document.querySelectorAll('.drop-placeholder').forEach(p => p.remove());

    // 创建新占位符
    const placeholder = document.createElement('div');
    placeholder.className = 'drop-placeholder border-2 border-dashed border-blue-400 bg-blue-50 p-2 mb-2 rounded';
    placeholder.innerHTML = `<div class="text-center text-blue-600 text-sm">${getLocalizedText('dropHere', '放置在这里')}</div>`;

    if (afterElement === null) {
        componentList.appendChild(placeholder);
        console.log('占位符添加到末尾');
    } else {
        componentList.insertBefore(placeholder, afterElement);
        console.log('占位符插入到位置:', afterElement.dataset.componentId);
    }
}

/**
 * 处理放置 - 修复版本
 */
function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();

    console.log('=== 开始处理放置事件 ===');

    const draggingElement = document.querySelector('.dragging');
    const placeholder = document.querySelector('.drop-placeholder');

    console.log('拖拽元素:', draggingElement?.dataset?.componentId);
    console.log('占位符存在:', !!placeholder);

    if (draggingElement && placeholder) {
        console.log('开始重排序操作');

        // 记录原始位置和目标位置
        const originalParent = draggingElement.parentNode;
        const targetParent = placeholder.parentNode;
        const nextSibling = placeholder.nextSibling;

        console.log('原始父元素:', originalParent?.id);
        console.log('目标父元素:', targetParent?.id);
        console.log('插入位置:', nextSibling?.dataset?.componentId || '末尾');

        // 移除占位符
        placeholder.remove();

        // 移动元素到新位置
        if (nextSibling) {
            targetParent.insertBefore(draggingElement, nextSibling);
        } else {
            targetParent.appendChild(draggingElement);
        }

        console.log('元素已移动到新位置');

        // 恢复拖拽元素样式
        draggingElement.classList.remove('dragging');
        draggingElement.style.opacity = '';

        // 重置位置记录
        lastPlaceholderPosition = null;

        // 更新显示顺序
        updateDisplayOrders();
        updateComponentsJsonField();

        console.log('=== 重排序完成 ===');
    } else {
        console.log('重排序失败：缺少必要元素');
        if (!draggingElement) console.log('- 找不到拖拽元素');
        if (!placeholder) console.log('- 找不到占位符');
    }

    // 清理所有占位符
    document.querySelectorAll('.drop-placeholder').forEach(p => p.remove());
    lastPlaceholderPosition = null;
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(e) {
    const componentList = document.getElementById('component-list');
    const rect = componentList.getBoundingClientRect();

    // 检查是否真的离开了容器
    if (e.clientX < rect.left || e.clientX > rect.right ||
        e.clientY < rect.top || e.clientY > rect.bottom) {
        document.querySelectorAll('.drop-placeholder').forEach(p => p.remove());
    }
}

/**
 * 获取拖拽后应插入的元素位置 - 简化版本
 */
function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.page-component:not(.dragging)')];

    return draggableElements.reduce((closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;

        if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
        } else {
            return closest;
        }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
}

/**
 * 初始化表单提交处理
 */
function initializeFormSubmission() {
    const pageForm = document.getElementById('page-form');
    if (!pageForm) return;

    pageForm.addEventListener('submit', function (e) {
        // 在提交前收集所有组件数据并更新隐藏字段
        updateComponentsJsonField();
    });
}

/**
 * 更新组件JSON隐藏字段
 */
function updateComponentsJsonField() {
    const componentsJsonField = document.getElementById('components-json');
    const componentList = document.getElementById('component-list');

    if (!componentsJsonField || !componentList) return;

    const components = [];
    const componentElements = componentList.querySelectorAll('.page-component');

    componentElements.forEach((element, index) => {
        const componentId = element.dataset.componentId;
        const template = element.dataset.template || 'Default';
        const visible = element.dataset.visible === 'true';
        const displayOrder = index; // 使用实际顺序
        const idNo = element.dataset.idNo;
        const columnSpan = element.dataset.columnSpan;

        // 获取组件参数
        const parametersPre = element.querySelector('pre');
        let parametersJson = '{}';
        if (parametersPre && parametersPre.textContent.trim()) {
            parametersJson = parametersPre.textContent.trim();
        }

        components.push({
            ComponentDefinitionId: componentId,
            TemplateKey: template,
            ColumnSpan: columnSpan,
            ParametersJson: parametersJson,
            DisplayOrder: displayOrder,
            IsVisible: visible,
            IdNo: idNo
        });
    });

    // 将组件数据序列化为JSON并设置到隐藏字段
    componentsJsonField.value = JSON.stringify(components);

    console.log('Components JSON updated:', components);
}

/**
 * 打开组件编辑器
 */
function openComponentEditor(componentElement) {
    const componentId = componentElement.dataset.componentId;
    const template = componentElement.dataset.template || 'Default';
    const modal = document.getElementById('component-modal');

    if (!modal) {
        console.error('Component modal not found');
        return;
    }

    // 显示模态框
    modal.classList.remove('hidden');

    // 获取组件数据并填充编辑器
    loadComponentEditor(componentId, template, componentElement);
}

/**
 * 切换组件可见性
 */
function toggleComponentVisibility(componentElement) {
    const isVisible = componentElement.dataset.visible === 'true';
    componentElement.dataset.visible = (!isVisible).toString();

    const visibilityBtn = componentElement.querySelector('.component-visibility-btn');
    const icon = visibilityBtn.querySelector('i');
    const preview = componentElement.querySelector('.p-6');

    if (!isVisible) {
        // 显示组件
        icon.className = 'fas fa-eye text-sm';
        if (preview) preview.classList.remove('opacity-50');
    } else {
        // 隐藏组件
        icon.className = 'fas fa-eye-slash text-sm';
        if (preview) preview.classList.add('opacity-50');
    }
}

/**
 * 加载组件编辑器
 */
function loadComponentEditor(componentId, template, componentElement) {
    const modal = document.getElementById('component-modal');
    const modalContent = modal.querySelector('.relative');

    // 构建编辑器HTML
    const editorHtml = `
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${getLocalizedText('editComponent', '编辑组件')}: ${componentId}</h3>
                <button type="button" class="close-modal-btn text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <!-- 模板选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">${getLocalizedText('template', '模板')}</label>
                    <select id="component-template" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="Default" ${template === 'Default' ? 'selected' : ''}>正在加载模板...</option>
                    </select>
                </div>
                
                <!-- 列跨度选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">${getLocalizedText('columnSpan', '列跨度')}</label>
                    <select id="component-column-span" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="12" ${componentElement.dataset.columnSpan === '12' ? 'selected' : ''}>12</option>
                        <option value="11" ${componentElement.dataset.columnSpan === '11' ? 'selected' : ''}>11</option>
                        <option value="10" ${componentElement.dataset.columnSpan === '10' ? 'selected' : ''}>10</option>
                        <option value="9" ${componentElement.dataset.columnSpan === '9' ? 'selected' : ''}>9</option>
                        <option value="8" ${componentElement.dataset.columnSpan === '8' ? 'selected' : ''}>8</option>
                        <option value="7" ${componentElement.dataset.columnSpan === '7' ? 'selected' : ''}>7</option>
                        <option value="6" ${componentElement.dataset.columnSpan === '6' ? 'selected' : ''}>6</option>
                        <option value="5" ${componentElement.dataset.columnSpan === '5' ? 'selected' : ''}>5</option>
                        <option value="4" ${componentElement.dataset.columnSpan === '4' ? 'selected' : ''}>4</option>
                        <option value="3" ${componentElement.dataset.columnSpan === '3' ? 'selected' : ''}>3</option>
                        <option value="2" ${componentElement.dataset.columnSpan === '2' ? 'selected' : ''}>2</option>
                        <option value="1" ${componentElement.dataset.columnSpan === '1' ? 'selected' : ''}>1</option>
                    </select>
                </div>
                
                <!-- 组件参数 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">${getLocalizedText('componentParametersJson', '组件参数 (JSON)')}</label>
                    <textarea id="component-parameters" rows="8" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm" placeholder="{}">{}</textarea>
                </div>
                
                <!-- 可见性设置 -->
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="component-visible" ${componentElement.dataset.visible === 'true' ? 'checked' : ''} class="mr-2 text-primary-600">
                        <span class="text-sm text-gray-700 dark:text-gray-300">${getLocalizedText('componentVisible', '组件可见')}</span>
                    </label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button type="button" class="cancel-edit-btn px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md">
                    ${getLocalizedText('cancel', '取消')}
                </button>
                <button type="button" class="save-component-btn px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md">
                    ${getLocalizedText('save', '保存')}
                </button>
            </div>
        </div>
    `;

    modalContent.innerHTML = editorHtml;

    // 加载模板选项到模态框
    loadModalComponentTemplates(componentId, template);

    // 绑定模态框事件
    bindModalEvents(componentElement);
}

/**
 * 加载模态框组件模板选项
 */
function loadModalComponentTemplates(componentId, currentTemplate) {
    const templateSelect = document.getElementById('component-template');
    if (!templateSelect) return;

    // 构建API URL（带语言前缀）
    const url = buildApiUrl(`/Admin/PageConfiguration/GetComponentTemplates/${componentId}`);

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.templates) {
                // 清空现有选项
                templateSelect.innerHTML = '';

                // 添加模板选项
                data.templates.forEach(template => {
                    const option = document.createElement('option');
                    option.value = template.id;
                    option.textContent = template.name;
                    option.title = template.description;

                    // 设置当前选中的模板
                    if (template.id === currentTemplate) {
                        option.selected = true;
                    }

                    templateSelect.appendChild(option);
                });

                console.log(`Loaded ${data.templates.length} templates for modal component: ${componentId}`);
            } else {
                console.warn('Failed to load templates for modal:', data.message);
                // 显示默认选项
                templateSelect.innerHTML = '<option value="Default">Default</option>';
            }
        })
        .catch(error => {
            console.error('Error loading modal component templates:', error);
            // 显示默认选项
            templateSelect.innerHTML = '<option value="Default">Default</option>';
        });
}

/**
 * 绑定模态框事件
 */
function bindModalEvents(componentElement) {
    const modal = document.getElementById('component-modal');

    // 关闭按钮
    const closeBtn = modal.querySelector('.close-modal-btn');
    const cancelBtn = modal.querySelector('.cancel-edit-btn');

    const closeModal = () => {
        modal.classList.add('hidden');
    };

    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (cancelBtn) cancelBtn.addEventListener('click', closeModal);

    // 背景点击关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });

    // 保存按钮
    const saveBtn = modal.querySelector('#save-component-btn');
    if (saveBtn) {
        saveBtn.addEventListener('click', () => {
            saveComponentChanges(componentElement);
            closeModal();
        });
    }
}

/**
 * 保存组件更改
 */
function saveComponentChanges(componentElement) {
    const template = document.getElementById('component-template').value;
    const parameters = document.getElementById('component-parameters').value;
    const visible = document.getElementById('component-visible').checked;
    const columnSpan = document.getElementById('component-column-span').value;

    // 更新组件数据
    componentElement.dataset.template = template;
    componentElement.dataset.visible = visible.toString();
    componentElement.dataset.columnSpan = columnSpan;

    // 更新显示
    const templateSpan = componentElement.querySelector('.ml-2.text-xs');
    if (templateSpan) {
        templateSpan.textContent = `(${template})`;
    }

    // 更新参数显示
    const parametersPre = componentElement.querySelector('pre');
    if (parametersPre) {
        try {
            const formattedJson = JSON.stringify(JSON.parse(parameters || '{}'), null, 2);
            parametersPre.textContent = formattedJson;
        } catch (e) {
            parametersPre.textContent = parameters || '{}';
        }
    }

    // 更新可见性显示
    const currentVisible = componentElement.dataset.visible === 'true';
    if (currentVisible !== visible) {
        toggleComponentVisibility(componentElement);
    }

    console.log('Component saved:', {
        id: componentElement.dataset.componentId,
        template,
        parameters,
        visible
    });

    // 更新JSON隐藏字段
    updateComponentsJsonField();
}

/**
 * 上移组件
 */
function moveComponentUp(componentElement) {
    const previousSibling = componentElement.previousElementSibling;
    if (previousSibling && previousSibling.classList.contains('component-item')) {
        componentElement.parentNode.insertBefore(componentElement, previousSibling);
        updateDisplayOrders();
    }
}

/**
 * 下移组件
 */
function moveComponentDown(componentElement) {
    const nextSibling = componentElement.nextElementSibling;
    if (nextSibling && nextSibling.classList.contains('component-item')) {
        componentElement.parentNode.insertBefore(nextSibling, componentElement);
        updateDisplayOrders();
    }
}

/**
 * 删除组件
 */
function removeComponent(componentElement) {
    // 检查是否是选中的组件
    const isSelected = componentElement.classList.contains('border-primary-600');

    componentElement.remove();
    updateDisplayOrders();
    updateEmptyState();

    // 如果删除的是选中的组件，清空属性面板
    if (isSelected) {
        clearPropertiesPanel();
    }

    // 更新JSON隐藏字段
    updateComponentsJsonField();
}

/**
 * 更新显示顺序
 */
function updateDisplayOrders() {
    const componentElements = document.querySelectorAll('.page-component');
    componentElements.forEach((element, index) => {
        // 更新data属性
        element.dataset.displayOrder = index;

        // 同时更新任何现有的表单输入（如果存在）
        const orderInput = element.querySelector('input[name*=".DisplayOrder"]');
        if (orderInput) {
            orderInput.value = index;
        }
    });
}

/**
 * 更新空状态显示
 */
function updateEmptyState() {
    const componentList = document.getElementById('component-list');
    const emptyState = document.getElementById('empty-state');

    if (!componentList || !emptyState) {
        return; // 如果容器不存在，直接返回
    }

    const componentElements = componentList.querySelectorAll('.page-component');

    if (componentElements.length === 0) {
        emptyState.classList.remove('hidden');
        componentList.classList.add('hidden');
    } else {
        emptyState.classList.add('hidden');
        componentList.classList.remove('hidden');
    }
}


/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    // 页面键自动生成（基于中文名称）
    const nameZhInput = document.querySelector('input[name="Name[zh]"]');
    const pageKeyInput = document.querySelector('input[name="PageKey"]');
    const routeInput = document.querySelector('input[name="Route"]');

    if (nameZhInput && pageKeyInput) {
        nameZhInput.addEventListener('input', function () {
            if (!pageKeyInput.value) {
                // 自动生成页面键（简单的拼音转换）
                const pageKey = generatePageKey(this.value);
                pageKeyInput.value = pageKey;

                // 自动生成路由
                if (!routeInput.value) {
                    routeInput.value = '/' + pageKey.toLowerCase();
                }
            }
        });
    }
}

/**
 * 生成页面键（简化版）
 */
function generatePageKey(chineseName) {
    // 简化实现：移除特殊字符，保留字母数字
    return chineseName
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/g, '_')
        .toLowerCase()
        .substring(0, 50);
}

/**
 * 初始化图片质量滑块
 */
function initializeImageQualitySlider() {
    const qualitySlider = document.querySelector('input[name="Performance.ImageQuality"]');
    if (qualitySlider) {
        qualitySlider.addEventListener('input', function () {
            const label = this.previousElementSibling;
            if (label) {
                label.innerHTML = label.innerHTML.replace(/\(\d+%\)/, `(${this.value}%)`);
            }
        });
    }
}

/**
 * 页面配置编辑器类
 */
class PageConfigurationEditor {
    constructor(options) {
        this.componentsData = options.componentsData || [];
        this.availableComponents = options.availableComponents || [];
        this.isNewPage = options.isNewPage || false;

        this.init();
    }

    init() {
        // 初始化现有的页面配置功能
        if (typeof initializePageConfiguration === 'function') {
            initializePageConfiguration();
        }

        // 初始化拖拽功能
        this.initializeDragAndDrop();

        // 初始化组件编辑
        this.initializeComponentEditing();
    }

    initializeDragAndDrop() {
        const componentItems = document.querySelectorAll('.component-item[data-component-id]');
        const pageCanvas = document.getElementById('page-canvas');
        const componentList = document.getElementById('component-list');
        const emptyState = document.getElementById('empty-state');

        if (!pageCanvas) return;

        // 为左侧组件库的组件添加拖拽事件
        componentItems.forEach(item => {
            item.draggable = true;

            item.addEventListener('dragstart', (e) => {
                const componentId = e.target.closest('.component-item').dataset.componentId;
                const componentName = e.target.closest('.component-item').dataset.componentName;

                e.dataTransfer.setData('text/plain', JSON.stringify({
                    componentId: componentId,
                    componentName: componentName,
                    source: 'library'
                }));

                e.dataTransfer.effectAllowed = 'copy';
                e.target.style.opacity = '0.5';
            });

            item.addEventListener('dragend', (e) => {
                e.target.style.opacity = '';
            });
        });

        // 为页面画布添加放置事件
        pageCanvas.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';

            // 只对组件库的拖拽显示视觉反馈
            const draggedLibraryItem = document.querySelector('.component-item[style*="opacity: 0.5"]');
            if (draggedLibraryItem) {
                pageCanvas.classList.add('bg-primary-50', 'border-primary-300');
            }
        });

        pageCanvas.addEventListener('dragleave', (e) => {
            // 检查是否真的离开了画布区域
            const rect = pageCanvas.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;

            if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                pageCanvas.classList.remove('bg-primary-50', 'border-primary-300');
            }
        });

        pageCanvas.addEventListener('drop', (e) => {
            e.preventDefault();
            pageCanvas.classList.remove('bg-primary-50', 'border-primary-300');

            try {
                const data = JSON.parse(e.dataTransfer.getData('text/plain'));
                if (data.source === 'library') {
                    this.addComponentToCanvas(data.componentId, data.componentName);
                }
            } catch (error) {
                console.error('Error parsing drag data:', error);
            }
        });

        console.log('Drag and drop functionality initialized');
    }

    initializeComponentEditing() {
        // TODO: 实现组件编辑功能
        console.log('Component editing functionality initialized');
    }

    addComponentToCanvas(componentId, componentName) {
        const pageCanvas = document.getElementById('page-canvas');
        const componentList = document.getElementById('component-list');
        const emptyState = document.getElementById('empty-state');

        if (!pageCanvas) {
            console.error('Page canvas not found');
            return;
        }

        // 隐藏空状态
        if (emptyState) {
            emptyState.classList.add('hidden');
        }

        // 显示组件列表
        if (componentList) {
            componentList.classList.remove('hidden');
        }

        // 创建新组件元素
        const newComponent = document.createElement('div');
        newComponent.className = 'page-component border-b border-gray-200 dark:border-gray-700 last:border-b-0 relative group';
        newComponent.dataset.componentId = componentId;
        newComponent.dataset.template = 'Default';
        newComponent.dataset.visible = 'true';
        newComponent.dataset.columnSpan = '12';

        // 获取当前最大显示顺序
        const existingComponents = componentList ? componentList.querySelectorAll('.page-component') : [];
        const maxDisplayOrder = existingComponents.length;
        newComponent.dataset.displayOrder = maxDisplayOrder;

        newComponent.innerHTML = `
            <!-- 组件工具栏 -->
            <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                <div class="flex items-center space-x-1 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 p-1">
                    <button type="button" class="component-edit-btn p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-blue-600 dark:text-blue-400" title="${getLocalizedText('editComponent', '编辑组件')}">
                        <i class="fas fa-edit text-sm"></i>
                    </button>
                    <button type="button" class="component-visibility-btn p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-600 dark:text-gray-400" title="${getLocalizedText('toggleVisibility', '切换可见性')}">
                        <i class="fas fa-eye text-sm"></i>
                    </button>
                    <button type="button" class="component-delete-btn p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-red-600 dark:text-red-400" title="${getLocalizedText('deleteComponent', '删除组件')}">
                        <i class="fas fa-trash text-sm"></i>
                    </button>
                    <div class="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
                    <button type="button" class="component-drag-handle p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-400 cursor-move" title="${getLocalizedText('dragToReorder', '拖拽排序')}">
                        <i class="fas fa-grip-vertical text-sm"></i>
                    </button>
                </div>
            </div>

            <!-- 组件预览 -->
            <div class="p-6">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <i class="fas fa-cube text-gray-400 mr-2"></i>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">${componentId}</span>
                        <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(Default)</span>
                        <span class="ml-2 text-xs text-blue-600 dark:text-blue-400">${getLocalizedText('columnSpan', '列跨度')}: 12</span>
                    </div>
                </div>
                
                <!-- 组件参数预览 -->
                <div class="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
                    <details>
                        <summary class="cursor-pointer text-gray-600 dark:text-gray-400">${getLocalizedText('componentParameters', '组件参数')}</summary>
                        <pre class="mt-1 text-gray-800 dark:text-gray-200 overflow-x-auto">{}</pre>
                    </details>
                </div>
            </div>
        `;

        // 添加到组件列表
        if (componentList) {
            componentList.appendChild(newComponent);
        } else {
            // 如果没有组件列表，创建一个
            const newComponentList = document.createElement('div');
            newComponentList.id = 'component-list';
            newComponentList.appendChild(newComponent);
            pageCanvas.appendChild(newComponentList);
        }

        // 为新组件添加事件监听器
        attachComponentEvents(newComponent, maxDisplayOrder);

        // 更新空状态显示
        updateEmptyState();

        console.log(`Added component: ${componentId}`);

        // 更新JSON隐藏字段
        updateComponentsJsonField();
    }

    updateEmptyState() {
        const componentList = document.getElementById('component-list');
        const emptyState = document.getElementById('empty-state');

        if (!componentList || !emptyState) return;

        const components = componentList.querySelectorAll('.page-component');

        if (components.length === 0) {
            emptyState.classList.remove('hidden');
            componentList.classList.add('hidden');
        } else {
            emptyState.classList.add('hidden');
            componentList.classList.remove('hidden');
        }
    }
}