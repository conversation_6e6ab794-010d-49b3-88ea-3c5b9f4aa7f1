﻿// 标签页加载状态追踪
const loadedTabs = new Set(['job-positions']); // 第一个标签已加载
// 标签页切换函数
async function switchTab(tabName) {
    // 更新 URL hash
    window.history.replaceState(null, null, '#' + tabName);

    // 检查是否已加载
    if (loadedTabs.has(tabName)) {
        MlSoftSites.switchTab(tabName);
        return;
    }

    // 懒加载内容
    if (tabName !== 'job-positions') {
        await loadTabContent(tabName);
    }

    MlSoftSites.switchTab(tabName);
}

// 懒加载标签页内容
async function loadTabContent(tabName) {
    const tabContainer = document.getElementById(`${tabName}-tab`);
    if (!tabContainer) return;

    MlSoftSites.showLoading(window.Resources?.Admin?.PleaseWaitProcessing);

    try {
        const response = await fetch(`${buildMultilingualUrl("tab", "Recruitment")}/${tabName}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        const result = await response.json();
        MlSoftSites.hideLoading();
        if (result.success) {
            tabContainer.innerHTML = result.html;
            loadedTabs.add(tabName);
            initializeTabScripts(tabName);
        } else {
            Dialog.error(result.message);
        }
    } catch (error) {
        console.error('Error loading tab:', error);
        Dialog.error(window.Resources?.Admin?.LoadTabError || 'Failed to load tab content');
    }
}



// 初始化标签页脚本
function initializeTabScripts(tabName) {
    if (tabName === 'employee-interviews') {
        // 初始化员工访谈相关脚本
        if (typeof initializeEmployeeInterviewsTab === 'function') {
            initializeEmployeeInterviewsTab();
        }
    } else if (tabName === 'statistics') {
        // 初始化统计页面相关脚本
        if (typeof initializeStatisticsTab === 'function') {
            initializeStatisticsTab();
        }
    }
}

// 初始化页面
function initializeRecruitmentPage() {
    const hash = window.location.hash.substring(1);
    const validTabs = ['job-positions', 'employee-interviews'];

    if (hash && validTabs.includes(hash)) {
        switchTab(hash);
    } else {
        switchTab('job-positions');
    }

    // 初始化TinyMCE编辑器
    if (window.AdminTinyMCE) {
        AdminTinyMCE.initAll('.tinymce-editor', {
            menubar: false,
            height: 200
        });
    }
}

// Modal语言标签页切换 - 使用通用系统
window.switchJobModalLanguageTab = function (lang) {
    return window.switchLanguageTab(lang, '#jobPositionModal', {
        buttonClass: 'job-modal-lang-tab-button',
        contentClass: 'job-modal-lang-content',
        contentIdPrefix: 'job-modal-lang-'
    });
};

// 职位管理相关函数
function openJobPositionModal() {
    document.getElementById('jobPositionModal').classList.remove('hidden');
    document.getElementById('jobModalTitle').textContent = window.Resources?.Admin?.CreateJobPosition || 'Create Job Position';
    document.getElementById('jobId').value = '';
    resetJobPositionForm();

    // 初始化第一个语言标签
    const firstLangBtn = document.querySelector('.job-modal-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#jobPositionModal', {
            buttonClass: 'job-modal-lang-tab-button',
            contentClass: 'job-modal-lang-content',
            contentIdPrefix: 'job-modal-lang-'
        });
    }

    // 确保 TinyMCE 编辑器在模态框显示后重新初始化
    setTimeout(() => {
        if (window.AdminTinyMCE) {
            AdminTinyMCE.initAll('.tinymce-editor', {
                menubar: false,
                height: 200
            });
        }
    }, 50);
}

function closeJobPositionModal() {
    document.getElementById('jobPositionModal').classList.add('hidden');
}

function resetJobPositionForm() {
    document.getElementById('jobPositionForm').reset();
    // 设置默认值
    document.getElementById('postDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('isActive').checked = true;
    document.getElementById('currency').value = 'JPY';
}

async function editJobPosition(id) {
    try {
        const response = await fetch(`${buildMultilingualUrl("job-position", "Recruitment")}/${id}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        const result = await response.json();

        if (result.success && result.data) {
            document.getElementById('jobModalTitle').textContent = window.Resources?.Admin?.EditJobPosition || 'Edit Job Position';
            document.getElementById('jobPositionModal').classList.remove('hidden');

            // 等待 TinyMCE 初始化完成后再填充数据
            setTimeout(() => {
                populateJobPositionForm(result.data);
            }, 100);
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.LoadJobError || 'Failed to load job position');
        }
    } catch (error) {
        console.error('Edit job position error:', error);
        Dialog.error(window.Resources?.Admin?.LoadJobError || 'Failed to load job position');
    }
}

function populateJobPositionForm(job) {
    document.getElementById('jobId').value = job.id || '';
    document.getElementById('jobType').value = job.type || 0;
    document.getElementById('employmentType').value = job.employmentType || 0;
    document.getElementById('experienceLevel').value = job.experienceLevel || 0;
    document.getElementById('salaryMin').value = job.salaryMin || '';
    document.getElementById('salaryMax').value = job.salaryMax || '';
    document.getElementById('currency').value = job.currency || 'JPY';
    document.getElementById('postDate').value = job.postDate ? job.postDate.split('T')[0] : '';
    document.getElementById('applicationDeadline').value = job.applicationDeadline ? job.applicationDeadline.split('T')[0] : '';
    document.getElementById('workingHours').value = job.workingHours || '';
    document.getElementById('probationPeriod').value = job.probationPeriod || '';

    document.getElementById('isActive').checked = job.isActive || false;
    document.getElementById('isFeatured').checked = job.isFeatured || false;

    // 填充多语言字段
    if (job.locale) {
        for (const [lang, fields] of Object.entries(job.locale)) {
            const elements = {
                jobTitle: document.getElementById(`jobTitle_${lang}`),
                jobDescription: document.getElementById(`jobDescription_${lang}`),
                requirements: document.getElementById(`requirements_${lang}`),
                benefits: document.getElementById(`benefits_${lang}`),
                workLocation: document.getElementById(`workLocation_${lang}`)
            };

            if (elements.jobTitle) elements.jobTitle.value = fields.jobTitle || '';

            // 设置 TinyMCE 编辑器内容
            if (elements.jobDescription) {
                if (typeof tinymce !== 'undefined' && tinymce.get(`jobDescription_${lang}`)) {
                    tinymce.get(`jobDescription_${lang}`).setContent(fields.jobDescription || '');
                } else {
                    elements.jobDescription.value = fields.jobDescription || '';
                }
            }

            if (elements.requirements) {
                if (typeof tinymce !== 'undefined' && tinymce.get(`requirements_${lang}`)) {
                    tinymce.get(`requirements_${lang}`).setContent(fields.requirements || '');
                } else {
                    elements.requirements.value = fields.requirements || '';
                }
            }
            if (elements.benefits) elements.benefits.value = fields.benefits || '';
            if (elements.workLocation) elements.workLocation.value = fields.workLocation || '';
        }
    }
}

async function submitJobPosition(event) {
    event.preventDefault();

    // 确保TinyMCE内容同步
    if (typeof tinymce !== 'undefined') {
        tinymce.triggerSave();
    }

    const formData = new FormData(event.target);
    const jobId = formData.get('jobId');
    const isEdit = jobId && jobId.trim() !== '';

    // 构建多语言字段
    const locale = window.collectMultilingualDataFromFormData(
        formData,
        ['jobTitle', 'jobDescription', 'requirements', 'benefits', 'workLocation'],
        window.SupportedLanguages
    );


    const data = {
        type: parseInt(formData.get('jobType')),
        employmentType: parseInt(formData.get('employmentType')),
        experienceLevel: parseInt(formData.get('experienceLevel')),
        salaryMin: formData.get('salaryMin') ? parseFloat(formData.get('salaryMin')) : null,
        salaryMax: formData.get('salaryMax') ? parseFloat(formData.get('salaryMax')) : null,
        currency: formData.get('currency'),
        postDate: formData.get('postDate'),
        applicationDeadline: formData.get('applicationDeadline') || null,
        workingHours: formData.get('workingHours'),
        probationPeriod: formData.get('probationPeriod'),
        isActive: formData.get('isActive') === 'on',
        isFeatured: formData.get('isFeatured') === 'on',
        locale: locale
    };

    try {
        const url = isEdit
            ? `${buildMultilingualUrl("job-position", "Recruitment")}/${jobId}`
            : buildMultilingualUrl("job-position", "Recruitment");

        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            Dialog.notify(result.message || window.Resources?.Admin?.SaveSuccess || 'Job position saved successfully', 'success');
            closeJobPositionModal();
            window.location.reload(); // 刷新页面以显示更新
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.SaveError || 'Failed to save job position');
        }
    } catch (error) {
        console.error('Submit job position error:', error);
        Dialog.error(window.Resources?.Admin?.SaveError || 'Failed to save job position');
    }
}

async function deleteJobPosition(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete || 'Are you sure you want to delete this job position?');
    if (confirmed) {
        try {
            const response = await fetch(`${buildMultilingualUrl("job-position", "Recruitment")}/${id}`, {
                method: 'DELETE',
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteSuccess || 'Job position deleted successfully', 'success');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteError || 'Failed to delete job position');
            }
        } catch (error) {
            console.error('Delete job position error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteError || 'Failed to delete job position');
        }
    }
}


// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeRecruitmentPage();

    // 处理浏览器前进后退
    window.addEventListener('hashchange', function () {
        initializeRecruitmentPage();
    });
});


function initializeEmployeeInterviewsTab() {
    // 初始化TinyMCE编辑器
    if (window.AdminTinyMCE) {
        AdminTinyMCE.initAll('.tinymce-editor', {
            menubar: false,
            height: 200
        });
    }

    // 初始化员工照片上传
    if (typeof window.__initEmployeePhotoUpload === 'function') {
        window.__initEmployeePhotoUpload();
    }
}

// Modal语言标签页切换 - 使用通用系统
window.switchInterviewModalLanguageTab = function (lang) {
    return window.switchLanguageTab(lang, '#employeeInterviewModal', {
        buttonClass: 'interview-modal-lang-tab-button',
        contentClass: 'interview-modal-lang-content',
        contentIdPrefix: 'interview-modal-lang-'
    });
};

// 员工访谈管理相关函数
function openEmployeeInterviewModal() {
    document.getElementById('employeeInterviewModal').classList.remove('hidden');
    document.getElementById('interviewModalTitle').textContent = window.Resources?.Admin?.CreateEmployeeInterview || 'Create Employee Interview';
    document.getElementById('interviewId').value = '';
    resetEmployeeInterviewForm();

    // 初始化第一个语言标签
    const firstLangBtn = document.querySelector('.interview-modal-lang-tab-button');
    if (firstLangBtn) {
        window.switchLanguageTab(firstLangBtn.getAttribute('data-lang'), '#employeeInterviewModal', {
            buttonClass: 'interview-modal-lang-tab-button',
            contentClass: 'interview-modal-lang-content',
            contentIdPrefix: 'interview-modal-lang-'
        });
    }

    // 重新初始化TinyMCE
    setTimeout(() => {
        if (window.AdminTinyMCE) {
            AdminTinyMCE.initAll('.tinymce-editor', {
                menubar: false,
                height: 200
            });
        }
        // 初始化图片上传
        if (typeof window.__initEmployeePhotoUpload === 'function') {
            window.__initEmployeePhotoUpload();
        }
    }, 100);
}

function closeEmployeeInterviewModal() {
    document.getElementById('employeeInterviewModal').classList.add('hidden');
}

function resetEmployeeInterviewForm() {
    document.getElementById('employeeInterviewForm').reset();
    // 设置默认值
    document.getElementById('interviewDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('yearsOfService').value = 1;
    document.getElementById('displayOrder').value = 0;

    // 清空并初始化照片上传
    const hidden = document.getElementById('photoUrl');
    if (hidden) hidden.value = '';
    if (typeof window.__initEmployeePhotoUpload === 'function') {
        window.__initEmployeePhotoUpload();
    }
}

async function editEmployeeInterview(id) {
    try {
        const response = await fetch(`${buildMultilingualUrl("employee-interview", "Recruitment")}/${id}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        const result = await response.json();

        if (result.success && result.data) {
            populateEmployeeInterviewForm(result.data);
            document.getElementById('interviewModalTitle').textContent = window.Resources?.Admin?.EditEmployeeInterview || 'Edit Employee Interview';
            document.getElementById('employeeInterviewModal').classList.remove('hidden');
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.LoadInterviewError || 'Failed to load employee interview');
        }
    } catch (error) {
        console.error('Edit employee interview error:', error);
        Dialog.error(window.Resources?.Admin?.LoadInterviewError || 'Failed to load employee interview');
    }
}

function populateEmployeeInterviewForm(interview) {
    document.getElementById('interviewId').value = interview.id || '';
    document.getElementById('departmentId').value = interview.departmentId || '';
    document.getElementById('interviewType').value = interview.interviewType || 0;
    document.getElementById('interviewStatus').value = interview.status || 0;
    document.getElementById('yearsOfService').value = interview.yearsOfService || 1;
    document.getElementById('interviewDate').value = interview.interviewDate ? interview.interviewDate.split('T')[0] : '';
    document.getElementById('displayOrder').value = interview.displayOrder || 0;
    document.getElementById('photoUrl').value = interview.photoUrl || '';
    document.getElementById('isInterviewFeatured').checked = interview.isFeatured || false;

    // 初始化/刷新图片上传（显示已有图片）
    if (typeof window.__initEmployeePhotoUpload === 'function') {
        window.__initEmployeePhotoUpload();
    }

    // 填充多语言字段
    if (interview.locale) {
        console.log('Interview locale structure:', interview.locale);

        for (const [lang, fields] of Object.entries(interview.locale)) {
            // Check if fields is an object and not null
            if (fields && typeof fields === 'object') {
                const elements = {
                    employeeName: document.getElementById(`employeeName_${lang}`),
                    position: document.getElementById(`position_${lang}`),
                    interviewContent: document.getElementById(`interviewContent_${lang}`),
                    workDescription: document.getElementById(`workDescription_${lang}`),
                    companyImpression: document.getElementById(`companyImpression_${lang}`)
                };

                //    if (elements.interviewContent) elements.interviewContent.value = fields.interviewContent || '';
                // 设置 TinyMCE 编辑器内容
                if (elements.interviewContent) {
                    if (typeof tinymce !== 'undefined' && tinymce.get(`interviewContent_${lang}`)) {
                        tinymce.get(`interviewContent_${lang}`).setContent(fields.interviewContent || '');
                    } else {
                        elements.interviewContent.value = fields.interviewContent || '';
                    }
                }

                if (elements.employeeName) elements.employeeName.value = fields.employeeName || '';
                if (elements.position) elements.position.value = fields.position || '';
            
                if (elements.workDescription) elements.workDescription.value = fields.workDescription || '';
                if (elements.companyImpression) elements.companyImpression.value = fields.companyImpression || '';
            } else {
                console.warn(`Invalid locale fields for language ${lang}:`, fields);
            }
        }
    }
}

async function submitEmployeeInterview(event) {
    event.preventDefault();

    // 确保TinyMCE内容同步
    if (typeof tinymce !== 'undefined') {
        tinymce.triggerSave();
    }

    const formData = new FormData(event.target);
    const interviewId = formData.get('interviewId');
    const isEdit = interviewId && interviewId.trim() !== '';

    // 构建多语言字段
    const locale =  window.collectMultilingualDataFromFormData(
        formData,
        ['employeeName', 'position', 'interviewContent', 'workDescription','companyImpression'],
        window.SupportedLanguages
    );



    const data = {
        interviewType: parseInt(formData.get('interviewType')),
        status: parseInt(formData.get('interviewStatus')),
        yearsOfService: parseInt(formData.get('yearsOfService')) || 1,
        interviewDate: formData.get('interviewDate'),
        displayOrder: parseInt(formData.get('displayOrder')) || 0,
        departmentId: formData.get('departmentId'),
        photoUrl: formData.get('photoUrl'),
        isFeatured: formData.get('isInterviewFeatured') === 'on',
        locale: locale
    };

    try {
        const url = isEdit
            ? `${buildMultilingualUrl("employee-interview", "Recruitment")}/${interviewId}`
            : buildMultilingualUrl("employee-interview", "Recruitment");

        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            Dialog.notify(result.message || window.Resources?.Admin?.SaveSuccess || 'Employee interview saved successfully', 'success');
            closeEmployeeInterviewModal();
            window.location.reload(); // 刷新页面以显示更新
        } else {
            Dialog.error(result.message || window.Resources?.Admin?.SaveError || 'Failed to save employee interview');
        }
    } catch (error) {
        console.error('Submit employee interview error:', error);
        Dialog.error(window.Resources?.Admin?.SaveError || 'Failed to save employee interview');
    }
}

async function deleteEmployeeInterview(id) {
    const confirmed = await Dialog.confirm(window.Resources?.Admin?.ConfirmDelete || 'Are you sure you want to delete this employee interview?');
    if (confirmed) {
        try {
            const response = await fetch(`${buildMultilingualUrl("employee-interview", "Recruitment")}/${id}`, {
                method: 'DELETE',
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            });

            const result = await response.json();

            if (result.success) {
                Dialog.notify(result.message || window.Resources?.Admin?.DeleteSuccess || 'Employee interview deleted successfully', 'success');
                window.location.reload();
            } else {
                Dialog.error(result.message || window.Resources?.Admin?.DeleteError || 'Failed to delete employee interview');
            }
        } catch (error) {
            console.error('Delete employee interview error:', error);
            Dialog.error(window.Resources?.Admin?.DeleteError || 'Failed to delete employee interview');
        }
    }
}

// 员工访谈 - 照片上传初始化
window.__initEmployeePhotoUpload = function () {
    var container = document.getElementById('employeePhotoUpload');
    if (!container || !window.SimpleImageUpload) return;

    // 读取已有值，供预览
    var hidden = document.getElementById('photoUrl');
    var initialUrl = hidden && hidden.value ? hidden.value : '';

    // 渲染上传控件（每次调用会重绘容器）
    window.__employeePhotoUploader = window.SimpleImageUpload({
        container: container,
        name: 'employeePhoto',
        value: initialUrl,
        autoUpload: true,
        uploadUrl: '/api/FileUpload/upload',
        folder: 'employee-interviews',
        allowedTypes: 'image/*',
        maxSize: '5MB',
        hiddenInputSelector: '#photoUrl',
        uploadText: (window.Resources?.Admin?.ClickToUpload || 'Click to upload'),
        onUploaded: function (info) {
            if (info && info.result) {
                var uploadedPath = (info.result.filePath || info.result.url || '');
                var hiddenEl = document.querySelector('#photoUrl');
                if (hiddenEl) hiddenEl.value = uploadedPath || '';
            }
            if (typeof Dialog !== 'undefined' && Dialog.notify) {
                Dialog.notify((window.Resources?.Shared?.FileUpload_UploadSuccess) || 'Upload success', 'success');
            }
        },
        onError: function (err) {
            console.error('Employee photo upload error:', err);
            if (typeof Dialog !== 'undefined' && Dialog.error) {
                Dialog.error((window.Resources?.Shared?.FileUpload_UploadError) || 'Upload failed');
            }
        }
    });
};
