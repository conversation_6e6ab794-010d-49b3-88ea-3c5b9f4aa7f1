// MlSoft Sites - Custom JavaScript (No jQuery)

// Dark Mode initialization function - can be called from head
window.DarkModeInit = {
    // Initialize dark mode before page renders to prevent flash
    init: function() {
        // Check for saved theme preference in localStorage
        const savedTheme = localStorage.getItem('theme');
        
        // Check system theme preference
        const systemPrefersDark = window.matchMedia && 
            window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // Determine theme to use: user preference > system preference > default light
        const shouldUseDark = savedTheme === 'dark' || 
            (!savedTheme && systemPrefersDark);
        
        // Apply dark class immediately to prevent flash
        if (shouldUseDark) {
            document.documentElement.classList.add('dark');
        }
        
        // Store the detected/applied theme for later use
        window.__INITIAL_THEME__ = shouldUseDark ? 'dark' : 'light';
    }
};

// Utility functions
const Utils = {
    // DOM ready function (replaces $(document).ready())
    ready: function(fn) {
        if (document.readyState !== 'loading') {
            fn();
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    },

    // AJAX helper function (replaces $.ajax())
    ajax: function(options) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        const config = Object.assign({}, defaults, options);

        return fetch(config.url, {
            method: config.method,
            headers: config.headers,
            body: config.data ? JSON.stringify(config.data) : null
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },

    // Show/hide elements
    show: function(element) {
        element.style.display = 'block';
    },

    hide: function(element) {
        element.style.display = 'none';
    },

    // Toggle element visibility
    toggle: function(element) {
        if (element.style.display === 'none') {
            this.show(element);
        } else {
            this.hide(element);
        }
    },

    // Add/remove classes
    addClass: function(element, className) {
        element.classList.add(className);
    },

    removeClass: function(element, className) {
        element.classList.remove(className);
    },

    toggleClass: function(element, className) {
        element.classList.toggle(className);
    },

    // Get/set data attributes
    getData: function(element, key) {
        return element.dataset[key];
    },

    setData: function(element, key, value) {
        element.dataset[key] = value;
    }
};

// Main application object
const MlSoftSites = {
    // Initialize the application
    init: function() {
        this.initNavigation();
        this.initForms();
        this.initAnimations();
        this.initLanguageSwitcher();
        this.initDarkMode();
        this.initBackToTop();

        // Only initialize parallax if there are parallax elements on the page
        if (document.querySelector('[data-parallax="true"]')) {
            this.initParallax();
        }
    },

    // Navigation functionality
    initNavigation: function() {
        // Mobile menu toggle (handled by Flowbite)
        // Additional navigation logic can be added here
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Active navigation highlighting
        this.highlightActiveNav();
    },

    // Highlight active navigation item
    highlightActiveNav: function() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('nav a[asp-controller]');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href)) {
                Utils.addClass(link, 'text-primary-700');
                Utils.addClass(link, 'font-semibold');
            }
        });
    },

    // Form handling
    initForms: function() {
        // Reserved for future form functionality
    },

    // Initialize animations
    initAnimations: function() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    Utils.addClass(entry.target, 'fade-in-up');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements with animation class
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    },

    // Dark mode functionality
    initDarkMode: function() {
        // Theme has already been set by the inline script in <head>
        // We just need to initialize the UI and event handlers
        
        // Update icon states based on current theme
        this.updateDarkModeIcons();

        // Add click event listener to dark mode toggle button
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', () => {
                this.toggleDarkMode();
            });
        }

        // Listen for system theme changes (only if user hasn't set a preference)
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addListener((e) => {
            // Only auto-switch if user hasn't manually set a preference
            if (!localStorage.getItem('theme')) {
                const newTheme = e.matches ? 'dark' : 'light';
                this.setTheme(newTheme, false); // false = don't save to localStorage
            }
        });
    },

    // Toggle between light and dark modes
    toggleDarkMode: function() {
        const html = document.documentElement;
        const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        this.setTheme(newTheme, true); // true = save to localStorage
    },

    // Set theme (light or dark)
    setTheme: function(theme, saveToStorage = true) {
        const html = document.documentElement;

        if (theme === 'dark') {
            Utils.addClass(html, 'dark');
        } else {
            Utils.removeClass(html, 'dark');
        }

        // Update icons
        this.updateDarkModeIcons();
        
        // Update theme-color meta tag for mobile browsers
        this.updateThemeColorMeta(theme);
        
        // Save to localStorage if requested (user manual change)
        if (saveToStorage) {
            localStorage.setItem('theme', theme);
        }
    },

    // Update dark mode toggle icons based on current theme
    updateDarkModeIcons: function() {
        const html = document.documentElement;
        const isDark = html.classList.contains('dark');
        const sunIcon = document.getElementById('sunIcon');
        const moonIcon = document.getElementById('moonIcon');

        if (sunIcon && moonIcon) {
            if (isDark) {
                Utils.addClass(sunIcon, 'hidden');
                Utils.removeClass(moonIcon, 'hidden');
            } else {
                Utils.removeClass(sunIcon, 'hidden');
                Utils.addClass(moonIcon, 'hidden');
            }
        }
    },

    // Update theme-color meta tag based on current theme
    updateThemeColorMeta: function(theme) {
        let themeColorMeta = document.querySelector('meta[name="theme-color"]');
        if (!themeColorMeta) {
            themeColorMeta = document.createElement('meta');
            themeColorMeta.name = 'theme-color';
            document.head.appendChild(themeColorMeta);
        }

        // Set theme color based on active theme CSS variables
        if (theme === 'dark') {
            // Use dark mode background color
            themeColorMeta.content = getComputedStyle(document.documentElement).getPropertyValue('--bg-primary').trim() || '#0f172a';
        } else {
            // Use light mode background color  
            themeColorMeta.content = getComputedStyle(document.documentElement).getPropertyValue('--bg-primary').trim() || '#ffffff';
        }
    },

    // Language switcher
    initLanguageSwitcher: function() {
        const langSwitcher = document.querySelector('#language-switcher');
        if (langSwitcher) {
            langSwitcher.addEventListener('change', function() {
                const selectedLang = this.value;
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('lang', selectedLang);
                window.location.href = currentUrl.toString();
            });
        }
    },

    // Tab switching utility
    switchTab: function(tabName, options = {}) {
        const defaults = {
            tabClass: 'tab-content',
            buttonClass: 'tab-button',
            activeClasses: ['active', 'border-primary-500', 'text-primary-600'],
            inactiveClasses: ['border-transparent', 'text-gray-500']
        };
        
        const config = Object.assign({}, defaults, options);
        
        // Hide all tabs
        document.querySelectorAll(`.${config.tabClass}`).forEach(tab => {
            Utils.addClass(tab, 'hidden');
        });

        // Show selected tab
        const selectedTab = document.getElementById(tabName + '-tab');
        if (selectedTab) {
            Utils.removeClass(selectedTab, 'hidden');
        }

        // Update tab buttons
        document.querySelectorAll(`.${config.buttonClass}`).forEach(button => {
            // Remove active classes
            config.activeClasses.forEach(cls => Utils.removeClass(button, cls));
            // Add inactive classes
            config.inactiveClasses.forEach(cls => Utils.addClass(button, cls));
        });

        // Activate selected button
        const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeButton) {
            // Remove inactive classes
            config.inactiveClasses.forEach(cls => Utils.removeClass(activeButton, cls));
            // Add active classes
            config.activeClasses.forEach(cls => Utils.addClass(activeButton, cls));
        }
    },

    // File upload utility
    uploadFile: function(file, url, options = {}) {
        const defaults = {
            uploadingMessage: 'Processing...',
            successMessage: 'Upload completed',
            errorMessage: 'Upload failed',
            showLoading: true,
            showResult: true
        };
        
        const config = Object.assign({}, defaults, options);
        
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', file);
            
            // Add additional form data
            if (config.additionalData) {
                Object.entries(config.additionalData).forEach(([key, value]) => {
                    formData.append(key, value);
                });
            }

            if (config.showLoading) {
                this.showLoading(config.uploadingMessage);
            }

            fetch(url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (config.showLoading) {
                    this.hideLoading();
                }
                
                if (result.success) {
                    if (config.showResult) {
                        Dialog.notify(config.successMessage, 'success');
                    }
                    resolve(result);
                } else {
                    if (config.showResult) {
                        Dialog.alert(result.message || config.errorMessage, null, 'error');
                    }
                    reject(new Error(result.message || config.errorMessage));
                }
            })
            .catch(error => {
                if (config.showLoading) {
                    this.hideLoading();
                }
                if (config.showResult) {
                    Dialog.alert(config.errorMessage, null, 'error');
                }
                reject(error);
            });
        });
    },

    // Universal alert system - replaced with Dialog system
    showAlert: function(message, type = 'success', options = {}) {
        const defaults = {
            duration: 5000,
            position: 'toast', // 'toast' for notification style, 'inline' for inline alert (not used anymore)
            container: null // specific container for inline alerts (not used anymore)
        };
        
        const config = Object.assign({}, defaults, options);
        
        // Use Dialog system based on success/error type
        if (type === 'success') {
            return Dialog.notify(message, 'success', config.duration);
        } else {
            return Dialog.alert(message, null, 'error');
        }
    },

    // Universal loading overlay with customizable message
    showLoading: function(message = 'Loading...') {
        // Remove existing overlay if any
        this.hideLoading();
        
        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.innerHTML = `
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg text-center max-w-sm mx-4">
                <div class="spinner mx-auto mb-4"></div>
                <p class="text-gray-600 dark:text-gray-300">${message}</p>
            </div>
        `;
        document.body.appendChild(overlay);
    },

    hideLoading: function() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    },

    // Parallax scrolling functionality
    initParallax: function() {
        const parallaxElements = document.querySelectorAll('[data-parallax="true"] .parallax-background');
        
        if (parallaxElements.length === 0) {
            return;
        }

        // Check if user prefers reduced motion
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        if (prefersReducedMotion) {
            return;
        }

        // Disable on mobile devices for better performance
        const isMobile = window.innerWidth <= 768;
        if (isMobile) {
            return;
        }

        let ticking = false;

        const updateParallax = () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            parallaxElements.forEach(element => {
                const container = element.closest('[data-parallax="true"]');
                if (!container) return;

                // Get container position relative to viewport
                const containerRect = container.getBoundingClientRect();
                const containerTop = containerRect.top + scrollTop;
                const containerHeight = containerRect.height;
                const windowHeight = window.innerHeight;
                
                // Only apply parallax if element is in or near viewport
                if (containerRect.bottom >= 0 && containerRect.top <= windowHeight) {
                    const speed = parseFloat(element.dataset.speed) || 0.5;
                    
                    // Calculate parallax offset
                    const yPos = (scrollTop - containerTop) * speed;
                    
                    // Apply transform with GPU acceleration
                    element.style.transform = `translate3d(0, ${yPos}px, 0)`;
                }
            });

            ticking = false;
        };

        const onScroll = () => {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        };

        // Use passive scroll listener for better performance
        window.addEventListener('scroll', onScroll, { passive: true });

        // Handle window resize
        window.addEventListener('resize', () => {
            const newIsMobile = window.innerWidth <= 768;
            if (newIsMobile && !isMobile) {
                // Reset transforms when switching to mobile
                parallaxElements.forEach(element => {
                    element.style.transform = '';
                });
            }
        }, { passive: true });

        // Initial call
        updateParallax();
    },

    initBackToTop: function() {
        const backToTopButton = document.getElementById('backToTop');
        if (!backToTopButton) return;

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.remove('opacity-100', 'visible');
                backToTopButton.classList.add('opacity-0', 'invisible');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

};

// Custom Dialog System - Replacement for native alert() and confirm()
const Dialog = {
    // Initialize dialog resources (should be called after page load with localized strings)
    init: function(resources = {}) {
        this.resources = Object.assign({
            // Default fallback values
            alert: 'Alert',
            warning: 'Warning', 
            error: 'Error',
            information: 'Information',
            question: 'Question',
            confirm: 'Confirm',
            ok: 'OK',
            cancel: 'Cancel',
            yes: 'Yes',
            no: 'No'
        }, resources);
    },

    // Get localized text with fallback
    getLocalizedText: function(key, fallback = '') {
        return this.resources && this.resources[key] ? this.resources[key] : fallback;
    },

    // Create dialog modal structure
    createDialog: function(options = {}) {
        const defaults = {
            title: this.getLocalizedText('alert', 'Alert'),
            message: '',
            type: 'info', // info, success, warning, error, question
            buttons: [{ text: this.getLocalizedText('ok', 'OK'), value: true, primary: true }],
            closeOnBackdrop: false,
            showIcon: true
        };

        const config = Object.assign({}, defaults, options);
        
        // Create modal backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'dialog-backdrop fixed inset-0 bg-gray-900/50 dark:bg-black/70 flex items-center justify-center z-50 transition-opacity duration-200';
        backdrop.style.opacity = '0';

        // Create modal content
        const modal = document.createElement('div');
        modal.className = 'dialog-modal bg-white dark:bg-gray-800 rounded-lg shadow-xl transform scale-95 transition-transform duration-200 max-w-md w-full mx-4';

        // Icon mapping
        const iconMap = {
            info: '<i class="fas fa-info-circle text-primary-500 text-2xl"></i>',
            success: '<i class="fas fa-check-circle text-green-600 text-2xl"></i>',
            warning: '<i class="fas fa-exclamation-triangle text-orange-500 text-2xl"></i>',
            error: '<i class="fas fa-times-circle text-red-600 text-2xl"></i>',
            question: '<i class="fas fa-question-circle text-primary-500 text-2xl"></i>'
        };

        const icon = config.showIcon && iconMap[config.type] ? `<div class="flex-shrink-0 mr-4">${iconMap[config.type]}</div>` : '';

        // Build buttons HTML
        const buttonsHtml = config.buttons.map((button, index) => {
            const isPrimary = button.primary === true;
            const baseClasses = 'px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
            const primaryClasses = 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500';
            const secondaryClasses = 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 focus:ring-gray-500 dark:focus:ring-gray-400';
            
            return `<button type="button" class="dialog-button ${baseClasses} ${isPrimary ? primaryClasses : secondaryClasses}" data-value="${button.value}" data-index="${index}">
                ${button.text}
            </button>`;
        }).join('');

        modal.innerHTML = `
            <div class="p-6">
                <!-- Header -->
                <div class="flex items-start mb-4">
                    ${icon}
                    <div class="flex-grow">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">${config.title}</h3>
                        <div class="text-gray-600 dark:text-gray-300">${config.message}</div>
                    </div>
                </div>
                
                <!-- Buttons -->
                <div class="flex justify-end space-x-3 mt-6">
                    ${buttonsHtml}
                </div>
            </div>
        `;

        backdrop.appendChild(modal);
        
        return { backdrop, modal, config };
    },

    // Show dialog and return promise
    show: function(options = {}) {
        return new Promise((resolve) => {
            const { backdrop, modal, config } = this.createDialog(options);
            
            // Add to DOM
            document.body.appendChild(backdrop);
            
            // Trigger animation
            requestAnimationFrame(() => {
                backdrop.style.opacity = '1';
                modal.style.transform = 'scale(1)';
            });

            // Button event handlers
            const buttons = modal.querySelectorAll('.dialog-button');
            buttons.forEach((button) => {
                button.addEventListener('click', () => {
                    const value = button.dataset.value;
                    const result = value === 'true' ? true : (value === 'false' ? false : value);
                    this.close(backdrop, () => resolve(result));
                });
            });

            // Backdrop click handler
            if (config.closeOnBackdrop) {
                backdrop.addEventListener('click', (e) => {
                    if (e.target === backdrop) {
                        this.close(backdrop, () => resolve(false));
                    }
                });
            }

            // Keyboard handlers
            const handleKeyPress = (e) => {
                if (e.key === 'Escape' && config.closeOnBackdrop) {
                    document.removeEventListener('keydown', handleKeyPress);
                    this.close(backdrop, () => resolve(false));
                } else if (e.key === 'Enter') {
                    // Find primary button or first button
                    const primaryButton = [...buttons].find(btn => btn.classList.contains('bg-primary-600'));
                    const targetButton = primaryButton || buttons[0];
                    if (targetButton) {
                        document.removeEventListener('keydown', handleKeyPress);
                        targetButton.click();
                    }
                }
            };
            
            document.addEventListener('keydown', handleKeyPress);
        });
    },

    // Close dialog with animation
    close: function(backdrop, callback) {
        const modal = backdrop.querySelector('.dialog-modal');
        
        backdrop.style.opacity = '0';
        modal.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            if (backdrop.parentNode) {
                backdrop.remove();
            }
            if (callback) callback();
        }, 200);
    },

    // Alert replacement - shows message with OK button
    alert: function(message, title, type = 'info') {
        const alertTitle = title || this.getLocalizedText('alert', 'Alert');
        
        return this.show({
            title: alertTitle,
            message: message,
            type: type,
            buttons: [{ 
                text: this.getLocalizedText('ok', 'OK'), 
                value: true, 
                primary: true 
            }],
            closeOnBackdrop: true
        });
    },

    // Confirm replacement - shows message with Yes/No or OK/Cancel buttons
    confirm: function(message, title, options = {}) {
        const defaults = {
            type: 'question',
            useYesNo: false, // if true, uses Yes/No instead of OK/Cancel
            closeOnBackdrop: false
        };
        
        const config = Object.assign({}, defaults, options);
        const confirmTitle = title || this.getLocalizedText('question', 'Question');
        
        const buttons = config.useYesNo ? [
            { text: this.getLocalizedText('no', 'No'), value: false, primary: false },
            { text: this.getLocalizedText('yes', 'Yes'), value: true, primary: true }
        ] : [
            { text: this.getLocalizedText('cancel', 'Cancel'), value: false, primary: false },
            { text: this.getLocalizedText('ok', 'OK'), value: true, primary: true }
        ];

        return this.show({
            title: confirmTitle,
            message: message,
            type: config.type,
            buttons: buttons,
            closeOnBackdrop: config.closeOnBackdrop
        });
    },

    // Warning dialog
    warning: function(message, title) {
        const warningTitle = title || this.getLocalizedText('warning', 'Warning');
        return this.alert(message, warningTitle, 'warning');
    },

    // Error dialog  
    error: function(message, title) {
        const errorTitle = title || this.getLocalizedText('error', 'Error');
        return this.alert(message, errorTitle, 'error');
    },

    // Success dialog
    success: function(message, title) {
        const successTitle = title || this.getLocalizedText('information', 'Information');
        return this.alert(message, successTitle, 'success');
    },

    // Info dialog
    info: function(message, title) {
        const infoTitle = title || this.getLocalizedText('information', 'Information');
        return this.alert(message, infoTitle, 'info');
    },

    // Notify - bottom-right notification with auto-dismiss
    notify: function(message, type = 'success', duration = 3000) {
        // Create notification container if it doesn't exist
        let notificationContainer = document.getElementById('notification-container');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notification-container';
            notificationContainer.className = 'fixed bottom-4 right-4 space-y-2 z-50 max-w-sm';
            document.body.appendChild(notificationContainer);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification transform translate-x-full transition-transform duration-300 ease-out';
        
        // Icon mapping for notifications
        const notifyIconMap = {
            success: '<i class="fas fa-check-circle text-green-500"></i>',
            info: '<i class="fas fa-info-circle text-primary-500"></i>',
            warning: '<i class="fas fa-exclamation-triangle text-orange-500"></i>',
            error: '<i class="fas fa-times-circle text-red-500"></i>'
        };

        // Style mapping for notifications
        const notifyStyleMap = {
            success: 'bg-white dark:bg-gray-800 border border-green-300 dark:border-green-600 shadow-lg',
            info: 'bg-white dark:bg-gray-800 border border-primary-300 dark:border-primary-600 shadow-lg',
            warning: 'bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-600 shadow-lg',
            error: 'bg-white dark:bg-gray-800 border border-red-300 dark:border-red-600 shadow-lg'
        };

        const icon = notifyIconMap[type] || notifyIconMap.success;
        const style = notifyStyleMap[type] || notifyStyleMap.success;

        notification.className += ` ${style} rounded-lg p-4 flex items-center space-x-3`;

        notification.innerHTML = `
            <div class="flex-shrink-0">
                ${icon}
            </div>
            <div class="flex-grow">
                <p class="text-sm font-medium text-gray-900 dark:text-white">${message}</p>
            </div>
            <div class="flex-shrink-0">
                <button type="button" class="notification-close text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none" aria-label="Close">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        `;

        // Add to container
        notificationContainer.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.classList.remove('translate-x-full');
        });

        // Auto-dismiss functionality
        const dismiss = () => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
                // Remove container if empty
                if (notificationContainer.children.length === 0) {
                    notificationContainer.remove();
                }
            }, 300);
        };

        // Close button handler
        const closeButton = notification.querySelector('.notification-close');
        closeButton.addEventListener('click', dismiss);

        // Auto-dismiss after specified duration
        if (duration > 0) {
            setTimeout(dismiss, duration);
        }

        return notification;
    }
};

// Initialize when DOM is ready
Utils.ready(function() {
    MlSoftSites.init();
});

// Export for global access
window.MlSoftSites = MlSoftSites;
window.Utils = Utils;
window.Dialog = Dialog;