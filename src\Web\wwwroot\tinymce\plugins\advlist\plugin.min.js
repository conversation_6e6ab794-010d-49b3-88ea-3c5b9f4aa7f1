!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager");const e=(t,e,r)=>{const s="UL"===e?"InsertUnorderedList":"InsertOrderedList";t.execCommand(s,!1,!1===r?null:{"list-style-type":r})},r=t=>e=>e.options.get(t),s=r("advlist_number_styles"),n=r("advlist_bullet_styles"),l=t=>null==t,o=t=>!l(t);class i{constructor(t,e){this.tag=t,this.value=e}static some(t){return new i(!0,t)}static none(){return i.singletonNone}fold(t,e){return this.tag?e(this.value):t()}isSome(){return this.tag}isNone(){return!this.tag}map(t){return this.tag?i.some(t(this.value)):i.none()}bind(t){return this.tag?t(this.value):i.none()}exists(t){return this.tag&&t(this.value)}forall(t){return!this.tag||t(this.value)}filter(t){return!this.tag||t(this.value)?this:i.none()}getOr(t){return this.tag?this.value:t}or(t){return this.tag?this:t}getOrThunk(t){return this.tag?this.value:t()}orThunk(t){return this.tag?this:t()}getOrDie(t){if(this.tag)return this.value;throw new Error(null!=t?t:"Called getOrDie on None")}static from(t){return o(t)?i.some(t):i.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(t){this.tag&&t(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}i.singletonNone=new i(!1);const a=Array.prototype.indexOf,u=Object.keys;var d=tinymce.util.Tools.resolve("tinymce.util.Tools");const c=t=>e=>o(e)&&t.test(e.nodeName),h=c(/^(OL|UL|DL)$/),g=c(/^(TH|TD)$/),p=t=>l(t)||"default"===t?"":t,m=(t,e)=>r=>((t,e)=>{const r=t.selection.getNode();return e({parents:t.dom.getParents(r),element:r}),t.on("NodeChange",e),()=>t.off("NodeChange",e)})(t,(s=>((t,s)=>{const n=t.selection.getStart(!0);r.setActive(((t,e,r)=>((t,e,r)=>{for(let e=0,n=t.length;e<n;e++){const n=t[e];if(h(s=n)&&!/\btox\-/.test(s.className))return i.some(n);if(r(n,e))break}var s;return i.none()})(e,0,g).exists((e=>e.nodeName===r&&((t,e)=>t.dom.isChildOf(e,t.getBody()))(t,e))))(t,s,e)),r.setEnabled(!((t,e)=>{const r=t.dom.getParent(e,"ol,ul,dl");return((t,e)=>null!==e&&!t.dom.isEditable(e))(t,r)||!t.selection.isEditable()})(t,n))})(t,s.parents))),v=(t,r,s,n,l,o)=>{const c={"lower-latin":"lower-alpha","upper-latin":"upper-alpha","lower-alpha":"lower-latin","upper-alpha":"upper-latin"},h=(g=t=>{return e=o,r=t,a.call(e,r)>-1;var e,r},((t,e)=>{const r={};return((t,e)=>{const r=u(t);for(let s=0,n=r.length;s<n;s++){const n=r[s];e(t[n],n)}})(t,((t,s)=>{const n=e(t,s);r[n.k]=n.v})),r})(c,((t,e)=>({k:e,v:g(t)}))));var g;t.ui.registry.addSplitButton(r,{tooltip:s,chevronTooltip:`${s} menu`,icon:"OL"===l?"ordered-list":"unordered-list",presets:"listpreview",columns:"OL"===l?3:4,fetch:t=>{t(d.map(o,(t=>{const e="OL"===l?"num":"bull",r="decimal"===t?"default":t,s=p(t),n=(t=>t.replace(/\-/g," ").replace(/\b\w/g,(t=>t.toUpperCase())))(t);return{type:"choiceitem",value:s,icon:"list-"+e+"-"+r,text:n}})))},onAction:()=>t.execCommand(n),onItemAction:(r,s)=>{e(t,l,s)},select:e=>{const r=(t=>{const e=t.dom.getParent(t.selection.getNode(),"ol,ul"),r=t.dom.getStyle(e,"listStyleType");return i.from(r)})(t);return r.exists((t=>e===t||c[t]===e&&!h[e]))},onSetup:m(t,l)})},y=(t,r,s,n,l,o)=>{o.length>1?v(t,r,s,n,l,o):((t,r,s,n,l,o)=>{t.ui.registry.addToggleButton(r,{active:!1,tooltip:s,icon:"OL"===l?"ordered-list":"unordered-list",onSetup:m(t,l),onAction:()=>t.queryCommandState(n)||""===o?t.execCommand(n):e(t,l,o)})})(t,r,s,n,l,p(o[0]))};t.add("advlist",(t=>{t.hasPlugin("lists")?((t=>{const e=t.options.register;e("advlist_number_styles",{processor:"string[]",default:"default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman".split(",")}),e("advlist_bullet_styles",{processor:"string[]",default:"default,disc,circle,square".split(",")})})(t),(t=>{y(t,"numlist","Numbered list","InsertOrderedList","OL",s(t)),y(t,"bullist","Bullet list","InsertUnorderedList","UL",n(t))})(t),(t=>{t.addCommand("ApplyUnorderedListStyle",((r,s)=>{e(t,"UL",s["list-style-type"])})),t.addCommand("ApplyOrderedListStyle",((r,s)=>{e(t,"OL",s["list-style-type"])}))})(t)):console.error("Please use the Lists plugin together with the List Styles plugin.")}))}();