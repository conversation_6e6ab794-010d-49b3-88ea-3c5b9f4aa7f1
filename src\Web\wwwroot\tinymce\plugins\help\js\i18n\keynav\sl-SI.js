tinymce.Resource.add('tinymce.html-i18n.help-keynav.sl-SI',
'<h1><PERSON><PERSON><PERSON><PERSON><PERSON> krmarjenja s tipkovnico</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Fokus na menijsko vrstico</dt>\n' +
  '  <dd>Windows ali Linux: Alt + F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Fokus na orodno vrstico</dt>\n' +
  '  <dd>Windows ali Linux: Alt + F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Fokus na nogo</dt>\n' +
  '  <dd>Windows ali Linux: Alt + F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Označitev obvestila</dt>\n' +
  '  <dd>Windows ali Linux: Alt + F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Fokus na kontekstualno orodno vrstico</dt>\n' +
  '  <dd>Windows, Linux ali macOS: Ctrl + F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Krmarjenje se bo začelo s prvim elementom uporabniškega vmesnika, ki bo izpostavljena ali podčrtan, če gre za prvi element na\n' +
  '  poti do elementa noge.</p>\n' +
  '\n' +
  '<h1>Krmarjenje med razdelki uporabniškega vmesnika</h1>\n' +
  '\n' +
  '<p>Če se želite pomakniti z enega dela uporabniškega vmesnika na naslednjega, pritisnite <strong>tabulatorko</strong>.</p>\n' +
  '\n' +
  '<p>Če se želite pomakniti z enega dela uporabniškega vmesnika na prejšnjega, pritisnite <strong>shift + tabulatorko</strong>.</p>\n' +
  '\n' +
  '<p>Zaporedje teh razdelkov uporabniškega vmesnika, ko pritiskate <strong>tabulatorko</strong>, je:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Menijska vrstica</li>\n' +
  '  <li>Posamezne skupine orodne vrstice</li>\n' +
  '  <li>Stranska vrstica</li>\n' +
  '  <li>Pod do elementa v nogi</li>\n' +
  '  <li>Gumb za preklop štetja besed v nogi</li>\n' +
  '  <li>Povezava do blagovne znamke v nogi</li>\n' +
  '  <li>Ročaj za spreminjanje velikosti urejevalnika v nogi</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Če razdelek uporabniškega vmesnika ni prisoten, je preskočen.</p>\n' +
  '\n' +
  '<p>Če ima noga fokus za krmarjenje s tipkovnico in ni vidne stranske vrstice, s pritiskom na <strong>shift + tabulatorko</strong>\n' +
  '  fokus premaknete na prvo skupino orodne vrstice, ne zadnjo.</p>\n' +
  '\n' +
  '<h1>Krmarjenje v razdelkih uporabniškega vmesnika</h1>\n' +
  '\n' +
  '<p>Če se želite premakniti z enega elementa uporabniškega vmesnika na naslednjega, pritisnite ustrezno <strong>puščično</strong> tipko.</p>\n' +
  '\n' +
  '<p><strong>Leva</strong> in <strong>desna</strong> puščična tipka</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>omogočata premikanje med meniji v menijski vrstici.</li>\n' +
  '  <li>odpreta podmeni v meniju.</li>\n' +
  '  <li>omogočata premikanje med gumbi v skupini orodne vrstice.</li>\n' +
  '  <li>omogočata premikanje med elementi na poti do elementov noge.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Spodnja</strong> in <strong>zgornja</strong> puščična tipka</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>omogočata premikanje med elementi menija.</li>\n' +
  '  <li>omogočata premikanje med elementi v pojavnem meniju orodne vrstice.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Puščične</strong> tipke omogočajo kroženje znotraj razdelka uporabniškega vmesnika, na katerem je fokus.</p>\n' +
  '\n' +
  '<p>Če želite zapreti odprt meni, podmeni ali pojavni meni, pritisnite tipko <strong>Esc</strong>.</p>\n' +
  '\n' +
  '<p>Če je trenutni fokus na »vrhu« določenega razdelka uporabniškega vmesnika, s pritiskom tipke <strong>Esc</strong> zaprete\n' +
  '  tudi celotno krmarjenje s tipkovnico.</p>\n' +
  '\n' +
  '<h1>Izvajanje menijskega elementa ali gumba orodne vrstice</h1>\n' +
  '\n' +
  '<p>Ko je označen želeni menijski element ali orodja vrstica, pritisnite <strong>vračalko</strong>, <strong>Enter</strong>\n' +
  '  ali <strong>preslednico</strong>, da izvedete element.</p>\n' +
  '\n' +
  '<h1>Krmarjenje po pogovornih oknih brez zavihkov</h1>\n' +
  '\n' +
  '<p>Ko odprete pogovorno okno brez zavihkov, ima fokus prva interaktivna komponenta.</p>\n' +
  '\n' +
  '<p>Med interaktivnimi komponentami pogovornega okna se premikate s pritiskom <strong>tabulatorke</strong> ali kombinacije tipke <strong>shift + tabulatorke</strong>.</p>\n' +
  '\n' +
  '<h1>Krmarjenje po pogovornih oknih z zavihki</h1>\n' +
  '\n' +
  '<p>Ko odprete pogovorno okno z zavihki, ima fokus prvi gumb v meniju zavihka.</p>\n' +
  '\n' +
  '<p>Med interaktivnimi komponentami tega zavihka pogovornega okna se premikate s pritiskom <strong>tabulatorke</strong> ali\n' +
  '  kombinacije tipke <strong>shift + tabulatorke</strong>.</p>\n' +
  '\n' +
  '<p>Na drug zavihek pogovornega okna preklopite tako, da fokus prestavite na meni zavihka in nato pritisnete ustrezno <strong>puščično</strong>\n' +
  '  tipko, da se pomaknete med razpoložljivimi zavihki.</p>\n');