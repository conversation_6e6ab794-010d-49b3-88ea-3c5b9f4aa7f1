!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>t.options.get(e),n=t("insertdatetime_dateformat"),a=t("insertdatetime_timeformat"),r=t("insertdatetime_formats"),s=t("insertdatetime_element"),i="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),o="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),l="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),m="January February March April May June July August September October November December".split(" "),c=(e,t)=>{if((e=""+e).length<t)for(let n=0;n<t-e.length;n++)e="0"+e;return e},d=(e,t,n=new Date)=>(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+n.getFullYear())).replace("%y",""+n.getYear())).replace("%m",c(n.getMonth()+1,2))).replace("%d",c(n.getDate(),2))).replace("%H",""+c(n.getHours(),2))).replace("%M",""+c(n.getMinutes(),2))).replace("%S",""+c(n.getSeconds(),2))).replace("%I",""+((n.getHours()+11)%12+1))).replace("%p",n.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(m[n.getMonth()]))).replace("%b",""+e.translate(l[n.getMonth()]))).replace("%A",""+e.translate(o[n.getDay()]))).replace("%a",""+e.translate(i[n.getDay()]))).replace("%%","%"),u=(e,t)=>{if(s(e)&&e.selection.isEditable()){const n=d(e,t);let a;a=/%[HMSIp]/.test(t)?d(e,"%Y-%m-%dT%H:%M"):d(e,"%Y-%m-%d");const r=e.dom.getParent(e.selection.getStart(),"time");r?((e,t,n,a)=>{const r=e.dom.create("time",{datetime:n},a);e.dom.replace(r,t),e.selection.select(r,!0),e.selection.collapse(!1)})(e,r,a,n):e.insertContent('<time datetime="'+a+'">'+n+"</time>")}else e.insertContent(d(e,t))};var p=tinymce.util.Tools.resolve("tinymce.util.Tools");const g=e=>t=>{const n=()=>{t.setEnabled(e.selection.isEditable())};return e.on("NodeChange",n),n(),()=>{e.off("NodeChange",n)}};e.add("insertdatetime",(e=>{(e=>{const t=e.options.register;t("insertdatetime_dateformat",{processor:"string",default:e.translate("%Y-%m-%d")}),t("insertdatetime_timeformat",{processor:"string",default:e.translate("%H:%M:%S")}),t("insertdatetime_formats",{processor:"string[]",default:["%H:%M:%S","%Y-%m-%d","%I:%M:%S %p","%D"]}),t("insertdatetime_element",{processor:"boolean",default:!1})})(e),(e=>{e.addCommand("mceInsertDate",((t,a)=>{u(e,null!=a?a:n(e))})),e.addCommand("mceInsertTime",((t,n)=>{u(e,null!=n?n:a(e))}))})(e),(e=>{const t=r(e),n=(e=>{let t=e;return{get:()=>t,set:e=>{t=e}}})((e=>{const t=r(e);return t.length>0?t[0]:a(e)})(e)),s=t=>e.execCommand("mceInsertDate",!1,t);e.ui.registry.addSplitButton("insertdatetime",{icon:"insert-time",tooltip:"Insert date/time",chevronTooltip:"Insert date/time menu",select:e=>e===n.get(),fetch:n=>{n(p.map(t,(t=>({type:"choiceitem",text:d(e,t),value:t}))))},onAction:e=>{s(n.get())},onItemAction:(e,t)=>{n.set(t),s(t)},onSetup:g(e)});const i=e=>()=>{n.set(e),s(e)};e.ui.registry.addNestedMenuItem("insertdatetime",{icon:"insert-time",text:"Date/time",getSubmenuItems:()=>p.map(t,(t=>({type:"menuitem",text:d(e,t),onAction:i(t)}))),onSetup:g(e)})})(e)}))}();