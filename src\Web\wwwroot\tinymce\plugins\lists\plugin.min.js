!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager");const e=t=>!(t=>null==t)(t),r=t=>"function"==typeof t;const s=(t,e)=>t===e,n=()=>false;class o{constructor(t,e){this.tag=t,this.value=e}static some(t){return new o(!0,t)}static none(){return o.singletonNone}fold(t,e){return this.tag?e(this.value):t()}isSome(){return this.tag}isNone(){return!this.tag}map(t){return this.tag?o.some(t(this.value)):o.none()}bind(t){return this.tag?t(this.value):o.none()}exists(t){return this.tag&&t(this.value)}forall(t){return!this.tag||t(this.value)}filter(t){return!this.tag||t(this.value)?this:o.none()}getOr(t){return this.tag?this.value:t}or(t){return this.tag?this:t}getOrThunk(t){return this.tag?this.value:t()}orThunk(t){return this.tag?this:t()}getOrDie(t){if(this.tag)return this.value;throw new Error(null!=t?t:"Called getOrDie on None")}static from(t){return e(t)?o.some(t):o.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(t){this.tag&&t(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}o.singletonNone=new o(!1);const a=Array.prototype.slice,i=(t,e,r)=>{for(let s=0,n=t.length;s<n;s++){const n=t[s];if(e(n,s))return o.some(n);if(r(n,s))break}return o.none()};r(Array.from)&&Array.from;const l=(t,e,r=s)=>t.exists((t=>r(t,e))),u=(c=/^\s+|\s+$/g,t=>t.replace(c,""));var c;const d=t=>{const e=(t=>{const e=a.call(t,0);return e.reverse(),e})(u(t).split("")),r=((t,e)=>{const r=t.length,s=new Array(r);for(let n=0;n<r;n++){const r=t[n];s[n]=e(r,n)}return s})(e,((t,e)=>{const r=t.toUpperCase().charCodeAt(0)-"A".charCodeAt(0)+1;return Math.pow(26,e)*r}));return s=(t,e)=>t+e,n=0,((t,e)=>{for(let r=0,s=t.length;r<s;r++)e(t[r],r)})(r,((t,e)=>{n=s(n,t)})),n;var s,n},m=t=>{if(--t<0)return"";{const e=t%26,r=Math.floor(t/26);return m(r)+String.fromCharCode("A".charCodeAt(0)+e)}},g=t=>{const e=parseInt(t.start,10);return l(t.listStyleType,"upper-alpha")?m(e):l(t.listStyleType,"lower-alpha")?m(e).toLowerCase():t.start},h=t=>t.options.get("forced_root_block");const p=t=>r=>e(r)&&t.test(r.nodeName),y=p(/^(OL|UL|DL)$/),v=p(/^(TH|TD)$/),f=p(/^(LI|DT|DD)$/),b=(t,e)=>i(t,y,v).exists((t=>t.nodeName===e&&!(t=>/\btox\-/.test(t.className))(t))),L=(t,e)=>{const r=t.selection.getNode();return e({parents:t.dom.getParents(r),element:r}),t.on("NodeChange",e),()=>t.off("NodeChange",e)},S=(t,e)=>{const r=t.dom.getParent(e,"ol,ul,dl");return((t,e)=>null!==e&&!t.dom.isEditable(e))(t,r)||!t.selection.isEditable()},C=t=>e(t)&&"ol"===t.nodeName.toLowerCase(),A=["OL","UL","DL"],T=A.join(","),N=(t,e)=>{const r=e||t.selection.getStart(!0);return t.dom.getParent(r,T,w(t,r))},w=(t,e)=>{const r=t.dom.getParents(e,t.dom.isBlock),s=(o=e=>(e=>e.nodeName.toLowerCase()!==h(t))(e)&&O(t.schema,e),i(r,o,n));var o;return s.getOr(t.getBody())},O=(t,e)=>!y(e)&&!f(e)&&(r=>{for(let n=0,o=r.length;n<o;n++)if(s=r[n],t.isValidChild(e.nodeName,s))return!0;var s;return!1})(A),x=(t,e)=>r=>(r.setEnabled(t.selection.isEditable()),L(t,(s=>{r.setActive(b(s.parents,e)),r.setEnabled(!S(t,s.element)&&t.selection.isEditable())}))),D=(t,e)=>r=>L(t,(s=>r.setEnabled(b(s.parents,e)&&!S(t,s.element))));t.add("lists",(t=>((t=>{t.addCommand("mceListProps",(()=>{(t=>{const e=N(t);C(e)&&!S(t,e)&&t.windowManager.open({title:"List Properties",body:{type:"panel",items:[{type:"input",name:"start",label:"Start list at number",inputMode:"numeric"}]},initialData:{start:g({start:t.dom.getAttrib(e,"start","1"),listStyleType:o.from(t.dom.getStyle(e,"list-style-type"))})},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:e=>{(t=>{switch((t=>/^[0-9]+$/.test(t)?2:/^[A-Z]+$/.test(t)?0:/^[a-z]+$/.test(t)?1:t.length>0?4:3)(t)){case 2:return o.some({listStyleType:o.none(),start:t});case 0:return o.some({listStyleType:o.some("upper-alpha"),start:d(t).toString()});case 1:return o.some({listStyleType:o.some("lower-alpha"),start:d(t).toString()});case 3:return o.some({listStyleType:o.none(),start:""});case 4:return o.none()}})(e.getData().start).each((e=>{t.execCommand("mceListUpdate",!1,{attrs:{start:"1"===e.start?"":e.start},styles:{"list-style-type":e.listStyleType.getOr("")}})})),e.close()}})})(t)}))})(t),(t=>{const e=e=>()=>t.execCommand(e);t.hasPlugin("advlist")||(t.ui.registry.addToggleButton("numlist",{icon:"ordered-list",active:!1,tooltip:"Numbered list",onAction:e("InsertOrderedList"),onSetup:x(t,"OL")}),t.ui.registry.addToggleButton("bullist",{icon:"unordered-list",active:!1,tooltip:"Bullet list",onAction:e("InsertUnorderedList"),onSetup:x(t,"UL")}))})(t),(t=>{const e={text:"List properties...",icon:"ordered-list",onAction:()=>t.execCommand("mceListProps"),onSetup:D(t,"OL")};t.ui.registry.addMenuItem("listprops",e),t.ui.registry.addContextMenu("lists",{update:e=>{const r=N(t,e);return C(r)?["listprops"]:[]}})})(t),(t=>({backspaceDelete:e=>{t.execCommand("mceListBackspaceDelete",!1,e)}}))(t))))}();