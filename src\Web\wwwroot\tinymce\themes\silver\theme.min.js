!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),x=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function C(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=y(!1),E=y(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,F=(e,t)=>D.call(e,t),I=(e,t)=>F(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},N=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},L=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},V=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},H=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},P=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t,o)=>(V(e,((e,n)=>{o=t(o,e,n)})),o),$=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),G=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},j=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},q=(e,t)=>j(L(e,t)),X=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=M.call(e,0);return t.reverse(),t},K=(e,t)=>P(e,(e=>!I(t,e))),J=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],Z=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},ee=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),te=e=>ee(e,0),oe=e=>ee(e,e.length-1),ne=p(Array.from)?Array.from:e=>M.call(e),se=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},re=Object.keys,ae=Object.hasOwnProperty,ie=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},le=(e,t)=>ce(e,((e,o)=>({k:o,v:t(e,o)}))),ce=(e,t)=>{const o={};return ie(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},de=e=>(t,o)=>{e[o]=t},ue=(e,t,o,n)=>{ie(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},me=(e,t)=>{const o={};return ue(e,t,de(o),b),o},ge=(e,t)=>{const o=[];return ie(e,((e,n)=>{o.push(t(e,n))})),o},pe=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},he=e=>ge(e,w),fe=(e,t)=>be(e,t)?A.from(e[t]):A.none(),be=(e,t)=>ae.call(e,t),ve=(e,t)=>be(e,t)&&void 0!==e[t]&&null!==e[t],xe=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return V(e,((n,s)=>{const r=re(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=re(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!X(t,(e=>I(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},ye=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},we=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),V(o,r),o=[])})),{get:n,map:e=>we((t=>{n((o=>{t(e(o))}))})),isReady:s}},Se={nu:we,pure:e=>we((t=>{t(e)}))},Ce=e=>{setTimeout((()=>{throw e}),0)},ke=e=>{const t=t=>{e().then(t,Ce)};return{map:t=>ke((()=>e().then(t))),bind:t=>ke((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>ke((()=>e().then((()=>t.toPromise())))),toLazy:()=>Se.nu(t),toCached:()=>{let t=null;return ke((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},Oe=e=>ke((()=>new Promise(e))),_e=e=>ke((()=>Promise.resolve(e))),Te=e=>{const t=t=>t(e),o=y(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>Ae.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},Ee=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>Ae.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},Ae={value:Te,error:Ee,fromOption:(e,t)=>e.fold((()=>Ee(t)),Te)},Me=e=>({...e,toCached:()=>Me(e.toCached()),bindFuture:t=>Me(e.bind((e=>e.fold((e=>_e(Ae.error(e))),(e=>t(e)))))),bindResult:t=>Me(e.map((e=>e.bind(t)))),mapResult:t=>Me(e.map((e=>e.map(t)))),mapError:t=>Me(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>Me(Oe((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(Ae.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),De=e=>Me(Oe(e)),Be="undefined"!=typeof window?window:Function("return this;")(),Fe=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Ie=(e,t,o)=>Math.min(Math.max(e,t),o);let Re=0;const Ne=e=>{const t=(new Date).getTime(),o=Math.floor(window.crypto.getRandomValues(new Uint32Array(1))[0]/4294967295*1e9);return Re++,e+"_"+o+Re+String(t)},ze=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)be(s,t)&&(o[t]=e(o[t],s[t]))}return o},Le=ze(((e,t)=>i(e)&&i(t)?Le(e,t):t)),Ve=ze(((e,t)=>t)),He=(e,t,o=S)=>e.exists((e=>o(e,t))),Pe=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Ue=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),We=(e,t)=>null!=e?A.some(t(e)):A.none(),$e=(e,t)=>e?A.some(t):A.none(),Ge=(e,t)=>((e,t)=>{let o=null!=t?t:Be;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t);xe([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const je=e=>{const t=ye(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},qe=()=>je((e=>e.unbind())),Xe=()=>{const e=je(b);return{...e,on:t=>e.get().each(t)}},Ye=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Ke=(e,t)=>Qe(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Je=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Qe=(e,t)=>Ye(e,t,0),Ze=(e,t)=>Ye(e,t,e.length-t.length),et=(ft=/^\s+|\s+$/g,e=>e.replace(ft,"")),tt=e=>e.length>0,ot=e=>!tt(e),nt=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},st=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},rt=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},at=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return rt(o.childNodes[0])},it=(e,t)=>{const o=(t||document).createElement(e);return rt(o)},lt=(e,t)=>{const o=(t||document).createTextNode(e);return rt(o)},ct=rt,dt=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},ut=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},mt=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),gt=xe([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),pt=(e,t,o)=>t(ct(o.startContainer),o.startOffset,ct(o.endContainer),o.endOffset),ht=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:A.none}),relative:(t,o)=>({ltr:st((()=>dt(e,t,o))),rtl:st((()=>A.some(dt(e,o,t))))}),exact:(t,o,n,s)=>({ltr:st((()=>ut(e,t,o,n,s))),rtl:st((()=>A.some(ut(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>gt.rtl(ct(e.endContainer),e.endOffset,ct(e.startContainer),e.startOffset))).getOrThunk((()=>pt(0,gt.ltr,o))):pt(0,gt.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});var ft;gt.ltr,gt.rtl;const bt=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},vt=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,xt=(e,t)=>{const o=void 0===t?document:t.dom;return vt(o)?A.none():A.from(o.querySelector(e)).map(ct)},yt=(e,t)=>e.dom===t.dom,wt=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},St=()=>Ct(0,0),Ct=(e,t)=>({major:e,minor:t}),kt={nu:Ct,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?St():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Ct(n(1),n(2))})(e,o)},unknown:St},Ot=(e,t)=>{const o=String(t).toLowerCase();return $(e,(e=>e.search(o)))},_t=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Tt=e=>t=>Je(t,e),Et=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Je(e,"edge/")&&Je(e,"chrome")&&Je(e,"safari")&&Je(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,_t],search:e=>Je(e,"chrome")&&!Je(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Je(e,"msie")||Je(e,"trident")},{name:"Opera",versionRegexes:[_t,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Tt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Tt("firefox")},{name:"Safari",versionRegexes:[_t,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Je(e,"safari")||Je(e,"mobile/"))&&Je(e,"applewebkit")}],At=[{name:"Windows",search:Tt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Je(e,"iphone")||Je(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Tt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Tt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Tt("linux"),versionRegexes:[]},{name:"Solaris",search:Tt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Tt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Tt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Mt={browsers:y(Et),oses:y(At)},Dt="Edge",Bt="Chromium",Ft="Opera",It="Firefox",Rt="Safari",Nt=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(Dt),isChromium:n(Bt),isIE:n("IE"),isOpera:n(Ft),isFirefox:n(It),isSafari:n(Rt)}},zt=()=>Nt({current:void 0,version:kt.unknown()}),Lt=Nt,Vt=(y(Dt),y(Bt),y("IE"),y(Ft),y(It),y(Rt),"Windows"),Ht="Android",Pt="Linux",Ut="macOS",Wt="Solaris",$t="FreeBSD",Gt="ChromeOS",jt=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(Vt),isiOS:n("iOS"),isAndroid:n(Ht),isMacOS:n(Ut),isLinux:n(Pt),isSolaris:n(Wt),isFreeBSD:n($t),isChromeOS:n(Gt)}},qt=()=>jt({current:void 0,version:kt.unknown()}),Xt=jt,Yt=(y(Vt),y("iOS"),y(Ht),y(Pt),y(Ut),y(Wt),y($t),y(Gt),e=>window.matchMedia(e).matches);let Kt=st((()=>((e,t,o)=>{const n=Mt.browsers(),s=Mt.oses(),r=t.bind((e=>((e,t)=>se(t.brands,(t=>{const o=t.brand.toLowerCase();return $(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:kt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Ot(e,t).map((e=>{const o=kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(zt,Lt),a=((e,t)=>Ot(e,t).map((e=>{const o=kt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(qt,Xt),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(s),isiPhone:y(r),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(window.navigator.userAgent,A.from(window.navigator.userAgentData),Yt)));const Jt=()=>Kt(),Qt=Object.getPrototypeOf,Zt=e=>{const t=Ge("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>Ge(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Qt(e).constructor.name))},eo=e=>e.dom.nodeName.toLowerCase(),to=e=>t=>(e=>e.dom.nodeType)(t)===e,oo=e=>no(e)&&Zt(e.dom),no=to(1),so=to(3),ro=to(9),ao=to(11),io=e=>t=>no(t)&&eo(t)===e,lo=e=>ct(e.dom.ownerDocument),co=e=>ro(e)?e:lo(e),uo=e=>ct(co(e).dom.documentElement),mo=e=>ct(co(e).dom.defaultView),go=e=>A.from(e.dom.parentNode).map(ct),po=e=>A.from(e.dom.parentElement).map(ct),ho=e=>A.from(e.dom.offsetParent).map(ct),fo=e=>A.from(e.dom.previousSibling).map(ct),bo=e=>A.from(e.dom.nextSibling).map(ct),vo=e=>L(e.dom.childNodes,ct),xo=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ct)},yo=e=>xo(e,0),wo=(e,t)=>({element:e,offset:t}),So=(e,t)=>{const o=vo(e);return o.length>0&&t<o.length?wo(o[t],0):wo(e,t)},Co=e=>ao(e)&&g(e.dom.host),ko=e=>ct(e.dom.getRootNode()),Oo=e=>Co(e)?e:ct(co(e).dom.body),_o=e=>{const t=ko(e);return Co(t)?A.some(t):A.none()},To=e=>ct(e.dom.host),Eo=e=>{const t=ct((e=>{if(g(e.target)){const t=ct(e.target);if(no(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return te(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=x(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Ao=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Eo(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:C(Mo,e,t,r,s)}},Mo=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Do=E,Bo=(e,t,o)=>((e,t,o,n)=>Ao(e,t,o,n,!1))(e,t,Do,o),Fo=(e,t,o)=>((e,t,o,n)=>Ao(e,t,o,n,!0))(e,t,Do,o),Io=Eo,Ro=()=>ct(document),No=(e,t=!1)=>e.dom.focus({preventScroll:t}),zo=e=>e.dom.blur(),Lo=e=>{const t=ko(e).dom;return e.dom===t.activeElement},Vo=(e=Ro())=>A.from(e.dom.activeElement).map(ct),Ho=e=>Vo(ko(e)).filter((t=>e.dom.contains(t.dom))),Po=(e,t)=>{go(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Uo=(e,t)=>{bo(e).fold((()=>{go(e).each((e=>{$o(e,t)}))}),(e=>{Po(e,t)}))},Wo=(e,t)=>{yo(e).fold((()=>{$o(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},$o=(e,t)=>{e.dom.appendChild(t.dom)},Go=(e,t)=>{V(t,(t=>{$o(e,t)}))},jo=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},qo=(e,t,o)=>{jo(e.dom,t,o)},Xo=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{jo(o,t,e)}))},Yo=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Ko=(e,t)=>A.from(Yo(e,t)),Jo=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Qo=(e,t)=>{e.dom.removeAttribute(t)},Zo=e=>{e.dom.textContent="",V(vo(e),(e=>{en(e)}))},en=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},tn=(e,t)=>ct(e.dom.cloneNode(t)),on=e=>e.dom.innerHTML,nn=(e,t)=>{const o=lo(e).dom,n=ct(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,vo(ct(o))})(t,o);Go(n,s),Zo(e),$o(e,n)},sn=e=>new Promise(((t,o)=>{const n=()=>{r(),t(e)},s=[Bo(e,"load",n),Bo(e,"error",(()=>{r(),o("Unable to load data from image: "+e.dom.src)}))],r=()=>V(s,(e=>e.unbind()));e.dom.complete&&n()})),rn=e=>void 0!==e.style&&p(e.style.getPropertyValue),an=e=>{const t=so(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return _o(ct(t)).fold((()=>o.body.contains(t)),(n=an,s=To,e=>n(s(e))));var n,s},ln=()=>cn(ct(document)),cn=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ct(t)},dn=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);rn(e)&&e.style.setProperty(t,o)},un=(e,t)=>{rn(e)&&e.style.removeProperty(t)},mn=(e,t,o)=>{const n=e.dom;dn(n,t,o)},gn=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{dn(o,t,e)}))},pn=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{e.fold((()=>{un(o,t)}),(e=>{dn(o,t,e)}))}))},hn=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||an(e)?n:fn(o,t)},fn=(e,t)=>rn(e)?e.style.getPropertyValue(t):"",bn=(e,t)=>{const o=e.dom,n=fn(o,t);return A.from(n).filter((e=>e.length>0))},vn=e=>{const t={},o=e.dom;if(rn(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},xn=(e,t,o)=>{const n=it(e);return mn(n,t,o),bn(n,t).isSome()},yn=(e,t)=>{const o=e.dom;un(o,t),He(Ko(e,"style").map(et),"")&&Qo(e,"style")},wn=e=>e.dom.offsetWidth,Sn=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=hn(o,e);return parseFloat(t)||0}return n},n=(e,t)=>W(t,((t,o)=>{const n=hn(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;rn(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Cn=Sn("height",(e=>{const t=e.dom;return an(e)?t.getBoundingClientRect().height:t.offsetHeight})),kn=e=>Cn.get(e),On=e=>Cn.getOuter(e),_n=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Tn=Sn("width",(e=>{const t=e.dom;return an(e)?t.getBoundingClientRect().width:t.offsetWidth})),En=e=>Tn.get(e),An=e=>Tn.getOuter(e),Mn=(e,t)=>({left:e,top:t,translate:(o,n)=>Mn(e+o,t+n)}),Dn=Mn,Bn=(e,t)=>void 0!==e?e:void 0!==t?t:0,Fn=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return Dn(o.offsetLeft,o.offsetTop);const r=Bn(null==n?void 0:n.pageYOffset,s.scrollTop),a=Bn(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Bn(s.clientTop,o.clientTop),l=Bn(s.clientLeft,o.clientLeft);return In(e).translate(a-l,r-i)},In=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Dn(o.offsetLeft,o.offsetTop):an(e)?(e=>{const t=e.getBoundingClientRect();return Dn(t.left,t.top)})(t):Dn(0,0)},Rn=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Dn(o,n)},Nn=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},zn=(e=>{const t=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:o=>{if(!e(o))throw new Error("Can only get text value of a text node");return t(o).getOr("")},getOption:t,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(so),Ln=(e,t)=>o=>"rtl"===Vn(o)?t:e,Vn=e=>"rtl"===hn(e,"direction")?"rtl":"ltr",Hn=(e,t)=>{const o=Yo(e,t);return void 0===o||""===o?[]:o.split(" ")};var Pn=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const Un=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ct(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},Wn=(e,t,o)=>Pn(((e,t)=>t(e)),Un,e,t,o),$n=(e,t)=>$(e.dom.childNodes,(e=>t(ct(e)))).map(ct),Gn=(e,t,o)=>Un(e,(e=>bt(e,t)),o),jn=(e,t)=>(e=>{const o=e.dom;return o.parentNode?$n(ct(o.parentNode),(o=>!yt(e,o)&&bt(o,t))):A.none()})(e),qn=(e,t)=>$n(e,(e=>bt(e,t))),Xn=(e,t)=>xt(t,e),Yn=(e,t,o)=>Pn(((e,t)=>bt(e,t)),Gn,e,t,o),Kn=(e,t)=>{e.dom.checked=t},Jn=e=>e.dom.checked,Qn=e=>void 0!==e.dom.classList,Zn=e=>Hn(e,"class"),es=(e,t)=>((e,t,o)=>{const n=Hn(e,t).concat([o]);return qo(e,t,n.join(" ")),!0})(e,"class",t),ts=(e,t)=>((e,t,o)=>{const n=P(Hn(e,t),(e=>e!==o));return n.length>0?qo(e,t,n.join(" ")):Qo(e,t),!1})(e,"class",t),os=(e,t)=>{Qn(e)?e.dom.classList.add(t):es(e,t)},ns=e=>{0===(Qn(e)?e.dom.classList:Zn(e)).length&&Qo(e,"class")},ss=(e,t)=>{Qn(e)?e.dom.classList.remove(t):ts(e,t),ns(e)},rs=(e,t)=>Qn(e)&&e.dom.classList.contains(t),as=(e,t)=>{V(t,(t=>{os(e,t)}))},is=(e,t)=>{V(t,(t=>{ss(e,t)}))},ls=e=>Qn(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Zn(e),cs=e=>e.dom.value,ds=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},us=(e,t,o)=>P(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ct(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),ms=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return vt(o)?[]:L(o.querySelectorAll(e),ct)})(t,e),gs=(e,t,o)=>Wn(e,t,o).isSome(),ps=(e,t,o)=>Yn(e,t,o).isSome(),hs=e=>p(e)?e:T,fs=(e,t,o)=>{let n=e.dom;const s=hs(o);for(;n.parentNode;){n=n.parentNode;const e=ct(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},bs=(e,t,o)=>{const n=t(e),s=hs(o);return n.orThunk((()=>s(e)?A.none():fs(e,t,s)))},vs=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),xs=xe([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),ys=(xs.before,xs.on,xs.after,e=>e.fold(w,w,w)),ws=xe([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Ss={domRange:ws.domRange,relative:ws.relative,exact:ws.exact,exactFromRange:e=>ws.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ct(e.startContainer),relative:(e,t)=>ys(e),exact:(e,t,o,n)=>e}))(e);return mo(t)},range:vs},Cs=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(vs(ct(t.startContainer),t.startOffset,ct(o.endContainer),o.endOffset))}return A.none()},ks=e=>{if(null===e.anchorNode||null===e.focusNode)return Cs(e);{const t=ct(e.anchorNode),o=ct(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=lo(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=yt(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(vs(t,e.anchorOffset,o,e.focusOffset)):Cs(e)}},Os=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(mt):A.none()})(ht(e,t)),_s={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},Ts=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),Es=(e,t)=>A.from(Ts.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(_s[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),As=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Ms=e=>{const t=void 0===e?window:e,o=t.document,n=Rn(ct(o));return(e=>{const t=void 0===e?window:e;return Jt().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return As(n.left,n.top,o,s)}),(e=>As(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Ds=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=Ds(e,o);return[t].concat(n)}));var Bs=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ct)},owner:e=>lo(e)});const Fs=e=>{const t=Ro(),o=Rn(t),n=((e,t)=>{const o=t.owner(e),n=Ds(t,o);return A.some(n)})(e,Bs);return n.fold(C(Fn,e),(t=>{const n=In(e),s=U(t,((e,t)=>{const o=In(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Dn(s.left+n.left+o.left,s.top+n.top+o.top)}))},Is=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Rs=e=>{const t=Fn(e),o=An(e),n=On(e);return Is(t.left,t.top,o,n)},Ns=e=>{const t=Fs(e),o=An(e),n=On(e);return Is(t.left,t.top,o,n)},zs=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Is(o,n,s-o,r-n)},Ls=()=>Ms(window),Vs=(e,t)=>yt(e.element,t.event.target),Hs=e=>(He(bn(e,"position"),"fixed")?A.none():ho(e)).orThunk((()=>{const t=it("span");return go(e).bind((e=>{$o(e,t);const o=ho(t);return en(t),o}))})),Ps=e=>Hs(e).map(Fn).getOrThunk((()=>Dn(0,0)));var Us;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(Us||(Us={}));const Ws=(e,t,o)=>e.stype===Us.Error?t(e.serror):o(e.svalue),$s=e=>({stype:Us.Value,svalue:e}),Gs=e=>({stype:Us.Error,serror:e}),js=$s,qs=Gs,Xs=Ws,Ys=e=>a(e)&&re(e).length>100?" removed due to size":JSON.stringify(e,null,2),Ks=(e,t)=>qs([{path:e,getErrorInfo:t}]),Js=e=>({extract:(t,o)=>((e,t)=>e.stype===Us.Error?t(e.serror):e)(e(o),(e=>((e,t)=>Ks(e,y(t)))(t,e))),toString:y("val")}),Qs=Js(js),Zs=y(Qs),er=(e,t)=>Js((o=>{const n=typeof o;return e(o)?js(o):qs(`Expected type: ${t} but got: ${n}`)})),tr=er(h,"number"),or=er(r,"string"),nr=er(d,"boolean"),sr=er(p,"function"),rr=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>rr(e[t])));default:return!1}},ar=Js((e=>rr(e)?js(e):qs("Expected value to be acceptable for sending via postMessage"))),ir=e=>({tag:"defaultedThunk",process:e}),lr=e=>ir(y(e)),cr=e=>({tag:"mergeWithThunk",process:e}),dr=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),ur=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},mr=e=>{const t=(e=>{const t=[],o=[];return V(e,(e=>{Ws(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,x(qs,j)(o)):js(t.values);var o},gr=(e,t,o,n)=>n(fe(e,t).getOrThunk((()=>o(e)))),pr=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>js(A.none())),(e=>((e,t)=>e.stype===Us.Value?{stype:Us.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>fe(t,o).fold((()=>((e,t,o)=>Ks(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Ys(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return gr(o,n,e.process,r);case"option":return((e,t,o)=>o(fe(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(fe(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return gr(o,n,y({}),(t=>{const n=Le(e.process(o),t);return r(n)}))}},hr=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),fr=e=>re(me(e,g)),br=e=>{const t=vr(e),o=U(e,((e,t)=>ur(t,(t=>Le(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:fr(n),r=P(s,(e=>!ve(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>Ks(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},vr=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)ur(r,((o,r,a,i)=>{const l=pr(a,e,t,o,i);Xs(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?qs(s):js(n)})(t,o,e),toString:()=>{const t=L(e,(e=>ur(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),xr=e=>({extract:(t,o)=>{const n=L(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return mr(n)},toString:()=>"array("+e.toString()+")"}),yr=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===Us.Value)return{stype:Us.Value,svalue:o(e.svalue)};s.push(e)}return mr(s)},toString:()=>"oneOf("+L(e,(e=>e.toString())).join(", ")+")"}},wr=(e,t)=>({extract:(o,n)=>{const s=re(n),r=((t,o)=>xr(Js(e)).extract(t,o))(o,s);return((e,t)=>e.stype===Us.Value?t(e.svalue):e)(r,(e=>{const s=L(e,(e=>dr(e,e,{tag:"required",process:{}},t)));return vr(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Sr=x(xr,vr),Cr=(e,t)=>({extract:(o,n)=>fe(n,e).fold((()=>((e,t)=>Ks(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>fe(o,n).fold((()=>((e,t,o)=>Ks(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Ys(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+re(t)}),kr=e=>Js((t=>e(t).fold(qs,js))),Or=(e,t)=>wr((t=>e(t).fold(Gs,$s)),t),_r=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===Us.Error?{stype:Us.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),Ws(n,Ae.error,Ae.value);var n},Tr=e=>e.fold((e=>{throw new Error(Ar(e))}),w),Er=(e,t,o)=>Tr(_r(e,t,o)),Ar=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return L(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Ys(e.input),Mr=(e,t)=>Cr(e,le(t,vr)),Dr=(e,t)=>((e,t)=>{const o=st(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),Br=dr,Fr=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),Ir=e=>kr((t=>I(e,t)?Ae.value(t):Ae.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),Rr=e=>Br(e,e,{tag:"required",process:{}},Zs()),Nr=(e,t)=>Br(e,e,{tag:"required",process:{}},t),zr=e=>Nr(e,tr),Lr=e=>Nr(e,or),Vr=(e,t)=>Br(e,e,{tag:"required",process:{}},Ir(t)),Hr=e=>Nr(e,sr),Pr=(e,t)=>Br(e,e,{tag:"required",process:{}},vr(t)),Ur=(e,t)=>Br(e,e,{tag:"required",process:{}},Sr(t)),Wr=(e,t)=>Br(e,e,{tag:"required",process:{}},xr(t)),$r=e=>Br(e,e,{tag:"option",process:{}},Zs()),Gr=(e,t)=>Br(e,e,{tag:"option",process:{}},t),jr=e=>Gr(e,tr),qr=e=>Gr(e,or),Xr=(e,t)=>Gr(e,Ir(t)),Yr=e=>Gr(e,sr),Kr=(e,t)=>Gr(e,xr(t)),Jr=(e,t)=>Gr(e,vr(t)),Qr=(e,t)=>Br(e,e,lr(t),Zs()),Zr=(e,t,o)=>Br(e,e,lr(t),o),ea=(e,t)=>Zr(e,t,tr),ta=(e,t)=>Zr(e,t,or),oa=(e,t,o)=>Zr(e,t,Ir(o)),na=(e,t)=>Zr(e,t,nr),sa=(e,t)=>Zr(e,t,sr),ra=(e,t,o)=>Zr(e,t,xr(o)),aa=(e,t,o)=>Zr(e,t,vr(o)),ia=(e,t)=>((e,t)=>({[e]:t}))(e,t),la=e=>(e=>{const t={};return V(e,(e=>{t[e.key]=e.value})),t})(e),ca=y,da=ca("touchstart"),ua=ca("touchmove"),ma=ca("touchend"),ga=ca("touchcancel"),pa=ca("mousedown"),ha=ca("mousemove"),fa=ca("mouseout"),ba=ca("mouseup"),va=ca("mouseover"),xa=ca("focusin"),ya=ca("focusout"),wa=ca("keydown"),Sa=ca("keyup"),Ca=ca("input"),ka=ca("change"),Oa=ca("click"),_a=ca("transitioncancel"),Ta=ca("transitionend"),Ea=ca("transitionstart"),Aa=ca("selectstart"),Ma=e=>y("alloy."+e),Da={tap:Ma("tap")},Ba=Ma("focus"),Fa=Ma("blur.post"),Ia=Ma("paste.post"),Ra=Ma("receive"),Na=Ma("execute"),za=Ma("focus.item"),La=Da.tap,Va=Ma("longpress"),Ha=Ma("sandbox.close"),Pa=Ma("typeahead.cancel"),Ua=Ma("system.init"),Wa=Ma("system.touchmove"),$a=Ma("system.touchend"),Ga=Ma("system.scroll"),ja=Ma("system.resize"),qa=Ma("system.attached"),Xa=Ma("system.detached"),Ya=Ma("system.dismissRequested"),Ka=Ma("system.repositionRequested"),Ja=Ma("focusmanager.shifted"),Qa=Ma("slotcontainer.visibility"),Za=Ma("system.external.element.scroll"),ei=Ma("change.tab"),ti=Ma("dismiss.tab"),oi=Ma("highlight"),ni=Ma("dehighlight"),si=e=>(e=>{if(Co(e))return"#shadow-root";{const t=(e=>tn(e,!1))(e);return(e=>{const t=it("div"),o=ct(e.dom.cloneNode(!0));return $o(t,o),on(t)})(t)}})(e);var ri;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ri||(ri={}));const ai=ye({}),ii=["alloy/data/Fields","alloy/debugging/Debugging"],li=(e,t,o)=>((e,t,o)=>{switch(fe(ai.get(),e).orThunk((()=>{const t=re(ai.get());return se(t,(t=>e.indexOf(t)>-1?A.some(ai.get()[t]):A.none()))})).getOr(ri.NORMAL)){case ri.NORMAL:return o(ci());case ri.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();I(["mousemove","mouseover","mouseout",Ua()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:L(o,(e=>I(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+si(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case ri.STOP:return!0}})(e,t,o),ci=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),di=y([Rr("menu"),Rr("selectedMenu")]),ui=y([Rr("item"),Rr("selectedItem")]);y(vr(ui().concat(di())));const mi=y(vr(ui())),gi=Pr("initSize",[Rr("numColumns"),Rr("numRows")]),pi=()=>Pr("markers",[Rr("backgroundMenu")].concat(di()).concat(ui())),hi=e=>Pr("markers",L(e,Rr)),fi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");$(t,(e=>e.indexOf("alloy")>0&&!R(ii,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Br(t,t,o,kr((e=>Ae.value(((...t)=>e.apply(void 0,t))))))),bi=e=>fi(0,e,lr(b)),vi=e=>fi(0,e,lr(A.none)),xi=e=>fi(0,e,{tag:"required",process:{}}),yi=e=>fi(0,e,{tag:"required",process:{}}),wi=(e,t)=>Fr(e,y(t)),Si=e=>Fr(e,w),Ci=y(gi),ki=e=>L(e,(e=>Ze(e,"/*")?e.substring(0,e.length-2):e)),Oi=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ki(r)}),e},_i=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},Ti=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])};var Ei;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(Ei||(Ei={}));const Ai="placeholder",Mi=xe([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Di=e=>be(e,"uiType"),Bi=(e,t,o,n)=>((e,t,o,n)=>Di(o)&&o.uiType===Ai?((e,t,o,n)=>e.exists((e=>e!==o.owner))?Mi.single(!0,y(o)):fe(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+re(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):Mi.single(!1,y(o)))(e,0,o,n).fold(((s,r)=>{const a=Di(o)?r(t,o.config,o.validated):r(t),i=fe(a,"components").getOr([]),l=q(i,(o=>Bi(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Di(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Fi=Mi.single,Ii=Mi.multiple,Ri=y(Ai),Ni=xe([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),zi=Qr("factory",{sketch:w}),Li=Qr("schema",[]),Vi=Rr("name"),Hi=Br("pname","pname",ir((e=>"<alloy."+Ne(e.name)+">")),Zs()),Pi=Fr("schema",(()=>[$r("preprocess")])),Ui=Qr("defaults",y({})),Wi=Qr("overrides",y({})),$i=vr([zi,Li,Vi,Hi,Ui,Wi]),Gi=vr([zi,Li,Vi,Ui,Wi]),ji=vr([zi,Li,Vi,Hi,Ui,Wi]),qi=vr([zi,Pi,Vi,Rr("unit"),Hi,Ui,Wi]),Xi=e=>e.fold(A.some,A.none,A.some,A.some),Yi=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Ki=(e,t)=>o=>{const n=Er("Converting part type",t,o);return e(n)},Ji=Ki(Ni.required,$i),Qi=Ki(Ni.external,Gi),Zi=Ki(Ni.optional,ji),el=Ki(Ni.group,qi),tl=y("entirety");var ol=Object.freeze({__proto__:null,required:Ji,external:Qi,optional:Zi,group:el,asNamedPart:Xi,name:Yi,asCommon:e=>e.fold(w,w,w,w),original:tl});const nl=(e,t,o,n)=>Le(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),sl=(e,t)=>{const o={};return V(t,(t=>{Xi(t).each((t=>{const n=rl(e,t.pname);o[t.name]=o=>{const s=Er("Part: "+t.name+" in "+e,vr(t.schema),o);return{...n,config:o,validated:s}}}))})),o},rl=(e,t)=>({uiType:Ri(),owner:e,name:t}),al=(e,t,o)=>({uiType:Ri(),owner:e,name:t,config:o,validated:{}}),il=e=>q(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>Pr(e.name,e.schema.concat([Si(tl())])))).toArray())),ll=e=>L(e,Yi),cl=(e,t,o)=>((e,t,o)=>{const n={},s={};return V(o,(e=>{e.fold((e=>{n[e.pname]=Fi(!0,((t,o,n)=>e.factory.sketch(nl(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=y(e.factory.sketch(nl(t,e,o[tl()]),o))}),(e=>{n[e.pname]=Fi(!1,((t,o,n)=>e.factory.sketch(nl(t,e,o,n))))}),(e=>{n[e.pname]=Ii(!0,((t,o,n)=>{const s=t[e.name];return L(s,(o=>e.factory.sketch(Le(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(s)}})(0,t,o),dl=(e,t,o)=>((e,t,o,n)=>{const s=le(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>q(o,(o=>Bi(e,t,o,n))))(e,t,o,s);return ie(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),ul=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},ml=(e,t,o)=>ul(e,t,o).getOrDie("Could not find part: "+o),gl=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=y(r.getByUid(s[e]))})),n},pl=(e,t)=>{const o=e.getSystem();return le(t.partUids,((e,t)=>y(o.getByUid(e))))},hl=e=>re(e.partUids),fl=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=y(r.getByUid(s[e]).getOrDie())})),n},bl=(e,t)=>{const o=ll(t);return la(L(o,(t=>({key:t,value:e+"-"+t}))))},vl=e=>Br("partUids","partUids",cr((t=>bl(t.uid,e))),Zs());var xl=Object.freeze({__proto__:null,generate:sl,generateOne:al,schemas:il,names:ll,substitutes:cl,components:dl,defaultUids:bl,defaultUidsSchema:vl,getAllParts:pl,getAllPartNames:hl,getPart:ul,getPartOrDie:ml,getParts:gl,getPartsOrDie:fl});const yl=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],wl=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>fe(o,e).getOr([]),i=(e,t,o)=>{const n=K(yl,o);return{offset:Dn(e,t),classesOn:q(o,a),classesOff:q(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},Sl=()=>wl(0,0,{}),Cl=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),kl=xe([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Ol=kl.southeast,_l=kl.southwest,Tl=kl.northeast,El=kl.northwest,Al=kl.south,Ml=kl.north,Dl=kl.east,Bl=kl.west,Fl=(e,t)=>J(["left","right","top","bottom"],(o=>fe(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Il="layout",Rl=e=>e.x,Nl=(e,t)=>e.x+e.width/2-t.width/2,zl=(e,t)=>e.x+e.width-t.width,Ll=(e,t)=>e.y-t.height,Vl=e=>e.y+e.height,Hl=(e,t)=>e.y+e.height/2-t.height/2,Pl=(e,t,o)=>Cl(Rl(e),Vl(e),o.southeast(),Ol(),"southeast",Fl(e,{left:1,top:3}),Il),Ul=(e,t,o)=>Cl(zl(e,t),Vl(e),o.southwest(),_l(),"southwest",Fl(e,{right:0,top:3}),Il),Wl=(e,t,o)=>Cl(Rl(e),Ll(e,t),o.northeast(),Tl(),"northeast",Fl(e,{left:1,bottom:2}),Il),$l=(e,t,o)=>Cl(zl(e,t),Ll(e,t),o.northwest(),El(),"northwest",Fl(e,{right:0,bottom:2}),Il),Gl=(e,t,o)=>Cl(Nl(e,t),Ll(e,t),o.north(),Ml(),"north",Fl(e,{bottom:2}),Il),jl=(e,t,o)=>Cl(Nl(e,t),Vl(e),o.south(),Al(),"south",Fl(e,{top:3}),Il),ql=(e,t,o)=>Cl((e=>e.x+e.width)(e),Hl(e,t),o.east(),Dl(),"east",Fl(e,{left:0}),Il),Xl=(e,t,o)=>Cl(((e,t)=>e.x-t.width)(e,t),Hl(e,t),o.west(),Bl(),"west",Fl(e,{right:1}),Il),Yl=()=>[Pl,Ul,Wl,$l,jl,Gl,ql,Xl],Kl=()=>[Ul,Pl,$l,Wl,jl,Gl,ql,Xl],Jl=()=>[Wl,$l,Pl,Ul,Gl,jl],Ql=()=>[$l,Wl,Ul,Pl,Gl,jl],Zl=()=>[Pl,Ul,Wl,$l,jl,Gl],ec=()=>[Ul,Pl,$l,Wl,jl,Gl],tc="data-alloy-placement",oc=e=>Ko(e,tc),nc="layout-inset",sc=e=>e.x,rc=(e,t)=>e.x+e.width/2-t.width/2,ac=(e,t)=>e.x+e.width-t.width,ic=e=>e.y,lc=(e,t)=>e.y+e.height-t.height,cc=(e,t)=>e.y+e.height/2-t.height/2,dc=(e,t,o)=>Cl(ac(e,t),lc(e,t),o.insetSouthwest(),El(),"southwest",Fl(e,{right:0,bottom:3}),nc),uc=(e,t,o)=>Cl(sc(e),lc(e,t),o.insetSoutheast(),Tl(),"southeast",Fl(e,{left:1,bottom:3}),nc),mc=(e,t,o)=>Cl(ac(e,t),ic(e),o.insetNorthwest(),_l(),"northwest",Fl(e,{right:0,top:2}),nc),gc=(e,t,o)=>Cl(sc(e),ic(e),o.insetNortheast(),Ol(),"northeast",Fl(e,{left:1,top:2}),nc),pc=(e,t,o)=>Cl(rc(e,t),ic(e),o.insetNorth(),Al(),"north",Fl(e,{top:2}),nc),hc=(e,t,o)=>Cl(rc(e,t),lc(e,t),o.insetSouth(),Ml(),"south",Fl(e,{bottom:3}),nc),fc=(e,t,o)=>Cl(ac(e,t),cc(e,t),o.insetEast(),Bl(),"east",Fl(e,{right:0}),nc),bc=(e,t,o)=>Cl(sc(e),cc(e,t),o.insetWest(),Dl(),"west",Fl(e,{left:1}),nc),vc=e=>{switch(e){case"north":return pc;case"northeast":return gc;case"northwest":return mc;case"south":return hc;case"southeast":return uc;case"southwest":return dc;case"east":return fc;case"west":return bc}},xc=(e,t,o,n,s)=>oc(n).map(vc).getOr(pc)(e,t,o,n,s),yc=e=>{switch(e){case"north":return hc;case"northeast":return uc;case"northwest":return dc;case"south":return pc;case"southeast":return gc;case"southwest":return mc;case"east":return bc;case"west":return fc}},wc=(e,t,o,n,s)=>oc(n).map(yc).getOr(pc)(e,t,o,n,s),Sc=(e,t)=>{((e,t)=>{const o=Cn.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);mn(e,"max-height",o+"px")})(e,Math.floor(t))},Cc=y(((e,t)=>{Sc(e,t),gn(e,{"overflow-x":"hidden","overflow-y":"auto"})})),kc=y(((e,t)=>{Sc(e,t)})),Oc=y(((e,t)=>{((e,t)=>{const o=Tn.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);mn(e,"max-width",o+"px")})(e,Math.floor(t))}));var _c;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(_c||(_c={}));const Tc="data-alloy-vertical-dir",Ec=e=>gs(e,(e=>no(e)&&Yo(e,"data-alloy-vertical-dir")===_c.BottomToTop));var Ac;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(Ac||(Ac={}));const Mc={init:()=>Dc({readState:y("No State required")})},Dc=e=>e,Bc={can:E,abort:T,run:b},Fc=e=>{if(!ve(e,"can")&&!ve(e,"abort")&&!ve(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Bc,...e}},Ic=(e,t)=>{Lc(e,e.element,t,{})},Rc=(e,t,o)=>{Lc(e,e.element,t,o)},Nc=e=>{Ic(e,Na())},zc=(e,t,o)=>{Lc(e,t,o,{})},Lc=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Vc=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Hc=e=>la(e),Pc=(e,t)=>({key:e,value:Fc({abort:t})}),Uc=e=>({key:e,value:Fc({run:(e,t)=>{t.event.prevent()}})}),Wc=(e,t)=>({key:e,value:Fc({run:t})}),$c=(e,t,o)=>({key:e,value:Fc({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Gc=e=>t=>({key:e,value:Fc({run:(e,o)=>{Vs(e,o)&&t(e,o)}})}),jc=(e,t,o)=>((e,t)=>Wc(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Vc(t,t.element,e,n)}))})))(e,t.partUids[o]),qc=(e,t)=>Wc(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>bs(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),Xc=e=>Wc(e,((e,t)=>{t.cut()})),Yc=e=>Wc(e,((e,t)=>{t.stop()})),Kc=(e,t)=>Gc(e)(t),Jc=Gc(qa()),Qc=Gc(Xa()),Zc=Gc(Ua()),ed=(e=>t=>Wc(e,t))(Na()),td=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),od=(e,t,o)=>Zc(((n,s)=>{o(n,e,t)})),nd=e=>({key:e,value:void 0}),sd=(e,t,o,n,s,r,a)=>{const i=e=>ve(e,o)?e[o]():A.none(),l=le(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ki(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...le(r,((e,t)=>Oi(e,t))),...l,revoke:C(nd,o),config:t=>{const n=Er(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:st((()=>Er(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>Ue(i(e),fe(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>td({}))),name:y(o),handlers:e=>i(e).map((e=>fe(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},rd=e=>la(e),ad=br([Rr("fields"),Rr("name"),Qr("active",{}),Qr("apis",{}),Qr("state",Mc),Qr("extra",{})]),id=e=>{const t=Er("Creating behaviour: "+e.name,ad,e);return((e,t,o,n,s,r)=>{const a=br(e),i=Jr(t,[(l=e,Gr("config",br(l)))]);var l;return sd(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},ld=br([Rr("branchKey"),Rr("branches"),Rr("name"),Qr("active",{}),Qr("apis",{}),Qr("state",Mc),Qr("extra",{})]),cd=e=>{const t=Er("Creating behaviour: "+e.name,ld,e);return((e,t,o,n,s,r)=>{const a=e,i=Jr(t,[Gr("config",e)]);return sd(a,i,t,o,n,s,r)})(Mr(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},dd=y(void 0),ud=(e,t)=>{const o=((e,t)=>{const o=Hc(t);return id({fields:[Rr("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:Mc}}},md=(e,t)=>{t.ignore||(No(e.element),t.onFocus(e))};var gd=Object.freeze({__proto__:null,focus:md,blur:(e,t)=>{t.ignore||zo(e.element)},isFocused:e=>Lo(e.element)}),pd=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return td(o)},events:e=>Hc([Wc(Ba(),((t,o)=>{md(t,e),o.stop()}))].concat(e.stopMousedown?[Wc(pa(),((e,t)=>{t.event.prevent()}))]:[]))}),hd=[bi("onFocus"),Qr("stopMousedown",!1),Qr("ignore",!1)];const fd=id({fields:hd,name:"focusing",active:pd,apis:gd}),bd=[8],vd=[9],xd=[13],yd=[27],wd=[32],Sd=[37],Cd=[38],kd=[39],Od=[40],_d=y("tooltipping.close.all"),Td=y("dismiss.popups"),Ed=y("reposition.popups"),Ad=y("mouse.released"),Md=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return $(n.concat(s),o)},Dd=(e,t,o)=>{const n=Y(e.slice(0,t));return $(n,o)},Bd=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return $(s.concat(n),o)},Fd=(e,t,o)=>{const n=e.slice(t+1);return $(n,o)},Id=e=>t=>{const o=t.raw;return I(e,o.which)},Rd=e=>t=>X(e,(e=>e(t))),Nd=e=>!0===e.raw.shiftKey,zd=e=>!0===e.raw.ctrlKey,Ld=k(Nd),Vd=(e,t)=>({matches:e,classification:t}),Hd=(e,t,o,n)=>{const s=ms(e.element,"."+t.highlightClass);V(s,(o=>{R(n,(e=>yt(e.element,o)))||(ss(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Ic(o,ni())})))}))},Pd=(e,t,o,n)=>{Hd(e,t,0,[n]),Ud(e,t,o,n)||(os(n.element,t.highlightClass),t.onHighlight(e,n),Ic(n,oi()))},Ud=(e,t,o,n)=>rs(n.element,t.highlightClass),Wd=(e,t,o)=>Xn(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),$d=(e,t,o)=>{const n=ms(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Gd=(e,t,o,n)=>{const s=ms(e.element,"."+t.itemClass);return G(s,(e=>rs(e,t.highlightClass))).bind((t=>{const o=Fe(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},jd=(e,t,o)=>{const n=ms(e.element,"."+t.itemClass);return Pe(L(n,(t=>e.getSystem().getByDom(t).toOptional())))};var qd=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Hd(e,t,0,[]),dehighlight:(e,t,o,n)=>{Ud(e,t,o,n)&&(ss(n.element,t.highlightClass),t.onDehighlight(e,n),Ic(n,ni()))},highlight:Pd,highlightFirst:(e,t,o)=>{Wd(e,t).each((n=>{Pd(e,t,o,n)}))},highlightLast:(e,t,o)=>{$d(e,t).each((n=>{Pd(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=ms(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>Ae.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Pd(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=jd(e,t);$(s,n).each((n=>{Pd(e,t,o,n)}))},isHighlighted:Ud,getHighlighted:(e,t,o)=>Xn(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Wd,getLast:$d,getPrevious:(e,t,o)=>Gd(e,t,0,-1),getNext:(e,t,o)=>Gd(e,t,0,1),getCandidates:jd}),Xd=[Rr("highlightClass"),Rr("itemClass"),bi("onHighlight"),bi("onDehighlight")];const Yd=id({fields:Xd,name:"highlighting",apis:qd}),Kd=(e,t,o)=>{t.exists((e=>o.exists((t=>yt(t,e)))))||Rc(e,Ja(),{prevFocus:t,newFocus:o})},Jd=()=>{const e=e=>Ho(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);Kd(t,n,s)}}},Qd=()=>{const e=e=>Yd.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Yd.highlight(t,e)}));const s=e(t);Kd(t,n,s)}}},Zd=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,$(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([Qr("focusManager",Jd()),Zr("focusInside","onFocus",kr((e=>I(["onFocus","onEnterOrSpace","onApi"],e)?Ae.value(e):Ae.error("Invalid value for focusInside")))),wi("handler",a),wi("state",t),wi("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==Ei.OnFocusMode?A.none():s(e).map((o=>Wc(Ba(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Wc(wa(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Id(wd.concat(xd))(n.event);e.focusInside===Ei.OnEnterOrSpaceMode&&r&&Vs(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Wc(Sa(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Hc(a.toArray().concat(i))}};return a},eu=e=>{const t=[$r("onEscape"),$r("onEnter"),Qr("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Qr("firstTabstop",0),Qr("useTabstopAt",E),$r("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>Yn(t,e))).getOr(t);return kn(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>Yn(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=ms(e.element,t.selector),s=P(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=P(ms(e.element,s.selector),(e=>o(s,e)));return n(e,s).bind((t=>G(a,C(yt,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?Md:Dd;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?Bd:Fd;return r(e,0,o,n)},l=y([Vd(Rd([Nd,Id(vd)]),a),Vd(Id(vd),i),Vd(Rd([Ld,Id(xd)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=y([Vd(Id(yd),((e,t,o)=>(e.getSystem().broadcastOn([_d()],{closedTooltip:()=>{t.stop()}}),t.isStopped()?A.none():o.onEscape.bind((o=>o(e,t)))))),Vd(Id(vd),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>go(e))(e).bind(yo).exists((t=>yt(t,e))))(n)?a:i)(e,t,o)))))]);return Zd(t,Mc.init,l,c,(()=>A.some(s)))};var tu=eu(Fr("cyclic",T)),ou=eu(Fr("cyclic",E));const nu=e=>io("input")(e)&&"radio"!==Yo(e,"type")||io("textarea")(e),su=(e,t,o)=>nu(o)&&Id(wd)(t.event)?A.none():((e,t,o)=>(zc(e,o,Na()),A.some(!0)))(e,0,o),ru=(e,t)=>A.some(!0),au=[Qr("execute",su),Qr("useSpace",!1),Qr("useEnter",!0),Qr("useControlEnter",!1),Qr("useDown",!1)],iu=(e,t,o)=>o.execute(e,t,e.element);var lu=Zd(au,Mc.init,((e,t,o,n)=>{const s=o.useSpace&&!nu(e.element)?wd:[],r=o.useEnter?xd:[],a=o.useDown?Od:[],i=s.concat(r).concat(a);return[Vd(Id(i),iu)].concat(o.useControlEnter?[Vd(Rd([zd,Id(xd)]),iu)]:[])}),((e,t,o,n)=>o.useSpace&&!nu(e.element)?[Vd(Id(wd),ru)]:[]),(()=>A.none()));const cu=()=>{const e=Xe();return Dc({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var du=Object.freeze({__proto__:null,flatgrid:cu,init:e=>e.state(e)});const uu=e=>(t,o,n,s)=>{const r=e(t.element);return hu(r,t,o,n,s)},mu=(e,t)=>{const o=Ln(e,t);return uu(o)},gu=(e,t)=>{const o=Ln(t,e);return uu(o)},pu=e=>(t,o,n,s)=>hu(e,t,o,n,s),hu=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),fu=pu,bu=pu,vu=pu,xu=(e,t,o)=>{const n=ms(e,o);return(e=>G(e,(e=>yt(e,t))).map((t=>({index:t,candidates:e}))))(P(n,_n))},yu=(e,t)=>G(e,(e=>yt(t,e))),wu=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),Su=(e,t,o,n,s)=>wu(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Fe(r,s,0,a-1);return A.some({row:t,column:i})})),Cu=(e,t,o,n,s)=>wu(e,t,n,((t,r)=>{const a=Fe(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Ie(r,0,i-1);return A.some({row:a,column:l})})),ku=[Rr("selector"),Qr("execute",su),vi("onEscape"),Qr("captureTab",!1),Ci()],Ou=(e,t,o)=>{Xn(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},_u=e=>(t,o,n,s)=>xu(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Tu=(e,t,o)=>o.captureTab?A.some(!0):A.none(),Eu=_u(((e,t,o,n)=>Su(e,t,o,n,-1))),Au=_u(((e,t,o,n)=>Su(e,t,o,n,1))),Mu=_u(((e,t,o,n)=>Cu(e,t,o,n,-1))),Du=_u(((e,t,o,n)=>Cu(e,t,o,n,1))),Bu=y([Vd(Id(Sd),mu(Eu,Au)),Vd(Id(kd),gu(Eu,Au)),Vd(Id(Cd),fu(Mu)),Vd(Id(Od),bu(Du)),Vd(Rd([Nd,Id(vd)]),Tu),Vd(Rd([Ld,Id(vd)]),Tu),Vd(Id(wd.concat(xd)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>Yn(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Fu=y([Vd(Id(yd),((e,t,o)=>o.onEscape(e,t))),Vd(Id(wd),ru)]);var Iu=Zd(ku,cu,Bu,Fu,(()=>A.some(Ou)));const Ru=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===eo(n)&&"disabled"===Yo(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return xu(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Nu=(e,t,o,n)=>Ru(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Ie(t+o,n,s);return i===e?A.from(r):a(i)})),zu=(e,t,o,n)=>Ru(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Fe(t,o,n,s);return i===e?A.none():a(i)})),Lu=[Rr("selector"),Qr("getInitial",A.none),Qr("execute",su),vi("onEscape"),Qr("executeOnMove",!1),Qr("allowVertical",!0),Qr("allowHorizontal",!0),Qr("cycles",!0)],Vu=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>Yn(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Hu=(e,t,o)=>{t.getInitial(e).orThunk((()=>Xn(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Pu=(e,t,o)=>(o.cycles?zu:Nu)(e,o.selector,t,-1),Uu=(e,t,o)=>(o.cycles?zu:Nu)(e,o.selector,t,1),Wu=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Vu(t,o,n):A.some(!0))),$u=y([Vd(Id(wd),ru),Vd(Id(yd),((e,t,o)=>o.onEscape(e,t)))]);var Gu=Zd(Lu,Mc.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Sd:[]].concat(o.allowVertical?Cd:[]),r=[...o.allowHorizontal?kd:[]].concat(o.allowVertical?Od:[]);return[Vd(Id(s),Wu(mu(Pu,Uu))),Vd(Id(r),Wu(gu(Pu,Uu))),Vd(Id(xd),Vu),Vd(Id(wd),Vu)]}),$u,(()=>A.some(Hu)));const ju=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),qu=(e,t,o,n)=>{const s=e[t].length,r=Fe(o,n,0,s-1);return ju(e,t,r)},Xu=(e,t,o,n)=>{const s=Fe(o,n,0,e.length-1),r=e[s].length,a=Ie(t,0,r-1);return ju(e,s,a)},Yu=(e,t,o,n)=>{const s=e[t].length,r=Ie(o+n,0,s-1);return ju(e,t,r)},Ku=(e,t,o,n)=>{const s=Ie(o+n,0,e.length-1),r=e[s].length,a=Ie(t,0,r-1);return ju(e,s,a)},Ju=[Pr("selectors",[Rr("row"),Rr("cell")]),Qr("cycles",!0),Qr("previousSelector",A.none),Qr("execute",su)],Qu=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return Xn(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Zu=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return Yn(n,s.selectors.row).bind((e=>{const t=ms(e,s.selectors.cell);return yu(t,n).bind((t=>{const n=ms(o,s.selectors.row);return yu(n,e).bind((e=>{const o=((e,t)=>L(e,(e=>ms(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},em=Zu(((e,t,o)=>qu(e,t,o,-1)),((e,t,o)=>Yu(e,t,o,-1))),tm=Zu(((e,t,o)=>qu(e,t,o,1)),((e,t,o)=>Yu(e,t,o,1))),om=Zu(((e,t,o)=>Xu(e,o,t,-1)),((e,t,o)=>Ku(e,o,t,-1))),nm=Zu(((e,t,o)=>Xu(e,o,t,1)),((e,t,o)=>Ku(e,o,t,1))),sm=y([Vd(Id(Sd),mu(em,tm)),Vd(Id(kd),gu(em,tm)),Vd(Id(Cd),fu(om)),Vd(Id(Od),bu(nm)),Vd(Id(wd.concat(xd)),((e,t,o)=>Ho(e.element).bind((n=>o.execute(e,t,n)))))]),rm=y([Vd(Id(wd),ru)]);var am=Zd(Ju,Mc.init,sm,rm,(()=>A.some(Qu)));const im=[Rr("selector"),Qr("execute",su),Qr("moveOnTab",!1)],lm=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),cm=(e,t,o)=>{Xn(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},dm=(e,t,o)=>zu(e,o.selector,t,-1),um=(e,t,o)=>zu(e,o.selector,t,1),mm=y([Vd(Id(Cd),vu(dm)),Vd(Id(Od),vu(um)),Vd(Rd([Nd,Id(vd)]),((e,t,o,n)=>o.moveOnTab?vu(dm)(e,t,o,n):A.none())),Vd(Rd([Ld,Id(vd)]),((e,t,o,n)=>o.moveOnTab?vu(um)(e,t,o,n):A.none())),Vd(Id(xd),lm),Vd(Id(wd),lm)]),gm=y([Vd(Id(wd),ru)]);var pm=Zd(im,Mc.init,mm,gm,(()=>A.some(cm)));const hm=[vi("onSpace"),vi("onEnter"),vi("onShiftEnter"),vi("onLeft"),vi("onRight"),vi("onTab"),vi("onShiftTab"),vi("onUp"),vi("onDown"),vi("onEscape"),Qr("stopSpaceKeyup",!1),$r("focusIn")];var fm=Zd(hm,Mc.init,((e,t,o)=>[Vd(Id(wd),o.onSpace),Vd(Rd([Ld,Id(xd)]),o.onEnter),Vd(Rd([Nd,Id(xd)]),o.onShiftEnter),Vd(Rd([Nd,Id(vd)]),o.onShiftTab),Vd(Rd([Ld,Id(vd)]),o.onTab),Vd(Id(Cd),o.onUp),Vd(Id(Od),o.onDown),Vd(Id(Sd),o.onLeft),Vd(Id(kd),o.onRight),Vd(Id(wd),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Vd(Id(wd),ru)]:[],Vd(Id(yd),o.onEscape)]),(e=>e.focusIn));const bm=tu.schema(),vm=ou.schema(),xm=Gu.schema(),ym=Iu.schema(),wm=am.schema(),Sm=lu.schema(),Cm=pm.schema(),km=fm.schema(),Om=cd({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:bm,cyclic:vm,flow:xm,flatgrid:ym,matrix:wm,execution:Sm,menu:Cm,special:km}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ve(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:du}),_m=Ne("alloy-premade"),Tm=e=>(Object.defineProperty(e.element.dom,_m,{value:e.uid,writable:!0}),ia(_m,e)),Em=e=>fe(e,_m),Am=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:ki(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),Mm=e=>e.getSystem().isConnected(),Dm=e=>{Ic(e,Xa());const t=e.components();V(t,Dm)},Bm=e=>{const t=e.components();V(t,Bm),Ic(e,qa())},Fm=(e,t)=>{e.getSystem().addToWorld(t),an(e.element)&&Bm(t)},Im=e=>{Dm(e),e.getSystem().removeFromWorld(e)},Rm=(e,t)=>{$o(e.element,t.element)},Nm=(e,t)=>{zm(e,t,$o)},zm=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),an(e.element)&&Bm(t),e.syncComponents()},Lm=e=>{Dm(e),en(e.element),e.getSystem().removeFromWorld(e)},Vm=e=>{const t=go(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));Lm(e),t.each((e=>{e.syncComponents()}))},Hm=e=>{const t=e.components();V(t,Lm),Zo(e.element),e.syncComponents()},Pm=(e,t)=>{Wm(e,t,$o)},Um=(e,t)=>{Wm(e,t,Uo)},Wm=(e,t,o)=>{o(e,t.element);const n=vo(t.element);V(n,(e=>{t.getByDom(e).each(Bm)}))},$m=e=>{const t=vo(e.element);V(t,(t=>{e.getByDom(t).each(Dm)})),en(e.element)},Gm=(e,t,o)=>{o.fold((()=>$o(e,t)),(e=>{yt(e,t)||(Po(e,t),en(e))}))},jm=(e,t,o)=>{const n=L(t,o),s=vo(e);return V(s.slice(n.length),en),n},qm=(e,t,o,n)=>{const s=xo(e,t),r=n(o,s),a=((e,t,o)=>xo(e,t).map((e=>{if(o.exists((t=>!yt(t,e)))){const t=o.map(eo).getOr("span"),n=it(t);return Po(e,n),n}return e})))(e,t,s);return Gm(e,r.element,a),r},Xm=(e,t)=>{const o=ko(t),n=Vo(o).bind((e=>{const o=t=>yt(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ct(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{Vo(o).filter((t=>yt(t,e))).fold((()=>{No(e)}),b)})),s},Ym=(e,t)=>{Xm((()=>{((e,t,o)=>{const n=e.components();(e=>{V(e.components(),(e=>en(e.element))),Zo(e.element),e.syncComponents()})(e);const s=o(t),r=K(n,s);V(r,(t=>{Dm(t),e.getSystem().removeFromWorld(t)})),V(s,(t=>{Mm(t)?Rm(e,t):(e.getSystem().addToWorld(t),Rm(e,t),an(e.element)&&Bm(t))})),e.syncComponents()})(e,t,(()=>L(t,e.getSystem().build)))}),e.element)},Km=(e,t)=>{Xm((()=>{((o,n)=>{const s=o.components(),r=q(n,(e=>Em(e).toArray()));V(s,(e=>{I(r,e)||Im(e)}));const a=((e,t,o)=>jm(e,t,((t,n)=>qm(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),i=K(s,a);V(i,(e=>{Mm(e)&&Im(e)})),V(a,(e=>{Mm(e)||Fm(o,e)})),o.syncComponents()})(e,t)}),e.element)},Jm=(e,t,o,n)=>{Im(t);const s=qm(e.element,o,n,e.getSystem().buildOrPatch);Fm(e,s),e.syncComponents()},Qm=(e,t,o)=>{const n=e.getSystem().build(o);zm(e,n,t)},Zm=(e,t,o,n)=>{Vm(t),Qm(e,((e,t)=>((e,t,o)=>{xo(e,o).fold((()=>{$o(e,t)}),(e=>{Po(e,t)}))})(e,t,o)),n)},eg=(e,t)=>e.components(),tg=(e,t,o,n,s)=>{const r=eg(e);return A.from(r[n]).map((o=>(s.fold((()=>Vm(o)),(s=>{(t.reuseDom?Jm:Zm)(e,o,n,s)})),o)))};var og=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Qm(e,$o,n)},prepend:(e,t,o,n)=>{Qm(e,Wo,n)},remove:(e,t,o,n)=>{const s=eg(e),r=$(s,(e=>yt(n.element,e.element)));r.each(Vm)},replaceAt:tg,replaceBy:(e,t,o,n,s)=>{const r=eg(e);return G(r,n).bind((o=>tg(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Km:Ym)(e,n),contents:eg});const ng=id({fields:[na("reuseDom",!0)],name:"replacing",apis:og}),sg=Hc([(e=>({key:e,value:Fc({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>yt(t,e.element)&&!yt(t,o))(e,n,s)||(console.warn(Ba()+" did not get interpreted by the desired target. \nOriginator: "+si(n)+"\nTarget: "+si(s)+"\nCheck the "+Ba()+" event handlers"),!1)}})}))(Ba())]);var rg=Object.freeze({__proto__:null,events:sg});const ag=y("alloy-id-"),ig=y("data-alloy-id"),lg=ag(),cg=ig(),dg=(e,t)=>{Object.defineProperty(e.dom,cg,{value:t,writable:!0})},ug=e=>{const t=no(e)?e.dom[cg]:null;return A.from(t)},mg=e=>Ne(e),gg=w,pg=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+si(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},hg=pg(),fg=(e,t)=>{const o={};return ie(e,((e,n)=>{ie(e,((e,s)=>{const r=fe(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},bg=e=>e.cHandler,vg=(e,t)=>({name:e,handler:t}),xg=(e,t)=>{const o={};return V(e,(e=>{o[e.name()]=e.handlers(t)})),o},yg=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=Z(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return Ae.value(s)}catch(e){return Ae.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=(e=>(...t)=>W(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=(e=>(...t)=>W(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{V(e,(e=>{e.run.apply(void 0,t)}))}}})(L(e,(e=>e.handler))))):((e,t)=>Ae.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(L(t,(e=>e.name)),null,2)]))(o,e)},wg=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return V(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,Ae.error(j(n))):((e,t)=>0===e.length?Ae.value(t):Ae.value(Le(t,Ve.apply(void 0,e))))(o.values,t);var n})(ge(e,((e,o)=>(1===e.length?Ae.value(e[0].handler):yg(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?P(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return ia(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Sg="alloy.base.behaviour",Cg=vr([Br("dom","dom",{tag:"required",process:{}},vr([Rr("tag"),Qr("styles",{}),Qr("classes",[]),Qr("attributes",{}),$r("value"),$r("innerHtml")])),Rr("components"),Rr("uid"),Qr("events",{}),Qr("apis",{}),Br("eventOrder","eventOrder",(Rg={[Na()]:["disabling",Sg,"toggling","typeaheadevents"],[Ba()]:[Sg,"focusing","keying"],[Ua()]:[Sg,"disabling","toggling","representing","tooltipping"],[Ca()]:[Sg,"representing","streaming","invalidating"],[Xa()]:[Sg,"representing","item-events","toolbar-button-events","tooltipping"],[pa()]:["focusing",Sg,"item-type-events"],[da()]:["focusing",Sg,"item-type-events"],[va()]:["item-type-events","tooltipping"],[Ra()]:["receiving","reflecting","tooltipping"]},cr(y(Rg))),Zs()),$r("domModification")]),kg=e=>e.events,Og=(e,t)=>{const o=re(e),n=re(t),s=K(n,o),r=(e=>{const o={},n={};return ue(e,((e,o)=>!be(t,o)||e!==t[o]),de(o),de(n)),{t:o,f:n}})(e).t;return{toRemove:s,toSet:r}},_g=(e,t)=>{const o=t.filter((t=>eo(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>be(e.dom,_m))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>W(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=Og(e.attributes,s),i=vn(t),{toSet:l,toRemove:c}=Og(e.styles,i),d=ls(t),u=K(d,e.classes),m=K(e.classes,d);return V(a,(e=>Qo(t,e))),Xo(t,r),as(t,m),is(t,u),V(c,(e=>yn(t,e))),gn(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{jm(e,t,((t,o)=>{const n=xo(e,o);return Gm(e,t,n),t}))})(t,o)}),(e=>{nn(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==cs(o)&&ds(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=it(e.tag);Xo(t,e.attributes),as(t,e.classes),gn(t,e.styles),e.innerHtml.each((e=>nn(t,e)));const o=e.domChildren;return Go(t,o),e.value.each((e=>{ds(t,e)})),t})(e)));return dg(o,e.uid),o},Tg=e=>{const t=(e=>{const t=fe(e,"behaviours").getOr({});return q(re(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=L(t,(e=>Jr(e.name(),[Rr("config"),Qr("state",Mc)]))),n=_r("component.behaviours",vr(o),e.behaviours).fold((t=>{throw new Error(Ar(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:le(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},Eg=(e,t)=>{const o=()=>m,n=ye(hg),s=Tr((e=>_r("custom.definition",Cg,e))(e)),r=Tg(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:L(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>td({})),td))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};V(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=fg(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return td({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=_g(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":kg(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...xg(t,e)};return fg(n,vg)})(e,o,n);return wg(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=ye(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(pg(o))},element:c,syncComponents:()=>{const e=vo(c),t=q(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},Ag=e=>{const t=lt(e);return Mg({element:t})},Mg=e=>{const t=Er("external.component",br([Rr("element"),$r("uid")]),e),o=ye(pg()),n=t.uid.getOrThunk((()=>mg("external")));dg(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(pg((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return Tm(s)},Dg=mg,Bg=(e,t)=>Em(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=gg(e),s=((e,t)=>{const o=fe(e,"components").getOr([]);return t.fold((()=>L(o,Fg)),(e=>L(o,((t,o)=>Bg(t,xo(e,o))))))})(n,t),r={...n,events:{...rg,...o},components:s};return Ae.value(Eg(r,t))})((e=>be(e,"uid"))(e)?e:{uid:Dg(""),...e},t).getOrDie())),Fg=e=>Bg(e,A.none()),Ig=Tm;var Rg,Ng=Object.freeze({__proto__:null,block:(e,t,o,n)=>{qo(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=rd([Om.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),fd.config({})]),a=n(s,r),i=s.getSystem().build(a);ng.append(s,Ig(i)),i.hasConfigured(Om)&&t.focus&&Om.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>ng.remove(s,i)))},unblock:(e,t,o)=>{Qo(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),zg=[sa("getRoot",A.none),na("focus",!0),bi("onBlock"),bi("onUnblock")];const Lg=id({fields:zg,name:"blocking",apis:Ng,state:Object.freeze({__proto__:null,init:()=>{const e=je((e=>e.destroy()));return Dc({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})});var Vg=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Hg=[Rr("find")],Pg=id({fields:Hg,name:"composing",apis:Vg});var Ug=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),Wg=[Nr("others",Or(Ae.value,Zs()))],$g=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===re(t.others).length)throw new Error("Cannot find any known coupled components");return fe(e,o)},o=y({});return Dc({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(fe(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=fe(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const Gg=id({fields:Wg,name:"coupling",apis:Ug,state:$g}),jg=["input","button","textarea","select"],qg=(e,t,o)=>{(t.disabled()?Zg:ep)(e,t)},Xg=(e,t)=>!0===t.useNative&&I(jg,eo(e.element)),Yg=e=>{qo(e.element,"disabled","disabled")},Kg=e=>{Qo(e.element,"disabled")},Jg=e=>{qo(e.element,"aria-disabled","true")},Qg=e=>{qo(e.element,"aria-disabled","false")},Zg=(e,t,o)=>{t.disableClass.each((t=>{os(e.element,t)})),(Xg(e,t)?Yg:Jg)(e),t.onDisabled(e)},ep=(e,t,o)=>{t.disableClass.each((t=>{ss(e.element,t)})),(Xg(e,t)?Kg:Qg)(e),t.onEnabled(e)},tp=(e,t)=>Xg(e,t)?(e=>Jo(e.element,"disabled"))(e):(e=>"true"===Yo(e.element,"aria-disabled"))(e);var op=Object.freeze({__proto__:null,enable:ep,disable:Zg,isDisabled:tp,onLoad:qg,set:(e,t,o,n)=>{(n?Zg:ep)(e,t)}}),np=Object.freeze({__proto__:null,exhibit:(e,t)=>td({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Hc([Pc(Na(),((t,o)=>tp(t,e))),od(e,t,qg)])}),sp=[sa("disabled",T),Qr("useNative",!0),$r("disableClass"),bi("onDisabled"),bi("onEnabled")];const rp=id({fields:sp,name:"disabling",active:np,apis:op}),ap=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},ip=(e,t)=>{pn(e,(e=>({...e,position:A.some(e.position)}))(t))},lp=(e,t)=>{const o=e.element;os(o,t.transitionClass),ss(o,t.fadeOutClass),os(o,t.fadeInClass),t.onShow(e)},cp=(e,t)=>{const o=e.element;os(o,t.transitionClass),ss(o,t.fadeInClass),os(o,t.fadeOutClass),t.onHide(e)},dp=(e,t)=>e.y>=t.y,up=(e,t)=>e.bottom<=t.bottom,mp=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),gp=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),pp=e=>e.box.x-e.win.x,hp=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(y(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return Dn(e.bounds.x,o)})(o,t);return{box:Is(n.left,n.top,En(e),kn(e)),location:o.location}})),fp=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(y(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return Dn(e.x,o)})(t,o),a=Is(r.left,r.top,t.width,t.height);n.setInitialPos({style:vn(e),position:hn(e,"position")||"static",bounds:a,location:s.location})},bp=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=Hs(e).getOr(ln()),r=Rs(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:ap("absolute",fe(n.style,"left").map((e=>t.x-r.x)),fe(n.style,"top").map((e=>t.y-r.y+a)),fe(n.style,"right").map((e=>r.right-t.right)),fe(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),vp=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:ap("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:ap("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},xp=(e,t,o)=>{const n=e.element;return He(bn(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>hp(e,t,o).filter((({box:e})=>((e,t,o)=>X(e,(e=>{switch(e){case"bottom":return up(t,o.bounds);case"top":return dp(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>bp(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>hp(e,t,o))).bind((({box:e,location:o})=>{const n=Ls(),s=pp({win:n,box:e}),r="top"===o?mp(n,s,t):gp(n,s,t);return vp(r)})))))(n,t,o):((e,t,o)=>{const n=Rs(e),s=Ls(),r=((e,t,o)=>{const n=t.win,s=t.box,r=pp(t);return se(e,(e=>{switch(e){case"bottom":return up(s,o.bounds)?A.none():A.some(gp(n,r,o));case"top":return dp(s,o.bounds)?A.none():A.some(mp(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(fp(e,n,t,o,r),vp(r)):A.none()})(n,t,o)},yp=(e,t,o)=>{o.setDocked(!1),V(["left","right","top","bottom","position"],(t=>yn(e.element,t))),t.onUndocked(e)},wp=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),ip(e.element,n),(s?t.onDocked:t.onUndocked)(e)},Sp=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(as(e.element,[t.fadeOutClass]),t.onHide(e)):(a?lp:cp)(e,t))}))}))},Cp=(e,t,o,n,s)=>{Sp(e,t,o,n,!0),wp(e,t,o,s.positionCss)},kp=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);Sp(e,t,o,n),xp(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return yp(e,t,o);case"absolute":return wp(e,t,o,s.positionCss);case"fixed":Cp(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},Op=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return hp(n,t,o).bind((({box:e})=>bp(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":yp(e,t,o);break;case"absolute":wp(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{is(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),kp(e,t,o)})(e,t,o)},_p=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Rs(e),r=Ls(),a=n(r,pp({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>fp(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),vp(a)):A.none()})(t.element,s,n,e).each((e=>{Cp(t,o,n,s,e)}))},Tp=_p(mp),Ep=_p(gp);var Ap=Object.freeze({__proto__:null,refresh:kp,reset:Op,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:Tp,forceDockToBottom:Ep}),Mp=Object.freeze({__proto__:null,events:(e,t)=>Hc([Kc(Ta(),((o,n)=>{e.contextual.each((e=>{rs(o.element,e.transitionClass)&&(is(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Wc(Ga(),((o,n)=>{kp(o,e,t)})),Wc(Za(),((o,n)=>{kp(o,e,t)})),Wc(ja(),((o,n)=>{Op(o,e,t)}))])}),Dp=[Jr("contextual",[Lr("fadeInClass"),Lr("fadeOutClass"),Lr("transitionClass"),Hr("lazyContext"),bi("onShow"),bi("onShown"),bi("onHide"),bi("onHidden")]),sa("lazyViewport",(()=>({bounds:Ls(),optScrollEnv:A.none()}))),ra("modes",["top","bottom"],or),bi("onDocked"),bi("onUndocked")];const Bp=id({fields:Dp,name:"docking",active:Mp,apis:Ap,state:Object.freeze({__proto__:null,init:e=>{const t=ye(!1),o=ye(!0),n=Xe(),s=ye(e.modes);return Dc({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),Fp=xe([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),Ip=e=>t=>t.translate(-e.left,-e.top),Rp=e=>t=>t.translate(e.left,e.top),Np=e=>(t,o)=>W(e,((e,t)=>t(e)),Dn(t,o)),zp=(e,t,o)=>e.fold(Np([Rp(o),Ip(t)]),Np([Ip(t)]),Np([])),Lp=(e,t,o)=>e.fold(Np([Rp(o)]),Np([]),Np([Rp(t)])),Vp=(e,t,o)=>e.fold(Np([]),Np([Ip(o)]),Np([Rp(t),Ip(o)])),Hp=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},Pp=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(Vp,Up),s(Lp,Wp),s(zp,$p))},Up=Fp.offset,Wp=Fp.absolute,$p=Fp.fixed,Gp=(e,t)=>{const o=Yo(e,t);return u(o)?NaN:parseInt(o,10)},jp=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=Gp(o,t.leftAttr),s=Gp(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some(Dn(n,s))})(e,t).fold((()=>o),(e=>$p(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?Xp(e,t,a,s,r):Yp(e,t,a,s,r),l=zp(a,s,r);return((e,t,o)=>{const n=e.element;qo(n,t.leftAttr,o.left+"px"),qo(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:$p(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},qp=(e,t,o,n)=>se(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=Lp(e,s,r),i=Lp(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:Pp(e.output,t,o,n),extra:e.extra}):A.none()})),Xp=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return qp(r,o,n,s).orThunk((()=>{const e=W(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=Lp(e,s,r),i=Lp(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Dn(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:Pp(e.output,o,n,s),extra:e.extra})))}))},Yp=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return qp(r,o,n,s)};var Kp=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=lo(e.element),o=Rn(t),r=Ps(s),a=((e,t,o)=>({coord:Pp(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=Hp(a.coord,0,r);pn(s,i)}}});const Jp=(e,t)=>aa(e,{},L(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Br(o,o,{tag:"option",process:{}},Js((e=>qs("The field: "+o+" is forbidden. "+n))));var o,n})).concat([Fr("dump",w)])),Qp=e=>e.dump,Zp=(e,t)=>({...rd(t),...e.dump}),eh=Jp,th=Zp,oh=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[Pr("parts",e)]:[]).concat([Rr("uid"),Qr("dom",{}),Qr("components",[]),Si("originalSpec"),Qr("debug.sketcher",{})]).concat(t))(n,s);return Er(e+" [SpecSchema]",br(r.concat(t)),o)},nh=(e,t,o,n,s)=>{const r=sh(s),a=il(o),i=vl(o),l=oh(e,t,r,a,[i]),c=cl(0,l,o);return n(l,dl(e,l,c.internals()),r,c.externals())},sh=e=>(e=>be(e,"uid"))(e)?e:{...e,uid:mg("uid")},rh=br([Rr("name"),Rr("factory"),Rr("configFields"),Qr("apis",{}),Qr("extraApis",{})]),ah=br([Rr("name"),Rr("factory"),Rr("configFields"),Rr("partFields"),Qr("apis",{}),Qr("extraApis",{})]),ih=e=>{const t=Er("Sketcher for "+e.name,rh,e),o=le(t.apis,Am),n=le(t.extraApis,((e,t)=>Oi(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=sh(n);return o(oh(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},lh=e=>{const t=Er("Sketcher for "+e.name,ah,e),o=sl(t.name,t.partFields),n=le(t.apis,Am),s=le(t.extraApis,((e,t)=>Oi(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>nh(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},ch=ih({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:Qp(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[Qr("components",[]),Jp("containerBehaviours",[]),Qr("events",{}),Qr("domModification",{}),Qr("eventOrder",{})]}),dh="data-initial-z-index",uh=(e,t)=>{e.getSystem().addToGui(t),(e=>{go(e.element).filter(no).each((t=>{bn(t,"z-index").each((e=>{qo(t,dh,e)})),mn(t,"z-index",hn(e.element,"z-index"))}))})(t)},mh=e=>{(e=>{go(e.element).filter(no).each((e=>{Ko(e,dh).fold((()=>yn(e,"z-index")),(t=>mn(e,"z-index",t))),Qo(e,dh)}))})(e),e.getSystem().removeFromGui(e)},gh=(e,t,o)=>e.getSystem().build(ch.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var ph=Jr("snaps",[Rr("getSnapPoints"),bi("onSensor"),Rr("leftAttr"),Rr("topAttr"),Qr("lazyViewport",Ls),Qr("mustSnap",!1)]);const hh=[Qr("useFixed",T),Rr("blockerClass"),Qr("getTarget",w),Qr("onDrag",b),Qr("repositionTarget",!0),Qr("onDrop",b),sa("getBounds",Ls),ph],fh=e=>{return(t=bn(e,"left"),o=bn(e,"top"),n=bn(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?$p:Up)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Fn(e);return Wp(t.left,t.top)}));var t,o,n},bh=(e,t)=>({bounds:e.getBounds(),height:On(t.element),width:An(t.element)}),vh=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>bh(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=lo(e.element),a=Rn(r),i=Ps(s),l=fh(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=Lp(t,o,n),i=Ie(a.left,r.x,r.x+r.width-s.width),l=Ie(a.top,r.y,r.y+r.height-s.height),c=Wp(i,l);return t.fold((()=>{const e=Vp(c,o,n);return Up(e.left,e.top)}),y(c),(()=>{const e=zp(c,o,n);return $p(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>Up(e+a,t+i)),((e,t)=>Wp(e+a,t+i)),((e,t)=>$p(e+a,t+i))));var t,a,i;const l=zp(e,n,s);return $p(l.left,l.top)}),(t=>{const a=jp(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=Hp(c,0,i);pn(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},xh=(e,t,o,n)=>{t.each(mh),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Qo(o,t.leftAttr),Qo(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},yh=e=>(t,o)=>{const n=e=>{o.setStartData(bh(t,e))};return Hc([Wc(Ga(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var wh=Object.freeze({__proto__:null,getData:e=>A.from(Dn(e.x,e.y)),getDelta:(e,t)=>Dn(t.left-e.left,t.top-e.top)});const Sh=(e,t,o)=>[Wc(pa(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>xh(n,A.some(l),e,t),a=_i(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),vh(n,e,t,wh,o)}},l=gh(n,e.blockerClass,(e=>Hc([Wc(pa(),e.forceDrop),Wc(ba(),e.drop),Wc(ha(),((t,o)=>{e.move(o.event)})),Wc(fa(),e.delayDrop)]))(i));o(n),uh(n,l)}))],Ch=[...hh,wi("dragger",{handlers:yh(Sh)})];var kh=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some(Dn(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>Dn(t.left-e.left,t.top-e.top)});const Oh=(e,t,o)=>{const n=Xe(),s=o=>{xh(o,n.get(),e,t),n.clear()};return[Wc(da(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{vh(r,e,t,kh,o)}},c=gh(r,e.blockerClass,(e=>Hc([Wc(da(),e.forceDrop),Wc(ma(),e.drop),Wc(ga(),e.drop),Wc(ua(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),uh(r,c)})),Wc(ua(),((o,n)=>{n.stop(),vh(o,e,t,kh,n.event)})),Wc(ma(),((e,t)=>{t.stop(),s(e)})),Wc(ga(),s)]},_h=Ch,Th=[...hh,wi("dragger",{handlers:yh(Oh)})],Eh=[...hh,wi("dragger",{handlers:yh(((e,t,o)=>[...Sh(e,t,o),...Oh(e,t,o)]))})];var Ah=Object.freeze({__proto__:null,mouse:_h,touch:Th,mouseOrTouch:Eh}),Mh=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=y({});return Dc({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const Dh=cd({branchKey:"mode",branches:Ah,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:Mh,apis:Kp}),Bh=["input","textarea"],Fh=e=>{const t=eo(e);return I(Bh,t)},Ih=(e,t)=>{const o=t.getRoot(e).getOr(e.element);ss(o,t.invalidClass),t.notify.each((t=>{Fh(e.element)&&qo(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{nn(e,t.validHtml)})),t.onValid(e)}))},Rh=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);os(s,t.invalidClass),t.notify.each((t=>{Fh(e.element)&&qo(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{nn(e,n)})),t.onInvalid(e,n)}))},Nh=(e,t,o)=>t.validator.fold((()=>_e(Ae.value(!0))),(t=>t.validate(e))),zh=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Nh(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(Rh(e,t,0,o),Ae.error(o))),(o=>(Ih(e,t),Ae.value(o)))):Ae.error("No longer in system"))));var Lh=Object.freeze({__proto__:null,markValid:Ih,markInvalid:Rh,query:Nh,run:zh,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return rs(o,t.invalidClass)}}),Vh=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Hc([Wc(t.onEvent,(t=>{zh(t,e).get(w)}))].concat(t.validateOnLoad?[Jc((t=>{zh(t,e).get(b)}))]:[])))).getOr({})}),Hh=[Rr("invalidClass"),Qr("getRoot",A.none),Jr("notify",[Qr("aria","alert"),Qr("getContainer",A.none),Qr("validHtml",""),bi("onValid"),bi("onInvalid"),bi("onValidate")]),Jr("validator",[Rr("validate"),Qr("onEvent","input"),Qr("validateOnLoad",!0)])];const Ph=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Uh=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var Wh=Object.freeze({__proto__:null,onLoad:Ph,onUnload:Uh,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),$h=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Jc(((o,n)=>{Ph(o,e,t)})),Qc(((o,n)=>{Uh(o,e,t)}))]:[od(e,t,Ph)];return Hc(o)}});const Gh=()=>{const e=ye(null);return Dc({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},jh=()=>{const e=ye({}),t=ye({});return Dc({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>fe(e.get(),o).orThunk((()=>fe(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};V(o,(e=>{r[e.value]=e,fe(e,"meta").each((t=>{fe(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var qh=Object.freeze({__proto__:null,memory:Gh,dataset:jh,manual:()=>Dc({readState:b}),init:e=>e.store.manager.state(e)});const Xh=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var Yh=[$r("initialValue"),Rr("getFallbackEntry"),Rr("getDataKey"),Rr("setValue"),wi("manager",{setValue:Xh,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{Xh(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:jh})],Kh=[Rr("getValue"),Qr("setValue",b),$r("initialValue"),wi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:Mc.init})],Jh=[$r("initialValue"),wi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:Gh})],Qh=[Zr("store",{mode:"memory"},Mr("mode",{memory:Jh,manual:Kh,dataset:Yh})),bi("onSetValue"),Qr("resetOnDom",!1)];const Zh=id({fields:Qh,name:"representing",active:$h,apis:Wh,extra:{setValueFrom:(e,t)=>{const o=Zh.getValue(t);Zh.setValue(e,o)}},state:qh}),ef=id({fields:Hh,name:"invalidating",active:Vh,apis:Lh,extra:{validation:e=>t=>{const o=Zh.getValue(t);return _e(e(o))}}});var tf=Object.freeze({__proto__:null,exhibit:(e,t)=>td({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const of=xe([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),nf=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>ap(e,u,m,h,h)),(()=>ap(e,h,m,g,h)),(()=>ap(e,u,h,h,p)),(()=>ap(e,h,h,g,p)),(()=>ap(e,u,m,h,h)),(()=>ap(e,u,h,h,p)),(()=>ap(e,u,m,h,h)),(()=>ap(e,h,m,g,h)))},sf=(e,t)=>e.fold((()=>{const e=t.rect;return ap("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>nf("absolute",t,e,o,n,s)),((e,o,n,s)=>nf("fixed",t,e,o,n,s))),rf=(e,t)=>{const o=C(Fs,t),n=e.fold(o,o,(()=>{const e=Rn();return Fs(t).translate(-e.left,-e.top)})),s=An(t),r=On(t);return Is(n.left,n.top,s,r)},af=(e,t)=>t.fold((()=>e.fold(Ls,Ls,Is)),(t=>e.fold(y(t),y(t),(()=>{const o=lf(e,t.x,t.y);return Is(o.left,o.top,t.width,t.height)})))),lf=(e,t,o)=>{const n=Dn(t,o);return e.fold(y(n),y(n),(()=>{const e=Rn();return n.translate(-e.left,-e.top)}))};of.none;const cf=of.relative,df=of.fixed,uf=xe([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),mf=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Ie(i,e.y,e.bottom):Ie(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Is(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Is(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Ie(a,o,d),g=Ie(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Is(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),s=y(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?uf.fit(x):uf.nofit(x,m,g,f)},gf=["top","bottom","right","left"],pf="data-alloy-transition-timer",hf=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>X(t,(t=>rs(e,t))))(e,t.classes))(e,n)){mn(e,"position",o.position);const a=rf(t,e),l=sf(t,{...s,rect:a}),c=J(gf,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return pe(t,((t,n)=>!((e,t,o=S)=>Ue(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(pn(e,c),i&&((e,t)=>{as(e,t.classes),Ko(e,pf).each((t=>{clearTimeout(parseInt(t,10)),Qo(e,pf)})),((e,t)=>{const o=qe(),n=qe();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return yt(t.target,e)&&ot(n)&&I(gf,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===Ta())&&(clearTimeout(s),Qo(e,pf),is(e,t.classes))}},l=Bo(e,Ea(),(t=>{a(t)&&(l.unbind(),o.set(Bo(e,Ta(),i)),n.set(Bo(e,_a(),i)))})),c=(e=>{const t=t=>{const o=hn(e,t).split(/\s*,\s*/);return P(o,tt)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ze(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return W(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);window.requestAnimationFrame((()=>{s=setTimeout(i,c+17),qo(e,pf,s)}))})(e,t)})(e,n),wn(e))}else is(e,n.classes)},ff=(e,t,o)=>void 0===e[t]?o:e[t],bf=(e,t,o,n)=>{const s=((e,t,o,n)=>{yn(t,"max-height"),yn(t,"max-width");const s=(r=t,{width:Math.ceil(An(r)),height:On(r)});var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=mf(m,a,i,r);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:uf.nofit(l,c,d,u)))};return W(t,((e,t)=>{const o=C(l,t);return e.fold(y(e),o)}),uf.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Ol(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=sf(o.origin,t);o.transition.each((s=>{hf(e,o.origin,n,s,t,o.lastPlacement)})),ip(e,n)})(t,s,n),((e,t)=>{((e,t)=>{qo(e,tc,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;is(e,o.off),as(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},vf=w,xf=()=>Jr("layouts",[Rr("onLtr"),Rr("onRtl"),$r("onBottomLtr"),$r("onBottomRtl")]),yf=(e,t,o,n,s,r,a)=>{const i=a.map(Ec).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return Ln(d,u)(e)};var wf=[Rr("hotspot"),$r("bubble"),Qr("overrides",{}),xf(),wi("placement",((e,t,o)=>{const n=t.hotspot,s=rf(o,n.element),r=yf(e.element,t,Zl(),ec(),Jl(),Ql(),A.some(t.hotspot.element));return A.some(vf({anchorBox:s,bubble:t.bubble.getOr(Sl()),overrides:t.overrides,layouts:r}))}))],Sf=[Rr("x"),Rr("y"),Qr("height",0),Qr("width",0),Qr("bubble",Sl()),Qr("overrides",{}),xf(),wi("placement",((e,t,o)=>{const n=lf(o,t.x,t.y),s=Is(n.left,n.top,t.width,t.height),r=yf(e.element,t,Yl(),Kl(),Yl(),Kl(),A.none());return A.some(vf({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const Cf=xe([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),kf=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),Of=e=>e.fold(w,w),_f=e=>W(e,((e,t)=>e.translate(t.left,t.top)),Dn(0,0)),Tf=e=>{const t=L(e,Of);return _f(t)},Ef=Cf.screen,Af=Cf.absolute,Mf=(e,t,o)=>{const n=lo(e.element),s=Rn(n),r=((e,t,o)=>{const n=mo(o.root).dom;return A.from(n.frameElement).map(ct).filter((t=>{const o=lo(t),n=lo(e.element);return yt(o,n)})).map(Fn)})(e,0,o).getOr(s);return Af(r,s.left,s.top)},Df=(e,t,o,n)=>{const s=Ef(Dn(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Bf=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Tf(r),l=()=>Tf(r),c=()=>(e=>{const t=L(e,kf);return _f(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?Jl():Zl(),m=o.showAbove?Ql():ec(),g=yf(s,o,u,m,u,m,A.none());var p,h,f,b;return vf({anchorBox:d,bubble:o.bubble.getOr(Sl()),overrides:o.overrides,layouts:g})}));var Ff=[Rr("node"),Rr("root"),$r("bubble"),xf(),Qr("overrides",{}),Qr("showAbove",!1),wi("placement",((e,t,o)=>{const n=Mf(e,0,t);return t.node.filter(an).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Df(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Bf(a,n,t,o,i)}))}))];const If=(e,t)=>({element:e,offset:t}),Rf=(e,t)=>so(e)?If(e,t):((e,t)=>{const o=vo(e);if(0===o.length)return If(e,t);if(t<o.length)return If(o[t],0);{const e=o[o.length-1],t=so(e)?(e=>zn.get(e))(e).length:vo(e).length;return If(e,t)}})(e,t),Nf=e=>void 0!==e.foffset,zf=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(ks))(e)))().map((e=>{if(Nf(e)){const t=Rf(e.start,e.soffset),o=Rf(e.finish,e.foffset);return Ss.range(t.element,t.offset,o.element,o.offset)}return e}));var Lf=[$r("getSelection"),Rr("root"),$r("bubble"),xf(),Qr("overrides",{}),Qr("showAbove",!1),wi("placement",((e,t,o)=>{const n=mo(t.root).dom,s=Mf(e,0,t),r=zf(n,t).bind((e=>{if(Nf(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(mt):A.none()})(ht(e,t)))(n,Ss.exactFromRange(e)).orThunk((()=>{const t=lt("\ufeff");Po(e.start,t);const o=Os(n,Ss.exact(t,0,t,1));return en(t),o}));return t.bind((e=>Df(e.left,e.top,e.width,e.height)))}{const t=le(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return Df(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=zf(n,t).bind((e=>Nf(e)?no(e.start)?A.some(e.start):po(e.start):A.some(e.firstCell))).getOr(e.element);return Bf(r,s,t,o,a)}))];const Vf="link-layout",Hf=e=>e.x+e.width,Pf=(e,t)=>e.x-t.width,Uf=(e,t)=>e.y-t.height+e.height,Wf=e=>e.y,$f=(e,t,o)=>Cl(Hf(e),Wf(e),o.southeast(),Ol(),"southeast",Fl(e,{left:0,top:2}),Vf),Gf=(e,t,o)=>Cl(Pf(e,t),Wf(e),o.southwest(),_l(),"southwest",Fl(e,{right:1,top:2}),Vf),jf=(e,t,o)=>Cl(Hf(e),Uf(e,t),o.northeast(),Tl(),"northeast",Fl(e,{left:0,bottom:3}),Vf),qf=(e,t,o)=>Cl(Pf(e,t),Uf(e,t),o.northwest(),El(),"northwest",Fl(e,{right:1,bottom:3}),Vf),Xf=()=>[$f,Gf,jf,qf],Yf=()=>[Gf,$f,qf,jf];var Kf=[Rr("item"),xf(),Qr("overrides",{}),wi("placement",((e,t,o)=>{const n=rf(o,t.item.element),s=yf(e.element,t,Xf(),Yf(),Xf(),Yf(),A.none());return A.some(vf({anchorBox:n,bubble:Sl(),overrides:t.overrides,layouts:s}))}))],Jf=Mr("type",{selection:Lf,node:Ff,hotspot:wf,submenu:Kf,makeshift:Sf});const Qf=[Wr("classes",or),oa("mode","all",["all","layout","placement"])],Zf=[Qr("useFixed",T),$r("getBounds")],eb=[Nr("anchor",Jf),Jr("transition",Qf)],tb=(e,t,o,n,s,r)=>{const a=Er("placement.info",vr(eb),s),i=a.anchor,l=n.element,c=o.get(n.uid);Xm((()=>{mn(l,"position","fixed");const s=bn(l,"visibility");mn(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return df(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Fn(e.element),o=e.element.dom.getBoundingClientRect();return cf(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=ff(a,"maxHeightFunction",Cc()),c=ff(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:af(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return bf(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{yn(l,"visibility")}),(e=>{mn(l,"visibility",e)})),bn(l,"left").isNone()&&bn(l,"top").isNone()&&bn(l,"right").isNone()&&bn(l,"bottom").isNone()&&He(bn(l,"position"),"fixed")&&yn(l,"position")}),l)};var ob=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();tb(e,t,o,n,s,r)},positionWithinBounds:tb,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;V(["position","left","right","top","bottom"],(e=>yn(s,e))),(e=>{Qo(e,tc)})(s),o.clear(n.uid)}});const nb=id({fields:Zf,name:"positioning",active:tf,apis:ob,state:Object.freeze({__proto__:null,init:()=>{let e={};return Dc({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>fe(e,t)})}})});var sb=Object.freeze({__proto__:null,events:e=>Hc([Wc(Ra(),((t,o)=>{const n=e.channels,s=re(n),r=o,a=((e,t)=>t.universal?e:P(e,(e=>I(t.channels,e))))(s,r);V(a,(e=>{const o=n[e],s=o.schema,a=Er("channel["+e+"] data\nReceiver: "+si(t.element),s,r.data);o.onReceive(t,a)}))}))])}),rb=[Nr("channels",Or(Ae.value,br([xi("onReceive"),Qr("schema",Zs())])))];const ab=id({fields:rb,name:"receiving",active:sb});var ib=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Km:Ym)(o,r)}))};return Hc([Wc(Ra(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;I(s.channels,n)&&o(t,s.data)}})),Jc(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),lb=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),cb=[Rr("channel"),$r("renderComponents"),$r("updateState"),$r("initialData"),na("reuseDom",!0)];const db=id({fields:cb,name:"reflecting",active:ib,apis:lb,state:Object.freeze({__proto__:null,init:()=>{const e=ye(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),ub=(e,t,o,n)=>{o.get().each((t=>{Hm(e)}));const s=t.getAttachPoint(e);Nm(s,e);const r=e.getSystem().build(n);return Nm(e,r),o.set(r),r},mb=(e,t,o,n)=>{const s=ub(e,t,o,n);return t.onOpen(e,s),s},gb=(e,t,o)=>{o.get().each((n=>{Hm(e),Vm(e),t.onClose(e,n),o.clear()}))},pb=(e,t,o)=>o.isOpen(),hb=(e,t,o)=>{const n=t.getAttachPoint(e);mn(e.element,"position",nb.getMode(n)),((e,t,o)=>{bn(e.element,t).fold((()=>{Qo(e.element,o)}),(t=>{qo(e.element,o,t)})),mn(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},fb=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>bn(e,t).isSome())))(e.element)||yn(e.element,"position"),((e,t,o)=>{Ko(e.element,o).fold((()=>yn(e.element,t)),(o=>mn(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var bb=Object.freeze({__proto__:null,cloak:hb,decloak:fb,open:mb,openWhileCloaked:(e,t,o,n,s)=>{hb(e,t),mb(e,t,o,n),s(),fb(e,t)},close:gb,isOpen:pb,isPartOf:(e,t,o,n)=>pb(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>ub(e,t,o,n)))}),vb=Object.freeze({__proto__:null,events:(e,t)=>Hc([Wc(Ha(),((o,n)=>{gb(o,e,t)}))])}),xb=[bi("onOpen"),bi("onClose"),Rr("isPartOf"),Rr("getAttachPoint"),Qr("cloakVisibilityAttr","data-precloak-visibility")],yb=Object.freeze({__proto__:null,init:()=>{const e=Xe(),t=y("not-implemented");return Dc({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const wb=id({fields:xb,name:"sandboxing",active:vb,apis:bb,state:yb}),Sb=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),Cb=e=>e.dimension.property,kb=(e,t)=>e.dimension.getDimension(t),Ob=(e,t)=>{const o=Sb(e,t);is(o,[t.shrinkingClass,t.growingClass])},_b=(e,t)=>{ss(e.element,t.openClass),os(e.element,t.closedClass),mn(e.element,Cb(t),"0px"),wn(e.element)},Tb=(e,t)=>{ss(e.element,t.closedClass),os(e.element,t.openClass),yn(e.element,Cb(t))},Eb=(e,t,o,n)=>{o.setCollapsed(),mn(e.element,Cb(t),kb(t,e.element)),Ob(e,t),_b(e,t),t.onStartShrink(e),t.onShrunk(e)},Ab=(e,t,o,n)=>{const s=n.getOrThunk((()=>kb(t,e.element)));o.setCollapsed(),mn(e.element,Cb(t),s),wn(e.element);const r=Sb(e,t);ss(r,t.growingClass),os(r,t.shrinkingClass),_b(e,t),t.onStartShrink(e)},Mb=(e,t,o)=>{const n=kb(t,e.element);("0px"===n?Eb:Ab)(e,t,o,A.some(n))},Db=(e,t,o)=>{const n=Sb(e,t),s=rs(n,t.shrinkingClass),r=kb(t,e.element);Tb(e,t);const a=kb(t,e.element);(s?()=>{mn(e.element,Cb(t),r),wn(e.element)}:()=>{_b(e,t)})(),ss(n,t.shrinkingClass),os(n,t.growingClass),Tb(e,t),mn(e.element,Cb(t),a),o.setExpanded(),t.onStartGrow(e)},Bb=(e,t,o)=>{const n=Sb(e,t);return!0===rs(n,t.growingClass)},Fb=(e,t,o)=>{const n=Sb(e,t);return!0===rs(n,t.shrinkingClass)};var Ib=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){yn(e.element,Cb(t));const o=kb(t,e.element);mn(e.element,Cb(t),o)}},grow:(e,t,o)=>{o.isExpanded()||Db(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&Mb(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&Eb(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:Bb,isShrinking:Fb,isTransitioning:(e,t,o)=>Bb(e,t)||Fb(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?Mb:Db)(e,t,o)},disableTransitions:Ob,immediateGrow:(e,t,o)=>{o.isExpanded()||(Tb(e,t),mn(e.element,Cb(t),kb(t,e.element)),Ob(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),Rb=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return td(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:ia(t.dimension.property,"0px")})},events:(e,t)=>Hc([Kc(Ta(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(Ob(o,e),t.isExpanded()&&yn(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),Nb=[Rr("closedClass"),Rr("openClass"),Rr("shrinkingClass"),Rr("growingClass"),$r("getAnimationRoot"),bi("onShrunk"),bi("onStartShrink"),bi("onGrown"),bi("onStartGrow"),Qr("expanded",!1),Nr("dimension",Mr("property",{width:[wi("property","width"),wi("getDimension",(e=>En(e)+"px"))],height:[wi("property","height"),wi("getDimension",(e=>kn(e)+"px"))]}))];const zb=id({fields:Nb,name:"sliding",active:Rb,apis:Ib,state:Object.freeze({__proto__:null,init:e=>{const t=ye(e.expanded);return Dc({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:C(t.set,!1),setExpanded:C(t.set,!0),readState:()=>"expanded: "+t.get()})}})});var Lb=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Hc([Wc(e.event,o),Qc((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Wc(e,(()=>t.cancel()))])).getOr([])))}});const Vb=e=>{const t=ye(null);return Dc({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var Hb=Object.freeze({__proto__:null,throttle:Vb,init:e=>e.stream.streams.state(e)}),Pb=[Nr("stream",Mr("mode",{throttle:[Rr("delay"),Qr("stopEvent",!0),wi("streams",{setup:(e,t)=>{const o=e.stream,n=nt(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:Vb})]})),Qr("event","input"),$r("cancelEvent"),xi("onStream")];const Ub=id({fields:Pb,name:"streaming",active:Lb,state:Hb});var Wb=Object.freeze({__proto__:null,exhibit:(e,t)=>td({attributes:la([{key:t.tabAttr,value:"true"}])})}),$b=[Qr("tabAttr","data-alloy-tabstop")];const Gb=id({fields:$b,name:"tabstopping",active:Wb}),jb=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?os(e.element,t):ss(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},qb=(e,t,o)=>{jb(e,t,o,!o.get())},Xb=(e,t,o)=>{jb(e,t,o,t.selected)};var Yb=Object.freeze({__proto__:null,onLoad:Xb,toggle:qb,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{jb(e,t,o,!0)},off:(e,t,o)=>{jb(e,t,o,!1)},set:jb}),Kb=Object.freeze({__proto__:null,exhibit:()=>td({}),events:(e,t)=>{const o=(n=e,s=t,r=qb,ed((e=>{r(e,n,s)})));var n,s,r;const a=od(e,t,Xb);return Hc(j([e.toggleOnExecute?[o]:[],[a]]))}});const Jb=(e,t,o)=>{qo(e.element,"aria-expanded",o)};var Qb=[Qr("selected",!1),$r("toggleClass"),Qr("toggleOnExecute",!0),bi("onToggled"),Zr("aria",{mode:"none"},Mr("mode",{pressed:[Qr("syncWithExpanded",!1),wi("update",((e,t,o)=>{qo(e.element,"aria-pressed",o),t.syncWithExpanded&&Jb(e,0,o)}))],checked:[wi("update",((e,t,o)=>{qo(e.element,"aria-checked",o)}))],expanded:[wi("update",Jb)],selected:[wi("update",((e,t,o)=>{qo(e.element,"aria-selected",o)}))],none:[wi("update",b)]}))];const Zb=id({fields:Qb,name:"toggling",active:Kb,apis:Yb,state:(ev=!1,{init:()=>{const e=ye(ev);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(ev),readState:()=>e.get()}}})});var ev;const tv=Ne("tooltip.exclusive"),ov=Ne("tooltip.show"),nv=Ne("tooltip.hide"),sv=Ne("tooltip.immediateHide"),rv=Ne("tooltip.immediateShow"),av=(e,t,o)=>{e.getSystem().broadcastOn([tv],{})};var iv=Object.freeze({__proto__:null,hideAllExclusive:av,immediateOpenClose:(e,t,o,n)=>Ic(e,n?rv:sv),isEnabled:(e,t,o)=>o.isEnabled(),setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&ng.set(e,n)}))},setEnabled:(e,t,o,n)=>o.setEnabled(n)}),lv=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{n.getSystem().isConnected()&&(Vm(n),e.onHide(o,n),t.clearTooltip())})),t.clearTimer()},n=o=>{if(!t.isShowing()&&t.isEnabled()){av(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Hc("normal"===e.mode?[Wc(va(),(e=>{Ic(o,ov)})),Wc(fa(),(e=>{Ic(o,nv)}))]:[]),behaviours:rd([ng.config({})])});t.setTooltip(s),Nm(n,s),e.onShow(o,s),nb.position(n,s,{anchor:e.anchor(o)})}},s=o=>{t.getTooltip().each((t=>{const n=e.lazySink(o).getOrDie();nb.position(n,t,{anchor:e.anchor(o)})}))};return Hc(j([[Zc((t=>{e.onSetup(t)})),Wc(ov,(o=>{t.resetTimer((()=>{n(o)}),e.delayForShow())})),Wc(nv,(n=>{t.resetTimer((()=>{o(n)}),e.delayForHide())})),Wc(rv,(e=>{t.resetTimer((()=>{n(e)}),0)})),Wc(sv,(e=>{t.resetTimer((()=>{o(e)}),0)})),Wc(Ra(),((e,n)=>{const s=n;s.universal||(I(s.channels,tv)||I(s.channels,_d()))&&(s.data.closedTooltip&&t.isShowing()&&s.data.closedTooltip(),o(e))})),Qc((e=>{o(e)}))],(()=>{switch(e.mode){case"normal":return[Wc(xa(),(e=>{Ic(e,rv)})),Wc(Fa(),(e=>{Ic(e,sv)})),Wc(va(),(e=>{Ic(e,ov)})),Wc(fa(),(e=>{Ic(e,nv)}))];case"follow-highlight":return[Wc(oi(),((e,t)=>{Ic(e,ov)})),Wc(ni(),(e=>{Ic(e,nv)}))];case"children-normal":return[Wc(xa(),((o,n)=>{Ho(o.element).each((r=>{bt(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Ic(o,rv)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Wc(Fa(),(e=>{Ho(e.element).fold((()=>{Ic(e,sv)}),b)})),Wc(va(),(o=>{Xn(o.element,"[data-mce-tooltip]:hover").each((n=>{t.getTooltip().fold((()=>{Ic(o,ov)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Wc(fa(),(e=>{Xn(e.element,"[data-mce-tooltip]:hover").fold((()=>{Ic(e,nv)}),b)}))];default:return[Wc(xa(),((o,n)=>{Ho(o.element).each((r=>{bt(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Ic(o,rv)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),Wc(Fa(),(e=>{Ho(e.element).fold((()=>{Ic(e,sv)}),b)}))]}})()]))}}),cv=[Rr("lazySink"),Rr("tooltipDom"),Qr("exclusive",!0),Qr("tooltipComponents",[]),sa("delayForShow",y(300)),sa("delayForHide",y(100)),sa("onSetup",b),oa("mode","normal",["normal","follow-highlight","children-keyboard-focus","children-normal"]),Qr("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([jl,Gl,Pl,Wl,Ul,$l]),onRtl:y([jl,Gl,Pl,Wl,Ul,$l])},bubble:wl(0,-2,{})}))),bi("onHide"),bi("onShow")],dv=Object.freeze({__proto__:null,init:()=>{const e=ye(!0),t=Xe(),o=Xe(),n=()=>{t.on(clearTimeout)},s=y("not-implemented");return Dc({getTooltip:o.get,isShowing:o.isSet,setTooltip:o.set,clearTooltip:o.clear,clearTimer:n,resetTimer:(e,o)=>{n(),t.set(setTimeout(e,o))},readState:s,isEnabled:()=>e.get(),setEnabled:t=>e.set(t)})}});const uv=id({fields:cv,name:"tooltipping",active:lv,state:dv,apis:iv}),mv=id({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Hc([Pc(Aa(),E)]),exhibit:()=>td({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),gv=e=>{const t=at(e),o=vo(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return W(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:on(t)};return{tag:eo(t),classes:s,attributes:n,...r}},pv=e=>{const t=(e=>void 0!==e.uid)(e)&&ve(e,"uid")?e.uid:mg("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}},hv=xl,fv=ol,bv=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Xe(),o=ye(!1),n=_i((t=>{e.triggerEvent(Va(),t),o.set(!0)}),400),s=la([{key:da(),value:e=>(Ti(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:ua(),value:e=>(n.cancel(),Ti(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:ma(),value:s=>(n.cancel(),t.get().filter((e=>yt(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(La(),s))))}]);return{fireIfReady:(e,t)=>fe(s,t).bind((t=>t(e)))}})(o),s=L(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Bo(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Xe(),a=Bo(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(Ia(),e)}),0))})),i=Bo(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===bd[0]&&!I(["input","textarea"],eo(e.target))&&!ps(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Bo(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Xe(),d=Bo(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(Fa(),e)}),0))}));return{unbind:()=>{V(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},vv=(e,t)=>{const o=fe(e,"target").getOr(t);return ye(o)},xv=xe([{stopped:[]},{resume:["element"]},{complete:[]}]),yv=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=ye(!1),n=ye(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),xv.complete())),(e=>{const o=e.descHandler;return bg(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),xv.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),xv.complete()):go(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),xv.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),xv.resume(n))))}))},wv=(e,t,o,n,s,r)=>yv(e,t,o,n,s,r).fold(E,(n=>wv(e,t,o,n,s,r)),T),Sv=(e,t,o,n,s)=>{const r=vv(o,n);return wv(e,t,o,n,r,s)},Cv=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{ie(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:C.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{ie(e,((e,o)=>{be(e,t)&&delete e[t]}))},filterByType:t=>fe(e,t).map((e=>ge(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>fe(e,o).bind((e=>bs(n,(t=>((e,t)=>ug(t).bind((t=>fe(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{ug(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return ug(t).getOrThunk((()=>((e,t)=>{const o=Ne(lg+"uid-");return dg(t,o),o})(0,e.element)))})(n);ve(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+si(s.element)+"\nCannot use it for: "+si(e.element)+"\nThe conflicting element is"+(an(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>fe(t,e)}},kv=e=>{const t=t=>go(e.element).fold(E,(e=>yt(t,e))),o=Cv(),n=(e,n)=>o.find(t,e,n),s=bv(e.element,{triggerEvent:(e,t)=>li(e,t.target,(o=>((e,t,o,n)=>Sv(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:y("real"),triggerEvent:(e,t,o)=>{li(e,t,(s=>Sv(n,e,o,t,s)))},triggerFocus:(e,t)=>{ug(e).fold((()=>{No(e)}),(o=>{li(Ba(),e,(o=>(((e,t,o,n,s)=>{const r=vv(o,n);yv(e,t,o,n,r,s)})(n,Ba(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:Fg,buildOrPatch:Bg,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),so(e.element)||(o.register(e),V(e.components(),a),r.triggerEvent(Ua(),e.element,{target:e.element}))},i=e=>{so(e.element)||(V(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Nm(e,t)},c=e=>{Vm(e)},d=e=>{const t=o.filter(Ra());V(t,(t=>{const o=t.descHandler;bg(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t)=>{const o=(e=>{const t=ye(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return V(e,(e=>{const t=e.descHandler;bg(t)(o)})),o.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>Ae.error(new Error('Could not find component with uid: "'+e+'" in system.'))),Ae.value),h=e=>{const t=ug(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),en(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},Ov=()=>{const e=(e,t)=>{t.stop(),Nc(e)};return[Wc(Oa(),e),Wc(La(),e),Xc(da()),Xc(pa())]},_v=e=>Hc(j([e.map((e=>ed(((t,o)=>{e(t),o.stop()})))).toArray(),Ov()])),Tv=ih({name:"Button",factory:e=>{const t=_v(e.action),o=e.dom.tag,n=t=>fe(e.dom,"attributes").bind((e=>fe(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:th(e.buttonBehaviours,[fd.config({}),Om.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[Qr("uid",void 0),Rr("dom"),Qr("components",[]),eh("buttonBehaviours",[fd,Om]),$r("action"),$r("role"),Qr("eventOrder",{})]}),Ev=y([Qr("shell",!1),Rr("makeItem"),Qr("setupItem",b),eh("listBehaviours",[ng])]),Av=Zi({name:"items",overrides:()=>({behaviours:rd([ng.config({})])})}),Mv=y([Av]),Dv=lh({name:y("CustomList")(),configFields:Ev(),partFields:Mv(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[ng.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Zp(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):ul(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=ng.contents(n),r=o.length,a=r-s.length,i=a>0?N(a,(()=>e.makeItem())):[],l=s.slice(r);V(l,(e=>ng.remove(n,e))),V(i,(e=>ng.append(n,e)));const c=ng.contents(n);V(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),Bv="aria-controls",Fv=()=>{const e=Ne(Bv);return{id:e,link:t=>{qo(t,Bv,e)},unlink:e=>{Qo(e,Bv)}}},Iv=(e,t)=>gs(t,(t=>yt(t,e.element)),T)||((e,t)=>(e=>Wn(e,(e=>{if(!no(e))return!1;const t=Yo(e,"id");return void 0!==t&&t.indexOf(Bv)>-1})).bind((e=>{const t=Yo(e,"id"),o=ko(e);return Xn(o,`[${Bv}="${t}"]`)})))(t).exists((t=>Iv(e,t))))(e,t),Rv="alloy.item-hover",Nv="alloy.item-focus",zv="alloy.item-toggled",Lv=e=>{(Ho(e.element).isNone()||fd.isFocused(e))&&(fd.isFocused(e)||fd.focus(e),Rc(e,Rv,{item:e}))},Vv=e=>{Rc(e,Nv,{item:e})},Hv=y(Rv),Pv=y(Nv),Uv=y(zv),Wv=e=>e.role.fold((()=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem")),w);var $v=[Rr("data"),Rr("components"),Rr("dom"),Qr("hasSubmenu",!1),$r("toggling"),$r("role"),eh("itemBehaviours",[Zb,fd,Om,Zh]),Qr("ignoreFocus",!1),Qr("domModification",{}),wi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:Wv(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:th(e.itemBehaviours,[e.toggling.fold(Zb.revoke,(t=>Zb.config(((e,t)=>({aria:{mode:t?"selected":"checked"},...me(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Rc(e,zv,{item:e,state:t})})(t,o)}}))(t,e.role.exists((e=>"option"===e)))))),fd.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Vv(e)}}),Om.config({mode:"execution"}),Zh.config({store:{mode:"memory",initialValue:e.data}}),ud("item-type-events",[...Ov(),Wc(va(),Lv),Wc(za(),fd.focus)])]),components:e.components,eventOrder:e.eventOrder}))),Qr("eventOrder",{})],Gv=[Rr("dom"),Rr("components"),wi("builder",(e=>({dom:e.dom,components:e.components,events:Hc([Yc(za())])})))];const jv=y("item-widget"),qv=y([Ji({name:"widget",overrides:e=>({behaviours:rd([Zh.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),Xv=[Rr("uid"),Rr("data"),Rr("components"),Rr("dom"),Qr("autofocus",!1),Qr("ignoreFocus",!1),eh("widgetBehaviours",[Zh,fd,Om]),Qr("domModification",{}),vl(qv()),wi("builder",(e=>{const t=cl(jv(),e,qv()),o=dl(jv(),e,t.internals()),n=t=>ul(t,e,"widget").map((e=>(Om.focusIn(e),e))),s=(t,o)=>nu(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Hc([ed(((e,t)=>{n(e).each((e=>{t.stop()}))})),Wc(va(),Lv),Wc(za(),((t,o)=>{e.autofocus?n(t):fd.focus(t)}))]),behaviours:th(e.widgetBehaviours,[Zh.config({store:{mode:"memory",initialValue:e.data}}),fd.config({ignore:e.ignoreFocus,onFocus:e=>{Vv(e)}}),Om.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:dd(),onLeft:s,onRight:s,onEscape:(t,o)=>fd.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(fd.focus(t),A.some(!0))})])}}))],Yv=Mr("type",{widget:Xv,item:$v,separator:Gv}),Kv=y([el({factory:{sketch:e=>{const t=Er("menu.spec item",Yv,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>be(t,"uid")?t:{...t,uid:mg("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Jv=y([qr("role"),Rr("value"),Rr("items"),Rr("dom"),Rr("components"),Qr("eventOrder",{}),Jp("menuBehaviours",[Yd,Zh,Pg,Om]),Zr("movement",{mode:"menu",moveOnTab:!0},Mr("mode",{grid:[Ci(),wi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[wi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),Rr("rowSelector"),Qr("previousSelector",A.none)],menu:[Qr("moveOnTab",!0),wi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),Nr("markers",mi()),Qr("fakeFocus",!1),Qr("focusManager",Jd()),bi("onHighlight"),bi("onDehighlight"),Qr("showMenuRole",!0)]),Qv=y("alloy.menu-focus"),Zv=lh({name:"Menu",configFields:Jv(),partFields:Kv(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Zp(e.menuBehaviours,[Yd.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),Zh.config({store:{mode:"memory",initialValue:e.value}}),Pg.config({find:A.some}),Om.config(e.movement.config(e,e.movement))]),events:Hc([Wc(Pv(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Yd.highlight(e,o),t.stop(),Rc(e,Qv(),{menu:e,item:o})}))})),Wc(Hv(),((e,t)=>{const o=t.event.item;Yd.highlight(e,o)})),Wc(Uv(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===Yo(o.element,"role")&&((e,t)=>{const o=ms(e.element,'[role="menuitemradio"][aria-checked="true"]');V(o,(o=>{yt(o,t.element)||e.getSystem().getByDom(o).each((e=>{Zb.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,...e.showMenuRole?{domModification:{attributes:{role:e.role.getOr("menu")}}}:{}})}),ex=(e,t,o,n)=>fe(o,n).bind((n=>fe(e,n).bind((n=>{const s=ex(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),tx=e=>"prepared"===e.type?A.some(e.menu):A.none(),ox=()=>{const e=ye({}),t=ye({}),o=ye({}),n=Xe(),s=ye({}),r=e=>a(e).bind(tx),a=e=>fe(t.get(),e),i=t=>fe(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};ie(e,((e,t)=>{V(e,(e=>{o[e]=t}))}));const n=t,s=ce(t,((e,t)=>({k:e,v:t}))),r=le(s,((e,t)=>[t].concat(ex(o,n,s,t))));return le(o,(e=>fe(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>fe(e.get(),t).map((e=>{const n=fe(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>fe(o.get(),e),collapse:e=>fe(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return K(re(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=P(i(t).toArray(),(e=>r(e).isSome()));return fe(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(q(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>pe(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>He(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},nx=tx,sx=Ne("tiered-menu-item-highlight"),rx=Ne("tiered-menu-item-dehighlight"),ax=y("collapse-item"),ix=ih({name:"TieredMenu",configFields:[yi("onExecute"),yi("onEscape"),xi("onOpenMenu"),xi("onOpenSubmenu"),bi("onRepositionMenu"),bi("onCollapseMenu"),Qr("highlightOnOpen",Ac.HighlightMenuAndItem),Pr("data",[Rr("primary"),Rr("menus"),Rr("expansions")]),Qr("fakeFocus",!1),bi("onHighlightItem"),bi("onDehighlightItem"),bi("onHover"),pi(),Rr("dom"),Qr("navigateOnHover",!0),Qr("stayInDom",!1),Jp("tmenuBehaviours",[Om,Yd,Pg,ng]),Qr("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Xe(),n=ox(),s=e=>Zh.getValue(e).value,r=t=>le(e.data.menus,((e,t)=>q(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Yd.highlight,i=(t,o)=>{a(t,o),Yd.getHighlighted(o).orThunk((()=>Yd.getFirst(o))).each((n=>{e.fakeFocus?Yd.highlight(o,n):zc(t,n.element,za())}))},l=(e,t)=>Pe(L(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));V(s,(o=>{is(o.element,[e.markers.backgroundMenu]),e.stayInDom||ng.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=ms(t.element,`.${e.markers.item}`),a=P(r,(e=>"true"===Yo(e,"aria-haspopup")));return V(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);ie(r,((e,t)=>{const o=I(n,t);qo(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return V(a,(t=>{os(t.element,e.markers.backgroundMenu)})),an(r.element)||ng.append(t,Ig(r)),is(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(rp)&&rp.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return an(l.element)||ng.append(t,Ig(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===m.HighlightSubmenu?(Yd.highlightFirst(l),u(t,n,s)):(Yd.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>Yn(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Hc([Wc(Qv(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Yd.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),ed(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Jc(((t,o)=>{(t=>{const o=((t,o,n)=>le(n,((n,s)=>{const r=()=>Zv.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Rc(e,sx,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Rc(e,rx,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?Qd():Jd()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{ng.append(t,Ig(o)),e.onOpenMenu(t,o),e.highlightOnOpen===Ac.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===Ac.HighlightJustMenu&&a(t,o)}))})),Wc(sx,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Wc(rx,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Wc(Hv(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Yd.getHighlighted(e).bind(Yd.getHighlighted),x={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=he(n.getMenus()),r=Pe(L(o,nx));return n.getTriggeringPath(t,(e=>((e,t,o)=>se(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Yd.getCandidates(e);return $(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===Yo(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Zp(e.tmenuBehaviours,[Om.config({mode:"special",onRight:h(((e,t)=>nu(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>nu(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{zc(e,t.element,za())}))}}),Yd.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),Pg.config({find:e=>Yd.getHighlighted(e)}),ng.config({})]),eventOrder:e.eventOrder,apis:x,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:ia(e,t),expansions:{}}),collapseItem:e=>({value:Ne(ax()),meta:{text:e}})}}),lx=y("sink"),cx=y(Zi({name:lx(),overrides:y({dom:{tag:"div"},behaviours:rd([nb.config({useFixed:E})]),events:Hc([Xc(wa()),Xc(pa()),Xc(Oa())])})})),dx=br([Qr("isExtraPart",T),Jr("fireEventInstead",[Qr("event",Ya())])]),ux=e=>{const t=Er("Dismissal",dx,e);return{[Td()]:{schema:br([Rr("target")]),onReceive:(e,o)=>{wb.isOpen(e)&&(wb.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>wb.close(e)),(t=>Ic(e,t.event))))}}}},mx=br([Jr("fireEventInstead",[Qr("event",Ka())]),Hr("doReposition")]),gx=e=>{const t=Er("Reposition",mx,e);return{[Ed()]:{onReceive:e=>{wb.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Ic(e,t.event)))}}}},px=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},hx=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=vx(n,e);return i.map((t=>t.bind((t=>{const i=t.menus[t.primary];return A.from(i).each((t=>{e.listRole.each((e=>{t.role=e}))})),A.from(ix.sketch({...r.menu(),uid:mg(""),data:t,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();nb.position(n,t,{anchor:o}),wb.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();nb.position(n,o,{anchor:{type:"submenu",item:t}}),wb.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();nb.position(s,t,{anchor:o}),V(n,(e=>{nb.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(fd.focus(n),wb.close(s),A.some(!0))}))}))))})(e,t,px(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{wb.isOpen(n)&&wb.close(n)}),(e=>{wb.cloak(n),wb.open(n,e),r(n)})),n)))},fx=(e,t,o,n,s,r,a)=>(wb.close(n),_e(n)),bx=(e,t,o,n,s,r)=>{const a=Gg.getCoupled(o,"sandbox");return(wb.isOpen(a)?fx:hx)(e,t,o,a,n,s,r)},vx=(e,t)=>e.getSystem().getByUid(t.uid+"-"+lx()).map((e=>()=>Ae.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>Ae.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),xx=e=>{wb.getState(e).each((e=>{ix.repositionMenus(e)}))},yx=(e,t,o)=>{const n=Fv(),s=vx(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id}},behaviours:th(e.sandboxBehaviours,[Zh.config({store:{mode:"memory",initialValue:t}}),wb.config({onOpen:(s,r)=>{const a=px(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=Pg.getCurrent(t).getOr(t),s=En(e.element);o?mn(n.element,"min-width",s+"px"):((e,t)=>{Tn.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,r)=>{n.unlink(t.element),s().getOr(r).element.dom.dispatchEvent(new window.FocusEvent("focusout")),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>Iv(o,n)||Iv(t,n),getAttachPoint:()=>s().getOrDie()}),Pg.config({find:e=>wb.getState(e).bind((e=>Pg.getCurrent(e)))}),ab.config({channels:{...ux({isExtraPart:T}),...gx({doReposition:xx})}})])}},wx=()=>[Qr("sandboxClasses",[]),eh("sandboxBehaviours",[Pg,ab,wb,Zh])],Sx=y([Rr("dom"),Rr("fetch"),bi("onOpen"),vi("onExecute"),Qr("getHotspot",A.some),Qr("getAnchorOverrides",y({})),xf(),Jp("dropdownBehaviours",[Zb,Gg,Om,fd]),Rr("toggleClass"),Qr("eventOrder",{}),$r("lazySink"),Qr("matchWidth",!1),Qr("useMinWidth",!1),$r("role"),$r("listRole")].concat(wx())),Cx=y([Qi({schema:[pi(),Qr("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),cx()]),kx=lh({name:"Dropdown",configFields:Sx(),partFields:Cx(),factory:(e,t,o,n)=>{const s=e=>{wb.getState(e).each((e=>{ix.highlightPrimary(e)}))},r=(t,o,s)=>bx(e,w,t,n,o,s),a={expand:e=>{Zb.isOn(e)||r(e,b,Ac.HighlightNone).get(b)},open:e=>{Zb.isOn(e)||r(e,b,Ac.HighlightMenuAndItem).get(b)},refetch:t=>Gg.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,Ac.HighlightMenuAndItem).map(b)),(o=>hx(e,w,t,o,n,b,Ac.HighlightMenuAndItem).map(b))),isOpen:Zb.isOn,close:e=>{Zb.isOn(e)&&r(e,b,Ac.HighlightMenuAndItem).get(b)},repositionMenus:e=>{Zb.isOn(e)&&(e=>{const t=Gg.getCoupled(e,"sandbox");xx(t)})(e)}},i=(e,t)=>(Nc(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Zp(e.dropdownBehaviours,[Zb.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),Gg.config({others:{sandbox:t=>yx(e,t,{onOpen:()=>Zb.on(t),onClose:()=>Zb.off(t)})}}),Om.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(kx.isOpen(e)){const t=Gg.getCoupled(e,"sandbox");s(t)}else kx.open(e);return A.some(!0)},onEscape:(e,t)=>kx.isOpen(e)?(kx.close(e),A.some(!0)):A.none()}),fd.config({})]),events:_v(A.some((e=>{r(e,s,Ac.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[Na()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":e.listRole.getOr("true"),...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:fe(e.dom,"attributes").bind((e=>fe(e,"type"))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Ox="form",_x=[Jp("formBehaviours",[Zh])],Tx=e=>"<alloy.field."+e+">",Ex=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Zp(e.formBehaviours,[Zh.config({store:{mode:"manual",getValue:t=>{const o=pl(t,e);return le(o,((e,t)=>e().bind((e=>{return o=Pg.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+si(e.element)),o.fold((()=>Ae.error(n)),Ae.value);var o,n})).map(Zh.getValue)))},setValue:(t,o)=>{ie(o,((o,n)=>{ul(t,e,n).each((e=>{Pg.getCurrent(e).each((e=>{Zh.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>ul(t,e,o).bind(Pg.getCurrent)}}),Ax={getField:Am(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),al(Ox,Tx(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>Ji({name:e,pname:Tx(e)})));return nh(Ox,_x,s,Ex,o)}},Mx=y([Rr("dom"),Qr("shell",!0),Jp("toolbarBehaviours",[ng])]),Dx=y([Zi({name:"groups",overrides:()=>({behaviours:rd([ng.config({})])})})]),Bx=lh({name:"Toolbar",configFields:Mx(),partFields:Dx(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[ng.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Zp(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):ul(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{ng.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),Fx=y([hi(["toggledClass"]),Rr("lazySink"),Hr("fetch"),Yr("getBounds"),Jr("fireDismissalEventInstead",[Qr("event",Ya())]),xf(),bi("onToggled")]),Ix=y([Qi({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:rd([Zb.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Qi({factory:Bx,schema:Mx(),name:"toolbar",overrides:e=>({toolbarBehaviours:rd([Om.config({mode:"cyclic",onEscape:t=>(ul(t,e,"button").each(fd.focus),A.none())})])})})]),Rx=Xe(),Nx=(e,t)=>{const o=Gg.getCoupled(e,"toolbarSandbox");wb.isOpen(o)?wb.close(o):wb.open(o,t.toolbar())},zx=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();nb.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:Oc()}}},s)},Lx=(e,t,o,n,s)=>{Bx.setGroups(t,s),zx(e,t,o,n),Zb.on(e)},Vx=lh({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Tv.sketch({...n.button(),action:e=>{Nx(e,n)},buttonBehaviours:th({dump:n.button().buttonBehaviours},[Gg.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=Fv();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:rd([Om.config({mode:"special",onEscape:e=>(wb.close(e),A.some(!0))}),wb.config({onOpen:(s,r)=>{const a=Rx.get().getOr(!1);o.fetch().get((s=>{Lx(e,r,o,t.layouts,s),n.link(e.element),a||Om.focusIn(r)}))},onClose:()=>{Zb.off(e),Rx.get().getOr(!1)||fd.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>Iv(o,n)||Iv(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),ab.config({channels:{...ux({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...gx({doReposition:()=>{wb.getState(Gg.getCoupled(e,"toolbarSandbox")).each((n=>{zx(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{wb.getState(Gg.getCoupled(t,"toolbarSandbox")).each((s=>{Lx(t,s,e,o.layouts,n)}))},reposition:t=>{wb.getState(Gg.getCoupled(t,"toolbarSandbox")).each((n=>{zx(t,n,e,o.layouts)}))},toggle:e=>{Nx(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{Rx.set(!0),Nx(e,t),Rx.clear()})(e,n)},getToolbar:e=>wb.getState(Gg.getCoupled(e,"toolbarSandbox")),isOpen:e=>wb.isOpen(Gg.getCoupled(e,"toolbarSandbox"))}}),configFields:Fx(),partFields:Ix(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),Hx=y([Qr("prefix","form-field"),Jp("fieldBehaviours",[Pg,Zh])]),Px=y([Zi({schema:[Rr("dom")],name:"label"}),Zi({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Rr("text")],name:"aria-descriptor"}),Ji({factory:{sketch:e=>{const t=((e,t)=>{const o={};return ie(e,((e,n)=>{I(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[Rr("factory")],name:"field"})]),Ux=lh({name:"FormField",configFields:Hx(),partFields:Px(),factory:(e,t,o,n)=>{const s=Zp(e.fieldBehaviours,[Pg.config({find:t=>ul(t,e,"field")}),Zh.config({store:{mode:"manual",getValue:e=>Pg.getCurrent(e).bind(Zh.getValue),setValue:(e,t)=>{Pg.getCurrent(e).each((e=>{Zh.setValue(e,t)}))}}})]),r=Hc([Jc(((t,o)=>{const n=gl(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Ne(e.prefix);n.label().each((e=>{qo(e.element,"for",o),qo(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Ne(e.prefix);qo(o.element,"id",n),qo(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>ul(t,e,"field"),getLabel:t=>ul(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}}),Wx=y([Qr("field1Name","field1"),Qr("field2Name","field2"),xi("onLockedChange"),hi(["lockClass"]),Qr("locked",!1),eh("coupledFieldBehaviours",[Pg,Zh]),sa("onInput",b)]),$x=(e,t)=>Ji({factory:Ux,name:e,overrides:e=>({fieldBehaviours:rd([ud("coupled-input-behaviour",[Wc(Ca(),(o=>{((e,t,o)=>ul(e,t,o).bind(Pg.getCurrent))(o,e,t).each((t=>{ul(o,e,"lock").each((n=>{Zb.isOn(n)&&e.onLockedChange(o,t,n),e.onInput(o)}))}))}))])])})}),Gx=y([$x("field1","field2"),$x("field2","field1"),Ji({factory:Tv,schema:[Rr("dom")],name:"lock",overrides:e=>({buttonBehaviours:rd([Zb.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),jx=lh({name:"FormCoupledInputs",configFields:Wx(),partFields:Gx(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:th(e.coupledFieldBehaviours,[Pg.config({find:A.some}),Zh.config({store:{mode:"manual",getValue:t=>{const o=fl(t,e,["field1","field2"]);return{[e.field1Name]:Zh.getValue(o.field1()),[e.field2Name]:Zh.getValue(o.field2())}},setValue:(t,o)=>{const n=fl(t,e,["field1","field2"]);ve(o,e.field1Name)&&Zh.setValue(n.field1(),o[e.field1Name]),ve(o,e.field2Name)&&Zh.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>ul(t,e,"field1"),getField2:t=>ul(t,e,"field2"),getLock:t=>ul(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),qx=ih({name:"HtmlSelect",configFields:[Rr("options"),Jp("selectBehaviours",[fd,Zh]),Qr("selectClasses",[]),Qr("selectAttributes",{}),$r("data")],factory:(e,t)=>{const o=L(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>ia("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Zp(e.selectBehaviours,[fd.config({}),Zh.config({store:{mode:"manual",getValue:e=>cs(e.element),setValue:(t,o)=>{const n=te(e.options);$(e.options,(e=>e.value===o)).isSome()?ds(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>ds(t.element,e.value)))},...n}})])}}}),Xx=ih({name:"InlineView",configFields:[Rr("lazySink"),bi("onShow"),bi("onHide"),Yr("onEscape"),Jp("inlineBehaviours",[wb,Zh,ab]),Jr("fireDismissalEventInstead",[Qr("event",Ya())]),Jr("fireRepositionEventInstead",[Qr("event",Ka())]),Qr("getRelated",A.none),Qr("isExtraPart",T),Qr("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();wb.openWhileCloaked(t,o,(()=>nb.positionWithinBounds(r,t,n,s()))),Zh.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>Zl(),onRtl:()=>ec()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return ix.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(wb.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{nb.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();nb.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();nb.positionWithinBounds(a,t,o,s()),V(n,(e=>{const t=i(e.triggeringPath);nb.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);wb.open(t,r),Zh.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{wb.isOpen(t)&&Zh.getValue(t).each((o=>{switch(o.mode){case"menu":wb.getState(t).each(ix.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();nb.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{wb.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{wb.isOpen(e)&&(Zh.setValue(e,A.none()),wb.close(e))},getContent:e=>wb.getState(e),reposition:s,isOpen:wb.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Zp(e.inlineBehaviours,[wb.config({isPartOf:(t,o,n)=>Iv(o,n)||((t,o)=>e.getRelated(t).exists((e=>Iv(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),Zh.config({store:{mode:"memory",initialValue:A.none()}}),ab.config({channels:{...ux({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...gx({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}}),Yx=y([ta("type","text"),$r("data"),Qr("inputAttributes",{}),Qr("inputStyles",{}),Qr("tag","input"),Qr("inputClasses",[]),bi("onSetValue"),sa("fromInputValue",w),sa("toInputValue",w),Qr("styles",{}),Qr("eventOrder",{}),Jp("inputBehaviours",[Zh,fd]),Qr("selectOnFocus",!0)]),Kx=e=>rd([fd.config({onFocus:e.selectOnFocus?t=>{const o=t.element,n=cs(o);"range"!==e.type&&o.dom.setSelectionRange(0,n.length)}:b})]),Jx=e=>({...Kx(e),...Zp(e.inputBehaviours,[Zh.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:t=>e.fromInputValue(cs(t.element)),setValue:(t,o)=>{cs(t.element)!==o&&ds(t.element,e.toInputValue(o))}},onSetValue:e.onSetValue})])}),Qx=e=>({tag:e.tag,attributes:{type:e.type,...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Zx=ih({name:"Input",configFields:Yx(),factory:(e,t)=>({uid:e.uid,dom:Qx(e),components:[],behaviours:Jx(e),eventOrder:e.eventOrder})}),ey=sl(jv(),qv()),ty=y([Rr("lazySink"),$r("dragBlockClass"),sa("getBounds",Ls),Qr("useTabstopAt",E),Qr("firstTabstop",0),Qr("eventOrder",{}),Jp("modalBehaviours",[Om]),vi("onExecute"),yi("onEscape")]),oy={sketch:w},ny=y([Zi({name:"draghandle",overrides:(e,t)=>({behaviours:rd([Dh.config({mode:"mouse",getTarget:e=>Gn(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Ji({schema:[Rr("dom")],name:"title"}),Ji({factory:oy,schema:[Rr("dom")],name:"close"}),Ji({factory:oy,schema:[Rr("dom")],name:"body"}),Zi({factory:oy,schema:[Rr("dom")],name:"footer"}),Qi({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[Qr("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Qr("components",[])],name:"blocker"})]),sy=lh({name:"ModalDialog",configFields:ty(),partFields:ny(),factory:(e,t,o,n)=>{const s=Xe(),r=Ne("modal-events"),a={...e.eventOrder,[qa()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])},i=Jt();return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([Ig(t)]),behaviours:rd([fd.config({}),ud("dialog-blocker-events",[Kc(xa(),(()=>{Lg.isBlocked(t)||Om.focusIn(t)}))])])});Nm(o,a),Om.focusIn(t)},hide:e=>{s.clear(),go(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Vm(e)}))}))},getBody:t=>ml(t,e,"body"),getFooter:t=>ul(t,e,"footer"),setIdle:e=>{Lg.unblock(e)},setBusy:(e,t)=>{Lg.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Zp(e.modalBehaviours,[ng.config({}),Om.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),Lg.config({getRoot:s.get}),ud(r,[Jc((t=>{const o=ml(t,e,"title").element,n=(e=>e.dom.textContent)(o);i.os.isMacOS()&&g(n)?qo(t.element,"aria-label",n):((e,t)=>{const o=Ko(e,"id").fold((()=>{const e=Ne("dialog-label");return qo(t,"id",e),e}),w);qo(e,"aria-labelledby",o)})(t.element,o)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),ry=Zi({schema:[Rr("dom")],name:"label"}),ay=e=>Zi({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Hc([$c(da(),((t,o,n)=>e(t,n)),[t]),$c(pa(),((t,o,n)=>e(t,n)),[t]),$c(ha(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),iy=ay("top-left"),ly=ay("top"),cy=ay("top-right"),dy=ay("right"),uy=ay("bottom-right"),my=ay("bottom"),gy=ay("bottom-left"),py=ay("left"),hy=Ji({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Hc([jc(da(),e,"spectrum"),jc(ua(),e,"spectrum"),jc(ma(),e,"spectrum"),jc(pa(),e,"spectrum"),jc(ha(),e,"spectrum"),jc(ba(),e,"spectrum")])})}),fy=e=>Nd(e.event),by=Ji({schema:[Fr("mouseIsDown",(()=>ye(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:rd([Om.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,fy(n)),onRight:(o,n)=>t.onRight(o,e,fy(n)),onUp:(o,n)=>t.onUp(o,e,fy(n)),onDown:(o,n)=>t.onDown(o,e,fy(n))}),Gb.config({}),fd.config({})]),events:Hc([Wc(da(),o),Wc(ua(),o),Wc(pa(),o),Wc(ha(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}});var vy=[ry,py,dy,ly,my,iy,cy,gy,uy,hy,by];const xy=y("slider.change.value"),yy=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>Dn(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>Dn(e.clientX,e.clientY))):A.none()}},wy=e=>e.model.minX,Sy=e=>e.model.minY,Cy=e=>e.model.minX-1,ky=e=>e.model.minY-1,Oy=e=>e.model.maxX,_y=e=>e.model.maxY,Ty=e=>e.model.maxX+1,Ey=e=>e.model.maxY+1,Ay=(e,t,o)=>t(e)-o(e),My=e=>Ay(e,Oy,wy),Dy=e=>Ay(e,_y,Sy),By=e=>My(e)/2,Fy=e=>Dy(e)/2,Iy=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,Ry=e=>e.snapToGrid,Ny=e=>e.snapStart,zy=e=>e.rounded,Ly=(e,t)=>void 0!==e[t+"-edge"],Vy=e=>Ly(e,"left"),Hy=e=>Ly(e,"right"),Py=e=>Ly(e,"top"),Uy=e=>Ly(e,"bottom"),Wy=e=>e.model.value.get(),$y=(e,t)=>({x:e,y:t}),Gy=(e,t)=>{Rc(e,xy(),{value:t})},jy=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),qy=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),Xy=(e,t,o)=>Math.max(t,Math.min(o,e)),Yy=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=Xy(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return Xy(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},Ky=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},Jy="top",Qy="right",Zy="bottom",ew="left",tw=e=>e.element.dom.getBoundingClientRect(),ow=(e,t)=>e[t],nw=e=>{const t=tw(e);return ow(t,ew)},sw=e=>{const t=tw(e);return ow(t,Qy)},rw=e=>{const t=tw(e);return ow(t,Jy)},aw=e=>{const t=tw(e);return ow(t,Zy)},iw=e=>{const t=tw(e);return ow(t,"width")},lw=e=>{const t=tw(e);return ow(t,"height")},cw=(e,t,o)=>(e+t)/2-o,dw=(e,t)=>{const o=tw(e),n=tw(t),s=ow(o,ew),r=ow(o,Qy),a=ow(n,ew);return cw(s,r,a)},uw=(e,t)=>{const o=tw(e),n=tw(t),s=ow(o,Jy),r=ow(o,Zy),a=ow(n,Jy);return cw(s,r,a)},mw=(e,t)=>{Rc(e,xy(),{value:t})},gw=(e,t,o)=>{const n={min:wy(t),max:Oy(t),range:My(t),value:o,step:Iy(t),snap:Ry(t),snapStart:Ny(t),rounded:zy(t),hasMinEdge:Vy(t),hasMaxEdge:Hy(t),minBound:nw(e),maxBound:sw(e),screenRange:iw(e)};return Yy(n)},pw=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?qy:jy)(Wy(o),wy(o),Oy(o),Iy(o,n));return mw(t,s),A.some(s)})(e,t,o,n).map(E),hw=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=iw(e),a=n.bind((t=>A.some(dw(t,e)))).getOr(0),i=s.bind((t=>A.some(dw(t,e)))).getOr(r),l={min:wy(t),max:Oy(t),range:My(t),value:o,hasMinEdge:Vy(t),hasMaxEdge:Hy(t),minBound:nw(e),minOffset:0,maxBound:sw(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return Ky(l)})(t,r,o,n,s);return nw(t)-nw(e)+a},fw=pw(-1),bw=pw(1),vw=A.none,xw=A.none,yw={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{Gy(e,Ty(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{Gy(e,Cy(t))}))};var ww=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=gw(e,t,o);return mw(e,n),n},setToMin:(e,t)=>{const o=wy(t);mw(e,o)},setToMax:(e,t)=>{const o=Oy(t);mw(e,o)},findValueOfOffset:gw,getValueFromEvent:e=>yy(e).map((e=>e.left)),findPositionOfValue:hw,setPositionFromValue:(e,t,o,n)=>{const s=Wy(o),r=hw(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=En(t.element)/2;mn(t.element,"left",r-a+"px")},onLeft:fw,onRight:bw,onUp:vw,onDown:xw,edgeActions:yw});const Sw=(e,t)=>{Rc(e,xy(),{value:t})},Cw=(e,t,o)=>{const n={min:Sy(t),max:_y(t),range:Dy(t),value:o,step:Iy(t),snap:Ry(t),snapStart:Ny(t),rounded:zy(t),hasMinEdge:Py(t),hasMaxEdge:Uy(t),minBound:rw(e),maxBound:aw(e),screenRange:lw(e)};return Yy(n)},kw=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?qy:jy)(Wy(o),Sy(o),_y(o),Iy(o,n));return Sw(t,s),A.some(s)})(e,t,o,n).map(E),Ow=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=lw(e),a=n.bind((t=>A.some(uw(t,e)))).getOr(0),i=s.bind((t=>A.some(uw(t,e)))).getOr(r),l={min:Sy(t),max:_y(t),range:Dy(t),value:o,hasMinEdge:Py(t),hasMaxEdge:Uy(t),minBound:rw(e),minOffset:0,maxBound:aw(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return Ky(l)})(t,r,o,n,s);return rw(t)-rw(e)+a},_w=A.none,Tw=A.none,Ew=kw(-1),Aw=kw(1),Mw={"top-left":A.none(),top:A.some(((e,t)=>{Gy(e,ky(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{Gy(e,Ey(t))})),"bottom-left":A.none(),left:A.none()};var Dw=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Cw(e,t,o);return Sw(e,n),n},setToMin:(e,t)=>{const o=Sy(t);Sw(e,o)},setToMax:(e,t)=>{const o=_y(t);Sw(e,o)},findValueOfOffset:Cw,getValueFromEvent:e=>yy(e).map((e=>e.top)),findPositionOfValue:Ow,setPositionFromValue:(e,t,o,n)=>{const s=Wy(o),r=Ow(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=kn(t.element)/2;mn(t.element,"top",r-a+"px")},onLeft:_w,onRight:Tw,onUp:Ew,onDown:Aw,edgeActions:Mw});const Bw=(e,t)=>{Rc(e,xy(),{value:t})},Fw=(e,t)=>({x:e,y:t}),Iw=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?qy:jy,a=t?Wy(n).x:r(Wy(n).x,wy(n),Oy(n),Iy(n,s)),i=t?r(Wy(n).y,Sy(n),_y(n),Iy(n,s)):Wy(n).y;return Bw(o,Fw(a,i)),A.some(a)})(e,t,o,n,s).map(E),Rw=Iw(-1,!1),Nw=Iw(1,!1),zw=Iw(-1,!0),Lw=Iw(1,!0),Vw={"top-left":A.some(((e,t)=>{Gy(e,$y(Cy(t),ky(t)))})),top:A.some(((e,t)=>{Gy(e,$y(By(t),ky(t)))})),"top-right":A.some(((e,t)=>{Gy(e,$y(Ty(t),ky(t)))})),right:A.some(((e,t)=>{Gy(e,$y(Ty(t),Fy(t)))})),"bottom-right":A.some(((e,t)=>{Gy(e,$y(Ty(t),Ey(t)))})),bottom:A.some(((e,t)=>{Gy(e,$y(By(t),Ey(t)))})),"bottom-left":A.some(((e,t)=>{Gy(e,$y(Cy(t),Ey(t)))})),left:A.some(((e,t)=>{Gy(e,$y(Cy(t),Fy(t)))}))};var Hw=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=gw(e,t,o.left),s=Cw(e,t,o.top),r=Fw(n,s);return Bw(e,r),r},setToMin:(e,t)=>{const o=wy(t),n=Sy(t);Bw(e,Fw(o,n))},setToMax:(e,t)=>{const o=Oy(t),n=_y(t);Bw(e,Fw(o,n))},getValueFromEvent:e=>yy(e),setPositionFromValue:(e,t,o,n)=>{const s=Wy(o),r=hw(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=Ow(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=En(t.element)/2,l=kn(t.element)/2;mn(t.element,"left",r-i+"px"),mn(t.element,"top",a-l+"px")},onLeft:Rw,onRight:Nw,onUp:zw,onDown:Lw,edgeActions:Vw});const Pw=lh({name:"Slider",configFields:[Qr("stepSize",1),Qr("speedMultiplier",10),Qr("onChange",b),Qr("onChoose",b),Qr("onInit",b),Qr("onDragStart",b),Qr("onDragEnd",b),Qr("snapToGrid",!1),Qr("rounded",!0),$r("snapStart"),Nr("model",Mr("mode",{x:[Qr("minX",0),Qr("maxX",100),Fr("value",(e=>ye(e.mode.minX))),Rr("getInitialValue"),wi("manager",ww)],y:[Qr("minY",0),Qr("maxY",100),Fr("value",(e=>ye(e.mode.minY))),Rr("getInitialValue"),wi("manager",Dw)],xy:[Qr("minX",0),Qr("maxX",100),Qr("minY",0),Qr("maxY",100),Fr("value",(e=>ye({x:e.mode.minX,y:e.mode.minY}))),Rr("getInitialValue"),wi("manager",Hw)]})),Jp("sliderBehaviours",[Om,Zh]),Fr("mouseIsDown",(()=>ye(!1)))],partFields:vy,factory:(e,t,o,n)=>{const s=t=>ml(t,e,"thumb"),r=t=>ml(t,e,"spectrum"),a=t=>ul(t,e,"left-edge"),i=t=>ul(t,e,"right-edge"),l=t=>ul(t,e,"top-edge"),c=t=>ul(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&ul(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{ul(t,e,"spectrum").map(Om.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Zp(e.sliderBehaviours,[Om.config({mode:"special",focusIn:b}),Zh.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),ab.config({channels:{[Ad()]:{onReceive:p}}})]),events:Hc([Wc(xy(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Jc(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Wc(da(),h),Wc(ma(),f),Wc(pa(),((e,t)=>{b(e),h(e,t)})),Wc(ba(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),Uw="container",Ww=[Jp("slotBehaviours",[])],$w=e=>"<alloy.field."+e+">",Gw=(e,t)=>{const o=t=>hl(e),n=(t,o)=>(n,s)=>ul(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==Yo(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;mn(o,"display","none"),qo(o,"aria-hidden","true"),Rc(e,Qa(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{V(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;yn(o,"display"),Qo(o,"aria-hidden"),Rc(e,Qa(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>ul(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:Qp(e.slotBehaviours),apis:c}},jw=le({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Am(e))),qw={...jw,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),al(Uw,$w(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>Ji({name:e,pname:$w(e)})));return nh(Uw,Ww,s,Gw,o)}},Xw=(e,t,o)=>({within:e,extra:t,withinWidth:o}),Yw=(e,t,o)=>{const n=W(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=P(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},Kw=e=>L(e,(e=>e.element)),Jw=(e,t)=>{const o=L(t,(e=>Ig(e)));Bx.setGroups(e,o)},Qw=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=ml(e,t,"primary"),r=Gg.getCoupled(e,"overflowGroup");mn(s.element,"visibility","hidden");const a=n.concat([r]),i=se(a,(e=>Ho(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),Jw(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=Yw(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>Yw(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=Kw(e.concat(t));return Xw(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=Kw(e).concat([o]);return Xw(s,Kw(t),n)})(r,a,n,i):((e,t,o)=>Xw(Kw(e),[],o))(r,0,i)})(En(s.element),t.builtGroups.get(),(e=>Math.ceil(e.element.dom.getBoundingClientRect().width)),r);0===l.extra.length?(ng.remove(s,r),o([])):(Jw(s,l.within),o(l.extra)),yn(s.element,"visibility"),wn(s.element),i.each(fd.focus)},Zw=y([Jp("splitToolbarBehaviours",[Gg]),Fr("builtGroups",(()=>ye([])))]),eS=y([hi(["overflowToggledClass"]),Yr("getOverflowBounds"),Rr("lazySink"),Fr("overflowGroups",(()=>ye([]))),bi("onOpened"),bi("onClosed")].concat(Zw())),tS=y([Ji({factory:Bx,schema:Mx(),name:"primary"}),Qi({schema:Mx(),name:"overflow"}),Qi({name:"overflow-button"}),Qi({name:"overflow-group"})]),oS=y([Rr("items"),hi(["itemSelector"]),Jp("tgroupBehaviours",[Om])]),nS=y([el({name:"items",unit:"item"})]),sS=lh({name:"ToolbarGroup",configFields:oS(),partFields:nS(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Zp(e.tgroupBehaviours,[Om.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),rS=e=>L(e,(e=>Ig(e))),aS=(e,t,o)=>{Qw(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{Vx.setGroups(e,rS(n))}))}))},iS=lh({name:"SplitFloatingToolbar",configFields:eS(),partFields:tS(),factory:(e,t,o,n)=>{const s=pv(Vx.sketch({fetch:()=>Oe((t=>{t(rS(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Ul,Pl],onRtl:()=>[Pl,Ul],onBottomLtr:()=>[$l,Wl],onBottomRtl:()=>[Wl,$l]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Zp(e.splitToolbarBehaviours,[Gg.config({others:{overflowGroup:()=>sS.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(L(o,t.getSystem().build)),aS(t,s,e)},refresh:t=>aS(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{Vx.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(Vx.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(Vx.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{Vx.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(Vx.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),lS=y([hi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),bi("onOpened"),bi("onClosed")].concat(Zw())),cS=y([Ji({factory:Bx,schema:Mx(),name:"primary"}),Ji({factory:Bx,schema:Mx(),name:"overflow",overrides:e=>({toolbarBehaviours:rd([zb.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{ul(t,e,"overflow-button").each((e=>{Zb.off(e)})),e.onClosed(t)},onGrown:t=>{e.onOpened(t)},onStartGrow:t=>{ul(t,e,"overflow-button").each(Zb.on)}}),Om.config({mode:"acyclic",onEscape:t=>(ul(t,e,"overflow-button").each(fd.focus),A.some(!0))})])})}),Qi({name:"overflow-button",overrides:e=>({buttonBehaviours:rd([Zb.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),Qi({name:"overflow-group"})]),dS=(e,t,o)=>{ul(e,t,"overflow-button").each((n=>{ul(e,t,"overflow").each((s=>{if(uS(e,t),zb.hasShrunk(s)){const e=t.onOpened;t.onOpened=n=>{o||Om.focusIn(s),e(n),t.onOpened=e}}else{const e=t.onClosed;t.onClosed=s=>{o||fd.focus(n),e(s),t.onClosed=e}}zb.toggleGrow(s)}))}))},uS=(e,t)=>{ul(e,t,"overflow").each((o=>{Qw(e,t,(e=>{const t=L(e,(e=>Ig(e)));Bx.setGroups(o,t)})),ul(e,t,"overflow-button").each((e=>{zb.hasGrown(o)&&Zb.on(e)})),zb.refresh(o)}))},mS=lh({name:"SplitSlidingToolbar",configFields:lS(),partFields:cS(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Zp(e.splitToolbarBehaviours,[Gg.config({others:{overflowGroup:e=>sS.sketch({...n["overflow-group"](),items:[Tv.sketch({...n["overflow-button"](),action:t=>{Ic(e,s)}})]})}}),ud("toolbar-toggle-events",[Wc(s,(t=>{dS(t,e,!1)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=L(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),uS(t,e)},refresh:t=>uS(t,e),toggle:t=>{dS(t,e,!1)},toggleWithoutFocusing:t=>{dS(t,e,!0)},isOpen:t=>((e,t)=>ul(e,t,"overflow").map(zb.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),gS=ih({name:"TabButton",configFields:[Qr("uid",void 0),Rr("value"),Br("dom","dom",cr((()=>({attributes:{role:"tab",id:Ne("aria"),"aria-selected":"false"}}))),Zs()),$r("action"),Qr("domModification",{}),Jp("tabButtonBehaviours",[fd,Om,Zh]),Rr("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:_v(e.action),behaviours:Zp(e.tabButtonBehaviours,[fd.config({}),Om.config({mode:"execution",useSpace:!0,useEnter:!0}),Zh.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),pS=y([Rr("tabs"),Rr("dom"),Qr("clickToDismiss",!1),Jp("tabbarBehaviours",[Yd,Om]),hi(["tabClass","selectedClass"])]),hS=el({factory:gS,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Yd.dehighlight(e,t),Rc(e,ti(),{tabbar:e,button:t})},o=(e,t)=>{Yd.highlight(e,t),Rc(e,ei(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Yd.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),fS=y([hS]),bS=lh({name:"Tabbar",configFields:pS(),partFields:fS(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Zp(e.tabbarBehaviours,[Yd.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{qo(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{qo(t.element,"aria-selected","false")}}),Om.config({mode:"flow",getInitial:e=>Yd.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),vS=ih({name:"Tabview",configFields:[Jp("tabviewBehaviours",[ng])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Zp(e.tabviewBehaviours,[ng.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),xS=y([Qr("selectFirst",!0),bi("onChangeTab"),bi("onDismissTab"),Qr("tabs",[]),Jp("tabSectionBehaviours",[])]),yS=Ji({factory:bS,schema:[Rr("dom"),Pr("markers",[Rr("tabClass"),Rr("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),wS=Ji({factory:vS,name:"tabview"}),SS=y([yS,wS]),CS=lh({name:"TabSection",configFields:xS(),partFields:SS(),factory:(e,t,o,n)=>{const s=(t,o)=>{ul(t,e,"tabbar").each((e=>{o(e).each(Nc)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:Qp(e.tabSectionBehaviours),events:Hc(j([e.selectFirst?[Jc(((e,t)=>{s(e,Yd.getFirst)}))]:[],[Wc(ei(),((t,o)=>{(t=>{const o=Zh.getValue(t);ul(t,e,"tabview").each((n=>{$(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Ko(t.element,"id").each((e=>{qo(n.element,"aria-labelledby",e)})),ng.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Wc(ti(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>ul(t,e,"tabview").map((e=>ng.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Yd.getCandidates(e);return $(o,(e=>Zh.getValue(e)===t)).filter((t=>!Yd.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),kS=(e,t,o)=>{const n=Zh.getValue(o);Zh.setValue(t,n),_S(t)},OS=(e,t)=>{const o=e.element,n=cs(o),s=o.dom;"number"!==Yo(o,"type")&&t(s,n)},_S=e=>{OS(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},TS=y("alloy.typeahead.itemexecute"),ES=y([$r("lazySink"),Rr("fetch"),Qr("minChars",5),Qr("responseTime",1e3),bi("onOpen"),Qr("getHotspot",A.some),Qr("getAnchorOverrides",y({})),Qr("layouts",A.none()),Qr("eventOrder",{}),aa("model",{},[Qr("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),Qr("selectsOver",!0),Qr("populateFromBrowse",!0)]),bi("onSetValue"),vi("onExecute"),bi("onItemExecute"),Qr("inputClasses",[]),Qr("inputAttributes",{}),Qr("inputStyles",{}),Qr("matchWidth",!0),Qr("useMinWidth",!1),Qr("dismissOnBlur",!0),hi(["openClass"]),$r("initialData"),$r("listRole"),Jp("typeaheadBehaviours",[fd,Zh,Ub,Om,Zb,Gg]),Fr("lazyTypeaheadComp",(()=>ye(A.none))),Fr("previewing",(()=>ye(!0)))].concat(Yx()).concat(wx())),AS=y([Qi({schema:[pi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=Zh.getValue(t),s=e.getDisplayText(n),r=Zh.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{kS(0,t,o),((e,t)=>{OS(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Yd.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&kS(e.model,t,n),Ko(n.element,"id").each((e=>qo(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Rc(e,TS(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&kS(e.model,t,o)}))}})})]),MS=lh({name:"Typeahead",configFields:ES(),partFields:AS(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=Gg.getCoupled(t,"sandbox");if(wb.isOpen(r))Pg.getCurrent(r).each((e=>{Yd.getHighlighted(e).fold((()=>{s(e)}),(()=>{Vc(r,e.element,"keydown",o)}))}));else{const o=e=>{Pg.getCurrent(e).each(s)};hx(e,a(t),t,r,n,o,Ac.HighlightMenuAndItem).get(b)}},r=Kx(e),a=e=>t=>t.map((t=>{const o=he(t.menus),n=q(o,(e=>P(e.items,(e=>"item"===e.type))));return Zh.getState(e).update(L(n,(e=>e.data))),t})),i=e=>Pg.getCurrent(e),l="typeaheadevents",c=[fd.config({}),Zh.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>cs(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{ds(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>ia("initialValue",e))).getOr({})}}),Ub.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=Gg.getCoupled(t,"sandbox");if(fd.isFocused(t)&&cs(t.element).length>=e.minChars){const o=i(s).bind((e=>Yd.getHighlighted(e).map(Zh.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Yd.highlightFirst(t)}),(e=>{Yd.highlightBy(t,(t=>Zh.getValue(t).value===e.value)),Yd.getHighlighted(t).orThunk((()=>(Yd.highlightFirst(t),A.none())))}))}))};hx(e,a(t),t,s,n,r,Ac.HighlightJustMenu).get(b)}},cancelEvent:Pa()}),Om.config({mode:"special",onDown:(e,t)=>(s(e,t,Yd.highlightFirst),A.some(!0)),onEscape:e=>{const t=Gg.getCoupled(e,"sandbox");return wb.isOpen(t)?(wb.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Yd.highlightLast),A.some(!0)),onEnter:t=>{const o=Gg.getCoupled(t,"sandbox"),n=wb.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Yd.getHighlighted(e))).map((e=>(Rc(t,TS(),{item:e}),!0)));{const s=Zh.getValue(t);return Ic(t,Pa()),e.onExecute(o,t,s),n&&wb.close(o),A.some(!0)}}}),Zb.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),Gg.config({others:{sandbox:t=>yx(e,t,{onOpen:()=>Zb.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Qo(e.element,"aria-activedescendant"))),Zb.off(t)}})}}),ud(l,[Jc((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Qc((t=>{e.lazyTypeaheadComp.set(A.none())})),ed((t=>{const o=b;bx(e,a(t),t,n,o,Ac.HighlightMenuAndItem).get(b)})),Wc(TS(),((t,o)=>{const n=Gg.getCoupled(t,"sandbox");kS(e.model,t,o.event.item),Ic(t,Pa()),e.onItemExecute(t,n,o.event.item,Zh.getValue(t)),wb.close(n),_S(t)}))].concat(e.dismissOnBlur?[Wc(Fa(),(e=>{const t=Gg.getCoupled(e,"sandbox");Ho(t.element).isNone()&&wb.close(t)}))]:[]))],d={[Xa()]:[Zh.name(),Ub.name(),l],...e.eventOrder};return{uid:e.uid,dom:Qx(Le(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Zp(e.typeaheadBehaviours,c)},eventOrder:d}}});var DS,BS,FS=tinymce.util.Tools.resolve("tinymce.ThemeManager"),IS=tinymce.util.Tools.resolve("tinymce.util.Delay"),RS=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),NS=tinymce.util.Tools.resolve("tinymce.EditorManager"),zS=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(DS||(DS={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(BS||(BS={}));const LS=e=>t=>t.options.get(e),VS=e=>t=>A.from(e(t)),HS=e=>{const t=zS.deviceType.isPhone(),o=zS.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:RS.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),N(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:BS.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!zS.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},PS=LS("readonly"),US=LS("disabled"),WS=LS("height"),$S=LS("width"),GS=VS(LS("min_width")),jS=VS(LS("min_height")),qS=VS(LS("max_width")),XS=VS(LS("max_height")),YS=VS(LS("style_formats")),KS=LS("style_formats_merge"),JS=LS("style_formats_autohide"),QS=LS("content_langs"),ZS=LS("removed_menuitems"),eC=LS("toolbar_mode"),tC=LS("toolbar_groups"),oC=LS("toolbar_location"),nC=LS("fixed_toolbar_container"),sC=LS("fixed_toolbar_container_target"),rC=LS("toolbar_persist"),aC=LS("toolbar_sticky_offset"),iC=LS("menubar"),lC=LS("toolbar"),cC=LS("file_picker_callback"),dC=LS("file_picker_validator_handler"),uC=LS("font_size_input_default_unit"),mC=LS("file_picker_types"),gC=LS("typeahead_urls"),pC=LS("anchor_top"),hC=LS("anchor_bottom"),fC=LS("draggable_modal"),bC=LS("statusbar"),vC=LS("elementpath"),xC=LS("branding"),yC=LS("resize"),wC=LS("paste_as_text"),SC=LS("sidebar_show"),CC=LS("promotion"),kC=LS("help_accessibility"),OC=LS("default_font_stack"),_C=LS("skin"),TC=e=>!1===e.options.get("skin"),EC=e=>!1!==e.options.get("menubar"),AC=e=>{const t=e.options.get("skin_url");if(TC(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return NS.baseURL+"/skins/ui/"+t}},MC=e=>e.options.get("line_height_formats").split(" "),DC=e=>{const t=lC(e),o=r(t),n=l(t)&&t.length>0;return!FC(e)&&(n||o||!0===t)},BC=e=>{const t=N(9,(t=>e.options.get("toolbar"+(t+1)))),o=P(t,r);return $e(o.length>0,o)},FC=e=>BC(e).fold((()=>{const t=lC(e);return f(t,r)&&t.length>0}),E),IC=e=>oC(e)===BS.bottom,RC=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=nC(e))&&void 0!==t?t:"";if(o.length>0)return Xn(ln(),o);const n=sC(e);return g(n)?A.some(ct(n)):A.none()},NC=e=>e.inline&&RC(e).isSome(),zC=e=>RC(e).getOrThunk((()=>Oo(ko(ct(e.getElement()))))),LC=e=>e.inline&&!EC(e)&&!DC(e)&&!FC(e),VC=e=>(e.options.get("toolbar_sticky")||e.inline)&&!NC(e)&&!LC(e),HC=e=>!NC(e)&&"split"===e.options.get("ui_mode"),PC=e=>{const t=e.options.get("menu");return le(t,(e=>({...e,items:e.items})))};var UC=Object.freeze({__proto__:null,get ToolbarMode(){return DS},get ToolbarLocation(){return BS},register:HS,getSkinUrl:AC,getSkinUrlOption:e=>A.from(e.options.get("skin_url")),isReadOnly:PS,isDisabled:US,getSkin:_C,isSkinDisabled:TC,getHeightOption:WS,getWidthOption:$S,getMinWidthOption:GS,getMinHeightOption:jS,getMaxWidthOption:qS,getMaxHeightOption:XS,getUserStyleFormats:YS,shouldMergeStyleFormats:KS,shouldAutoHideStyleFormats:JS,getLineHeightFormats:MC,getContentLanguages:QS,getRemovedMenuItems:ZS,isMenubarEnabled:EC,isMultipleToolbars:FC,isToolbarEnabled:DC,isToolbarPersist:rC,getMultipleToolbarsOption:BC,getUiContainer:zC,useFixedContainer:NC,isSplitUiMode:HC,getToolbarMode:eC,isDraggableModal:fC,isDistractionFree:LC,isStickyToolbar:VC,getStickyToolbarOffset:aC,getToolbarLocation:oC,isToolbarLocationBottom:IC,getToolbarGroups:tC,getMenus:PC,getMenubar:iC,getToolbar:lC,getFilePickerCallback:cC,getFilePickerTypes:mC,useTypeaheadUrls:gC,getAnchorTop:pC,getAnchorBottom:hC,getFilePickerValidatorHandler:dC,getFontSizeInputDefaultUnit:uC,useStatusBar:bC,useElementPath:vC,promotionEnabled:CC,useBranding:xC,getResize:yC,getPasteAsText:wC,getSidebarShow:SC,useHelpAccessibility:kC,getDefaultFontStack:OC});const WC=["visible","hidden","clip"],$C=e=>et(e).length>0&&!I(WC,e),GC=e=>{if(oo(e)){const t=hn(e,"overflow-x"),o=hn(e,"overflow-y");return $C(t)||$C(o)}return!1},jC=e=>e.plugins.fullscreen&&e.plugins.fullscreen.isFullscreen(),qC=(e,t)=>HC(e)?((e,t)=>{const o=us(t,GC),n=0===o.length?_o(t).map(To).map((e=>us(e,GC))).getOr([]):o;return te(n).map((t=>({element:t,others:n.slice(1),isFullscreen:()=>jC(e)})))})(e,t):A.none(),XC=e=>{const t=[...L(e.others,Rs),Ls()];return e.isFullscreen()?Ls():((e,t)=>W(t,((e,t)=>zs(e,t)),e))(Rs(e.element),t)},{entries:YC,setPrototypeOf:KC,isFrozen:JC,getPrototypeOf:QC,getOwnPropertyDescriptor:ZC}=Object;let{freeze:ek,seal:tk,create:ok}=Object,{apply:nk,construct:sk}="undefined"!=typeof Reflect&&Reflect;ek||(ek=function(e){return e}),tk||(tk=function(e){return e}),nk||(nk=function(e,t,o){return e.apply(t,o)}),sk||(sk=function(e,t){return new e(...t)});const rk=yk(Array.prototype.forEach),ak=yk(Array.prototype.lastIndexOf),ik=yk(Array.prototype.pop),lk=yk(Array.prototype.push),ck=yk(Array.prototype.splice),dk=yk(String.prototype.toLowerCase),uk=yk(String.prototype.toString),mk=yk(String.prototype.match),gk=yk(String.prototype.replace),pk=yk(String.prototype.indexOf),hk=yk(String.prototype.trim),fk=yk(Object.prototype.hasOwnProperty),bk=yk(RegExp.prototype.test),vk=(xk=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return sk(xk,t)});var xk;function yk(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return nk(e,t,n)}}function wk(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:dk;KC&&KC(e,null);let n=t.length;for(;n--;){let s=t[n];if("string"==typeof s){const e=o(s);e!==s&&(JC(t)||(t[n]=e),s=e)}e[s]=!0}return e}function Sk(e){for(let t=0;t<e.length;t++)fk(e,t)||(e[t]=null);return e}function Ck(e){const t=ok(null);for(const[o,n]of YC(e))fk(e,o)&&(Array.isArray(n)?t[o]=Sk(n):n&&"object"==typeof n&&n.constructor===Object?t[o]=Ck(n):t[o]=n);return t}function kk(e,t){for(;null!==e;){const o=ZC(e,t);if(o){if(o.get)return yk(o.get);if("function"==typeof o.value)return yk(o.value)}e=QC(e)}return function(){return null}}const Ok=ek(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),_k=ek(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Tk=ek(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ek=ek(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ak=ek(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Mk=ek(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Dk=ek(["#text"]),Bk=ek(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Fk=ek(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ik=ek(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Rk=ek(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Nk=tk(/\{\{[\w\W]*|[\w\W]*\}\}/gm),zk=tk(/<%[\w\W]*|[\w\W]*%>/gm),Lk=tk(/\$\{[\w\W]*/gm),Vk=tk(/^data-[\-\w.\u00B7-\uFFFF]+$/),Hk=tk(/^aria-[\-\w]+$/),Pk=tk(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Uk=tk(/^(?:\w+script|data):/i),Wk=tk(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),$k=tk(/^html$/i),Gk=tk(/^[a-z][.\w]*(-[.\w]+)+$/i);var jk=Object.freeze({__proto__:null,ARIA_ATTR:Hk,ATTR_WHITESPACE:Wk,CUSTOM_ELEMENT:Gk,DATA_ATTR:Vk,DOCTYPE_NAME:$k,ERB_EXPR:zk,IS_ALLOWED_URI:Pk,IS_SCRIPT_OR_DATA:Uk,MUSTACHE_EXPR:Nk,TMPLIT_EXPR:Lk});const qk=function(){return"undefined"==typeof window?null:window};var Xk=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:qk();const o=t=>e(t);if(o.version="3.2.6",o.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)return o.isSupported=!1,o;let{document:n}=t;const s=n,r=s.currentScript,{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=kk(h,"cloneNode"),b=kk(h,"remove"),v=kk(h,"nextSibling"),x=kk(h,"childNodes"),y=kk(h,"parentNode");if("function"==typeof i){const e=n.createElement("template");e.content&&e.content.ownerDocument&&(n=e.content.ownerDocument)}let w,S="";const{implementation:C,createNodeIterator:k,createDocumentFragment:O,getElementsByTagName:_}=n,{importNode:T}=s;let E={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof YC&&"function"==typeof y&&C&&void 0!==C.createHTMLDocument;const{MUSTACHE_EXPR:A,ERB_EXPR:M,TMPLIT_EXPR:D,DATA_ATTR:B,ARIA_ATTR:F,IS_SCRIPT_OR_DATA:I,ATTR_WHITESPACE:R,CUSTOM_ELEMENT:N}=jk;let{IS_ALLOWED_URI:z}=jk,L=null;const V=wk({},[...Ok,..._k,...Tk,...Ak,...Dk]);let H=null;const P=wk({},[...Bk,...Fk,...Ik,...Rk]);let U=Object.seal(ok(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),W=null,$=null,G=!0,j=!0,q=!1,X=!0,Y=!1,K=!0,J=!1,Q=!1,Z=!1,ee=!1,te=!1,oe=!1,ne=!0,se=!1,re=!0,ae=!1,ie={},le=null;const ce=wk({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let de=null;const ue=wk({},["audio","video","img","source","image","track"]);let me=null;const ge=wk({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),pe="http://www.w3.org/1998/Math/MathML",he="http://www.w3.org/2000/svg",fe="http://www.w3.org/1999/xhtml";let be=fe,ve=!1,xe=null;const ye=wk({},[pe,he,fe],uk);let we=wk({},["mi","mo","mn","ms","mtext"]),Se=wk({},["annotation-xml"]);const Ce=wk({},["title","style","font","a","script"]);let ke=null;const Oe=["application/xhtml+xml","text/html"];let _e=null,Te=null;const Ee=n.createElement("form"),Ae=function(e){return e instanceof RegExp||e instanceof Function},Me=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Te||Te!==e){if(e&&"object"==typeof e||(e={}),e=Ck(e),ke=-1===Oe.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,_e="application/xhtml+xml"===ke?uk:dk,L=fk(e,"ALLOWED_TAGS")?wk({},e.ALLOWED_TAGS,_e):V,H=fk(e,"ALLOWED_ATTR")?wk({},e.ALLOWED_ATTR,_e):P,xe=fk(e,"ALLOWED_NAMESPACES")?wk({},e.ALLOWED_NAMESPACES,uk):ye,me=fk(e,"ADD_URI_SAFE_ATTR")?wk(Ck(ge),e.ADD_URI_SAFE_ATTR,_e):ge,de=fk(e,"ADD_DATA_URI_TAGS")?wk(Ck(ue),e.ADD_DATA_URI_TAGS,_e):ue,le=fk(e,"FORBID_CONTENTS")?wk({},e.FORBID_CONTENTS,_e):ce,W=fk(e,"FORBID_TAGS")?wk({},e.FORBID_TAGS,_e):Ck({}),$=fk(e,"FORBID_ATTR")?wk({},e.FORBID_ATTR,_e):Ck({}),ie=!!fk(e,"USE_PROFILES")&&e.USE_PROFILES,G=!1!==e.ALLOW_ARIA_ATTR,j=!1!==e.ALLOW_DATA_ATTR,q=e.ALLOW_UNKNOWN_PROTOCOLS||!1,X=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Y=e.SAFE_FOR_TEMPLATES||!1,K=!1!==e.SAFE_FOR_XML,J=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Z=e.FORCE_BODY||!1,ne=!1!==e.SANITIZE_DOM,se=e.SANITIZE_NAMED_PROPS||!1,re=!1!==e.KEEP_CONTENT,ae=e.IN_PLACE||!1,z=e.ALLOWED_URI_REGEXP||Pk,be=e.NAMESPACE||fe,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,Se=e.HTML_INTEGRATION_POINTS||Se,U=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(U.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(U.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(U.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Y&&(j=!1),te&&(ee=!0),ie&&(L=wk({},Dk),H=[],!0===ie.html&&(wk(L,Ok),wk(H,Bk)),!0===ie.svg&&(wk(L,_k),wk(H,Fk),wk(H,Rk)),!0===ie.svgFilters&&(wk(L,Tk),wk(H,Fk),wk(H,Rk)),!0===ie.mathMl&&(wk(L,Ak),wk(H,Ik),wk(H,Rk))),e.ADD_TAGS&&(L===V&&(L=Ck(L)),wk(L,e.ADD_TAGS,_e)),e.ADD_ATTR&&(H===P&&(H=Ck(H)),wk(H,e.ADD_ATTR,_e)),e.ADD_URI_SAFE_ATTR&&wk(me,e.ADD_URI_SAFE_ATTR,_e),e.FORBID_CONTENTS&&(le===ce&&(le=Ck(le)),wk(le,e.FORBID_CONTENTS,_e)),re&&(L["#text"]=!0),J&&wk(L,["html","head","body"]),L.table&&(wk(L,["tbody"]),delete W.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw vk('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw vk('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');w=e.TRUSTED_TYPES_POLICY,S=w.createHTML("")}else void 0===w&&(w=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,r)),null!==w&&"string"==typeof S&&(S=w.createHTML(""));ek&&ek(e),Te=e}},De=wk({},[..._k,...Tk,...Ek]),Be=wk({},[...Ak,...Mk]),Fe=function(e){lk(o.removed,{element:e});try{y(e).removeChild(e)}catch(t){b(e)}},Ie=function(e,t){try{lk(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){lk(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ee||te)try{Fe(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Re=function(e){let t=null,o=null;if(Z)e="<remove></remove>"+e;else{const t=mk(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ke&&be===fe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const s=w?w.createHTML(e):e;if(be===fe)try{t=(new g).parseFromString(s,ke)}catch(e){}if(!t||!t.documentElement){t=C.createDocument(be,"template",null);try{t.documentElement.innerHTML=ve?S:s}catch(e){}}const r=t.body||t.documentElement;return e&&o&&r.insertBefore(n.createTextNode(o),r.childNodes[0]||null),be===fe?_.call(t,J?"html":"body")[0]:J?t.documentElement:r},Ne=function(e){return k.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT|d.SHOW_PROCESSING_INSTRUCTION|d.SHOW_CDATA_SECTION,null)},ze=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Le=function(e){return"function"==typeof l&&e instanceof l};function Ve(e,t,n){rk(e,(e=>{e.call(o,t,n,Te)}))}const He=function(e){let t=null;if(Ve(E.beforeSanitizeElements,e,null),ze(e))return Fe(e),!0;const n=_e(e.nodeName);if(Ve(E.uponSanitizeElement,e,{tagName:n,allowedTags:L}),K&&e.hasChildNodes()&&!Le(e.firstElementChild)&&bk(/<[/\w!]/g,e.innerHTML)&&bk(/<[/\w!]/g,e.textContent))return Fe(e),!0;if(7===e.nodeType)return Fe(e),!0;if(K&&8===e.nodeType&&bk(/<[/\w]/g,e.data))return Fe(e),!0;if(!L[n]||W[n]){if(!W[n]&&Ue(n)){if(U.tagNameCheck instanceof RegExp&&bk(U.tagNameCheck,n))return!1;if(U.tagNameCheck instanceof Function&&U.tagNameCheck(n))return!1}if(re&&!le[n]){const t=y(e)||e.parentNode,o=x(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n){const s=f(o[n],!0);s.__removalCount=(e.__removalCount||0)+1,t.insertBefore(s,v(e))}}return Fe(e),!0}return e instanceof c&&!function(e){let t=y(e);t&&t.tagName||(t={namespaceURI:be,tagName:"template"});const o=dk(e.tagName),n=dk(t.tagName);return!!xe[e.namespaceURI]&&(e.namespaceURI===he?t.namespaceURI===fe?"svg"===o:t.namespaceURI===pe?"svg"===o&&("annotation-xml"===n||we[n]):Boolean(De[o]):e.namespaceURI===pe?t.namespaceURI===fe?"math"===o:t.namespaceURI===he?"math"===o&&Se[n]:Boolean(Be[o]):e.namespaceURI===fe?!(t.namespaceURI===he&&!Se[n])&&!(t.namespaceURI===pe&&!we[n])&&!Be[o]&&(Ce[o]||!De[o]):!("application/xhtml+xml"!==ke||!xe[e.namespaceURI]))}(e)?(Fe(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!bk(/<\/no(script|embed|frames)/i,e.innerHTML)?(Y&&3===e.nodeType&&(t=e.textContent,rk([A,M,D],(e=>{t=gk(t,e," ")})),e.textContent!==t&&(lk(o.removed,{element:e.cloneNode()}),e.textContent=t)),Ve(E.afterSanitizeElements,e,null),!1):(Fe(e),!0)},Pe=function(e,t,o){if(ne&&("id"===t||"name"===t)&&(o in n||o in Ee))return!1;if(j&&!$[t]&&bk(B,t));else if(G&&bk(F,t));else if(!H[t]||$[t]){if(!(Ue(e)&&(U.tagNameCheck instanceof RegExp&&bk(U.tagNameCheck,e)||U.tagNameCheck instanceof Function&&U.tagNameCheck(e))&&(U.attributeNameCheck instanceof RegExp&&bk(U.attributeNameCheck,t)||U.attributeNameCheck instanceof Function&&U.attributeNameCheck(t))||"is"===t&&U.allowCustomizedBuiltInElements&&(U.tagNameCheck instanceof RegExp&&bk(U.tagNameCheck,o)||U.tagNameCheck instanceof Function&&U.tagNameCheck(o))))return!1}else if(me[t]);else if(bk(z,gk(o,R,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==pk(o,"data:")||!de[e])if(q&&!bk(I,gk(o,R,"")));else if(o)return!1;return!0},Ue=function(e){return"annotation-xml"!==e&&mk(e,N)},We=function(e){Ve(E.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||ze(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:H,forceKeepAttr:void 0};let s=t.length;for(;s--;){const r=t[s],{name:a,namespaceURI:i,value:l}=r,c=_e(a),d=l;let u="value"===a?d:hk(d);if(n.attrName=c,n.attrValue=u,n.keepAttr=!0,n.forceKeepAttr=void 0,Ve(E.uponSanitizeAttribute,e,n),u=n.attrValue,!se||"id"!==c&&"name"!==c||(Ie(a,e),u="user-content-"+u),K&&bk(/((--!?|])>)|<\/(style|title)/i,u)){Ie(a,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){Ie(a,e);continue}if(!X&&bk(/\/>/i,u)){Ie(a,e);continue}Y&&rk([A,M,D],(e=>{u=gk(u,e," ")}));const m=_e(e.nodeName);if(Pe(m,c,u)){if(w&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(i);else switch(p.getAttributeType(m,c)){case"TrustedHTML":u=w.createHTML(u);break;case"TrustedScriptURL":u=w.createScriptURL(u)}if(u!==d)try{i?e.setAttributeNS(i,a,u):e.setAttribute(a,u),ze(e)?Fe(e):ik(o.removed)}catch(t){Ie(a,e)}}else Ie(a,e)}Ve(E.afterSanitizeAttributes,e,null)},$e=function e(t){let o=null;const n=Ne(t);for(Ve(E.beforeSanitizeShadowDOM,t,null);o=n.nextNode();)Ve(E.uponSanitizeShadowNode,o,null),He(o),We(o),o.content instanceof a&&e(o.content);Ve(E.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,i=null,c=null;if(ve=!e,ve&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Le(e)){if("function"!=typeof e.toString)throw vk("toString is not a function");if("string"!=typeof(e=e.toString()))throw vk("dirty is not a string, aborting")}if(!o.isSupported)return e;if(Q||Me(t),o.removed=[],"string"==typeof e&&(ae=!1),ae){if(e.nodeName){const t=_e(e.nodeName);if(!L[t]||W[t])throw vk("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)n=Re("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),1===r.nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?n=r:n.appendChild(r);else{if(!ee&&!Y&&!J&&-1===e.indexOf("<"))return w&&oe?w.createHTML(e):e;if(n=Re(e),!n)return ee?null:oe?S:""}n&&Z&&Fe(n.firstChild);const d=Ne(ae?e:n);for(;i=d.nextNode();)He(i),We(i),i.content instanceof a&&$e(i.content);if(ae)return e;if(ee){if(te)for(c=O.call(n.ownerDocument);n.firstChild;)c.appendChild(n.firstChild);else c=n;return(H.shadowroot||H.shadowrootmode)&&(c=T.call(s,c,!0)),c}let u=J?n.outerHTML:n.innerHTML;return J&&L["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&bk($k,n.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+u),Y&&rk([A,M,D],(e=>{u=gk(u,e," ")})),w&&oe?w.createHTML(u):u},o.setConfig=function(){Me(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Q=!0},o.clearConfig=function(){Te=null,Q=!1},o.isValidAttribute=function(e,t,o){Te||Me({});const n=_e(e),s=_e(t);return Pe(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&lk(E[e],t)},o.removeHook=function(e,t){if(void 0!==t){const o=ak(E[e],t);return-1===o?void 0:ck(E[e],o,1)[0]}return ik(E[e])},o.removeHooks=function(e){E[e]=[]},o.removeAllHooks=function(){E={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}();const Yk=e=>Xk().sanitize(e);var Kk=tinymce.util.Tools.resolve("tinymce.util.I18n");const Jk={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-disc":!0,"list-bull-default":!0,"list-bull-square":!0},Qk="temporary-placeholder",Zk=e=>()=>fe(e,Qk).getOr("!not found!"),eO=(e,t)=>{const o=e.toLowerCase();if(Kk.isRtl()){const e=((e,t)=>Ze(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return be(t,e)?e:o}return o},tO=(e,t)=>fe(t,eO(e,t)),oO=(e,t)=>{const o=t();return tO(e,o).getOrThunk(Zk(o))},nO=()=>ud("add-focusable",[Jc((e=>{qn(e.element,"svg").each((e=>qo(e,"focusable","false")))}))]),sO=(e,t,o,n)=>{var s,r,a;const i=(e=>!!Kk.isRtl()&&be(Jk,e))(t)?["tox-icon--flip"]:[],l=fe(o,eO(t,o)).or(n).getOrThunk(Zk(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(i),innerHtml:l},behaviours:rd([...null!==(r=e.behaviours)&&void 0!==r?r:[],nO()]),eventOrder:null!==(a=e.eventOrder)&&void 0!==a?a:{}}},rO=(e,t,o,n=A.none())=>sO(t,e,o(),n),aO={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},iO=ih({name:"Notification",factory:e=>{const t=Ne("notification-text"),o=pv({dom:gv(`<p id=${t}>${Yk(e.backstageProvider.translate(e.text))}</p>`),behaviours:rd([ng.config({})])}),n=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),s=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),r=pv({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(0)]},s(0)],behaviours:rd([ng.config({})])}),a={updateProgress:(e,t)=>{e.getSystem().isConnected()&&r.getOpt(e).each((e=>{ng.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(t)]},s(t)])}))},updateText:(e,t)=>{if(e.getSystem().isConnected()){const n=o.get(e);ng.set(n,[Ag(t)])}}},i=j([e.icon.toArray(),[e.level],A.from(aO[e.level]).toArray()]),l=pv(Tv.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":e.backstageProvider.translate("Close")}},components:[rO("close",{tag:"span",classes:["tox-icon"]},e.iconProvider)],buttonBehaviours:rd([Gb.config({}),uv.config({...e.backstageProvider.tooltips.getConfig({tooltipText:e.backstageProvider.translate("Close")})})]),action:t=>{e.onAction(t)}})),c=((e,t,o)=>{const n=o(),s=$(e,(e=>be(n,eO(e,n))));return sO({tag:"div",classes:["tox-notification__icon"]},s.getOr(Qk),n,A.none())})(i,0,e.iconProvider),d=[c,{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:rd([ng.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert","aria-labelledby":t},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},behaviours:rd([Gb.config({}),fd.config({}),Om.config({mode:"special",onEscape:t=>(e.onAction(t),A.some(!0))})]),components:d.concat(e.progress?[r.asSpec()]:[]).concat([l.asSpec()]),apis:a}},configFields:[oa("level","info",["success","error","warning","warn","info"]),Rr("progress"),$r("icon"),Rr("onAction"),Rr("text"),Rr("iconProvider"),Rr("backstageProvider")],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var lO=(e,t,o,n)=>{const s=t.backstage.shared,r=()=>ct(""===e.queryCommandValue("ToggleView")?e.getContentAreaContainer():e.getContainer()),a=()=>{const e=Rs(r());return A.some(e)},i=e=>{a().each((t=>{V(e,(e=>{yn(e.element,"width"),En(e.element)>t.width&&mn(e.element,"width",t.width+"px")}))}))};return{open:(t,l,c)=>{const d=()=>{n.on((t=>{l();const o=c();(e=>{ng.remove(e,u),m()})(t),((t,o)=>{0===vo(t.element).length?((t,o)=>{Xx.hide(t),n.clear(),o&&e.focus()})(t,o):((e,t)=>{t&&Om.focusIn(e)})(t,o)})(t,o)}))},u=Fg(iO.sketch({text:t.text,level:I(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,onAction:d,iconProvider:s.providers.icons,backstageProvider:s.providers}));if(n.isSet()){const e=Ig(u);n.on((t=>{ng.append(t,e),Xx.reposition(t),u.hasConfigured(Bp)&&Bp.refresh(t),i(t.components())}))}else{const t=Fg(Xx.sketch({dom:{tag:"div",classes:["tox-notifications-container"],attributes:{"aria-label":"Notifications",role:"region"}},lazySink:s.getSink,fireDismissalEventInstead:{},...s.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}},inlineBehaviours:rd([Om.config({mode:"cyclic",selector:".tox-notification, .tox-notification a, .tox-notification button"}),ng.config({}),...VC(e)&&s.header.isPositionedAtTop()?[]:[Bp.config({contextual:{lazyContext:()=>A.some(Rs(r())),fadeInClass:"tox-notification-container-dock-fadein",fadeOutClass:"tox-notification-container-dock-fadeout",transitionClass:"tox-notification-container-dock-transition"},modes:["top"],lazyViewport:t=>qC(e,t.element).map((e=>({bounds:XC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Fn(e.element).top})}))).getOrThunk((()=>({bounds:Ls(),optScrollEnv:A.none()})))})]])})),i=Ig(u),l={maxHeightFunction:kc()},c={...s.anchors.banner(),overrides:l};n.set(t),o.add(t),Xx.showWithinBounds(t,i,{anchor:c},a)}h(t.timeout)&&t.timeout>0&&IS.setEditorTimeout(e,(()=>{d()}),t.timeout);const m=()=>{n.on((e=>{Xx.reposition(e),e.hasConfigured(Bp)&&Bp.refresh(e),i(e.components())}))};return{close:d,reposition:m,text:e=>{iO.updateText(u,e)},settings:t,getEl:()=>u.element.dom,progressBar:{value:e=>{iO.updateProgress(u,e)}}}},close:e=>{e.close()},getArgs:e=>e.settings}};var cO;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(cO||(cO={}));var dO=cO;const uO="tox-menu-nav__js",mO="tox-collection__item",gO="tox-swatch",pO={normal:uO,color:gO},hO="tox-collection__item--enabled",fO="tox-collection__item-icon",bO="tox-collection__item-label",vO="tox-collection__item-caret",xO="tox-collection__item--active",yO="tox-collection__item-container",wO="tox-collection__item-container--row",SO=e=>fe(pO,e).getOr(uO),CO=e=>"color"===e?"tox-swatches":"tox-menu",kO=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:CO(e),tieredMenu:"tox-tiered-menu"}),OO=e=>{const t=kO(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:SO(e)}},_O=(e,t,o)=>{const n=kO(o);return{tag:"div",classes:j([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},TO=[Zv.parts.items({})],EO=(e,t,o)=>{const n=kO(o);return{dom:{tag:"div",classes:j([[n.tieredMenu]])},markers:OO(o)}},AO=Ne("refetch-trigger-event"),MO=Ne("redirect-menu-item-interaction"),DO="tox-menu__searcher",BO=e=>Xn(e.element,`.${DO}`).bind((t=>e.getSystem().getByDom(t).toOptional())),FO=BO,IO=e=>({fetchPattern:Zh.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),RO=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Rc(e,MO,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[mO]},components:[Zx.sketch({inputClasses:[DO,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:rd([ud(n,[Wc(Ca(),(e=>{Ic(e,AO)})),Wc(wa(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Om.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Om.name()]}})]}},NO="tox-collection--results__js",zO=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:Ne("aria-item-search-result-id"),"aria-selected":"false"}}}:e},LO="Use arrow keys to navigate.",VO=(e,t)=>o=>{const n=z(o,t);return L(n,(t=>({dom:e,components:t})))},HO=(e,t)=>{const o=[];let n=[];return V(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(be(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),L(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},PO=(e,t,o)=>Zv.parts.items({preprocess:n=>{const s=L(n,o);return"auto"!==e&&e>1?VO({tag:"div",classes:["tox-collection__group"]},e)(s):HO(s,((e,o)=>"separator"===t[o].type))}}),UO=(e,t,o=!0)=>{return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{...(n=t,R(n,(e=>"widget"===e.type))?{"aria-label":Kk.translate(LO)}:{})}},components:[PO(e,t,w)]};var n},WO=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),$O=e=>(console.error(Ar(e)),console.log(e),A.none()),GO=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Zv.parts.items({preprocess:e=>HO(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},jO=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"],attributes:{"aria-label":Kk.translate(LO)}},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Zv.parts.items({preprocess:"auto"!==e?VO({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("imageselector"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-image-selector-menu"]},components:[{dom:{tag:"div",classes:["tox-image-selector"]},components:[Zv.parts.items({preprocess:"auto"!==e?VO({tag:"div",classes:["tox-image-selector__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=UO(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?UO(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=Ne("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[RO({i18n:Kk.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],NO],attributes:{id:n}},components:[PO(e,t,zO)]}]}})(n,o,s.searchMode):((e,t)=>{const o=Ne("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",NO].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:o}},components:[PO(e,t,zO)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Zv.parts.items({preprocess:VO({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:_O(t,n,s.menuType),components:TO,items:o}},qO=Lr("type"),XO=Lr("name"),YO=Lr("label"),KO=Lr("text"),JO=Lr("title"),QO=Lr("icon"),ZO=Lr("url"),e_=Lr("value"),t_=Hr("fetch"),o_=Hr("getSubmenuItems"),n_=Hr("onAction"),s_=Hr("onItemAction"),r_=sa("onSetup",(()=>b)),a_=qr("name"),i_=qr("text"),l_=qr("role"),c_=qr("icon"),d_=qr("tooltip"),u_=qr("chevronTooltip"),m_=qr("label"),g_=qr("shortcut"),p_=Yr("select"),h_=na("active",!1),f_=na("borderless",!1),b_=na("enabled",!0),v_=na("primary",!1),x_=e=>Qr("columns",e),y_=Qr("meta",{}),w_=sa("onAction",b),S_=e=>ta("type",e),C_=e=>Br("name","name",ir((()=>Ne(`${e}-name`))),or),k_=[qO,KO,Vr("level",["info","warn","error","success"]),QO,Qr("url","")],O_=vr(k_),__=[qO,KO,b_,C_("button"),c_,f_,Xr("buttonType",["primary","secondary","toolbar"]),v_,ta("context","mode:design")],T_=vr(__),E_=[qO,XO],A_=E_.concat([m_]),M_=E_.concat([YO,b_,ta("context","mode:design")]),D_=vr(M_),B_=nr,F_=A_.concat([x_("auto"),ta("context","mode:design")]),I_=vr(F_),R_=Sr([e_,KO,QO]),N_=A_.concat([ta("storageKey","default"),ta("context","mode:design")]),z_=vr(N_),L_=or,V_=vr(A_),H_=or,P_=E_.concat([ta("tag","textarea"),Lr("scriptId"),Lr("scriptUrl"),Yr("onFocus"),Zr("settings",void 0,ar)]),U_=E_.concat([ta("tag","textarea"),Hr("init")]),W_=kr((e=>_r("customeditor.old",br(U_),e).orThunk((()=>_r("customeditor.new",br(P_),e))))),$_=or,G_=[b_,i_,l_,g_,Br("value","value",ir((()=>Ne("menuitem-value"))),Zs()),y_,ta("context","mode:design")];const j_=vr([qO,XO].concat(G_)),q_=nr,X_=[C_("button"),c_,oa("align","end",["start","end"]),v_,b_,Xr("buttonType",["primary","secondary"]),ta("context","mode:design")],Y_=[...X_,KO],K_=[Vr("type",["submit","cancel","custom"]),...Y_],J_=[Vr("type",["menu"]),i_,d_,c_,Wr("items",j_),...X_],Q_=[...X_,Vr("type",["togglebutton"]),d_,c_,i_,na("active",!1)],Z_=Mr("type",{submit:K_,cancel:K_,custom:K_,menu:J_,togglebutton:Q_}),eT=A_.concat([ta("context","mode:design")]),tT=vr(eT),oT=xr(Qs),nT=e=>[qO,zr("columns"),e],sT=[qO,Lr("html"),oa("presets","presentation",["presentation","document"]),sa("onInit",b),na("stretched",!1)],rT=vr(sT),aT=A_.concat([na("border",!1),na("sandboxed",!0),na("streamContent",!1),na("transparent",!0)]),iT=vr(aT),lT=or,cT=vr(E_.concat([qr("height")])),dT=vr([Lr("url"),jr("zoom"),jr("cachedWidth"),jr("cachedHeight")]),uT=A_.concat([qr("inputMode"),qr("placeholder"),na("maximized",!1),b_,ta("context","mode:design")]),mT=vr(uT),gT=or,pT=e=>[qO,YO,e,oa("align","start",["start","center","end"]),qr("for")],hT=[KO,e_],fT=[KO,Wr("items",Dr(0,(()=>bT)))],bT=yr([vr(hT),vr(fT)]),vT=A_.concat([Wr("items",bT),b_,ta("context","mode:design")]),xT=vr(vT),yT=or,wT=A_.concat([Ur("items",[KO,e_]),ea("size",1),b_,ta("context","mode:design")]),ST=vr(wT),CT=or,kT=A_.concat([na("constrain",!0),b_,ta("context","mode:design")]),OT=vr(kT),_T=vr([Lr("width"),Lr("height")]),TT=E_.concat([YO,ea("min",0),ea("max",0)]),ET=vr(TT),AT=tr,MT=[qO,Wr("header",or),Wr("cells",xr(or))],DT=vr(MT),BT=A_.concat([qr("placeholder"),na("maximized",!1),b_,ta("context","mode:design"),Gr("spellcheck",nr)]);const FT=vr(BT),IT=or,RT=[ta("buttonType","default"),qr("text"),qr("tooltip"),qr("icon"),Zr("search",!1,yr([nr,vr([qr("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),Hr("fetch"),sa("onSetup",(()=>b)),ta("context","mode:design")],NT=vr([qO,...RT]),zT=e=>_r("menubutton",NT,e),LT=[Vr("type",["directory","leaf"]),JO,Lr("id"),Gr("menu",NT),qr("customStateIcon"),qr("customStateIconTooltip")],VT=vr(LT),HT=LT.concat([Wr("children",Dr(0,(()=>Cr("type",{directory:PT,leaf:VT}))))]),PT=vr(HT),UT=Cr("type",{directory:PT,leaf:VT}),WT=[qO,Wr("items",UT),Yr("onLeafAction"),Yr("onToggleExpand"),ra("defaultExpandedIds",[],or),qr("defaultSelectedId")],$T=vr(WT),GT=A_.concat([oa("filetype","file",["image","media","file"]),b_,qr("picker_text"),ta("context","mode:design")]),jT=vr(GT),qT=vr([e_,y_]),XT=e=>Br("items","items",{tag:"required",process:{}},xr(kr((t=>_r(`Checking item of ${e}`,YT,t).fold((e=>Ae.error(Ar(e))),(e=>Ae.value(e))))))),YT=hr((()=>{return Cr("type",{alertbanner:O_,bar:vr((e=XT("bar"),[qO,e])),button:T_,checkbox:D_,colorinput:z_,colorpicker:V_,dropzone:tT,grid:vr(nT(XT("grid"))),iframe:iT,input:mT,listbox:xT,selectbox:ST,sizeinput:OT,slider:ET,textarea:FT,urlinput:jT,customeditor:W_,htmlpanel:rT,imagepreview:cT,collection:I_,label:vr(pT(XT("label"))),table:DT,tree:$T,panel:JT});var e})),KT=[qO,Qr("classes",[]),Wr("items",YT)],JT=vr(KT),QT=[C_("tab"),JO,Wr("items",YT)],ZT=[qO,Ur("tabs",QT)],eE=vr(ZT),tE=Y_,oE=Z_,nE=vr([Lr("title"),Nr("body",Cr("type",{panel:JT,tabpanel:eE})),ta("size","normal"),ra("buttons",[],oE),Qr("initialData",{}),sa("onAction",b),sa("onChange",b),sa("onSubmit",b),sa("onClose",b),sa("onCancel",b),sa("onTabChange",b)]),sE=vr([Vr("type",["cancel","custom"]),...tE]),rE=vr([Lr("title"),Lr("url"),jr("height"),jr("width"),Kr("buttons",sE),sa("onAction",b),sa("onCancel",b),sa("onClose",b),sa("onMessage",b)]),aE=e=>a(e)?[e].concat(q(he(e),aE)):l(e)?q(e,aE):[],iE=e=>r(e.type)&&r(e.name),lE={checkbox:B_,colorinput:L_,colorpicker:H_,dropzone:oT,input:gT,iframe:lT,imagepreview:dT,selectbox:CT,sizeinput:_T,slider:AT,listbox:yT,size:_T,textarea:IT,urlinput:qT,customeditor:$_,collection:R_,togglemenuitem:q_},cE=e=>{const t=(e=>P(aE(e),iE))(e),o=q(t,(e=>(e=>A.from(lE[e.type]))(e).fold((()=>[]),(t=>[Nr(e.name,t)]))));return vr(o)},dE=e=>{var t;return{internalDialog:Tr(_r("dialog",nE,e)),dataValidator:cE(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},uE={open:(e,t)=>{const o=dE(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Tr(_r("dialog",rE,t))),redial:e=>dE(e)},mE=vr([qO,i_]),gE=vr([S_("autocompleteitem"),h_,b_,y_,e_,i_,c_]);vr([qO,Lr("trigger"),ea("minChars",1),x_(1),ea("maxResults",10),Yr("matches"),t_,n_,ra("highlightOn",[],or)]);const pE=[b_,d_,c_,i_,r_,ta("context","mode:design")],hE=vr([qO,n_,g_].concat(pE)),fE=e=>_r("toolbarbutton",hE,e),bE=[h_].concat(pE),vE=vr(bE.concat([qO,n_,g_])),xE=e=>_r("ToggleButton",vE,e),yE=[sa("predicate",T),oa("scope","node",["node","editor"]),oa("position","selection",["node","selection","line"])],wE=pE.concat([S_("contextformbutton"),ta("align","end"),v_,n_,Fr("original",w)]),SE=bE.concat([S_("contextformbutton"),ta("align","end"),v_,n_,Fr("original",w)]),CE=pE.concat([S_("contextformbutton")]),kE=bE.concat([S_("contextformtogglebutton")]),OE=[m_,Wr("commands",Mr("type",{contextformbutton:wE,contextformtogglebutton:SE})),Gr("launch",Mr("type",{contextformbutton:CE,contextformtogglebutton:kE})),sa("onInput",b),sa("onSetup",b)],_E=[...yE,...OE,Vr("type",["contextform"]),sa("initValue",y("")),qr("placeholder")],TE=[...yE,...OE,Vr("type",["contextsliderform"]),sa("initValue",y(0)),sa("min",y(0)),sa("max",y(100))],EE=[...yE,...OE,Vr("type",["contextsizeinputform"]),sa("initValue",y({width:"",height:""}))],AE=Mr("type",{contextform:_E,contextsliderform:TE,contextsizeinputform:EE}),ME=pE.concat([S_("contexttoolbarbutton")]),DE=vr([S_("contexttoolbar"),Jr("launch",ME),Nr("items",yr([or,Sr([qr("name"),qr("label"),Wr("items",or)])]))].concat(yE)),BE=e=>({name:e.name.getOrUndefined(),label:e.label.getOrUndefined(),items:e.items}),FE=[qO,Lr("src"),qr("alt"),ra("classes",[],or)],IE=vr(FE),RE=[qO,KO,a_,ra("classes",["tox-collection__item-label"],or)],NE=vr(RE),zE=hr((()=>Cr("type",{cardimage:IE,cardtext:NE,cardcontainer:LE}))),LE=vr([qO,ta("direction","horizontal"),ta("align","left"),ta("valign","middle"),Wr("items",zE)]),VE=vr([qO,m_,Wr("items",zE),r_,w_].concat(G_)),HE=vr([qO,h_,c_,m_].concat(G_)),PE=[qO,Lr("fancytype"),w_],UE=[Qr("initData",{})].concat(PE),WE=[Yr("select"),aa("initData",{},[na("allowCustomColors",!0),ta("storageKey","default"),Kr("colors",Zs())])].concat(PE),$E=[Yr("select"),Pr("initData",[zr("columns"),ra("items",[],Zs())])].concat(PE),GE=Mr("fancytype",{inserttable:UE,colorswatch:WE,imageselect:$E}),jE=vr([qO,h_,ZO,m_,d_].concat(G_)),qE=vr([qO,h_,QO,YO,d_,e_].concat(G_)),XE=vr([qO,r_,w_,c_].concat(G_)),YE=vr([qO,o_,r_,c_].concat(G_)),KE=vr([qO,c_,h_,r_,n_].concat(G_)),JE=vr([c_,d_,sa("onShow",b),sa("onHide",b),r_]),QE=vr([qO,Nr("items",yr([Sr([XO,Wr("items",or)]),or]))].concat(pE)),ZE=vr([qO,d_,u_,c_,i_,p_,t_,r_,oa("presets","normal",["normal","color","listpreview"]),x_(1),n_,s_,ta("context","mode:design")]),eA=[i_,c_,qr("tooltip"),oa("buttonType","secondary",["primary","secondary"]),na("borderless",!1),Hr("onAction"),ta("context","mode:design")],tA={button:[...eA,KO,Vr("type",["button"])],togglebutton:[...eA,na("active",!1),Vr("type",["togglebutton"])]},oA=[Vr("type",["group"]),ra("buttons",[],Mr("type",tA))],nA=Mr("type",{...tA,group:oA}),sA=vr([ra("buttons",[],nA),Hr("onShow"),Hr("onHide")]),rA=(e,t,o)=>{const n=ms(e.element,"."+o);if(n.length>0){const e=G(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},aA=e=>((e,t)=>rd([ud(e,t)]))(Ne("unnamed-events"),e),iA=e=>rp.config({disabled:e,disableClass:"tox-collection__item--state-disabled"}),lA=e=>rp.config({disabled:e}),cA=e=>rp.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),dA=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},uA=(e,t)=>Jc((o=>{p(e.onBeforeSetup)&&e.onBeforeSetup(o),dA(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),mA=(e,t)=>Qc((o=>dA(e,o)(t.get()))),gA=(e,t,o)=>Qc((n=>(o.set(Zh.getValue(n)),dA(e,n)(t.get())))),pA="silver.uistate",hA="setDisabled",fA="init",bA=["switchmode",fA],vA=(e,t)=>{const o=e.mainUi.outerContainer,n=[e.mainUi.mothership,...e.uiMotherships];t===hA&&V(n,(e=>{e.broadcastOn([Td()],{target:o.element})})),V(n,(e=>{e.broadcastOn([pA],t)}))},xA=(e,t)=>{e.on("init SwitchMode",(e=>{vA(t,e.type)})),e.on("DisabledStateChange",(o=>{if(!o.isDefaultPrevented()){const n=o.state?hA:fA;vA(t,n),o.state||e.nodeChanged()}})),e.on("NodeChange",(o=>{const n=e.ui.isEnabled()?o.type:hA;vA(t,n)})),PS(e)&&e.mode.set("readonly")},yA=e=>ab.config({channels:{[pA]:{onReceive:(t,o)=>{if(o===hA||"setEnabled"===o)return void rp.set(t,o===hA);const{contextType:n,shouldDisable:s}=e();("mode"!==n||I(bA,o))&&rp.set(t,s)}}}}),wA=(e,t)=>ed(((o,n)=>{dA(e,o)(e.onAction),e.triggersSubmenu||t!==dO.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Ic(o,Ha()),n.stop())})),SA={[Na()]:["disabling","alloy.base.behaviour","toggling","item-events"]},CA=Pe,kA=(e,t,o,n)=>{const s=ye(b);return{type:"item",dom:t.dom,components:CA(t.optComponents),data:e.data,eventOrder:SA,hasSubmenu:e.triggersSubmenu,itemBehaviours:rd([ud("item-events",[wA(e,o),uA(e,s),mA(e,s)]),iA((()=>!e.enabled||n.checkUiComponentContext(e.context).shouldDisable)),yA((()=>n.checkUiComponentContext(e.context))),ng.config({})].concat(e.itemBehaviours))}},OA=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),_A=(e,t)=>{var o,n;const s=it("div");return os(s,"tox-image-selector-loading-spinner"),{dom:{tag:e.tag,attributes:null!==(o=e.attributes)&&void 0!==o?o:{},classes:e.classes},components:[{dom:{tag:"div",classes:["tox-image-selector-image-wrapper"]},components:[{dom:{tag:"img",attributes:{src:t},classes:["tox-image-selector-image-img"]}}]},...e.checkMark.toArray()],behaviours:rd([...null!==(n=e.behaviours)&&void 0!==n?n:[],ud("render-image-events",[Jc((e=>{var t;t=e.element,os(t,"tox-image-selector-loading-spinner-wrapper"),$o(t,s),Xn(e.element,"img").each((t=>{sn(t).catch((e=>{console.error(e)})).finally((()=>{(e=>{ss(e,"tox-image-selector-loading-spinner-wrapper"),en(s)})(e.element)}))}))}))])])}},TA=e=>{const t=zS.os.isMacOS()||zS.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=L(n,(e=>{const t=e.toLowerCase().trim();return be(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},EA=(e,t,o=[fO])=>rO(e,{tag:"div",classes:o},t),AA=e=>({dom:{tag:"div",classes:[bO]},components:[Ag(Kk.translate(e))]}),MA=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),DA=(e,t)=>({dom:{tag:"div",classes:[bO]},components:[{dom:{tag:e.tag,styles:e.styles},components:[Ag(Kk.translate(t))]}]}),BA=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[Ag(TA(e))]}),FA=e=>EA("checkmark",e,["tox-collection__item-checkmark"]),IA=(e,t)=>{const o=e.map((e=>({attributes:{id:Ne("menu-item"),"aria-label":Kk.translate(e)}}))).getOr({});return{tag:"div",classes:[uO,mO].concat(t),...o}},RA=e=>({dom:{tag:"label"},components:[Ag(e)]}),NA=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return tO(e,n).or(o).getOrThunk(Zk(n))})(e,t.icons,o))),r=e.ariaLabel.map((e=>({"aria-label":t.translate(e),"data-mce-name":e}))).getOr({});return{dom:(()=>{const e=gO,t=s.getOr(""),o={tag:"div",attributes:r,classes:[e]};return"custom"===n?{...o,tag:"button",classes:[...o.classes,"tox-swatches__picker-btn"],innerHtml:t}:"remove"===n?{...o,classes:[...o.classes,"tox-swatch--remove"],innerHtml:t}:g(n)?{...o,attributes:{...o.attributes,"data-mce-color":n},styles:{"background-color":n},innerHtml:t}:o})(),optComponents:[]}})(e,t,n):"img"===e.presets?(e=>{var t,o;return{dom:IA(e.ariaLabel,["tox-collection__item-image-selector"]),optComponents:[A.some((t=e.iconContent.getOrDie(),o={tag:"div",classes:["tox-collection__item-image"],checkMark:e.checkMark},_A(o,t))),e.labelContent.map(RA)]}})(e):((e,t,o,n)=>{const s={tag:"div",classes:[fO]},r=o?e.iconContent.map((e=>rO(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>AA),(e=>be(e,"style")?C(DA,e.style):AA)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(MA(e,[bO]))));return{dom:IA(e.ariaLabel,[]),optComponents:[r,l,e.shortcutContent.map(BA),a,e.caret,e.labelContent.map(RA)]}})(e,t,o,n),zA=(e,t,o)=>fe(e,"tooltipWorker").map((e=>[uv.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:kc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{uv.setComponents(t,[Mg({element:ct(e)})])}))}})])).getOrThunk((()=>o.map((e=>[uv.config({...t.providers.tooltips.getConfig({tooltipText:e}),mode:"follow-highlight"})])).getOr([]))),LA=(e,t)=>{const o=(e=>RS.DOM.encode(e))(Kk.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},VA=(e,t)=>L(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":wO,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[yO,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,VA(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>I(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return MA(LA(e.text,n),e.classes)}})),HA=(e,t,o,n,s,r,a,i=!0)=>{const l=NA({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),labelContent:e.label,ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(FA(a.icons)):A.none(),caret:A.none(),value:e.value},a,i),c=e.text.filter(y(!t)).map((e=>uv.config(a.tooltips.getConfig({tooltipText:a.translate(e)}))));return Le(kA({context:e.context,data:OA(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Zb.set(e,t)},isActive:()=>Zb.isOn(e),isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[...c.toArray()]},l,r,a),{toggling:{toggleClass:hO,toggleOnExecute:!1,selected:e.active,exclusive:!0}})},PA=e=>({value:GA(e)}),UA=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,WA=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,$A=e=>UA.test(e)||WA.test(e),GA=e=>Ke(e,"#").toUpperCase(),jA=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},qA=e=>{const t=jA(e.red)+jA(e.green)+jA(e.blue);return PA(t)},XA=(e,t,o)=>({hue:e,saturation:t,value:o}),YA=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,XA(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,XA(Math.round(t),Math.round(100*o),Math.round(100*n)))},KA=Math.min,JA=Math.max,QA=Math.round,ZA=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,eM=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,tM=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),oM=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},nM=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=JA(0,KA(r,1)),a=JA(0,KA(a,1)),0===r)return t=o=n=QA(255*a),tM(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=QA(255*(t+d)),o=QA(255*(o+d)),n=QA(255*(n+d)),tM(t,o,n,1)},sM=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(UA,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=WA.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return tM(o,n,s,1)},rM=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return tM(s,r,a,i)},aM=e=>{const t=ZA.exec(e);if(null!==t)return A.some(rM(t[1],t[2],t[3],"1"));const o=eM.exec(e);return null!==o?A.some(rM(o[1],o[2],o[3],o[4])):A.none()},iM=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,lM=tM(255,0,0,1),cM=e=>qA(nM(e)),dM=(e,t)=>{e.dispatch("ResizeContent",t)},uM=(e,t)=>{e.dispatch("TextColorChange",t)},mM=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),gM=e=>{e.dispatch("ContextToolbarClose")},pM=(e,t)=>()=>{e(),t()},hM=(e,t=E)=>bM(e,"NodeChange",(o=>{o.setEnabled(e.selection.isEditable()&&t())})),fM=(e,t)=>o=>{const n=hM(e)(o),s=((e,t)=>o=>{const n=qe(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},bM=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},vM=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},xM=(e,t)=>()=>e.execCommand(t);var yM=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const wM={},SM=e=>fe(wM,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=yM.getItem(t);if(m(o)){const e=yM.getItem("tinymce-custom-colors");yM.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=yM.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{((e,t)=>{const o=F(e,t);return-1===o?A.none():A.some(o)})(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),yM.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return wM[e]=n,n})),CM=(e,t)=>{SM(e).add(t)},kM="forecolor",OM="hilitecolor",_M=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:e[o],icon:"checkmark",type:"choiceitem"});return t},TM=e=>t=>t.options.get(e),EM="#000000",AM=(e,t)=>t===kM&&e.options.isSet("color_map_foreground")?TM("color_map_foreground")(e):t===OM&&e.options.isSet("color_map_background")?TM("color_map_background")(e):e.options.isSet("color_map_raw")?TM("color_map_raw")(e):TM("color_map")(e),MM=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(AM(e,t).length))),DM=(e,t)=>{const o=TM("color_cols")(e),n=MM(e,t);return o===MM(e)?n:o},BM=(e,t="default")=>Math.round(t===kM?TM("color_cols_foreground")(e):t===OM?TM("color_cols_background")(e):TM("color_cols")(e)),FM=TM("custom_colors"),IM=TM("color_default_foreground"),RM=TM("color_default_background"),NM=(e,t)=>{const o=ct(e.selection.getStart()),n="hilitecolor"===t?bs(o,(e=>{if(no(e)){const t=hn(e,"background-color");return $e((e=>aM(e).exists((e=>0!==e.alpha)))(t),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):hn(o,"color");return aM(n).map((e=>"#"+qA(e).value))},zM=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},LM=(e,t,o,n)=>{"custom"===o?qM(e)((o=>{o.each((o=>{CM(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),NM(e,t).getOr(EM)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},VM=(e,t,o)=>e.concat((e=>L(SM(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(zM(o))),HM=(e,t,o)=>n=>{n(VM(e,t,o))},PM=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},UM=(e,t)=>{e.setTooltip(t)},WM=(e,t)=>o=>{const n=NM(e,t);return He(n,o.toUpperCase())},$M=(e,t,o)=>{if(ot(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=VM(AM(e,t),t,!1),r=$(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},GM=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:$M(e,o,n.get()),chevronTooltip:"forecolor"===t?"Text color menu":"Background color menu",presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:WM(e,o),columns:BM(e,o),fetch:HM(AM(e,o),o,FM(e)),onAction:t=>{LM(e,o,n.get(),b)},onItemAction:(s,r)=>{LM(e,o,r,(o=>{n.set(o),uM(e,{name:t,color:o})}))},onSetup:s=>{PM(s,t,n.get());const r=n=>{n.name===t&&(PM(s,n.name,n.color),UM(s,$M(e,o,n.color)))};return e.on("TextColorChange",r),pM(hM(e)(s),(()=>{e.off("TextColorChange",r)}))}})},jM=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(UM(n,$M(e,o,s.get())),PM(n,t,s.get()),hM(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:WM(e,o),initData:{storageKey:o},onAction:n=>{LM(e,o,n.value,(o=>{s.set(o),uM(e,{name:t,color:o})}))}}]})},qM=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},XM=(e,t,o,n,s,r,a,i)=>{const l=WO(t),c=YM(t,o,n,"color"!==s?"normal":"color",r,a,i);return jO(e,l,c,n,{menuType:s})},YM=(e,t,o,n,s,r,a)=>Pe(L(e,(i=>{return"choiceitem"===i.type?(l=i,_r("choicemenuitem",HE,l)).fold($O,(i=>A.some(HA(i,1===o,n,t,r(i.value),s,a,WO(e))))):"imageitem"===i.type?(e=>_r("imagemenuitem",jE,e))(i).fold($O,(e=>A.some(((e,t,o,n,s)=>{const r=NA({presets:"img",textContent:A.none(),htmlContent:A.none(),ariaLabel:e.tooltip,iconContent:A.some(e.url),labelContent:e.label,shortcutContent:A.none(),checkMark:A.some(FA(s.icons)),caret:A.none(),value:e.value},s,!0),a=e.tooltip.map((e=>uv.config(s.tooltips.getConfig({tooltipText:s.translate(e)}))));return Le(kA({context:e.context,data:OA(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Zb.set(e,t)},isActive:()=>Zb.isOn(e),isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t)}),onAction:o=>{t(e.value),o.setActive(!0)},onSetup:e=>(e.setActive(o),b),triggersSubmenu:!1,itemBehaviours:[...a.toArray()]},r,n,s),{toggling:{toggleClass:hO,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(e,t,r(e.value),s,a)))):"resetimage"===i.type?(e=>_r("resetimageitem",qE,e))(i).fold($O,(i=>A.some(HA({...i,type:"choiceitem",text:i.tooltip,icon:A.some(i.icon),label:A.some(i.label)},1===o,n,t,r(i.value),s,a,WO(e))))):A.none();var l}))),KM=(e,t)=>{const o=OO(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+{color:"tox-swatches__row",imageselector:"tox-image-selector__row",listpreview:"tox-collection__group",normal:"tox-collection__group"}[t],previousSelector:e=>"color"===t?Xn(e.element,"[aria-checked=true]"):A.none()}},JM=Ne("cell-over"),QM=Ne("cell-execute"),ZM=(e,t,o)=>{const n=o=>Rc(o,QM,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return Fg({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:rd([ud("insert-table-picker-cell",[Wc(va(),fd.focus),Wc(Na(),n),Wc(Oa(),s),Wc(La(),s)]),Zb.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),fd.config({onFocus:o=>Rc(o,JM,{row:e,col:t})})])})},eD=e=>q(e,(e=>L(e,Ig))),tD=(e,t)=>Ag(`${t}x${e}`),oD={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=(e=>{const t=[];for(let o=0;o<10;o++){const n=[];for(let t=0;t<10;t++){const s=e(o+1,t+1);n.push(ZM(o,t,s))}t.push(n)}return t})(o),s=tD(0,0),r=pv({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:rd([ng.config({})])});return{type:"widget",data:{value:Ne("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ey.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:eD(n).concat(r.asSpec()),behaviours:rd([ud("insert-table-picker",[Jc((e=>{ng.set(r.get(e),[s])})),qc(JM,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Zb.set(e[n][s],n<=t&&s<=o)})(n,s,a),ng.set(r.get(e),[tD(s+1,a+1)])})),qc(QM,((t,o,n)=>{const{row:s,col:r}=n.event;Ic(t,Ha()),e.onAction({numRows:s+1,numColumns:r+1})}))]),Om.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>VM(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(zM(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r=XM(Ne("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,dO.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),a={...r,markers:OO(s),movement:KM(n,s),showMenuRole:!1};return{type:"widget",data:{value:Ne("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ey.widget(Zv.sketch(a))]}},imageselect:(e,t)=>{const o="imageselector",n=e.initData.columns,s=XM(Ne("menu-value"),e.initData.items,(t=>{e.onAction({value:t})}),n,o,dO.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),r={...s,markers:OO(o),movement:KM(n,o),showMenuRole:!1};return{type:"widget",data:{value:Ne("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem","tox-collection--toolbar"]},autofocus:!0,components:[ey.widget(Zv.sketch(r))]}}},nD=e=>({type:"separator",dom:{tag:"div",classes:[mO,"tox-collection__group-heading"]},components:e.text.map(Ag).toArray()}),sD=(e,t,o)=>{FO(e).each((e=>{var n;((e,t)=>{Ko(t.element,"id").each((t=>qo(e.element,"aria-activedescendant",t)))})(e,o),(rs((n=t).element,NO)?A.some(n.element):Xn(n.element,"."+NO)).each((t=>{Ko(t,"id").each((t=>qo(e.element,"aria-controls",t)))}))})),qo(o.element,"aria-selected","true")},rD=(e,t,o)=>{qo(o.element,"aria-selected","false")},aD=e=>Gg.getExistingCoupled(e,"sandbox").bind(BO).map(IO).map((e=>e.fetchPattern)).getOr("");var iD;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(iD||(iD={}));const lD=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,_r("menuitem",XE,i)).fold($O,(e=>A.some(((e,t,o,n=!0)=>{const s=NA({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),labelContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return kA({context:e.context,data:OA(e),getApi:e=>({isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>_r("nestedmenuitem",YE,e))(e).fold($O,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,EA("chevron-down",a,[vO])):(e=>EA("chevron-right",e,[vO]))(o.icons);var a;const i=NA({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,labelContent:A.none(),caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return kA({context:e.context,data:OA(e),getApi:e=>({isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t),setIconFill:(t,o)=>{Xn(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{qo(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);qo(e.element,"aria-label",n)}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>_r("togglemenuitem",KE,e))(e).fold($O,(e=>A.some(((e,t,o,n=!0)=>{const s=NA({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),labelContent:A.none(),ariaLabel:e.text,checkMark:A.some(FA(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return Le(kA({context:e.context,data:OA(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Zb.set(e,t)},isActive:()=>Zb.isOn(e),isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:hO,toggleOnExecute:!1,selected:e.active},role:e.role.getOrUndefined()})})(a(e),t,r,n))));case"separator":return(e=>_r("separatormenuitem",mE,e))(e).fold($O,(e=>A.some(nD(e))));case"fancymenuitem":return(e=>_r("fancymenuitem",GE,e))(e).fold($O,(e=>((e,t)=>fe(oD,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},cD=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||WO(e);return Pe(L(e,(e=>{switch(e.type){case"separator":return(n=e,_r("Autocompleter.Separator",mE,n)).fold($O,(e=>A.some(nD(e))));case"cardmenuitem":return(e=>_r("cardmenuitem",VE,e))(e).fold($O,(e=>A.some(((e,t,o,n)=>{const s={dom:IA(e.label,[]),optComponents:[A.some({dom:{tag:"div",classes:[yO,wO]},components:VA(e.items,n)})]};return kA({context:"mode:design",data:OA({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>{rp.set(e,!t),V(ms(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(rp)&&rp.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:zA(e.meta,r,A.none()),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>_r("Autocompleter.Item",gE,e))(e).fold($O,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=NA({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>LA(e,t))):A.none(),ariaLabel:e.text,labelContent:A.none(),iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon),c=e.text.filter((e=>!o&&""!==e));return kA({context:"mode:design",data:OA(e),enabled:e.enabled,getApi:y({}),onAction:t=>s(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:zA(e,a,c)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},dD=(e,t,o,n,s,r)=>{const a=WO(t),i=Pe(L(t,(e=>{const t=e=>lD(e,o,n,(e=>s?!be(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?GO:jO)(e,a,i,1,l)},uD=e=>ix.singleData(e.value,e),mD=e=>vs(ct(e.startContainer),e.startOffset,ct(e.endContainer),e.endOffset),gD=(e,t)=>{const o=Ne("autocompleter"),n=ye(!1),s=ye(!1),r=Xe(),a=Fg(Xx.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:rd([ud("dismissAutocompleter",[Wc(Ya(),(()=>u())),Wc(oi(),((t,o)=>{Ko(o.event.target,"id").each((t=>qo(ct(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),i=()=>Xx.isOpen(a),l=s.get,c=()=>{if(i()){Xx.hide(a),e.dom.remove(o,!1);const t=ct(e.getBody());Ko(t,"aria-owns").filter((e=>e===o)).each((()=>{Qo(t,"aria-owns"),Qo(t,"aria-activedescendant")}))}},d=()=>Xx.getContent(a).bind((e=>ee(e.components(),0))),u=()=>e.execCommand("mceAutocompleterClose"),m=s=>{const i=(o=>{const s=se(o,(e=>A.from(e.columns))).getOr(1);return q(o,(o=>{const a=o.items;return cD(a,o.matchText,((t,s)=>{const a={hide:()=>u(),reload:t=>{c(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};e.execCommand("mceAutocompleterRefreshActiveRange"),r.get().each((e=>{n.set(!0),o.onAction(a,e,t,s),n.set(!1)}))}),s,dO.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);i.length>0?(((t,o)=>{const n=se(t,(e=>A.from(e.columns))).getOr(1);Xx.showMenuAt(a,{anchor:{type:"selection",getSelection:()=>r.get().map(mD),root:ct(e.getBody())}},((e,t,o,n)=>{const s=KM(t,n),r=OO(n);return{data:uD({...e,movement:s,menuBehaviours:aA("auto"!==t?[]:[Jc(((e,t)=>{rA(e,4,r.item).each((({numColumns:t,numRows:o})=>{Om.setGridSize(e,o,t)}))}))])}),menu:{markers:OO(n),fakeFocus:o===iD.ContentFocus}}})(jO("autocompleter-value",!0,o,n,{menuType:"normal"}),n,iD.ContentFocus,"normal")),d().each(Yd.highlightFirst)})(s,i),qo(ct(e.getBody()),"aria-owns",o),e.inline||g()):c()},g=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>tn(e,!0))(a.element);gn(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),Xn(s,'[role="menu"]').each((e=>{yn(e,"position"),yn(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),m(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>m(e))),e.on("AutocompleterUpdateActiveRange",(({range:e})=>r.set(e))),e.on("AutocompleterEnd",(()=>{c(),s.set(!1),n.set(!1),r.clear()}));((e,t)=>{const o=(e,t)=>{Rc(e,wa(),{raw:t})},n=()=>e.getMenu().bind(Yd.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Nc),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Yd.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(()=>{!e.isActive()||e.isProcessingAction()||t.queryCommandState("mceAutoCompleterInRange")||e.cancelIfNecessary()}))})({cancelIfNecessary:u,isMenuOpen:i,isActive:l,isProcessingAction:n.get,getMenu:d},e)};var pD=tinymce.util.Tools.resolve("tinymce.html.Entities");const hD=(e,t,o,n)=>{const s=fD(e,t,o,n);return Ux.sketch(s)},fD=(e,t,o,n)=>({dom:bD(o),components:e.toArray().concat([t]),fieldBehaviours:rd(n)}),bD=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),vD=(e,t)=>Ux.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ag(t.translate(e))]}),xD=Ne("form-component-change"),yD=Ne("form-component-input"),wD=Ne("form-close"),SD=Ne("form-cancel"),CD=Ne("form-action"),kD=Ne("form-submit"),OD=Ne("form-block"),_D=Ne("form-unblock"),TD=Ne("form-tabchange"),ED=Ne("form-resize"),AD=(e,t,o)=>{const n=e.label.map((e=>vD(e,t))),s=t.icons(),r=e=>(t,o)=>{Yn(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,Yo(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.checkUiComponentContext("mode:design").shouldDisable||t.isDisabled()||Rc(o,CD,{name:e.name,value:r})})),i=[Wc(va(),r(((e,t,o)=>{No(o,!0)}))),Wc(Oa(),a),Wc(La(),a),Wc(xa(),r(((e,t,o)=>{Xn(e.element,"."+xO).each((e=>{ss(e,xO)})),os(o,xO)}))),Wc(ya(),r((e=>{Xn(e.element,"."+xO).each((e=>{ss(e,xO),zo(e)}))}))),ed(r(((t,o,n,s)=>{Rc(t,CD,{name:e.name,value:s})})))],l=(e,t)=>L(ms(e.element,".tox-collection__item"),t),c=Ux.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:rd([rp.config({disabled:()=>t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{l(e,(e=>{os(e,"tox-collection__item--state-disabled"),qo(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{ss(e,"tox-collection__item--state-disabled"),Qo(e,"aria-disabled")}))}}),yA((()=>t.checkUiComponentContext(e.context))),ng.config({}),uv.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{Xn(e.element,"."+xO+"[data-mce-tooltip]").each((o=>{Ko(o,"data-mce-tooltip").each((o=>{uv.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-keyboard-focus",anchor:e=>({type:"node",node:Xn(e.element,"."+xO).orThunk((()=>xt(".tox-collection__item"))),root:e.element,layouts:{onLtr:y([jl,Gl,Pl,Wl,Ul,$l]),onRtl:y([jl,Gl,Pl,Wl,Ul,$l])},bubble:wl(0,-2,{})})}),Zh.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=t.checkUiComponentContext("mode:design").shouldDisable||t.isDisabled()?" tox-collection__item--state-disabled":"",a=L(n,(t=>{const o=Kk.translate(t.text),n=1===e.columns?`<div class="tox-collection__item-label">${o}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(t.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=o.replace(/\_| \- |\-/g,(e=>i[e]));return`<div data-mce-tooltip="${l}" class="tox-collection__item${r}" tabindex="-1" data-collection-item-value="${pD.encodeAllRaw(t.value)}" aria-label="${l}">${a}${n}</div>`})),i="auto"!==e.columns&&e.columns>1?z(a,e.columns):[a],l=L(i,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));nn(o.element,l.join(""))})(o,n),"auto"===e.columns&&rA(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Om.setGridSize(o,e,t)})),Ic(o,ED)}}),Gb.config({}),Om.config((d=e.columns,1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${mO}`}})),ud("collection-events",i)]),eventOrder:{[Na()]:["disabling","alloy.base.behaviour","collection-events"],[xa()]:["collection-events","tooltipping"]}});var d;return hD(n,c,["tox-form__group--collection"],[])},MD=Ne("color-input-change"),DD=Ne("color-swatch-change"),BD=Ne("color-picker-cancel"),FD=()=>Pg.config({find:A.some}),ID=e=>Pg.config({find:t=>xo(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),RD=vr([Qr("preprocess",w),Qr("postprocess",w)]),ND=(e,t)=>{const o=Er("RepresentingConfigs.memento processors",RD,t);return Zh.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=Zh.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);Zh.setValue(r,s)}}})},zD=(e,t,o)=>Zh.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),LD=(e,t,o)=>zD(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),VD=e=>Zh.config({store:{mode:"memory",initialValue:e}}),HD=Ne("rgb-hex-update"),PD=Ne("slider-update"),UD=Ne("palette-update"),WD=Ne("valid-input"),$D=Ne("invalid-input"),GD=Ne("validating-input"),jD="colorcustom.rgb.",qD={isEnabled:E,setEnabled:b,immediatelyShow:b,immediatelyHide:b},XD=(e,t,o,n,s,r)=>{const a=(e,t)=>{const o=t.get();e!==o.isEnabled()&&(o.setEnabled(e),e?o.immediatelyShow():o.immediatelyHide())},i=(o,n,s)=>ef.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Rc(e,GD,{type:o})},onValid:e=>{a(!1,s),Rc(e,WD,{type:o,value:Zh.getValue(e)})},onInvalid:e=>{a(!0,s),Rc(e,$D,{type:o,value:Zh.getValue(e)})}},validator:{validate:t=>{const o=Zh.getValue(t),s=n(o)?Ae.value(!0):Ae.error(e("aria.input.invalid"));return _e(s)},validateOnLoad:!1}}),l=(o,n,a,l,c)=>{const d=ye(qD),u=e(jD+"range"),m=Ux.parts.label({dom:{tag:"label"},components:[Ag(a)]}),g=Ux.parts.field({data:c,factory:Zx,inputAttributes:{type:"text","aria-label":l,..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:rd([i(n,o,d),Gb.config({}),uv.config({...s({tooltipText:"",onSetup:e=>{d.set({isEnabled:()=>uv.isEnabled(e),setEnabled:t=>uv.setEnabled(e,t),immediatelyShow:()=>uv.immediateOpenClose(e,!0),immediatelyHide:()=>uv.immediateOpenClose(e,!1)}),uv.setEnabled(e,!1)},onShow:(o,s)=>{uv.setComponents(o,[{dom:{tag:"p",classes:[t("rgb-warning-note")]},components:[Ag(e("hex"===n?"colorcustom.rgb.invalidHex":"colorcustom.rgb.invalid"))]}])}})})]),onSetValue:e=>{ef.isInvalid(e)&&ef.run(e).get(b)}}),p=Ne("aria-invalid"),h=pv(r("invalid",A.some(p),"warning")),f=[m,g,pv({dom:{tag:"div",classes:[t("invalid-icon")]},components:[h.asSpec()]}).asSpec()],v="hex"!==n?[Ux.parts["aria-descriptor"]({text:u})]:[],x=f.concat(v);return{dom:{tag:"div",attributes:{role:"presentation"},classes:[t("rgb-container")]},components:x}},c=(e,t)=>{const o=t.red,n=t.green,s=t.blue;Zh.setValue(e,{red:o,green:n,blue:s})},d=pv({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),u=(e,t)=>{d.getOpt(e).each((e=>{mn(e.element,"background-color","#"+t.value)}))},m=ih({factory:()=>{const s={red:ye(A.some(255)),green:ye(A.some(255)),blue:ye(A.some(255)),hex:ye(A.some("ffffff"))},r=e=>s[e].get(),a=(e,t)=>{s[e].set(t)},i=e=>{const t=e.red,o=e.green,n=e.blue;a("red",A.some(t)),a("green",A.some(o)),a("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?a(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=PA(t);a("hex",A.some(n.value));const s=sM(n);c(e,s),i(s),Rc(e,HD,{hex:n}),u(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);a(t,A.some(n)),r("red").bind((e=>r("green").bind((t=>r("blue").map((o=>tM(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=qA(t);return Ax.getField(e,"hex").each((t=>{fd.isFocused(t)||Zh.setValue(e,{hex:o.value})})),o})(e,t);Rc(e,HD,{hex:o}),u(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(jD+t+".label"),description:e(jD+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return Le(Ax.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",Ux.sketch(l(oM,"red",h.label,h.description,255))),o.field("green",Ux.sketch(l(oM,"green",f.label,f.description,255))),o.field("blue",Ux.sketch(l(oM,"blue",b.label,b.description,255))),o.field("hex",Ux.sketch(l($A,"hex",v.label,v.description,"ffffff"))),d.asSpec()],formBehaviours:rd([ef.config({invalidClass:t("form-invalid")}),ud("rgb-form-events",[Wc(WD,g),Wc($D,m),Wc(GD,m)])])}))),{apis:{updateHex:(e,t)=>{Zh.setValue(e,{hex:t.value}),((e,t)=>{const o=sM(t);c(e,o),i(o)})(e,t),u(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return m},YD=(e,t,o,n)=>{const s=ih({name:"ColourPicker",configFields:[Rr("dom"),Qr("onValidHex",b),Qr("onInvalidHex",b)],factory:s=>{const r=XD(e,t,s.onValidHex,s.onInvalidHex,o,n),a=((e,t)=>{const o=Pw.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=Pw.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)},r=ih({factory:r=>{const a=y({x:0,y:0}),i=rd([Pg.config({find:A.some}),fd.config({})]);return Pw.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||qo(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Rc(t,UD,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,iM(lM))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=XA(t,100,100),r=nM(n);s(o,iM(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=YA(sM(o));Pw.setValue(t,{x:n.saturation,y:100-n.value}),qo(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}});return r})(e,t),i={paletteRgba:ye(lM),paletteHue:ye(0)},l=pv(((e,t)=>{const o=Pw.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=Pw.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return Pw.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:rd([fd.config({})]),onChange:(e,t,o)=>{qo(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Rc(e,PD,{value:o})}})})(0,t)),c=pv(a.sketch({})),d=pv(r.sketch({})),u=(e,t,o)=>{c.getOpt(e).each((e=>{a.setHue(e,o)}))},m=(e,t)=>{d.getOpt(e).each((e=>{r.updateHex(e,t)}))},g=(e,t,o)=>{l.getOpt(e).each((e=>{Pw.setValue(e,(e=>100-e/360*100)(o))}))},p=(e,t)=>{c.getOpt(e).each((e=>{a.setThumb(e,t)}))},f=(e,t,o,n)=>{((e,t)=>{const o=sM(e);i.paletteRgba.set(o),i.paletteHue.set(t)})(t,o),V(n,(n=>{n(e,t,o)}))};return{uid:s.uid,dom:s.dom,components:[c.asSpec(),l.asSpec(),d.asSpec()],behaviours:rd([ud("colour-picker-events",[Wc(HD,(()=>{const e=[u,g,p];return(t,o)=>{const n=o.event.hex,s=(e=>YA(sM(e)))(n);f(t,n,s.hue,e)}})()),Wc(UD,(()=>{const e=[m];return(t,o)=>{const n=o.event.value,s=i.paletteHue.get(),r=XA(s,n.x,100-n.y),a=cM(r);f(t,a,s,e)}})()),Wc(PD,(()=>{const e=[u,m];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=i.paletteRgba.get(),r=YA(s),a=XA(n,r.saturation,r.value),l=cM(a);f(t,l,n,e)}})())]),Pg.config({find:e=>d.getOpt(e)}),Om.config({mode:"acyclic"})])}}});return s},KD={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red channel","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green channel","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue channel","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.rgb.invalid":"Numbers only, 0 to 255","colorcustom.rgb.invalidHex":"Hexadecimal only, 000000 to FFFFFF","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var JD=tinymce.util.Tools.resolve("tinymce.Resource");const QD=e=>be(e,"init");var ZD=tinymce.util.Tools.resolve("tinymce.util.Tools");const eB=Ne("browse.files.event"),tB=(e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{V(e,(e=>{e(t,o)}))},r=pv({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:rd([ud("input-file-events",[Xc(Oa()),Xc(La())])])}),a=e.label.map((e=>vD(e,t))),i=Ux.parts.field({factory:Tv,dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[Ag(t.translate("Browse for an image")),r.asSpec()],action:e=>{r.get(e).element.dom.click()},buttonBehaviours:rd([FD(),VD(o.getOr([])),Gb.config({}),lA((()=>t.checkUiComponentContext(e.context).shouldDisable)),yA((()=>t.checkUiComponentContext(e.context)))])}),l={dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:rd([rp.config({disabled:()=>t.checkUiComponentContext(e.context).shouldDisable}),yA((()=>t.checkUiComponentContext(e.context))),Zb.config({toggleClass:"dragenter",toggleOnExecute:!1}),ud("dropzone-events",[Wc("dragenter",s([n,Zb.toggle])),Wc("dragleave",s([n,Zb.toggle])),Wc("dragover",n),Wc("drop",s([n,(e,t)=>{var o;if(!rp.isDisabled(e)){const n=t.event.raw;Rc(e,eB,{files:null===(o=n.dataTransfer)||void 0===o?void 0:o.files})}}])),Wc(ka(),((e,t)=>{const o=t.event.raw.target;Rc(e,eB,{files:o.files})}))])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[Ag(t.translate("Drop an image here"))]},i]}]};return hD(a,l,["tox-form__group--stretched"],[ud("handle-files",[Wc(eB,((o,n)=>{Ux.getField(o).each((o=>{var s,r;s=o,(r=n.event.files)&&(Zh.setValue(s,((e,t)=>{const o=ZD.explode(t.getOption("images_file_types"));return P(ne(e),(e=>R(o,(t=>Ze(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(r,t)),Rc(s,xD,{name:e.name}))}))}))])])},oB=Ne("alloy-fake-before-tabstop"),nB=Ne("alloy-fake-after-tabstop"),sB=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:rd([fd.config({ignore:!0}),Gb.config({})])}),rB=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[sB([oB]),t,sB([nB])],behaviours:rd([ID(1)])}),aB=(e,t)=>{Rc(e,wa(),{raw:{which:9,shiftKey:t}})},iB=(e,t)=>{const o=t.element;rs(o,oB)?aB(e,!0):rs(o,nB)&&aB(e,!1)},lB=e=>ps(e,["."+oB,"."+nB].join(","),T),cB=Ne("update-dialog"),dB=Ne("update-title"),uB=Ne("update-body"),mB=Ne("update-footer"),gB=Ne("body-send-message"),pB=Ne("dialog-focus-shifted"),hB=Jt().browser,fB=hB.isSafari(),bB=hB.isFirefox(),vB=fB||bB,xB=hB.isChromium(),yB=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,wB=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),SB=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!xB&&!fB||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(yB),r=()=>{const e=n.contentWindow;g(e)&&(s?wB(e,"bottom"):!s&&vB&&0!==o&&wB(e,o))};fB&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),fB||r()}))},CB=$e(vB,fB?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(SB,e))),kB=Ne("toolbar.button.execute"),OB=Ne("common-button-display-events"),_B={[Na()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events","tooltipping"],[qa()]:["toolbar-button-events",OB],[Xa()]:["toolbar-button-events","dropdown-events","tooltipping"],[pa()]:["focusing","alloy.base.behaviour",OB]},TB=e=>mn(e.element,"width",hn(e.element,"width")),EB=(e,t,o)=>rO(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),AB=(e,t)=>EB(e,t,[]),MB=(e,t)=>EB(e,t,[ng.config({})]),DB=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[Ag(o.translate(e))],behaviours:rd([ng.config({})])}),BB=Ne("update-menu-text"),FB=Ne("update-menu-icon"),IB=Ne("update-tooltip-text"),RB=(e,t,o,n)=>{const s=ye(b),r=ye(e.tooltip),a=e.text.map((e=>pv(DB(e,t,o.providers)))),i=e.icon.map((e=>pv(MB(e,o.providers.icons)))),l=(e,t)=>{const o=Zh.getValue(e);return fd.focus(o),Rc(o,"keydown",{raw:t.event.raw}),kx.close(o),A.some(!0)},c=e.role.fold((()=>({})),(e=>({role:e}))),d=A.from(e.listRole).map((e=>({listRole:e}))).getOr({}),u=e.ariaLabel.fold((()=>({})),(e=>({"aria-label":o.providers.translate(e)}))),m=rO("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),p=Ne("common-button-display-events"),h="dropdown-events",f=pv(kx.sketch({...e.uid?{uid:e.uid}:{},...c,...d,dom:{tag:"button",classes:[t,`${t}--select`].concat(L(e.classes,(e=>`${t}--${e}`))),attributes:{...u,...g(n)?{"data-mce-name":n}:{}}},components:CA([i.map((e=>e.asSpec())),a.map((e=>e.asSpec())),A.some(m)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{FO(e).each((e=>fd.focus(e)))})(n)},dropdownBehaviours:rd([...e.dropdownBehaviours,lA((()=>e.disabled||o.providers.checkUiComponentContext(e.context).shouldDisable)),yA((()=>o.providers.checkUiComponentContext(e.context))),mv.config({}),ng.config({}),...e.tooltip.map((t=>uv.config(o.providers.tooltips.getConfig({tooltipText:o.providers.translate(t),onShow:t=>{if(Ue(r.get(),e.tooltip,((e,t)=>t!==e)).getOr(!1)){const e=o.providers.translate(r.get().getOr(""));uv.setComponents(t,o.providers.tooltips.getComponents({tooltipText:e}))}}})))).toArray(),ud(h,[uA(e,s),mA(e,s)]),ud(p,[Jc(((t,o)=>{"listbox"!==e.listRole&&TB(t)}))]),ud("update-dropdown-width-variable",[Wc(ja(),((e,t)=>kx.close(e)))]),ud("menubutton-update-display-text",[Wc(BB,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{ng.set(e,[Ag(o.providers.translate(t.event.text))])}))})),Wc(FB,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{ng.set(e,[MB(t.event.icon,o.providers.icons)])}))})),Wc(IB,((e,t)=>{const n=o.providers.translate(t.event.text);qo(e.element,"aria-label",n),r.set(A.some(t.event.text))}))])]),eventOrder:Le(_B,{[pa()]:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[qa()]:["toolbar-button-events",uv.name(),h,p]}),sandboxBehaviours:rd([Om.config({mode:"special",onLeft:l,onRight:l}),ud("dropdown-sandbox-events",[Wc(AO,((e,t)=>{(e=>{const t=Zh.getValue(e),o=BO(e).map(IO);kx.refetch(t).get((()=>{const e=Gg.getCoupled(t,"sandbox");o.each((t=>BO(e).each((e=>((e,t)=>{Zh.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Wc(MO,((e,t)=>{((e,t)=>{(e=>wb.getState(e).bind(Yd.getHighlighted).bind(Yd.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...EO(0,e.columns,e.presets),fakeFocus:e.searchable,..."listbox"===e.listRole?{}:{onHighlightItem:sD,onCollapseMenu:(e,t,o)=>{Yd.getHighlighted(o).each((t=>{sD(e,o,t)}))},onDehighlightItem:rD}}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{Cc()(e,t-10)}}),fetch:t=>Oe(C(e.fetch,t))}));return f.asSpec()},NB=e=>"separator"===e.type,zB={type:"separator"},LB=(e,t)=>{const o=((e,t)=>{const o=W(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!NB(e[e.length-1])?e.concat([zB]):e:be(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&NB(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{if((e=>be(e,"getSubmenuItems"))(o)){const n=(e=>{const t=fe(e,"value").getOrThunk((()=>Ne("generated-menu-item")));return Le({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=LB(o,t);return{item:e,menus:Le(n.menus,{[e.value]:n.items}),expansions:Le(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:Le(e.menus,s.menus),items:[s.item,...e.items],expansions:Le(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},VB=(e,t,o,n)=>{const s=Ne("primary-menu"),r=LB(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=dD(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=le(r.menus,((e,n)=>dD(n,e,t,o,!1,l))),d=Le(c,ia(s,i));return A.from(ix.tieredData(s,d,r.expansions))},HB=e=>!be(e,"items"),PB="data-value",UB=(e,t,o,n,s)=>L(o,(o=>HB(o)?{type:"togglemenuitem",...s?{}:{role:"option"},text:o.text,value:o.value,active:o.value===n,onAction:()=>{Zh.setValue(e,o.value),Rc(e,xD,{name:t}),fd.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>UB(e,t,o.items,n,s)})),WB=(e,t)=>se(e,(e=>HB(e)?$e(e.value===t,e):WB(e.items,t))),$B=e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit},GB=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return Ae.value({value:e,unit:o})}return Ae.error(e)},jB=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>be(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},qB=e=>A.none(),XB=(e,t)=>{const o=GB(e).toOptional(),n=GB(t).toOptional();return Ue(o,n,((e,t)=>jB(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>jB(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(qB))).getOr(qB)},YB=(e,t)=>{const o=e.label.map((e=>vD(e,t))),n=[rp.config({disabled:()=>e.disabled||t.checkUiComponentContext(e.context).shouldDisable}),yA((()=>t.checkUiComponentContext(e.context))),Om.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Ic(e,kD),A.some(!0))}),ud("textfield-change",[Wc(Ca(),((t,o)=>{Rc(t,xD,{name:e.name})})),Wc(Ia(),((t,o)=>{Rc(t,xD,{name:e.name})}))]),Gb.config({})],s=e.validation.map((e=>ef.config({getRoot:e=>po(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=Zh.getValue(t),n=e.validator(o);return _e(!0===n?Ae.value(o):Ae.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r=e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),a=e.inputMode.fold(y({}),(e=>({inputmode:e}))),i={...e.spellcheck.fold(y({}),(e=>({spellcheck:e}))),...r,...a,"data-mce-name":e.name},l=Ux.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:i,inputClasses:[e.classname],inputBehaviours:rd(j([n,s])),selectOnFocus:!1,factory:Zx}),c=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[l]}:l,d=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),u=[rp.config({disabled:()=>e.disabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(rp.disable)},onEnabled:e=>{Ux.getField(e).each(rp.enable)}}),yA((()=>t.checkUiComponentContext(e.context)))];return hD(o,c,d,u)},KB=e=>({isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t),setActive:t=>{const o=e.element;t?(os(o,"tox-tbtn--enabled"),qo(o,"aria-pressed",!0)):(ss(o,"tox-tbtn--enabled"),Qo(o,"aria-pressed"))},isActive:()=>rs(e.element,"tox-tbtn--enabled"),setTooltip:t=>{Rc(e,IB,{text:t})},setText:t=>{Rc(e,BB,{text:t})},setIcon:t=>Rc(e,FB,{icon:t})}),JB=(e,t,o,n,s=!0,r)=>{const a="bordered"===e.buttonType?["bordered"]:[];return RB({text:e.text,icon:e.icon,tooltip:e.tooltip,ariaLabel:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?aD(t):""};e.fetch((t=>{n(VB(t,dO.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,KB(t))},onSetup:e.onSetup,getApi:e=>KB(e),columns:1,presets:"normal",classes:a,dropdownBehaviours:[...s?[Gb.config({})]:[]],context:e.context},t,o.shared,r)},QB=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{No(t.element),Rc(t,CD,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(L(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,context:e.context,onAction:n(e),onSetup:s(e)}})))}},ZB=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{"aria-label":e}},components:[Ag(e)]}),eF=(e,t,o)=>{e.customStateIcon.each((n=>t.push(nF(n,o.shared.providers.icons,e.customStateIconTooltip.fold((()=>[]),(e=>[uv.config(o.shared.providers.tooltips.getConfig({tooltipText:e}))])),["tox-icon-custom-state"]))))},tF=Ne("leaf-label-event-id"),oF=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>JB(e,"tox-mbtn",r,A.none(),o))),i=[ZB(e.title)];return eF(e,i,r),a.each((e=>i.push(e))),Tv.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[wa()]:[tF,"keying"]},buttonBehaviours:rd([...o?[Gb.config({})]:[],Zb.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),ab.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?Zb.on:Zb.off)(t)}}}}),ud(tF,[Jc(((t,o)=>{s.each((o=>{(o===e.id?Zb.on:Zb.off)(t)}))})),Wc(wa(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(Gn(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{qn(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(fd.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},nF=(e,t,o,n,s)=>rO(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"].concat(n||[]),behaviours:o,attributes:s},t),sF=Ne("directory-label-event-id"),rF=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>JB(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a=n.shared.providers.icons,nF("chevron-right",a,[]))]},ZB(e.title)];var a;eF(e,r,n),s.each((e=>{r.push(e)}));const i=t=>{Gn(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!Zb.isOn(o);Zb.toggle(o),Rc(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Tv.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:i,eventOrder:{[wa()]:[sF,"keying"]},buttonBehaviours:rd([...t?[Gb.config({})]:[],ud(sF,[Wc(wa(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&Gn(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!Zb.isOn(o)&&n||Zb.isOn(o)&&s?(i(e),t.stop()):s&&!Zb.isOn(o)&&(Gn(o.element,".tox-tree--directory").each((e=>{qn(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(fd.focus)}))})),t.stop())}))}))}))])])})},aF=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?oF({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):lF({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:rd([zb.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),ng.config({})])}),iF=Ne("directory-event-id"),lF=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=ye(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[rF({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),aF({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:rd([ud(iF,[Jc(((e,t)=>{Zb.set(e,c)})),Wc("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),Zb.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?oF({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):lF({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?zb.grow(r):zb.shrink(r),ng.set(r,c)}})])}},cF=Ne("tree-event-id"),dF=(e,t,o=[],n,s,r,a)=>{const i=t.fold((()=>({})),(e=>({action:e}))),l={buttonBehaviours:rd([iA((()=>!e.enabled||a.checkUiComponentContext(e.context).shouldDisable)),yA((()=>a.checkUiComponentContext(e.context))),Gb.config({}),...r.map((e=>uv.config(a.tooltips.getConfig({tooltipText:a.translate(e)})))).toArray(),ud("button press",[Uc("click")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...i},c=Le(l,{dom:n});return Le(c,{components:s})},uF=(e,t,o,n=[],s)=>{const r={tag:"button",classes:["tox-tbtn"],attributes:{...e.tooltip.map((e=>({"aria-label":o.translate(e)}))).getOr({}),"data-mce-name":s}},a=e.icon.map((e=>AB(e,o.icons))),i=CA([a]);return dF(e,t,n,r,i,e.tooltip,o)},mF=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},gF=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>AB(e,o.icons))),i=[a.getOrThunk((()=>Ag(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c={tag:"button",classes:[...mF(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s],attributes:{"aria-label":r,"data-mce-name":e.text}},d=e.icon.map(y(r));return dF(e,t,n,c,i,d,o)},pF=(e,t,o,n=[],s=[])=>{const r=gF(e,A.some(t),o,n,s);return Tv.sketch(r)},hF=(e,t)=>o=>{"custom"===t?Rc(o,CD,{name:e,value:{}}):"submit"===t?Ic(o,kD):"cancel"===t?Ic(o,SD):console.error("Unknown button type: ",t)},fF=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,buttonType:"default",type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:QB(n.items,t,o)},r=pv(JB(s,"tox-tbtn",o,A.none(),!0,e.text.or(e.tooltip).getOrUndefined()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=hF(e.name,t),s={...e,context:"cancel"===t?"any":e.context,borderless:!1};return pF(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t,o)=>{var n,s;const r=e.icon.map((e=>MB(e,t.icons))).map(pv),a=e.buttonType.getOr(e.primary?"primary":"secondary"),i={...e,name:null!==(n=e.name)&&void 0!==n?n:"",primary:"primary"===a,tooltip:e.tooltip,enabled:null!==(s=e.enabled)&&void 0!==s&&s,borderless:!1},l=i.tooltip.or(e.text).map((e=>({"aria-label":t.translate(e)}))).getOr({}),c=mF(null!=a?a:"secondary"),d=e.icon.isSome()&&e.text.isSome(),u={tag:"button",classes:[...c.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...d?["tox-button--icon-and-text"]:[]],attributes:{...l,...g(o)?{"data-mce-name":o}:{}}},m=t.translate(e.text.getOr("")),p=Ag(m),h=[...CA([r.map((e=>e.asSpec()))]),...e.text.isSome()?[p]:[]],f=dF(i,A.some((o=>{Rc(o,CD,{name:e.name,value:{setIcon:e=>{r.map((n=>n.getOpt(o).each((o=>{ng.set(o,[MB(e,t.icons)])}))))}}})})),[],u,h,e.tooltip,t);return Tv.sketch(f)})(e,o.shared.providers,e.text.or(e.tooltip).getOrUndefined());throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},bF={type:"separator"},vF=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),xF=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),yF=(e,t)=>(e=>L(e,vF))(((e,t)=>P(t,(t=>t.type===e)))(e,t)),wF=e=>yF("header",e.targets),SF=e=>yF("anchor",e.targets),CF=e=>A.from(e.anchorTop).map((e=>xF("<top>",e))).toArray(),kF=e=>A.from(e.anchorBottom).map((e=>xF("<bottom>",e))).toArray(),OF=(e,t)=>{const o=e.toLowerCase();return P(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Je(n.toLowerCase(),o)||Je(s.toLowerCase(),o)}))},_F=Ne("aria-invalid"),TF=e=>(t,o,n,s,r)=>fe(o,"name").fold((()=>e(o,s,A.none(),r)),(a=>t.field(a,e(o,s,fe(n,a),r)))),EF={bar:TF(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:L(e.items,t.interpreter)}))(e,t.shared))),collection:TF(((e,t,o)=>AD(e,t.shared.providers,o))),alertbanner:TF(((e,t)=>((e,t)=>{const o=oO(e.icon,t.icons);return ch.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[Tv.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Rc(t,CD,{name:"alert-banner",value:e.url}),buttonBehaviours:rd([nO()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:TF(((e,t,o)=>((e,t,o)=>YB({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o,context:e.context,spellcheck:A.none()},t))(e,t.shared.providers,o))),textarea:TF(((e,t,o)=>((e,t,o)=>YB({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o,context:e.context,spellcheck:e.spellcheck},t))(e,t.shared.providers,o))),label:TF(((e,t,o,n)=>((e,t,o)=>{const n="tox-label",s="center"===e.align?[`${n}--center`]:[],r="end"===e.align?[`${n}--end`]:[],a=pv({dom:{tag:"label",classes:[n,...s,...r]},components:[Ag(t.providers.translate(e.label))]}),i=L(e.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[a.asSpec(),...i],behaviours:rd([FD(),ng.config({}),(l=A.none(),LD(l,on,nn)),Om.config({mode:"acyclic"}),ud("label",[Jc((t=>{e.for.each((e=>{o(e).each((e=>{a.getOpt(t).each((t=>{var o;const n=null!==(o=Yo(e.element,"id"))&&void 0!==o?o:Ne("form-field");qo(e.element,"id",n),qo(t.element,"for",n)}))}))}))}))])])};var l})(e,t.shared,n))),iframe:(EI=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=ye(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>qo(o,"srcdoc",n);t?CB.fold(y(SB),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>vD(e,t))),c=Ux.parts.field({factory:{sketch:e=>rB(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:rd([Gb.config({}),fd.config({}),zD(o,i.getValue,i.setValue),ab.config({channels:{[pB]:{onReceive:(e,t)=>{t.newFocus.each((t=>{po(e.element).each((o=>{(yt(e.element,t)?os:ss)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return hD(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n,s)=>{const r=Le(t,{source:"dynamic"});return TF(EI)(e,r,o,n,s)}),button:TF(((e,t)=>((e,t)=>{const o=hF(e.name,"custom");return n=A.none(),s=Ux.parts.field({factory:Tv,...gF(e,A.some(o),t,[VD(""),FD()])}),hD(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:TF(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=Ux.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:rd([FD(),rp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{po(e.element).each((e=>os(e,"tox-checkbox--disabled")))},onEnabled:e=>{po(e.element).each((e=>ss(e,"tox-checkbox--disabled")))}}),Gb.config({}),fd.config({}),LD(o,Jn,Kn),Om.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),ud("checkbox-events",[Wc(ka(),((t,o)=>{Rc(t,xD,{name:e.name})}))])])}),r=Ux.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[Ag(t.translate(e.label))],behaviours:rd([mv.config({})])}),a=e=>rO("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=pv({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return Ux.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:rd([rp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable}),yA((()=>t.checkUiComponentContext(e.context)))])})})(e,t.shared.providers,o))),colorinput:TF(((e,t,o)=>((e,t,o,n)=>{const s=Ux.parts.field({factory:Zx,inputClasses:["tox-textfield"],data:n,onSetValue:e=>ef.run(e).get(b),inputBehaviours:rd([rp.config({disabled:()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable}),yA((()=>t.providers.checkUiComponentContext(e.context))),Gb.config({}),ef.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>po(e.element),notify:{onValid:e=>{const t=Zh.getValue(e);Rc(e,MD,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=Zh.getValue(e);if(0===t.length)return _e(Ae.value(!0));{const e=it("span");mn(e,"background-color",t);const o=bn(e,"background-color").fold((()=>Ae.error("blah")),(e=>Ae.value(t)));return _e(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>vD(e,t.providers))),a=(e,t)=>{Rc(e,DD,{value:t})},i=pv(((e,t)=>kx.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:rd([lA((()=>t.providers.isDisabled()||t.providers.checkUiComponentContext(e.context).shouldDisable)),yA((()=>t.providers.checkUiComponentContext(e.context))),mv.config({}),Gb.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>Oe((t=>e.fetch(t))).map((n=>A.from(uD(Le(XM(Ne("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,dO.CLOSE_ON_EXECUTE,T,t.providers),{movement:KM(e.columns,e.presets)}))))),parts:{menu:EO(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Ul,Pl,jl],onLtr:()=>[Pl,Ul,jl]},components:[],fetch:HM(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Ic(t,BD)),(o=>{a(t,o),CM(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))},context:e.context},t));return Ux.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:rd([ud("form-field-events",[Wc(MD,((t,o)=>{i.getOpt(t).each((e=>{mn(e.element,"background-color",o.event.color)})),Rc(t,xD,{name:e.name})})),Wc(DD,((e,t)=>{Ux.getField(e).each((o=>{Zh.setValue(o,t.event.value),Pg.getCurrent(e).each(fd.focus)}))})),Wc(BD,((e,t)=>{Ux.getField(e).each((t=>{Pg.getCurrent(e).each(fd.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:TF(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=YD((e=>t=>r(t)?e.translate(KD[t]):e.translate(t))(t),n,t.tooltips.getConfig,((e,o,n=e,s=e)=>rO(n,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:t.translate(s),"aria-live":"polite",...o.fold((()=>({})),(e=>({id:e})))}},t.icons))),a=pv(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Rc(e,CD,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Rc(e,CD,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:rd([zD(o,(e=>{const t=a.get(e);return Pg.getCurrent(t).bind((e=>Zh.getValue(e).hex)).map((e=>"#"+Ke(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>ee(e,1))),n=a.get(e);Pg.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{Zh.setValue(e,{hex:o.getOr("")}),Ax.getField(e,"hex").each((e=>{Ic(e,Ca())}))}))})),FD()])}})(0,t.shared.providers,o))),dropzone:TF(((e,t,o)=>tB(e,t.shared.providers,o))),grid:TF(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:L(e.items,t.interpreter)}))(e,t.shared))),listbox:TF(((e,t,o)=>((e,t,o)=>{const n=R(e.items,(e=>!HB(e))),s=t.shared.providers,r=o.bind((t=>WB(e.items,t))).orThunk((()=>te(e.items).filter(HB))),a=e.label.map((e=>vD(e,s))),i=Ux.parts.field({dom:{},factory:{sketch:o=>RB({context:e.context,uid:o.uid,text:r.map((e=>e.text)),icon:A.none(),tooltip:A.none(),role:$e(!n,"combobox"),...n?{}:{listRole:"listbox"},ariaLabel:e.label,fetch:(o,s)=>{const r=UB(o,e.name,e.items,Zh.getValue(o),n);s(VB(r,dO.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Gb.config({}),zD(r.map((e=>e.value)),(e=>Yo(e.element,PB)),((t,o)=>{WB(e.items,o).each((e=>{qo(t.element,PB,e.value),Rc(t,BB,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),l={dom:{tag:"div",classes:["tox-listboxfield"]},components:[i]};return Ux.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:j([a.toArray(),[l]]),fieldBehaviours:rd([rp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(rp.disable)},onEnabled:e=>{Ux.getField(e).each(rp.enable)}})])})})(e,t,o))),selectbox:TF(((e,t,o)=>((e,t,o)=>{const n=L(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>vD(e,t))),r=Ux.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:qx,selectBehaviours:rd([rp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable}),Gb.config({}),ud("selectbox-change",[Wc(ka(),((t,o)=>{Rc(t,xD,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(rO("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:j([[r],a.toArray()])};return Ux.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:j([s.toArray(),[i]]),fieldBehaviours:rd([rp.config({disabled:()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(rp.disable)},onEnabled:e=>{Ux.getField(e).each(rp.enable)}}),yA((()=>t.checkUiComponentContext(e.context)))])})})(e,t.shared.providers,o))),sizeinput:TF(((e,t)=>((e,t)=>{let o=qB;const n=Ne("ratio-event"),s=e=>rO(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=()=>!e.enabled||t.checkUiComponentContext(e.context).shouldDisable,a=yA((()=>t.checkUiComponentContext(e.context))),i=e.label.getOr("Constrain proportions"),l=t.translate(i),c=jx.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":l,"data-mce-name":i}},components:[s("lock"),s("unlock")],buttonBehaviours:rd([rp.config({disabled:r}),a,Gb.config({}),uv.config(t.tooltips.getConfig({tooltipText:l}))])}),d=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),u=t=>Ux.parts.field({factory:Zx,inputClasses:["tox-textfield"],inputBehaviours:rd([rp.config({disabled:r}),a,Gb.config({}),ud("size-input-events",[Wc(xa(),((e,o)=>{Rc(e,n,{isField1:t})})),Wc(ka(),((t,o)=>{Rc(t,xD,{name:e.name})}))])]),selectOnFocus:!1}),m=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Ag(t.translate(e))]}),g=jx.parts.field1(d([Ux.parts.label(m("Width")),u(!0)])),p=jx.parts.field2(d([Ux.parts.label(m("Height")),u(!1)]));return jx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[g,p,d([m("\xa0"),c])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{GB(Zh.getValue(e)).each((e=>{o(e).each((e=>{Zh.setValue(t,$B(e))}))}))},coupledFieldBehaviours:rd([rp.config({disabled:r,onDisabled:e=>{jx.getField1(e).bind(Ux.getField).each(rp.disable),jx.getField2(e).bind(Ux.getField).each(rp.disable),jx.getLock(e).each(rp.disable)},onEnabled:e=>{jx.getField1(e).bind(Ux.getField).each(rp.enable),jx.getField2(e).bind(Ux.getField).each(rp.enable),jx.getLock(e).each(rp.enable)}}),yA((()=>t.checkUiComponentContext("mode:design"))),ud("size-input-events2",[Wc(n,((e,t)=>{const n=t.event.isField1,s=n?jx.getField1(e):jx.getField2(e),r=n?jx.getField2(e):jx.getField1(e),a=s.map(Zh.getValue).getOr(""),i=r.map(Zh.getValue).getOr("");o=XB(a,i)}))])])})})(e,t.shared.providers))),slider:TF(((e,t,o)=>((e,t,o)=>{const n=Pw.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ag(t.translate(e.label))]}),s=Pw.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=Pw.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return Pw.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:rd([FD(),fd.config({})]),onChoose:(t,o,n)=>{Rc(t,xD,{name:e.name,value:n})},onChange:(t,o,n)=>{Rc(t,xD,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:TF(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=Zh.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":_F,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=Zh.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=OF(a,(e=>L(e,(e=>xF(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,OF(a,wF(t)),OF(a,j([CF(t),SF(t),kF(t)]))],W(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(bF,t)),[])):n;var s}))})(e.filetype,n,o),r=VB(s,dO.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return _e(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(ef)&&ef.run(e).get(b)},typeaheadBehaviours:rd([...o.getValidationHandler().map((t=>ef.config({getRoot:e=>po(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{qo(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=Zh.getValue(o);return De((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=Ae.error(e.message);o(t)}else{const t=Ae.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),rp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable}),Gb.config({}),ud("urlinput-events",[Wc(Ca(),(t=>{const o=cs(t.element),n=o.trim();n!==o&&ds(t.element,n),"file"===e.filetype&&Rc(t,xD,{name:e.name})})),Wc(ka(),(t=>{Rc(t,xD,{name:e.name}),r(t)})),Wc(Ia(),(t=>{Rc(t,xD,{name:e.name}),r(t)}))])]),eventOrder:{[Ca()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:EO(0,0,"normal")},onExecute:(e,t,o)=>{Rc(t,kD,{})},onItemExecute:(t,o,n,s)=>{r(t),Rc(t,xD,{name:e.name})}},i=Ux.parts.field({...a,factory:MS}),l=e.label.map((e=>vD(e,s))),c=pv(((e,t,o=e,n=e)=>rO(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(_F),"warning")),d=pv({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=Ne("browser.url.event"),g=pv({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:rd([rp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable})])}),p=pv(pF({context:e.context,name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Ic(e,m)),s,[],["tox-browse-url"]));return Ux.sketch({dom:bD([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:j([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:rd([rp.config({disabled:()=>!e.enabled||s.checkUiComponentContext(e.context).shouldDisable,onDisabled:e=>{Ux.getField(e).each(rp.disable),p.getOpt(e).each(rp.disable)},onEnabled:e=>{Ux.getField(e).each(rp.enable),p.getOpt(e).each(rp.enable)}}),yA((()=>s.checkUiComponentContext(e.context))),ud("url-input-events",[Wc(m,(t=>{Pg.getCurrent(t).each((o=>{const n=Zh.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{Zh.setValue(o,n),Rc(t,xD,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:TF((e=>{const t=Xe(),o=pv({dom:{tag:e.tag}}),n=Xe(),s=!QD(e)&&e.onFocus.isSome()?[fd.config({onFocus:t=>{e.onFocus.each((e=>{e(t.element.dom)}))}}),Gb.config({})]:[];return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:rd([ud("custom-editor-events",[Jc((s=>{o.getOpt(s).each((o=>{(QD(e)?e.init(o.element.dom):JD.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),zD(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),FD()].concat(s)),components:[o.asSpec()]}})),htmlpanel:TF(((e,t)=>((e,t)=>{const o=["tox-form__group",...e.stretched?["tox-form__group--stretched"]:[]],n=ud("htmlpanel",[Jc((t=>{e.onInit(t.element.dom)}))]);return"presentation"===e.presets?ch.sketch({dom:{tag:"div",classes:o,innerHtml:e.html},containerBehaviours:rd([uv.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{Xn(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Ho(e.element))).each((o=>{Ko(o,"data-mce-tooltip").each((o=>{uv.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-normal",anchor:e=>({type:"node",node:Xn(e.element,"[data-mce-tooltip]:hover").orThunk((()=>Ho(e.element).filter((e=>Ko(e,"data-mce-tooltip").isSome())))),root:e.element,layouts:{onLtr:y([jl,Gl,Pl,Wl,Ul,$l]),onRtl:y([jl,Gl,Pl,Wl,Ul,$l])},bubble:wl(0,-2,{})})}),n])}):ch.sketch({dom:{tag:"div",classes:o,innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:rd([Gb.config({}),fd.config({}),n])})})(e,t.shared.providers))),imagepreview:TF(((e,t,o)=>((e,t)=>{const o=ye(t.getOr({url:""})),n=pv({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=pv({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:rd([FD(),zD(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=En(e),s=kn(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(En(e.element),kn(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{gn(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;t.url!==Yo(n,"src")&&(qo(n,"src",t.url),ss(e.element,"tox-imagepreview__loaded")),a(),sn(n).then((t=>{e.getSystem().isConnected()&&(os(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:TF(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:L(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:L(n,(e=>({dom:{tag:"tr"},components:L(e,o)})))})],behaviours:rd([Gb.config({}),fd.config({})])};var n,s})(e,t.shared.providers))),tree:TF(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=ye(s),a=ye(e.defaultSelectedId),i=Ne("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?oF({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):lF({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:rd([Om.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),ud(cF,[Wc("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),ab.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),ng.set(e,l(A.some(t.value),r.get()))}}}}),ng.config({})])}})(e,t))),panel:TF(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:L(e.items,t.shared.interpreter)}))(e,t)))},AF={field:(e,t)=>t,record:y([])},MF=(e,t,o,n,s)=>{const r=Le(n,{shared:{interpreter:t=>DF(e,t,o,r,s)}});return DF(e,t,o,r,s)},DF=(e,t,o,n,s)=>fe(EF,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(r=>r(e,t,o,n,s))),BF=(e,t,o,n)=>DF(AF,e,t,o,n),FF={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},IF=(e,t,o)=>{const n={maxHeightFunction:kc()};return()=>o()?{type:"node",root:Oo(ko(e())),node:A.from(e()),bubble:wl(12,12,FF),layouts:{onRtl:()=>[gc],onLtr:()=>[mc]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:wl(-12,12,FF),layouts:{onRtl:()=>[Pl,Ul,jl],onLtr:()=>[Ul,Pl,jl]},overrides:n}},RF=(e,t,o,n)=>{const s={maxHeightFunction:kc()};return()=>n()?{type:"node",root:Oo(ko(t())),node:A.from(t()),bubble:wl(12,12,FF),layouts:{onRtl:()=>[pc],onLtr:()=>[pc]},overrides:s}:e?{type:"node",root:Oo(ko(t())),node:A.from(t()),bubble:wl(0,-On(t()),FF),layouts:{onRtl:()=>[Gl],onLtr:()=>[Gl]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:wl(0,0,FF),layouts:{onRtl:()=>[Gl],onLtr:()=>[Gl]},overrides:s}},NF=(e,t,o)=>()=>o()?{type:"node",root:Oo(ko(e())),node:A.from(e()),layouts:{onRtl:()=>[pc],onLtr:()=>[pc]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[jl],onLtr:()=>[jl]}},zF=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ct(e),lastCell:ct(t)};return A.some(n)}return A.some(Ss.range(ct(t.startContainer),t.startOffset,ct(t.endContainer),t.endOffset))}}),LF=e=>t=>({type:"node",root:e(),node:t}),VF=(e,t,o,n)=>{const s=NC(e),r=()=>ct(e.getBody()),a=()=>ct(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:IF(a,t,i),inlineBottomDialog:RF(e.inline,a,o,i),banner:NF(a,t,i),cursor:zF(e,r),node:LF(r)}},HF=e=>(t,o)=>{qM(e)(t,o)},PF=e=>()=>FM(e),UF=e=>t=>AM(e,t),WF=e=>t=>BM(e,t),$F=e=>()=>fC(e),GF=e=>ve(e,"items"),jF=e=>ve(e,"format"),qF=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],XF=e=>W(e,((e,t)=>{if(be(t,"items")){const o=XF(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(be(t,"inline")||(e=>be(e,"block"))(t)||(e=>be(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),YF=e=>YS(e).map((t=>{const o=((e,t)=>{const o=XF(t),n=t=>{V(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return KS(e)?qF.concat(o):o})).getOr(qF),KF=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),JF=(e,t,o,n)=>{const s=t=>L(t,(t=>GF(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:y(t)}})(t):jF(t)?(e=>KF(e,o,n))(t):(e=>{const t=re(e);return 1===t.length&&I(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:Ne(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},QF=e=>{let t=0;const o=e=>[{dom:{tag:"div",classes:["tox-tooltip__body"]},components:[Ag(e.tooltipText)]}];return{getConfig:n=>({delayForShow:()=>t>0?60:300,delayForHide:y(300),exclusive:!0,lazySink:e,tooltipDom:{tag:"div",classes:["tox-tooltip","tox-tooltip--up"]},tooltipComponents:o(n),onShow:(e,o)=>{t++,n.onShow&&n.onShow(e,o)},onHide:(e,o)=>{t--,n.onHide&&n.onHide(e,o)},onSetup:n.onSetup}),getComponents:o}},ZF=ZD.trim,eI=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},tI=eI("true"),oI=eI("false"),nI=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),sI=e=>e.innerText||e.textContent,rI=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&iI(e),aI=e=>e&&/^(H[1-6])$/.test(e.nodeName),iI=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return tI(t)}return!1})(e)&&!oI(e),lI=e=>aI(e)&&iI(e),cI=e=>{var t;const o=(e=>e.id?e.id:Ne("h"))(e);return nI("header",null!==(t=sI(e))&&void 0!==t?t:"","#"+o,(e=>aI(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},dI=e=>{const t=e.id||e.name,o=sI(e);return nI("anchor",o||"#"+t,"#"+t,0,b)},uI=e=>ZF(e.title).length>0,mI=e=>{const t=(e=>{const t=L(ms(ct(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return P((e=>L(P(e,lI),cI))(t).concat((e=>L(P(e,rI),dI))(t)),uI)},gI="tinymce-url-history",pI=e=>r(e)&&/^https?/.test(e),hI=e=>a(e)&&pe(e,(e=>{return!(l(t=e)&&t.length<=5&&X(t,pI));var t})).isNone(),fI=()=>{const e=yM.getItem(gI);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+gI+" was not valid JSON",e),{};throw e}return hI(t)?t:(console.log("Local storage "+gI+" was not valid format",t),{})},bI=e=>{const t=fI();return fe(t,e).getOr([])},vI=(e,t)=>{if(!pI(e))return;const o=fI(),n=fe(o,t).getOr([]),s=P(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!hI(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));yM.setItem(gI,JSON.stringify(e))})(o)},xI=e=>!!e,yI=e=>le(ZD.makeMap(e,/[, ]/),xI),wI=e=>A.from(cC(e)),SI=e=>A.from(e).filter(r).getOrUndefined(),CI=e=>({getHistory:bI,addToHistory:vI,getLinkInformation:()=>(e=>gC(e)?A.some({targets:mI(e.getBody()),anchorTop:SI(pC(e)),anchorBottom:SI(hC(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(dC(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(mC(e)).filter(xI).map(yI);return wI(e).fold(T,(e=>t.fold(E,(e=>re(e).length>0&&e))))})(e);return d(o)?o?wI(e):A.none():o[t]?wI(e):A.none()})(e,t).map((o=>n=>Oe((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),kI={skipFocus:"ToggleToolbarDrawer skipFocus is deprecated see migration guide: https://www.tiny.cloud/docs/tinymce/latest/migration-from-7x/"},OI=b,_I=T,TI=y([]);var EI,AI=Object.freeze({__proto__:null,setup:OI,isDocked:_I,getBehaviours:TI});const MI=y(Ne("toolbar-height-change")),DI={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},BI="tox-tinymce--toolbar-sticky-on",FI="tox-tinymce--toolbar-sticky-off",II=(e,t)=>I(Bp.getModes(e),t),RI=e=>{const t=e.element;po(t).each((o=>{const n="padding-"+Bp.getModes(e)[0];if(Bp.isDocked(e)){const e=En(o);mn(t,"width",e+"px"),mn(o,n,(e=>On(e)+(parseInt(hn(e,"margin-top"),10)||0)+(parseInt(hn(e,"margin-bottom"),10)||0))(t)+"px")}else yn(t,"width"),yn(o,n)}))},NI=(e,t)=>{t?(ss(e,DI.fadeOutClass),as(e,[DI.transitionClass,DI.fadeInClass])):(ss(e,DI.fadeInClass),as(e,[DI.fadeOutClass,DI.transitionClass]))},zI=(e,t)=>{const o=ct(e.getContainer());t?(os(o,BI),ss(o,FI)):(os(o,FI),ss(o,BI))},LI=(e,t)=>{const o=Xe(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||RI(t),zI(e,Bp.isDocked(t)),t.getSystem().broadcastOn([Ed()],{}),n().each((e=>e.getSystem().broadcastOn([Ed()],{})))},a=e.inline?[]:[ab.config({channels:{[MI()]:{onReceive:RI}}})];return[fd.config({}),Bp.config({contextual:{lazyContext:t=>{const o=On(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Rs(ct(n));return qC(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(II(t,"top")?0:o);return Is(s.x,n,s.width,e)}),(e=>{const n=zs(s,XC(e)),r=II(t,"top")?n.y:n.y+o;return Is(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>NI(e,!0)))},onShown:e=>{s((e=>is(e,[DI.transitionClass,DI.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=lo(t);Vo(o).filter((e=>!yt(t,e))).filter((t=>yt(t,ct(o.dom.body))||wt(e,t))).each((()=>No(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Ho(e).orThunk((()=>t().toOptional().bind((e=>Ho(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>NI(e,!1)))},onHidden:()=>{s((e=>is(e,[DI.transitionClass])))},...DI},lazyViewport:t=>qC(e,t.element).fold((()=>{const o=Ls(),n=aC(e),s=o.y+(II(t,"top")&&!jC(e)?n:0),r=o.height-(II(t,"bottom")?n:0);return{bounds:Is(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:XC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Fn(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var VI=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(Bp.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(RI)})),e.on("SkinLoaded",(()=>{o().each((e=>{Bp.isDocked(e)?Bp.reset(e):Bp.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(Bp.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{Bp.refresh(t);const o=t.element;_n(o)&&((e,t)=>{const o=lo(t),n=mo(t).dom.innerHeight,s=Rn(o),r=ct(e.elm),a=Ns(r),i=kn(r),l=a.y,c=l+i,d=Fn(t),u=kn(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Nn(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Nn(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{zI(e,!1)}))},isDocked:e=>e().map(Bp.isDocked).getOr(!1),getBehaviours:LI}),HI=ih({factory:(e,t)=>{const o={focus:Om.focusIn,setMenus:(e,o)=>{const n=L(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())},context:"any"},n=zT(o).mapError((e=>Ar(e))).getOrDie();return JB(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));ng.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:rd([ng.config({}),ud("menubar-events",[Jc((t=>{e.onSetup(t)})),Wc(va(),((e,t)=>{Xn(e.element,".tox-mbtn--active").each((o=>{Yn(t.event.target,".tox-mbtn").each((t=>{yt(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{kx.expand(e),kx.close(o),fd.focus(e)}))}))}))}))})),Wc(Ja(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{kx.isOpen(o)&&(kx.expand(e),kx.close(o))}))}))}))]),Om.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),Gb.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Rr("dom"),Rr("uid"),Rr("onEscape"),Rr("backstage"),Qr("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const PI=e=>({element:()=>e.element.dom}),UI=(e,t)=>{const o=L(re(t),(e=>{const o=t[e],n=Tr((e=>_r("sidebar",JE,e))(o));return{name:e,getApi:PI,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return L(o,(t=>{const n=ye(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:aA([uA(t,n),mA(t,n),Wc(Qa(),((e,t)=>{const n=t.event,s=$(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},WI=e=>qw.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:UI(t,e),slotBehaviours:aA([Jc((e=>qw.hideAllSlots(e)))])}))),$I=(e,t)=>{qo(e,"role",t)},GI=e=>Pg.getCurrent(e).bind((e=>zb.isGrowing(e)||zb.hasGrown(e)?Pg.getCurrent(e).bind((e=>$(qw.getSlotNames(e),(t=>qw.isShowing(e,t))))):A.none())),jI=Ne("FixSizeEvent"),qI=Ne("AutoSizeEvent"),XI=e=>Pg.getCurrent(e).each((e=>No(e.element,!0))),YI=(e,t,o)=>{const n=ye(!1),s=Xe(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?te(s.composedPath()):A.from(s.target)).map(ct).filter(no).exists((e=>rs(e,"mce-pastebin"))))&&(o.preventDefault(),XI(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ct).each((e=>{t?(Ko(e,o).each((t=>qo(e,n,t))),qo(e,o,-1)):(Qo(e,o),Ko(e,n).each((t=>{qo(e,o,t),Qo(e,n)})))}))})(e,o),o)Lg.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:gv('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),yn(s,"display"),Qo(s,"aria-hidden"),e.hasFocus()&&XI(t);else{const o=Pg.getCurrent(t).exists((e=>Lo(e.element)));Lg.unblock(t),mn(s,"display","none"),qo(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=IS.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},KI=e=>{const t=e.label.isNone()?e.title.fold((()=>({})),(e=>({attributes:{"aria-label":e}}))):e.label.fold((()=>({})),(e=>({attributes:{"aria-label":e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"].concat(e.label.isSome()?["tox-toolbar__group_with_label"]:[]),...t},components:[...e.label.map((e=>({dom:{tag:"span",classes:["tox-label","tox-label--context-toolbar"]},components:[Ag(e)]}))).toArray(),sS.parts.items({})],items:e.items,markers:{itemSelector:".tox-tbtn:not([disabled]), .tox-toolbar-nav-item:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:rd([Gb.config({}),fd.config({ignore:!0})])}},JI=e=>sS.sketch(KI(e)),QI=(e,t)=>{const o=Jc((t=>{const o=L(e.initGroups,JI);Bx.setGroups(t,o)}));return rd([cA((()=>e.providers.checkUiComponentContext("any").shouldDisable)),yA((()=>e.providers.checkUiComponentContext("any"))),Om.config({mode:t,onEscape:e.onEscape,visibilitySelector:".tox-toolbar__overflow",selector:".tox-toolbar__group"}),ud("toolbar-events",[o])])},ZI=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":KI({title:A.none(),label:A.none(),items:[]}),"overflow-button":uF({context:"any",name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers,[],"overflow-button")},splitToolbarBehaviours:QI(e,t)}},eR=e=>{const t=ZI(e),o=iS.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return iS.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Ns(t),n=uo(t),s=Ns(n),r=Math.max(n.dom.scrollHeight,s.height);return Is(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},tR=e=>{const t=mS.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=mS.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=ZI(e);return mS.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([MI()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([MI()],{type:"closed"}),e.onToggled(t,!1)}})},oR=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return Bx.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===DS.scrolling?["tox-toolbar--scrolling"]:[])},components:[Bx.parts.groups({})],toolbarBehaviours:QI(e,t)})},nR=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>MB(e,t.icons))).map(pv),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=mF(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(Ag),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=CA([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=dF(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{ng.set(o,[MB(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(os(t,"tox-button--enabled"),qo(t,"aria-pressed",!0)):(ss(t,"tox-button--enabled"),Qo(t,"aria-pressed"))},isActive:()=>rs(o.element,"tox-button--enabled"),focus:()=>No(o.element)}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,e.tooltip,t);return Tv.sketch(h)})(e,t),sR=Jt().deviceType,rR=sR.isPhone(),aR=sR.isTablet();var iR=lh({name:"silver.View",configFields:[Rr("viewConfig")],partFields:[Zi({factory:{sketch:e=>{let t=!1;const o=L(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:L(e.buttons,(e=>nR(e,t)))}))(o,e.providers)):nR(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...rR||aR?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:rd([fd.config({}),Om.config({mode:"flow",selector:"button, .tox-button",focusInside:Ei.OnEnterOrSpaceMode})]),components:t?o:[ch.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),ch.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[Rr("buttons"),Rr("providers")],name:"header"}),Zi({factory:{sketch:e=>({uid:e.uid,behaviours:rd([fd.config({}),Gb.config({})]),dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>hv.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,behaviours:rd([fd.config({}),Om.config({mode:"cyclic",focusInside:Ei.OnEnterOrSpaceMode})]),apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const lR=(e,t,o)=>ge(t,((t,n)=>{const s=Tr(_r("view",sA,t));return e.slot(n,iR.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[iR.parts.header({buttons:s.buttons,providers:o})]:[],iR.parts.pane({})]}))})),cR=(e,t)=>qw.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:lR(o,e,t),slotBehaviours:aA([Jc((e=>qw.hideAllSlots(e)))])}))),dR=e=>$(qw.getSlotNames(e),(t=>qw.isShowing(e,t))),uR=(e,t,o)=>{qw.getSlot(e,t).each((e=>{iR.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:y(n)}))}))}))};var mR=ih({factory:(e,t)=>{const o={setViews:(e,o)=>{ng.set(e,[cR(o,t.backstage.shared.providers)])},whichView:e=>Pg.getCurrent(e).bind(dR),toggleView:(e,t,o,n)=>Pg.getCurrent(e).exists((s=>{const r=dR(s),a=r.exists((e=>n===e)),i=qw.getSlot(s,n).isSome();return i&&(qw.hideAllSlots(s),a?((e=>{const t=e.element;mn(t,"display","none"),qo(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;yn(t,"display"),Qo(t,"aria-hidden")})(e),qw.showSlot(s,n),((e,t)=>{uR(e,t,iR.getOnShow)})(s,n)),r.each((e=>((e,t)=>uR(e,t,iR.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:rd([ng.config({}),Pg.config({find:e=>{const t=ng.contents(e);return te(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[Rr("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const gR=fv.optional({factory:HI,name:"menubar",schema:[Rr("backstage")]}),pR=fv.optional({factory:{sketch:e=>Dv.sketch({uid:e.uid,dom:e.dom,listBehaviours:rd([Om.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>oR({type:e.type,uid:Ne("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{Bx.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[Rr("dom"),Rr("onEscape")]}),hR=fv.optional({factory:{sketch:e=>{const t=(e=>e.type===DS.sliding?tR:e.type===DS.floating?eR:oR)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[Rr("dom"),Rr("onEscape"),Rr("getSink")]}),fR=fv.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?LI:TI;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:rd(o(t,e.sharedBackstage))}}},name:"header",schema:[Rr("dom")]}),bR=fv.optional({factory:{sketch:e=>{const t=e.promotionLink?[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-upgrade-to-cloud/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u{1f49d} Get all features"}}]:[];return{uid:e.uid,dom:e.dom,components:t}}},name:"promotion",schema:[Rr("dom"),Rr("promotionLink")]}),vR=fv.optional({name:"socket",schema:[Rr("dom")]}),xR=fv.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:rd([Gb.config({}),fd.config({}),zb.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{Pg.getCurrent(e).each(qw.hideAllSlots),Ic(e,qI)},onGrown:e=>{Ic(e,qI)},onStartGrow:e=>{Rc(e,jI,{width:bn(e.element,"width").getOr("")})},onStartShrink:e=>{Rc(e,jI,{width:En(e.element)+"px"})}}),ng.config({}),Pg.config({find:e=>{const t=ng.contents(e);return te(t)}})])}],behaviours:rd([ID(0),ud("sidebar-sliding-events",[Wc(jI,((e,t)=>{mn(e.element,"width",t.event.width)})),Wc(qI,((e,t)=>{yn(e.element,"width")}))])])})},name:"sidebar",schema:[Rr("dom")]}),yR=fv.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:rd([ng.config({}),Lg.config({focus:!1}),Pg.config({find:e=>te(e.components())})]),components:[]})},name:"throbber",schema:[Rr("dom")]}),wR=fv.optional({factory:mR,name:"viewWrapper",schema:[Rr("backstage")]}),SR=fv.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var CR=lh({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s=e=>{jn(e,".tox-statusbar").each((e=>{"none"===hn(e,"display")&&"true"===Yo(e,"aria-hidden")?(yn(e,"display"),Qo(e,"aria-hidden")):(mn(e,"display","none"),qo(e,"aria-hidden","true"))}))},a={getSocket:t=>hv.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{hv.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{Pg.getCurrent(e).each((n=>{ng.set(n,[WI(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&be(t,s)&&Pg.getCurrent(n).each((t=>{qw.showSlot(t,s),zb.immediateGrow(n),yn(n.element,"width"),$I(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{hv.getPart(t,e,"sidebar").each((e=>((e,t)=>{Pg.getCurrent(e).each((o=>{Pg.getCurrent(o).each((n=>{zb.hasGrown(o)?qw.isShowing(n,t)?(zb.shrink(o),$I(e.element,"presentation")):(qw.hideAllSlots(n),qw.showSlot(n,t),$I(e.element,"region")):(qw.hideAllSlots(n),qw.showSlot(n,t),zb.grow(o),$I(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>hv.getPart(t,e,"sidebar").bind(GI).getOrNull(),getHeader:t=>hv.getPart(t,e,"header"),getToolbar:t=>hv.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{hv.getPart(t,e,"toolbar").each((e=>{const t=L(o,JI);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{hv.getPart(t,e,"multiple-toolbar").each((e=>{const t=L(o,(e=>L(e,JI)));Dv.setItems(e,t)}))},refreshToolbar:t=>{hv.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{hv.getPart(t,e,"toolbar").each((e=>{We(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{hv.getPart(t,e,"toolbar").each((e=>{We(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>hv.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>hv.getPart(t,e,"throbber"),focusToolbar:t=>{hv.getPart(t,e,"toolbar").orThunk((()=>hv.getPart(t,e,"multiple-toolbar"))).each((e=>{Om.focusIn(e)}))},setMenubar:(t,o)=>{hv.getPart(t,e,"menubar").each((e=>{HI.setMenus(e,o)}))},focusMenubar:t=>{hv.getPart(t,e,"menubar").each((e=>{HI.focus(e)}))},setViews:(t,o)=>{hv.getPart(t,e,"viewWrapper").each((e=>{mR.setViews(e,o)}))},toggleView:(t,o)=>hv.getPart(t,e,"viewWrapper").exists((e=>mR.toggleView(e,(()=>a.showMainView(t)),(()=>a.hideMainView(t)),o))),whichView:t=>hv.getPart(t,e,"viewWrapper").bind(mR.whichView).getOrNull(),hideMainView:t=>{n=a.isToolbarDrawerToggled(t),n&&a.toggleToolbarDrawer(t),hv.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),mn(t,"display","none"),qo(t,"aria-hidden","true")}))},showMainView:t=>{n&&a.toggleToolbarDrawer(t),hv.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),yn(t,"display"),Qo(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:a,behaviours:e.behaviours}},configFields:[Rr("dom"),Rr("behaviours")],partFields:[fR,gR,hR,pR,vR,xR,bR,yR,wR,SR],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const kR={file:{title:"File",items:"newdocument restoredraft | preview | importword exportpdf exportword | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code suggestededits revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed inserttemplate codesample inserttable accordion math | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},OR=e=>e.split(" "),_R=(e,t)=>{const o={...kR,...t.menus},n=re(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?OR("file edit view insert format tools table help"):OR(!1===t.menubar?"":t.menubar),a=P(s,(e=>{const o=be(kR,e);return n?o||fe(t.menus,e).exists((e=>be(e,"items"))):o})),i=L(a,(n=>{const s=o[n];return((e,t,o)=>{const n=ZS(o).split(/[ ,]/);return{text:e.title,getItems:()=>q(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:OR(s.items)},t,e)}));return P(i,(e=>e.getItems().length>0&&R(e.getItems(),(e=>r(e)||"separator"!==e.type))))},TR=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),ER=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),AR=e=>A.from(tinymce.Resource.get(e)).filter(r),MR=(e,t,o="")=>{const n=(e=>{const t=_C(e);return t?A.from(t):A.none()})(e).map((e=>((e,t)=>"ui/"+e+"/"+t)(e,`${t}.css`))),s=n.bind(AR);return Ue(n,s,((e,t)=>({_kind:"load-raw",key:e,css:t}))).getOrThunk((()=>{const n=e.editorManager.suffix;return{_kind:"load-stylesheet",url:o+`/${t}${n}.css`}}))},DR=(e,t)=>{const o=e.ui.styleSheetLoader,n=MR(e,"skin",t);switch(n._kind){case"load-raw":const{key:t,css:s}=n;return ER(e,t,s,o),Promise.resolve();case"load-stylesheet":const{url:r}=n;return TR(e,r,o);default:return Promise.resolve()}},BR=(e,t)=>{var o;if(o=ct(e.getElement()),!_o(o).isSome())return Promise.resolve();{const o=RS.DOM.styleSheetLoader,n=MR(e,"skin.shadowdom",t);switch(n._kind){case"load-raw":const{key:t,css:s}=n;return ER(e,t,s,o),Promise.resolve();case"load-stylesheet":const{url:r}=n;return TR(e,r,o);default:return Promise.resolve()}}},FR=(e,t)=>(async(e,t)=>{const o=AC(t);if(await((e,t,o)=>{const n=MR(e,t?"content.inline":"content",o);switch(n._kind){case"load-raw":const{key:s,css:r}=n;return t?ER(e,s,r,e.ui.styleSheetLoader):e.on("PostRender",(()=>{ER(e,s,r,e.dom.styleSheetLoader)})),Promise.resolve();case"load-stylesheet":const{url:a}=n;return o&&e.contentCSS.push(a),Promise.resolve();default:return Promise.resolve()}})(t,e,o),!TC(t)&&r(o))return Promise.all([DR(t,o),BR(t,o)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),(e=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),IR=C(FR,!1),RR=C(FR,!0),NR=e=>({isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t),setText:t=>Rc(e,BB,{text:t}),setIcon:t=>Rc(e,FB,{icon:t})}),zR=e=>({setActive:t=>{Zb.set(e,t)},isActive:()=>Zb.isOn(e),isEnabled:()=>!rp.isDisabled(e),setEnabled:t=>rp.set(e,!t),setText:t=>Rc(e,BB,{text:t}),setIcon:t=>Rc(e,FB,{icon:t})}),LR=(e,t)=>e.map((e=>({"aria-label":t.translate(e)}))).getOr({}),VR=Ne("focus-button"),HR=(e,t,o,n,s,r,a)=>{const i=t.map((e=>pv(DB(e,"tox-tbtn",s)))),l=e.map((e=>pv(MB(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:{...LR(o,s),...g(a)?{"data-mce-name":a}:{}}},components:CA([l.map((e=>e.asSpec())),i.map((e=>e.asSpec()))]),eventOrder:{[pa()]:["focusing","alloy.base.behaviour",OB],[qa()]:[OB,"toolbar-group-button-events"],[Xa()]:[OB,"toolbar-group-button-events","tooltipping"]},buttonBehaviours:rd([cA((()=>s.checkUiComponentContext(r).shouldDisable)),yA((()=>s.checkUiComponentContext(r))),ud(OB,[Jc(((e,t)=>TB(e))),Wc(BB,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{ng.set(e,[Ag(s.translate(t.event.text))])}))})),Wc(FB,((e,t)=>{l.bind((t=>t.getOpt(e))).each((e=>{ng.set(e,[MB(t.event.icon,s.icons)])}))})),Wc(pa(),((e,t)=>{t.event.prevent(),Ic(e,VR)}))])].concat(n.getOr([])))}},PR=(e,t,o,n)=>{var s;const r=ye(b),a=HR(e.icon,e.text,e.tooltip,A.none(),o,e.context,n);return Tv.sketch({dom:a.dom,components:a.components,eventOrder:_B,buttonBehaviours:{...rd([ud("toolbar-button-events",[(i={onAction:e.onAction,getApi:t.getApi},ed(((e,t)=>{dA(i,e)((t=>{Rc(e,kB,{buttonApi:t}),i.onAction(t)}))}))),uA(t,r),mA(t,r)]),...e.tooltip.map((t=>uv.config(o.tooltips.getConfig({tooltipText:o.translate(t)+e.shortcut.map((e=>` (${TA(e)})`)).getOr("")})))).toArray(),cA((()=>!e.enabled||o.checkUiComponentContext(e.context).shouldDisable)),yA((()=>o.checkUiComponentContext(e.context)))].concat(t.toolbarButtonBehaviours)),[OB]:null===(s=a.buttonBehaviours)||void 0===s?void 0:s[OB]}});var i},UR=(e,t,o,n)=>PR(e,{toolbarButtonBehaviours:o.length>0?[ud("toolbarButtonWith",o)]:[],getApi:NR,onSetup:e.onSetup},t,n),WR=(e,t,o,n)=>PR(e,{toolbarButtonBehaviours:[ng.config({}),Zb.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[ud("toolbarToggleButtonWith",o)]:[]),getApi:zR,onSetup:e.onSetup},t,n),$R=(e,t,o)=>n=>Oe((e=>t.fetch(e))).map((s=>A.from(uD(Le(XM(Ne("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,dO.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:KM(t.columns,t.presets),menuBehaviours:aA("auto"!==t.columns?[]:[Jc(((e,o)=>{rA(e,4,SO(t.presets)).each((({numRows:t,numColumns:o})=>{Om.setGridSize(e,t,o)}))}))])}))))),GR=e=>{Xx.getContent(e).each((e=>{Xn(e.element,".tox-toolbar-slider__input,.tox-toolbar-textfield").fold((()=>Om.focusIn(e)),No)}))},jR=Ne("forward-slide"),qR=Ne("backward-slide"),XR=Ne("change-slide-event"),YR="tox-pop--resizing",KR=(e,t,o)=>ot(o)?e.translate(t):e.translate([t,e.translate(o)]),JR=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=q(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>q(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return q(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(VB(a,dO.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},QR=(e,t)=>{const o=t.dataset,n="basic"===o.type?()=>L(o.data,(e=>KF(e,t.isSelectedFor,t.getPreviewFor))):o.getData;return{items:JR(e,t),getStyleItems:n}},ZR=(e,t,o,n,s,r)=>{const{items:a,getStyleItems:i}=QR(t,o),l=ye(o.tooltip);return RB({context:"mode:design",text:o.icon.isSome()?A.none():o.text,icon:o.icon,ariaLabel:A.some(o.tooltip),tooltip:A.none(),role:A.none(),fetch:a.getFetch(t,i),onSetup:t=>{const r=o=>t.setTooltip(KR(e,n(o.value),o.value));return e.on(s,r),pM(bM(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),rp.set(t.getComponent(),!e.selection.isEditable()||0===i().length)}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:y(e),setTooltip:o=>{const n=t.shared.providers.translate(o);qo(e.element,"aria-label",n),l.set(o)}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[uv.config({...t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(o.tooltip),onShow:e=>{if(o.tooltip!==l.get()){const o=t.shared.providers.translate(l.get());uv.setComponents(e,t.shared.providers.tooltips.getComponents({tooltipText:o}))}}})})]},"tox-tbtn",t.shared,r)};var eN;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(eN||(eN={}));const tN=(e,t,o)=>{const n=(s=((e,t)=>t===eN.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),L(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},oN=y("Alignment {0}"),nN="left",sN=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],rN=e=>{const t={type:"basic",data:sN};return{tooltip:KR(e,oN(),nN),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>$(sN,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=$(sN,(t=>e.formatter.match(t.format))).fold(y(nN),(e=>e.title.toLowerCase()));Rc(t,FB,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},aN=(e,t)=>{const o=t(),n=L(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>$(o,(t=>t.format===e))))},iN=y("Block {0}"),lN="Paragraph",cN=e=>{const t=tN(e,"block_formats",eN.SemiColon);return{tooltip:KR(e,iN(),lN),text:A.some(lN),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:vM(e),updateText:o=>{const n=aN(e,(()=>t.data)).fold(y(lN),(e=>e.title));Rc(o,BB,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},dN=y("Font {0}"),uN="System Font",mN=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],gN=e=>{const t=e.split(/\s*,\s*/);return L(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},pN=(e,t)=>t.length>0&&X(t,(t=>e.indexOf(t.toLowerCase())>-1)),hN=e=>{const t=()=>{const t=e=>e?gN(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=OC(e),i=$(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>$e(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=gN(e.toLowerCase());return pN(o,mN)||pN(o,t)}return!1})(r,a),{title:uN,format:r})));return{matchOpt:i,font:n}},o=tN(e,"font_family_formats",eN.SemiColon);return{tooltip:KR(e,dN(),uN),text:A.some(uN),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(y(s),(e=>e.title));Rc(o,BB,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}};var fN=tinymce.util.Tools.resolve("tinymce.util.VK");const bN=y("Font size {0}"),vN="12pt",xN={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},yN={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},wN=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":fe(yN,e).getOr(e),SN=e=>fe(xN,e).getOr(""),CN=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=wN(s,e),r=SN(n);t=$(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=y(A.none),n=tN(e,"font_size_formats",eN.Space);return{tooltip:KR(e,bN(),vN),text:A.some(vN),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(y(s),(e=>e.title));Rc(o,BB,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},kN=e=>ot(e)?"Formats":"Format {0}",ON=(e,t)=>{const o="Formats";return{tooltip:KR(e,kN(""),""),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:vM(e),updateText:t=>{const n=e=>GF(e)?q(e.items,n):jF(e)?[{title:e.title,format:e.format}]:[],s=q(YF(e),n),r=aN(e,y(s)).fold(y({title:o,tooltipLabel:""}),(e=>({title:e.title,tooltipLabel:e.title})));Rc(t,BB,{text:r.title}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r.tooltipLabel})},shouldHide:JS(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},_N=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],TN=(e,t)=>(o,n,s,r)=>{const a=e(o).mapError((e=>Ar(e))).getOrDie();return t(a,n,s,r)},EN={button:TN(fE,((e,t,o,n)=>((e,t,o)=>UR(e,t,[],o))(e,t.shared.providers,n))),togglebutton:TN(xE,((e,t,o,n)=>((e,t,o)=>WR(e,t,[],o))(e,t.shared.providers,n))),menubutton:TN(zT,((e,t,o,n)=>JB(e,"tox-tbtn",t,A.none(),!1,n))),splitbutton:TN((e=>_r("SplitButton",ZE,e)),((e,t,o,n)=>((e,t,o)=>{const n=ye(b),s=ye(e.tooltip.getOr("")),r=((e,t,o)=>n=>{const s=n.getSystem(),r=n.element,a=()=>{const e=rs(r,"tox-split-button__chevron");return{mainOpt:e?fo(r).bind((e=>s.getByDom(e).toOptional())):A.some(n),chevronOpt:e?A.some(n):bo(r).bind((e=>s.getByDom(e).toOptional().filter((e=>rs(e.element,"tox-split-button__chevron")))))}},i=e=>{const{mainOpt:t,chevronOpt:o}=a();t.each(e),o.each(e)};return{isEnabled:()=>{const{mainOpt:e}=a();return e.exists((e=>!rp.isDisabled(e)))},setEnabled:e=>i((t=>rp.set(t,!e))),setText:e=>{const{mainOpt:t}=a();t.each((t=>Rc(t,BB,{text:e})))},setIcon:e=>{const{mainOpt:t}=a();t.each((t=>Rc(t,FB,{icon:e})))},setIconFill:(e,t)=>i((o=>{Xn(o.element,`svg path[class="${e}"], rect[class="${e}"]`).each((e=>{qo(e,"fill",t)}))})),isActive:()=>{const{mainOpt:e}=a();return e.exists((e=>Zb.isOn(e)))},setActive:e=>{const{mainOpt:t}=a();t.each((t=>Zb.set(t,e)))},setTooltip:n=>{e.set(n);const{mainOpt:s,chevronOpt:r}=a();s.each((e=>qo(e.element,"aria-label",t.providers.translate(n))));const i=o.chevronTooltip.map((e=>t.providers.translate(e))).getOr(t.providers.translate(`${n} menu`));r.each((e=>qo(e.element,"aria-label",i)))}}})(s,t,e),a=Ne("tox-split-menu"),i=ye(!1),l=()=>e.tooltip.map((e=>t.providers.translate(e))).getOr(t.providers.translate("Text color")),c=()=>e.chevronTooltip.map((e=>t.providers.translate(e))).getOrThunk((()=>{const e=l();return t.providers.translate(["{0} menu",e])})),d=(e,t)=>{i.set(e),qo(t.element,"aria-expanded",String(e))},u=kx.sketch({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:oO("chevron-down",t.providers.icons),attributes:{"aria-label":c(),...g(o)?{"data-mce-name":o+"-chevron"}:{},"aria-haspopup":"menu","aria-expanded":String(i.get()),"aria-controls":a}},components:[],toggleClass:"tox-tbtn--enabled",dropdownBehaviours:rd([ud("split-dropdown-events",[Jc(((e,t)=>TB(e))),uA({getApi:r,onSetup:e.onSetup},n),Wc("alloy-dropdown-open",(e=>d(!0,e))),Wc("alloy-dropdown-close",(e=>d(!1,e)))]),cA((()=>t.providers.checkUiComponentContext(e.context).shouldDisable)),yA((()=>t.providers.checkUiComponentContext(e.context))),mv.config({}),uv.config(t.providers.tooltips.getConfig({tooltipText:c(),onShow:o=>{if(s.get()!==e.tooltip.getOr("")){const n=e.chevronTooltip.map((e=>t.providers.translate(e))).getOr(`${t.providers.translate(s.get())} menu`);uv.setComponents(o,t.providers.tooltips.getComponents({tooltipText:n}))}}}))]),lazySink:t.getSink,fetch:$R(r,e,t.providers),getHotspot:e=>fo(e.element).bind((t=>e.getSystem().getByDom(t).toOptional())),onOpen:(e,t,o)=>{Yd.highlightBy(o,(e=>rs(e.element,"tox-collection__item--active"))),Yd.getHighlighted(o).each(Om.focusIn)},parts:{menu:{...EO(0,e.columns,e.presets),dom:{...EO(0,e.columns,e.presets).dom,tag:"div",attributes:{id:a}}}}}),m=Tv.sketch({...HR(e.icon,e.text,A.none(),A.some([Zb.config({toggleClass:"tox-tbtn--enabled",aria:"color"===e.presets?{mode:"none"}:{mode:"pressed"},toggleOnExecute:!1}),cA((()=>t.providers.checkUiComponentContext(e.context).shouldDisable)),yA((()=>t.providers.checkUiComponentContext(e.context))),ud("split-main-aria-events",[]),...e.tooltip.isSome()?[uv.config(t.providers.tooltips.getConfig({tooltipText:t.providers.translate(e.tooltip.getOr("")),onShow:o=>{if(s.get()!==e.tooltip.getOr("")){const e=t.providers.translate(s.get());uv.setComponents(o,t.providers.tooltips.getComponents({tooltipText:e}))}}}))]:[]]),t.providers,e.context,o),dom:{...HR(e.icon,e.text,A.none(),A.none(),t.providers,e.context,o).dom,classes:["tox-tbtn","tox-split-button__main"],attributes:{"aria-label":l(),...g(o)?{"data-mce-name":o}:{}}},action:t=>{if(e.onAction){const o=r(t);o.isEnabled()&&e.onAction(o)}}});return[m,u]})(e,t.shared,n))),grouptoolbarbutton:TN((e=>_r("GroupToolbarButton",QE,e)),((e,t,o,n)=>{const s=o.ui.registry.getAll().buttons,r={[Tc]:t.shared.header.isPositionedAtTop()?_c.TopToBottom:_c.BottomToTop};if(eC(o)===DS.floating)return((e,t,o,n,s)=>{const r=t.shared,a=ye(b),i={toolbarButtonBehaviours:[],getApi:NR,onSetup:e.onSetup},l=[ud("toolbar-group-button-events",[uA(i,a),mA(i,a)]),...e.tooltip.map((e=>uv.config(t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(e)})))).toArray()];return Vx.sketch({lazySink:r.getSink,fetch:()=>Oe((t=>{t(L(o(e.items),JI))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:HR(e.icon,e.text,e.tooltip,A.some(l),r.providers,e.context,s),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>MN(o,{buttons:s,toolbar:e,allowToolbarGroups:!1},t,A.none())),r,n);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},AN={styles:(e,t)=>{const o={type:"advanced",...t.styles};return ZR(e,t,ON(e,o),kN,"StylesTextUpdate","styles")},fontsize:(e,t)=>ZR(e,t,CN(e),bN,"FontSizeTextUpdate","fontsize"),fontsizeinput:(e,t)=>((e,t,o,n)=>{let s=A.none();const r=bM(e,"NodeChange SwitchMode DisabledStateChange",(t=>{const n=t.getComponent();s=A.some(n),o.updateInputValue(n),rp.set(n,!e.selection.isEditable()||US(e))})),a=e=>({getComponent:y(e)}),i=ye(b),l=Ne("custom-number-input-events"),c=(e,t,n)=>{const r=s.map((e=>Zh.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=s.map((e=>e.element.dom.selectionStart-i)),c=s.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,n),s.each((e=>{Zh.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},d=(e,t)=>c(((e,t)=>e-t),e,t),u=(e,t)=>c(((e,t)=>e+t),e,t),m=e=>po(e.element).fold(A.none,(e=>(No(e),A.some(!0)))),p=e=>Lo(e.element)?(yo(e.element).each((e=>No(e))),A.some(!0)):A.none(),h=(o,n,s,r)=>{const i=ye(b),l=t.shared.providers.translate(s),c=Ne("altExecuting"),d=bM(e,"NodeChange SwitchMode DisabledStateChange",(t=>{rp.set(t.getComponent(),!e.selection.isEditable()||US(e))})),u=e=>{rp.isDisabled(e)||o(!0)};return Tv.sketch({dom:{tag:"button",attributes:{"aria-label":l,"data-mce-name":n},classes:r.concat(n)},components:[AB(n,t.shared.providers.icons)],buttonBehaviours:rd([rp.config({}),uv.config(t.shared.providers.tooltips.getConfig({tooltipText:l})),ud(c,[uA({onSetup:d,getApi:a},i),mA({getApi:a},i),Wc(wa(),((e,t)=>{t.event.raw.keyCode!==fN.SPACEBAR&&t.event.raw.keyCode!==fN.ENTER||rp.isDisabled(e)||o(!1)})),Wc(Oa(),u),Wc(ma(),u)])]),eventOrder:{[wa()]:[c,"keying"],[Oa()]:[c,"alloy.base.behaviour"],[ma()]:[c,"alloy.base.behaviour"],[qa()]:["alloy.base.behaviour",c,"tooltipping"],[Xa()]:[c,"tooltipping"]}})},f=pv(h((e=>d(!1,e)),"minus","Decrease font size",[])),v=pv(h((e=>u(!1,e)),"plus","Increase font size",[])),x=pv({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Zx.sketch({inputBehaviours:rd([rp.config({}),ud(l,[uA({onSetup:r,getApi:a},i),mA({getApi:a},i)]),ud("input-update-display-text",[Wc(BB,((e,t)=>{Zh.setValue(e,t.event.text)})),Wc(ya(),(e=>{o.onAction(Zh.getValue(e))})),Wc(ka(),(e=>{o.onAction(Zh.getValue(e))}))]),Om.config({mode:"special",onEnter:e=>(c(w,!0,!0),A.some(!0)),onEscape:m,onUp:e=>(u(!0,!1),A.some(!0)),onDown:e=>(d(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:rd([fd.config({}),Om.config({mode:"special",onEnter:p,onSpace:p,onEscape:m}),ud("input-wrapper-events",[Wc(va(),(e=>{V([f,v],(t=>{const o=ct(t.get(e).element.dom);Lo(o)&&zo(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"],attributes:{...g(n)?{"data-mce-name":n}:{}}},components:[f.asSpec(),x.asSpec(),v.asSpec()],behaviours:rd([fd.config({}),Om.config({mode:"flow",focusInside:Ei.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>Lo(e.element)?A.none():(No(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Rc(e,BB,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{Es(o,["unsupportedLength","empty"]);const s=t(),r=Es(o,["unsupportedLength","empty"]).or(Es(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=uC(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e),"fontsizeinput"),fontfamily:(e,t)=>ZR(e,t,hN(e),dN,"FontFamilyTextUpdate","fontfamily"),blocks:(e,t)=>ZR(e,t,cN(e),iN,"BlocksTextUpdate","blocks"),align:(e,t)=>ZR(e,t,rN(e),oN,"AlignTextUpdate","align"),navigateback:(e,t)=>{const o=Tr(fE({type:"button",icon:"chevron-left",tooltip:"Back",onAction:b}));return UR(o,t.shared.providers,[Wc(kB,(e=>{Ic(e,qR)}))])}},MN=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=L(_N,(t=>{const o=P(t.items,(t=>be(e,t)||be(AN,t)));return{name:t.name,items:o}}));return P(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return L(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>(be(e,"name")||be(e,"label"))&&be(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=L(s,(s=>{const r=q(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>fe(t,o.toLowerCase()).orThunk((()=>r.bind((e=>se(e,(e=>fe(t,e+o.toLowerCase()))))))).fold((()=>fe(AN,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o,n)=>fe(EN,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(s=>A.some(s(e,t,o,n)))))(t,s,e,o.toLowerCase()):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).map((e=>Array.isArray(e)?e:[e])).getOr([])));return{title:A.from(e.translate(s.name)),label:$e(void 0!==s.label,e.translate(s.label)),items:r}}));return P(a,(e=>e.items.length>0))},DN=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return MN(e,s,n,A.none())}));CR.setToolbars(s,t)}else CR.setToolbar(s,MN(e,o,n,A.none()))},BN=Jt(),FN=BN.os.isiOS()&&BN.os.version.major<=12;var IN=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=ye(0),l=r.outerContainer;IR(e);const d=ct(s.targetNode),u=Oo(ko(d));Um(d,r.mothership),((e,t,o)=>{HC(e)&&Um(o.mainUi.mothership.element,o.popupUi.mothership),Pm(t,o.dialogUi.mothership)})(e,u,t),e.on("PostRender",(()=>{CR.setSidebar(l,o.sidebar,SC(e))})),e.on("SkinLoaded",(()=>{DN(e,t,o,n),i.set(e.getWin().innerWidth),CR.setMenubar(l,_R(e,o)),CR.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=ye(Dn(s.innerWidth,s.innerHeight)),i=ye(Dn(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set(Dn(s.innerWidth,s.innerHeight)),dM(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(Dn(t.offsetWidth,t.offsetHeight)),dM(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=Fo(ct(e.getBody()),"load",c);e.on("hide",(()=>{V(o,(e=>{mn(e.element,"display","none")}))})),e.on("show",(()=>{V(o,(e=>{yn(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=CR.getSocket(l).getOrDie("Could not find expected socket element");if(FN){gn(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=(e=>{let t=null;return{cancel:()=>{c(t)||(clearTimeout(t),t=null)},throttle:(...o)=>{c(t)&&(t=setTimeout((()=>{t=null,e.apply(null,o)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Bo(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}xA(e,t),e.addCommand("ToggleSidebar",((t,o)=>{CR.toggleSidebar(l,o),(e=>{e.dispatch("ToggleSidebar")})(e)})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=CR.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(CR.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([Td()],{target:t}),V(a,(e=>{e.broadcastOn([Td()],{target:t})})),c(CR.whichView(l))&&(e.focus(),e.nodeChanged(),CR.refreshToolbar(l)),(e=>{e.dispatch("ToggleView")})(e)}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=CR.whichView(l))&&void 0!==e?e:""}));const g=eC(e);g!==DS.sliding&&g!==DS.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(CR.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{vA(t,e?"setEnabled":"setDisabled")},isEnabled:()=>!rp.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const RN=e=>h(e)?e+"px":e,NN=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},zN=e=>{const t=$S(e),o=GS(e),n=qS(e);return(s=t,/^[0-9\.]+(|px)$/i.test(""+s)?A.some(parseInt(""+s,10)):A.none()).map((e=>NN(e,o,n)));var s},{ToolbarLocation:LN,ToolbarMode:VN}=UC,HN=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=RS.DOM,l=NC(e),c=VC(e),d=qS(e).or(zN(e)),u=n.shared.header,m=u.isPositionedAtTop,g=eC(e),p=g===VN.sliding||g===VN.floating,h=ye(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?kn(e.components()[1].element):0)):0,v=()=>{V(a,(e=>{e.broadcastOn([Ed()],{})}))},x=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>Ms().width-In(t).left-10));mn(e.element,"max-width",o+"px")}));const n=Rn(),a=!(l||l||!(Fn(r.outerContainer.element).left+An(r.outerContainer.element)>=window.innerWidth-40||bn(r.outerContainer.element,"width").isSome())||(mn(r.outerContainer.element,"position","absolute"),mn(r.outerContainer.element,"left","0px"),yn(r.outerContainer.element,"width"),0));if(p&&CR.refreshToolbar(r.outerContainer),!l){const o=Rn(),i=$e(n.left!==o.left,n);((o,n)=>{s.on((s=>{const a=CR.getToolbar(r.outerContainer),i=b(a),l=Rs(t),c=((e,t)=>HC(e)?Hs(t):A.none())(e,r.outerContainer.element),d=c.fold((()=>l.x),(e=>{const t=Rs(e);return yt(e,ln())?l.x:l.x-t.x})),u=$e(o,Math.ceil(r.outerContainer.element.dom.getBoundingClientRect().width)).filter((e=>e>150)).map((e=>{const t=n.getOr(Rn()),o=window.innerWidth-(d-t.left),s=Math.max(Math.min(e,o),150);return o<e&&mn(r.outerContainer.element,"width",s+"px"),{width:s+"px"}})).getOr({width:"max-content"}),g={position:"absolute",left:Math.round(d)+"px",top:c.fold((()=>m()?Math.max(l.y-kn(s.element)+i,0):l.bottom),(e=>{var t;const o=Rs(e),n=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=yt(e,ln())?Math.max(l.y-kn(s.element)+i,0):l.y-o.y+n-kn(s.element)+i;return m()?r:l.bottom}))+"px"};gn(r.outerContainer.element,{...g,...u})}))})(a,i),i.each((e=>{Nn(e.left,o.top)}))}c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(oC(e)){case LN.auto:const e=CR.getToolbar(r.outerContainer),n=b(e),s=kn(o.element)-n,a=Rs(t);if(a.y>s)return"top";{const e=uo(t),o=Math.max(e.dom.scrollHeight,kn(e));return a.bottom<o-s||Ls().bottom<a.bottom-s?"bottom":"top"}case LN.bottom:return"bottom";case LN.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{Bp.setModes(e,[i]),u.setDockingMode(i);const t=m()?_c.TopToBottom:_c.BottomToTop;qo(e.element,Tc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),mn(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{yn(e.element,"display")})),w(),HC(e)?x((e=>Bp.isDocked(e)?Bp.reset(e):Bp.refresh(e))):x(Bp.refresh)},hide:()=>{h.set(!1),mn(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{mn(e.element,"display","none")}))},update:x,updateMode:()=>{w()&&x(Bp.reset)},repositionPopups:v}},PN=(e,t)=>{const o=Rs(e);return{pos:t?o.y:o.bottom,bounds:o}};var UN=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=Xe(),i=ct(s.targetNode),l=HN(e,i,t,n,a),c=rC(e);RR(e);const d=()=>{if(a.isSet())return void l.show();a.set(CR.getHeader(r.outerContainer).getOrDie());const s=zC(e);HC(e)?(Um(i,r.mothership),Um(i,t.popupUi.mothership)):Pm(s,r.mothership),Pm(s,t.dialogUi.mothership);const d=()=>{DN(e,t,o,n),CR.setMenubar(r.outerContainer,_R(e,o)),l.show(),((e,t,o,n)=>{const s=ye(PN(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=PN(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&dM(e,n),o.isVisible()&&(i!==r?o.update(Bp.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(Bp.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=nt((()=>o.update(Bp.refresh)),33);e.on("ScrollWindow",(()=>{const e=Rn().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),HC(e)&&e.on("ElementScroll",(e=>{o.update(Bp.refresh)}));const l=qe();l.set(Fo(ct(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};c?e.once("SkinLoaded",d):d()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),xA(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{vA(t,e?"setEnabled":"setDisabled")},isEnabled:()=>!rp.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const WN="contexttoolbar-hide",$N=(e,t,o)=>({setInputEnabled:t=>{!t&&o&&No(o),rp.set(e,!t)},isInputEnabled:()=>!rp.isDisabled(e),hide:()=>{Ic(e,Ha())},back:()=>{Ic(e,qR)},getValue:()=>t.get().getOrThunk((()=>Zh.getValue(e))),setValue:o=>{e.getSystem().isConnected()?Zh.setValue(e,o):t.set(o)}}),GN=(e,t,o)=>{const n=go(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));return $N(n.getOr(e),t,o)},jN=(e,t,o)=>Wc(kB,((n,s)=>{const r=e.get(n),a=$N(r,o,n.element);t.onAction(a,s.event.buttonApi)})),qN=(e,t,o,n)=>{const s=L(t,(t=>pv(((e,t,o,n)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o,n)=>{const{primary:s,...r}=t.original,a=Tr(xE({...r,type:"togglebutton",onAction:b}));return WR(a,o,[jN(e,t,n)])})(e,t,o,n):((e,t,o,n)=>{const{primary:s,...r}=t.original,a=Tr(fE({...r,type:"button",onAction:b}));return UR(a,o,[jN(e,t,n)])})(e,t,o,n))(e,t,o,n))));return{asSpecs:()=>L(s,(e=>e.asSpec())),findPrimary:e=>se(t,((t,o)=>t.primary?A.from(s[o]).bind((t=>t.getOpt(e))).filter(k(rp.isDisabled)):A.none()))}},XN=(e,t,o,n)=>{const{width:s,height:r}=e.initValue();let a=qB;const i=Ne("ratio-event"),l=e=>$N(e,n),c=e=>rO(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),d=()=>!1,u=e.label.getOr("Constrain proportions"),m=t.translate(u),g=jx.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-lock-context-form-size-input","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":m,"data-mce-name":u}},components:[c("lock"),c("unlock")],buttonBehaviours:rd([rp.config({disabled:d}),Gb.config({}),uv.config(t.tooltips.getConfig({tooltipText:m}))])}),p=e=>({dom:{tag:"div",classes:["tox-context-form__group"]},components:e}),h=e=>Gn(e.element,"div.tox-focusable-wrapper").fold(A.none,(e=>(No(e),A.some(!0)))),f=e=>Ux.parts.field({factory:Zx,inputClasses:["tox-textfield","tox-toolbar-textfield","tox-textfield-size"],data:e?s:r,inputBehaviours:rd([rp.config({disabled:d}),Gb.config({}),ud("size-input-toolbar-events",[Wc(xa(),((t,o)=>{Rc(t,i,{isField1:e})}))]),Om.config({mode:"special",onEnter:o,onEscape:h})]),selectOnFocus:!1}),v=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Ag(t.translate(e))]}),x=e=>({dom:{tag:"div",classes:["tox-focusable-wrapper","tox-toolbar-nav-item"]},components:[e],behaviours:rd([Gb.config({}),fd.config({}),Om.config({mode:"special",onEnter:e=>Xn(e.element,"input").fold(A.none,(e=>(No(e),A.some(!0))))})])}),y=x(jx.parts.field1(p([Ux.parts.label(v("Width:")),f(!0)]))),w=x(jx.parts.field2(p([Ux.parts.label(v("Height:")),f(!1)]))),S=ye(b),C=[uA({onBeforeSetup:e=>Xn(e.element,"input").each(No),onSetup:e.onSetup,getApi:l},S),gA({getApi:l},S,n)];return jx.sketch({dom:{tag:"div",classes:["tox-context-form__group"]},components:[y,p([g]),w],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,o)=>{GB(Zh.getValue(e)).each((e=>{a(e).each((e=>{Zh.setValue(t,$B(e))}))}))},onInput:e=>Ic(e,yD),coupledFieldBehaviours:rd([fd.config({}),Om.config({mode:"flow",focusInside:Ei.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-focusable-wrapper"}),rp.config({disabled:d,onDisabled:e=>{jx.getField1(e).bind(Ux.getField).each(rp.disable),jx.getField2(e).bind(Ux.getField).each(rp.disable),jx.getLock(e).each(rp.disable)},onEnabled:e=>{jx.getField1(e).bind(Ux.getField).each(rp.enable),jx.getField2(e).bind(Ux.getField).each(rp.enable),jx.getLock(e).each(rp.enable)}}),yA((()=>t.checkUiComponentContext("mode:design"))),ud("size-input-toolbar-events2",[Wc(i,((e,t)=>{const o=t.event.isField1,n=o?jx.getField1(e):jx.getField2(e),s=o?jx.getField2(e):jx.getField1(e),r=n.map(Zh.getValue).getOr(""),i=s.map(Zh.getValue).getOr("");a=XB(r,i)})),Wc(yD,(t=>e.onInput(l(t)))),...C])])})},YN=(e,t,o)=>Ux.sketch({dom:{tag:"div",classes:["tox-context-form__group"]},components:[...e.toArray(),t],fieldBehaviours:rd([rp.config({disabled:()=>o.checkUiComponentContext("mode:design").shouldDisable,onDisabled:e=>{(e=>{Ho(e.element).each((e=>{Gn(e,'[tabindex="-1"]').each((e=>{No(e)}))}))})(e),Ux.getField(e).each(rp.disable)},onEnabled:e=>{Ux.getField(e).each(rp.enable)}})])}),KN=(e,t,o,n)=>{const s=ye(b),r=e=>GN(e,n),a=e.label.map((e=>Ux.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ag(t.translate(e))]}))),i=Ux.parts.field({factory:Zx,type:"range",inputClasses:["tox-toolbar-slider__input","tox-toolbar-nav-item"],inputAttributes:{min:String(e.min()),max:String(e.max())},data:e.initValue().toString(),fromInputValue:t=>(e=>{const t=parseFloat(e);return isNaN(t)?A.none():A.some(t)})(t).getOr(e.min()),toInputValue:e=>String(e),inputBehaviours:rd([rp.config({disabled:()=>t.checkUiComponentContext("mode:design").shouldDisable}),yA((()=>t.checkUiComponentContext("mode:design"))),Om.config({mode:"special",onEnter:o,onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())}),ud("slider-events",[uA({onSetup:e.onSetup,getApi:r,onBeforeSetup:Om.focusIn},s),gA({getApi:r},s,n),Wc(Ca(),(t=>{e.onInput(r(t))}))])])});return YN(a,i,t)},JN=(e,t,o,n)=>{const s=ye(b),r=e=>GN(e,n),a=e.label.map((e=>Ux.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ag(t.translate(e))]}))),i={...e.placeholder.map((e=>({placeholder:t.translate(e)}))).getOr({})},l=Ux.parts.field({factory:Zx,inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-item"],inputAttributes:i,data:e.initValue(),selectOnFocus:!0,inputBehaviours:rd([rp.config({disabled:()=>t.checkUiComponentContext("mode:design").shouldDisable}),yA((()=>t.checkUiComponentContext("mode:design"))),Om.config({mode:"special",onEnter:o,onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())}),ud("input-events",[uA({onSetup:e.onSetup,getApi:e=>Gn(e.element,".tox-toolbar").bind((e=>Xn(e,"button:enabled"))).fold((()=>GN(e,n)),(t=>GN(e,n,t))),onBeforeSetup:Om.focusIn},s),gA({getApi:r},s,n),Wc(Ca(),(t=>{e.onInput(r(t))}))])])});return YN(a,l,t)},QN=(e,t,o)=>{const n=Xe(),s=pv(e(o,(e=>a.findPrimary(e).orThunk((()=>i.findPrimary(e))).map((e=>(Nc(e),!0)))),n)),r=H(t.commands,(e=>"start"===e.align)),a=qN(s,r.pass,o,n),i=qN(s,r.fail,o,n);return P([{title:A.none(),label:A.none(),items:a.asSpecs()},{title:A.none(),label:A.none(),items:[s.asSpec()]},{title:A.none(),label:A.none(),items:i.asSpecs()}],(e=>e.items.length>0))},ZN=(e,t)=>{switch(e.type){case"contextform":return QN(C(JN,e),e,t);case"contextsliderform":return QN(C(KN,e),e,t);case"contextsizeinputform":return QN(C(XN,e),e,t)}},ez=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,tz=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=So(ct(e.startContainer),e.startOffset).element;return(so(o)?go(o):A.some(o)).filter(no).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Rn();return Is(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Ns(ct(e.getBody()));return Is(o.x+t.left,o.y+t.top,t.width,t.height)}},oz=(e,t,o,n=0)=>{const s=Ms(window),r=Rs(ct(e.getContentAreaContainer())),a=EC(e)||DC(e)||FC(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Is(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ct(e.getContainer()),i=Xn(a,".tox-editor-header").getOr(a),l=Rs(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Rs(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Is(i,c,l,d-c)}},nz={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},sz={maxHeightFunction:kc(),maxWidthFunction:Oc()},rz=e=>"node"===e,az=(e,t,o,n,s)=>{const r=tz(e),a=n.lastElement().exists((e=>yt(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=So(ct(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&yt(n.element,t)})(e,o)?a?xc:pc:a?((e,o)=>{const s=bn(e,"position");mn(e,"position",o);const a=ez(r,Rs(t),-20)&&!n.isReposition()?wc:xc;return s.each((t=>mn(e,"position",t))),a})(t,n.getMode()):("fixed"===n.getMode()?s.y+Rn().top:s.y)+(kn(t)+12)<=r.y?pc:hc},iz=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...az(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>rz(n)?[s(e)]:[];return t?{onLtr:e=>[jl,Pl,Ul,Wl,$l,Gl].concat(r(e)),onRtl:e=>[jl,Ul,Pl,$l,Wl,Gl].concat(r(e))}:{onLtr:e=>[Gl,jl,Wl,Pl,$l,Ul].concat(r(e)),onRtl:e=>[Gl,jl,$l,Ul,Wl,Pl].concat(r(e))}},lz=(e,t)=>{const o=P(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=H(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},cz=(e,t)=>{const o={},n=[],s=[],r={},a={},i=re(e);return V(i,(i=>{const l=e[i];"contextform"===l.type||"contextsliderform"===l.type||"contextsizeinputform"===l.type?((e,i)=>{const l=Tr(_r("ContextForm",AE,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,o)=>{var i;(i=o,_r("ContextToolbar",DE,i)).each((i=>{i.launch.isSome()&&(r["toolbar:"+e]={...o.launch,type:"button",onAction:()=>{t(i)}}),"editor"===o.scope?s.push(i):n.push(i),a[e]=i}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},dz="tox-pop--transition",uz=(e,t,o,n)=>{const s=n.backstage,a=s.shared,i=Jt().deviceType.isTouch,l=Xe(),c=Xe(),d=Xe(),u=(e=>{const t=ye([]),o=Xx.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Xx.getContent(e).each((e=>{yn(e.element,"visibility")})),ss(e.element,YR),yn(e.element,"width")},onHide:()=>{t.set([]),e.onHide()},inlineBehaviours:rd([ud("context-toolbar-events",[Kc(Ta(),((e,t)=>{"width"===t.event.raw.propertyName&&(ss(e.element,YR),yn(e.element,"width"))})),Wc(XR,((t,o)=>{const n=t.element;yn(n,"width");const s=En(n),r=Ho(t.element).isSome();yn(n,"left"),yn(n,"right"),yn(n,"max-width"),Xx.setContent(t,o.event.contents),os(n,YR);const a=En(n);mn(n,"transition","none"),Xx.reposition(t),yn(n,"transition"),mn(n,"width",s+"px"),o.event.focus.fold((()=>{r&&GR(t)}),(o=>{Vo(ko(t.element)).fold((()=>No(o)),(t=>{yt(t,o)||e.focusElement(o)}))})),setTimeout((()=>{mn(t.element,"width",a+"px")}),0)})),Wc(jR,((e,o)=>{Xx.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:Vo(ko(e.element))}]))})),Rc(e,XR,{contents:o.event.forwardContents,focus:A.none()})})),Wc(qR,((o,n)=>{e.onBack(),oe(t.get()).each((e=>{t.set(t.get().slice(0,t.get().length-1)),Rc(o,XR,{contents:Ig(e.bar),focus:e.focus})}))}))]),Om.config({mode:"special",onEscape:o=>oe(t.get()).fold((()=>e.onEscape()),(e=>(Ic(o,qR),A.some(!0))))})]),lazySink:()=>Ae.value(e.sink)});return{sketch:o,inSubtoolbar:()=>t.get().length>0}})({sink:o,onEscape:()=>(e.focus(),gM(e),A.some(!0)),onHide:()=>{gM(e)},onBack:()=>{(e=>{e.dispatch("ContextFormSlideBack")})(e)},focusElement:t=>{e.getBody().contains(t.dom)?e.focus():No(t)}}),m=Fg(u.sketch),g=()=>{const t=d.get().getOr("node"),o=rz(t)?1:0;return oz(e,a,t,o)},p=()=>!(e.removed||i()&&s.isContextMenuOpen()),h=()=>{if(p()){const t=g(),o=He(d.get(),"node")?((e,t)=>t.filter((e=>an(e)&&oo(e))).map(Ns).getOrThunk((()=>tz(e))))(e,l.get()):tz(e);return t.height<=0||!ez(o,t,.01)}return!0},f=()=>{l.clear(),c.clear(),d.clear(),Xx.hide(m)},v=()=>{if(Xx.isOpen(m)){const e=m.element;yn(e,"display"),h()?mn(e,"display","none"):(c.set(0),Xx.reposition(m))}},x=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:rd([Om.config({mode:"acyclic"}),ud("pop-dialog-wrap-events",[Jc((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Om.focusIn(t)))})),Qc((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),y=e=>{const t=S([e]);Rc(m,jR,{forwardContents:x(t)})},w=st((()=>cz(t,y))),S=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...w().formNavigators},i=eC(e)===DS.scrolling?DS.scrolling:DS.default,l=j(L(t,(t=>{return"contexttoolbar"===t.type?((t,o)=>MN(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:","toolbar:"])))(s,(o=t,{...o,launch:o.launch.getOrUndefined(),items:r(o.items)?o.items:L(o.items,BE)})):((e,t)=>ZN(e,t))(t,a.providers);var o})));return oR({type:i,uid:Ne("context-toolbar"),initGroups:l,onEscape:A.none,cyclicKeying:!0,providers:a.providers})},C=(t,n)=>{if(O.cancel(),!p())return;const s=S(t),r=t[0].position,u=((t,n)=>{const s="node"===t?a.anchors.node(n):a.anchors.cursor(),r=((e,t,o,n)=>"line"===t?{bubble:wl(12,0,nz),layouts:{onLtr:()=>[ql],onRtl:()=>[Xl]},overrides:sz}:{bubble:wl(0,12,nz,1/12),layouts:iz(e,o,n,t),overrides:sz})(e,t,i(),{lastElement:l.get,isReposition:()=>He(c.get(),0),getMode:()=>nb.getMode(o)});return Le(s,r)})(r,n);d.set(r),c.set(1);const f=m.element;yn(f,"display"),(e=>He(Ue(e,l.get(),yt),!0))(n)||(ss(f,dz),nb.reset(o,m)),Xx.showWithinBounds(m,x(s),{anchor:u,transition:{classes:[dz],mode:"placement"}},(()=>A.some(g()))),n.fold(l.clear,l.set),h()&&mn(f,"display","none")};let k=!1;const O=nt((()=>{!e.hasFocus()||e.removed||k||(rs(m.element,dz)?O.throttle():((e,t)=>{const o=ct(t.getBody()),n=e=>yt(e,o),s=ct(t.selection.getNode());return(e=>!n(e)&&!wt(o,e))(s)?A.none():((e,t,o)=>{const n=lz(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=lz(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>P(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=L(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():fs(t,(e=>{if(no(e)){const{contextToolbars:t,contextForms:n}=lz(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>$(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>P(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(w(),e).fold(f,(e=>{C(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",f),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",v),e.on("click focus SetContent",O.throttle),e.on("keyup",(e=>{var t;((t=e.keyCode)!==fN.ENTER&&t!==fN.SPACEBAR||!u.inSubtoolbar())&&O.throttle()})),e.on(WN,f),e.on("contexttoolbar-show",(t=>{const o=w();fe(o.lookupTable,t.toolbarKey).each((o=>{C([o],$e(t.target!==e,t.target)),GR(m)}))})),e.on("focusout",(t=>{IS.setEditorTimeout(e,(()=>{Ho(o.element).isNone()&&Ho(m.element).isNone()&&!e.hasFocus()&&f()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&f()})),e.on("DisabledStateChange",(e=>{e.state&&f()})),e.on("ExecCommand",(({command:e})=>{"toggleview"===e.toLowerCase()&&f()})),e.on("AfterProgressState",(t=>{t.state?f():e.hasFocus()&&O.throttle()})),e.on("dragstart",(()=>{k=!0})),e.on("dragend drop",(()=>{k=!1})),e.on("NodeChange",(e=>{u.inSubtoolbar()?(mn(m.element,"transition","none"),v(),yn(m.element,"transition")):Ho(m.element).fold(O.throttle,b)}))}))},mz=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Xe();return L(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(He(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},gz=e=>{mz(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:MC,hash:e=>(e=>Es(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:hM(e),onMenuSetup:hM(e)}))(e)),(e=>A.from(QS(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ct(e.selection.getNode());return bs(t,(e=>A.some(e).filter(no).bind((e=>Ko(e,"lang").map((t=>({code:t,customCode:Ko(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=qe();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),pM(o.clear,hM(e)(t))},onMenuSetup:hM(e)}))))(e).each((t=>mz(e,t)))},pz=e=>bM(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),hz=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),pM((()=>e.off("PastePlainTextToggle",n)),hM(e)(o))},fz=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},bz=e=>{(e=>{(e=>{ZD.each([{name:"bold",text:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:fM(e,t.name),onAction:fz(e,t.name),shortcut:t.shortcut})}));for(let t=1;t<=6;t++){const o="h"+t,n=`Access+${t}`;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:fM(e,o),onAction:fz(e,o),shortcut:n})}})(e),(e=>{ZD.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy",context:"any"},{name:"help",text:"Help",action:"mceHelp",icon:"help",shortcut:"Alt+0",context:"any"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A",context:"any"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print",shortcut:"Meta+P",context:"any"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:xM(e,t.action),shortcut:t.shortcut,context:t.context})})),ZD.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:hM(e),onAction:xM(e,t.action)})}))})(e),(e=>{ZD.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:xM(e,t.action),onSetup:fM(e,t.name)})}))})(e)})(e),(e=>{ZD.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C",context:"any"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A",context:"any"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P",context:"any"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:xM(e,t.action),context:t.context})})),ZD.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:hM(e),onAction:xM(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:hM(e),onAction:fz(e,"code")})})(e)},vz=(e,t)=>bM(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),xz=e=>bM(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),yz=(e,t)=>{(e=>{V([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:xM(e,t.cmd),onSetup:fM(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:hM(e),onAction:xM(e,"JustifyNone")})})(e),bz(e),((e,t)=>{((e,t)=>{const o=QR(t,rN(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:hM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=QR(t,hN(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:hM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=QR(t,ON(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:hM(e,(()=>n.getStyleItems().length>0)),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=QR(t,cN(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:hM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=QR(t,CN(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:hM(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:vz(e,"hasUndo"),onAction:xM(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:vz(e,"hasRedo"),onAction:xM(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:vz(e,"hasUndo"),onAction:xM(e,"undo"),shortcut:"Meta+Z"}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:vz(e,"hasRedo"),onAction:xM(e,"redo"),shortcut:"Meta+Y"})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=IM(e),o=RM(e),n=ye(t),s=ye(o);GM(e,"forecolor","forecolor",n),GM(e,"backcolor","hilitecolor",s),jM(e,"forecolor","forecolor","Text color",n),jM(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:xM(e,"mceToggleVisualAid"),context:"any"})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:xz(e),onAction:xM(e,"mceToggleVisualAid"),context:"any"})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:pz(e),onAction:xM(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:hM(e,(()=>e.queryCommandState("indent"))),onAction:xM(e,"indent")})})(e)})(e),gz(e),(e=>{const t=ye(wC(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:hz(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:hz(e,t)})})(e),(e=>{e.ui.registry.addContext("editable",(()=>e.selection.isEditable())),e.ui.registry.addContext("mode",(t=>e.mode.get()===t)),e.ui.registry.addContext("any",E),e.ui.registry.addContext("formatting",(t=>e.formatter.canApply(t))),e.ui.registry.addContext("insert",(t=>e.schema.isValidChild(e.selection.getNode().tagName,t)))})(e)},wz=e=>r(e)?e.split(/[ ,]/):e,Sz=e=>t=>t.options.get(e),Cz=Sz("contextmenu_never_use_native"),kz=Sz("contextmenu_avoid_overlap"),Oz=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:P(o,(e=>be(t,e)))},_z=(e,t)=>({type:"makeshift",x:e,y:t}),Tz=e=>"longpress"===e.type||0===e.type.indexOf("touch"),Ez=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(Tz(e)){const t=e.touches[0];return _z(t.pageX,t.pageY)}return _z(e.pageX,e.pageY)})(t):((e,t)=>{const o=RS.DOM.getPos(e);return((e,t,o)=>_z(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(Tz(e)){const t=e.touches[0];return _z(t.clientX,t.clientY)}return _z(e.clientX,e.clientY)})(t)):Az(e),Az=e=>({type:"selection",root:ct(e.selection.getNode())}),Mz=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ct(e.selection.getNode())),root:ct(e.getBody())}))(e);case"point":return Ez(e,t);case"selection":return Az(e)}},Dz=(e,t,o,n,s,r)=>{const a=o(),i=Mz(e,t,r);VB(a,dO.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),Xx.showMenuAt(s,{anchor:i},{menu:{markers:OO("normal")},data:e})}))},Bz={onLtr:()=>[jl,Pl,Ul,Wl,$l,Gl,pc,hc,gc,uc,mc,dc],onRtl:()=>[jl,Ul,Pl,$l,Wl,Gl,pc,hc,mc,dc,gc,uc]},Fz={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},Iz=(e,t,o,n,s,r)=>{const a=Jt(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=Mz(e,t,o);return{bubble:wl(0,"point"===o?12:0,Fz),layouts:Bz,overrides:{maxWidthFunction:Oc(),maxHeightFunction:kc()},...n}})(e,t,r);VB(o,dO.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?Ac.HighlightMenuAndItem:Ac.HighlightNone;Xx.showMenuWithinBounds(s,{anchor:i},{menu:{markers:OO("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(oz(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(WN)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{IS.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Os(e.getWin(),Ss.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},Rz=e=>r(e)?"|"===e:"separator"===e.type,Nz={type:"separator"},zz=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return Nz;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:L(t,zz)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},Lz=(e,t)=>{if(0===t.length)return e;const o=oe(e).filter((e=>!Rz(e))).fold((()=>[]),(e=>[Nz]));return e.concat(o).concat(t).concat([Nz])},Vz=(e,t)=>!(e=>"longpress"===e.type||be(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),Hz=(e,t)=>Vz(e,t)?e.selection.getStart(!0):t.target,Pz=(e,t,o)=>{const n=Jt().deviceType.isTouch,s=Fg(Xx.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:rd([ud("dismissContextMenu",[Wc(Ya(),((t,o)=>{wb.close(t),e.focus()}))])])})),a=()=>Xx.hide(s),i=t=>{if(Cz(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!Cz(e))(e,t)||(e=>0===Oz(e).length)(e))return;const a=((e,t)=>{const o=kz(e),n=Vz(e,t)?"selection":"point";if(tt(o)){const s=Hz(e,t);return ps(ct(s),o)?"node":n}return n})(e,t);(n()?Iz:Dz)(e,t,(()=>{const o=Hz(e,t),n=e.ui.registry.getAll(),s=Oz(e);return((e,t,o)=>{const n=W(t,((t,n)=>fe(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&tt(et(n)))return Lz(t,n.split(" "));if(l(n)&&n.length>0){const e=L(n,zz);return Lz(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&Rz(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},Uz=(e,t,o,n,s,r)=>e.fold((()=>Dh.snap({sensor:Wp(o-20,n-20),range:Dn(s,r),output:Wp(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return Dh.snap({sensor:Wp(s,r),range:Dn(40,40),output:Wp(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),Wz=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>yt(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),$z=e=>pv(Tv.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:rd([Dh.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),mv.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),Gz=(e,t)=>{const o=ye([]),n=ye([]),s=ye(!1),r=Xe(),a=Xe(),i=e=>{const o=Ns(e);return Uz(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Ns(e);return Uz(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=Wz((()=>L(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=Wz((()=>L(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=$z(c),m=$z(d),g=Fg(u.asSpec()),p=Fg(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);Dh.snapTo(t,r),((t,o)=>{const n=o.dom.getBoundingClientRect();yn(t.element,"display");const r=mo(ct(e.getBody())).dom.innerHeight,a=n[s]<0,i=((e,t)=>e[s]>t)(n,r);(a||i)&&mn(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");if(Jt().deviceType.isTouch()){const i=e=>L(e,ct);e.on("TableSelectionChange",(e=>{s.get()||(Nm(t,g),Nm(t,p),s.set(!0));const l=ct(e.start),c=ct(e.finish);r.set(l),a.set(c),A.from(e.otherCells).each((e=>{o.set(i(e.upOrLeftCells)),n.set(i(e.downOrRightCells)),f(l),b(c)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Vm(g),Vm(p),s.set(!1)),r.clear(),a.clear()}))}},jz=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:rd([Om.config({mode:"flow",selector:"div[role=button]"}),rp.config({disabled:o.isDisabled}),yA((()=>o.checkUiComponentContext("any"))),Gb.config({}),ng.config({}),ud("elementPathEvents",[Jc(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Om.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=mM(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?W(r,((t,n,r)=>{const a=((t,n,s)=>Tv.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s}},components:[Ag(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:rd([uv.config({...o.tooltips.getConfig({tooltipText:o.translate(["Select the {0} element",n.nodeName.toLowerCase()]),onShow:(e,t)=>{((e,t)=>{const o=A.from(Yo(e,"id")).getOrThunk((()=>{const e=Ne("aria");return qo(t,"id",e),e}));qo(e,"aria-describedby",o)})(e.element,t.element)},onHide:e=>{var t;t=e.element,Qo(t,"aria-describedby")}})}),lA(o.isDisabled),yA((()=>o.checkUiComponentContext("any")))])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[Ag(` ${s} `)]},a])}),[]):[];ng.set(t,a)}))}))])]),components:[]}};var qz;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(qz||(qz={}));const Xz=e=>{const t=ct(e.getContainer());return{height:kn(t),width:En(t)}},Yz=(e,t,o)=>{const n=ct(e.getContainer()),s=((e,t,o,n)=>({height:NN(n.height+t.top,jS(e),XS(e)),width:o===qz.Both?NN(n.width+t.left,GS(e),qS(e)):n.width}))(e,t,o,Xz(e));return ie(s,((e,t)=>{h(e)&&mn(n,t,RN(e))})),(e=>{e.dispatch("ResizeEditor")})(e),s},Kz=(e,t,o)=>{qo(e.element,"aria-valuetext",((e,t)=>t===qz.Both?Kk.translate(["Editor's height: {0} pixels, Editor's width: {1} pixels",e.height,e.width]):Kk.translate(["Editor's height: {0} pixels",e.height]))(t,o))},Jz=(e,t,o,n,s)=>{const r=Dn(20*n,20*s),a=Yz(e,r,o);return Kz(t,a,o),A.some(!0)},Qz=(e,t)=>{const o=()=>{const o=[],n=kC(e),s=vC(e),r=xC(e)||e.hasPlugin("wordcount");return s&&o.push(jz(e,{},t)),n&&o.push((()=>{const e=TA("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[Ag(Kk.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>ng.set(e,[Ag(t.translate(["{0} "+n,o[n]]))]);return Tv.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:rd([lA(t.isDisabled),yA((()=>t.checkUiComponentContext("any"))),Gb.config({}),ng.config({}),Zh.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),ud("wordcount-events",[ed((e=>{const t=Zh.getValue(e),n="words"===t.mode?"characters":"words";Zh.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Jc((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=Zh.getValue(t);Zh.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[Na()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),xC(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v7",rel:"noopener",target:"_blank","aria-label":e.translate(["Build with {0}","TinyMCE"])},innerHtml:e.translate(["Build with {0}",'<svg height="16" viewBox="0 0 80 16" width="80" xmlns="http://www.w3.org/2000/svg"><g opacity=".8"><path d="m80 3.537v-2.202h-7.976v11.585h7.976v-2.25h-5.474v-2.621h4.812v-2.069h-4.812v-2.443zm-10.647 6.929c-.493.217-1.13.337-1.864.337s-1.276-.156-1.805-.47a3.732 3.732 0 0 1 -1.3-1.298c-.324-.554-.48-1.191-.48-1.877s.156-1.335.48-1.877a3.635 3.635 0 0 1 1.3-1.299 3.466 3.466 0 0 1 1.805-.481c.65 0 .914.06 1.263.18.36.12.698.277.986.47.289.192.578.384.842.6l.12.085v-2.586l-.023-.024c-.385-.35-.855-.614-1.384-.818-.53-.205-1.155-.313-1.877-.313-.721 0-1.6.144-2.333.445a5.773 5.773 0 0 0 -1.937 1.251 5.929 5.929 0 0 0 -1.324 1.9c-.324.735-.48 1.565-.48 2.455s.156 1.72.48 2.454c.325.734.758 1.383 1.324 1.913.553.53 1.215.938 1.937 1.25a6.286 6.286 0 0 0 2.333.434c.819 0 1.384-.108 1.961-.313.59-.216 1.083-.505 1.468-.866l.024-.024v-2.49l-.12.096c-.41.337-.878.626-1.396.866zm-14.869-4.15-4.8-5.04-.024-.025h-.902v11.67h2.502v-6.847l2.827 3.08.385.409.397-.41 2.791-3.067v6.845h2.502v-11.679h-.902l-4.788 5.052z"/><path clip-rule="evenodd" d="m15.543 5.137c0-3.032-2.466-5.113-4.957-5.137-.36 0-.745.024-1.094.096-.157.024-3.85.758-3.85.758-3.032.602-4.62 2.466-4.704 4.788-.024.89-.024 4.27-.024 4.27.036 3.165 2.406 5.138 5.017 5.126.337 0 1.119-.109 1.287-.145.144-.024.385-.084.746-.144.661-.12 1.684-.325 3.067-.602 2.37-.409 4.103-2.009 4.44-4.33.156-1.023.084-4.692.084-4.692zm-3.213 3.308-2.346.457v2.31l-5.859 1.143v-5.75l2.346-.458v3.441l3.513-.686v-3.44l-3.513.685v-2.297l5.859-1.143v5.75zm20.09-3.296-.083-1.023h-2.13v8.794h2.346v-4.884c0-1.107.95-1.985 2.057-1.997 1.095 0 1.901.89 1.901 1.997v4.884h2.346v-5.245c-.012-2.105-1.588-3.777-3.67-3.765a3.764 3.764 0 0 0 -2.778 1.25l.012-.011zm-6.014-4.102 2.346-.458v2.298l-2.346.457z" fill-rule="evenodd"/><path d="m28.752 4.126h-2.346v8.794h2.346z"/><path clip-rule="evenodd" d="m43.777 15.483 4.043-11.357h-2.418l-1.54 4.355-.445 1.324-.36-1.324-1.54-4.355h-2.418l3.151 8.794-1.083 3.08zm-21.028-5.51c0 .722.541 1.034.878 1.034s.638-.048.95-.144l.518 1.708c-.217.145-.879.518-2.13.518a2.565 2.565 0 0 1 -2.562-2.587c-.024-1.082-.024-2.49 0-4.21h-1.54v-2.142h1.54v-1.912l2.346-.458v2.37h2.201v2.142h-2.2v3.693-.012z" fill-rule="evenodd"/></g></svg>\n'.trim()])},behaviours:rd([fd.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=yC(e);return!1===t?qz.None:"both"===t?qz.Both:qz.Vertical})(e);if(o===qz.None)return A.none();const n=o===qz.Both?Kk.translate("Press the arrow keys to resize the editor."):Kk.translate("Press the Up and Down arrow keys to resize the editor."),s=o===qz.Both?"tox-statusbar__resize-cursor-both":"tox-statusbar__resize-cursor-default";return A.some(rO("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle",s],attributes:{"aria-label":t.translate(n),"data-mce-name":"resize-handle",role:"separator"},behaviours:[Dh.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>{const r=Yz(e,s,o);Kz(t,r,o)},blockerClass:"tox-blocker"}),Om.config({mode:"special",onLeft:t=>Jz(e,t,o,-1,0),onRight:t=>Jz(e,t,o,1,0),onUp:t=>Jz(e,t,o,0,-1),onDown:t=>Jz(e,t,o,0,1)}),Gb.config({}),fd.config({}),uv.config(t.tooltips.getConfig({tooltipText:t.translate("Resize")})),ud("set-aria-valuetext",[Jc((t=>{const n=()=>{Kz(t,Xz(e),o)};e._skinLoaded?n():e.once("SkinLoaded",n)}))])],eventOrder:{[qa()]:["add-focusable","set-aria-valuetext"]}},t.icons))})(e,t);return n.concat(s.toArray())})()}},Zz=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),eL=(e,t)=>{const o=e.inline,n=o?UN:IN,s=VC(e)?VI:AI,r=(()=>{const e=Xe(),t=Xe(),o=Xe();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>yt(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=Xe(),i=Xe(),l=Xe(),c=Jt().deviceType.isTouch()?["tox-platform-touch"]:[],d=IC(e),u=eC(e),m=pv({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=pv({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(CR.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",CR.getToolbar),v=r.lazyGetInOuterOrDie("throbber",CR.getThrobber),x=((e,t,o,n)=>{const s=ye(!1),r=(e=>{const t=ye(IC(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Kk.translate,isDisabled:()=>!t.ui.isEnabled(),getOption:t.options.get,tooltips:QF(e.dialog),checkUiComponentContext:e=>{if(US(t))return{contextType:"disabled",shouldDisable:!0};const[o,n=""]=e.split(":"),s=t.ui.registry.getAll().contexts;return{contextType:o,shouldDisable:!fe(s,o).fold((()=>fe(s,"mode").map((e=>e("design"))).getOr(!1)),(e=>"!"===n.charAt(0)?!e(n.slice(1)):e(n)))}}},i=CI(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=ye([]),s=ye([]),r=ye(!1);return e.on("PreInit",(s=>{const r=YF(e),a=JF(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=JF(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:HF(e),hasCustomColors:PF(e),getColors:UF(e),getColorCols:WF(e)}))(t),d=(e=>({isDraggableModal:$F(e)}))(t),u={shared:{providers:a,anchors:VF(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m=e=>A.none(),g={...u,shared:{...u.shared,interpreter:e=>BF(e,{},g,m),getSink:e.popup}},p={...u,shared:{...u.shared,interpreter:e=>BF(e,{},p,m),getSink:e.dialog}};return{popup:g,dialog:p}})({popup:()=>Ae.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>Ae.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),y=()=>{const t=(()=>{const t={attributes:{[Tc]:d?_c.BottomToTop:_c.TopToBottom}},o=CR.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:x.popup,onEscape:()=>{e.focus()}}),n=CR.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:x.popup.shared.getSink,providers:x.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=CR.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:x.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=FC(e),a=DC(e),i=EC(e),l=CC(e),c=r||a||i,g=[(h=l,CR.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]},promotionLink:h})),o];var h;return CR.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(c?[]:["tox-editor-header--empty"]),...t},components:j([i?g:[],r?[s]:a?[n]:[],NC(e)?[]:[m.asSpec()]]),sticky:VC(e),editor:e,sharedBackstage:x.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[CR.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),CR.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=CR.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:x.popup}),r=CR.parts.viewWrapper({backstage:x.popup}),i=bC(e)&&!o?A.some(Qz(e,x.popup.shared.providers)):A.none(),l=j([d?[]:[t],o?[]:[n],d?[t]:[]]),h=CR.parts.editorContainer({components:j([l,o?[]:[g.asSpec()]])}),f=LC(e),v={role:"application",...Kk.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},y=Fg(CR.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r,...i.toArray()],s],behaviours:rd([yA((()=>x.popup.shared.providers.checkUiComponentContext("any"))),rp.config({disableClass:"tox-tinymce--disabled"}),Om.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=kv(y);return a.set(w),{mothership:w,outerContainer:y}},w=t=>{const o=RN((e=>(e=>{const t=((e,t)=>{if("number"==typeof t)return A.from(t);const o=/^([0-9.]+)(pt|em|px)$/.exec(t.trim());if(o){const t=o[2],n=Number.parseFloat(o[1]);if(Number.isNaN(n)||n<0)return A.none();if("em"===t)return A.from(n*Number.parseFloat(window.getComputedStyle(e.dom).fontSize));if("pt"===t)return A.from(.75*n);if("px"===t)return A.from(n)}return A.none()})(ct(e.targetElm),WS(e)),o=jS(e),n=XS(e);return t.map((e=>NN(e,o,n)))})(e).getOr(WS(e)))(e)),n=RN((e=>zN(e).getOr($S(e)))(e));return e.inline||(xn("div","width",n)&&mn(t.element,"width",n),xn("div","height",o)?mn(t.element,"height",o):mn(t.element,"height","400px")),o},S=t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;le(tC(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=BC(e),h={menuItems:l,menus:PC(e),menubar:iC(e),toolbar:g.getOrThunk((()=>lC(e))),allowToolbarGroups:u===DS.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{CR.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{CR.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t,o)=>{(null==t?void 0:t.skipFocus)?(console.warn(kI.skipFocus,(new Error).stack),CR.toggleToolbarDrawerWithoutFocusing(f)):(null==o?void 0:o.skip_focus)?CR.toggleToolbarDrawerWithoutFocusing(f):CR.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>CR.isToolbarDrawerToggled(f))),e.on("blur",(()=>{eC(e)===DS.floating&&CR.isToolbarDrawerToggled(f)&&CR.toggleToolbarDrawerWithoutFocusing(f)})),((e,t,o)=>{const n=(e,n)=>{V([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{V([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Td(),{target:e.target}),a=e=>{s(_d(),{closedTooltip:()=>{e.preventDefault()}})},i=Ro(),l=Bo(i,"touchstart",r),c=Bo(i,"touchmove",(e=>n(Wa(),e))),d=Bo(i,"touchend",(e=>n($a(),e))),u=Bo(i,"mousedown",r),m=Bo(i,"mouseup",(e=>{0===e.raw.button&&s(Ad(),{target:e.target})})),g=e=>s(Td(),{target:ct(e.target)}),p=e=>{0===e.button&&s(Ad(),{target:ct(e.target)})},h=()=>{V(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},f=e=>n(Ga(),Io(e)),b=e=>{s(Ed(),{}),n(ja(),Io(e))},v=ko(ct(e.getElement())),x=Fo(v,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=qC(e,t.element).map((e=>[e.element,...e.others])).getOr([]);R(s,(e=>yt(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Za(),o))}}))})),y=()=>s(Ed(),{}),w=t=>{t.state&&s(Td(),{target:ct(e.getContainer())})},S=e=>{s(Td(),{target:ct(e.relatedTarget.getContainer())})},C=t=>e.dispatch("focusin",t),k=t=>e.dispatch("focusout",t);e.on("PostRender",(()=>{e.on("click",g),e.on("tap",g),e.on("mouseup",p),e.on("mousedown",h),e.on("ScrollWindow",f),e.on("ResizeWindow",b),e.on("ResizeEditor",y),e.on("AfterProgressState",w),e.on("DismissPopups",S),e.on("CloseActiveTooltips",a),V([t,...o],(e=>{e.element.dom.addEventListener("focusin",C),e.element.dom.addEventListener("focusout",k)}))})),e.on("remove",(()=>{e.off("click",g),e.off("tap",g),e.off("mouseup",p),e.off("mousedown",h),e.off("ScrollWindow",f),e.off("ResizeWindow",b),e.off("ResizeEditor",y),e.off("AfterProgressState",w),e.off("DismissPopups",S),e.off("CloseActiveTooltips",a),V([t,...o],(e=>{e.element.dom.removeEventListener("focusin",C),e.element.dom.removeEventListener("focusout",k)})),u.unbind(),l.unbind(),c.unbind(),d.unbind(),m.unbind(),x.unbind()})),e.on("detach",(()=>{V([t,...o],$m),V([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,x.popup.shared,p),yz(e,x.popup),Pz(e,x.popup.shared.getSink,x.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();V(re(t),(o=>{const n=t[o],s=()=>He(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}},context:"any"})}))})(e),YI(e,v,x.popup.shared),uz(e,c,r.sink,{backstage:x.popup}),Gz(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,x.popup,b)};return{popups:{backstage:x.popup,getMothership:()=>Zz("popups",l)},dialogs:{backstage:x.dialog,getMothership:()=>Zz("dialogs",i)},renderUI:()=>{const o=y(),n=(()=>{const t=zC(e),o=yt(ln(),t)&&"grid"===hn(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...Kk.isRtl()?{dir:"rtl"}:{}}},behaviours:rd([nb.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Hc([Wc(ja(),(e=>{mn(e.element,"width",document.body.clientWidth+"px")}))])},a=Fg(Le(n,o?r:{})),l=kv(a);return i.set(l),{sink:a,mothership:l}})(),a=HC(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...Kk.isRtl()?{dir:"rtl"}:{}}},behaviours:rd([nb.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=Fg(e),n=kv(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(n);r.dialogUi.set(n),r.popupUi.set(a),r.mainUi.set(o);const d={popupUi:a,dialogUi:n,mainUi:o,uiMotherships:r.getUiMotherships()};return S(d)}}},tL=e=>{const t=[],o={};return ie(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?Ae.error(t):Ae.value(o)},oL=(e,t,o,n)=>{const s=pv(Ax.sketch((s=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:L(e.items,(e=>MF(s,e,t,o,n)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[s.asSpec()]}],behaviours:rd([Om.config({mode:"acyclic",useTabstopAt:k(lB)}),(r=s,Pg.config({find:r.getOpt})),ND(s,{postprocess:e=>tL(e).fold((e=>(console.error(e),{})),w)}),ud("dialog-body-panel",[Wc(xa(),((e,t)=>{e.getSystem().broadcastOn([pB],{newFocus:A.some(t.event.target)})}))])])};var r},nL=(e,t)=>{mn(e,"height",t+"px"),mn(e,"flex-basis",t+"px")},sL=(e,t,o)=>{Gn(e,'[role="dialog"]').each((e=>{Xn(e,'[role="tablist"]').each((n=>{o.get().map((o=>(mn(t,"height","0"),mn(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=uo(e).dom,s=Gn(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===hn(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=kn(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+En(o)?Math.max(kn(o),a):a,l=parseInt(hn(e,"margin-top"),10)||0,c=parseInt(hn(e,"margin-bottom"),10)||0;return r-(kn(e)+l+c-i)})(e,t,n))))).each((e=>{nL(t,e)}))}))}))},rL=e=>Xn(e,'[role="tabpanel"]'),aL="send-data-to-section",iL="send-data-to-view",lL=(e,t,o,n)=>{const s=ye({}),r=e=>{const t=Zh.getValue(e),o=tL(t).getOr({}),n=s.get(),r=Le(n,o);s.set(r)},a=e=>{const t=s.get();Zh.setValue(e,t)},i=ye(null),l=L(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[Ag(o.shared.providers.translate(e.title))],view:()=>[Ax.sketch((s=>({dom:{tag:"div",classes:["tox-form"]},components:L(e.items,(e=>MF(s,e,t,o,n))),formBehaviours:rd([Om.config({mode:"acyclic",useTabstopAt:k(lB)}),ud("TabView.form.events",[Jc(a),Qc(r)]),ab.config({channels:la([{key:aL,value:{onReceive:r}},{key:iL,value:{onReceive:a}}])})])})))]}))),c=(e=>{const t=Xe(),o=[Jc((o=>{const n=o.element;rL(n).each((s=>{mn(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>L(e,((n,s)=>{ng.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return ng.set(o,[]),r.height})))(e,s,o),r=(e=>te(Z(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),sL(n,s,t),yn(s,"visibility"),((e,t)=>{te(e).each((e=>CS.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{sL(n,s,t)}))}))})),Wc(ja(),(e=>{const o=e.element;rL(o).each((e=>{sL(o,e,t)}))})),Wc(ED,((e,o)=>{const n=e.element;rL(n).each((e=>{const o=Vo(ko(e));mn(e,"visibility","hidden");const s=bn(e,"height").map((e=>parseInt(e,10)));yn(e,"height"),yn(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),sL(n,e,t)):s.each((t=>{nL(e,t)})),yn(e,"visibility"),o.each(No)}))}))];return{extraEvents:o,selectFirst:!1}})(l);return CS.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=Zh.getValue(t);Rc(e,TD,{name:n,oldName:i.get()}),i.set(n)},tabs:l,components:[CS.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[bS.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:rd([Gb.config({})])}),CS.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:c.selectFirst,tabSectionBehaviours:rd([ud("tabpanel",c.extraEvents),Om.config({mode:"acyclic"}),Pg.config({find:e=>te(CS.getViewItems(e))}),zD(A.none(),(e=>(e.getSystem().broadcastOn([aL],{}),s.get())),((e,t)=>{s.set(t),e.getSystem().broadcastOn([iL],{})}))])})},cL=(e,t,o,n,s,r)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:rd([ID(0),db.config({channel:`${uB}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[lL(t,e.initialData,n,r)]:[oL(t,e.initialData,n,r)]},initialData:e})])}),dL=zS.deviceType.isTouch(),uL=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),mL=(e,t)=>sy.parts.close(Tv.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:rd([Gb.config({})])})),gL=()=>sy.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),pL=(e,t)=>sy.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:gv(`<p>${Yk(t.translate(e))}</p>`)}]}]}),hL=e=>sy.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),fL=(e,t)=>[ch.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),ch.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],bL=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return sy.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!lB(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:gv(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:dL?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:rd([fd.config({}),ud("dialog-events",e.dialogEvents.concat([Kc(xa(),((e,t)=>{Lg.isBlocked(e)||Om.focusIn(e)})),Wc(Ja(),((e,t)=>{e.getSystem().broadcastOn([pB],{newFocus:t.event.newFocus})}))])),ud("scroll-lock",[Jc((()=>{os(ln(),s)})),Qc((()=>{ss(ln(),s)}))]),...e.extraBehaviours]),eventOrder:{[Na()]:["dialog-events"],[qa()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Xa()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},vL=e=>Tv.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),"data-mce-name":"close"}},buttonBehaviours:rd([Gb.config({}),uv.config(e.tooltips.getConfig({tooltipText:e.translate("Close")}))]),components:[rO("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Ic(e,SD)}}),xL=(e,t,o,n)=>({dom:{tag:"h1",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:rd([db.config({channel:`${dB}-${t}`,initialData:e,renderComponents:e=>[Ag(n.translate(e.title))]})])}),yL=()=>({dom:gv('<div class="tox-dialog__draghandle"></div>')}),wL=(e,t,o)=>((e,t,o)=>{const n=sy.parts.title(xL(e,t,A.none(),o)),s=sy.parts.draghandle(yL()),r=sy.parts.close(vL(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return ch.sketch({dom:gv('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),SL=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:gv('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),CL=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=Xn(e().element,".tox-dialog__header").map((e=>kn(e)));sy.setBusy(e(),((e,s)=>SL(o.message,s,t,n)))},onUnblock:()=>{sy.setIdle(e())}}),kL="tox-dialog--fullscreen",OL="tox-dialog--width-lg",_L="tox-dialog--width-md",TL=e=>{switch(e){case"large":return A.some(OL);case"medium":return A.some(_L);default:return A.none()}},EL=(e,t)=>{const o=ct(t.element.dom);rs(o,kL)||(is(o,[OL,_L]),TL(e).each((e=>os(o,e))))},AL=(e,t)=>{const o=ct(e.element.dom),n=ls(o),s=$(n,(e=>e===OL||e===_L)).or(TL(t));((e,t)=>{V(t,(t=>{((e,t)=>{const o=Qn(e)?e.dom.classList.toggle(t):((e,t)=>I(Zn(e),t)?ts(e,t):es(e,t))(e,t);ns(e)})(e,t)}))})(o,[kL,...s.toArray()])},ML=(e,t,o)=>Fg(bL({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[VD({}),...e.extraBehaviours],onEscape:e=>{Ic(e,SD)},dialogEvents:t,eventOrder:{[Ra()]:[db.name(),ab.name()],[qa()]:["scroll-lock",db.name(),"messages","dialog-events","alloy.base.behaviour"],[Xa()]:["alloy.base.behaviour","dialog-events","messages",db.name(),"scroll-lock"]}})),DL=(e,t={})=>L(e,(e=>"menu"===e.type?(e=>{const o=L(e.items,(e=>{const o=fe(t,e.name).getOr(ye(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),BL=e=>W(e,((e,t)=>"menu"===t.type?W(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),FL=(e,t)=>[qc(xa(),iB),e(wD,((e,o,n,s)=>{Lo(s.element)&&Vo(ko(s.element)).each(zo),t.onClose(),o.onClose()})),e(SD,((e,t,o,n)=>{t.onCancel(e),Ic(n,wD)})),Wc(_D,((e,o)=>t.onUnblock())),Wc(OD,((e,o)=>t.onBlock(o.event)))],IL=(e,t,o)=>{const n=(t,o)=>Wc(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{db.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...FL(n,t),n(kD,((e,t)=>t.onSubmit(e))),n(xD,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(CD,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?Om.focusIn(s):void 0,a=e=>Jo(e,"disabled")||Ko(e,"aria-disabled").exists((e=>"true"===e)),i=ko(s.element),l=Vo(i);t.onAction(e,{name:n.name,value:n.value}),Vo(i).fold(r,(e=>{a(e)||l.exists((t=>wt(e,t)&&a(t)))?r():o().toOptional().filter((t=>!wt(t.element,e))).each(r)}))})),n(TD,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Qc((t=>{const o=e();Zh.setValue(t,o.getData())}))]},RL=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=H(o,(e=>"start"===e.align)),s=(e,t)=>ch.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:L(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},NL=(e,t,o)=>({dom:gv('<div class="tox-dialog__footer"></div>'),components:[],behaviours:rd([db.config({channel:`${mB}-${t}`,initialData:e,updateState:(e,t)=>{const n=L(t.buttons,(e=>{const t=pv(((e,t)=>fF(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>$(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:RL})])}),zL=(e,t,o)=>sy.parts.footer(NL(e,t,o)),LL=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=Pg.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return Ax.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>db.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},VL=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...Zh.getValue(n),...le(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=Le(r,t),i=((e,t)=>{const o=e.getRoot();return db.getState(o).get().map((e=>Tr(_r("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();Zh.setValue(l,i),ie(o,((e,t)=>{be(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{LL(e,t).each(o?rp.enable:rp.disable)},focus:t=>{LL(e,t).each(fd.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Rc(t,OD,{message:e})}))},unblock:()=>{n((e=>{Ic(e,_D)}))},showTab:t=>{n((o=>{const n=e.getBody();db.getState(n).get().exists((e=>e.isTabPanel()))&&Pg.getCurrent(n).each((e=>{CS.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=DL(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${cB}-${a}`],i),n.getSystem().broadcastOn([`${dB}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${uB}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${mB}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Ic(e,wD)}))},toggleFullscreen:e.toggleFullscreen};return s},HL=(e,t,o,n=!1,s)=>{const r=Ne("dialog"),a=Ne("dialog-label"),i=Ne("dialog-content"),l=e.internalDialog,c=ye(l.size),d=TL(c.get()).toArray(),u=pv(((e,t,o,n)=>ch.sketch({dom:gv('<div class="tox-dialog__header"></div>'),components:[xL(e,t,A.some(o),n),yL(),vL(n)],containerBehaviours:rd([Dh.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>Yn(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"},onDrag:(e,t)=>{e.getSystem().broadcastOn([Ed()],{target:t})}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=pv(((e,t,o,n,s,r)=>cL(e,t,A.some(o),n,s,r))({body:l.body,initialData:l.initialData},r,i,o,n,(e=>LL(x,e)))),g=DL(l.buttons),p=BL(g),h=$e(0!==g.length,pv(((e,t,o)=>NL(e,t,o))({buttons:g},r,o))),f=IL((()=>w),{onBlock:e=>{Lg.block(v,((t,n)=>{const s=u.getOpt(v).map((e=>kn(e.element)));return SL(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{Lg.unblock(v)},onClose:()=>t.closeWindow()},o.shared.getSink),b=Jt().os,v=Fg({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog",...b.isMacOS()?{"aria-label":l.title}:{"aria-labelledby":a}}},eventOrder:{[Ra()]:[db.name(),ab.name()],[Na()]:["execute-on-form"],[qa()]:["reflecting","execute-on-form"]},behaviours:rd([Om.config({mode:"cyclic",onEscape:e=>(Ic(e,wD),A.some(!0)),useTabstopAt:e=>!lB(e)&&("button"!==eo(e)||"disabled"!==Yo(e,"disabled")),firstTabstop:1}),db.config({channel:`${cB}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),EL(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),fd.config({}),ud("execute-on-form",f.concat([Kc(xa(),((e,t)=>{Om.focusIn(e)})),Wc(Ja(),((e,t)=>{e.getSystem().broadcastOn([pB],{newFocus:t.event.newFocus})}))])),Lg.config({getRoot:()=>A.some(v)}),ng.config({}),VD({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),x={getId:y(r),getRoot:y(v),getFooter:()=>h.map((e=>e.get(v))),getBody:()=>m.get(v),getFormWrapper:()=>{const e=m.get(v);return Pg.getCurrent(e).getOr(e)},toggleFullscreen:()=>{AL(v,c.get())}},w=VL(x,t.redial,p);return{dialog:v,instanceApi:w}};var PL=tinymce.util.Tools.resolve("tinymce.util.URI");const UL=["insertContent","setContent","execCommand","close","block","unblock"],WL=e=>a(e)&&-1!==UL.indexOf(e.mceAction),$L=(e,t,o,n)=>{const s=Ne("dialog"),i=wL(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[rB(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:rd([Gb.config({}),fd.config({})])})]}],behaviours:rd([Om.config({mode:"acyclic",useTabstopAt:k(lB)})])};return sy.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(zL({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>Wc(e,((e,o)=>{n(e,((n,s)=>{t(y,n,o.event,e)}))})),n=(e,t)=>{db.getState(e).get().each((o=>{t(o,e)}))};return[...FL(o,t),o(CD,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,CL((()=>x),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new PL(e.url,{base_uri:new PL(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=qe(),v=[db.config({channel:`${cB}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),ud("messages",[Jc((()=>{const t=Bo(ct(window),"message",(t=>{if(h.isSameOrigin(new PL(t.raw.origin))){const n=t.raw.data;WL(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,y,n):(e=>!WL(e)&&a(e)&&be(e,"mceAction"))(n)&&e.onMessage(y,n)}}));b.set(t)})),Qc(b.clear)]),ab.config({channels:{[gB]:{onReceive:(e,t)=>{Xn(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],x=ML({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),y=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Rc(t,OD,{message:e})}))},unblock:()=>{t((e=>{Ic(e,_D)}))},close:()=>{t((e=>{Ic(e,wD)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([gB],e)}))}}})(x);return{dialog:x,instanceApi:y}},GL=(e,t)=>Tr(_r("data",t,e)),jL=e=>ps(e,".tox-alert-dialog")||ps(e,".tox-confirm-dialog"),qL=(e,t,o,n)=>t&&o?[]:[Bp.config({contextual:{lazyContext:()=>A.some(Rs(ct(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition",onHide:n},modes:["top"],lazyViewport:t=>qC(e,t.element).map((e=>({bounds:XC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Fn(e.element).top})}))).getOrThunk((()=>({bounds:Ls(),optScrollEnv:A.none()})))})],XL=e=>{const t=e.editor,o=VC(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{sy.hide(l),n()},r=pv(fF({context:"any",name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=gL(),i=mL(s,t.providers),l=Fg(bL({lazySink:()=>t.getSink(),header:uL(a,i),body:pL(o,t.providers),footer:A.some(hL(fL([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Wc(SD,s)],eventOrder:{}}));sy.show(l);const c=r.get(l);fd.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{sy.hide(c),n(e)},r=pv(fF({context:"any",name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=fF({context:"any",name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=gL(),l=mL((()=>s(!1)),t.providers),c=Fg(bL({lazySink:()=>t.getSink(),header:uL(i,l),body:pL(o,t.providers),footer:A.some(hL(fL([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Wc(SD,(()=>s(!1))),Wc(kD,(()=>s(!0)))],eventOrder:{}}));sy.show(c);const d=r.get(c);fd.focus(d)}}})(e.backstages.dialog),r=(t,o)=>uE.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=Ne("dialog"),s=e.internalDialog,r=wL(s.title,n,o),a=ye(s.size),i=TL(a.get()).toArray(),l=((e,t,o,n)=>{const s=cL(e,t,A.none(),o,!1,n);return sy.parts.body(s)})({body:s.body,initialData:s.initialData},n,o,(e=>LL(h,e))),c=DL(s.buttons),d=BL(c),u=$e(0!==c.length,zL({buttons:c},n,o)),m=IL((()=>f),CL((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[db.config({channel:`${cB}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),EL(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=ML(g,m,o),h={getId:y(n),getRoot:y(p),getBody:()=>sy.getBody(p),getFooter:()=>sy.getFooter(p),getFormWrapper:()=>{const e=sy.getBody(p);return Pg.getCurrent(e).getOr(e)},toggleFullscreen:()=>{AL(p,a.get())}},f=VL(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:uE.redial,closeWindow:()=>{sy.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return sy.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>uE.open(((n,i,l)=>{const c=GL(i,l),d=Xe(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Xx.reposition(e),o&&u||Bp.refresh(e)})),g=HL({dataValidator:l,initialData:c,internalDialog:n},{redial:uE.redial,closeWindow:()=>{d.on(Xx.hide),t.off("ResizeEditor",m),t.off("ScrollWindow",p),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=()=>g.dialog.getSystem().broadcastOn([Ed()],{target:g.dialog.element}),h=Fg(Xx.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:rd([ud("window-manager-inline-events",[Wc(Ya(),((e,t)=>{Ic(g.dialog,SD)}))]),...qL(t,o,u,(()=>g.dialog.getSystem().broadcastOn([Td()],{target:g.dialog.element})))]),isExtraPart:(e,t)=>jL(t)}));return d.set(h),Xx.showWithinBounds(h,Ig(g.dialog),{anchor:s},(()=>{const e=t.inline?ln():ct(t.getContainer()),o=Rs(e);return A.some(o)})),o&&u||(Bp.refresh(h),t.on("ResizeEditor",m)),t.on("ScrollWindow",p),g.instanceApi.setData(c),Om.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>uE.open(((o,a,i)=>{const l=GL(a,i),c=Xe(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{Xx.reposition(e),Bp.refresh(e)})),m=HL({dataValidator:i,initialData:l,internalDialog:o},{redial:uE.redial,closeWindow:()=>{c.on(Xx.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=Fg(Xx.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:rd([ud("window-manager-inline-events",[Wc(Ya(),((e,t)=>{Ic(m.dialog,SD)}))]),Bp.config({contextual:{lazyContext:()=>A.some(Rs(ct(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>qC(t,e.element).map((e=>({bounds:XC(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Fn(e.element).top})}))).getOrThunk((()=>({bounds:Ls(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>jL(t)}));return c.set(g),Xx.showWithinBounds(g,Ig(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=qC(t,e.element).map((e=>XC(e))).getOr(Ls()),n=Rs(ct(t.getContentAreaContainer())),s=zs(n,o);return A.some(Is(s.x,s.y,s.width,s.height-15))})))),Bp.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),Om.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>uE.openUrl((o=>{const s=$L(o,{closeWindow:()=>{sy.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return sy.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}},YL=e=>{HS(e),(e=>{const t=e.options.register,o=e=>{return f(e,r)?{value:(t=e,_M(t.map(((e,t)=>t%2==0?"#"+(e=>{return(t=e,$A(t)?A.some({value:GA(t)}):A.none()).orThunk((()=>aM(e).map(qA))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return qA(tM(s,r,a,i))}));var t})(e).value:e)))),valid:!0}:{valid:!1,message:"Must be an array of strings."};var t},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_raw",{processor:e=>f(e,r)?{value:_M(e),valid:!0}:{valid:!1,message:"Must be an array of strings."}}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:MM(e)}),t("color_cols_foreground",{processor:n,default:DM(e,kM)}),t("color_cols_background",{processor:n,default:DM(e,OM)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:EM}),t("color_default_background",{processor:"string",default:EM})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:wz(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)};FS.add("silver",(e=>{YL(e);let t=()=>Ls();const{dialogs:o,popups:n,renderUI:s}=eL(e,{getPopupSinkBounds:()=>t()});gD(e,n.backstage.shared);const r=XL({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}}),a=Xe();return{renderUI:()=>{const o=s();return qC(e,n.getMothership().element).each((e=>{t=()=>XC(e)})),o},getWindowManagerImpl:y(r),getNotificationManagerImpl:()=>lO(e,{backstage:n.backstage},n.getMothership(),a),getPromotionElement:()=>Xn(ct(e.getContainer()),".tox-promotion").map((e=>e.dom)).getOrNull()}}))}();