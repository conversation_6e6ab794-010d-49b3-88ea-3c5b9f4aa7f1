/**
 * TinyMCE version 8.0.2 (2025-08-14)
 */

/**
 * This file bundles the code of
 * DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE
 */

!function(){"use strict";var e=function(e){if(null===e)return"null";if(void 0===e)return"undefined";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t},t=function(e){return{eq:e}},n=t((function(e,t){return e===t})),o=function(e){return t((function(t,n){if(t.length!==n.length)return!1;for(var o=t.length,r=0;r<o;r++)if(!e.eq(t[r],n[r]))return!1;return!0}))},r=function(e){return t((function(r,s){var a=Object.keys(r),i=Object.keys(s);if(!function(e,n){return function(e,n){return t((function(t,o){return e.eq(n(t),n(o))}))}(o(e),(function(e){return function(e,t){return Array.prototype.slice.call(e).sort(t)}(e,n)}))}(n).eq(a,i))return!1;for(var l=a.length,d=0;d<l;d++){var c=a[d];if(!e.eq(r[c],s[c]))return!1}return!0}))},s=t((function(t,n){if(t===n)return!0;var a=e(t);return a===e(n)&&(function(e){return-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(e)}(a)?t===n:"array"===a?o(s).eq(t,n):"object"===a&&r(s).eq(t,n))}));const a=Object.getPrototypeOf,i=(e,t,n)=>{var o;return!!n(e,t.prototype)||(null===(o=e.constructor)||void 0===o?void 0:o.name)===t.name},l=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&i(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":t})(t)===e,d=e=>t=>typeof t===e,c=e=>t=>e===t,m=(e,t)=>f(e)&&i(e,t,((e,t)=>a(e)===t)),u=l("string"),f=l("object"),g=e=>m(e,Object),p=l("array"),h=c(null),b=d("boolean"),v=c(void 0),y=e=>null==e,C=e=>!y(e),w=d("function"),E=d("number"),x=(e,t)=>{if(p(e)){for(let n=0,o=e.length;n<o;++n)if(!t(e[n]))return!1;return!0}return!1},S=()=>{},_=(e,t)=>(...n)=>e(t.apply(null,n)),k=(e,t)=>n=>e(t(n)),N=e=>()=>e,A=e=>e,R=(e,t)=>e===t;function D(e,...t){return(...n)=>{const o=t.concat(n);return e.apply(null,o)}}const T=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},B=e=>e(),P=e=>{e()},L=N(!1),M=N(!0);class I{constructor(e,t){this.tag=e,this.value=t}static some(e){return new I(!0,e)}static none(){return I.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?I.some(e(this.value)):I.none()}bind(e){return this.tag?e(this.value):I.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:I.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return C(e)?I.some(e):I.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}I.singletonNone=new I(!1);const F=Array.prototype.slice,U=Array.prototype.indexOf,z=Array.prototype.push,j=(e,t)=>U.call(e,t),$=(e,t)=>j(e,t)>-1,H=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return!0;return!1},V=(e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o},q=(e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)},W=(e,t)=>{for(let n=e.length-1;n>=0;n--)t(e[n],n)},K=(e,t)=>{const n=[],o=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?n:o).push(s)}return{pass:n,fail:o}},Y=(e,t)=>{const n=[];for(let o=0,r=e.length;o<r;o++){const r=e[o];t(r,o)&&n.push(r)}return n},G=(e,t,n)=>(W(e,((e,o)=>{n=t(n,e,o)})),n),X=(e,t,n)=>(q(e,((e,o)=>{n=t(n,e,o)})),n),Q=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++){const r=e[o];if(t(r,o))return I.some(r);if(n(r,o))break}return I.none()},Z=(e,t)=>Q(e,t,L),J=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return I.some(n);return I.none()},ee=e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!p(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);z.apply(t,e[n])}return t},te=(e,t)=>ee(V(e,t)),ne=(e,t)=>{for(let n=0,o=e.length;n<o;++n)if(!0!==t(e[n],n))return!1;return!0},oe=e=>{const t=F.call(e,0);return t.reverse(),t},re=(e,t)=>Y(e,(e=>!$(t,e))),se=(e,t)=>{const n={};for(let o=0,r=e.length;o<r;o++){const r=e[o];n[String(r)]=t(r,o)}return n},ae=(e,t)=>{const n=F.call(e,0);return n.sort(t),n},ie=(e,t)=>t>=0&&t<e.length?I.some(e[t]):I.none(),le=e=>ie(e,0),de=e=>ie(e,e.length-1),ce=w(Array.from)?Array.from:e=>F.call(e),me=(e,t)=>{for(let n=0;n<e.length;n++){const o=t(e[n],n);if(o.isSome())return o}return I.none()},ue=(e,t)=>{const n=[],o=w(t)?e=>H(n,(n=>t(n,e))):e=>$(n,e);for(let t=0,r=e.length;t<r;t++){const r=e[t];o(r)||n.push(r)}return n},fe=Object.keys,ge=Object.hasOwnProperty,pe=(e,t)=>{const n=fe(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}},he=(e,t)=>be(e,((e,n)=>({k:n,v:t(e,n)}))),be=(e,t)=>{const n={};return pe(e,((e,o)=>{const r=t(e,o);n[r.k]=r.v})),n},ve=e=>(t,n)=>{e[n]=t},ye=(e,t,n,o)=>{pe(e,((e,r)=>{(t(e,r)?n:o)(e,r)}))},Ce=(e,t)=>{const n={};return ye(e,t,ve(n),S),n},we=(e,t)=>{const n=[];return pe(e,((e,o)=>{n.push(t(e,o))})),n},Ee=e=>we(e,A),xe=(e,t)=>Se(e,t)?I.from(e[t]):I.none(),Se=(e,t)=>ge.call(e,t),_e=(e,t)=>Se(e,t)&&void 0!==e[t]&&null!==e[t],ke=e=>{if(!p(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],n={};return q(e,((o,r)=>{const s=fe(o);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],i=o[a];if(void 0!==n[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!p(i))throw new Error("case arguments must be an array");t.push(a),n[a]=(...n)=>{const o=n.length;if(o!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+o);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,n)},match:e=>{const o=fe(e);if(t.length!==o.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+o.join(","));if(!ne(t,(e=>$(o,e))))throw new Error("Not all branches were specified when using match. Specified: "+o.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,n)},log:e=>{console.log(e,{constructors:t,constructor:a,params:n})}}}})),n},Ne=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Ae=e=>{const t=t=>t(e),n=N(e),o=()=>r,r={tag:!0,inner:e,fold:(t,n)=>n(e),isValue:M,isError:L,map:t=>De.value(t(e)),mapError:o,bind:t,exists:t,forall:t,getOr:n,or:o,getOrThunk:n,orThunk:o,getOrDie:n,each:t=>{t(e)},toOptional:()=>I.some(e)};return r},Re=e=>{const t=()=>n,n={tag:!1,inner:e,fold:(t,n)=>t(e),isValue:L,isError:M,map:t,mapError:t=>De.error(t(e)),bind:t,exists:L,forall:M,getOr:A,or:A,getOrThunk:B,orThunk:B,getOrDie:O(String(e)),each:S,toOptional:I.none};return n},De={value:Ae,error:Re,fromOption:(e,t)=>e.fold((()=>Re(t)),Ae)},Te="undefined"!=typeof window?window:Function("return this;")(),Oe=()=>window.crypto.getRandomValues(new Uint32Array(1))[0]/4294967295;let Be=0;const Pe=e=>{const t=(new Date).getTime(),n=Math.floor(1e9*Oe());return Be++,e+"_"+n+Be+String(t)},Le=()=>window.isSecureContext?window.crypto.randomUUID():(()=>{const e=(()=>{const e=window.crypto.getRandomValues(new Uint8Array(16));return e[6]=15&e[6]|64,e[8]=63&e[8]|128,e})(),t=(t,n)=>{let o="";for(let r=t;r<=n;++r)o+=e[r].toString(16).padStart(2,"0");return o};return`${t(0,3)}-${t(4,5)}-${t(6,7)}-${t(8,9)}-${t(10,15)}`})(),Me=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const n={};for(let o=0;o<t.length;o++){const r=t[o];for(const t in r)Se(r,t)&&(n[t]=e(n[t],r[t]))}return n},Ie=Me(((e,t)=>g(e)&&g(t)?Ie(e,t):t)),Fe=Me(((e,t)=>t)),Ue=(e,t,n=R)=>e.exists((e=>n(e,t))),ze=(e,t,n=R)=>je(e,t,n).getOr(e.isNone()&&t.isNone()),je=(e,t,n)=>e.isSome()&&t.isSome()?I.some(n(e.getOrDie(),t.getOrDie())):I.none(),$e=(e,t)=>e?I.some(t):I.none(),He=(e,t)=>((e,t)=>{let n=null!=t?t:Te;for(let t=0;t<e.length&&null!=n;++t)n=n[e[t]];return n})(e.split("."),t);ke([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ve=e=>{const t=[],n=[];return q(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{n.push(e)}))})),{errors:t,values:n}},qe=e=>{const t=Ne(I.none()),n=()=>t.get().each((e=>clearInterval(e)));return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:o=>{n(),t.set(I.some(setInterval(o,e)))}}},We=()=>{const e=(e=>{const t=Ne(I.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(I.some(e))}}})(S);return{...e,on:t=>e.get().each(t)}},Ke=(e,t,n)=>""===t||e.length>=t.length&&e.substr(n,n+t.length)===t,Ye=(e,t)=>Xe(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Ge=(e,t,n=0,o)=>{const r=e.indexOf(t,n);return-1!==r&&(!!v(o)||r+t.length<=o)},Xe=(e,t)=>Ke(e,t,0),Qe=(e,t)=>Ke(e,t,e.length-t.length),Ze=e=>t=>t.replace(e,""),Je=Ze(/^\s+|\s+$/g),et=Ze(/^\s+/g),tt=Ze(/\s+$/g),nt=e=>e.length>0,ot=e=>!nt(e),rt=(e,t=10)=>{const n=parseInt(e,t);return isNaN(n)?I.none():I.some(n)},st=(e,t)=>{let n=null;return{cancel:()=>{h(n)||(clearTimeout(n),n=null)},throttle:(...o)=>{h(n)&&(n=setTimeout((()=>{n=null,e.apply(null,o)}),t))}}},at=(e,t)=>{let n=null;const o=()=>{h(n)||(clearTimeout(n),n=null)};return{cancel:o,throttle:(...r)=>{o(),n=setTimeout((()=>{n=null,e.apply(null,r)}),t)}}},it=e=>{let t,n=!1;return(...o)=>(n||(n=!0,t=e.apply(null,o)),t)},lt="\ufeff",dt="\xa0",ct=e=>e===lt,mt=e=>{const t={};return q(e,(e=>{t[e]={}})),fe(t)},ut=e=>void 0!==e.length,ft=Array.isArray,gt=(e,t,n)=>{if(!e)return!1;if(n=n||e,ut(e)){for(let o=0,r=e.length;o<r;o++)if(!1===t.call(n,e[o],o,e))return!1}else for(const o in e)if(Se(e,o)&&!1===t.call(n,e[o],o,e))return!1;return!0},pt=(e,t)=>{const n=[];return gt(e,((o,r)=>{n.push(t(o,r,e))})),n},ht=(e,t)=>{const n=[];return gt(e,((o,r)=>{t&&!t(o,r,e)||n.push(o)})),n},bt=(e,t,n,o)=>{let r=v(n)?e[0]:n;for(let n=0;n<e.length;n++)r=t.call(o,r,e[n],n);return r},vt=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1},yt=e=>e[e.length-1],Ct=()=>wt(0,0),wt=(e,t)=>({major:e,minor:t}),Et={nu:wt,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?Ct():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const o=e[n];if(o.test(t))return o}})(e,t);if(!n)return{major:0,minor:0};const o=e=>Number(t.replace(n,"$"+e));return wt(o(1),o(2))})(e,n)},unknown:Ct},xt=(e,t)=>{const n=String(t).toLowerCase();return Z(e,(e=>e.search(n)))},St=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,_t=e=>t=>Ge(t,e),kt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Ge(e,"edge/")&&Ge(e,"chrome")&&Ge(e,"safari")&&Ge(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,St],search:e=>Ge(e,"chrome")&&!Ge(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Ge(e,"msie")||Ge(e,"trident")},{name:"Opera",versionRegexes:[St,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:_t("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:_t("firefox")},{name:"Safari",versionRegexes:[St,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Ge(e,"safari")||Ge(e,"mobile/"))&&Ge(e,"applewebkit")}],Nt=[{name:"Windows",search:_t("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Ge(e,"iphone")||Ge(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:_t("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:_t("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:_t("linux"),versionRegexes:[]},{name:"Solaris",search:_t("sunos"),versionRegexes:[]},{name:"FreeBSD",search:_t("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:_t("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],At={browsers:N(kt),oses:N(Nt)},Rt="Edge",Dt="Chromium",Tt="Opera",Ot="Firefox",Bt="Safari",Pt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isEdge:o(Rt),isChromium:o(Dt),isIE:o("IE"),isOpera:o(Tt),isFirefox:o(Ot),isSafari:o(Bt)}},Lt=()=>Pt({current:void 0,version:Et.unknown()}),Mt=Pt,It=(N(Rt),N(Dt),N("IE"),N(Tt),N(Ot),N(Bt),"Windows"),Ft="Android",Ut="Linux",zt="macOS",jt="Solaris",$t="FreeBSD",Ht="ChromeOS",Vt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isWindows:o(It),isiOS:o("iOS"),isAndroid:o(Ft),isMacOS:o(zt),isLinux:o(Ut),isSolaris:o(jt),isFreeBSD:o($t),isChromeOS:o(Ht)}},qt=()=>Vt({current:void 0,version:Et.unknown()}),Wt=Vt,Kt=(N(It),N("iOS"),N(Ft),N(Ut),N(zt),N(jt),N($t),N(Ht),e=>window.matchMedia(e).matches);let Yt=it((()=>((e,t,n)=>{const o=At.browsers(),r=At.oses(),s=t.bind((e=>((e,t)=>me(t.brands,(t=>{const n=t.brand.toLowerCase();return Z(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Et.nu(parseInt(t.version,10),0)})))})))(o,e))).orThunk((()=>((e,t)=>xt(e,t).map((e=>{const n=Et.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e))).fold(Lt,Mt),a=((e,t)=>xt(e,t).map((e=>{const n=Et.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e).fold(qt,Wt),i=((e,t,n,o)=>{const r=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!r,a=e.isiOS()||e.isAndroid(),i=a||o("(pointer:coarse)"),l=r||!s&&a&&o("(min-device-width:768px)"),d=s||a&&!l,c=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),m=!d&&!l&&!c;return{isiPad:N(r),isiPhone:N(s),isTablet:N(l),isPhone:N(d),isTouch:N(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:N(c),isDesktop:N(m)}})(a,s,e,n);return{browser:s,os:a,deviceType:i}})(window.navigator.userAgent,I.from(window.navigator.userAgentData),Kt)));const Gt=()=>Yt(),Xt=Object.getPrototypeOf,Qt=e=>{const t=He("ownerDocument.defaultView",e);return f(e)&&((e=>((e,t)=>{const n=((e,t)=>He(e,t))(e,t);if(null==n)throw new Error(e+" not available on this browser");return n})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Xt(e).constructor.name))},Zt=window.navigator.userAgent,Jt=Gt(),en=Jt.browser,tn=Jt.os,nn=Jt.deviceType,on=-1!==Zt.indexOf("Windows Phone"),rn={transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",documentMode:en.isIE()?document.documentMode||7:10,cacheSuffix:null,container:null,canHaveCSP:!en.isIE(),windowsPhone:on,browser:{current:en.current,version:en.version,isChromium:en.isChromium,isEdge:en.isEdge,isFirefox:en.isFirefox,isIE:en.isIE,isOpera:en.isOpera,isSafari:en.isSafari},os:{current:tn.current,version:tn.version,isAndroid:tn.isAndroid,isChromeOS:tn.isChromeOS,isFreeBSD:tn.isFreeBSD,isiOS:tn.isiOS,isLinux:tn.isLinux,isMacOS:tn.isMacOS,isSolaris:tn.isSolaris,isWindows:tn.isWindows},deviceType:{isDesktop:nn.isDesktop,isiPad:nn.isiPad,isiPhone:nn.isiPhone,isPhone:nn.isPhone,isTablet:nn.isTablet,isTouch:nn.isTouch,isWebView:nn.isWebView}},sn=/^\s*|\s*$/g,an=e=>y(e)?"":(""+e).replace(sn,""),ln=function(e,t,n,o){o=o||this,e&&(n&&(e=e[n]),gt(e,((e,r)=>!1!==t.call(o,e,r,n)&&(ln(e,t,n,o),!0))))},dn={trim:an,isArray:ft,is:(e,t)=>t?!("array"!==t||!ft(e))||typeof e===t:void 0!==e,toArray:e=>{if(ft(e))return e;{const t=[];for(let n=0,o=e.length;n<o;n++)t[n]=e[n];return t}},makeMap:(e,t,n={})=>{const o=u(e)?e.split(t||","):e||[];let r=o.length;for(;r--;)n[o[r]]={};return n},each:gt,map:pt,grep:ht,inArray:(e,t)=>{if(e)for(let n=0,o=e.length;n<o;n++)if(e[n]===t)return n;return-1},hasOwn:Se,extend:(e,...t)=>{for(let n=0;n<t.length;n++){const o=t[n];for(const t in o)if(Se(o,t)){const n=o[t];void 0!==n&&(e[t]=n)}}return e},walk:ln,resolve:(e,t=window)=>{const n=e.split(".");for(let e=0,o=n.length;e<o&&(t=t[n[e]]);e++);return t},explode:(e,t)=>p(e)?e:""===e?[]:pt(e.split(t||","),an),_addCacheSuffix:e=>{const t=rn.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},cn=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},mn={fromHtml:(e,t)=>{const n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return cn(n.childNodes[0])},fromTag:(e,t)=>{const n=(t||document).createElement(e);return cn(n)},fromText:(e,t)=>{const n=(t||document).createTextNode(e);return cn(n)},fromDom:cn,fromPoint:(e,t,n)=>I.from(e.dom.elementFromPoint(t,n)).map(cn)},un=(e,t,n)=>{const o=e.document.createRange();var r;return r=o,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,n)=>{e.setEnd(t.dom,n)}),(t=>{e.setEndAfter(t.dom)}))})(o,n),o},fn=(e,t,n,o,r)=>{const s=e.document.createRange();return s.setStart(t.dom,n),s.setEnd(o.dom,r),s},gn=ke([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),pn=(e,t,n)=>t(mn.fromDom(n.startContainer),n.startOffset,mn.fromDom(n.endContainer),n.endOffset);gn.ltr,gn.rtl;const hn=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},bn=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,vn=(e,t)=>e.dom===t.dom,yn=(e,t)=>{const n=e.dom,o=t.dom;return n!==o&&n.contains(o)},Cn=hn,wn=(e,t)=>{const n=[],o=e=>(n.push(e),t(e));let r=t(e);do{r=r.bind(o)}while(r.isSome());return n},En=e=>e.dom.nodeName.toLowerCase(),xn=e=>e.dom.nodeType,Sn=e=>t=>xn(t)===e,_n=e=>8===xn(e)||"#comment"===En(e),kn=e=>Nn(e)&&Qt(e.dom),Nn=Sn(1),An=Sn(3),Rn=Sn(9),Dn=Sn(11),Tn=e=>t=>Nn(t)&&En(t)===e,On=e=>mn.fromDom(e.dom.ownerDocument),Bn=e=>Rn(e)?e:On(e),Pn=e=>mn.fromDom(Bn(e).dom.defaultView),Ln=e=>I.from(e.dom.parentNode).map(mn.fromDom),Mn=e=>I.from(e.dom.parentElement).map(mn.fromDom),In=(e,t)=>{const n=w(t)?t:L;let o=e.dom;const r=[];for(;null!==o.parentNode&&void 0!==o.parentNode;){const e=o.parentNode,t=mn.fromDom(e);if(r.push(t),!0===n(t))break;o=e}return r},Fn=e=>Ln(e).map(Hn).map((t=>Y(t,(t=>!vn(e,t))))).getOr([]),Un=e=>I.from(e.dom.previousSibling).map(mn.fromDom),zn=e=>I.from(e.dom.nextSibling).map(mn.fromDom),jn=e=>oe(wn(e,Un)),$n=e=>wn(e,zn),Hn=e=>V(e.dom.childNodes,mn.fromDom),Vn=(e,t)=>{const n=e.dom.childNodes;return I.from(n[t]).map(mn.fromDom)},qn=e=>Vn(e,0),Wn=e=>Vn(e,e.dom.childNodes.length-1),Kn=e=>e.dom.childNodes.length,Yn=e=>Dn(e)&&C(e.dom.host),Gn=e=>mn.fromDom(e.dom.getRootNode()),Xn=e=>Yn(e)?e:(e=>{const t=e.dom.head;if(null==t)throw new Error("Head is not available yet");return mn.fromDom(t)})(Bn(e)),Qn=e=>mn.fromDom(e.dom.host),Zn=e=>{if(C(e.target)){const t=mn.fromDom(e.target);if(Nn(t)&&Jn(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return le(t)}}return I.from(e.target)},Jn=e=>C(e.dom.shadowRoot),eo=(e,t,n,o)=>((e,t,n,o,r)=>{const s=((e,t)=>n=>{e(n)&&t((e=>{const t=mn.fromDom(Zn(e).getOr(e.target)),n=()=>e.stopPropagation(),o=()=>e.preventDefault(),r=_(o,n);return((e,t,n,o,r,s,a)=>({target:e,x:t,y:n,stop:o,prevent:r,kill:s,raw:a}))(t,e.clientX,e.clientY,n,o,r,e)})(n))})(n,o);return e.dom.addEventListener(t,s,r),{unbind:D(to,e,t,s,r)}})(e,t,n,o,!1),to=(e,t,n,o)=>{e.dom.removeEventListener(t,n,o)},no=M,oo=()=>mn.fromDom(document),ro=(e,t=!1)=>e.dom.focus({preventScroll:t}),so=e=>{const t=Gn(e).dom;return e.dom===t.activeElement},ao=(e=oo())=>I.from(e.dom.activeElement).map(mn.fromDom),io=(e,t)=>{Ln(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},lo=(e,t)=>{zn(e).fold((()=>{Ln(e).each((e=>{mo(e,t)}))}),(e=>{io(e,t)}))},co=(e,t)=>{qn(e).fold((()=>{mo(e,t)}),(n=>{e.dom.insertBefore(t.dom,n.dom)}))},mo=(e,t)=>{e.dom.appendChild(t.dom)},uo=(e,t)=>{io(e,t),mo(t,e)},fo=(e,t)=>{q(t,((n,o)=>{const r=0===o?e:t[o-1];lo(r,n)}))},go=(e,t)=>{q(t,(t=>{mo(e,t)}))},po=(e,t,n)=>{if(!(u(n)||b(n)||E(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},ho=(e,t,n)=>{po(e.dom,t,n)},bo=(e,t)=>{const n=e.dom;pe(t,((e,t)=>{po(n,t,e)}))},vo=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},yo=(e,t)=>I.from(vo(e,t)),Co=(e,t)=>{const n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},wo=(e,t)=>{e.dom.removeAttribute(t)},Eo=e=>{const t=e.dom.attributes;return null==t||0===t.length},xo=e=>X(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),So=e=>{e.dom.textContent="",q(Hn(e),(e=>{_o(e)}))},_o=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},ko=e=>{const t=Hn(e);t.length>0&&fo(e,t),_o(e)},No=(e,t)=>mn.fromDom(e.dom.cloneNode(t)),Ao=e=>No(e,!1),Ro=e=>No(e,!0),Do=(e,t)=>{const n=((e,t)=>{const n=mn.fromTag(t),o=xo(e);return bo(n,o),n})(e,t);lo(e,n);const o=Hn(e);return go(n,o),_o(e),n},To=e=>V(e,mn.fromDom),Oo=e=>e.dom.innerHTML,Bo=(e,t)=>{const n=On(e).dom,o=mn.fromDom(n.createDocumentFragment()),r=((e,t)=>{const n=(t||document).createElement("div");return n.innerHTML=e,Hn(mn.fromDom(n))})(t,n);go(o,r),So(e),mo(e,o)},Po=e=>void 0!==e.style&&w(e.style.getPropertyValue),Lo=e=>{const t=An(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return(e=>{const t=Gn(e);return Yn(t)?I.some(t):I.none()})(mn.fromDom(t)).fold((()=>n.body.contains(t)),k(Lo,Qn))},Mo=(e,t,n)=>{if(!u(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);Po(e)&&e.style.setProperty(t,n)},Io=(e,t,n)=>{const o=e.dom;Mo(o,t,n)},Fo=(e,t)=>{const n=e.dom;pe(t,((e,t)=>{Mo(n,t,e)}))},Uo=(e,t)=>{const n=e.dom,o=window.getComputedStyle(n).getPropertyValue(t);return""!==o||Lo(e)?o:zo(n,t)},zo=(e,t)=>Po(e)?e.style.getPropertyValue(t):"",jo=(e,t)=>{const n=e.dom,o=zo(n,t);return I.from(o).filter((e=>e.length>0))},$o=e=>{const t={},n=e.dom;if(Po(n))for(let e=0;e<n.style.length;e++){const o=n.style.item(e);t[o]=n.style[o]}return t},Ho=(e,t)=>{((e,t)=>{Po(e)&&e.style.removeProperty(t)})(e.dom,t),Ue(yo(e,"style").map(Je),"")&&wo(e,"style")},Vo=(e=>{const t=t=>{const n=(e=>{const t=e.dom;return Lo(e)?t.getBoundingClientRect().height:t.offsetHeight})(t);if(n<=0||null===n){const n=Uo(t,e);return parseFloat(n)||0}return n},n=(e,t)=>X(t,((t,n)=>{const o=Uo(e,n),r=void 0===o?0:parseInt(o,10);return isNaN(r)?t:t+r}),0);return{set:(t,n)=>{if(!E(n)&&!n.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+n);const o=t.dom;Po(o)&&(o.style[e]=n+"px")},get:t,getOuter:t,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}})("height"),qo=(e,t)=>({left:e,top:t,translate:(n,o)=>qo(e+n,t+o)}),Wo=qo,Ko=(e,t)=>void 0!==e?e:void 0!==t?t:0,Yo=e=>{const t=e.dom,n=t.ownerDocument.body;return n===t?Wo(n.offsetLeft,n.offsetTop):Lo(e)?(e=>{const t=e.getBoundingClientRect();return Wo(t.left,t.top)})(t):Wo(0,0)},Go=e=>{const t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return Wo(n,o)},Xo=(e,t,n)=>{const o=(void 0!==n?n.dom:document).defaultView;o&&o.scrollTo(e,t)},Qo=(e,t)=>{Gt().browser.isSafari()&&w(e.dom.scrollIntoViewIfNeeded)?e.dom.scrollIntoViewIfNeeded(!1):e.dom.scrollIntoView(t)},Zo=(e,t)=>{const n=(t||document).createDocumentFragment();return q(e,(e=>{n.appendChild(e.dom)})),mn.fromDom(n)},Jo=(e=>{const t=t=>e(t)?I.from(t.dom.nodeValue):I.none();return{get:n=>{if(!e(n))throw new Error("Can only get text value of a text node");return t(n).getOr("")},getOption:t,set:(t,n)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}})(An),er=e=>Jo.get(e),tr=(e,t)=>Jo.set(e,t),nr=(e,t)=>{const n=vo(e,t);return void 0===n||""===n?[]:n.split(" ")};var or=(e,t,n,o,r)=>e(n,o)?I.some(n):w(r)&&r(n)?I.none():t(n,o,r);const rr=(e,t,n)=>{let o=e.dom;const r=w(n)?n:L;for(;o.parentNode;){o=o.parentNode;const e=mn.fromDom(o);if(t(e))return I.some(e);if(r(e))break}return I.none()},sr=(e,t,n)=>or(((e,t)=>t(e)),rr,e,t,n),ar=(e,t)=>{const n=e=>{for(let o=0;o<e.childNodes.length;o++){const r=mn.fromDom(e.childNodes[o]);if(t(r))return I.some(r);const s=n(e.childNodes[o]);if(s.isSome())return s}return I.none()};return n(e.dom)},ir=(e,t,n)=>rr(e,(e=>hn(e,t)),n),lr=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return bn(n)?I.none():I.from(n.querySelector(e)).map(mn.fromDom)})(t,e),dr=(e,t,n)=>or(((e,t)=>hn(e,t)),ir,e,t,n),cr=e=>void 0!==e.dom.classList,mr=e=>nr(e,"class"),ur=(e,t)=>((e,t,n)=>{const o=nr(e,t).concat([n]);return ho(e,t,o.join(" ")),!0})(e,"class",t),fr=(e,t)=>((e,t,n)=>{const o=Y(nr(e,t),(e=>e!==n));return o.length>0?ho(e,t,o.join(" ")):wo(e,t),!1})(e,"class",t),gr=(e,t)=>{cr(e)?e.dom.classList.add(t):ur(e,t)},pr=e=>{0===(cr(e)?e.dom.classList:mr(e)).length&&wo(e,"class")},hr=(e,t)=>{cr(e)?e.dom.classList.remove(t):fr(e,t),pr(e)},br=(e,t)=>cr(e)&&e.dom.classList.contains(t),vr=(e,t=!1)=>{return Lo(e)?e.dom.isContentEditable:(n=e,dr(n,"[contenteditable]")).fold(N(t),(e=>"true"===yr(e)));var n},yr=e=>e.dom.contentEditable,Cr=(e,t)=>{e.dom.contentEditable=t?"true":"false"},wr=(e,t,n)=>Y(In(e,n),t),Er=(e,t)=>{let n=[];return q(Hn(e),(e=>{t(e)&&(n=n.concat([e])),n=n.concat(Er(e,t))})),n},xr=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return bn(n)?[]:V(n.querySelectorAll(e),mn.fromDom)})(t,e),Sr=(e,t,n)=>rr(e,t,n).isSome(),_r=(e,t)=>((e,t)=>{const n=e.dom;return n.parentNode?((e,t)=>Z(e.dom.childNodes,(e=>t(mn.fromDom(e)))).map(mn.fromDom))(mn.fromDom(n.parentNode),(n=>!vn(e,n)&&t(n))):I.none()})(e,t).isSome(),kr=(e,t)=>ar(e,t).isSome(),Nr=e=>w(e)?e:L,Ar=(e,t,n)=>{const o=t(e),r=Nr(n);return o.orThunk((()=>r(e)?I.none():((e,t,n)=>{let o=e.dom;const r=Nr(n);for(;o.parentNode;){o=o.parentNode;const e=mn.fromDom(o),n=t(e);if(n.isSome())return n;if(r(e))break}return I.none()})(e,t,r)))},Rr=["img","br"],Dr=e=>{return(t=e,Jo.getOption(t)).filter((e=>0!==e.trim().length||e.indexOf(dt)>-1)).isSome()||$(Rr,En(e))||(e=>kn(e)&&"false"===vo(e,"contenteditable"))(e);var t},Tr=(e,t,n,o)=>({start:e,soffset:t,finish:n,foffset:o}),Or=ke([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Br={before:Or.before,on:Or.on,after:Or.after,cata:(e,t,n,o)=>e.fold(t,n,o),getStart:e=>e.fold(A,A,A)},Pr=ke([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Lr={domRange:Pr.domRange,relative:Pr.relative,exact:Pr.exact,exactFromRange:e=>Pr.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>mn.fromDom(e.startContainer),relative:(e,t)=>Br.getStart(e),exact:(e,t,n,o)=>e}))(e);return Pn(t)},range:Tr},Mr=(e,t)=>{const n=En(e);return"input"===n?Br.after(e):$(["br","img"],n)?0===t?Br.before(e):Br.after(e):Br.on(e,t)},Ir=(e,t)=>{const n=e.fold(Br.before,Mr,Br.after),o=t.fold(Br.before,Mr,Br.after);return Lr.relative(n,o)},Fr=(e,t,n,o)=>{const r=Mr(e,t),s=Mr(n,o);return Lr.relative(r,s)},Ur=e=>{const t=Lr.getWin(e).dom,n=(e,n,o,r)=>fn(t,e,n,o,r),o=(e=>e.match({domRange:e=>{const t=mn.fromDom(e.startContainer),n=mn.fromDom(e.endContainer);return Fr(t,e.startOffset,n,e.endOffset)},relative:Ir,exact:Fr}))(e);return((e,t)=>{const n=((e,t)=>t.match({domRange:e=>({ltr:N(e),rtl:I.none}),relative:(t,n)=>({ltr:it((()=>un(e,t,n))),rtl:it((()=>I.some(un(e,n,t))))}),exact:(t,n,o,r)=>({ltr:it((()=>fn(e,t,n,o,r))),rtl:it((()=>I.some(fn(e,o,r,t,n))))})}))(e,t);return((e,t)=>{const n=t.ltr();return n.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>gn.rtl(mn.fromDom(e.endContainer),e.endOffset,mn.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>pn(0,gn.ltr,n))):pn(0,gn.ltr,n)})(0,n)})(t,o).match({ltr:n,rtl:n})},zr=(e,t,n)=>((e,t,n)=>((e,t,n)=>e.caretPositionFromPoint?((e,t,n)=>{var o;return I.from(null===(o=e.caretPositionFromPoint)||void 0===o?void 0:o.call(e,t,n)).bind((t=>{if(null===t.offsetNode)return I.none();const n=e.createRange();return n.setStart(t.offsetNode,t.offset),n.collapse(),I.some(n)}))})(e,t,n):e.caretRangeFromPoint?((e,t,n)=>{var o;return I.from(null===(o=e.caretRangeFromPoint)||void 0===o?void 0:o.call(e,t,n))})(e,t,n):I.none())(e.document,t,n).map((e=>Tr(mn.fromDom(e.startContainer),e.startOffset,mn.fromDom(e.endContainer),e.endOffset))))(e,t,n),jr=(e,t,n,o)=>({x:e,y:t,width:n,height:o,right:e+n,bottom:t+o}),$r=e=>{const t=void 0===e?window:e,n=t.document,o=Go(mn.fromDom(n));return(e=>{const t=void 0===e?window:e;return Gt().browser.isFirefox()?I.none():I.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,n=e.clientWidth,r=e.clientHeight;return jr(o.left,o.top,n,r)}),(e=>jr(Math.max(e.pageLeft,o.left),Math.max(e.pageTop,o.top),e.width,e.height)))};class Hr{constructor(e,t){this.node=e,this.rootNode=t,this.current=this.current.bind(this),this.next=this.next.bind(this),this.prev=this.prev.bind(this),this.prev2=this.prev2.bind(this)}current(){return this.node}next(e){return this.node=this.findSibling(this.node,"firstChild","nextSibling",e),this.node}prev(e){return this.node=this.findSibling(this.node,"lastChild","previousSibling",e),this.node}prev2(e){return this.node=this.findPreviousNode(this.node,e),this.node}findSibling(e,t,n,o){if(e){if(!o&&e[t])return e[t];if(e!==this.rootNode){let t=e[n];if(t)return t;for(let o=e.parentNode;o&&o!==this.rootNode;o=o.parentNode)if(t=o[n],t)return t}}}findPreviousNode(e,t){if(e){const n=e.previousSibling;if(this.rootNode&&n===this.rootNode)return;if(n){if(!t)for(let e=n.lastChild;e;e=e.lastChild)if(!e.lastChild)return e;return n}const o=e.parentNode;if(o&&o!==this.rootNode)return o}}}const Vr=/^[ \t\r\n]*$/,qr=e=>Vr.test(e),Wr=e=>"\n"===e||"\r"===e,Kr=(e,t=4,n=!0,o=!0)=>{const r=((e,t)=>t<=0?"":new Array(t+1).join(" "))(0,t),s=e.replace(/\t/g,r),a=X(s,((e,t)=>(e=>-1!==" \f\t\v".indexOf(e))(t)||t===dt?e.pcIsSpace||""===e.str&&n||e.str.length===s.length-1&&o||((e,t)=>t<e.length&&t>=0&&Wr(e[t]))(s,e.str.length+1)?{pcIsSpace:!1,str:e.str+dt}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:Wr(t),str:e.str+t}),{pcIsSpace:!1,str:""});return a.str},Yr=e=>t=>!!t&&t.nodeType===e,Gr=e=>!!e&&!Object.getPrototypeOf(e),Xr=Yr(1),Qr=e=>Xr(e)&&kn(mn.fromDom(e)),Zr=e=>{const t=e.toLowerCase();return e=>C(e)&&e.nodeName.toLowerCase()===t},Jr=e=>{const t=e.map((e=>e.toLowerCase()));return e=>{if(e&&e.nodeName){const n=e.nodeName.toLowerCase();return $(t,n)}return!1}},es=(e,t)=>{const n=t.toLowerCase().split(" ");return t=>{if(Xr(t)){const o=t.ownerDocument.defaultView;if(o)for(let r=0;r<n.length;r++){const s=o.getComputedStyle(t,null);if((s?s.getPropertyValue(e):null)===n[r])return!0}}return!1}},ts=e=>Xr(e)&&e.hasAttribute("data-mce-bogus"),ns=e=>Xr(e)&&"TABLE"===e.tagName,os=e=>t=>{if(Qr(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},rs=Jr(["textarea","input"]),ss=Yr(3),as=Yr(4),is=Yr(7),ls=Yr(8),ds=Yr(9),cs=Yr(11),ms=Zr("br"),us=Zr("img"),fs=Zr("a"),gs=os("true"),ps=os("false"),hs=e=>Qr(e)&&e.isContentEditable&&C(e.parentElement)&&!e.parentElement.isContentEditable,bs=Jr(["td","th"]),vs=Jr(["td","th","caption"]),ys=Zr("template"),Cs=Jr(["video","audio","object","embed"]),ws=Zr("li"),Es=Zr("details"),xs=Zr("summary"),Ss={skipBogus:!0,includeZwsp:!1,checkRootAsContent:!1},_s=e=>Xr(e)&&e.hasAttribute("data-mce-bookmark");const ks=(e,t,n,o)=>ss(e)&&!((e,t,n)=>qr(e.data)&&!((e,t,n)=>{const o=mn.fromDom(t),r=mn.fromDom(e),s=n.getWhitespaceElements();return Sr(r,(e=>Se(s,En(e))),D(vn,o))})(e,t,n))(e,t,n)&&(!o.includeZwsp||!(e=>{for(const t of e)if(!ct(t))return!1;return!0})(e.data)),Ns=(e,t,n,o)=>w(o.isContent)&&o.isContent(t)||((e,t)=>Xr(e)&&Se(t.getNonEmptyElements(),e.nodeName))(t,e)||_s(t)||(e=>Xr(e)&&"A"===e.nodeName&&!e.hasAttribute("href")&&(e.hasAttribute("name")||e.hasAttribute("id")))(t)||ks(t,n,e,o)||ps(t)||gs(t)&&(e=>Mn(mn.fromDom(e)).exists((e=>!vr(e))))(t),As=(e,t,n)=>{const o={...Ss,...n};if(o.checkRootAsContent&&Ns(e,t,t,o))return!1;let r=t.firstChild,s=0;if(!r)return!0;const a=new Hr(r,t);do{if(o.skipBogus&&Xr(r)){const e=r.getAttribute("data-mce-bogus");if(e){r=a.next("all"===e);continue}}if(ls(r))r=a.next(!0);else if(ms(r))s++,r=a.next();else{if(Ns(e,r,t,o))return!1;r=a.next()}}while(r);return s<=1},Rs=(e,t,n)=>As(e,t.dom,{checkRootAsContent:!0,...n}),Ds=(e,t,n)=>Ns(e,t,t,{includeZwsp:Ss.includeZwsp,...n}),Ts=e=>{const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":"html"},Os=e=>"html"!==Ts(e),Bs=e=>Os(e.nodeName),Ps=e=>Ts(e.nodeName),Ls=["svg","math"],Ms="data-mce-block",Is=e=>V((e=>Y(fe(e),(e=>!/[A-Z]/.test(e))))(e),(e=>{const t=CSS.escape(e);return`${t}:`+V(Ls,(e=>`not(${e} ${t})`)).join(":")})).join(","),Fs=(e,t)=>C(t.querySelector(e))?(t.setAttribute(Ms,"true"),"inline-boundary"===t.getAttribute("data-mce-selected")&&t.removeAttribute("data-mce-selected"),!0):(t.removeAttribute(Ms),!1),Us=(e,t)=>{const n=Is(e.getTransparentElements()),o=Is(e.getBlockElements());return Y(t.querySelectorAll(n),(e=>Fs(o,e)))},zs=(e,t,n)=>{var o;const r=n?"lastChild":"firstChild";for(let n=t[r];n;n=n[r])if(As(e,n,{checkRootAsContent:!0}))return void(null===(o=n.parentNode)||void 0===o||o.removeChild(n))},js=(e,t,n)=>{const o=e.getBlockElements(),r=mn.fromDom(t),s=e=>En(e)in o,a=e=>vn(e,r);q(To(n),(t=>{rr(t,s,a).each((n=>{const o=(t=>Y(Hn(t),(t=>s(t)&&!e.isValidChild(En(n),En(t)))))(t);if(o.length>0){const t=Mn(n);q(o,(t=>{rr(t,s,a).each((n=>{((e,t,n)=>{const o=document.createRange(),r=t.parentNode;if(r){o.setStartBefore(t),o.setEndBefore(n);const s=o.extractContents();zs(e,s,!0),o.setStartAfter(n),o.setEndAfter(t);const a=o.extractContents();zs(e,a,!1),As(e,s,{checkRootAsContent:!0})||r.insertBefore(s,t),As(e,n,{checkRootAsContent:!0})||r.insertBefore(n,t),As(e,a,{checkRootAsContent:!0})||r.insertBefore(a,t),r.removeChild(t)}})(e,n.dom,t.dom)}))})),t.each((t=>Us(e,t.dom)))}}))}))},$s=(e,t)=>{const n=Us(e,t);js(e,t,n),((e,t,n)=>{q([...n,...Ks(e,t)?[t]:[]],(t=>q(xr(mn.fromDom(t),t.nodeName.toLowerCase()),(t=>{Ys(e,t.dom)&&ko(t)}))))})(e,t,n)},Hs=(e,t)=>{if(Ws(e,t)){const n=Is(e.getBlockElements());Fs(n,t)}},Vs=e=>e.hasAttribute(Ms),qs=(e,t)=>Se(e.getTransparentElements(),t),Ws=(e,t)=>Xr(t)&&qs(e,t.nodeName),Ks=(e,t)=>Ws(e,t)&&Vs(t),Ys=(e,t)=>Ws(e,t)&&!Vs(t),Gs=(e,t)=>1===t.type&&qs(e,t.name)&&u(t.attr(Ms)),Xs=Gt().browser,Qs=e=>Z(e,Nn),Zs=(e,t)=>e.children&&$(e.children,t),Js=(e,t={})=>{let n=0;const o={},r=mn.fromDom(e),s=Bn(r),a=e=>{mo(Xn(r),e)},i=e=>{const t=Xn(r);lr(t,"#"+e).each(_o)},l=e=>xe(o,e).getOrThunk((()=>({id:"mce-u"+n++,passed:[],failed:[],count:0}))),d=e=>new Promise(((n,r)=>{let i;const d=dn._addCacheSuffix(e),c=l(d);o[d]=c,c.count++;const m=(e,t)=>{q(e,P),c.status=t,c.passed=[],c.failed=[],i&&(i.onload=null,i.onerror=null,i=null)},u=()=>m(c.passed,2),f=()=>m(c.failed,3);if(n&&c.passed.push(n),r&&c.failed.push(r),1===c.status)return;if(2===c.status)return void u();if(3===c.status)return void f();c.status=1;const g=mn.fromTag("link",s.dom);bo(g,{rel:"stylesheet",type:"text/css",id:c.id});const p=((e,t)=>{const n=t.crossOrigin;return t.contentCssCors?"anonymous":w(n)?n(e):void 0})(e,t);void 0!==p&&ho(g,"crossOrigin",p),t.referrerPolicy&&ho(g,"referrerpolicy",t.referrerPolicy),i=g.dom,i.onload=u,i.onerror=f,a(g),ho(g,"href",d)})),c=e=>{const t=dn._addCacheSuffix(e);xe(o,t).each((e=>{0==--e.count&&(delete o[t],i(e.id))}))};return{load:d,loadRawCss:(e,t)=>{const n=l(e);o[e]=n,n.count++;const r=mn.fromTag("style",s.dom);bo(r,{rel:"stylesheet",type:"text/css",id:n.id,"data-mce-key":e}),r.dom.innerHTML=t,a(r)},loadAll:e=>Promise.allSettled(V(e,(e=>d(e).then(N(e))))).then((e=>{const t=K(e,(e=>"fulfilled"===e.status));return t.fail.length>0?Promise.reject(V(t.fail,(e=>e.reason))):V(t.pass,(e=>e.value))})),unload:c,unloadRawCss:e=>{xe(o,e).each((t=>{0==--t.count&&(delete o[e],i(t.id))}))},unloadAll:e=>{q(e,(e=>{c(e)}))},_setReferrerPolicy:e=>{t.referrerPolicy=e},_setContentCssCors:e=>{t.contentCssCors=e},_setCrossOrigin:e=>{t.crossOrigin=e}}},ea=(()=>{const e=new WeakMap;return{forElement:(t,n)=>{const o=Gn(t).dom;return I.from(e.get(o)).getOrThunk((()=>{const t=Js(o,n);return e.set(o,t),t}))}}})(),ta=(e,t)=>C(e)&&(Ds(t,e)||t.isInline(e.nodeName.toLowerCase())),na=e=>(e=>"span"===e.nodeName.toLowerCase())(e)&&"bookmark"===e.getAttribute("data-mce-type"),oa=(e,t,n,o)=>{var r;const s=o||t;if(Xr(t)&&na(t))return t;const a=t.childNodes;for(let t=a.length-1;t>=0;t--)oa(e,a[t],n,s);if(Xr(t)){const e=t.childNodes;1===e.length&&na(e[0])&&(null===(r=t.parentNode)||void 0===r||r.insertBefore(e[0],t))}return(e=>cs(e)||ds(e))(t)||Ds(n,t)||(e=>!!Xr(e)&&e.childNodes.length>0)(t)||((e,t,n)=>ss(e)&&e.data.length>0&&((e,t,n)=>{const o=new Hr(e,t).prev(!1),r=new Hr(e,t).next(!1),s=v(o)||ta(o,n),a=v(r)||ta(r,n);return s&&a})(e,t,n))(t,s,n)||e.remove(t),t},ra=dn.makeMap,sa=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,aa=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,ia=/[<>&\"\']/g,la=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,da={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"},ca={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},ma={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"},ua=(e,t)=>{const n={};if(e){const o=e.split(",");t=t||10;for(let e=0;e<o.length;e+=2){const r=String.fromCharCode(parseInt(o[e],t));if(!ca[r]){const t="&"+o[e+1]+";";n[r]=t,n[t]=r}}return n}},fa=ua("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32),ga=(e,t)=>e.replace(t?sa:aa,(e=>ca[e]||e)),pa=(e,t)=>e.replace(t?sa:aa,(e=>e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":ca[e]||"&#"+e.charCodeAt(0)+";")),ha=(e,t,n)=>{const o=n||fa;return e.replace(t?sa:aa,(e=>ca[e]||o[e]||e))},ba={encodeRaw:ga,encodeAllRaw:e=>(""+e).replace(ia,(e=>ca[e]||e)),encodeNumeric:pa,encodeNamed:ha,getEncodeFunc:(e,t)=>{const n=ua(t)||fa,o=ra(e.replace(/\+/g,","));return o.named&&o.numeric?(e,t)=>e.replace(t?sa:aa,(e=>void 0!==ca[e]?ca[e]:void 0!==n[e]?n[e]:e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";")):o.named?t?(e,t)=>ha(e,t,n):ha:o.numeric?pa:ga},decode:e=>e.replace(la,((e,t)=>t?(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))>65535?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):da[t]||String.fromCharCode(t):ma[e]||fa[e]||(e=>{const t=mn.fromTag("div").dom;return t.innerHTML=e,t.textContent||t.innerText||e})(e)))},va=(e,t)=>(e=dn.trim(e))?e.split(t||" "):[],ya=e=>new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$"),Ca=e=>Object.freeze(["id","accesskey","class","dir","lang","style","tabindex","title","role",..."html4"!==e?["contenteditable","contextmenu","draggable","dropzone","hidden","spellcheck","translate","itemprop","itemscope","itemtype"]:[],..."html5-strict"!==e?["xml:lang"]:[]]),wa=e=>{let t,n;t="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",n="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(t+=" article aside details dialog figure main header footer hgroup section nav a ins del canvas map",n+=" audio canvas command data datalist mark meter output picture progress template time wbr video ruby bdi keygen svg"),"html5-strict"!==e&&(n=[n,"acronym applet basefont big font strike tt"].join(" "),t=[t,"center dir isindex noframes"].join(" "));const o=[t,n].join(" ");return{blockContent:t,phrasingContent:n,flowContent:o}},Ea=e=>{const{blockContent:t,phrasingContent:n,flowContent:o}=wa(e),r=e=>Object.freeze(e.split(" "));return Object.freeze({blockContent:r(t),phrasingContent:r(n),flowContent:r(o)})},xa={html4:it((()=>Ea("html4"))),html5:it((()=>Ea("html5"))),"html5-strict":it((()=>Ea("html5-strict")))},Sa=(e,t)=>{const{blockContent:n,phrasingContent:o,flowContent:r}=xa[e]();return"blocks"===t?I.some(n):"phrasing"===t?I.some(o):"flow"===t?I.some(r):I.none()},_a=e=>I.from(/^(@?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)$/.exec(e)).map((e=>({preset:"@"===e[1],name:e[2]}))),ka={},Na=dn.makeMap,Aa=dn.each,Ra=dn.extend,Da=dn.explode,Ta=(e,t={})=>{const n=Na(e," ",Na(e.toUpperCase()," "));return Ra(n,t)},Oa=e=>Ta("td th li dt dd figcaption caption details summary",e.getTextBlockElements()),Ba=(e,t)=>{if(e){const n={};return u(e)&&(e={"*":e}),Aa(e,((e,o)=>{n[o]=n[o.toUpperCase()]="map"===t?Na(e,/[, ]/):Da(e,/[, ]/)})),n}},Pa=(e={})=>{var t;const n={},o={};let r=[];const s={},a={},i=(t,n,o)=>{const r=e[t];if(r)return Na(r,/[, ]/,Na(r.toUpperCase(),/[, ]/));{let e=ka[t];return e||(e=Ta(n,o),ka[t]=e),e}},l=null!==(t=e.schema)&&void 0!==t?t:"html5",d=(e=>{const t=Ca(e),{phrasingContent:n,flowContent:o}=wa(e),r={},s=(e,t,n)=>{r[e]={attributes:se(t,N({})),attributesOrder:t,children:se(n,N({}))}},a=(e,n="",o="")=>{const r=va(o),a=va(e);let i=a.length;const l=[...t,...va(n)];for(;i--;)s(a[i],l.slice(),r)},i=(e,t)=>{const n=va(e),o=va(t);let s=n.length;for(;s--;){const e=r[n[s]];for(let t=0,n=o.length;t<n;t++)e.attributes[o[t]]={},e.attributesOrder.push(o[t])}};return"html5-strict"!==e&&(q(va("acronym applet basefont big font strike tt"),(e=>{a(e,"",n)})),q(va("center dir isindex noframes"),(e=>{a(e,"",o)}))),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",o),a("dd div","",o),a("address dt caption","","html4"===e?n:o),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",n),a("blockquote","cite",o),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",o),a("dl","","dt dd"),a("a","href target rel media hreflang type","html4"===e?n:o),a("q","cite",n),a("ins del","cite datetime",o),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",o),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[o,"param"].join(" ")),a("param","name value"),a("map","name",[o,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",o),a("th","colspan rowspan headers scope abbr",o),a("form","accept-charset action autocomplete enctype method name novalidate target",o),a("fieldset","disabled form name",[o,"legend"].join(" ")),a("label","form for",n),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?o:n),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[o,"li"].join(" ")),a("noscript","",o),"html4"!==e&&(a("wbr"),a("ruby","",[n,"rt rp"].join(" ")),a("figcaption","",o),a("mark rt rp bdi","",n),a("summary","",[n,"h1 h2 h3 h4 h5 h6"].join(" ")),a("canvas","width height",o),a("data","value",n),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[o,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[o,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[n,"option"].join(" ")),a("article section nav aside main header footer","",o),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[o,"figcaption"].join(" ")),a("time","datetime",n),a("dialog","open",o),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",n),a("progress","value max",n),a("meter","value min max low high optimum",n),a("details","open",[o,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name"),s("svg","id tabindex lang xml:space class style x y width height viewBox preserveAspectRatio zoomAndPan transform".split(" "),[])),"html5-strict"!==e&&(i("script","language xml:space"),i("style","xml:space"),i("object","declare classid code codebase codetype archive standby align border hspace vspace"),i("embed","align name hspace vspace"),i("param","valuetype type"),i("a","charset name rev shape coords"),i("br","clear"),i("applet","codebase archive code object alt name width height align hspace vspace"),i("img","name longdesc align border hspace vspace"),i("iframe","longdesc frameborder marginwidth marginheight scrolling align"),i("font basefont","size color face"),i("input","usemap align"),i("select"),i("textarea"),i("h1 h2 h3 h4 h5 h6 div p legend caption","align"),i("ul","type compact"),i("li","type"),i("ol dl menu dir","compact"),i("pre","width xml:space"),i("hr","align noshade size width"),i("isindex","prompt"),i("table","summary width frame rules cellspacing cellpadding align bgcolor"),i("col","width align char charoff valign"),i("colgroup","width align char charoff valign"),i("thead","align char charoff valign"),i("tr","align char charoff valign bgcolor"),i("th","axis align char charoff valign nowrap bgcolor width height"),i("form","accept"),i("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),i("tfoot","align char charoff valign"),i("tbody","align char charoff valign"),i("area","nohref"),i("body","background bgcolor text link vlink alink")),"html4"!==e&&(i("input button select textarea","autofocus"),i("input textarea","placeholder"),i("a","download"),i("link script img","crossorigin"),i("img","loading"),i("iframe","sandbox seamless allow allowfullscreen loading referrerpolicy")),"html4"!==e&&q([r.video,r.audio],(e=>{delete e.children.audio,delete e.children.video})),q(va("a form meter progress dfn"),(e=>{r[e]&&delete r[e].children[e]})),delete r.caption.children.table,delete r.script,r})(l);!1===e.verify_html&&(e.valid_elements="*[*]");const c=Ba(e.valid_styles),m=Ba(e.invalid_styles,"map"),g=Ba(e.valid_classes,"map"),h=i("whitespace_elements","pre script noscript style textarea video audio iframe object code"),v=i("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),y=i("void_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),C=i("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls allowfullscreen"),w="td th iframe video audio object script code",E=i("non_empty_elements",w+" pre svg textarea summary",y),x=i("move_caret_before_on_enter_elements",w+" table",y),S="h1 h2 h3 h4 h5 h6",_=i("text_block_elements",S+" p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),k=i("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary html body multicol listing colgroup col",_),A=i("text_inline_elements","span strong b em i font s strike u var cite dfn code mark q sup sub samp"),R=i("transparent_elements","a ins del canvas map"),D=i("wrap_block_elements","pre "+S);Aa("script noscript iframe noframes noembed title style textarea xmp plaintext".split(" "),(e=>{a[e]=new RegExp("</"+e+"[^>]*>","gi")}));const T=e=>{const t=I.from(n["@"]),o=/[*?+]/;q(((e,t)=>{const n=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)])?$/;return te(va(t,","),(t=>{const o=n.exec(t);if(o){const t=o[1],n=o[2],r=o[3],s=o[4],a=o[5],i={attributes:{},attributesOrder:[]};if(e.each((e=>((e,t)=>{pe(e.attributes,((e,n)=>{t.attributes[n]=e})),t.attributesOrder.push(...e.attributesOrder)})(e,i))),"#"===t?i.paddEmpty=!0:"-"===t&&(i.removeEmpty=!0),"!"===s&&(i.removeEmptyAttrs=!0),a&&((e,t)=>{const n=/^([!\-])?(\w+[\\:]:\w+|[^=~<]+)?(?:([=~<])(.*))?$/,o=/[*?+]/,{attributes:r,attributesOrder:s}=t;q(va(e,"|"),(e=>{const a=n.exec(e);if(a){const e={},n=a[1],i=a[2].replace(/[\\:]:/g,":"),l=a[3],d=a[4];if("!"===n&&(t.attributesRequired=t.attributesRequired||[],t.attributesRequired.push(i),e.required=!0),"-"===n)return delete r[i],void s.splice(dn.inArray(s,i),1);if(l&&("="===l?(t.attributesDefault=t.attributesDefault||[],t.attributesDefault.push({name:i,value:d}),e.defaultValue=d):"~"===l?(t.attributesForced=t.attributesForced||[],t.attributesForced.push({name:i,value:d}),e.forcedValue=d):"<"===l&&(e.validValues=dn.makeMap(d,"?"))),o.test(i)){const n=e;t.attributePatterns=t.attributePatterns||[],n.pattern=ya(i),t.attributePatterns.push(n)}else r[i]||s.push(i),r[i]=e}}))})(a,i),r&&(i.outputName=n),"@"===n){if(!e.isNone())return[];e=I.some(i)}return[r?{name:n,element:i,aliasName:r}:{name:n,element:i}]}return[]}))})(t,null!=e?e:""),(({name:e,element:t,aliasName:s})=>{if(s&&(n[s]=t),o.test(e)){const n=t;n.pattern=ya(e),r.push(n)}else n[e]=t}))},O=e=>{r=[],q(fe(n),(e=>{delete n[e]})),T(e)},B=(e,t)=>{var r,a;delete ka.text_block_elements,delete ka.block_elements;const i=!!t.extends&&!oe(t.extends),d=t.extends;if(o[e]=d?o[d]:{},s[e]=null!=d?d:e,E[e.toUpperCase()]={},E[e]={},i||(k[e.toUpperCase()]={},k[e]={}),d&&!n[e]&&n[d]){const t=(e=>{const t=e=>p(e)?V(e,t):(e=>f(e)&&e.source&&"[object RegExp]"===Object.prototype.toString.call(e))(e)?new RegExp(e.source,e.flags):f(e)?he(e,t):e;return t(e)})(n[d]);delete t.removeEmptyAttrs,delete t.removeEmpty,n[e]=t}else n[e]={attributesOrder:[],attributes:{}};if(p(t.attributes)){const o=e=>{s.attributesOrder.push(e),s.attributes[e]={}},s=null!==(r=n[e])&&void 0!==r?r:{};delete s.attributesDefault,delete s.attributesForced,delete s.attributePatterns,delete s.attributesRequired,s.attributesOrder=[],s.attributes={},q(t.attributes,(e=>{const t=Ca(l);_a(e).each((({preset:e,name:n})=>{e?"global"===n&&q(t,o):o(n)}))})),n[e]=s}if(b(t.padEmpty)){const o=null!==(a=n[e])&&void 0!==a?a:{};o.paddEmpty=t.padEmpty,n[e]=o}if(p(t.children)){const n={},r=e=>{n[e]={}},s=e=>{Sa(l,e).each((e=>{q(e,r)}))};q(t.children,(e=>{_a(e).each((({preset:e,name:t})=>{e?s(t):r(t)}))})),o[e]=n}d&&pe(o,((t,n)=>{t[d]&&(o[n]=t=Ra({},o[n]),t[e]=t[d])}))},P=e=>{f(e)?pe(e,((e,t)=>B(t,e))):u(e)&&(e=>{q((e=>{const t=/^(~)?(.+)$/;return te(va(e,","),(e=>{const n=t.exec(e);return n?[{cloneName:"~"===n[1]?"span":"div",name:n[2]}]:[]}))})(null!=e?e:""),(({name:e,cloneName:t})=>{B(e,{extends:t})}))})(e)},L=e=>{q((e=>{const t=/^([+\-]?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)\[([^\]]+)]$/;return te(va(e,","),(e=>{const n=t.exec(e);if(n){const e=n[1],t=e?(e=>"-"===e?"remove":"add")(e):"replace";return[{operation:t,name:n[2],validChildren:te(va(n[3],"|"),(e=>_a(e).toArray()))}]}return[]}))})(null!=e?e:""),(({operation:e,name:t,validChildren:n})=>{const r="replace"===e?{"#comment":{}}:o[t],s=t=>{"remove"===e?delete r[t]:r[t]={}};q(n,(({preset:e,name:t})=>{e?(e=>{Sa(l,e).each((e=>{q(e,s)}))})(t):s(t)})),o[t]=r}))},M=e=>{const t=n[e];if(t)return t;let o=r.length;for(;o--;){const t=r[o];if(t.pattern.test(e))return t}},F=N(c),U=N(m),z=N(g),j=N(C),$=N(k),H=N(_),W=N(A),K=N(Object.seal(y)),Y=N(v),G=N(E),X=N(x),Q=N(h),Z=N(R),J=N(D),ee=N(Object.seal(a)),ne=(e,t)=>{const n=M(e);if(n){if(!t)return!0;{if(n.attributes[t])return!0;const e=n.attributePatterns;if(e){let n=e.length;for(;n--;)if(e[n].pattern.test(t))return!0}}}return!1},oe=e=>Se($(),e),re=e=>!Xe(e,"#")&&ne(e)&&!oe(e),ae=N(s);return e.valid_elements?(O(e.valid_elements),Aa(d,((e,t)=>{o[t]=e.children}))):(Aa(d,((e,t)=>{n[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},o[t]=e.children})),Aa(va("strong/b em/i"),(e=>{const t=va(e,"/");n[t[1]].outputName=t[0]})),Aa(A,((t,o)=>{n[o]&&(e.padd_empty_block_inline_children&&(n[o].paddInEmptyBlock=!0),n[o].removeEmpty=!0)})),Aa(va("ol ul blockquote a table tbody"),(e=>{n[e]&&(n[e].removeEmpty=!0)})),Aa(va("p h1 h2 h3 h4 h5 h6 th td pre div address caption li summary"),(e=>{n[e]&&(n[e].paddEmpty=!0)})),Aa(va("span"),(e=>{n[e].removeEmptyAttrs=!0}))),delete n.svg,P(e.custom_elements),L(e.valid_children),T(e.extended_valid_elements),L("+ol[ul|ol],+ul[ul|ol]"),Aa({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},((e,t)=>{n[t]&&(n[t].parentsRequired=va(e))})),e.invalid_elements&&Aa(Da(e.invalid_elements),(e=>{n[e]&&delete n[e]})),M("span")||T("span[!data-mce-type|*]"),{type:l,children:o,elements:n,getValidStyles:F,getValidClasses:z,getBlockElements:$,getInvalidStyles:U,getVoidElements:K,getTextBlockElements:H,getTextInlineElements:W,getBoolAttrs:j,getElementRule:M,getSelfClosingElements:Y,getNonEmptyElements:G,getMoveCaretBeforeOnEnterElements:X,getWhitespaceElements:Q,getTransparentElements:Z,getSpecialElements:ee,isValidChild:(e,t)=>{const n=o[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:ne,isBlock:oe,isInline:re,isWrapper:e=>Se(J(),e)||re(e),getCustomElements:ae,addValidElements:T,setValidElements:O,addCustomElements:P,addValidChildren:L}},La=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Ma=e=>(e=>{return{value:(t=e,Ye(t,"#").toUpperCase())};var t})(La(e.red)+La(e.green)+La(e.blue)),Ia=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,Fa=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,Ua=(e,t,n,o)=>((e,t,n,o)=>({red:e,green:t,blue:n,alpha:o}))(parseInt(e,10),parseInt(t,10),parseInt(n,10),parseFloat(o)),za=e=>{const t=Ia.exec(e);if(null!==t)return I.some(Ua(t[1],t[2],t[3],"1"));const n=Fa.exec(e);return null!==n?I.some(Ua(n[1],n[2],n[3],n[4])):I.none()},ja=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,$a=e=>za(e).map(Ma).map((e=>"#"+e.value)).getOr(e),Ha=(e={},t)=>{const n=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,o=/\s*([^:]+):\s*([^;]+);?/g,r=/\s+$/,s={};let a,i;const l=lt;t&&(a=t.getValidStyles(),i=t.getInvalidStyles());const d="\\\" \\' \\; \\: ; : \ufeff".split(" ");for(let e=0;e<d.length;e++)s[d[e]]=l+e,s[l+e]=d[e];const c={parse:t=>{const a={};let i=!1;const d=e.url_converter,m=e.url_converter_scope||c,u=(e,t,n)=>{const o=a[e+"-top"+t];if(!o)return;const r=a[e+"-right"+t];if(!r)return;const s=a[e+"-bottom"+t];if(!s)return;const i=a[e+"-left"+t];if(!i)return;const l=[o,r,s,i];let d=l.length-1;for(;d--&&l[d]===l[d+1];);d>-1&&n||(a[e+t]=-1===d?l[0]:l.join(" "),delete a[e+"-top"+t],delete a[e+"-right"+t],delete a[e+"-bottom"+t],delete a[e+"-left"+t])},f=e=>{const t=a[e];if(!t)return;const n=t.indexOf(",")>-1?[t]:t.split(" ");let o=n.length;for(;o--;)if(n[o]!==n[0])return!1;return a[e]=n[0],!0},g=e=>(i=!0,s[e]),p=(e,t)=>(i&&(e=e.replace(/\uFEFF[0-9]/g,(e=>s[e]))),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e),h=e=>String.fromCharCode(parseInt(e.slice(1),16)),b=e=>e.replace(/\\[0-9a-f]+/gi,h),v=(t,n,o,r,s,a)=>{if(s=s||a)return"'"+(s=p(s)).replace(/\'/g,"\\'")+"'";if(n=p(n||o||r||""),!e.allow_script_urls){const t=n.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(t))return"";if(!e.allow_svg_data_urls&&/^data:image\/svg/i.test(t))return""}return d&&(n=d.call(m,n,"style")),"url('"+n.replace(/\'/g,"\\'")+"')"};if(t){let s;for(t=(t=t.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,g).replace(/\"[^\"]+\"|\'[^\']+\'/g,(e=>e.replace(/[;:]/g,g)));s=o.exec(t);){o.lastIndex=s.index+s[0].length;let t=s[1].replace(r,"").toLowerCase(),d=s[2].replace(r,"");if(t&&d){if(t=b(t),d=b(d),-1!==t.indexOf(l)||-1!==t.indexOf('"'))continue;if(!e.allow_script_urls&&("behavior"===t||/expression\s*\(|\/\*|\*\//.test(d)))continue;"font-weight"===t&&"700"===d?d="bold":"color"!==t&&"background-color"!==t||(d=d.toLowerCase()),"rgb"==(E=d,Ia.test(E)?"rgb":Fa.test(E)?"rgba":"other")&&za(d).each((e=>{d=$a(ja(e)).toLowerCase()})),d=d.replace(n,v),a[t]=i?p(d,!0):d}}u("border","",!0),u("border","-width"),u("border","-color"),u("border","-style"),u("padding",""),u("margin",""),C="border-style",w="border-color",f(y="border-width")&&f(C)&&f(w)&&(a.border=a[y]+" "+a[C]+" "+a[w],delete a[y],delete a[C],delete a[w]),"medium none"===a.border&&delete a.border,"none"===a["border-image"]&&delete a["border-image"]}var y,C,w,E;return a},serialize:(e,t)=>{let n="";const o=(t,o)=>{const r=o[t];if(r)for(let t=0,o=r.length;t<o;t++){const o=r[t],s=e[o];s&&(n+=(n.length>0?" ":"")+o+": "+s+";")}};return t&&a?(o("*",a),o(t,a)):pe(e,((e,o)=>{e&&((e,t)=>{if(!i||!t)return!0;let n=i["*"];return!(n&&n[e]||(n=i[t],n&&n[e]))})(o,t)&&(n+=(n.length>0?" ":"")+o+": "+e+";")})),n}};return c},Va={keyLocation:!0,layerX:!0,layerY:!0,returnValue:!0,webkitMovementX:!0,webkitMovementY:!0,keyIdentifier:!0,mozPressure:!0},qa=(e,t)=>{const n=null!=t?t:{};for(const t in e)Se(Va,t)||(n[t]=e[t]);return C(e.composedPath)&&(n.composedPath=()=>e.composedPath()),C(e.getModifierState)&&(n.getModifierState=t=>e.getModifierState(t)),C(e.getTargetRanges)&&(n.getTargetRanges=()=>e.getTargetRanges()),n},Wa=(e,t,n,o)=>{var r;const s=qa(t,o);return s.type=e,y(s.target)&&(s.target=null!==(r=s.srcElement)&&void 0!==r?r:n),(e=>y(e.preventDefault)||(e=>e instanceof Event||w(e.initEvent))(e))(t)&&(s.preventDefault=()=>{s.defaultPrevented=!0,s.isDefaultPrevented=M,w(t.preventDefault)&&t.preventDefault()},s.stopPropagation=()=>{s.cancelBubble=!0,s.isPropagationStopped=M,w(t.stopPropagation)&&t.stopPropagation()},s.stopImmediatePropagation=()=>{s.isImmediatePropagationStopped=M,s.stopPropagation()},(e=>e.isDefaultPrevented===M||e.isDefaultPrevented===L)(s)||(s.isDefaultPrevented=!0===s.defaultPrevented?M:L,s.isPropagationStopped=!0===s.cancelBubble?M:L,s.isImmediatePropagationStopped=L)),s},Ka=/^(?:mouse|contextmenu)|click/,Ya=(e,t,n,o)=>{e.addEventListener(t,n,o||!1)},Ga=(e,t,n,o)=>{e.removeEventListener(t,n,o||!1)},Xa=(e,t)=>{const n=Wa(e.type,e,document,t);if((e=>C(e)&&Ka.test(e.type))(e)&&v(e.pageX)&&!v(e.clientX)){const t=n.target.ownerDocument||document,o=t.documentElement,r=t.body,s=n;s.pageX=e.clientX+(o&&o.scrollLeft||r&&r.scrollLeft||0)-(o&&o.clientLeft||r&&r.clientLeft||0),s.pageY=e.clientY+(o&&o.scrollTop||r&&r.scrollTop||0)-(o&&o.clientTop||r&&r.clientTop||0)}return n},Qa=(e,t,n)=>{const o=e.document,r={type:"ready"};if(n.domLoaded)return void t(r);const s=()=>{Ga(e,"DOMContentLoaded",s),Ga(e,"load",s),n.domLoaded||(n.domLoaded=!0,t(r)),e=null};"complete"===o.readyState||"interactive"===o.readyState&&o.body?s():Ya(e,"DOMContentLoaded",s),n.domLoaded||Ya(e,"load",s)};class Za{constructor(){this.domLoaded=!1,this.events={},this.count=1,this.expando="mce-data-"+(+new Date).toString(32),this.hasFocusIn="onfocusin"in document.documentElement,this.count=1}bind(e,t,n,o){const r=this;let s;const a=window,i=e=>{r.executeHandlers(Xa(e||a.event),l)};if(!e||ss(e)||ls(e))return n;let l;e[r.expando]?l=e[r.expando]:(l=r.count++,e[r.expando]=l,r.events[l]={}),o=o||e;const d=t.split(" ");let c=d.length;for(;c--;){let t=d[c],m=i,u=!1,f=!1;"DOMContentLoaded"===t&&(t="ready"),r.domLoaded&&"ready"===t&&"complete"===e.readyState?n.call(o,Xa({type:t})):(r.hasFocusIn||"focusin"!==t&&"focusout"!==t||(u=!0,f="focusin"===t?"focus":"blur",m=e=>{const t=Xa(e||a.event);t.type="focus"===t.type?"focusin":"focusout",r.executeHandlers(t,l)}),s=r.events[l][t],s?"ready"===t&&r.domLoaded?n(Xa({type:t})):s.push({func:n,scope:o}):(r.events[l][t]=s=[{func:n,scope:o}],s.fakeName=f,s.capture=u,s.nativeHandler=m,"ready"===t?Qa(e,m,r):Ya(e,f||t,m,u)))}return e=s=null,n}unbind(e,t,n){if(!e||ss(e)||ls(e))return this;const o=e[this.expando];if(o){let r=this.events[o];if(t){const o=t.split(" ");let s=o.length;for(;s--;){const t=o[s],a=r[t];if(a){if(n){let e=a.length;for(;e--;)if(a[e].func===n){const n=a.nativeHandler,o=a.fakeName,s=a.capture,i=a.slice(0,e).concat(a.slice(e+1));i.nativeHandler=n,i.fakeName=o,i.capture=s,r[t]=i}}n&&0!==a.length||(delete r[t],Ga(e,a.fakeName||t,a.nativeHandler,a.capture))}}}else pe(r,((t,n)=>{Ga(e,t.fakeName||n,t.nativeHandler,t.capture)})),r={};for(const e in r)if(Se(r,e))return this;delete this.events[o];try{delete e[this.expando]}catch(t){e[this.expando]=null}}return this}fire(e,t,n){return this.dispatch(e,t,n)}dispatch(e,t,n){if(!e||ss(e)||ls(e))return this;const o=Xa({type:t,target:e},n);do{const t=e[this.expando];t&&this.executeHandlers(o,t),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow}while(e&&!o.isPropagationStopped());return this}clean(e){if(!e||ss(e)||ls(e))return this;if(e[this.expando]&&this.unbind(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName){this.unbind(e);const t=e.getElementsByTagName("*");let n=t.length;for(;n--;)(e=t[n])[this.expando]&&this.unbind(e)}return this}destroy(){this.events={}}cancel(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}executeHandlers(e,t){const n=this.events[t],o=n&&n[e.type];if(o)for(let t=0,n=o.length;t<n;t++){const n=o[t];if(n&&!1===n.func.call(n.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return}}}Za.Event=new Za;const Ja=dn.each,ei=dn.grep,ti="data-mce-style",ni=dn.makeMap("fill-opacity font-weight line-height opacity orphans widows z-index zoom"," "),oi=(e,t,n)=>{y(n)||""===n?wo(e,t):ho(e,t,n)},ri=e=>e.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase())),si=(e,t)=>{let n=0;if(e)for(let o=e.nodeType,r=e.previousSibling;r;r=r.previousSibling){const e=r.nodeType;(!t||!ss(r)||e!==o&&r.data.length)&&(n++,o=e)}return n},ai=(e,t)=>{const n=vo(t,"style"),o=e.serialize(e.parse(n),En(t));oi(t,ti,o)},ii=(e,t,n)=>{const o=ri(t);y(n)||""===n?Ho(e,o):Io(e,o,((e,t)=>E(e)?Se(ni,t)?e+"":e+"px":e)(n,o))},li=(e,t={})=>{const n={},o=window,r={};let s=0;const a=ea.forElement(mn.fromDom(e),{contentCssCors:t.contentCssCors,referrerPolicy:t.referrerPolicy,crossOrigin:e=>{const n=t.crossOrigin;return w(n)?n(e,"stylesheet"):void 0}}),i=[],l=t.schema?t.schema:Pa({}),d=Ha({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope},t.schema),c=t.ownEvents?new Za:Za.Event,m=l.getBlockElements(),f=t=>t&&e&&u(t)?e.getElementById(t):t,h=e=>{const t=f(e);return C(t)?mn.fromDom(t):null},b=(e,t,n="")=>{let o;const r=h(e);if(C(r)&&Nn(r)){const e=G[t];o=e&&e.get?e.get(r.dom,t):vo(r,t)}return C(o)?o:n},v=e=>{const t=f(e);return y(t)?[]:t.attributes},E=(e,n,o)=>{O(e,(e=>{if(Xr(e)){const r=mn.fromDom(e),s=""===o?null:o,a=vo(r,n),i=G[n];i&&i.set?i.set(r.dom,s,n):oi(r,n,s),a!==s&&t.onSetAttrib&&t.onSetAttrib({attrElm:r.dom,attrName:n,attrValue:s})}}))},x=()=>t.root_element||e.body,_=(t,n)=>((e,t,n)=>{let o=0,r=0;const s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===Uo(mn.fromDom(e),"position")){const n=t.getBoundingClientRect();return o=n.left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,r=n.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop,{x:o,y:r}}let a=t;for(;a&&a!==n&&a.nodeType&&!Zs(a,n);){const e=a;o+=e.offsetLeft||0,r+=e.offsetTop||0,a=e.offsetParent}for(a=t.parentNode;a&&a!==n&&a.nodeType&&!Zs(a,n);)o-=a.scrollLeft||0,r-=a.scrollTop||0,a=a.parentNode;r+=(e=>Xs.isFirefox()&&"table"===En(e)?Qs(Hn(e)).filter((e=>"caption"===En(e))).bind((e=>Qs($n(e)).map((t=>{const n=t.dom.offsetTop,o=e.dom.offsetTop,r=e.dom.offsetHeight;return n<=o?-r:0})))).getOr(0):0)(mn.fromDom(t))}return{x:o,y:r}})(e.body,f(t),n),k=(e,t,n)=>{const o=f(e);var r;if(!y(o)&&(Qr(o)||Xr(r=o)&&"http://www.w3.org/2000/svg"===r.namespaceURI))return n?Uo(mn.fromDom(o),ri(t)):("float"===(t=t.replace(/-(\D)/g,((e,t)=>t.toUpperCase())))&&(t="cssFloat"),o.style?o.style[t]:void 0)},A=e=>{const t=f(e);if(!t)return{w:0,h:0};let n=k(t,"width"),o=k(t,"height");return n&&-1!==n.indexOf("px")||(n="0"),o&&-1!==o.indexOf("px")||(o="0"),{w:parseInt(n,10)||t.offsetWidth||t.clientWidth,h:parseInt(o,10)||t.offsetHeight||t.clientHeight}},R=(e,t)=>{if(!e)return!1;const n=p(e)?e:[e];return H(n,(e=>hn(mn.fromDom(e),t)))},D=(e,t,n,o)=>{const r=[];let s=f(e);o=void 0===o;const a=n||("BODY"!==x().nodeName?x().parentNode:null);if(u(t))if("*"===t)t=Xr;else{const e=t;t=t=>R(t,e)}for(;s&&!(s===a||y(s.nodeType)||ds(s)||cs(s));){if(!t||t(s)){if(!o)return[s];r.push(s)}s=s.parentNode}return o?r:null},T=(e,t,n)=>{let o=t;if(e){u(t)&&(o=e=>R(e,t));for(let t=e[n];t;t=t[n])if(w(o)&&o(t))return t}return null},O=function(e,t,n){const o=null!=n?n:this;if(p(e)){const n=[];return Ja(e,((e,r)=>{const s=f(e);s&&n.push(t.call(o,s,r))})),n}{const n=f(e);return!!n&&t.call(o,n)}},B=(e,t)=>{O(e,(e=>{pe(t,((t,n)=>{E(e,n,t)}))}))},P=(e,t)=>{O(e,(e=>{const n=mn.fromDom(e);Bo(n,t)}))},L=(t,n,o,r,s)=>O(t,(t=>{const a=u(n)?e.createElement(n):n;return C(o)&&B(a,o),r&&(!u(r)&&r.nodeType?a.appendChild(r):u(r)&&P(a,r)),s?a:t.appendChild(a)})),M=(t,n,o)=>L(e.createElement(t),t,n,o,!0),I=ba.encodeAllRaw,F=(e,t)=>O(e,(e=>{const n=mn.fromDom(e);return t&&q(Hn(n),(e=>{An(e)&&0===e.dom.length?_o(e):io(n,e)})),_o(n),n.dom})),U=(e,t,n)=>{O(e,(e=>{if(Xr(e)){const o=mn.fromDom(e),r=t.split(" ");q(r,(e=>{C(n)?(n?gr:hr)(o,e):((e,t)=>{const n=cr(e)?e.dom.classList.toggle(t):((e,t)=>$(mr(e),t)?fr(e,t):ur(e,t))(e,t);pr(e)})(o,e)}))}}))},z=(e,t,n)=>O(t,(o=>{var r;const s=p(t)?e.cloneNode(!0):e;return n&&Ja(ei(o.childNodes),(e=>{s.appendChild(e)})),null===(r=o.parentNode)||void 0===r||r.replaceChild(s,o),o})),j=()=>e.createRange(),V=(n,r,s,a)=>{if(p(n)){let e=n.length;const t=[];for(;e--;)t[e]=V(n[e],r,s,a);return t}return!t.collect||n!==e&&n!==o||i.push([n,r,s,a]),c.bind(n,r,s,a||Y)},W=(t,n,r)=>{if(p(t)){let e=t.length;const o=[];for(;e--;)o[e]=W(t[e],n,r);return o}if(i.length>0&&(t===e||t===o)){let e=i.length;for(;e--;){const[o,s,a]=i[e];t!==o||n&&n!==s||r&&r!==a||c.unbind(o,s,a)}}return c.unbind(t,n,r)},K=e=>{if(e&&Qr(e)){const t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},Y={doc:e,settings:t,win:o,files:r,stdMode:!0,boxModel:!0,styleSheetLoader:a,boundEvents:i,styles:d,schema:l,events:c,isBlock:e=>u(e)?Se(m,e):Xr(e)&&(Se(m,e.nodeName)||Ks(l,e)),root:null,clone:(e,t)=>e.cloneNode(t),getRoot:x,getViewPort:e=>{const t=$r(e);return{x:t.x,y:t.y,w:t.width,h:t.height}},getRect:e=>{const t=f(e),n=_(t),o=A(t);return{x:n.x,y:n.y,w:o.w,h:o.h}},getSize:A,getParent:(e,t,n)=>{const o=D(e,t,n,!1);return o&&o.length>0?o[0]:null},getParents:D,get:f,getNext:(e,t)=>T(e,t,"nextSibling"),getPrev:(e,t)=>T(e,t,"previousSibling"),select:(n,o)=>{var r,s;const a=null!==(s=null!==(r=f(o))&&void 0!==r?r:t.root_element)&&void 0!==s?s:e;return w(a.querySelectorAll)?ce(a.querySelectorAll(n)):[]},is:R,add:L,create:M,createHTML:(e,t,n="")=>{let o="<"+e;for(const e in t)_e(t,e)&&(o+=" "+e+'="'+I(t[e])+'"');return ot(n)&&Se(l.getVoidElements(),e)?o+" />":o+">"+n+"</"+e+">"},createFragment:t=>{const n=e.createElement("div"),o=e.createDocumentFragment();let r;for(o.appendChild(n),t&&(n.innerHTML=t);r=n.firstChild;)o.appendChild(r);return o.removeChild(n),o},remove:F,setStyle:(e,n,o)=>{O(e,(e=>{const r=mn.fromDom(e);ii(r,n,o),t.update_styles&&ai(d,r)}))},getStyle:k,setStyles:(e,n)=>{O(e,(e=>{const o=mn.fromDom(e);pe(n,((e,t)=>{ii(o,t,e)})),t.update_styles&&ai(d,o)}))},removeAllAttribs:e=>O(e,(e=>{const t=e.attributes;for(let n=t.length-1;n>=0;n--)e.removeAttributeNode(t.item(n))})),setAttrib:E,setAttribs:B,getAttrib:b,getPos:_,parseStyle:e=>d.parse(e),serializeStyle:(e,t)=>d.serialize(e,t),addStyle:t=>{if(Y!==li.DOM&&e===document){if(n[t])return;n[t]=!0}let o=e.getElementById("mceDefaultStyles");if(!o){o=e.createElement("style"),o.id="mceDefaultStyles",o.type="text/css";const t=e.head;t.firstChild?t.insertBefore(o,t.firstChild):t.appendChild(o)}o.styleSheet?o.styleSheet.cssText+=t:o.appendChild(e.createTextNode(t))},loadCSS:e=>{e||(e=""),q(e.split(","),(e=>{r[e]=!0,a.load(e).catch(S)}))},addClass:(e,t)=>{U(e,t,!0)},removeClass:(e,t)=>{U(e,t,!1)},hasClass:(e,t)=>{const n=h(e),o=t.split(" ");return C(n)&&ne(o,(e=>br(n,e)))},toggleClass:U,show:e=>{O(e,(e=>Ho(mn.fromDom(e),"display")))},hide:e=>{O(e,(e=>Io(mn.fromDom(e),"display","none")))},isHidden:e=>{const t=h(e);return C(t)&&Ue(jo(t,"display"),"none")},uniqueId:e=>(e||"mce_")+s++,setHTML:P,getOuterHTML:e=>{const t=h(e);return C(t)?Xr(t.dom)?t.dom.outerHTML:(e=>{const t=mn.fromTag("div"),n=mn.fromDom(e.dom.cloneNode(!0));return mo(t,n),Oo(t)})(t):""},setOuterHTML:(e,t)=>{O(e,(e=>{Xr(e)&&(e.outerHTML=t)}))},decode:ba.decode,encode:I,insertAfter:(e,t)=>{const n=f(t);return O(e,(e=>{const t=null==n?void 0:n.parentNode,o=null==n?void 0:n.nextSibling;return t&&(o?t.insertBefore(e,o):t.appendChild(e)),e}))},replace:z,rename:(e,t)=>{if(e.nodeName!==t.toUpperCase()){const n=M(t);return Ja(v(e),(t=>{E(n,t.nodeName,b(e,t.nodeName))})),z(n,e,!0),n}return e},findCommonAncestor:(e,t)=>{let n=e;for(;n;){let e=t;for(;e&&n!==e;)e=e.parentNode;if(n===e)break;n=n.parentNode}return!n&&e.ownerDocument?e.ownerDocument.documentElement:n},run:O,getAttribs:v,isEmpty:(e,t,n)=>{if(g(t)){const o=e=>{const n=e.nodeName.toLowerCase();return Boolean(t[n])};return As(l,e,{...n,isContent:o})}return As(l,e,n)},createRng:j,nodeIndex:si,split:(e,t,n)=>{let o,r,s=j();if(e&&t&&e.parentNode&&t.parentNode){const a=e.parentNode;return s.setStart(a,si(e)),s.setEnd(t.parentNode,si(t)),o=s.extractContents(),s=j(),s.setStart(t.parentNode,si(t)+1),s.setEnd(a,si(e)+1),r=s.extractContents(),a.insertBefore(oa(Y,o,l),e),n?a.insertBefore(n,e):a.insertBefore(t,e),a.insertBefore(oa(Y,r,l),e),F(e),n||t}},bind:V,unbind:W,fire:(e,t,n)=>c.dispatch(e,t,n),dispatch:(e,t,n)=>c.dispatch(e,t,n),getContentEditable:K,getContentEditableParent:e=>{const t=x();let n=null;for(let o=e;o&&o!==t&&(n=K(o),null===n);o=o.parentNode);return n},isEditable:e=>{if(C(e)){const t=Xr(e)?e:e.parentElement;return C(t)&&Qr(t)&&vr(mn.fromDom(t))}return!1},destroy:()=>{if(i.length>0){let e=i.length;for(;e--;){const[t,n,o]=i[e];c.unbind(t,n,o)}}pe(r,((e,t)=>{a.unload(t),delete r[t]}))},isChildOf:(e,t)=>e===t||t.contains(e),dumpRng:e=>"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset},G=((e,t,n)=>{const o=t.keep_values,r={set:(e,o,r)=>{const s=mn.fromDom(e);w(t.url_converter)&&C(o)&&(o=t.url_converter.call(t.url_converter_scope||n(),String(o),r,e)),oi(s,"data-mce-"+r,o),oi(s,r,o)},get:(e,t)=>{const n=mn.fromDom(e);return vo(n,"data-mce-"+t)||vo(n,t)}},s={style:{set:(t,n)=>{const r=mn.fromDom(t);o&&oi(r,ti,n),wo(r,"style"),u(n)&&Fo(r,e.parse(n))},get:t=>{const n=mn.fromDom(t),o=vo(n,ti)||vo(n,"style");return e.serialize(e.parse(o),En(n))}}};return o&&(s.href=s.src=r),s})(d,t,N(Y));return Y};li.DOM=li(document),li.nodeIndex=si;const di=li.DOM;class ci{constructor(e={}){this.states={},this.queue=[],this.scriptLoadedCallbacks={},this.queueLoadedCallbacks=[],this.loading=!1,this.settings=e}_setReferrerPolicy(e){this.settings.referrerPolicy=e}_setCrossOrigin(e){this.settings.crossOrigin=e}loadScript(e){return new Promise(((t,n)=>{const o=di;let r;const s=()=>{o.remove(a),r&&(r.onerror=r.onload=r=null)},a=o.uniqueId();r=document.createElement("script"),r.id=a,r.type="text/javascript",r.src=dn._addCacheSuffix(e),this.settings.referrerPolicy&&o.setAttrib(r,"referrerpolicy",this.settings.referrerPolicy);const i=this.settings.crossOrigin;if(w(i)){const t=i(e);void 0!==t&&o.setAttrib(r,"crossorigin",t)}r.onload=()=>{s(),t()},r.onerror=()=>{s(),n("Failed to load script: "+e)},(document.getElementsByTagName("head")[0]||document.body).appendChild(r)}))}isDone(e){return 2===this.states[e]}markDone(e){this.states[e]=2}add(e){const t=this;return t.queue.push(e),void 0===t.states[e]&&(t.states[e]=0),new Promise(((n,o)=>{t.scriptLoadedCallbacks[e]||(t.scriptLoadedCallbacks[e]=[]),t.scriptLoadedCallbacks[e].push({resolve:n,reject:o})}))}load(e){return this.add(e)}remove(e){delete this.states[e],delete this.scriptLoadedCallbacks[e]}loadQueue(){const e=this.queue;return this.queue=[],this.loadScripts(e)}loadScripts(e){const t=this,n=(e,n)=>{xe(t.scriptLoadedCallbacks,n).each((t=>{q(t,(t=>t[e](n)))})),delete t.scriptLoadedCallbacks[n]},o=e=>{const t=Y(e,(e=>"rejected"===e.status));return t.length>0?Promise.reject(te(t,(({reason:e})=>p(e)?e:[e]))):Promise.resolve()},r=e=>Promise.allSettled(V(e,(e=>2===t.states[e]?(n("resolve",e),Promise.resolve()):3===t.states[e]?(n("reject",e),Promise.reject(e)):(t.states[e]=1,t.loadScript(e).then((()=>{t.states[e]=2,n("resolve",e);const s=t.queue;return s.length>0?(t.queue=[],r(s).then(o)):Promise.resolve()}),(()=>(t.states[e]=3,n("reject",e),Promise.reject(e)))))))),s=e=>(t.loading=!0,r(e).then((e=>{t.loading=!1;const n=t.queueLoadedCallbacks.shift();return I.from(n).each(P),o(e)}))),a=mt(e);return t.loading?new Promise(((e,n)=>{t.queueLoadedCallbacks.push((()=>{s(a).then(e,n)}))})):s(a)}}ci.ScriptLoader=new ci;const mi={},ui=Ne("en"),fi=()=>xe(mi,ui.get()),gi={getData:()=>he(mi,(e=>({...e}))),setCode:e=>{e&&ui.set(e)},getCode:()=>ui.get(),add:(e,t)=>{let n=mi[e];n||(mi[e]=n={});const o=V(fe(t),(e=>e.toLowerCase()));pe(t,((e,r)=>{const s=r.toLowerCase();s!==r&&((e,t)=>{const n=e.indexOf(t);return-1!==n&&e.indexOf(t,n+1)>n})(o,s)?(Se(t,s)||(n[s]=e),n[r]=e):n[s]=e}))},translate:e=>{const t=fi().getOr({}),n=e=>w(e)?Object.prototype.toString.call(e):o(e)?"":""+e,o=e=>""===e||null==e,r=e=>{const o=n(e);return Se(t,o)?n(t[o]):xe(t,o.toLowerCase()).map(n).getOr(o)},s=e=>e.replace(/{context:\w+}$/,""),a=e=>e.replaceAll("...","\u2026");if(o(e))return"";if(f(i=e)&&Se(i,"raw"))return a(n(e.raw));var i;if((e=>p(e)&&e.length>1)(e)){const t=e.slice(1);return a(s(r(e[0]).replace(/\{([0-9]+)\}/g,((e,o)=>Se(t,o)?n(t[o]):e))))}return a(s(r(e)))},isRtl:()=>fi().bind((e=>xe(e,"_dir"))).exists((e=>"rtl"===e)),hasCode:e=>Se(mi,e)},pi=()=>{const e=[],t={},n={},o=[],r=(e,t)=>{const n=Y(o,(n=>n.name===e&&n.state===t));q(n,(e=>e.resolve()))},s=e=>Se(t,e),a=(e,n)=>{const o=gi.getCode();!o||n&&-1===(","+(n||"")+",").indexOf(","+o+",")||ci.ScriptLoader.add(t[e]+"/langs/"+o+".js")},i=(e,t="added")=>"added"===t&&(e=>Se(n,e))(e)||"loaded"===t&&s(e)?Promise.resolve():new Promise((n=>{o.push({name:e,state:t,resolve:n})}));return{items:e,urls:t,lookup:n,get:e=>{if(n[e])return n[e].instance},requireLangPack:(e,t)=>{!1!==pi.languageLoad&&(s(e)?a(e,t):i(e,"loaded").then((()=>a(e,t))))},add:(t,o)=>(e.push(o),n[t]={instance:o},r(t,"added"),o),remove:e=>{delete t[e],delete n[e]},createUrl:(e,t)=>u(t)?u(e)?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}:t,load:(e,o)=>{if(t[e])return Promise.resolve();let s=u(o)?o:o.prefix+o.resource+o.suffix;0!==s.indexOf("/")&&-1===s.indexOf("://")&&(s=pi.baseURL+"/"+s),t[e]=s.substring(0,s.lastIndexOf("/"));const a=()=>(r(e,"loaded"),Promise.resolve());return n[e]?a():ci.ScriptLoader.add(s).then(a)},waitFor:i}};pi.languageLoad=!0,pi.baseURL="",pi.PluginManager=pi(),pi.ThemeManager=pi(),pi.ModelManager=pi();const hi=N("mce-annotation"),bi=N("data-mce-annotation"),vi=N("data-mce-annotation-uid"),yi=N("data-mce-annotation-active"),Ci=N("data-mce-annotation-classes"),wi=N("data-mce-annotation-attrs"),Ei=e=>t=>vn(t,e),xi=(e,t)=>{const n=e.selection.getRng(),o=mn.fromDom(n.startContainer),r=mn.fromDom(e.getBody()),s=t.fold((()=>"."+hi()),(e=>`[${bi()}="${e}"]`)),a=Vn(o,n.startOffset).getOr(o);return dr(a,s,Ei(r)).bind((t=>yo(t,`${vi()}`).bind((n=>yo(t,`${bi()}`).map((t=>{const o=_i(e,n);return{uid:n,name:t,elements:o}}))))))},Si=(e,t)=>Co(e,"data-mce-bogus")||((e,t,n)=>ir(e,'[data-mce-bogus="all"]',n).isSome())(e,0,Ei(t)),_i=(e,t)=>{const n=mn.fromDom(e.getBody()),o=xr(n,`[${vi()}="${t}"]`);return Y(o,(e=>!Si(e,n)))},ki=(e,t)=>{const n=mn.fromDom(e.getBody()),o=xr(n,`[${bi()}="${t}"]`),r={};return q(o,(e=>{if(!Si(e,n)){const t=vo(e,vi()),n=xe(r,t).getOr([]);r[t]=n.concat([e])}})),r},Ni=(e,t,n=L)=>{const o=new Hr(e,t),r=e=>{let t;do{t=o[e]()}while(t&&!ss(t)&&!n(t));return I.from(t).filter(ss)};return{current:()=>I.from(o.current()).filter(ss),next:()=>r("next"),prev:()=>r("prev"),prev2:()=>r("prev2")}},Ai=(e,t)=>{const n=t||(t=>e.isBlock(t)||ms(t)||ps(t)),o=(e,t,n,r)=>{if(ss(e)){const n=r(e,t,e.data);if(-1!==n)return I.some({container:e,offset:n})}return n().bind((e=>o(e.container,e.offset,n,r)))};return{backwards:(t,r,s,a)=>{const i=Ni(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.prev().map((e=>({container:e,offset:e.length})))),s).getOrNull()},forwards:(t,r,s,a)=>{const i=Ni(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.next().map((e=>({container:e,offset:0})))),s).getOrNull()}}},Ri=e=>{let t;return n=>(t=t||se(e,M),Se(t,En(n)))},Di=e=>Nn(e)&&"br"===En(e),Ti=Ri(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),Oi=Ri(["ul","ol","dl"]),Bi=Ri(["li","dd","dt"]),Pi=Ri(["thead","tbody","tfoot"]),Li=Ri(["td","th"]),Mi=Ri(["pre","script","textarea","style"]),Ii=()=>{const e=mn.fromTag("br");return ho(e,"data-mce-bogus","1"),e},Fi=e=>{So(e),mo(e,Ii())},Ui=lt,zi=ct,ji=e=>e.replace(/\uFEFF/g,""),$i=Xr,Hi=ss,Vi=e=>(Hi(e)&&(e=e.parentNode),$i(e)&&e.hasAttribute("data-mce-caret")),qi=e=>Hi(e)&&zi(e.data),Wi=e=>Vi(e)||qi(e),Ki=e=>e.firstChild!==e.lastChild||!ms(e.firstChild),Yi=e=>{const t=e.container();return!!ss(t)&&(t.data.charAt(e.offset())===Ui||e.isAtStart()&&qi(t.previousSibling))},Gi=e=>{const t=e.container();return!!ss(t)&&(t.data.charAt(e.offset()-1)===Ui||e.isAtEnd()&&qi(t.nextSibling))},Xi=e=>Hi(e)&&e.data[0]===Ui,Qi=e=>Hi(e)&&e.data[e.data.length-1]===Ui,Zi=e=>e&&e.hasAttribute("data-mce-caret")?((e=>{var t;const n=e.getElementsByTagName("br"),o=n[n.length-1];ts(o)&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o))})(e),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("data-mce-style"),e.removeAttribute("_moz_abspos"),e):null,Ji=e=>Vi(e.startContainer),el=Math.round,tl=e=>e?{left:el(e.left),top:el(e.top),bottom:el(e.bottom),right:el(e.right),width:el(e.width),height:el(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0},nl=(e,t)=>(e=tl(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e),ol=(e,t,n)=>e>=0&&e<=Math.min(t.height,n.height)/2,rl=(e,t)=>{const n=Math.min(t.height/2,e.height/2);return e.bottom-n<t.top||!(e.top>t.bottom)&&ol(t.top-e.bottom,e,t)},sl=(e,t)=>e.top>t.bottom||!(e.bottom<t.top)&&ol(t.bottom-e.top,e,t),al=(e,t,n)=>{const o=Math.max(Math.min(t,e.left+e.width),e.left),r=Math.max(Math.min(n,e.top+e.height),e.top);return Math.sqrt((t-o)*(t-o)+(n-r)*(n-r))},il=e=>{const t=e.startContainer,n=e.startOffset;return t===e.endContainer&&t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},ll=(e,t)=>{if(Xr(e)&&e.hasChildNodes()){const n=e.childNodes,o=((e,t,n)=>Math.min(Math.max(e,0),n))(t,0,n.length-1);return n[o]}return e},dl=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),cl=e=>u(e)&&e.charCodeAt(0)>=768&&dl.test(e),ml=gs,ul=ps,fl=ms,gl=ss,pl=Jr(["script","style","textarea"]),hl=Jr(["img","input","textarea","hr","iframe","video","audio","object","embed"]),bl=Jr(["table"]),vl=Wi,yl=e=>!vl(e)&&(gl(e)?!pl(e.parentNode):hl(e)||fl(e)||bl(e)||Cl(e)),Cl=e=>!(e=>Xr(e)&&"true"===e.getAttribute("unselectable"))(e)&&ul(e),wl=(e,t)=>yl(e)&&((e,t)=>{for(let n=e.parentNode;n&&n!==t;n=n.parentNode){if(Cl(n))return!1;if(ml(n))return!0}return!0})(e,t),El=Xr,xl=yl,Sl=es("display","block table"),_l=es("float","left right"),kl=((...e)=>t=>{for(let n=0;n<e.length;n++)if(!e[n](t))return!1;return!0})(El,xl,T(_l)),Nl=T(es("white-space","pre pre-line pre-wrap")),Al=ss,Rl=ms,Dl=li.nodeIndex,Tl=(e,t)=>t<0&&Xr(e)&&e.hasChildNodes()?void 0:ll(e,t),Ol=e=>e?e.createRange():li.DOM.createRng(),Bl=e=>u(e)&&/[\r\n\t ]/.test(e),Pl=e=>!!e.setStart&&!!e.setEnd,Ll=e=>{const t=e.startContainer,n=e.startOffset;if(Bl(e.toString())&&Nl(t.parentNode)&&ss(t)){const e=t.data;if(Bl(e[n-1])||Bl(e[n+1]))return!0}return!1},Ml=e=>0===e.left&&0===e.right&&0===e.top&&0===e.bottom,Il=e=>{var t;let n;const o=e.getClientRects();return n=o.length>0?tl(o[0]):tl(e.getBoundingClientRect()),!Pl(e)&&Rl(e)&&Ml(n)?(e=>{const t=e.ownerDocument,n=Ol(t),o=t.createTextNode(dt),r=e.parentNode;r.insertBefore(o,e),n.setStart(o,0),n.setEnd(o,1);const s=tl(n.getBoundingClientRect());return r.removeChild(o),s})(e):Ml(n)&&Pl(e)&&null!==(t=(e=>{const t=e.startContainer,n=e.endContainer,o=e.startOffset,r=e.endOffset;if(t===n&&ss(n)&&0===o&&1===r){const t=e.cloneRange();return t.setEndAfter(n),Il(t)}return null})(e))&&void 0!==t?t:n},Fl=(e,t)=>{const n=nl(e,t);return n.width=1,n.right=n.left+1,n},Ul=(e,t,n)=>{const o=()=>(n||(n=(e=>{const t=[],n=e=>{var n,o;0!==e.height&&(t.length>0&&(n=e,o=t[t.length-1],n.left===o.left&&n.top===o.top&&n.bottom===o.bottom&&n.right===o.right)||t.push(e))},o=(e,t)=>{const o=Ol(e.ownerDocument);if(t<e.data.length){if(cl(e.data[t]))return;if(cl(e.data[t-1])&&(o.setStart(e,t),o.setEnd(e,t+1),!Ll(o)))return void n(Fl(Il(o),!1))}t>0&&(o.setStart(e,t-1),o.setEnd(e,t),Ll(o)||n(Fl(Il(o),!1))),t<e.data.length&&(o.setStart(e,t),o.setEnd(e,t+1),Ll(o)||n(Fl(Il(o),!0)))},r=e.container(),s=e.offset();if(Al(r))return o(r,s),t;if(El(r))if(e.isAtEnd()){const e=Tl(r,s);Al(e)&&o(e,e.data.length),kl(e)&&!Rl(e)&&n(Fl(Il(e),!1))}else{const a=Tl(r,s);if(Al(a)&&o(a,0),kl(a)&&e.isAtEnd())return n(Fl(Il(a),!1)),t;const i=Tl(e.container(),e.offset()-1);kl(i)&&!Rl(i)&&(Sl(i)||Sl(a)||!kl(a))&&n(Fl(Il(i),!1)),kl(a)&&n(Fl(Il(a),!0))}return t})(Ul(e,t))),n);return{container:N(e),offset:N(t),toRange:()=>{const n=Ol(e.ownerDocument);return n.setStart(e,t),n.setEnd(e,t),n},getClientRects:o,isVisible:()=>o().length>0,isAtStart:()=>(Al(e),0===t),isAtEnd:()=>Al(e)?t>=e.data.length:t>=e.childNodes.length,isEqual:n=>n&&e===n.container()&&t===n.offset(),getNode:n=>Tl(e,n?t-1:t)}};Ul.fromRangeStart=e=>Ul(e.startContainer,e.startOffset),Ul.fromRangeEnd=e=>Ul(e.endContainer,e.endOffset),Ul.after=e=>Ul(e.parentNode,Dl(e)+1),Ul.before=e=>Ul(e.parentNode,Dl(e)),Ul.isAbove=(e,t)=>je(le(t.getClientRects()),de(e.getClientRects()),rl).getOr(!1),Ul.isBelow=(e,t)=>je(de(t.getClientRects()),le(e.getClientRects()),sl).getOr(!1),Ul.isAtStart=e=>!!e&&e.isAtStart(),Ul.isAtEnd=e=>!!e&&e.isAtEnd(),Ul.isTextPosition=e=>!!e&&ss(e.container()),Ul.isElementPosition=e=>!Ul.isTextPosition(e);const zl=(e,t)=>{ss(t)&&0===t.data.length&&e.remove(t)},jl=(e,t,n)=>{cs(n)?((e,t,n)=>{const o=I.from(n.firstChild),r=I.from(n.lastChild);t.insertNode(n),o.each((t=>zl(e,t.previousSibling))),r.each((t=>zl(e,t.nextSibling)))})(e,t,n):((e,t,n)=>{t.insertNode(n),zl(e,n.previousSibling),zl(e,n.nextSibling)})(e,t,n)},$l=ss,Hl=ts,Vl=li.nodeIndex,ql=e=>{const t=e.parentNode;return Hl(t)?ql(t):t},Wl=e=>e?bt(e.childNodes,((e,t)=>(Hl(t)&&"BR"!==t.nodeName?e=e.concat(Wl(t)):e.push(t),e)),[]):[],Kl=e=>t=>e===t,Yl=e=>($l(e)?"text()":e.nodeName.toLowerCase())+"["+(e=>{let t,n;t=Wl(ql(e)),n=vt(t,Kl(e),e),t=t.slice(0,n+1);const o=bt(t,((e,n,o)=>($l(n)&&$l(t[o-1])&&e++,e)),0);return t=ht(t,Jr([e.nodeName])),n=vt(t,Kl(e),e),n-o})(e)+"]",Gl=(e,t)=>{let n,o=[],r=t.container(),s=t.offset();if($l(r))n=((e,t)=>{let n=e;for(;(n=n.previousSibling)&&$l(n);)t+=n.data.length;return t})(r,s);else{const e=r.childNodes;s>=e.length?(n="after",s=e.length-1):n="before",r=e[s]}o.push(Yl(r));let a=((e,t)=>{const n=[];for(let o=t.parentNode;o&&o!==e;o=o.parentNode)n.push(o);return n})(e,r);return a=ht(a,T(ts)),o=o.concat(pt(a,(e=>Yl(e)))),o.reverse().join("/")+","+n},Xl=(e,t)=>{if(!t)return null;const n=t.split(","),o=n[0].split("/"),r=n.length>1?n[1]:"before",s=bt(o,((e,t)=>{const n=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t);return n?("text()"===n[1]&&(n[1]="#text"),((e,t,n)=>{let o=Wl(e);return o=ht(o,((e,t)=>!$l(e)||!$l(o[t-1]))),o=ht(o,Jr([t])),o[n]})(e,n[1],parseInt(n[2],10))):null}),e);if(!s)return null;if(!$l(s)&&s.parentNode){let e;return e="after"===r?Vl(s)+1:Vl(s),Ul(s.parentNode,e)}return((e,t)=>{let n=e,o=0;for(;$l(n);){const r=n.data.length;if(t>=o&&t<=o+r){e=n,t-=o;break}if(!$l(n.nextSibling)){e=n,t=r;break}o+=r,n=n.nextSibling}return $l(e)&&t>e.data.length&&(t=e.data.length),Ul(e,t)})(s,parseInt(r,10))},Ql=ps,Zl=(e,t,n,o,r)=>{const s=r?o.startContainer:o.endContainer;let a=r?o.startOffset:o.endOffset;const i=[],l=e.getRoot();if(ss(s))i.push(n?((e,t,n)=>{let o=e(t.data.slice(0,n)).length;for(let n=t.previousSibling;n&&ss(n);n=n.previousSibling)o+=e(n.data).length;return o})(t,s,a):a);else{let t=0;const o=s.childNodes;a>=o.length&&o.length&&(t=1,a=Math.max(0,o.length-1)),i.push(e.nodeIndex(o[a],n)+t)}for(let t=s;t&&t!==l;t=t.parentNode)i.push(e.nodeIndex(t,n));return i},Jl=(e,t,n)=>{let o=0;return dn.each(e.select(t),(e=>"all"===e.getAttribute("data-mce-bogus")?void 0:e!==n&&void o++)),o},ed=(e,t)=>{let n=t?e.startContainer:e.endContainer,o=t?e.startOffset:e.endOffset;if(Xr(n)&&"TR"===n.nodeName){const r=n.childNodes;n=r[Math.min(t?o:o-1,r.length-1)],n&&(o=t?0:n.childNodes.length,t?e.setStart(n,o):e.setEnd(n,o))}},td=e=>(ed(e,!0),ed(e,!1),e),nd=(e,t)=>{if(Xr(e)&&(e=ll(e,t),Ql(e)))return e;if(Wi(e)){ss(e)&&Vi(e)&&(e=e.parentNode);let t=e.previousSibling;if(Ql(t))return t;if(t=e.nextSibling,Ql(t))return t}},od=(e,t,n)=>{const o=n.getNode(),r=n.getRng();if("IMG"===o.nodeName||Ql(o)){const e=o.nodeName;return{name:e,index:Jl(n.dom,e,o)}}const s=(e=>nd(e.startContainer,e.startOffset)||nd(e.endContainer,e.endOffset))(r);if(s){const e=s.tagName;return{name:e,index:Jl(n.dom,e,s)}}return((e,t,n,o)=>{const r=t.dom,s=Zl(r,e,n,o,!0),a=t.isForward(),i=Ji(o)?{isFakeCaret:!0}:{};return t.isCollapsed()?{start:s,forward:a,...i}:{start:s,end:Zl(r,e,n,o,!1),forward:a,...i}})(e,n,t,r)},rd=(e,t,n)=>{const o={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",o,"&#xFEFF;"):e.create("span",o)},sd=(e,t)=>{const n=e.dom;let o=e.getRng();const r=n.uniqueId(),s=e.isCollapsed(),a=e.getNode(),i=a.nodeName,l=e.isForward();if("IMG"===i)return{name:i,index:Jl(n,i,a)};const d=td(o.cloneRange());if(!s){d.collapse(!1);const e=rd(n,r+"_end",t);jl(n,d,e)}o=td(o),o.collapse(!0);const c=rd(n,r+"_start",t);return jl(n,o,c),e.moveToBookmark({id:r,keep:!0,forward:l}),{id:r,forward:l}},ad=D(od,A,!0),id=e=>"inline-command"===e.type||"inline-format"===e.type,ld=e=>"block-command"===e.type||"block-format"===e.type,dd=e=>{var t;const n=t=>De.error({message:t,pattern:e}),o=(t,o,r)=>{if(void 0!==e.format){let r;if(p(e.format)){if(!ne(e.format,u))return n(t+" pattern has non-string items in the `format` array");r=e.format}else{if(!u(e.format))return n(t+" pattern has non-string `format` parameter");r=[e.format]}return De.value(o(r))}return void 0!==e.cmd?u(e.cmd)?De.value(r(e.cmd,e.value)):n(t+" pattern has non-string `cmd` parameter"):n(t+" pattern is missing both `format` and `cmd` parameters")};if(!f(e))return n("Raw pattern is not an object");if(!u(e.start))return n("Raw pattern is missing `start` parameter");if(void 0!==e.end){if(!u(e.end))return n("Inline pattern has non-string `end` parameter");if(0===e.start.length&&0===e.end.length)return n("Inline pattern has empty `start` and `end` parameters");let t=e.start,r=e.end;return 0===r.length&&(r=t,t=""),o("Inline",(e=>({type:"inline-format",start:t,end:r,format:e})),((e,n)=>({type:"inline-command",start:t,end:r,cmd:e,value:n})))}if(void 0!==e.replacement)return u(e.replacement)?0===e.start.length?n("Replacement pattern has empty `start` parameter"):De.value({type:"inline-command",start:"",end:e.start,cmd:"mceInsertContent",value:e.replacement}):n("Replacement pattern has non-string `replacement` parameter");{const r=null!==(t=e.trigger)&&void 0!==t?t:"space";return 0===e.start.length?n("Block pattern has empty `start` parameter"):o("Block",(t=>({type:"block-format",start:e.start,format:t[0],trigger:r})),((t,n)=>({type:"block-command",start:e.start,cmd:t,value:n,trigger:r})))}},cd=e=>Y(e,ld),md=e=>Y(e,id),ud=(e,t)=>({...e,blockPatterns:Y(e.blockPatterns,(e=>((e,t)=>("block-command"===e.type||"block-format"===e.type)&&e.trigger===t)(e,t)))}),fd=e=>{const t=Ve(V(e,dd));return q(t.errors,(e=>console.error(e.message,e.pattern))),t.values},gd=(e,t,n)=>{e.dispatch(t,n)},pd=(e,t,n,o)=>{e.dispatch("FormatApply",{format:t,node:n,vars:o})},hd=(e,t,n,o)=>{e.dispatch("FormatRemove",{format:t,node:n,vars:o})},bd=(e,t)=>e.dispatch("SetContent",t),vd=(e,t)=>e.dispatch("GetContent",t),yd=(e,t)=>{e.dispatch("AutocompleterUpdateActiveRange",t)},Cd=(e,t)=>e.dispatch("PastePlainTextToggle",{state:t}),wd=Gt().deviceType,Ed=wd.isTouch(),xd=li.DOM,Sd=e=>m(e,RegExp),_d=e=>t=>t.options.get(e),kd=e=>u(e)||f(e),Nd=(e,t="")=>n=>{const o=u(n);if(o){if(-1!==n.indexOf("=")){const r=(e=>{const t=e.indexOf("=")>0?e.split(/[;,](?![^=;,]*(?:[;,]|$))/):e.split(",");return X(t,((e,t)=>{const n=t.split("="),o=n[0],r=n.length>1?n[1]:o;return e[Je(o)]=Je(r),e}),{})})(n);return{value:xe(r,e.id).getOr(t),valid:o}}return{value:n,valid:o}}return{valid:!1,message:"Must be a string."}},Ad=_d("iframe_attrs"),Rd=_d("doctype"),Dd=_d("document_base_url"),Td=_d("body_id"),Od=_d("body_class"),Bd=_d("content_security_policy"),Pd=_d("br_in_pre"),Ld=_d("forced_root_block"),Md=_d("forced_root_block_attrs"),Id=_d("newline_behavior"),Fd=_d("br_newline_selector"),Ud=_d("no_newline_selector"),zd=_d("keep_styles"),jd=_d("end_container_on_empty_block"),$d=_d("automatic_uploads"),Hd=_d("images_reuse_filename"),Vd=_d("images_replace_blob_uris"),qd=_d("icons"),Wd=_d("icons_url"),Kd=_d("images_upload_url"),Yd=_d("images_upload_base_path"),Gd=_d("images_upload_credentials"),Xd=_d("images_upload_handler"),Qd=_d("content_css_cors"),Zd=_d("referrer_policy"),Jd=_d("crossorigin"),ec=_d("language"),tc=_d("language_url"),nc=_d("indent_use_margin"),oc=_d("indentation"),rc=_d("content_css"),sc=_d("content_style"),ac=_d("font_css"),ic=_d("directionality"),lc=_d("inline_boundaries_selector"),dc=_d("object_resizing"),cc=_d("resize_img_proportional"),mc=_d("placeholder"),uc=_d("event_root"),fc=_d("service_message"),gc=_d("theme"),pc=_d("theme_url"),hc=_d("model"),bc=_d("model_url"),vc=_d("inline_boundaries"),yc=_d("formats"),Cc=_d("preview_styles"),wc=_d("format_empty_lines"),Ec=_d("format_noneditable_selector"),xc=_d("custom_ui_selector"),Sc=_d("inline"),_c=_d("hidden_input"),kc=_d("submit_patch"),Nc=_d("add_form_submit_trigger"),Ac=_d("add_unload_trigger"),Rc=_d("custom_undo_redo_levels"),Dc=_d("disable_nodechange"),Tc=_d("readonly"),Oc=_d("editable_root"),Bc=_d("content_css_cors"),Pc=_d("plugins"),Lc=_d("external_plugins"),Mc=_d("block_unsupported_drop"),Ic=_d("visual"),Fc=_d("visual_table_class"),Uc=_d("visual_anchor_class"),zc=_d("iframe_aria_text"),jc=_d("setup"),$c=_d("init_instance_callback"),Hc=_d("urlconverter_callback"),Vc=_d("auto_focus"),qc=_d("browser_spellcheck"),Wc=_d("protect"),Kc=_d("paste_block_drop"),Yc=_d("paste_data_images"),Gc=_d("paste_preprocess"),Xc=_d("paste_postprocess"),Qc=_d("newdocument_content"),Zc=_d("paste_webkit_styles"),Jc=_d("paste_remove_styles_if_webkit"),em=_d("paste_merge_formats"),tm=_d("smart_paste"),nm=_d("paste_as_text"),om=_d("paste_tab_spaces"),rm=_d("allow_html_data_urls"),sm=_d("text_patterns"),am=_d("text_patterns_lookup"),im=_d("noneditable_class"),lm=_d("editable_class"),dm=_d("noneditable_regexp"),cm=_d("preserve_cdata"),mm=_d("highlight_on_focus"),um=_d("xss_sanitization"),fm=_d("init_content_sync"),gm=e=>dn.explode(e.options.get("images_file_types")),pm=_d("table_tab_navigation"),hm=_d("details_initial_state"),bm=_d("details_serialized_state"),vm=_d("sandbox_iframes"),ym=e=>e.options.get("sandbox_iframes_exclusions"),Cm=_d("convert_unsafe_embeds"),wm=_d("license_key"),Em=_d("api_key"),xm=_d("disabled"),Sm=_d("user_id"),_m=_d("fetch_users"),km=_d("lists_indent_on_tab"),Nm=e=>I.from(e.options.get("list_max_depth")),Am=Xr,Rm=ss,Dm=e=>{const t=e.parentNode;t&&t.removeChild(e)},Tm=e=>{const t=ji(e);return{count:e.length-t.length,text:t}},Om=e=>{let t;for(;-1!==(t=e.data.lastIndexOf(Ui));)e.deleteData(t,1)},Bm=(e,t)=>(Lm(e),t),Pm=(e,t)=>Ul.isTextPosition(t)?((e,t)=>Rm(e)&&t.container()===e?((e,t)=>{const n=Tm(e.data.substr(0,t.offset())),o=Tm(e.data.substr(t.offset()));return(n.text+o.text).length>0?(Om(e),Ul(e,t.offset()-n.count)):t})(e,t):Bm(e,t))(e,t):((e,t)=>t.container()===e.parentNode?((e,t)=>{const n=t.container(),o=((e,t)=>{const n=j(e,t);return-1===n?I.none():I.some(n)})(ce(n.childNodes),e).map((e=>e<t.offset()?Ul(n,t.offset()-1):t)).getOr(t);return Lm(e),o})(e,t):Bm(e,t))(e,t),Lm=e=>{Am(e)&&Wi(e)&&(Ki(e)?e.removeAttribute("data-mce-caret"):Dm(e)),Rm(e)&&(Om(e),0===e.data.length&&Dm(e))},Mm=ps,Im=Cs,Fm=bs,Um=(e,t,n)=>{const o=nl(t.getBoundingClientRect(),n);let r,s;if("BODY"===e.tagName){const t=e.ownerDocument.documentElement;r=e.scrollLeft||t.scrollLeft,s=e.scrollTop||t.scrollTop}else{const t=e.getBoundingClientRect();r=e.scrollLeft-t.left,s=e.scrollTop-t.top}o.left+=r,o.right+=r,o.top+=s,o.bottom+=s,o.width=1;let a=t.offsetWidth-t.clientWidth;return a>0&&(n&&(a*=-1),o.left+=a,o.right+=a),o},zm=(e,t,n,o)=>{const r=We();let s,a;const i=Ld(e),l=e.dom,d=()=>{(e=>{var t,n;const o=xr(mn.fromDom(e),"*[contentEditable=false],video,audio,embed,object");for(let e=0;e<o.length;e++){const r=o[e].dom;let s=r.previousSibling;if(Qi(s)){const e=s.data;1===e.length?null===(t=s.parentNode)||void 0===t||t.removeChild(s):s.deleteData(e.length-1,1)}s=r.nextSibling,Xi(s)&&(1===s.data.length?null===(n=s.parentNode)||void 0===n||n.removeChild(s):s.deleteData(0,1))}})(t),a&&(Lm(a),a=null),r.on((e=>{l.remove(e.caret),r.clear()})),s&&(clearInterval(s),s=void 0)};return{isShowing:r.isSet,show:(e,c)=>{let m;if(d(),Fm(c))return null;if(!n(c))return a=((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(Ui),r=e.parentNode;if(t){const t=e.previousSibling;if(Hi(t)){if(Wi(t))return t;if(Qi(t))return t.splitText(t.data.length-1)}null==r||r.insertBefore(o,e)}else{const t=e.nextSibling;if(Hi(t)){if(Wi(t))return t;if(Xi(t))return t.splitText(1),t}e.nextSibling?null==r||r.insertBefore(o,e.nextSibling):null==r||r.appendChild(o)}return o})(c,e),m=c.ownerDocument.createRange(),$m(a.nextSibling)?(m.setStart(a,0),m.setEnd(a,0)):(m.setStart(a,1),m.setEnd(a,1)),m;{const n=((e,t,n)=>{var o;const r=(null!==(o=t.ownerDocument)&&void 0!==o?o:document).createElement(e);r.setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(Ii().dom);const s=t.parentNode;return n?null==s||s.insertBefore(r,t):t.nextSibling?null==s||s.insertBefore(r,t.nextSibling):null==s||s.appendChild(r),r})(i,c,e),d=Um(t,c,e);l.setStyle(n,"top",d.top),l.setStyle(n,"caret-color","transparent"),a=n;const u=l.create("div",{class:"mce-visual-caret","data-mce-bogus":"all"});l.setStyles(u,{...d}),l.add(t,u),r.set({caret:u,element:c,before:e}),e&&l.addClass(u,"mce-visual-caret-before"),s=window.setInterval((()=>{r.on((e=>{o()?l.toggleClass(e.caret,"mce-visual-caret-hidden"):l.addClass(e.caret,"mce-visual-caret-hidden")}))}),500),m=c.ownerDocument.createRange(),m.setStart(n,0),m.setEnd(n,0)}return m},hide:d,getCss:()=>".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}",reposition:()=>{r.on((e=>{const n=Um(t,e.element,e.before);l.setStyles(e.caret,{...n})}))},destroy:()=>clearInterval(s)}},jm=()=>rn.browser.isFirefox(),$m=e=>Mm(e)||Im(e),Hm=e=>($m(e)||ns(e)&&jm())&&Mn(mn.fromDom(e)).exists(vr),Vm=gs,qm=ps,Wm=Cs,Km=es("display","block table table-cell table-row table-caption list-item"),Ym=Wi,Gm=Vi,Xm=Xr,Qm=ss,Zm=yl,Jm=e=>1===e,eu=e=>-1===e,tu=(e,t)=>{let n;for(;n=e(t);)if(!Gm(n))return n;return null},nu=(e,t,n,o,r)=>{const s=new Hr(e,o),a=qm(e)||Gm(e);let i;if(eu(t)){if(a&&(i=tu(s.prev.bind(s),!0),n(i)))return i;for(;i=tu(s.prev.bind(s),r);)if(n(i))return i}if(Jm(t)){if(a&&(i=tu(s.next.bind(s),!0),n(i)))return i;for(;i=tu(s.next.bind(s),r);)if(n(i))return i}return null},ou=(e,t)=>{for(;e&&e!==t;){if(Km(e))return e;e=e.parentNode}return null},ru=(e,t,n)=>ou(e.container(),n)===ou(t.container(),n),su=(e,t)=>{if(!t)return I.none();const n=t.container(),o=t.offset();return Xm(n)?I.from(n.childNodes[o+e]):I.none()},au=(e,t)=>{var n;const o=(null!==(n=t.ownerDocument)&&void 0!==n?n:document).createRange();return e?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),o},iu=(e,t,n)=>ou(t,e)===ou(n,e),lu=(e,t,n)=>{const o=e?"previousSibling":"nextSibling";let r=n;for(;r&&r!==t;){let e=r[o];if(e&&Ym(e)&&(e=e[o]),qm(e)||Wm(e)){if(iu(t,e,r))return e;break}if(Zm(e))break;r=r.parentNode}return null},du=D(au,!0),cu=D(au,!1),mu=(e,t,n)=>{let o;const r=D(lu,!0,t),s=D(lu,!1,t),a=n.startContainer,i=n.startOffset;if(Vi(a)){const e=Qm(a)?a.parentNode:a,t=e.getAttribute("data-mce-caret");if("before"===t&&(o=e.nextSibling,Hm(o)))return du(o);if("after"===t&&(o=e.previousSibling,Hm(o)))return cu(o)}if(!n.collapsed)return n;if(ss(a)){if(Ym(a)){if(1===e){if(o=s(a),o)return du(o);if(o=r(a),o)return cu(o)}if(-1===e){if(o=r(a),o)return cu(o);if(o=s(a),o)return du(o)}return n}if(Qi(a)&&i>=a.data.length-1)return 1===e&&(o=s(a),o)?du(o):n;if(Xi(a)&&i<=1)return-1===e&&(o=r(a),o)?cu(o):n;if(i===a.data.length)return o=s(a),o?du(o):n;if(0===i)return o=r(a),o?cu(o):n}return n},uu=(e,t)=>su(e?0:-1,t).filter(qm),fu=(e,t,n)=>{const o=mu(e,t,n);return-1===e?Ul.fromRangeStart(o):Ul.fromRangeEnd(o)},gu=e=>I.from(e.getNode()).map(mn.fromDom),pu=(e,t)=>{let n=t;for(;n=e(n);)if(n.isVisible())return n;return n},hu=(e,t)=>{const n=ru(e,t);return!(n||!ms(e.getNode()))||n},bu=ps,vu=ss,yu=Xr,Cu=ms,wu=yl,Eu=e=>hl(e)||(e=>!!Cl(e)&&!X(ce(e.getElementsByTagName("*")),((e,t)=>e||ml(t)),!1))(e),xu=wl,Su=(e,t)=>e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null,_u=(e,t)=>{if(Jm(e)){if(wu(t.previousSibling)&&!vu(t.previousSibling))return Ul.before(t);if(vu(t))return Ul(t,0)}if(eu(e)){if(wu(t.nextSibling)&&!vu(t.nextSibling))return Ul.after(t);if(vu(t))return Ul(t,t.data.length)}return eu(e)?Cu(t)?Ul.before(t):Ul.after(t):Ul.before(t)},ku=(e,t,n)=>{let o,r,s,a;if(!yu(n)||!t)return null;if(t.isEqual(Ul.after(n))&&n.lastChild){if(a=Ul.after(n.lastChild),eu(e)&&wu(n.lastChild)&&yu(n.lastChild))return Cu(n.lastChild)?Ul.before(n.lastChild):a}else a=t;const i=a.container();let l=a.offset();if(vu(i)){if(eu(e)&&l>0)return Ul(i,--l);if(Jm(e)&&l<i.length)return Ul(i,++l);o=i}else{if(eu(e)&&l>0&&(r=Su(i,l-1),wu(r)))return!Eu(r)&&(s=nu(r,e,xu,r),s)?vu(s)?Ul(s,s.data.length):Ul.after(s):vu(r)?Ul(r,r.data.length):Ul.before(r);if(Jm(e)&&l<i.childNodes.length&&(r=Su(i,l),wu(r)))return Cu(r)?((e,t)=>{const n=t.nextSibling;return n&&wu(n)?vu(n)?Ul(n,0):Ul.before(n):ku(1,Ul.after(t),e)})(n,r):!Eu(r)&&(s=nu(r,e,xu,r),s)?vu(s)?Ul(s,0):Ul.before(s):vu(r)?Ul(r,0):Ul.after(r);o=r||a.getNode()}if(o&&(Jm(e)&&a.isAtEnd()||eu(e)&&a.isAtStart())&&(o=nu(o,e,M,n,!0),xu(o,n)))return _u(e,o);r=o?nu(o,e,xu,n):o;const d=yt(Y(((e,t)=>{const n=[];let o=e;for(;o&&o!==t;)n.push(o),o=o.parentNode;return n})(i,n),bu));return!d||r&&d.contains(r)?r?_u(e,r):null:(a=Jm(e)?Ul.after(d):Ul.before(d),a)},Nu=e=>({next:t=>ku(1,t,e),prev:t=>ku(-1,t,e)}),Au=e=>Ul.isTextPosition(e)?0===e.offset():yl(e.getNode()),Ru=e=>{if(Ul.isTextPosition(e)){const t=e.container();return e.offset()===t.data.length}return yl(e.getNode(!0))},Du=(e,t)=>!Ul.isTextPosition(e)&&!Ul.isTextPosition(t)&&e.getNode()===t.getNode(!0),Tu=(e,t,n)=>{const o=Nu(t);return I.from(e?o.next(n):o.prev(n))},Ou=(e,t,n)=>Tu(e,t,n).bind((o=>ru(n,o,t)&&((e,t,n)=>{return e?!Du(t,n)&&(o=t,!(!Ul.isTextPosition(o)&&ms(o.getNode())))&&Ru(t)&&Au(n):!Du(n,t)&&Au(t)&&Ru(n);var o})(e,n,o)?Tu(e,t,o):I.some(o))),Bu=(e,t,n,o)=>Ou(e,t,n).bind((n=>o(n)?Bu(e,t,n,o):I.some(n))),Pu=(e,t)=>{const n=e?t.firstChild:t.lastChild;return ss(n)?I.some(Ul(n,e?0:n.data.length)):n?yl(n)?I.some(e?Ul.before(n):ms(o=n)?Ul.before(o):Ul.after(o)):((e,t,n)=>{const o=e?Ul.before(n):Ul.after(n);return Tu(e,t,o)})(e,t,n):I.none();var o},Lu=D(Tu,!0),Mu=D(Tu,!1),Iu=D(Pu,!0),Fu=D(Pu,!1),Uu="_mce_caret",zu=e=>Xr(e)&&e.id===Uu,ju=(e,t)=>{let n=t;for(;n&&n!==e;){if(zu(n))return n;n=n.parentNode}return null},$u=e=>Se(e,"name"),Hu=e=>dn.isArray(e.start),Vu=e=>!(!$u(e)&&b(e.forward))||e.forward,qu=(e,t)=>(Xr(t)&&e.isBlock(t)&&!t.innerHTML&&(t.innerHTML='<br data-mce-bogus="1" />'),t),Wu=(e,t)=>Fu(e).fold(L,(e=>(t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0))),Ku=(e,t,n)=>!(!(e=>!e.hasChildNodes())(t)||!ju(e,t)||(((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(Ui);e.appendChild(o),t.setStart(o,0),t.setEnd(o,0)})(t,n),0)),Yu=(e,t,n,o)=>{const r=n[t?"start":"end"],s=e.getRoot();if(r){let e=s,n=r[0];for(let t=r.length-1;e&&t>=1;t--){const n=e.childNodes;if(Ku(s,e,o))return!0;if(r[t]>n.length-1)return!!Ku(s,e,o)||Wu(e,o);e=n[r[t]]}ss(e)&&(n=Math.min(r[0],e.data.length)),Xr(e)&&(n=Math.min(r[0],e.childNodes.length)),t?o.setStart(e,n):o.setEnd(e,n)}return!0},Gu=e=>ss(e)&&e.data.length>0,Xu=(e,t,n)=>{const o=e.get(n.id+"_"+t),r=null==o?void 0:o.parentNode,s=n.keep;if(o&&r){let a,i;if("start"===t?s?o.hasChildNodes()?(a=o.firstChild,i=1):Gu(o.nextSibling)?(a=o.nextSibling,i=0):Gu(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)+1):(a=r,i=e.nodeIndex(o)):s?o.hasChildNodes()?(a=o.firstChild,i=1):Gu(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)):(a=r,i=e.nodeIndex(o)),!s){const r=o.previousSibling,s=o.nextSibling;let l;for(dn.each(dn.grep(o.childNodes),(e=>{ss(e)&&(e.data=e.data.replace(/\uFEFF/g,""))}));l=e.get(n.id+"_"+t);)e.remove(l,!0);if(ss(s)&&ss(r)&&!rn.browser.isOpera()){const t=r.data.length;r.appendData(s.data),e.remove(s),a=r,i=t}}return I.some(Ul(a,i))}return I.none()},Qu=(e,t,n)=>((e,t,n=!1)=>2===t?od(ji,n,e):3===t?(e=>{const t=e.getRng();return{start:Gl(e.dom.getRoot(),Ul.fromRangeStart(t)),end:Gl(e.dom.getRoot(),Ul.fromRangeEnd(t)),forward:e.isForward()}})(e):t?(e=>({rng:e.getRng(),forward:e.isForward()}))(e):sd(e,!1))(e,t,n),Zu=(e,t)=>{((e,t)=>{const n=e.dom;if(t){if(Hu(t))return((e,t)=>{const n=e.createRng();return Yu(e,!0,t,n)&&Yu(e,!1,t,n)?I.some({range:n,forward:Vu(t)}):I.none()})(n,t);if((e=>u(e.start))(t))return((e,t)=>{const n=I.from(Xl(e.getRoot(),t.start)),o=I.from(Xl(e.getRoot(),t.end));return je(n,o,((n,o)=>{const r=e.createRng();return r.setStart(n.container(),n.offset()),r.setEnd(o.container(),o.offset()),{range:r,forward:Vu(t)}}))})(n,t);if((e=>Se(e,"id"))(t))return((e,t)=>{const n=Xu(e,"start",t),o=Xu(e,"end",t);return je(n,o.or(n),((n,o)=>{const r=e.createRng();return r.setStart(qu(e,n.container()),n.offset()),r.setEnd(qu(e,o.container()),o.offset()),{range:r,forward:Vu(t)}}))})(n,t);if($u(t))return((e,t)=>I.from(e.select(t.name)[t.index]).map((t=>{const n=e.createRng();return n.selectNode(t),{range:n,forward:!0}})))(n,t);if((e=>Se(e,"rng"))(t))return I.some({range:t.rng,forward:Vu(t)})}return I.none()})(e,t).each((({range:t,forward:n})=>{e.setRng(t,n)}))},Ju=e=>Xr(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type"),ef=(tf=dt,e=>tf===e);var tf;const nf=e=>""!==e&&-1!==" \f\n\r\t\v".indexOf(e),of=e=>!nf(e)&&!ef(e)&&!ct(e),rf=e=>{const t=[];if(e)for(let n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},sf=(e,t)=>{const n=xr(t,"td[data-mce-selected],th[data-mce-selected]");return n.length>0?n:(e=>Y((e=>te(e,(e=>{const t=il(e);return t?[mn.fromDom(t)]:[]})))(e),Li))(e)},af=e=>sf(rf(e.selection.getSel()),mn.fromDom(e.getBody())),lf=(e,t)=>ir(e,"table",t),df=e=>qn(e).fold(N([e]),(t=>[e].concat(df(t)))),cf=e=>Wn(e).fold(N([e]),(t=>"br"===En(t)?Un(t).map((t=>[e].concat(cf(t)))).getOr([]):[e].concat(cf(t)))),mf=(e,t)=>je((e=>{const t=e.startContainer,n=e.startOffset;return ss(t)?0===n?I.some(mn.fromDom(t)):I.none():I.from(t.childNodes[n]).map(mn.fromDom)})(t),(e=>{const t=e.endContainer,n=e.endOffset;return ss(t)?n===t.data.length?I.some(mn.fromDom(t)):I.none():I.from(t.childNodes[n-1]).map(mn.fromDom)})(t),((t,n)=>{const o=Z(df(e),D(vn,t)),r=Z(cf(e),D(vn,n));return o.isSome()&&r.isSome()})).getOr(!1),uf=(e,t,n,o)=>{const r=n,s=new Hr(n,r),a=Ce(e.schema.getMoveCaretBeforeOnEnterElements(),((e,t)=>!$(["td","th","table"],t.toLowerCase())));let i=n;do{if(ss(i)&&0!==dn.trim(i.data).length)return void(o?t.setStart(i,0):t.setEnd(i,i.data.length));if(a[i.nodeName])return void(o?t.setStartBefore(i):"BR"===i.nodeName?t.setEndBefore(i):t.setEndAfter(i))}while(i=o?s.next():s.prev());"BODY"===r.nodeName&&(o?t.setStart(r,0):t.setEnd(r,r.childNodes.length))},ff=e=>{const t=e.selection.getSel();return C(t)&&t.rangeCount>0},gf=(e,t)=>{const n=af(e);n.length>0?q(n,(n=>{const o=n.dom,r=e.dom.createRng();r.setStartBefore(o),r.setEndAfter(o),t(r,!0)})):t(e.selection.getRng(),!1)},pf=(e,t,n)=>{const o=sd(e,t);n(o),e.moveToBookmark(o)},hf=(e,t)=>e.startContainer===e.endContainer&&e.endOffset-e.startOffset==1&&t(e.startContainer.childNodes[e.startOffset]),bf=e=>E(null==e?void 0:e.nodeType),vf=e=>Xr(e)&&!Ju(e)&&!zu(e)&&!ts(e),yf=(e,t,n)=>{const{selection:o,dom:r}=e,s=o.getNode(),a=ps(s);pf(o,!0,(()=>{t()})),a&&ps(s)&&r.isChildOf(s,e.getBody())?e.selection.select(s):n(o.getStart())&&Cf(r,o)},Cf=(e,t)=>{var n,o;const r=t.getRng(),{startContainer:s,startOffset:a}=r;if(!((e,t)=>{if(vf(t)&&!/^(TD|TH)$/.test(t.nodeName)){const n=e.getAttrib(t,"data-mce-selected"),o=parseInt(n,10);return!isNaN(o)&&o>0}return!1})(e,t.getNode())&&Xr(s)){const i=s.childNodes,l=e.getRoot();let d;if(a<i.length){const t=i[a];d=new Hr(t,null!==(n=e.getParent(t,e.isBlock))&&void 0!==n?n:l)}else{const t=i[i.length-1];d=new Hr(t,null!==(o=e.getParent(t,e.isBlock))&&void 0!==o?o:l),d.next(!0)}for(let n=d.current();n;n=d.next()){if("false"===e.getContentEditable(n))return;if(ss(n)&&!Sf(n))return r.setStart(n,0),void t.setRng(r)}}},wf=(e,t,n)=>{if(e){const o=t?"nextSibling":"previousSibling";for(e=n?e:e[o];e;e=e[o])if(Xr(e)||!Sf(e))return e}},Ef=(e,t)=>!!e.getTextBlockElements()[t.nodeName.toLowerCase()]||Ks(e,t),xf=(e,t,n)=>e.schema.isValidChild(t,n),Sf=(e,t=!1)=>{if(C(e)&&ss(e)){const n=t?e.data.replace(/ /g,"\xa0"):e.data;return qr(n)}return!1},_f=(e,t)=>{const n=e.dom;return vf(t)&&"false"===n.getContentEditable(t)&&((e,t)=>{const n="[data-mce-cef-wrappable]",o=Ec(e),r=ot(o)?n:`${n},${o}`;return hn(mn.fromDom(t),r)})(e,t)&&0===n.select('[contenteditable="true"]',t).length},kf=(e,t)=>w(e)?e(t):(C(t)&&(e=e.replace(/%(\w+)/g,((e,n)=>t[n]||e))),e),Nf=(e,t)=>(t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()),Af=(e,t)=>{if(y(e))return null;{let n=String(e);return"color"!==t&&"backgroundColor"!==t||(n=$a(n)),"fontWeight"===t&&700===e&&(n="bold"),"fontFamily"===t&&(n=n.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),n}},Rf=(e,t,n)=>{const o=e.getStyle(t,n);return Af(o,n)},Df=(e,t)=>{let n;return e.getParent(t,(t=>!!Xr(t)&&(n=e.getStyle(t,"text-decoration"),!!n&&"none"!==n))),n},Tf=(e,t,n)=>e.getParents(t,n,e.getRoot()),Of=(e,t,n)=>{const o=e.formatter.get(t);return C(o)&&H(o,n)},Bf=e=>_e(e,"block"),Pf=e=>_e(e,"selector"),Lf=e=>_e(e,"inline"),Mf=e=>Pf(e)&&!1!==e.expand&&!Lf(e),If=e=>(e=>{const t=[];let n=e;for(;n;){if(ss(n)&&n.data!==Ui||n.childNodes.length>1)return[];Xr(n)&&t.push(n),n=n.firstChild}return t})(e).length>0,Ff=e=>zu(e.dom)&&If(e.dom),Uf=Ju,zf=Tf,jf=Sf,$f=Ef,Hf=(e,t)=>{let n=t;for(;n;){if(Xr(n)&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},Vf=(e,t,n,o)=>{const r=t.data;if(e){for(let e=n;e>0;e--)if(o(r.charAt(e-1)))return e}else for(let e=n;e<r.length;e++)if(o(r.charAt(e)))return e;return-1},qf=(e,t,n)=>Vf(e,t,n,(e=>ef(e)||nf(e))),Wf=(e,t,n)=>Vf(e,t,n,of),Kf=(e,t,n,o,r,s)=>{let a;const i=e.getParent(n,(t=>hs(t)||e.isBlock(t))),l=C(i)?i:t,d=(t,n,o)=>{const s=Ai(e),i=r?s.backwards:s.forwards;return I.from(i(t,n,((e,t)=>Uf(e.parentNode)?-1:(a=e,o(r,e,t))),l))};return d(n,o,qf).bind((e=>s?d(e.container,e.offset+(r?-1:0),Wf):I.some(e))).orThunk((()=>a?I.some({container:a,offset:r?0:a.length}):I.none()))},Yf=(e,t,n,o,r)=>{const s=o[r];ss(o)&&ot(o.data)&&s&&(o=s);const a=zf(e,o);for(let o=0;o<a.length;o++)for(let r=0;r<t.length;r++){const s=t[r];if((!C(s.collapsed)||s.collapsed===n.collapsed)&&Pf(s)&&e.is(a[o],s.selector))return a[o]}return o},Gf=(e,t,n,o)=>{var r;let s=n;const a=e.getRoot(),i=t[0];if(Bf(i)&&(s=i.wrapper?null:e.getParent(n,i.block,a)),!s){const t=null!==(r=e.getParent(n,"LI,TD,TH,SUMMARY"))&&void 0!==r?r:a;s=e.getParent(ss(n)?n.parentNode:n,(t=>t!==a&&$f(e.schema,t)),t)}if(s&&Bf(i)&&i.wrapper&&(s=zf(e,s,"ul,ol").reverse()[0]||s),!s)for(s=n;s&&s[o]&&!e.isBlock(s[o])&&(s=s[o],!Nf(s,"br")););return s||n},Xf=(e,t,n,o)=>{const r=n.parentNode;return!C(n[o])&&(!(r!==t&&!y(r)&&!e.isBlock(r))||Xf(e,t,r,o))},Qf=(e,t,n,o,r,s)=>{let a=n;const i=r?"previousSibling":"nextSibling",l=e.getRoot();if(ss(n)&&!jf(n)&&(r?o>0:o<n.data.length))return n;for(;a;){if(hs(a))return n;if(!t[0].block_expand&&e.isBlock(a))return s?a:n;for(let t=a[i];t;t=t[i]){const n=ss(t)&&!Xf(e,l,t,i);if(!Uf(t)&&(!ms(d=t)||!d.getAttribute("data-mce-bogus")||d.nextSibling)&&!jf(t,n))return a}if(a===l||a.parentNode===l){n=a;break}a=a.parentNode}var d;return n},Zf=e=>Uf(e.parentNode)||Uf(e),Jf=(e,t,n,o={})=>{const{includeTrailingSpace:r=!1,expandToBlock:s=!0}=o,a=e.getParent(t.commonAncestorContainer,(e=>hs(e))),i=C(a)?a:e.getRoot();let{startContainer:l,startOffset:d,endContainer:c,endOffset:m}=t;const u=n[0];return Xr(l)&&l.hasChildNodes()&&(l=ll(l,d),ss(l)&&(d=0)),Xr(c)&&c.hasChildNodes()&&(c=ll(c,t.collapsed?m:m-1),ss(c)&&(m=c.data.length)),l=Hf(e,l),c=Hf(e,c),Zf(l)&&(l=Uf(l)?l:l.parentNode,l=t.collapsed?l.previousSibling||l:l.nextSibling||l,ss(l)&&(d=t.collapsed?l.length:0)),Zf(c)&&(c=Uf(c)?c:c.parentNode,c=t.collapsed?c.nextSibling||c:c.previousSibling||c,ss(c)&&(m=t.collapsed?0:c.length)),t.collapsed&&(Kf(e,i,l,d,!0,r).each((({container:e,offset:t})=>{l=e,d=t})),Kf(e,i,c,m,!1,r).each((({container:e,offset:t})=>{c=e,m=t}))),(Lf(u)||u.block_expand)&&(Lf(u)&&ss(l)&&0!==d||(l=Qf(e,n,l,d,!0,s)),Lf(u)&&ss(c)&&m!==c.data.length||(c=Qf(e,n,c,m,!1,s))),Mf(u)&&(l=Yf(e,n,t,l,"previousSibling"),c=Yf(e,n,t,c,"nextSibling")),(Bf(u)||Pf(u))&&(l=Gf(e,n,l,"previousSibling"),c=Gf(e,n,c,"nextSibling"),Bf(u)&&(e.isBlock(l)||(l=Qf(e,n,l,d,!0,s),ss(l)&&(d=0)),e.isBlock(c)||(c=Qf(e,n,c,m,!1,s),ss(c)&&(m=c.data.length)))),Xr(l)&&l.parentNode&&(d=e.nodeIndex(l),l=l.parentNode),Xr(c)&&c.parentNode&&(m=e.nodeIndex(c)+1,c=c.parentNode),{startContainer:l,startOffset:d,endContainer:c,endOffset:m}},eg=(e,t,n)=>{var o;const r=t.startOffset,s=ll(t.startContainer,r),a=t.endOffset,i=ll(t.endContainer,a-1),l=e=>{const t=e[0];ss(t)&&t===s&&r>=t.data.length&&e.splice(0,1);const n=e[e.length-1];return 0===a&&e.length>0&&n===i&&ss(n)&&e.splice(e.length-1,1),e},d=(e,t,n)=>{const o=[];for(;e&&e!==n;e=e[t])o.push(e);return o},c=(t,n)=>e.getParent(t,(e=>e.parentNode===n),n),m=(e,t,o)=>{const r=o?"nextSibling":"previousSibling";for(let s=e,a=s.parentNode;s&&s!==t;s=a){a=s.parentNode;const t=d(s===e?s:s[r],r);t.length&&(o||t.reverse(),n(l(t)))}};if(s===i)return n(l([s]));const u=null!==(o=e.findCommonAncestor(s,i))&&void 0!==o?o:e.getRoot();if(e.isChildOf(s,i))return m(s,u,!0);if(e.isChildOf(i,s))return m(i,u);const f=c(s,u)||s,g=c(i,u)||i;m(s,f,!0);const p=d(f===s?f:f.nextSibling,"nextSibling",g===i?g.nextSibling:g);p.length&&n(l(p)),m(i,g)},tg=['pre[class*=language-][contenteditable="false"]',"figure.image","div[data-ephox-embed-iri]","div.tiny-pageembed","div.mce-toc","div[data-mce-toc]","div.mce-footnotes"],ng=(e,t,n,o,r,s)=>{const{uid:a=t,...i}=n;gr(e,hi()),ho(e,`${vi()}`,a),ho(e,`${bi()}`,o);const{attributes:l={},classes:d=[]}=r(a,i);if(bo(e,l),((e,t)=>{q(t,(t=>{gr(e,t)}))})(e,d),s){d.length>0&&ho(e,`${Ci()}`,d.join(","));const t=fe(l);t.length>0&&ho(e,`${wi()}`,t.join(","))}},og=(e,t,n,o,r)=>{const s=mn.fromTag("span",e);return ng(s,t,n,o,r,!1),s},rg=(e,t,n,o,r,s)=>{const a=[],i=og(e.getDoc(),n,s,o,r),l=We(),d=()=>{l.clear()},c=e=>{q(e,m)},m=t=>{switch(((e,t,n,o)=>Mn(t).fold((()=>"skipping"),(r=>"br"===o||(e=>An(e)&&er(e)===Ui)(t)?"valid":(e=>Nn(e)&&br(e,hi()))(t)?"existing":zu(t.dom)?"caret":H(tg,(e=>hn(t,e)))?"valid-block":xf(e,n,o)&&xf(e,En(r),n)?"valid":"invalid-child")))(e,t,"span",En(t))){case"invalid-child":{d();const e=Hn(t);c(e),d();break}case"valid-block":d(),ng(t,n,s,o,r,!0);break;case"valid":{const e=l.get().getOrThunk((()=>{const e=Ao(i);return a.push(e),l.set(e),e}));uo(t,e);break}}};return eg(e.dom,t,(e=>{d(),(e=>{const t=V(e,mn.fromDom);c(t)})(e)})),a},sg=e=>{const t=(()=>{const e={};return{register:(t,n)=>{e[t]={name:t,settings:n}},lookup:t=>xe(e,t).map((e=>e.settings)),getNames:()=>fe(e)}})();((e,t)=>{const n=bi(),o=e=>I.from(e.attr(n)).bind(t.lookup),r=e=>{var t,n;e.attr(vi(),null),e.attr(bi(),null),e.attr(yi(),null);const o=I.from(e.attr(wi())).map((e=>e.split(","))).getOr([]),r=I.from(e.attr(Ci())).map((e=>e.split(","))).getOr([]);q(o,(t=>e.attr(t,null)));const s=null!==(n=null===(t=e.attr("class"))||void 0===t?void 0:t.split(" "))&&void 0!==n?n:[],a=re(s,[hi()].concat(r));e.attr("class",a.length>0?a.join(" "):null),e.attr(Ci(),null),e.attr(wi(),null)};e.serializer.addTempAttr(yi()),e.serializer.addAttributeFilter(n,(e=>{for(const t of e)o(t).each((e=>{!1===e.persistent&&("span"===t.name?t.unwrap():r(t))}))}))})(e,t);const n=((e,t)=>{const n=Ne({}),o=()=>({listeners:[],previous:We()}),r=(e,t)=>{s(e,(e=>(t(e),e)))},s=(e,t)=>{const r=n.get(),s=t(xe(r,e).getOrThunk(o));r[e]=s,n.set(r)},a=(t,n)=>{q(_i(e,t),(e=>{n?ho(e,yi(),"true"):wo(e,yi())}))},i=at((()=>{const n=ae(t.getNames());q(n,(t=>{s(t,(n=>{const o=n.previous.get();return xi(e,I.some(t)).fold((()=>{o.each((e=>{(e=>{r(e,(t=>{q(t.listeners,(t=>t(!1,e)))}))})(t),n.previous.clear(),a(e,!1)}))}),(({uid:e,name:t,elements:s})=>{Ue(o,e)||(o.each((e=>a(e,!1))),((e,t,n)=>{r(e,(o=>{q(o.listeners,(o=>o(!0,e,{uid:t,nodes:V(n,(e=>e.dom))})))}))})(t,e,s),n.previous.set(e),a(e,!0))})),{previous:n.previous,listeners:n.listeners}}))}))}),30);return e.on("remove",(()=>{i.cancel()})),e.on("NodeChange",(()=>{i.throttle()})),{addListener:(e,t)=>{s(e,(e=>({previous:e.previous,listeners:e.listeners.concat([t])})))}}})(e,t),o=Tn("span"),r=e=>{q(e,(e=>{o(e)?ko(e):(e=>{hr(e,hi()),wo(e,`${vi()}`),wo(e,`${bi()}`),wo(e,`${yi()}`);const t=yo(e,`${wi()}`).map((e=>e.split(","))).getOr([]),n=yo(e,`${Ci()}`).map((e=>e.split(","))).getOr([]);var o;q(t,(t=>wo(e,t))),o=e,q(n,(e=>{hr(o,e)})),wo(e,`${Ci()}`),wo(e,`${wi()}`)})(e)}))};return{register:(e,n)=>{t.register(e,n)},annotate:(n,o)=>{t.lookup(n).each((t=>{((e,t,n,o)=>{e.undoManager.transact((()=>{const r=e.selection,s=r.getRng(),a=af(e).length>0,i=Pe("mce-annotation");if(s.collapsed&&!a&&((e,t)=>{const n=Jf(e.dom,t,[{inline:"span"}]);t.setStart(n.startContainer,n.startOffset),t.setEnd(n.endContainer,n.endOffset),e.selection.setRng(t)})(e,s),r.getRng().collapsed&&!a){const s=og(e.getDoc(),i,o,t,n.decorate);Bo(s,dt),r.getRng().insertNode(s.dom),r.select(s.dom)}else pf(r,!1,(()=>{gf(e,(r=>{rg(e,r,i,t,n.decorate,o)}))}))}))})(e,n,t,o)}))},annotationChanged:(e,t)=>{n.addListener(e,t)},remove:t=>{xi(e,I.some(t)).each((({elements:t})=>{const n=e.selection.getBookmark();r(t),e.selection.moveToBookmark(n)}))},removeAll:t=>{const n=e.selection.getBookmark();pe(ki(e,t),((e,t)=>{r(e)})),e.selection.moveToBookmark(n)},getAll:t=>{const n=ki(e,t);return he(n,(e=>V(e,(e=>e.dom))))}}},ag=e=>({getBookmark:D(Qu,e),moveToBookmark:D(Zu,e)});ag.isBookmarkNode=Ju;const ig=(e,t,n)=>!n.collapsed&&H(n.getClientRects(),(n=>((e,t,n)=>t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom)(n,e,t))),lg=(e,t)=>{const n=An(t)?er(t).length:Hn(t).length+1;return e>n?n:e<0?0:e},dg=e=>Lr.range(e.start,lg(e.soffset,e.start),e.finish,lg(e.foffset,e.finish)),cg=(e,t)=>!Gr(t.dom)&&(yn(e,t)||vn(e,t)),mg=e=>t=>cg(e,t.start)&&cg(e,t.finish),ug=e=>Lr.range(mn.fromDom(e.startContainer),e.startOffset,mn.fromDom(e.endContainer),e.endOffset),fg=e=>{const t=document.createRange();try{return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),I.some(t)}catch(e){return I.none()}},gg=e=>{const t=(e=>e.inline||rn.browser.isFirefox())(e)?(n=mn.fromDom(e.getBody()),(e=>{const t=e.getSelection();return(t&&0!==t.rangeCount?I.from(t.getRangeAt(0)):I.none()).map(ug)})(Pn(n).dom).filter(mg(n))):I.none();var n;e.bookmark=t.isSome()?t:e.bookmark},pg=e=>(e.bookmark?e.bookmark:I.none()).bind((t=>{return n=mn.fromDom(e.getBody()),o=t,I.from(o).filter(mg(n)).map(dg);var n,o})).bind(fg),hg={isEditorUIElement:e=>{const t=e.className.toString();return-1!==t.indexOf("tox-")||-1!==t.indexOf("mce-")}},bg={setEditorTimeout:(e,t,n)=>((e,t)=>(E(t)||(t=0),window.setTimeout(e,t)))((()=>{e.removed||t()}),n),setEditorInterval:(e,t,n)=>{const o=((e,t)=>(E(t)||(t=0),window.setInterval(e,t)))((()=>{e.removed?window.clearInterval(o):t()}),n);return o}},vg=(e,t)=>e.view(t).fold(N([]),(t=>{const n=e.owner(t),o=vg(e,n);return[t].concat(o)}));var yg=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?I.none():I.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(mn.fromDom)},owner:e=>Bn(e)});const Cg=e=>{const t=oo(),n=Go(t),o=((e,t)=>{const n=t.owner(e);return vg(t,n)})(e,yg),r=Yo(e),s=G(o,((e,t)=>{const n=Yo(t);return{left:e.left+n.left,top:e.top+n.top}}),{left:0,top:0});return Wo(s.left+r.left+n.left,s.top+r.top+n.top)};let wg;const Eg=li.DOM,xg=e=>{const t=e.classList;return void 0!==t&&(t.contains("tox-edit-area")||t.contains("tox-edit-area__iframe")||t.contains("mce-content-body"))},Sg=(e,t)=>{const n=xc(e),o=Eg.getParent(t,(t=>(e=>Xr(e)&&hg.isEditorUIElement(e))(t)||!!n&&e.dom.is(t,n)));return null!==o},_g=e=>{try{const t=Gn(mn.fromDom(e.getElement()));return ao(t).fold((()=>document.body),(e=>e.dom))}catch(e){return document.body}},kg=(e,t)=>{const n=t.editor;(e=>{const t=st((()=>{gg(e)}),0);e.on("init",(()=>{e.inline&&((e,t)=>{const n=()=>{t.throttle()};li.DOM.bind(document,"mouseup",n),e.on("remove",(()=>{li.DOM.unbind(document,"mouseup",n)}))})(e,t),((e,t)=>{((e,t)=>{e.on("mouseup touchend",(e=>{t.throttle()}))})(e,t),e.on("keyup NodeChange AfterSetSelectionRange",(t=>{(e=>"nodechange"===e.type&&e.selectionChange)(t)||gg(e)}))})(e,t)})),e.on("remove",(()=>{t.cancel()}))})(n);const o=(e,t)=>{mm(e)&&!0!==e.inline&&t(mn.fromDom(e.getContainer()),"tox-edit-focus")};n.on("focusin",(()=>{const t=e.focusedEditor;if(xg(_g(n))&&o(n,gr),t!==n){t&&t.dispatch("blur",{focusedEditor:n}),e.setActive(n),e.focusedEditor=n,n.dispatch("focus",{blurredEditor:t}),n.focus(!0);const o=Gt().browser;!0!==n.inline&&(o.isSafari()||o.isChromium())&&(e=>{if(!e.iframeElement)return;const t=mn.fromDom(e.iframeElement),n=Cg(t),o=$r(window);(n.top<o.y||n.top>o.bottom-25)&&t.dom.scrollIntoView({block:"center"})})(n)}})),n.on("focusout",(()=>{bg.setEditorTimeout(n,(()=>{const t=e.focusedEditor;xg(_g(n))&&t===n||o(n,hr),Sg(n,_g(n))||t!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null)}))})),wg||(wg=t=>{const n=e.activeEditor;n&&Zn(t).each((t=>{const o=t;o.ownerDocument===document&&(o===document.body||Sg(n,o)||e.focusedEditor!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null))}))},Eg.bind(document,"focusin",wg))},Ng=(e,t)=>{e.focusedEditor===t.editor&&(e.focusedEditor=null),!e.activeEditor&&wg&&(Eg.unbind(document,"focusin",wg),wg=null)},Ag=(e,t)=>{((e,t)=>(e=>e.collapsed?I.from(ll(e.startContainer,e.startOffset)).map(mn.fromDom):I.none())(t).bind((t=>Pi(t)?I.some(t):yn(e,t)?I.none():I.some(e))))(mn.fromDom(e.getBody()),t).bind((e=>Iu(e.dom))).fold((()=>{e.selection.normalize()}),(t=>e.selection.setRng(t.toRange())))},Rg=e=>{if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},Dg=e=>e.inline?(e=>{const t=e.getBody();return t&&(n=mn.fromDom(t),so(n)||(o=n,ao(Gn(o)).filter((e=>o.dom.contains(e.dom)))).isSome());var n,o})(e):(e=>C(e.iframeElement)&&so(mn.fromDom(e.iframeElement)))(e),Tg=e=>Dg(e)||(e=>{const t=Gn(mn.fromDom(e.getElement()));return ao(t).filter((t=>!xg(t.dom)&&Sg(e,t.dom))).isSome()})(e),Og=e=>e.editorManager.setActive(e),Bg={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,ESC:27,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,modifierPressed:e=>e.shiftKey||e.ctrlKey||e.altKey||Bg.metaKeyPressed(e),metaKeyPressed:e=>rn.os.isMacOS()||rn.os.isiOS()?e.metaKey:e.ctrlKey&&!e.altKey},Pg="data-mce-selected",Lg=Math.abs,Mg=Math.round,Ig={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]},Fg=(e,t)=>{const n=t.dom,o=t.getDoc(),r=document,s=t.getBody();let a,i,l,d,c,m,u,f,g,p,h,b,v,y,w;const E=e=>C(e)&&(us(e)||n.is(e,"figure.image")),x=e=>Cs(e)||n.hasClass(e,"mce-preview-object"),S=e=>{const n=e.target;((e,t)=>{if((e=>"longpress"===e.type||0===e.type.indexOf("touch"))(e)){const n=e.touches[0];return E(e.target)&&!ig(n.clientX,n.clientY,t)}return E(e.target)&&!ig(e.clientX,e.clientY,t)})(e,t.selection.getRng())&&!e.isDefaultPrevented()&&t.selection.select(n)},_=e=>n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?[e,e.firstElementChild]:n.is(e,"figure.image")?[e.querySelector("img")]:[e],k=e=>{const o=dc(t);return!(!o||t.mode.isReadOnly())&&"false"!==e.getAttribute("data-mce-resize")&&e!==t.getBody()&&(n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?hn(mn.fromDom(e.firstElementChild),o):hn(mn.fromDom(e),o))},N=(e,o,r)=>{if(C(r)){const s=_(e);q(s,(e=>{e.style[o]||!t.schema.isValid(e.nodeName.toLowerCase(),o)?n.setStyle(e,o,r):n.setAttrib(e,o,""+r)}))}},A=(e,t,n)=>{N(e,"width",t),N(e,"height",n)},R=e=>{let o,r,c,C,S;o=e.screenX-m,r=e.screenY-u,b=o*d[2]+f,v=r*d[3]+g,b=b<5?5:b,v=v<5?5:v,c=(E(a)||x(a))&&!1!==cc(t)?!Bg.modifierPressed(e):Bg.modifierPressed(e),c&&(Lg(o)>Lg(r)?(v=Mg(b*p),b=Mg(v/p)):(b=Mg(v/p),v=Mg(b*p))),A(i,b,v),C=d.startPos.x+o,S=d.startPos.y+r,C=C>0?C:0,S=S>0?S:0,n.setStyles(l,{left:C,top:S,display:"block"}),l.innerHTML=b+" &times; "+v,o=s.scrollWidth-y,r=s.scrollHeight-w,o+r!==0&&n.setStyles(l,{left:C-o,top:S-r}),h||(((e,t,n,o,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:n,height:o,origin:r})})(t,a,f,g,"corner-"+d.name),h=!0)},D=()=>{const e=h;h=!1,e&&(N(a,"width",b),N(a,"height",v)),n.unbind(o,"mousemove",R),n.unbind(o,"mouseup",D),r!==o&&(n.unbind(r,"mousemove",R),n.unbind(r,"mouseup",D)),n.remove(i),n.remove(l),n.remove(c),T(a),e&&(((e,t,n,o,r)=>{e.dispatch("ObjectResized",{target:t,width:n,height:o,origin:r})})(t,a,b,v,"corner-"+d.name),n.setAttrib(a,"style",n.getAttrib(a,"style"))),t.nodeChanged()},T=e=>{M();const h=n.getPos(e,s),C=h.x,E=h.y,S=e.getBoundingClientRect(),N=S.width||S.right-S.left,T=S.height||S.bottom-S.top;a!==e&&(B(),a=e,b=v=0);const O=t.dispatch("ObjectSelected",{target:e});k(e)&&!O.isDefaultPrevented()?pe(Ig,((e,t)=>{let h=n.get("mceResizeHandle"+t);h&&n.remove(h),h=n.add(s,"div",{id:"mceResizeHandle"+t,"data-mce-bogus":"all",class:"mce-resizehandle",unselectable:!0,style:"cursor:"+t+"-resize; margin:0; padding:0"}),n.bind(h,"mousedown",(h=>{h.stopImmediatePropagation(),h.preventDefault(),(h=>{const b=_(a)[0];m=h.screenX,u=h.screenY,f=b.clientWidth,g=b.clientHeight,p=g/f,d=e,d.name=t,d.startPos={x:N*e[0]+C,y:T*e[1]+E},y=s.scrollWidth,w=s.scrollHeight,c=n.add(s,"div",{class:"mce-resize-backdrop","data-mce-bogus":"all"}),n.setStyles(c,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}),i=((e,t)=>{if(x(t))return e.create("img",{src:rn.transparentSrc});if(ns(t)){const n=Xe(d.name,"n")?le:de,o=t.cloneNode(!0);return n(e.select("tr",o)).each((t=>{const n=e.select("td,th",t);e.setStyle(t,"height",null),q(n,(t=>e.setStyle(t,"height",null)))})),o}return t.cloneNode(!0)})(n,a),n.addClass(i,"mce-clonedresizable"),n.setAttrib(i,"data-mce-bogus","all"),i.contentEditable="false",n.setStyles(i,{left:C,top:E,margin:0}),A(i,N,T),i.removeAttribute(Pg),s.appendChild(i),n.bind(o,"mousemove",R),n.bind(o,"mouseup",D),r!==o&&(n.bind(r,"mousemove",R),n.bind(r,"mouseup",D)),l=n.add(s,"div",{class:"mce-resize-helper","data-mce-bogus":"all"},f+" &times; "+g)})(h)})),e.elm=h,n.setStyles(h,{left:N*e[0]+C-h.offsetWidth/2,top:T*e[1]+E-h.offsetHeight/2})})):B(!1)},O=st(T,0),B=(e=!0)=>{O.cancel(),M(),a&&e&&a.removeAttribute(Pg),pe(Ig,((e,t)=>{const o=n.get("mceResizeHandle"+t);o&&(n.unbind(o),n.remove(o))}))},P=(e,t)=>n.isChildOf(e,t),L=o=>{if(h||t.removed||t.composing)return;const r="mousedown"===o.type?o.target:e.getNode(),a=dr(mn.fromDom(r),"table,img,figure.image,hr,video,span.mce-preview-object,details").map((e=>e.dom)).filter((e=>n.isEditable(e.parentElement)||"IMG"===e.nodeName&&n.isEditable(e))).getOrUndefined(),i=C(a)?n.getAttrib(a,Pg,"1"):"1";if(q(n.select(`img[${Pg}],hr[${Pg}]`),(e=>{e.removeAttribute(Pg)})),C(a)&&P(a,s)&&Tg(t)){I();const t=e.getStart(!0);if(P(t,a)&&P(e.getEnd(!0),a))return n.setAttrib(a,Pg,i),void O.throttle(a)}B()},M=()=>{pe(Ig,(e=>{e.elm&&(n.unbind(e.elm),delete e.elm)}))},I=()=>{try{t.getDoc().execCommand("enableObjectResizing",!1,"false")}catch(e){}};return t.on("init",(()=>{I(),t.on("NodeChange ResizeEditor ResizeWindow ResizeContent drop",L),t.on("keyup compositionend",(e=>{a&&"TABLE"===a.nodeName&&L(e)})),t.on("hide blur",B),t.on("contextmenu longpress",S,!0)})),t.on("remove",M),{isResizable:k,showResizeRect:T,hideResizeRect:B,updateResizeRect:L,destroy:()=>{O.cancel(),a=i=c=null}}},Ug=(e,t,n)=>{const o=Pn(mn.fromDom(n));return zr(o.dom,e,t).map((e=>{const t=n.createRange();return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),t})).getOrUndefined()},zg=(e,t)=>C(e)&&C(t)&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset,jg=(e,t,n)=>null!==((e,t,n)=>{let o=e;for(;o&&o!==t;){if(n(o))return o;o=o.parentNode}return null})(e,t,n),$g=(e,t,n)=>jg(e,t,(e=>e.nodeName===n)),Hg=(e,t)=>Wi(e)&&!jg(e,t,zu),Vg=(e,t,n)=>{const o=t.parentNode;if(o){const r=new Hr(t,e.getParent(o,e.isBlock)||e.getRoot());let s;for(;s=r[n?"prev":"next"]();)if(ms(s))return!0}return!1},qg=(e,t,n,o,r)=>{const s=e.getRoot(),a=e.schema.getNonEmptyElements(),i=r.parentNode;let l,d;if(!i)return I.none();const c=e.getParent(i,e.isBlock)||s;if(o&&ms(r)&&t&&e.isEmpty(c))return I.some(Ul(i,e.nodeIndex(r)));const m=new Hr(r,c);for(;d=m[o?"prev":"next"]();){if("false"===e.getContentEditableParent(d)||Hg(d,s))return I.none();if(ss(d)&&d.data.length>0)return $g(d,s,"A")?I.none():I.some(Ul(d,o?d.data.length:0));if(e.isBlock(d)||a[d.nodeName.toLowerCase()])return I.none();l=d}return ls(l)?I.none():n&&l?I.some(Ul(l,0)):I.none()},Wg=(e,t,n,o)=>{const r=e.getRoot();let s,a=!1,i=n?o.startContainer:o.endContainer,l=n?o.startOffset:o.endOffset;const d=Xr(i)&&l===i.childNodes.length,c=e.schema.getNonEmptyElements();let m=n;if(Wi(i))return I.none();if(Xr(i)&&l>i.childNodes.length-1&&(m=!1),ds(i)&&(i=r,l=0),i===r){if(m&&(s=i.childNodes[l>0?l-1:0],s)){if(Wi(s))return I.none();if(c[s.nodeName]||ns(s))return I.none()}if(i.hasChildNodes()){if(l=Math.min(!m&&l>0?l-1:l,i.childNodes.length-1),i=i.childNodes[l],l=ss(i)&&d?i.data.length:0,!t&&i===r.lastChild&&ns(i))return I.none();if(((e,t)=>{let n=t;for(;n&&n!==e;){if(ps(n))return!0;n=n.parentNode}return!1})(r,i)||Wi(i))return I.none();if(Es(i))return I.none();if(i.hasChildNodes()&&!ns(i)){s=i;const t=new Hr(i,r);do{if(ps(s)||Wi(s)){a=!1;break}if(ss(s)&&s.data.length>0){l=m?0:s.data.length,i=s,a=!0;break}if(c[s.nodeName.toLowerCase()]&&!vs(s)){l=e.nodeIndex(s),i=s.parentNode,m||l++,a=!0;break}}while(s=m?t.next():t.prev())}}}return t&&(ss(i)&&0===l&&qg(e,d,t,!0,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),Xr(i)&&(s=i.childNodes[l],s||(s=i.childNodes[l-1]),!s||!ms(s)||(e=>{var t;return"A"===(null===(t=e.previousSibling)||void 0===t?void 0:t.nodeName)})(s)||Vg(e,s,!1)||Vg(e,s,!0)||qg(e,d,t,!0,s).each((e=>{i=e.container(),l=e.offset(),a=!0})))),m&&!t&&ss(i)&&l===i.data.length&&qg(e,d,t,!1,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),a&&i?I.some(Ul(i,l)):I.none()},Kg=(e,t)=>{const n=t.collapsed,o=t.cloneRange(),r=Ul.fromRangeStart(t);return Wg(e,n,!0,o).each((e=>{n&&Ul.isAbove(r,e)||o.setStart(e.container(),e.offset())})),n||Wg(e,n,!1,o).each((e=>{o.setEnd(e.container(),e.offset())})),n&&o.collapse(!0),zg(t,o)?I.none():I.some(o)},Yg=(e,t)=>e.splitText(t),Gg=e=>{let t=e.startContainer,n=e.startOffset,o=e.endContainer,r=e.endOffset;if(t===o&&ss(t)){if(n>0&&n<t.data.length)if(o=Yg(t,n),t=o.previousSibling,r>n){r-=n;const e=Yg(o,r).previousSibling;t=o=e,r=e.data.length,n=0}else r=0}else if(ss(t)&&n>0&&n<t.data.length&&(t=Yg(t,n),n=0),ss(o)&&r>0&&r<o.data.length){const e=Yg(o,r).previousSibling;o=e,r=e.data.length}return{startContainer:t,startOffset:n,endContainer:o,endOffset:r}},Xg=e=>({walk:(t,n)=>eg(e,t,n),split:Gg,expand:(t,n={type:"word"})=>{if("word"===n.type){const n=Jf(e,t,[{inline:"span"}],{includeTrailingSpace:!1,expandToBlock:!1}),o=e.createRng();return o.setStart(n.startContainer,n.startOffset),o.setEnd(n.endContainer,n.endOffset),o}return t},normalize:t=>Kg(e,t).fold(L,(e=>(t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0)))});Xg.compareRanges=zg,Xg.getCaretRangeFromPoint=Ug,Xg.getSelectedNode=il,Xg.getNode=ll;const Qg=e=>"textarea"===En(e),Zg=(e,t)=>{const n=(e=>{const t=e.dom.ownerDocument,n=t.body,o=t.defaultView,r=t.documentElement;if(n===e.dom)return Wo(n.offsetLeft,n.offsetTop);const s=Ko(null==o?void 0:o.pageYOffset,r.scrollTop),a=Ko(null==o?void 0:o.pageXOffset,r.scrollLeft),i=Ko(r.clientTop,n.clientTop),l=Ko(r.clientLeft,n.clientLeft);return Yo(e).translate(a-l,s-i)})(e),o=(e=>Vo.get(e))(e);return{element:e,bottom:n.top+o,height:o,pos:n,cleanup:t}},Jg=(e,t,n,o)=>{op(e,((r,s)=>tp(e,t,n,o)),n)},ep=(e,t,n,o,r)=>{const s={elm:o.element.dom,alignToTop:r};((e,t)=>e.dispatch("ScrollIntoView",t).isDefaultPrevented())(e,s)||(n(e,t,Go(t).top,o,r),((e,t)=>{e.dispatch("AfterScrollIntoView",t)})(e,s))},tp=(e,t,n,o)=>{const r=mn.fromDom(e.getBody()),s=mn.fromDom(e.getDoc());r.dom.offsetWidth;const a=((e,t)=>{const n=((e,t)=>{const n=Hn(e);if(0===n.length||Qg(e))return{element:e,offset:t};if(t<n.length&&!Qg(n[t]))return{element:n[t],offset:0};{const o=n[n.length-1];return Qg(o)?{element:e,offset:t}:"img"===En(o)?{element:o,offset:1}:An(o)?{element:o,offset:er(o).length}:{element:o,offset:Hn(o).length}}})(e,t),o=mn.fromHtml('<span data-mce-bogus="all" style="display: inline-block;">\ufeff</span>');return io(n.element,o),Zg(o,(()=>_o(o)))})(mn.fromDom(n.startContainer),n.startOffset);ep(e,s,t,a,o),a.cleanup()},np=(e,t,n,o)=>{const r=mn.fromDom(e.getDoc());ep(e,r,n,(e=>Zg(mn.fromDom(e),S))(t),o)},op=(e,t,n)=>{const o=n.startContainer,r=n.startOffset,s=n.endContainer,a=n.endOffset;t(mn.fromDom(o),mn.fromDom(s));const i=e.dom.createRng();i.setStart(o,r),i.setEnd(s,a),e.selection.setRng(n)},rp=(e,t,n,o,r)=>{const s=t.pos;if(o)Xo(s.left,Math.max(0,s.top-30),r);else{const o=s.top-n+t.height+30;Xo(-e.getBody().getBoundingClientRect().left,o,r)}},sp=(e,t,n,o,r,s)=>{const a=o+n,i=r.pos.top,l=r.bottom,d=l-i>=o;i<n?rp(e,r,o,!1!==s,t):i>a?rp(e,r,o,d?!1!==s:!0===s,t):l>a&&!d&&rp(e,r,o,!0===s,t)},ap=(e,t,n,o,r)=>{const s=Pn(t).dom.innerHeight;sp(e,t,n,s,o,r)},ip=(e,t,n,o,r)=>{const s=Pn(t).dom.innerHeight;sp(e,t,n,s,o,r);const a=Cg(o.element),i=$r(window);a.top<i.y?Qo(o.element,!1!==r):a.top>i.bottom&&Qo(o.element,!0===r)},lp=(e,t,n)=>Jg(e,ap,t,n),dp=(e,t,n)=>np(e,t,ap,n),cp=(e,t,n)=>Jg(e,ip,t,n),mp=(e,t,n)=>np(e,t,ip,n),up=(e,t,n)=>{(e.inline?lp:cp)(e,t,n)},fp=(e,t)=>t.collapsed?e.isEditable(t.startContainer):e.isEditable(t.startContainer)&&e.isEditable(t.endContainer),gp=(e,t,n,o,r)=>{const s=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return I.from(s).map(mn.fromDom).map((e=>o&&t.collapsed?e:Vn(e,r(e,a)).getOr(e))).bind((e=>Nn(e)?I.some(e):Ln(e).filter(Nn))).map((e=>e.dom)).getOr(e)},pp=(e,t,n=!1)=>gp(e,t,!0,n,((e,t)=>Math.min(Kn(e),t))),hp=(e,t,n=!1)=>gp(e,t,!1,n,((e,t)=>t>0?t-1:t)),bp=(e,t)=>{const n=e;for(;e&&ss(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},vp=(e,t)=>V(t,(t=>{const n=e.dispatch("GetSelectionRange",{range:t});return n.range!==t?n.range:t})),yp={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Cp=(e,t,n)=>{const o=n?"lastChild":"firstChild",r=n?"prev":"next";if(e[o])return e[o];if(e!==t){let n=e[r];if(n)return n;for(let o=e.parent;o&&o!==t;o=o.parent)if(n=o[r],n)return n}},wp=e=>{var t;const n=null!==(t=e.value)&&void 0!==t?t:"";if(!qr(n))return!1;const o=e.parent;return!o||"span"===o.name&&!o.attr("style")||!/^[ ]+$/.test(n)},Ep=e=>{const t="a"===e.name&&!e.attr("href")&&e.attr("id");return e.attr("name")||e.attr("id")&&!e.firstChild||e.attr("data-mce-bookmark")||t};class xp{static create(e,t){const n=new xp(e,yp[e]||1);return t&&pe(t,((e,t)=>{n.attr(t,e)})),n}constructor(e,t){this.name=e,this.type=t,1===t&&(this.attributes=[],this.attributes.map={})}replace(e){const t=this;return e.parent&&e.remove(),t.insert(e,t),t.remove(),t}attr(e,t){const n=this;if(!u(e))return C(e)&&pe(e,((e,t)=>{n.attr(t,e)})),n;const o=n.attributes;if(o){if(void 0!==t){if(null===t){if(e in o.map){delete o.map[e];let t=o.length;for(;t--;)if(o[t].name===e)return o.splice(t,1),n}return n}if(e in o.map){let n=o.length;for(;n--;)if(o[n].name===e){o[n].value=t;break}}else o.push({name:e,value:t});return o.map[e]=t,n}return o.map[e]}}clone(){const e=this,t=new xp(e.name,e.type),n=e.attributes;if(n){const e=[];e.map={};for(let t=0,o=n.length;t<o;t++){const o=n[t];"id"!==o.name&&(e[e.length]={name:o.name,value:o.value},e.map[o.name]=o.value)}t.attributes=e}return t.value=e.value,t}wrap(e){const t=this;return t.parent&&(t.parent.insert(e,t),e.append(t)),t}unwrap(){const e=this;for(let t=e.firstChild;t;){const n=t.next;e.insert(t,e,!0),t=n}e.remove()}remove(){const e=this,t=e.parent,n=e.next,o=e.prev;return t&&(t.firstChild===e?(t.firstChild=n,n&&(n.prev=null)):o&&(o.next=n),t.lastChild===e?(t.lastChild=o,o&&(o.next=null)):n&&(n.prev=o),e.parent=e.next=e.prev=null),e}append(e){const t=this;e.parent&&e.remove();const n=t.lastChild;return n?(n.next=e,e.prev=n,t.lastChild=e):t.lastChild=t.firstChild=e,e.parent=t,e}insert(e,t,n){e.parent&&e.remove();const o=t.parent||this;return n?(t===o.firstChild?o.firstChild=e:t.prev&&(t.prev.next=e),e.prev=t.prev,e.next=t,t.prev=e):(t===o.lastChild?o.lastChild=e:t.next&&(t.next.prev=e),e.next=t.next,e.prev=t,t.next=e),e.parent=o,e}getAll(e){const t=this,n=[];for(let o=t.firstChild;o;o=Cp(o,t))o.name===e&&n.push(o);return n}children(){const e=[];for(let t=this.firstChild;t;t=t.next)e.push(t);return e}empty(){const e=this;if(e.firstChild){const t=[];for(let n=e.firstChild;n;n=Cp(n,e))t.push(n);let n=t.length;for(;n--;){const e=t[n];e.parent=e.firstChild=e.lastChild=e.next=e.prev=null}}return e.firstChild=e.lastChild=null,e}isEmpty(e,t={},n){var o;const r=this;let s=r.firstChild;if(Ep(r))return!1;if(s)do{if(1===s.type){if(s.attr("data-mce-bogus"))continue;if(e[s.name])return!1;if(Ep(s))return!1}if(8===s.type)return!1;if(3===s.type&&!wp(s))return!1;if(3===s.type&&s.parent&&t[s.parent.name]&&qr(null!==(o=s.value)&&void 0!==o?o:""))return!1;if(n&&n(s))return!1}while(s=Cp(s,r));return!0}walk(e){return Cp(this,null,e)}}const Sp=dn.makeMap("NOSCRIPT STYLE SCRIPT XMP IFRAME NOEMBED NOFRAMES PLAINTEXT"," "),_p=e=>u(e.nodeValue)&&e.nodeValue.includes(Ui),kp=e=>(0===e.length?"":`${V(e,(e=>`[${e}]`)).join(",")},`)+'[data-mce-bogus="all"]',Np=e=>document.createTreeWalker(e,NodeFilter.SHOW_COMMENT,(e=>_p(e)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP)),Ap=e=>document.createTreeWalker(e,NodeFilter.SHOW_TEXT,(e=>{if(_p(e)){const t=e.parentNode;return t&&Se(Sp,t.nodeName)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}return NodeFilter.FILTER_SKIP})),Rp=e=>null!==Np(e).nextNode(),Dp=e=>null!==Ap(e).nextNode(),Tp=(e,t)=>null!==t.querySelector(kp(e)),Op=(e,t)=>{q(((e,t)=>t.querySelectorAll(kp(e)))(e,t),(t=>{const n=mn.fromDom(t);"all"===vo(n,"data-mce-bogus")?_o(n):q(e,(e=>{Co(n,e)&&wo(n,e)}))}))},Bp=e=>{let t=e.nextNode();for(;null!==t;)t.nodeValue=null,t=e.nextNode()},Pp=_(Bp,Np),Lp=_(Bp,Ap),Mp=(e,t)=>{const n=[{condition:D(Tp,t),action:D(Op,t)},{condition:Rp,action:Pp},{condition:Dp,action:Lp}];let o=e,r=!1;return q(n,(({condition:t,action:n})=>{t(o)&&(r||(o=e.cloneNode(!0),r=!0),n(o))})),o},Ip=e=>{const t=xr(e,"[data-mce-bogus]");q(t,(e=>{"all"===vo(e,"data-mce-bogus")?_o(e):Di(e)?(io(e,mn.fromText(lt)),_o(e)):ko(e)}))},Fp=e=>{const t=xr(e,"input");q(t,(e=>{wo(e,"name")}))},Up=(e,t,n)=>{let o;return o="raw"===t.format?dn.trim(ji(Mp(n,e.serializer.getTempAttrs()).innerHTML)):"text"===t.format?((e,t)=>{const n=e.getDoc(),o=Gn(mn.fromDom(e.getBody())),r=mn.fromTag("div",n);ho(r,"data-mce-bogus","all"),Fo(r,{position:"fixed",left:"-9999999px",top:"0"}),Bo(r,t.innerHTML),Ip(r),Fp(r);const s=(e=>Yn(e)?e:mn.fromDom(Bn(e).dom.body))(o);mo(s,r);const a=ji(r.dom.innerText);return _o(r),a})(e,n):"tree"===t.format?e.serializer.serialize(n,t):((e,t)=>{const n=Ld(e),o=new RegExp(`^(<${n}[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/${n}>[\r\n]*|<br \\/>[\r\n]*)$`);return t.replace(o,"")})(e,e.serializer.serialize(n,t)),"text"!==t.format&&!Mi(mn.fromDom(n))&&u(o)?dn.trim(o):o},zp=dn.makeMap,jp=e=>{const t=[],n=(e=e||{}).indent,o=zp(e.indent_before||""),r=zp(e.indent_after||""),s=ba.getEncodeFunc(e.entity_encoding||"raw",e.entities),a="xhtml"!==e.element_format;return{start:(e,i,l)=>{if(n&&o[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}if(t.push("<",e),i)for(let e=0,n=i.length;e<n;e++){const n=i[e];t.push(" ",n.name,'="',s(n.value,!0),'"')}if(t[t.length]=!l||a?">":" />",l&&n&&r[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}},end:e=>{let o;t.push("</",e,">"),n&&r[e]&&t.length>0&&(o=t[t.length-1],o.length>0&&"\n"!==o&&t.push("\n"))},text:(e,n)=>{e.length>0&&(t[t.length]=n?e:s(e))},cdata:e=>{t.push("<![CDATA[",e,"]]>")},comment:e=>{t.push("\x3c!--",e,"--\x3e")},pi:(e,o)=>{o?t.push("<?",e," ",s(o),"?>"):t.push("<?",e,"?>"),n&&t.push("\n")},doctype:e=>{t.push("<!DOCTYPE",e,">",n?"\n":"")},reset:()=>{t.length=0},getContent:()=>t.join("").replace(/\n$/,"")}},$p=(e={},t=Pa())=>{const n=jp(e);return e.validate=!("validate"in e)||e.validate,{serialize:o=>{const r=e.validate,s={3:e=>{var t;n.text(null!==(t=e.value)&&void 0!==t?t:"",e.raw)},8:e=>{var t;n.comment(null!==(t=e.value)&&void 0!==t?t:"")},7:e=>{n.pi(e.name,e.value)},10:e=>{var t;n.doctype(null!==(t=e.value)&&void 0!==t?t:"")},4:e=>{var t;n.cdata(null!==(t=e.value)&&void 0!==t?t:"")},11:e=>{let t=e;if(t=t.firstChild)do{a(t)}while(t=t.next)}};n.reset();const a=e=>{var o;const i=s[e.type];if(i)i(e);else{const s=e.name,i=s in t.getVoidElements();let l=e.attributes;if(r&&l&&l.length>1){const n=[];n.map={};const o=t.getElementRule(e.name);if(o){for(let e=0,t=o.attributesOrder.length;e<t;e++){const t=o.attributesOrder[e];if(t in l.map){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}for(let e=0,t=l.length;e<t;e++){const t=l[e].name;if(!(t in n.map)){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}l=n}}if(n.start(s,l,i),Os(s))u(e.value)&&n.text(e.value,!0),n.end(s);else if(!i){let t=e.firstChild;if(t){"pre"!==s&&"textarea"!==s||3!==t.type||"\n"!==(null===(o=t.value)||void 0===o?void 0:o[0])||n.text("\n",!0);do{a(t)}while(t=t.next)}n.end(s)}}};return 1!==o.type||e.inner?3===o.type?s[3](o):s[11](o):a(o),n.getContent()}}},Hp=new Set;q(["margin","margin-left","margin-right","margin-top","margin-bottom","padding","padding-left","padding-right","padding-top","padding-bottom","border","border-width","border-style","border-color","background","background-attachment","background-clip","background-image","background-origin","background-position","background-repeat","background-size","float","position","left","right","top","bottom","z-index","display","transform","width","max-width","min-width","height","max-height","min-height","overflow","overflow-x","overflow-y","text-overflow","vertical-align","transition","transition-delay","transition-duration","transition-property","transition-timing-function"],(e=>{Hp.add(e)}));const Vp=new Set;q(["background-color"],(e=>{Vp.add(e)}));const qp=["font","text-decoration","text-emphasis"],Wp=(e,t)=>fe(((e,t)=>e.parseStyle(e.getAttrib(t,"style")))(e,t)),Kp=(e,t)=>H(Wp(e,t),(e=>(e=>Hp.has(e))(e))),Yp=(e,t,n)=>I.from(n.container()).filter(ss).exists((o=>{const r=e?0:-1;return t(o.data.charAt(n.offset()+r))})),Gp=D(Yp,!0,nf),Xp=D(Yp,!1,nf),Qp=e=>{const t=e.container();return ss(t)&&(0===t.data.length||zi(t.data)&&ag.isBookmarkNode(t.parentNode))},Zp=(e,t)=>n=>su(e?0:-1,n).filter(t).isSome(),Jp=e=>us(e)&&"block"===Uo(mn.fromDom(e),"display"),eh=e=>ps(e)&&!(e=>Xr(e)&&"all"===e.getAttribute("data-mce-bogus"))(e),th=Zp(!0,Jp),nh=Zp(!1,Jp),oh=Zp(!0,Cs),rh=Zp(!1,Cs),sh=Zp(!0,ns),ah=Zp(!1,ns),ih=Zp(!0,eh),lh=Zp(!1,eh),dh=(e,t)=>((e,t,n)=>yn(t,e)?In(e,(e=>n(e)||vn(e,t))).slice(0,-1):[])(e,t,L),ch=(e,t)=>[e].concat(dh(e,t)),mh=(e,t,n)=>Bu(e,t,n,Qp),uh=(e,t,n)=>Z(ch(mn.fromDom(t.container()),e),(e=>t=>e.isBlock(En(t)))(n)),fh=(e,t,n,o)=>mh(e,t.dom,n).forall((e=>uh(t,n,o).fold((()=>!ru(e,n,t.dom)),(o=>!ru(e,n,t.dom)&&yn(o,mn.fromDom(e.container())))))),gh=(e,t,n,o)=>uh(t,n,o).fold((()=>mh(e,t.dom,n).forall((e=>!ru(e,n,t.dom)))),(t=>mh(e,t.dom,n).isNone())),ph=D(gh,!1),hh=D(gh,!0),bh=D(fh,!1),vh=D(fh,!0),yh=e=>gu(e).exists(Di),Ch=(e,t,n,o)=>{const r=Y(ch(mn.fromDom(n.container()),t),(e=>o.isBlock(En(e)))),s=le(r).getOr(t);return Tu(e,s.dom,n).filter(yh)},wh=(e,t,n)=>gu(t).exists(Di)||Ch(!0,e,t,n).isSome(),Eh=(e,t,n)=>(e=>I.from(e.getNode(!0)).map(mn.fromDom))(t).exists(Di)||Ch(!1,e,t,n).isSome(),xh=D(Ch,!1),Sh=D(Ch,!0),_h=e=>Ul.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd(),kh=(e,t,n)=>{const o=Y(ch(mn.fromDom(t.container()),e),(e=>n.isBlock(En(e))));return le(o).getOr(e)},Nh=(e,t,n)=>_h(t)?Xp(t):Xp(t)||Mu(kh(e,t,n).dom,t).exists(Xp),Ah=(e,t,n)=>_h(t)?Gp(t):Gp(t)||Lu(kh(e,t,n).dom,t).exists(Gp),Rh=e=>gu(e).bind((e=>sr(e,Nn))).exists((e=>(e=>$(["pre","pre-wrap"],e))(Uo(e,"white-space")))),Dh=(e,t)=>n=>{return o=new Hr(n,e)[t](),C(o)&&ps(o)&&Km(o);var o},Th=(e,t,n)=>!Rh(t)&&(((e,t,n)=>((e,t)=>Mu(e.dom,t).isNone())(e,t)||((e,t)=>Lu(e.dom,t).isNone())(e,t)||ph(e,t,n)||hh(e,t,n)||Eh(e,t,n)||wh(e,t,n))(e,t,n)||Nh(e,t,n)||Ah(e,t,n)),Oh=(e,t,n)=>!Rh(t)&&(ph(e,t,n)||bh(e,t,n)||Eh(e,t,n)||Nh(e,t,n)||((e,t)=>{const n=Mu(e.dom,t).getOr(t),o=Dh(e.dom,"prev");return t.isAtStart()&&(o(t.container())||o(n.container()))})(e,t)),Bh=(e,t,n)=>!Rh(t)&&(hh(e,t,n)||vh(e,t,n)||wh(e,t,n)||Ah(e,t,n)||((e,t)=>{const n=Lu(e.dom,t).getOr(t),o=Dh(e.dom,"next");return t.isAtEnd()&&(o(t.container())||o(n.container()))})(e,t)),Ph=(e,t,n)=>Oh(e,t,n)||Bh(e,(e=>{const t=e.container(),n=e.offset();return ss(t)&&n<t.data.length?Ul(t,n+1):e})(t),n),Lh=(e,t)=>ef(e.charAt(t)),Mh=(e,t)=>nf(e.charAt(t)),Ih=(e,t,n,o)=>{const r=t.data,s=Ul(t,0);return n||!Lh(r,0)||Ph(e,s,o)?!!(n&&Mh(r,0)&&Oh(e,s,o))&&(t.data=dt+r.slice(1),!0):(t.data=" "+r.slice(1),!0)},Fh=(e,t,n,o)=>{const r=t.data,s=Ul(t,r.length-1);return n||!Lh(r,r.length-1)||Ph(e,s,o)?!!(n&&Mh(r,r.length-1)&&Bh(e,s,o))&&(t.data=r.slice(0,-1)+dt,!0):(t.data=r.slice(0,-1)+" ",!0)},Uh=(e,t,n)=>{const o=t.container();if(!ss(o))return I.none();if((e=>{const t=e.container();return ss(t)&&Ge(t.data,dt)})(t)){const r=Ih(e,o,!1,n)||(e=>{const t=e.data,n=(e=>{const t=e.split("");return V(t,((e,n)=>ef(e)&&n>0&&n<t.length-1&&of(t[n-1])&&of(t[n+1])?" ":e)).join("")})(t);return n!==t&&(e.data=n,!0)})(o)||Fh(e,o,!1,n);return $e(r,t)}if(Ph(e,t,n)){const r=Ih(e,o,!0,n)||Fh(e,o,!0,n);return $e(r,t)}return I.none()},zh=(e,t,n,o)=>{if(0===n)return;const r=mn.fromDom(e),s=rr(r,(e=>o.isBlock(En(e)))).getOr(r),a=e.data.slice(t,t+n),i=t+n>=e.data.length&&Bh(s,Ul(e,e.data.length),o),l=0===t&&Oh(s,Ul(e,0),o);e.replaceData(t,n,Kr(a,4,l,i))},jh=(e,t,n)=>{const o=e.data.slice(t),r=o.length-et(o).length;zh(e,t,r,n)},$h=(e,t,n)=>{const o=e.data.slice(0,t),r=o.length-tt(o).length;zh(e,t-r,r,n)},Hh=(e,t,n,o,r=!0)=>{const s=tt(e.data).length,a=r?e:t,i=r?t:e;return r?a.appendData(i.data):a.insertData(0,i.data),_o(mn.fromDom(i)),o&&jh(a,s,n),a},Vh=(e,t)=>((e,t)=>{const n=e.container(),o=e.offset();return!Ul.isTextPosition(e)&&n===t.parentNode&&o>Ul.before(t).offset()})(t,e)?Ul(t.container(),t.offset()-1):t,qh=e=>{return yl(e.previousSibling)?I.some((t=e.previousSibling,ss(t)?Ul(t,t.data.length):Ul.after(t))):e.previousSibling?Fu(e.previousSibling):I.none();var t},Wh=e=>{return yl(e.nextSibling)?I.some((t=e.nextSibling,ss(t)?Ul(t,0):Ul.before(t))):e.nextSibling?Iu(e.nextSibling):I.none();var t},Kh=(e,t,n)=>((e,t,n)=>e?((e,t)=>Wh(t).orThunk((()=>qh(t))).orThunk((()=>((e,t)=>Lu(e,Ul.after(t)).orThunk((()=>Mu(e,Ul.before(t)))))(e,t))))(t,n):((e,t)=>qh(t).orThunk((()=>Wh(t))).orThunk((()=>((e,t)=>I.from(t.previousSibling?t.previousSibling:t.parentNode).bind((t=>Mu(e,Ul.before(t)))).orThunk((()=>Lu(e,Ul.after(t)))))(e,t))))(t,n))(e,t,n).map(D(Vh,n)),Yh=(e,t,n)=>{n.fold((()=>{e.focus()}),(n=>{e.selection.setRng(n.toRange(),t)}))},Gh=(e,t)=>t&&Se(e.schema.getBlockElements(),En(t)),Xh=(e,t,n,o=!0,r=!1)=>{const s=Kh(t,e.getBody(),n.dom),a=rr(n,D(Gh,e),(i=e.getBody(),e=>e.dom===i));var i;const l=((e,t,n,o)=>{const r=Un(e).filter(An),s=zn(e).filter(An);return _o(e),(a=r,i=s,l=t,d=(e,t,r)=>{const s=e.dom,a=t.dom,i=s.data.length;return Hh(s,a,n,o),r.container()===a?Ul(s,i):r},a.isSome()&&i.isSome()&&l.isSome()?I.some(d(a.getOrDie(),i.getOrDie(),l.getOrDie())):I.none()).orThunk((()=>(o&&(r.each((e=>$h(e.dom,e.dom.length,n))),s.each((e=>jh(e.dom,0,n)))),t)));var a,i,l,d})(n,s,e.schema,((e,t)=>Se(e.schema.getTextInlineElements(),En(t)))(e,n));e.dom.isEmpty(e.getBody())?(e.setContent(""),e.selection.setCursorLocation()):a.bind((t=>((e,t,n)=>{if(Rs(e,t)){const e=mn.fromHtml('<br data-mce-bogus="1">');return n?q(Hn(t),(e=>{Ff(e)||_o(e)})):So(t),mo(t,e),I.some(Ul.before(e.dom))}return I.none()})(e.schema,t,r))).fold((()=>{o&&Yh(e,t,l)}),(n=>{o&&Yh(e,t,I.some(n))}))},Qh=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,Zh=(e,t)=>hn(mn.fromDom(t),lc(e))&&!Ks(e.schema,t)&&e.dom.isEditable(t),Jh=e=>{var t;return"rtl"===li.DOM.getStyle(e,"direction",!0)||(e=>Qh.test(e))(null!==(t=e.textContent)&&void 0!==t?t:"")},eb=(e,t,n)=>{const o=((e,t,n)=>Y(li.DOM.getParents(n.container(),"*",t),e))(e,t,n);return I.from(o[o.length-1])},tb=(e,t)=>{const n=t.container(),o=t.offset();return e?qi(n)?ss(n.nextSibling)?Ul(n.nextSibling,0):Ul.after(n):Yi(t)?Ul(n,o+1):t:qi(n)?ss(n.previousSibling)?Ul(n.previousSibling,n.previousSibling.data.length):Ul.before(n):Gi(t)?Ul(n,o-1):t},nb=D(tb,!0),ob=D(tb,!1),rb=(e,t)=>{const n=e=>e.stopImmediatePropagation();e.on("beforeinput input",n,!0),e.getDoc().execCommand(t),e.off("beforeinput input",n)},sb=e=>rb(e,"Delete"),ab=e=>rb(e,"ForwardDelete"),ib=e=>Ti(e)||Bi(e),lb=(e,t)=>yn(e,t)?sr(t,ib,(e=>t=>Ue(Ln(t),e,vn))(e)):I.none(),db=(e,t=!0)=>{e.dom.isEmpty(e.getBody())&&e.setContent("",{no_selection:!t})},cb=(e,t,n)=>je(Iu(n),Fu(n),((o,r)=>{const s=tb(!0,o),a=tb(!1,r),i=tb(!1,t);return e?Lu(n,i).exists((e=>e.isEqual(a)&&t.isEqual(s))):Mu(n,i).exists((e=>e.isEqual(s)&&t.isEqual(a)))})).getOr(!0),mb=e=>(_n(e)?Un(e):Wn(e)).bind(mb).orThunk((()=>I.some(e))),ub=(e,t,n,o=!0)=>{var r;t.deleteContents();const s=mb(n).getOr(n),a=mn.fromDom(null!==(r=e.dom.getParent(s.dom,e.dom.isBlock))&&void 0!==r?r:n.dom);if(a.dom===e.getBody()?db(e,o):Rs(e.schema,a,{checkRootAsContent:!1})&&(Fi(a),o&&e.selection.setCursorLocation(a.dom,0)),!vn(n,a)){const t=Ue(Ln(a),n)?[]:Fn(a);q(t.concat(Hn(n)),(t=>{vn(t,a)||yn(t,a)||!Rs(e.schema,t)||_o(t)}))}},fb=e=>xr(e,"td,th"),gb=(e,t)=>lf(mn.fromDom(e),t),pb=(e,t)=>({start:e,end:t}),hb=ke([{singleCellTable:["rng","cell"]},{fullTable:["table"]},{partialTable:["cells","outsideDetails"]},{multiTable:["startTableCells","endTableCells","betweenRng"]}]),bb=(e,t)=>dr(mn.fromDom(e),"td,th",t),vb=e=>!vn(e.start,e.end),yb=(e,t)=>lf(e.start,t).bind((n=>lf(e.end,t).bind((e=>$e(vn(n,e),n))))),Cb=e=>t=>yb(t,e).map((e=>((e,t,n)=>({rng:e,table:t,cells:n}))(t,e,fb(e)))),wb=(e,t,n,o)=>{if(n.collapsed||!e.forall(vb))return I.none();if(t.isSameTable){const t=e.bind(Cb(o));return I.some({start:t,end:t})}{const e=bb(n.startContainer,o),t=bb(n.endContainer,o),r=e.bind((e=>t=>lf(t,e).bind((e=>de(fb(e)).map((e=>pb(t,e))))))(o)).bind(Cb(o)),s=t.bind((e=>t=>lf(t,e).bind((e=>le(fb(e)).map((e=>pb(e,t))))))(o)).bind(Cb(o));return I.some({start:r,end:s})}},Eb=(e,t)=>J(e,(e=>vn(e,t))),xb=e=>je(Eb(e.cells,e.rng.start),Eb(e.cells,e.rng.end),((t,n)=>e.cells.slice(t,n+1))),Sb=(e,t)=>{const{startTable:n,endTable:o}=t,r=e.cloneRange();return n.each((e=>r.setStartAfter(e.dom))),o.each((e=>r.setEndBefore(e.dom))),r},_b=(e,t)=>{const n=(e=>t=>vn(e,t))(e),o=((e,t)=>{const n=bb(e.startContainer,t),o=bb(e.endContainer,t);return je(n,o,pb)})(t,n),r=((e,t)=>{const n=gb(e.startContainer,t),o=gb(e.endContainer,t),r=n.isSome(),s=o.isSome(),a=je(n,o,vn).getOr(!1);return(e=>je(e.startTable,e.endTable,((t,n)=>{const o=kr(t,(e=>vn(e,n))),r=kr(n,(e=>vn(e,t)));return o||r?{...e,startTable:o?I.none():e.startTable,endTable:r?I.none():e.endTable,isSameTable:!1,isMultiTable:!1}:e})).getOr(e))({startTable:n,endTable:o,isStartInTable:r,isEndInTable:s,isSameTable:a,isMultiTable:!a&&r&&s})})(t,n);return((e,t,n)=>e.exists((e=>((e,t)=>!vb(e)&&yb(e,t).exists((e=>{const t=e.dom.rows;return 1===t.length&&1===t[0].cells.length})))(e,n)&&mf(e.start,t))))(o,t,n)?o.map((e=>hb.singleCellTable(t,e.start))):r.isMultiTable?((e,t,n,o)=>wb(e,t,n,o).bind((({start:e,end:o})=>{const r=e.bind(xb).getOr([]),s=o.bind(xb).getOr([]);if(r.length>0&&s.length>0){const e=Sb(n,t);return I.some(hb.multiTable(r,s,e))}return I.none()})))(o,r,t,n):((e,t,n,o)=>wb(e,t,n,o).bind((({start:e,end:t})=>e.or(t))).bind((e=>{const{isSameTable:o}=t,r=xb(e).getOr([]);if(o&&e.cells.length===r.length)return I.some(hb.fullTable(e.table));if(r.length>0){if(o)return I.some(hb.partialTable(r,I.none()));{const e=Sb(n,t);return I.some(hb.partialTable(r,I.some({...t,rng:e})))}}return I.none()})))(o,r,t,n)},kb=e=>q(e,(e=>{wo(e,"contenteditable"),Fi(e)})),Nb=(e,t,n,o)=>{const r=n.cloneRange();o?(r.setStart(n.startContainer,n.startOffset),r.setEndAfter(t.dom.lastChild)):(r.setStartBefore(t.dom.firstChild),r.setEnd(n.endContainer,n.endOffset)),Tb(e,r,t,!1).each((e=>e()))},Ab=e=>{const t=af(e),n=mn.fromDom(e.selection.getNode());bs(n.dom)&&Rs(e.schema,n)?e.selection.setCursorLocation(n.dom,0):e.selection.collapse(!0),t.length>1&&H(t,(e=>vn(e,n)))&&ho(n,"data-mce-selected","1")},Rb=(e,t,n)=>I.some((()=>{const o=e.selection.getRng(),r=n.bind((({rng:n,isStartInTable:r})=>{const s=((e,t)=>I.from(e.dom.getParent(t,e.dom.isBlock)).map(mn.fromDom))(e,r?n.endContainer:n.startContainer);n.deleteContents(),((e,t,n)=>{n.each((n=>{t?_o(n):(Fi(n),e.selection.setCursorLocation(n.dom,0))}))})(e,r,s.filter(D(Rs,e.schema)));const a=r?t[0]:t[t.length-1];return Nb(e,a,o,r),Rs(e.schema,a)?I.none():I.some(r?t.slice(1):t.slice(0,-1))})).getOr(t);kb(r),Ab(e)})),Db=(e,t,n,o)=>I.some((()=>{const r=e.selection.getRng(),s=t[0],a=n[n.length-1];Nb(e,s,r,!0),Nb(e,a,r,!1);const i=Rs(e.schema,s)?t:t.slice(1),l=Rs(e.schema,a)?n:n.slice(0,-1);kb(i.concat(l)),o.deleteContents(),Ab(e)})),Tb=(e,t,n,o=!0)=>I.some((()=>{ub(e,t,n,o)})),Ob=(e,t)=>I.some((()=>Xh(e,!1,t))),Bb=(e,t)=>Z(ch(t,e),Li),Pb=(e,t)=>Z(ch(t,e),Tn("caption")),Lb=(e,t)=>I.some((()=>{Fi(t),e.selection.setCursorLocation(t.dom,0)})),Mb=(e,t)=>e?sh(t):ah(t),Ib=(e,t,n)=>{const o=mn.fromDom(e.getBody());return Pb(o,n).fold((()=>((e,t,n,o)=>{const r=Ul.fromRangeStart(e.selection.getRng());return Bb(n,o).bind((o=>Rs(e.schema,o,{checkRootAsContent:!1})?Lb(e,o):((e,t,n,o,r)=>Ou(n,e.getBody(),r).bind((e=>Bb(t,mn.fromDom(e.getNode())).bind((e=>vn(e,o)?I.none():I.some(S))))))(e,n,t,o,r)))})(e,t,o,n).orThunk((()=>$e(((e,t)=>{const n=Ul.fromRangeStart(e.selection.getRng());return Mb(t,n)||Tu(t,e.getBody(),n).exists((e=>Mb(t,e)))})(e,t),S)))),(n=>((e,t,n,o)=>{const r=Ul.fromRangeStart(e.selection.getRng());return Rs(e.schema,o)?Lb(e,o):((e,t,n,o,r)=>Ou(n,e.getBody(),r).fold((()=>I.some(S)),(s=>((e,t,n,o)=>Iu(e.dom).bind((r=>Fu(e.dom).map((e=>t?n.isEqual(r)&&o.isEqual(e):n.isEqual(e)&&o.isEqual(r))))).getOr(!0))(o,n,r,s)?((e,t)=>Lb(e,t))(e,o):((e,t,n)=>Pb(e,mn.fromDom(n.getNode())).fold((()=>I.some(S)),(e=>$e(!vn(e,t),S))))(t,o,s))))(e,n,t,o,r)})(e,t,o,n)))},Fb=(e,t)=>{const n=mn.fromDom(e.selection.getStart(!0)),o=af(e);return e.selection.isCollapsed()&&0===o.length?Ib(e,t,n):((e,t,n)=>{const o=mn.fromDom(e.getBody()),r=e.selection.getRng();return 0!==n.length?Rb(e,n,I.none()):((e,t,n,o)=>Pb(t,o).fold((()=>((e,t,n)=>_b(t,n).bind((t=>t.fold(D(Tb,e),D(Ob,e),D(Rb,e),D(Db,e)))))(e,t,n)),(t=>((e,t)=>Lb(e,t))(e,t))))(e,o,r,t)})(e,n,o)},Ub=(e,t)=>{let n=t;for(;n&&n!==e;){if(gs(n)||ps(n))return n;n=n.parentNode}return null},zb=["data-ephox-","data-mce-","data-alloy-","data-snooker-","_"],jb=dn.each,$b=e=>{const t=e.dom,n=new Set(e.serializer.getTempAttrs()),o=e=>H(zb,(t=>Xe(e,t)))||n.has(e);return{compare:(e,n)=>{if(e.nodeName!==n.nodeName||e.nodeType!==n.nodeType)return!1;const r=e=>{const n={};return jb(t.getAttribs(e),(r=>{const s=r.nodeName.toLowerCase();"style"===s||o(s)||(n[s]=t.getAttrib(e,s))})),n},s=(e,t)=>{for(const n in e)if(Se(e,n)){const o=t[n];if(v(o))return!1;if(e[n]!==o)return!1;delete t[n]}for(const e in t)if(Se(t,e))return!1;return!0};if(Xr(e)&&Xr(n)){if(!s(r(e),r(n)))return!1;if(!s(t.parseStyle(t.getAttrib(e,"style")),t.parseStyle(t.getAttrib(n,"style"))))return!1}return!Ju(e)&&!Ju(n)},isAttributeInternal:o}},Hb=(e,t)=>{if(ss(e))return{container:e,offset:t};const n=Xg.getNode(e,t);return ss(n)?{container:n,offset:t>=e.childNodes.length?n.data.length:0}:n.previousSibling&&ss(n.previousSibling)?{container:n.previousSibling,offset:n.previousSibling.data.length}:n.nextSibling&&ss(n.nextSibling)?{container:n.nextSibling,offset:0}:{container:e,offset:t}},Vb=li.DOM,qb=(e,t,n,o)=>{if(dn.each(n.styles,((n,r)=>{e.setStyle(t,r,kf(n,o))})),n.styles){const n=e.getAttrib(t,"style");n&&e.setAttrib(t,"data-mce-style",n)}},Wb=(e,t,n,o,r)=>{const s=e.dom;w(n.onformat)&&n.onformat(t,n,o,r),qb(s,t,n,o),dn.each(n.attributes,((e,n)=>{s.setAttrib(t,n,kf(e,o))})),dn.each(n.classes,(e=>{const n=kf(e,o);s.hasClass(t,n)||s.addClass(t,n)}))},Kb=Nf,Yb=(e,t,n)=>{const o=e.formatter.get(n);if(o)for(let n=0;n<o.length;n++){const r=o[n];if(Pf(r)&&!1===r.inherit&&e.dom.is(t,r.selector))return!0}return!1},Gb=(e,t,n,o,r)=>{const s=e.dom.getRoot();if(t===s)return!1;const a=e.dom.getParent(t,(t=>!!Yb(e,t,n)||t.parentNode===s||!!Zb(e,t,n,o,!0)));return!!Zb(e,a,n,o,r)},Xb=(e,t,n)=>!(!Lf(n)||!Kb(t,n.inline))||!(!Bf(n)||!Kb(t,n.block))||!!Pf(n)&&Xr(t)&&e.is(t,n.selector),Qb=(e,t,n,o,r,s)=>{const a=n[o],i="attributes"===o;if(w(n.onmatch))return n.onmatch(t,n,o);if(a)if(ut(a)){for(let n=0;n<a.length;n++)if(i?e.getAttrib(t,a[n]):Rf(e,t,a[n]))return!0}else for(const o in a)if(Se(a,o)){const l=i?e.getAttrib(t,o):Rf(e,t,o),d=kf(a[o],s),c=y(l)||ot(l);if(c&&y(d))continue;if(r&&c&&!n.exact)return!1;if((!r||n.exact)&&!Kb(l,Af(d,o)))return!1}return!0},Zb=(e,t,n,o,r)=>{const s=e.formatter.get(n),a=e.dom;if(s&&Xr(t))for(let n=0;n<s.length;n++){const i=s[n];if(Xb(e.dom,t,i)&&Qb(a,t,i,"attributes",r,o)&&Qb(a,t,i,"styles",r,o)){const n=i.classes;if(n)for(let r=0;r<n.length;r++)if(!e.dom.hasClass(t,kf(n[r],o)))return;return i}}},Jb=(e,t,n,o,r)=>{if(o)return Gb(e,o,t,n,r);if(o=e.selection.getNode(),Gb(e,o,t,n,r))return!0;const s=e.selection.getStart();return!(s===o||!Gb(e,s,t,n,r))},ev=Ui,tv=e=>{if(e){const t=new Hr(e,e);for(let e=t.current();e;e=t.next())if(ss(e))return e}return null},nv=e=>{const t=mn.fromTag("span");return bo(t,{id:Uu,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&mo(t,mn.fromText(ev)),t},ov=(e,t,n)=>{const o=e.dom,r=e.selection;if(If(t))Xh(e,!1,mn.fromDom(t),n,!0);else{const e=r.getRng(),n=o.getParent(t,o.isBlock),s=e.startContainer,a=e.startOffset,i=e.endContainer,l=e.endOffset,d=(e=>{const t=tv(e);return t&&t.data.charAt(0)===ev&&t.deleteData(0,1),t})(t);o.remove(t,!0),s===d&&a>0&&e.setStart(d,a-1),i===d&&l>0&&e.setEnd(d,l-1),n&&o.isEmpty(n)&&Fi(mn.fromDom(n)),r.setRng(e)}},rv=(e,t,n)=>{const o=e.dom,r=e.selection;if(t)ov(e,t,n);else if(!(t=ju(e.getBody(),r.getStart())))for(;t=o.get(Uu);)ov(e,t,n)},sv=(e,t)=>(e.appendChild(t),t),av=(e,t)=>{var n;const o=G(e,((e,t)=>sv(e,t.cloneNode(!1))),t),r=null!==(n=o.ownerDocument)&&void 0!==n?n:document;return sv(o,r.createTextNode(ev))},iv=e=>tr(e,er(e).replace(new RegExp(`${dt}$`)," ")),lv=(e,t)=>{const n=()=>{null===t||e.dom.isEmpty(t)||Un(mn.fromDom(t)).each((e=>{An(e)?iv(e):ar(e,(e=>An(e))).each((e=>{An(e)&&iv(e)}))}))};e.once("input",(t=>{t.data&&!nf(t.data)&&(t.isComposing?e.once("compositionend",(()=>{n()})):n())}))},dv=(e,t,n,o)=>{const a=e.dom,i=e.selection;let l=!1;const d=e.formatter.get(t);if(!d)return;const c=i.getRng(),m=c.startContainer,u=c.startOffset;let f=m;ss(m)&&(u!==m.data.length&&(l=!0),f=f.parentNode);const g=[];let h;for(;f;){if(Zb(e,f,t,n,o)){h=f;break}f.nextSibling&&(l=!0),g.push(f),f=f.parentNode}if(h)if(l){const r=i.getBookmark();c.collapse(!0);let s=Jf(a,c,d,{includeTrailingSpace:!0});s=Gg(s),e.formatter.remove(t,n,s,o),i.moveToBookmark(r)}else{const l=ju(e.getBody(),h),d=C(l)?a.getParents(h.parentNode,M,l):[],c=nv(!1).dom;((e,t,n)=>{var o,r;const s=e.dom,a=s.getParent(n,D(Ef,e.schema));a&&s.isEmpty(a)?null===(o=n.parentNode)||void 0===o||o.replaceChild(t,n):((e=>{const t=xr(e,"br"),n=Y((e=>{const t=[];let n=e.dom;for(;n;)t.push(mn.fromDom(n)),n=n.lastChild;return t})(e).slice(-1),Di);t.length===n.length&&q(n,_o)})(mn.fromDom(n)),s.isEmpty(n)?null===(r=n.parentNode)||void 0===r||r.replaceChild(t,n):s.insertAfter(t,n))})(e,c,null!=l?l:h);const m=((e,t,n,o,a,i)=>{const l=e.formatter,d=e.dom,c=Y(fe(l.get()),(e=>e!==o&&!Ge(e,"removeformat"))),m=((e,t,n)=>X(n,((n,o)=>{const r=((e,t)=>Of(e,t,(e=>{const t=e=>w(e)||e.length>1&&"%"===e.charAt(0);return H(["styles","attributes"],(n=>xe(e,n).exists((e=>{const n=p(e)?e:Ee(e);return H(n,t)}))))})))(e,o);return e.formatter.matchNode(t,o,{},r)?n.concat([o]):n}),[]))(e,n,c);if(Y(m,(t=>!((e,t,n)=>{const o=["inline","block","selector","attributes","styles","classes"],a=e=>Ce(e,((e,t)=>H(o,(e=>e===t))));return Of(e,t,(t=>{const o=a(t);return Of(e,n,(e=>{const t=a(e);return((e,t,n=s)=>r(n).eq(e,t))(o,t)}))}))})(e,t,o))).length>0){const e=n.cloneNode(!1);return d.add(t,e),l.remove(o,a,e,i),d.remove(e),I.some(e)}return I.none()})(e,c,h,t,n,o),u=av([...g,...m.toArray(),...d],c);l&&ov(e,l,C(l)),i.setCursorLocation(u,1),lv(e,c),a.isEmpty(h)&&a.remove(h)}},cv=e=>{const t=nv(!1),n=av(e,t.dom);return{caretContainer:t,caretPosition:Ul(n,0)}},mv=(e,t)=>{const{caretContainer:n,caretPosition:o}=cv(t);return io(mn.fromDom(e),n),_o(mn.fromDom(e)),o},uv=(e,t)=>{if(zu(t.dom))return!1;const n=e.schema.getTextInlineElements();return Se(n,En(t))&&!zu(t.dom)&&!ts(t.dom)},fv=["fontWeight","fontStyle","color","fontSize","fontFamily"],gv=(e,t)=>{const n=e.get(t);return p(n)?Z(n,(e=>Lf(e)&&"span"===e.inline&&(e=>f(e.styles)&&H(fe(e.styles),(e=>$(fv,e))))(e))):I.none()},pv=(e,t)=>Mu(t,Ul.fromRangeStart(e)).isNone(),hv=(e,t)=>!1===Lu(t,Ul.fromRangeEnd(e)).exists((e=>!ms(e.getNode())||Lu(t,e).isSome())),bv=e=>t=>ws(t)&&e.isEditable(t),vv=e=>Y(e.getSelectedBlocks(),bv(e.dom)),yv=dn.each,Cv=e=>Xr(e)&&!Ju(e)&&!zu(e)&&!ts(e),wv=(e,t)=>{for(let n=e;n;n=n[t]){if(ss(n)&&nt(n.data))return e;if(Xr(n)&&!Ju(n))return n}return e},Ev=(e,t,n)=>{const o=$b(e),r=Qr(t)&&e.dom.isEditable(t),s=Qr(n)&&e.dom.isEditable(n);if(r&&s){const r=wv(t,"previousSibling"),s=wv(n,"nextSibling");if(o.compare(r,s)){for(let e=r.nextSibling;e&&e!==s;){const t=e;e=e.nextSibling,r.appendChild(t)}return e.dom.remove(s),dn.each(dn.grep(s.childNodes),(e=>{r.appendChild(e)})),r}}return n},xv=(e,t,n,o)=>{var r;if(o&&!1!==t.merge_siblings){const t=null!==(r=Ev(e,wf(o),o))&&void 0!==r?r:o;Ev(e,t,wf(t,!0))}},Sv=(e,t,n)=>{yv(e.childNodes,(e=>{Cv(e)&&(t(e)&&n(e),e.hasChildNodes()&&Sv(e,t,n))}))},_v=(e,t)=>n=>!(!n||!Rf(e,n,t)),kv=(e,t,n)=>o=>{e.setStyle(o,t,n),""===o.getAttribute("style")&&o.removeAttribute("style"),((e,t)=>{"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)})(e,o)},Nv=ke([{keep:[]},{rename:["name"]},{removed:[]}]),Av=/^(src|href|style)$/,Rv=dn.each,Dv=Nf,Tv=(e,t,n)=>e.isChildOf(t,n)&&t!==n&&!e.isBlock(n),Ov=(e,t,n)=>{let o=t[n?"startContainer":"endContainer"],r=t[n?"startOffset":"endOffset"];if(Xr(o)){const e=o.childNodes.length-1;!n&&r&&r--,o=o.childNodes[r>e?e:r]}return ss(o)&&n&&r>=o.data.length&&(o=new Hr(o,e.getBody()).next()||o),ss(o)&&!n&&0===r&&(o=new Hr(o,e.getBody()).prev()||o),o},Bv=(e,t)=>{const n=t?"firstChild":"lastChild",o=e[n];return(e=>/^(TR|TH|TD)$/.test(e.nodeName))(e)&&o?"TR"===e.nodeName&&o[n]||o:e},Pv=(e,t,n,o)=>{var r;const s=e.create(n,o);return null===(r=t.parentNode)||void 0===r||r.insertBefore(s,t),s.appendChild(t),s},Lv=(e,t,n,o,r)=>{const s=mn.fromDom(t),a=mn.fromDom(e.create(o,r)),i=n?$n(s):jn(s);return go(a,i),n?(io(s,a),co(a,s)):(lo(s,a),mo(a,s)),a.dom},Mv=(e,t,n)=>{const o=t.parentNode;let r;const s=e.dom,a=Ld(e);Bf(n)&&o===s.getRoot()&&(n.list_block&&Dv(t,n.list_block)||q(ce(t.childNodes),(t=>{xf(e,a,t.nodeName.toLowerCase())?r?r.appendChild(t):(r=Pv(s,t,a),s.setAttribs(r,Md(e))):r=null}))),(e=>Pf(e)&&Lf(e)&&Ue(xe(e,"mixed"),!0))(n)&&!Dv(n.inline,t)||s.remove(t,!0)},Iv=(e,t,n)=>E(e)?{name:t,value:null}:{name:e,value:kf(t,n)},Fv=(e,t)=>{""===e.getAttrib(t,"style")&&(t.removeAttribute("style"),t.removeAttribute("data-mce-style"))},Uv=(e,t,n,o,r)=>{let s=!1;Rv(n.styles,((a,i)=>{const{name:l,value:d}=Iv(i,a,o),c=Af(d,l);(n.remove_similar||h(d)||!Xr(r)||Dv(Rf(e,r,l),c))&&e.setStyle(t,l,""),s=!0})),s&&Fv(e,t)},zv=(e,t,n,o,r)=>{const s=e.dom,a=$b(e),i=e.schema;if(Lf(t)&&qs(i,t.inline)&&Ks(i,o)&&o.parentElement===e.getBody())return Mv(e,o,t),Nv.removed();if(!t.ceFalseOverride&&o&&"false"===s.getContentEditableParent(o))return Nv.keep();if(o&&!Xb(s,o,t)&&!((e,t)=>t.links&&"A"===e.nodeName)(o,t))return Nv.keep();const l=o,d=t.preserve_attributes;if(Lf(t)&&"all"===t.remove&&p(d)){const e=Y(s.getAttribs(l),(e=>$(d,e.name.toLowerCase())));if(s.removeAllAttribs(l),q(e,(e=>s.setAttrib(l,e.name,e.value))),e.length>0)return Nv.rename("span")}if("all"!==t.remove){Uv(s,l,t,n,r),Rv(t.attributes,((e,o)=>{const{name:a,value:i}=Iv(o,e,n);if(t.remove_similar||h(i)||!Xr(r)||Dv(s.getAttrib(r,a),i)){if("class"===a){const e=s.getAttrib(l,a);if(e){let t="";if(q(e.split(/\s+/),(e=>{/mce\-\w+/.test(e)&&(t+=(t?" ":"")+e)})),t)return void s.setAttrib(l,a,t)}}if(Av.test(a)&&l.removeAttribute("data-mce-"+a),"style"===a&&Jr(["li"])(l)&&"none"===s.getStyle(l,"list-style-type"))return l.removeAttribute(a),void s.setStyle(l,"list-style-type","none");"class"===a&&l.removeAttribute("className"),l.removeAttribute(a)}})),Rv(t.classes,(e=>{e=kf(e,n),Xr(r)&&!s.hasClass(r,e)||s.removeClass(l,e)}));const e=s.getAttribs(l);for(let t=0;t<e.length;t++){const n=e[t].nodeName;if(!a.isAttributeInternal(n))return Nv.keep()}}return"none"!==t.remove?(Mv(e,l,t),Nv.removed()):Nv.keep()},jv=(e,t,n,o)=>zv(e,t,n,o,o).fold(N(o),(t=>(e.dom.createFragment().appendChild(o),e.dom.rename(o,t))),N(null)),$v=(e,t,n,o,r)=>{(o||e.selection.isEditable())&&((e,t,n,o,r)=>{const s=e.formatter.get(t),a=s[0],i=e.dom,l=e.selection,d=o=>{const i=((e,t,n,o,r)=>{let s;return t.parentNode&&q(Tf(e.dom,t.parentNode).reverse(),(t=>{if(!s&&Xr(t)&&"_start"!==t.id&&"_end"!==t.id){const a=Zb(e,t,n,o,r);a&&!1!==a.split&&(s=t)}})),s})(e,o,t,n,r);return((e,t,n,o,r,s,a,i)=>{var l,d;let c,m;const u=e.dom;if(n){const s=n.parentNode;for(let n=o.parentNode;n&&n!==s;n=n.parentNode){let o=u.clone(n,!1);for(let n=0;n<t.length&&(o=jv(e,t[n],i,o),null!==o);n++);o&&(c&&o.appendChild(c),m||(m=o),c=o)}a.mixed&&u.isBlock(n)||(o=null!==(l=u.split(n,o))&&void 0!==l?l:o),c&&m&&(null===(d=r.parentNode)||void 0===d||d.insertBefore(c,r),m.appendChild(r),Lf(a)&&xv(e,a,0,c))}return o})(e,s,i,o,o,0,a,n)},c=t=>H(s,(o=>Hv(e,o,n,t,t))),m=t=>{const n=ce(t.childNodes),o=c(t)||H(s,(e=>Xb(i,t,e))),r=t.parentNode;if(!o&&C(r)&&Mf(a)&&c(r),a.deep&&n.length)for(let e=0;e<n.length;e++)m(n[e]);q(["underline","line-through","overline"],(n=>{Xr(t)&&e.dom.getStyle(t,"text-decoration")===n&&t.parentNode&&Df(i,t.parentNode)===n&&Hv(e,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:n}},void 0,t)}))},u=e=>{const t=i.get(e?"_start":"_end");if(t){let n=t[e?"firstChild":"lastChild"];return(e=>Ju(e)&&Xr(e)&&("_start"===e.id||"_end"===e.id))(n)&&(n=n[e?"firstChild":"lastChild"]),ss(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),i.remove(t,!0),n}return null},f=t=>{let n,o,r=Jf(i,t,s,{includeTrailingSpace:t.collapsed});if(a.split){if(r=Gg(r),n=Ov(e,r,!0),o=Ov(e,r),n!==o){if(n=Bv(n,!0),o=Bv(o,!1),Tv(i,n,o)){const e=I.from(n.firstChild).getOr(n);return d(Lv(i,e,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void u(!0)}if(Tv(i,o,n)){const e=I.from(o.lastChild).getOr(o);return d(Lv(i,e,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void u(!1)}n=Pv(i,n,"span",{id:"_start","data-mce-type":"bookmark"}),o=Pv(i,o,"span",{id:"_end","data-mce-type":"bookmark"});const e=i.createRng();e.setStartAfter(n),e.setEndBefore(o),eg(i,e,(e=>{q(e,(e=>{Ju(e)||Ju(e.parentNode)||d(e)}))})),d(n),d(o),n=u(!0),o=u()}else n=o=d(n);r.startContainer=n.parentNode?n.parentNode:n,r.startOffset=i.nodeIndex(n),r.endContainer=o.parentNode?o.parentNode:o,r.endOffset=i.nodeIndex(o)+1}eg(i,r,(e=>{q(e,m)}))};if(o){if(bf(o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),f(e)}else f(o);hd(e,t,o,n)}else l.isCollapsed()&&Lf(a)&&!af(e).length?dv(e,t,n,r):(yf(e,(()=>gf(e,f)),(o=>Lf(a)&&Jb(e,t,n,o))),e.nodeChanged()),((e,t,n)=>{"removeformat"===t?q(vv(e.selection),(t=>{q(fv,(n=>e.dom.setStyle(t,n,""))),Fv(e.dom,t)})):gv(e.formatter,t).each((t=>{q(vv(e.selection),(o=>Uv(e.dom,o,t,n,null)))}))})(e,t,n),hd(e,t,o,n)})(e,t,n,o,r)},Hv=(e,t,n,o,r)=>zv(e,t,n,o,r).fold(L,(t=>(e.dom.rename(o,t),!0)),M),Vv=["fontsize","subscript","superscript"],qv=["strikethrough",...Vv],Wv=(e,t)=>H(Vv,(n=>((e,t,n)=>C(e.matchNode(t.dom,n,{},"fontsize"===n)))(e,t,n))),Kv=(e,t,n,o,r)=>{const s=t=>vn(mn.fromDom(e.getRoot()),t)||e.isBlock(t.dom);q(t,(t=>{((e,t,n,o,r)=>{const s=In(t,e).filter(Nn);return((e,t)=>{for(let n=e.length-1;n>=0;n--)if(t(e[n],n))return I.some(n);return I.none()})(s,n).map((e=>{const t=s[e];return{container:t,innerWrapper:o(t),outerWrappers:[...r(Ao(t)).toArray(),...te(s.slice(0,e),(e=>n(e)?r(e).toArray():[Ao(e)]))]}}))})(s,t,n,o,r).each((({container:o,innerWrapper:s,outerWrappers:a})=>{e.split(o.dom,t.dom),((e,t,n,o)=>{q(Hn(e),(e=>{Nn(e)&&n(e)&&o(e).isNone()&&ko(e)})),q(Hn(e),(e=>mo(t,e))),co(e,t)})(t,s,n,r),((e,t)=>{if(t.length>0){const n=t[t.length-1];io(e,n);const o=X(t.slice(0,t.length-1),((e,t)=>(mo(e,t),t)),n);mo(o,e)}})(t,a)}))}))},Yv=(e,t,n)=>{const o=(e=>{const t={},n=n=>{let o=e[n?"startContainer":"endContainer"],r=e[n?"startOffset":"endOffset"];if(Xr(o)){const e=Vb.create("span",{"data-mce-type":"bookmark"});o.hasChildNodes()?r===o.childNodes.length?o.appendChild(e):o.insertBefore(e,o.childNodes[r]):o.appendChild(e),o=e,r=0}t[n?"startContainer":"endContainer"]=o,t[n?"startOffset":"endOffset"]=r};return n(!0),e.collapsed||n(),t})(e.selection.getRng());Kv(e.dom,n,(n=>C(Zb(e,n.dom,t))),(n=>{const o=mn.fromTag(En(n)),r=Zb(e,n.dom,t,{});return C(r)&&(e=>!p(e.attributes)&&!p(e.styles))(r)&&Wb(e,o.dom,r),o}),(n=>{const o=Zb(e,n.dom,t,{});return C(o)?((e,t,n,o)=>zv(e,t,{},o).fold((()=>I.some(o)),(t=>I.some(e.dom.rename(o,t))),I.none))(e,o,0,n.dom).map(mn.fromDom):I.some(n)})),e.selection.setRng((e=>{const t=t=>{let n=e[t?"startContainer":"endContainer"],o=e[t?"startOffset":"endOffset"];if(n){if(Xr(n)&&n.parentNode){const e=n;o=(e=>{var t;let n=null===(t=e.parentNode)||void 0===t?void 0:t.firstChild,o=0;for(;n;){if(n===e)return o;Xr(n)&&"bookmark"===n.getAttribute("data-mce-type")||o++,n=n.nextSibling}return-1})(n),n=n.parentNode,Vb.remove(e),!n.hasChildNodes()&&Vb.isBlock(n)&&n.appendChild(Vb.create("br"))}e[t?"startContainer":"endContainer"]=n,e[t?"startOffset":"endOffset"]=o}};t(!0),t();const n=Vb.createRng();return n.setStart(e.startContainer,e.startOffset),e.endContainer&&n.setEnd(e.endContainer,e.endOffset),(e=>{const t=e.cloneRange(),n=Hb(e.startContainer,e.startOffset);t.setStart(n.container,n.offset);const o=Hb(e.endContainer,e.endOffset);return t.setEnd(o.container,o.offset),t})(n)})(o))},Gv=e=>["h1","h2","h3","h4","h5","h6"].includes(e.name),Xv=(e,t,n,o)=>{const r=n.name;for(let t=0,s=e.length;t<s;t++){const s=e[t];if(s.name===r){const e=o.nodes[r];e?e.nodes.push(n):o.nodes[r]={filter:s,nodes:[n]}}}if(n.attributes)for(let e=0,r=t.length;e<r;e++){const r=t[e],s=r.name;if(s in n.attributes.map){const e=o.attributes[s];e?e.nodes.push(n):o.attributes[s]={filter:r,nodes:[n]}}}},Qv=(e,t)=>{const n=(e,n)=>{pe(e,(e=>{const o=ce(e.nodes);q(e.filter.callbacks,(r=>{for(let t=o.length-1;t>=0;t--){const r=o[t];(n?void 0!==r.attr(e.filter.name):r.name===e.filter.name)&&!y(r.parent)||o.splice(t,1)}o.length>0&&r(o,e.filter.name,t)}))}))};n(e.nodes,!1),n(e.attributes,!0)},Zv=(e,t,n,o={})=>{const r=((e,t,n)=>{const o={nodes:{},attributes:{}};return n.firstChild&&(n=>{let r=n;for(;r=r.walk();)Xv(e,t,r,o)})(n),o})(e,t,n);Qv(r,o)},Jv=(e,t,n,o)=>{if((e.pad_empty_with_br||t.insert)&&n(o)){const e=new xp("br",1);t.insert&&e.attr("data-mce-bogus","1"),o.empty().append(e)}else o.empty().append(new xp("#text",3)).value=dt},ey=(e,t)=>{const n=null==e?void 0:e.firstChild;return C(n)&&n===e.lastChild&&n.name===t},ty=(e,t,n,o)=>o.isEmpty(t,n,(t=>((e,t)=>{const n=e.getElementRule(t.name);return!0===(null==n?void 0:n.paddEmpty)})(e,t))),ny=e=>{let t;for(let n=e;n;n=n.parent){const e=n.attr("contenteditable");if("false"===e)break;"true"===e&&(t=n)}return I.from(t)},oy=(e,t,n=e.parent)=>{if(t.getSpecialElements()[e.name])e.empty().remove();else{const o=e.children();for(const e of o)n&&!t.isValidChild(n.name,e.name)&&oy(e,t,n);e.unwrap()}},ry=(e,t,n,o=S)=>{const r=t.getTextBlockElements(),s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=dn.makeMap("tr,td,th,tbody,thead,tfoot,table,summary"),l=new Set,d=e=>e!==n&&!i[e.name];for(let n=0;n<e.length;n++){const i=e[n];let c,m,u;if(!i.parent||l.has(i))continue;if(r[i.name]&&"li"===i.parent.name){let e=i.next;for(;e&&r[e.name];)e.name="li",l.add(e),i.parent.insert(e,i.parent),e=e.next;i.unwrap();continue}const f=[i];for(c=i.parent;c&&!t.isValidChild(c.name,i.name)&&d(c);c=c.parent)f.push(c);if(c&&f.length>1)if(sy(t,i,c))oy(i,t);else{f.reverse(),m=f[0].clone(),o(m);let e=m;for(let n=0;n<f.length-1;n++){t.isValidChild(e.name,f[n].name)&&n>0?(u=f[n].clone(),o(u),e.append(u)):u=e;for(let e=f[n].firstChild;e&&e!==f[n+1];){const t=e.next;u.append(e),e=t}e=u}ty(t,s,a,m)?c.insert(i,f[0],!0):(c.insert(m,f[0],!0),c.insert(i,m)),c=f[0],(ty(t,s,a,c)||ey(c,"br"))&&c.empty().remove()}else if(i.parent){if("li"===i.name){let e=i.prev;if(e&&("ul"===e.name||"ol"===e.name)){e.append(i);continue}if(e=i.next,e&&("ul"===e.name||"ol"===e.name)&&e.firstChild){e.insert(i,e.firstChild,!0);continue}const t=new xp("ul",1);o(t),i.wrap(t);continue}if(t.isValidChild(i.parent.name,"div")&&t.isValidChild("div",i.name)){const e=new xp("div",1);o(e),i.wrap(e)}else oy(i,t)}}},sy=(e,t,n=t.parent)=>!(!n||(!e.children[t.name]||e.isValidChild(n.name,t.name))&&("a"!==t.name||!(e=>{let t=e;for(;t;){if("a"===t.name)return!0;t=t.parent}return!1})(n))&&(!(e=>"summary"===e.name)(n)||!Gv(t)||(null==n?void 0:n.firstChild)===t&&(null==n?void 0:n.lastChild)===t)),ay=e=>e.collapsed?e:(e=>{const t=Ul.fromRangeStart(e),n=Ul.fromRangeEnd(e),o=e.commonAncestorContainer;return Tu(!1,o,n).map((r=>!ru(t,n,o)&&ru(t,r,o)?((e,t,n,o)=>{const r=document.createRange();return r.setStart(e,t),r.setEnd(n,o),r})(t.container(),t.offset(),r.container(),r.offset()):e)).getOr(e)})(e),iy=dn.explode,ly=()=>{const e={};return{addFilter:(t,n)=>{q(iy(t),(t=>{Se(e,t)||(e[t]={name:t,callbacks:[]}),e[t].callbacks.push(n)}))},getFilters:()=>Ee(e),removeFilter:(t,n)=>{q(iy(t),(t=>{if(Se(e,t))if(C(n)){const o=e[t],r=Y(o.callbacks,(e=>e!==n));r.length>0?o.callbacks=r:delete e[t]}else delete e[t]}))}}},dy=e=>e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&"),cy=(e,t,n)=>{var o;const r=Ha();t.convert_fonts_to_spans&&((e,t,n)=>{e.addNodeFilter("font",(e=>{q(e,(e=>{const o=t.parse(e.attr("style")),r=e.attr("color"),s=e.attr("face"),a=e.attr("size");r&&(o.color=r),s&&(o["font-family"]=s),a&&rt(a).each((e=>{o["font-size"]=n[e-1]})),e.name="span",e.attr("style",t.serialize(o)),(e=>{q(["color","face","size"],(t=>{e.attr(t,null)}))})(e)}))}))})(e,r,dn.explode(null!==(o=t.font_size_legacy_values)&&void 0!==o?o:"")),((e,t,n)=>{e.addNodeFilter("strike",(e=>{const o="html4"!==t.type;q(e,(e=>{if(o)e.name="s";else{const t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))}}))}))})(e,n,r)},my=e=>{const[t,...n]=e.split(","),o=n.join(","),r=/data:([^/]+\/[^;]+)(;.+)?/.exec(t);if(r){const e=";base64"===r[2],t=(e=>{try{return decodeURIComponent(e)}catch(t){return e}})(o),n=e?(e=>{const t=/([a-z0-9+\/=\s]+)/i.exec(e);return t?t[1]:""})(t):t;return I.some({type:r[1],data:n,base64Encoded:e})}return I.none()},uy=(e,t,n=!0)=>{let o=t;if(n)try{o=atob(t)}catch(e){return I.none()}const r=new Uint8Array(o.length);for(let e=0;e<r.length;e++)r[e]=o.charCodeAt(e);return I.some(new Blob([r],{type:e}))},fy=e=>new Promise(((t,n)=>{const o=new FileReader;o.onloadend=()=>{t(o.result)},o.onerror=()=>{var e;n(null===(e=o.error)||void 0===e?void 0:e.message)},o.readAsDataURL(e)}));let gy=0;const py=(e,t,n)=>my(e).bind((({data:e,type:o,base64Encoded:r})=>{if(t&&!r)return I.none();{const t=r?e:btoa(e);return n(t,o)}})),hy=(e,t,n)=>{const o=e.create("blobid"+gy++,t,n);return e.add(o),o},by=(e,t,n=!1)=>py(t,n,((t,n)=>I.from(e.getByData(t,n)).orThunk((()=>uy(n,t).map((n=>hy(e,n,t))))))),vy=/^(?:(?:(?:[A-Za-z][A-Za-z\d.+-]{0,14}:\/\/(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?|www\.|[-;:&=+$,.\w]+@)([A-Za-z\d-]+(?:\.[A-Za-z\d-]+)*))(?::\d+)?(?:\/(?:[-.~*+=!;:'%@$(),\/\w]*[-~*+=%@$()\/\w])?)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+)?)?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+)?)?)$/,yy=e=>I.from(e.match(vy)).bind((e=>ie(e,1))).map((e=>Xe(e,"www.")?e.substring(4):e)),Cy=(e,t)=>{I.from(e.attr("src")).bind(yy).forall((e=>!$(t,e)))&&e.attr("sandbox","")},wy=(e,t)=>Xe(e,`${t}/`),{entries:Ey,setPrototypeOf:xy,isFrozen:Sy,getPrototypeOf:_y,getOwnPropertyDescriptor:ky}=Object;let{freeze:Ny,seal:Ay,create:Ry}=Object,{apply:Dy,construct:Ty}="undefined"!=typeof Reflect&&Reflect;Ny||(Ny=function(e){return e}),Ay||(Ay=function(e){return e}),Dy||(Dy=function(e,t,n){return e.apply(t,n)}),Ty||(Ty=function(e,t){return new e(...t)});const Oy=Ky(Array.prototype.forEach),By=Ky(Array.prototype.lastIndexOf),Py=Ky(Array.prototype.pop),Ly=Ky(Array.prototype.push),My=Ky(Array.prototype.splice),Iy=Ky(String.prototype.toLowerCase),Fy=Ky(String.prototype.toString),Uy=Ky(String.prototype.match),zy=Ky(String.prototype.replace),jy=Ky(String.prototype.indexOf),$y=Ky(String.prototype.trim),Hy=Ky(Object.prototype.hasOwnProperty),Vy=Ky(RegExp.prototype.test),qy=(Wy=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Ty(Wy,t)});var Wy;function Ky(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return Dy(e,t,o)}}function Yy(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Iy;xy&&xy(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"==typeof r){const e=n(r);e!==r&&(Sy(t)||(t[o]=e),r=e)}e[r]=!0}return e}function Gy(e){for(let t=0;t<e.length;t++)Hy(e,t)||(e[t]=null);return e}function Xy(e){const t=Ry(null);for(const[n,o]of Ey(e))Hy(e,n)&&(Array.isArray(o)?t[n]=Gy(o):o&&"object"==typeof o&&o.constructor===Object?t[n]=Xy(o):t[n]=o);return t}function Qy(e,t){for(;null!==e;){const n=ky(e,t);if(n){if(n.get)return Ky(n.get);if("function"==typeof n.value)return Ky(n.value)}e=_y(e)}return function(){return null}}const Zy=Ny(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Jy=Ny(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),eC=Ny(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),tC=Ny(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),nC=Ny(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),oC=Ny(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),rC=Ny(["#text"]),sC=Ny(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),aC=Ny(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),iC=Ny(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),lC=Ny(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),dC=Ay(/\{\{[\w\W]*|[\w\W]*\}\}/gm),cC=Ay(/<%[\w\W]*|[\w\W]*%>/gm),mC=Ay(/\$\{[\w\W]*/gm),uC=Ay(/^data-[\-\w.\u00B7-\uFFFF]+$/),fC=Ay(/^aria-[\-\w]+$/),gC=Ay(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),pC=Ay(/^(?:\w+script|data):/i),hC=Ay(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),bC=Ay(/^html$/i),vC=Ay(/^[a-z][.\w]*(-[.\w]+)+$/i);var yC=Object.freeze({__proto__:null,ARIA_ATTR:fC,ATTR_WHITESPACE:hC,CUSTOM_ELEMENT:vC,DATA_ATTR:uC,DOCTYPE_NAME:bC,ERB_EXPR:cC,IS_ALLOWED_URI:gC,IS_SCRIPT_OR_DATA:pC,MUSTACHE_EXPR:dC,TMPLIT_EXPR:mC});const CC=function(){return"undefined"==typeof window?null:window};var wC=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:CC();const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)return n.isSupported=!1,n;let{document:o}=t;const r=o,s=r.currentScript,{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:d,NodeFilter:c,NamedNodeMap:m=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:u,DOMParser:f,trustedTypes:g}=t,p=d.prototype,h=Qy(p,"cloneNode"),b=Qy(p,"remove"),v=Qy(p,"nextSibling"),y=Qy(p,"childNodes"),C=Qy(p,"parentNode");if("function"==typeof i){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let w,E="";const{implementation:x,createNodeIterator:S,createDocumentFragment:_,getElementsByTagName:k}=o,{importNode:N}=r;let A={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof Ey&&"function"==typeof C&&x&&void 0!==x.createHTMLDocument;const{MUSTACHE_EXPR:R,ERB_EXPR:D,TMPLIT_EXPR:T,DATA_ATTR:O,ARIA_ATTR:B,IS_SCRIPT_OR_DATA:P,ATTR_WHITESPACE:L,CUSTOM_ELEMENT:M}=yC;let{IS_ALLOWED_URI:I}=yC,F=null;const U=Yy({},[...Zy,...Jy,...eC,...nC,...rC]);let z=null;const j=Yy({},[...sC,...aC,...iC,...lC]);let $=Object.seal(Ry(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),H=null,V=null,q=!0,W=!0,K=!1,Y=!0,G=!1,X=!0,Q=!1,Z=!1,J=!1,ee=!1,te=!1,ne=!1,oe=!0,re=!1,se=!0,ae=!1,ie={},le=null;const de=Yy({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ce=null;const me=Yy({},["audio","video","img","source","image","track"]);let ue=null;const fe=Yy({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",pe="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml";let be=he,ve=!1,ye=null;const Ce=Yy({},[ge,pe,he],Fy);let we=Yy({},["mi","mo","mn","ms","mtext"]),Ee=Yy({},["annotation-xml"]);const xe=Yy({},["title","style","font","a","script"]);let Se=null;const _e=["application/xhtml+xml","text/html"];let ke=null,Ne=null;const Ae=o.createElement("form"),Re=function(e){return e instanceof RegExp||e instanceof Function},De=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Ne||Ne!==e){if(e&&"object"==typeof e||(e={}),e=Xy(e),Se=-1===_e.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,ke="application/xhtml+xml"===Se?Fy:Iy,F=Hy(e,"ALLOWED_TAGS")?Yy({},e.ALLOWED_TAGS,ke):U,z=Hy(e,"ALLOWED_ATTR")?Yy({},e.ALLOWED_ATTR,ke):j,ye=Hy(e,"ALLOWED_NAMESPACES")?Yy({},e.ALLOWED_NAMESPACES,Fy):Ce,ue=Hy(e,"ADD_URI_SAFE_ATTR")?Yy(Xy(fe),e.ADD_URI_SAFE_ATTR,ke):fe,ce=Hy(e,"ADD_DATA_URI_TAGS")?Yy(Xy(me),e.ADD_DATA_URI_TAGS,ke):me,le=Hy(e,"FORBID_CONTENTS")?Yy({},e.FORBID_CONTENTS,ke):de,H=Hy(e,"FORBID_TAGS")?Yy({},e.FORBID_TAGS,ke):Xy({}),V=Hy(e,"FORBID_ATTR")?Yy({},e.FORBID_ATTR,ke):Xy({}),ie=!!Hy(e,"USE_PROFILES")&&e.USE_PROFILES,q=!1!==e.ALLOW_ARIA_ATTR,W=!1!==e.ALLOW_DATA_ATTR,K=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Y=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,G=e.SAFE_FOR_TEMPLATES||!1,X=!1!==e.SAFE_FOR_XML,Q=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,ne=e.RETURN_TRUSTED_TYPE||!1,J=e.FORCE_BODY||!1,oe=!1!==e.SANITIZE_DOM,re=e.SANITIZE_NAMED_PROPS||!1,se=!1!==e.KEEP_CONTENT,ae=e.IN_PLACE||!1,I=e.ALLOWED_URI_REGEXP||gC,be=e.NAMESPACE||he,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,Ee=e.HTML_INTEGRATION_POINTS||Ee,$=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Re(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&($.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Re(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&($.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&($.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),G&&(W=!1),te&&(ee=!0),ie&&(F=Yy({},rC),z=[],!0===ie.html&&(Yy(F,Zy),Yy(z,sC)),!0===ie.svg&&(Yy(F,Jy),Yy(z,aC),Yy(z,lC)),!0===ie.svgFilters&&(Yy(F,eC),Yy(z,aC),Yy(z,lC)),!0===ie.mathMl&&(Yy(F,nC),Yy(z,iC),Yy(z,lC))),e.ADD_TAGS&&(F===U&&(F=Xy(F)),Yy(F,e.ADD_TAGS,ke)),e.ADD_ATTR&&(z===j&&(z=Xy(z)),Yy(z,e.ADD_ATTR,ke)),e.ADD_URI_SAFE_ATTR&&Yy(ue,e.ADD_URI_SAFE_ATTR,ke),e.FORBID_CONTENTS&&(le===de&&(le=Xy(le)),Yy(le,e.FORBID_CONTENTS,ke)),se&&(F["#text"]=!0),Q&&Yy(F,["html","head","body"]),F.table&&(Yy(F,["tbody"]),delete H.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw qy('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw qy('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');w=e.TRUSTED_TYPES_POLICY,E=w.createHTML("")}else void 0===w&&(w=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(g,s)),null!==w&&"string"==typeof E&&(E=w.createHTML(""));Ny&&Ny(e),Ne=e}},Te=Yy({},[...Jy,...eC,...tC]),Oe=Yy({},[...nC,...oC]),Be=function(e){Ly(n.removed,{element:e});try{C(e).removeChild(e)}catch(t){b(e)}},Pe=function(e,t){try{Ly(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){Ly(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ee||te)try{Be(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Le=function(e){let t=null,n=null;if(J)e="<remove></remove>"+e;else{const t=Uy(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===Se&&be===he&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=w?w.createHTML(e):e;if(be===he)try{t=(new f).parseFromString(r,Se)}catch(e){}if(!t||!t.documentElement){t=x.createDocument(be,"template",null);try{t.documentElement.innerHTML=ve?E:r}catch(e){}}const s=t.body||t.documentElement;return e&&n&&s.insertBefore(o.createTextNode(n),s.childNodes[0]||null),be===he?k.call(t,Q?"html":"body")[0]:Q?t.documentElement:s},Me=function(e){return S.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},Ie=function(e){return e instanceof u&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof m)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Fe=function(e){return"function"==typeof l&&e instanceof l};function Ue(e,t,o){Oy(e,(e=>{e.call(n,t,o,Ne)}))}const ze=function(e){let t=null;if(Ue(A.beforeSanitizeElements,e,null),Ie(e))return Be(e),!0;const o=ke(e.nodeName);if(Ue(A.uponSanitizeElement,e,{tagName:o,allowedTags:F}),X&&e.hasChildNodes()&&!Fe(e.firstElementChild)&&Vy(/<[/\w!]/g,e.innerHTML)&&Vy(/<[/\w!]/g,e.textContent))return Be(e),!0;if(7===e.nodeType)return Be(e),!0;if(X&&8===e.nodeType&&Vy(/<[/\w]/g,e.data))return Be(e),!0;if(!F[o]||H[o]){if(!H[o]&&$e(o)){if($.tagNameCheck instanceof RegExp&&Vy($.tagNameCheck,o))return!1;if($.tagNameCheck instanceof Function&&$.tagNameCheck(o))return!1}if(se&&!le[o]){const t=C(e)||e.parentNode,n=y(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=h(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,v(e))}}return Be(e),!0}return e instanceof d&&!function(e){let t=C(e);t&&t.tagName||(t={namespaceURI:be,tagName:"template"});const n=Iy(e.tagName),o=Iy(t.tagName);return!!ye[e.namespaceURI]&&(e.namespaceURI===pe?t.namespaceURI===he?"svg"===n:t.namespaceURI===ge?"svg"===n&&("annotation-xml"===o||we[o]):Boolean(Te[n]):e.namespaceURI===ge?t.namespaceURI===he?"math"===n:t.namespaceURI===pe?"math"===n&&Ee[o]:Boolean(Oe[n]):e.namespaceURI===he?!(t.namespaceURI===pe&&!Ee[o])&&!(t.namespaceURI===ge&&!we[o])&&!Oe[n]&&(xe[n]||!Te[n]):!("application/xhtml+xml"!==Se||!ye[e.namespaceURI]))}(e)?(Be(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!Vy(/<\/no(script|embed|frames)/i,e.innerHTML)?(G&&3===e.nodeType&&(t=e.textContent,Oy([R,D,T],(e=>{t=zy(t,e," ")})),e.textContent!==t&&(Ly(n.removed,{element:e.cloneNode()}),e.textContent=t)),Ue(A.afterSanitizeElements,e,null),!1):(Be(e),!0)},je=function(e,t,n){if(oe&&("id"===t||"name"===t)&&(n in o||n in Ae))return!1;if(W&&!V[t]&&Vy(O,t));else if(q&&Vy(B,t));else if(!z[t]||V[t]){if(!($e(e)&&($.tagNameCheck instanceof RegExp&&Vy($.tagNameCheck,e)||$.tagNameCheck instanceof Function&&$.tagNameCheck(e))&&($.attributeNameCheck instanceof RegExp&&Vy($.attributeNameCheck,t)||$.attributeNameCheck instanceof Function&&$.attributeNameCheck(t))||"is"===t&&$.allowCustomizedBuiltInElements&&($.tagNameCheck instanceof RegExp&&Vy($.tagNameCheck,n)||$.tagNameCheck instanceof Function&&$.tagNameCheck(n))))return!1}else if(ue[t]);else if(Vy(I,zy(n,L,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==jy(n,"data:")||!ce[e])if(K&&!Vy(P,zy(n,L,"")));else if(n)return!1;return!0},$e=function(e){return"annotation-xml"!==e&&Uy(e,M)},He=function(e){Ue(A.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Ie(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z,forceKeepAttr:void 0};let r=t.length;for(;r--;){const s=t[r],{name:a,namespaceURI:i,value:l}=s,d=ke(a),c=l;let m="value"===a?c:$y(c);if(o.attrName=d,o.attrValue=m,o.keepAttr=!0,o.forceKeepAttr=void 0,Ue(A.uponSanitizeAttribute,e,o),m=o.attrValue,!re||"id"!==d&&"name"!==d||(Pe(a,e),m="user-content-"+m),X&&Vy(/((--!?|])>)|<\/(style|title)/i,m)){Pe(a,e);continue}if(o.forceKeepAttr)continue;if(!o.keepAttr){Pe(a,e);continue}if(!Y&&Vy(/\/>/i,m)){Pe(a,e);continue}G&&Oy([R,D,T],(e=>{m=zy(m,e," ")}));const u=ke(e.nodeName);if(je(u,d,m)){if(w&&"object"==typeof g&&"function"==typeof g.getAttributeType)if(i);else switch(g.getAttributeType(u,d)){case"TrustedHTML":m=w.createHTML(m);break;case"TrustedScriptURL":m=w.createScriptURL(m)}if(m!==c)try{i?e.setAttributeNS(i,a,m):e.setAttribute(a,m),Ie(e)?Be(e):Py(n.removed)}catch(t){Pe(a,e)}}else Pe(a,e)}Ue(A.afterSanitizeAttributes,e,null)},Ve=function e(t){let n=null;const o=Me(t);for(Ue(A.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)Ue(A.uponSanitizeShadowNode,n,null),ze(n),He(n),n.content instanceof a&&e(n.content);Ue(A.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,s=null,i=null,d=null;if(ve=!e,ve&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw qy("toString is not a function");if("string"!=typeof(e=e.toString()))throw qy("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Z||De(t),n.removed=[],"string"==typeof e&&(ae=!1),ae){if(e.nodeName){const t=ke(e.nodeName);if(!F[t]||H[t])throw qy("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)o=Le("\x3c!----\x3e"),s=o.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?o=s:o.appendChild(s);else{if(!ee&&!G&&!Q&&-1===e.indexOf("<"))return w&&ne?w.createHTML(e):e;if(o=Le(e),!o)return ee?null:ne?E:""}o&&J&&Be(o.firstChild);const c=Me(ae?e:o);for(;i=c.nextNode();)ze(i),He(i),i.content instanceof a&&Ve(i.content);if(ae)return e;if(ee){if(te)for(d=_.call(o.ownerDocument);o.firstChild;)d.appendChild(o.firstChild);else d=o;return(z.shadowroot||z.shadowrootmode)&&(d=N.call(r,d,!0)),d}let m=Q?o.outerHTML:o.innerHTML;return Q&&F["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&Vy(bC,o.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+m),G&&Oy([R,D,T],(e=>{m=zy(m,e," ")})),w&&ne?w.createHTML(m):m},n.setConfig=function(){De(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Z=!0},n.clearConfig=function(){Ne=null,Z=!1},n.isValidAttribute=function(e,t,n){Ne||De({});const o=ke(e),r=ke(t);return je(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&Ly(A[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=By(A[e],t);return-1===n?void 0:My(A[e],n,1)[0]}return Py(A[e])},n.removeHooks=function(e){A[e]=[]},n.removeAllHooks=function(){A={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const EC=dn.each,xC=dn.trim,SC=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],_C={ftp:21,http:80,https:443,mailto:25},kC=["img","video"],NC=(e,t,n)=>{const o=(e=>{try{return decodeURIComponent(e)}catch(t){return unescape(e)}})(t).replace(/\s/g,"");return!e.allow_script_urls&&(!!/((java|vb)script|mhtml):/i.test(o)||!e.allow_html_data_urls&&(/^data:image\//i.test(o)?((e,t)=>C(e)?!e:!C(t)||!$(kC,t))(e.allow_svg_data_urls,n)&&/^data:image\/svg\+xml/i.test(o):/^data:/i.test(o)))};class AC{static parseDataUri(e){let t;const n=decodeURIComponent(e).split(","),o=/data:([^;]+)/.exec(n[0]);return o&&(t=o[1]),{type:t,data:n[1]}}static isDomSafe(e,t,n={}){if(n.allow_script_urls)return!0;{const o=ba.decode(e).replace(/[\s\u0000-\u001F]+/g,"");return!NC(n,o,t)}}static getDocumentBaseUrl(e){var t;let n;return n=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?null!==(t=e.href)&&void 0!==t?t:"":e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(n)&&(n=n.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(n)||(n+="/")),n}constructor(e,t={}){this.path="",this.directory="",e=xC(e),this.settings=t;const n=t.base_uri,o=this;if(/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))return void(o.source=e);const r=0===e.indexOf("//");if(0!==e.indexOf("/")||r||(e=(n&&n.protocol||"http")+"://mce_host"+e),!/^[\w\-]*:?\/\//.test(e)){const t=n?n.path:new AC(document.location.href).directory;if(""===(null==n?void 0:n.protocol))e="//mce_host"+o.toAbsPath(t,e);else{const r=/([^#?]*)([#?]?.*)/.exec(e);r&&(e=(n&&n.protocol||"http")+"://mce_host"+o.toAbsPath(t,r[1])+r[2])}}e=e.replace(/@@/g,"(mce_at)");const s=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?(\[[a-zA-Z0-9:.%]+\]|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e);s&&EC(SC,((e,t)=>{let n=s[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n})),n&&(o.protocol||(o.protocol=n.protocol),o.userInfo||(o.userInfo=n.userInfo),o.port||"mce_host"!==o.host||(o.port=n.port),o.host&&"mce_host"!==o.host||(o.host=n.host),o.source=""),r&&(o.protocol="")}setPath(e){const t=/^(.*?)\/?(\w+)?$/.exec(e);t&&(this.path=t[0],this.directory=t[1],this.file=t[2]),this.source="",this.getURI()}toRelative(e){if("./"===e)return e;const t=new AC(e,{base_uri:this});if("mce_host"!==t.host&&this.host!==t.host&&t.host||this.port!==t.port||this.protocol!==t.protocol&&""!==t.protocol)return t.getURI();const n=this.getURI(),o=t.getURI();if(n===o||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===o)return n;let r=this.toRelPath(this.path,t.path);return t.query&&(r+="?"+t.query),t.anchor&&(r+="#"+t.anchor),r}toAbsolute(e,t){const n=new AC(e,{base_uri:this});return n.getURI(t&&this.isSameOrigin(n))}isSameOrigin(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;const t=this.protocol?_C[this.protocol]:null;if(t&&(this.port||t)==(e.port||t))return!0}return!1}toRelPath(e,t){let n,o,r=0,s="";const a=e.substring(0,e.lastIndexOf("/")).split("/"),i=t.split("/");if(a.length>=i.length)for(n=0,o=a.length;n<o;n++)if(n>=i.length||a[n]!==i[n]){r=n+1;break}if(a.length<i.length)for(n=0,o=i.length;n<o;n++)if(n>=a.length||a[n]!==i[n]){r=n+1;break}if(1===r)return t;for(n=0,o=a.length-(r-1);n<o;n++)s+="../";for(n=r-1,o=i.length;n<o;n++)s+=n!==r-1?"/"+i[n]:i[n];return s}toAbsPath(e,t){let n=0;const o=/\/$/.test(t)?"/":"",r=e.split("/"),s=t.split("/"),a=[];EC(r,(e=>{e&&a.push(e)}));const i=[];for(let e=s.length-1;e>=0;e--)0!==s[e].length&&"."!==s[e]&&(".."!==s[e]?n>0?n--:i.push(s[e]):n++);const l=a.length-n;let d;return d=l<=0?oe(i).join("/"):a.slice(0,l).join("/")+"/"+oe(i).join("/"),0!==d.indexOf("/")&&(d="/"+d),o&&d.lastIndexOf("/")!==d.length-1&&(d+=o),d}getURI(e=!1){let t;return this.source&&!e||(t="",e||(this.protocol?t+=this.protocol+"://":t+="//",this.userInfo&&(t+=this.userInfo+"@"),this.host&&(t+=this.host),this.port&&(t+=":"+this.port)),this.path&&(t+=this.path),this.query&&(t+="?"+this.query),this.anchor&&(t+="#"+this.anchor),this.source=t),this.source}}const RC=dn.makeMap("src,href,data,background,action,formaction,poster,xlink:href"),DC="data-mce-type";let TC=0;const OC=(e,t,n,o,r)=>{var s,a,i,l;const d=t.validate,c=n.getSpecialElements();8===e.nodeType&&(!t.allow_conditional_comments&&/^\[if/i.test(null!==(s=e.nodeValue)&&void 0!==s?s:"")&&(e.nodeValue=" "+e.nodeValue),t.sanitize&&t.allow_html_in_comments&&u(e.nodeValue)&&(e.nodeValue=(e=>e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"))(e.nodeValue)));const m=null!==(a=null==r?void 0:r.tagName)&&void 0!==a?a:e.nodeName.toLowerCase();if("html"!==o&&n.isValid(o))return void(C(r)&&(r.allowedTags[m]=!0));if(1!==e.nodeType||"body"===m)return;const f=mn.fromDom(e),g=Co(f,DC),p=vo(f,"data-mce-bogus");if(!g&&u(p))return void("all"===p?_o(f):ko(f));const h=n.getElementRule(m);if(!d||h){if(C(r)&&(r.allowedTags[m]=!0),d&&h&&!g){if(q(null!==(i=h.attributesForced)&&void 0!==i?i:[],(e=>{ho(f,e.name,"{$uid}"===e.value?"mce_"+TC++:e.value)})),q(null!==(l=h.attributesDefault)&&void 0!==l?l:[],(e=>{Co(f,e.name)||ho(f,e.name,"{$uid}"===e.value?"mce_"+TC++:e.value)})),h.attributesRequired&&!H(h.attributesRequired,(e=>Co(f,e))))return void ko(f);if(h.removeEmptyAttrs&&Eo(f))return void ko(f);h.outputName&&h.outputName!==m&&Do(f,h.outputName)}}else Se(c,m)?_o(f):ko(f)},BC=(e,t,n,o,r,s)=>"html"!==n&&!Os(o)||!(r in RC&&NC(e,s,o))&&(!e.validate||t.isValid(o,r)||Xe(r,"data-")||Xe(r,"aria-")),PC=(e,t)=>e.hasAttribute(DC)&&("id"===t||"class"===t||"style"===t),LC=(e,t)=>e in t.getBoolAttrs(),MC=(e,t,n,o)=>{const{attributes:r}=e;for(let s=r.length-1;s>=0;s--){const a=r[s],i=a.name,l=a.value;BC(t,n,o,e.tagName.toLowerCase(),i,l)||PC(e,i)?LC(i,n)&&e.setAttribute(i,i):e.removeAttribute(i)}},IC=(e,t,n)=>{const o=wC();return o.addHook("uponSanitizeElement",((o,r)=>{OC(o,e,t,n.track(o),r)})),o.addHook("uponSanitizeAttribute",((o,r)=>{((e,t,n,o,r)=>{const s=e.tagName.toLowerCase(),{attrName:a,attrValue:i}=r;r.keepAttr=BC(t,n,o,s,a,i),r.keepAttr?(r.allowedAttributes[a]=!0,LC(a,n)&&(r.attrValue=a),t.allow_svg_data_urls&&Xe(i,"data:image/svg+xml")&&(r.forceKeepAttr=!0)):PC(e,a)&&(r.forceKeepAttr=!0)})(o,e,t,n.current(),r)})),o},FC=(e,t)=>{const n=wC(),o=t.allow_mathml_annotation_encodings,r=p(o)&&o.length>0;n.addHook("uponSanitizeElement",((e,n)=>{var s;const a=null!==(s=n.tagName)&&void 0!==s?s:e.nodeName.toLowerCase();((e,n)=>r&&"semantics"===n?I.some(!0):"annotation"===n?I.some(Xr(e)&&(e=>{const t=e.getAttribute("encoding");return r&&u(t)&&$(o,t)})(e)):p(t.extended_mathml_elements)&&t.extended_mathml_elements.includes(n)?I.from(!0):I.none())(e,a).each((o=>{n.allowedTags[a]=o,!o&&t.sanitize&&Xr(e)&&e.remove()}))})),n.addHook("uponSanitizeAttribute",((e,n)=>{p(t.extended_mathml_attributes)&&t.extended_mathml_attributes.includes(n.attrName)&&(n.forceKeepAttr=!0)})),n.sanitize(e,{IN_PLACE:!0,USE_PROFILES:{mathMl:!0}})},UC=e=>t=>{const n=Ps(t);if("svg"===n)(e=>{const t=["type","href","role","arcrole","title","show","actuate","label","from","to"].map((e=>`xlink:${e}`)),n={IN_PLACE:!0,USE_PROFILES:{html:!0,svg:!0,svgFilters:!0},ALLOWED_ATTR:t};wC().sanitize(e,n)})(t);else{if("math"!==n)throw new Error("Not a namespace element");FC(t,e)}},zC=["script","style","template","param"],jC=dn.makeMap,$C=dn.extend,HC=(e,t,n,o,r)=>{const s=e.name,a=s in n&&"title"!==s&&"textarea"!==s&&"noscript"!==s,i=t.childNodes;for(let t=0,s=i.length;t<s;t++){const s=i[t],l=new xp(s.nodeName.toLowerCase(),s.nodeType);if(Xr(s)){const e=s.attributes;for(let t=0,n=e.length;t<n;t++){const n=e[t];l.attr(n.name,n.value)}Os(l.name)&&(o(s),l.value=s.innerHTML)}else ss(s)?(l.value=s.data,a&&(l.raw=!0)):ls(s)?l.value=r?dy(s.data):s.data:(as(s)||is(s))&&(l.value=s.data);if(ys(s)){const e=xp.create("#text");e.value=s.innerHTML,e.raw=!0,l.append(e)}else Os(l.name)||HC(l,s,n,o,r);e.append(l)}},VC=(e={},t=Pa())=>{const n=ly(),o=ly(),r={validate:!0,root_name:"body",sanitize:!0,allow_html_in_comments:!1,...e},s=new DOMParser,a=((e,t)=>{const n=(()=>{const e=We(),t=()=>e.get().map(Ps).getOr("html");return{track:n=>(Bs(n)?e.set(n):e.get().exists((e=>!e.contains(n)))&&e.clear(),t()),current:t,reset:()=>{e.clear()}}})();if(e.sanitize){const o=IC(e,t,n),r=(t,r)=>{o.sanitize(t,((e,t)=>{const n={IN_PLACE:!0,ALLOW_UNKNOWN_PROTOCOLS:!0,ALLOWED_TAGS:["#comment","#cdata-section","body"],ALLOWED_ATTR:[]};return n.PARSER_MEDIA_TYPE=t,e.allow_script_urls?n.ALLOWED_URI_REGEXP=/.*/:e.allow_html_data_urls&&(n.ALLOWED_URI_REGEXP=/^(?!(\w+script|mhtml):)/i),n})(e,r)),o.removed=[],n.reset()};return{sanitizeHtmlElement:r,sanitizeNamespaceElement:UC(e)}}return{sanitizeHtmlElement:(o,r)=>{const s=document.createNodeIterator(o,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT|NodeFilter.SHOW_TEXT);let a;for(;a=s.nextNode();){const o=n.track(a);OC(a,e,t,o),Xr(a)&&MC(a,e,t,o)}n.reset()},sanitizeNamespaceElement:S}})(r,t),i=n.addFilter,l=n.getFilters,d=n.removeFilter,c=o.addFilter,m=o.getFilters,f=o.removeFilter,g=(e,n)=>{const o=u(n.attr(DC)),r=1===n.type&&!Se(e,n.name)&&!Gs(t,n)&&!Os(n.name);return 3===n.type||r&&!o},p={schema:t,addAttributeFilter:c,getAttributeFilters:m,removeAttributeFilter:f,addNodeFilter:i,getNodeFilters:l,removeNodeFilter:d,parse:(e,n={})=>{var o;const i=r.validate,d=null!==(o=n.context)&&void 0!==o?o:r.root_name,c=((e,n,o="html")=>{const r="xhtml"===o?"application/xhtml+xml":"text/html",i=Se(t.getSpecialElements(),n.toLowerCase()),l=i?`<${n}>${e}</${n}>`:e,d=s.parseFromString("xhtml"===o?`<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>${l}</body></html>`:/^[\s]*<head/i.test(e)||/^[\s]*<html/i.test(e)||/^[\s]*<!DOCTYPE/i.test(e)?`<html>${l}</html>`:`<body>${l}</body>`,r).body;return a.sanitizeHtmlElement(d,r),i?d.firstChild:d})(e,d,n.format);$s(t,c);const u=new xp(d,11);HC(u,c,t.getSpecialElements(),a.sanitizeNamespaceElement,r.sanitize&&r.allow_html_in_comments),c.innerHTML="";const[f,p]=((e,t,n,o)=>{const r=n.validate,s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=$C(jC(zC),t.getBlockElements()),l=Oa(t),d=/[ \t\r\n]+/g,c=/^[ \t\r\n]+/,m=/[ \t\r\n]+$/,u=e=>{let t=e.parent;for(;C(t);){if(t.name in a)return!0;t=t.parent}return!1},f=n=>n.name in i||Gs(t,n)||Os(n.name)&&n.parent===e,g=(t,n)=>{const r=n?t.prev:t.next;return!C(r)&&!y(t.parent)&&f(t.parent)&&(t.parent!==e||!0===o.isRootContent)};return[e=>{var t;if(3===e.type&&!u(e)){let n=null!==(t=e.value)&&void 0!==t?t:"";n=n.replace(d," "),(((e,t)=>C(e)&&(t(e)||"br"===e.name))(e.prev,f)||g(e,!0))&&(n=n.replace(c,"")),0===n.length||" "===n&&e.prev&&8===e.prev.type&&e.next&&8===e.next.type?e.remove():e.value=n}},e=>{var i;if(1===e.type){const i=t.getElementRule(e.name);if(r&&i){const r=ty(t,s,a,e);i.paddInEmptyBlock&&r&&(e=>{let n=e;for(;C(n);){if(n.name in l)return ty(t,s,a,n);n=n.parent}return!1})(e)?Jv(n,o,f,e):i.removeEmpty&&r?f(e)?e.remove():e.unwrap():i.paddEmpty&&(r||(e=>{var t;return ey(e,"#text")&&(null===(t=null==e?void 0:e.firstChild)||void 0===t?void 0:t.value)===dt})(e))&&Jv(n,o,f,e)}}else if(3===e.type&&!u(e)){let t=null!==(i=e.value)&&void 0!==i?i:"";(e.next&&f(e.next)||g(e,!1))&&(t=t.replace(m,"")),0===t.length?e.remove():e.value=t}}]})(u,t,r,n),h=[],b=i?e=>((e,n)=>{sy(t,e)&&n.push(e)})(e,h):S,v={nodes:{},attributes:{}},w=e=>Xv(l(),m(),e,v);((e,t,n)=>{const o=[];for(let n=e,r=n;n;r=n,n=n.walk()){const s=n;q(t,(e=>e(s))),y(s.parent)&&s!==e?n=r:o.push(s)}for(let e=o.length-1;e>=0;e--){const t=o[e];q(n,(e=>e(t)))}})(u,[f,w],[p,b]),h.reverse(),i&&h.length>0&&(n.context?n.invalid=!0:ry(h,t,u,w));const E=((e,t)=>{var n;const o=null!==(n=t.forced_root_block)&&void 0!==n?n:e.forced_root_block;return!1===o?"":!0===o?"p":o})(r,n);return E&&("body"===u.name||n.isRootContent)&&((e,n)=>{const o=$C(jC(zC),t.getBlockElements()),s=/^[ \t\r\n]+/,a=/[ \t\r\n]+$/;let i=e.firstChild,l=null;const d=e=>{var t,n;e&&(i=e.firstChild,i&&3===i.type&&(i.value=null===(t=i.value)||void 0===t?void 0:t.replace(s,"")),i=e.lastChild,i&&3===i.type&&(i.value=null===(n=i.value)||void 0===n?void 0:n.replace(a,"")))};if(t.isValidChild(e.name,n.toLowerCase())){for(;i;){const t=i.next;g(o,i)?(l||(l=new xp(n,1),l.attr(r.forced_root_block_attrs),e.insert(l,i)),l.append(i)):(d(l),l=null),i=t}d(l)}})(u,E),n.invalid||Qv(v,n),u}};return((e,t)=>{var n,o;const r=e.schema;e.addAttributeFilter("href",(e=>{let n=e.length;const o=e=>{const t=e?dn.trim(e):"";return/\b(noopener)\b/g.test(t)?t:(e=>e.split(" ").filter((e=>e.length>0)).concat(["noopener"]).sort().join(" "))(t)};if(!t.allow_unsafe_link_target)for(;n--;){const t=e[n];"a"===t.name&&"_blank"===t.attr("target")&&t.attr("rel",o(t.attr("rel")))}})),t.allow_html_in_named_anchor||e.addAttributeFilter("id,name",(e=>{let t,n,o,r,s=e.length;for(;s--;)if(r=e[s],"a"===r.name&&r.firstChild&&!r.attr("href"))for(o=r.parent,t=r.lastChild;t&&o;)n=t.prev,o.insert(t,r),t=n})),t.fix_list_elements&&e.addNodeFilter("ul,ol",(e=>{let t,n,o=e.length;for(;o--;)if(t=e[o],n=t.parent,n&&("ul"===n.name||"ol"===n.name))if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{const e=new xp("li",1);e.attr("style","list-style-type: none"),t.wrap(e)}}));const s=r.getValidClasses();t.validate&&s&&e.addAttributeFilter("class",(e=>{var t;let n=e.length;for(;n--;){const o=e[n],r=null!==(t=o.attr("class"))&&void 0!==t?t:"",a=dn.explode(r," ");let i="";for(let e=0;e<a.length;e++){const t=a[e];let n=!1,r=s["*"];r&&r[t]&&(n=!0),r=s[o.name],!n&&r&&r[t]&&(n=!0),n&&(i&&(i+=" "),i+=t)}i.length||(i=null),o.attr("class",i)}})),((e,t)=>{const{blob_cache:n}=t;if(n){const t=e=>{const t=e.attr("src");(e=>e.attr("src")===rn.transparentSrc||C(e.attr("data-mce-placeholder")))(e)||(e=>C(e.attr("data-mce-bogus")))(e)||y(t)||by(n,t,!0).each((t=>{e.attr("src",t.blobUri())}))};e.addAttributeFilter("src",(e=>q(e,t)))}})(e,t);const a=null!==(n=t.sandbox_iframes)&&void 0!==n&&n,i=ue(null!==(o=t.sandbox_iframes_exclusions)&&void 0!==o?o:[]);t.convert_unsafe_embeds&&e.addNodeFilter("object,embed",(e=>q(e,(e=>{e.replace((({type:e,src:t,width:n,height:o}={},r,s)=>{const a=(e=>v(e)?"iframe":wy(e,"image")?"img":wy(e,"video")?"video":wy(e,"audio")?"audio":"iframe")(e),i=new xp(a,1);return i.attr("audio"===a?{src:t}:{src:t,width:n,height:o}),"audio"!==a&&"video"!==a||i.attr("controls",""),"iframe"===a&&r&&Cy(i,s),i})({type:e.attr("type"),src:"object"===e.name?e.attr("data"):e.attr("src"),width:e.attr("width"),height:e.attr("height")},a,i))})))),a&&e.addNodeFilter("iframe",(e=>q(e,(e=>Cy(e,i)))))})(p,r),((e,t,n)=>{t.inline_styles&&cy(e,t,n)})(p,r,t),p},qC=e=>e instanceof xp,WC=(e,t,n)=>{const o=(e=>qC(e)?$p({validate:!1}).serialize(e):e)(e),r=t(o);if(r.isDefaultPrevented())return r;if(qC(e)){if(r.content!==o){const t=VC({validate:!1,forced_root_block:!1,...n}).parse(r.content,{context:e.name});return{...r,content:t}}return{...r,content:e}}return r},KC=e=>({sanitize:um(e),sandbox_iframes:vm(e),sandbox_iframes_exclusions:ym(e)}),YC=(e,t)=>{if(t.no_events)return De.value(t);{const n=((e,t)=>e.dispatch("BeforeGetContent",t))(e,t);return n.isDefaultPrevented()?De.error(vd(e,{content:"",...n}).content):De.value(n)}},GC=(e,t,n)=>{if(n.no_events)return t;{const o=WC(t,(t=>vd(e,{...n,content:t})),KC(e));return o.content}},XC=(e,t)=>{if(t.no_events)return De.value(t);{const n=WC(t.content,(n=>((e,t)=>e.dispatch("BeforeSetContent",t))(e,{...t,content:n})),KC(e));return n.isDefaultPrevented()?(bd(e,n),De.error(void 0)):De.value(n)}},QC=(e,t,n)=>{n.no_events||bd(e,{...n,content:t})},ZC="autoresize_on_init,content_editable_state,padd_empty_with_br,block_elements,boolean_attributes,editor_deselector,editor_selector,elements,file_browser_callback_types,filepicker_validator_handler,force_hex_style_colors,force_p_newlines,gecko_spellcheck,images_dataimg_filter,media_scripts,mode,move_caret_before_on_enter_elements,non_empty_elements,self_closing_elements,short_ended_elements,special,spellchecker_select_languages,spellchecker_whitelist,tab_focus,tabfocus_elements,table_responsive_width,text_block_elements,text_inline_elements,toolbar_drawer,types,validate,whitespace_elements,paste_enable_default_filters,paste_filter_drop,paste_word_valid_elements,paste_retain_style_properties,paste_convert_word_fake_lists,template_cdate_classes,template_mdate_classes,template_selected_content_classes,template_preview_replace_values,template_replace_values,templates,template_cdate_format,template_mdate_format".split(","),JC=[],ew="bbcode,colorpicker,contextmenu,fullpage,legacyoutput,spellchecker,template,textcolor,rtc".split(","),tw=[{name:"export",replacedWith:"Export to PDF"}],nw=(e,t)=>{const n=Y(t,(t=>Se(e,t)));return ae(n)},ow=e=>{const t=nw(e,ZC),n=e.forced_root_block;return!1!==n&&""!==n||t.push("forced_root_block (false only)"),ae(t)},rw=e=>nw(e,JC),sw=(e,t)=>{const n=dn.makeMap(e.plugins," "),o=Y(t,(e=>Se(n,e)));return ae(o)},aw=e=>sw(e,ew),iw=e=>sw(e,tw.map((e=>e.name))),lw=e=>Z(tw,(t=>t.name===e)).fold((()=>e),(t=>t.replacedWith?`${e}, replaced by ${t.replacedWith}`:e)),dw={fire:'The "fire" event api has been deprecated and will be removed in TinyMCE 9. Use "dispatch" instead.',selectionSetContent:'The "editor.selection.setContent" method has been deprecated and will be removed in TinyMCE 9. Use "editor.insertContent" instead.'},cw=e=>{console.warn(dw[e],(new Error).stack)},mw=e=>0===e.dom.length?(_o(e),I.none()):I.some(e),uw=(e,t,n,o,r)=>{e.bind((e=>((o?$h:jh)(e.dom,o?e.dom.length:0,r),t.filter(An).map((t=>((e,t,n,o,r)=>{const s=e.dom,a=t.dom,i=o?s.length:a.length;o?(Hh(s,a,r,!1,!o),n.setStart(a,i)):(Hh(a,s,r,!1,!o),n.setEnd(a,i))})(e,t,n,o,r)))))).orThunk((()=>{const e=((e,t)=>e.filter((e=>ag.isBookmarkNode(e.dom))).bind(t?zn:Un))(t,o).or(t).filter(An);return e.map((e=>((e,t,n)=>{Ln(e).each((o=>{const r=e.dom;t&&Oh(o,Ul(r,0),n)?jh(r,0,n):!t&&Bh(o,Ul(r,r.length),n)&&$h(r,r.length,n)}))})(e,o,r)))}))},fw=(e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,selection:!0,content:t}))(n,t);XC(e,o).each((t=>{const n=((e,t)=>{if("raw"!==t.format){const n=e.selection.getRng(),o=e.dom.getParent(n.commonAncestorContainer,e.dom.isBlock),r=o?{context:o.nodeName.toLowerCase()}:{},s=e.parser.parse(t.content,{forced_root_block:!1,...r,...t});return $p({validate:!1},e.schema).serialize(s)}return t.content})(e,t),o=e.selection.getRng();((e,t,n)=>{const o=I.from(t.firstChild).map(mn.fromDom),r=I.from(t.lastChild).map(mn.fromDom);e.deleteContents(),e.insertNode(t);const s=o.bind(Un).filter(An).bind(mw),a=r.bind(zn).filter(An).bind(mw);uw(s,o,e,!0,n),uw(a,r,e,!1,n),e.collapse(!1)})(o,o.createContextualFragment(n),e.schema),e.selection.setRng(o),up(e,o),QC(e,n,t)}))},gw=(e,t)=>{let n=t.firstChild,o=t.lastChild;return n&&"meta"===n.name&&(n=n.next),o&&"mce_marker"===o.attr("id")&&(o=o.prev),((e,t)=>{const n=e.getNonEmptyElements();return C(t)&&(t.isEmpty(n)||((e,t)=>e.getBlockElements()[t.name]&&(e=>C(e.firstChild)&&e.firstChild===e.lastChild)(t)&&(e=>"br"===e.name||e.value===dt)(t.firstChild))(e,t))})(e,o)&&(o=null==o?void 0:o.prev),!(!n||n!==o||"ul"!==n.name&&"ol"!==n.name)},pw=e=>{return e.length>0&&(!(n=e[e.length-1]).firstChild||C(null==(t=n)?void 0:t.firstChild)&&t.firstChild===t.lastChild&&(e=>e.data===dt||ms(e))(t.firstChild))?e.slice(0,-1):e;var t,n},hw=(e,t)=>{const n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},bw=(e,t)=>{const n=Ul.after(e),o=Nu(t).prev(n);return o?o.toRange():null},vw=(e,t,n,o)=>{const r=((e,t,n)=>{const o=t.serialize(n);return(e=>{var t,n;const o=e.firstChild,r=e.lastChild;return o&&"META"===o.nodeName&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o)),r&&"mce_marker"===r.id&&(null===(n=r.parentNode)||void 0===n||n.removeChild(r)),e})(e.createFragment(o))})(t,e,o),s=hw(t,n.startContainer),a=pw((i=r.firstChild,Y(null!==(l=null==i?void 0:i.childNodes)&&void 0!==l?l:[],(e=>"LI"===e.nodeName))));var i,l;const d=t.getRoot(),c=e=>{const o=Ul.fromRangeStart(n),r=Nu(t.getRoot()),a=1===e?r.prev(o):r.next(o),i=null==a?void 0:a.getNode();return!i||hw(t,i)!==s};return s?c(1)?((e,t,n)=>{const o=e.parentNode;return o&&dn.each(t,(t=>{o.insertBefore(t,e)})),((e,t)=>{const n=Ul.before(e),o=Nu(t).next(n);return o?o.toRange():null})(e,n)})(s,a,d):c(2)?((e,t,n,o)=>(o.insertAfter(t.reverse(),e),bw(t[0],n)))(s,a,d,t):((e,t,n,o)=>{const r=((e,t)=>{const n=t.cloneRange(),o=t.cloneRange();return n.setStartBefore(e),o.setEndAfter(e),[n.cloneContents(),o.cloneContents()]})(e,o),s=e.parentNode;return s&&(s.insertBefore(r[0],e),dn.each(t,(t=>{s.insertBefore(t,e)})),s.insertBefore(r[1],e),s.removeChild(e)),bw(t[t.length-1],n)})(s,a,d,n):null},yw=["pre"],Cw=bs,ww=(e,t)=>{const n=e.schema.getTextInlineElements(),o=e.dom;if(t){const t=e.getBody(),r=$b(e),s="*[data-mce-fragment]",a=o.select(s);dn.each(a,(e=>{const a=e=>C(n[e.nodeName.toLowerCase()]),i=e=>1===e.childNodes.length;if(!Kp(o,l=e)&&!((e,t)=>Kp(e,t)&&H(Wp(e,t),(e=>(e=>Vp.has(e))(e))))(o,l)&&a(e)&&i(e)){const n=Wp(o,e),l=(e,t)=>ne(e,(e=>$(t,e))),d=t=>i(e)&&o.is(t,s)&&a(t)&&(t.nodeName===e.nodeName&&l(n,Wp(o,t))||d(t.children[0])),c=n=>C(n)&&n!==t&&(r.compare(e,n)||c(n.parentElement)),m=n=>C(n)&&n!==t&&o.is(n,s)&&(((e,t,n)=>{const o=Wp(e,t),r=Wp(e,n),s=o=>{var r,s;const a=null!==(r=e.getStyle(t,o))&&void 0!==r?r:"",i=null!==(s=e.getStyle(n,o))&&void 0!==s?s:"";return nt(a)&&nt(i)&&a!==i};return H(o,(e=>{const t=t=>H(t,(t=>t===e));if(!t(r)&&t(qp)){const e=Y(r,(e=>H(qp,(t=>Xe(e,t)))));return H(e,s)}return s(e)}))})(o,e,n)||m(n.parentElement));(d(e.children[0])||c(e.parentElement)&&!m(e.parentElement))&&o.remove(e,!0)}var l})),((e,t)=>{const n=Y(t,(t=>Wv(e.formatter,t)));Yv(e,"strikethrough",n)})(e,To(a))}},Ew=(e,t,n)=>{var o;const r=e.selection,s=e.dom,a=e.parser,i=n.merge,l=$p({validate:!0},e.schema),d='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;</span>';n.preserve_zwsp||(t=ji(t)),-1===t.indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,d);let c=r.getRng();const m=c.startContainer,u=e.getBody();m===u&&r.isCollapsed()&&s.isBlock(u.firstChild)&&((e,t)=>C(t)&&!e.schema.getVoidElements()[t.nodeName])(e,u.firstChild)&&s.isEmpty(u.firstChild)&&(c=s.createRng(),c.setStart(u.firstChild,0),c.setEnd(u.firstChild,0),r.setRng(c)),r.isCollapsed()||(e=>{const t=e.dom,n=ay(e.selection.getRng());e.selection.setRng(n);const o=t.getParent(n.startContainer,Cw);((e,t,n)=>!!C(n)&&n===e.getParent(t.endContainer,Cw)&&mf(mn.fromDom(n),t))(t,n,o)?Tb(e,n,mn.fromDom(o)):hf(n,fs)||(r=n,hf(r,ss))?n.deleteContents():e.getDoc().execCommand("Delete",!1);var r})(e);const f=r.getNode(),g={context:f.nodeName.toLowerCase(),data:n.data,insert:!0},p=a.parse(t,g);if(!0===n.paste&&gw(e.schema,p)&&((e,t)=>!!hw(e,t))(s,f))return c=vw(l,s,r.getRng(),p),c&&r.setRng(c),t;!0===n.paste&&((e,t,n,o)=>{var r;const s=t.firstChild,a=t.lastChild,i=s===("bookmark"===a.attr("data-mce-type")?a.prev:a),l=$(yw,s.name);if(i&&l){const t="false"!==s.attr("contenteditable"),a=(null===(r=e.getParent(n,e.isBlock))||void 0===r?void 0:r.nodeName.toLowerCase())===s.name,i=I.from(Ub(o,n)).forall(gs);return t&&a&&i}return!1})(s,p,f,e.getBody())&&(null===(o=p.firstChild)||void 0===o||o.unwrap()),(e=>{let t=e;for(;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")})(p);let h=p.lastChild;if(h&&"mce_marker"===h.attr("id")){const t=h;for(h=h.prev;h&&"table"!==h.name;h=h.walk(!0))if(3===h.type||!s.isBlock(h.name)){h.parent&&e.schema.isValidChild(h.parent.name,"span")&&h.parent.insert(t,h,"br"===h.name);break}}if(e._selectionOverrides.showBlockCaretContainer(f),g.invalid||((e,t,n)=>{var o;return H(n.children(),Gv)&&"SUMMARY"===(null===(o=e.getParent(t,e.isBlock))||void 0===o?void 0:o.nodeName)})(s,f,p)){fw(e,d);let n,o=r.getNode();const i=e.getBody();for(ds(o)?o=n=i:n=o;n&&n!==i;)o=n,n=n.parentNode;t=o===i?i.innerHTML:s.getOuterHTML(o);const c=a.parse(t),m=(e=>{for(let t=e;t;t=t.walk())if("mce_marker"===t.attr("id"))return I.some(t);return I.none()})(c),u=m.bind(ny).getOr(c);m.each((e=>e.replace(p)));const f=(e=>{const t=[];for(let n=e.firstChild;C(n);n=n.walk())t.push(n);return t})(p);p.unwrap();const g=Y(f,(t=>sy(e.schema,t)));ry(g,e.schema,u),Zv(a.getNodeFilters(),a.getAttributeFilters(),c),t=l.serialize(c),o===i?s.setHTML(i,t):s.setOuterHTML(o,t)}else t=l.serialize(p),((e,t,n)=>{var o;"all"===n.getAttribute("data-mce-bogus")?null===(o=n.parentNode)||void 0===o||o.insertBefore(e.dom.createFragment(t),n):((e,t)=>{if(e.isBlock(t)&&e.isEditable(t)){const e=t.childNodes;return 1===e.length&&ms(e[0])||0===e.length}return!1})(e.dom,n)?e.dom.setHTML(n,t):fw(e,t,{no_events:!0})})(e,t,f);var b;return ww(e,i),((e,t)=>{var n,o,r;let s;const a=e.dom,i=e.selection;if(!t)return;i.scrollIntoView(t);const l=Ub(e.getBody(),t);if(l&&"false"===a.getContentEditable(l))return a.remove(t),void i.select(l);let d=a.createRng();const c=t.previousSibling;if(ss(c)){d.setStart(c,null!==(o=null===(n=c.nodeValue)||void 0===n?void 0:n.length)&&void 0!==o?o:0);const e=t.nextSibling;ss(e)&&(c.appendData(e.data),null===(r=e.parentNode)||void 0===r||r.removeChild(e))}else d.setStartBefore(t),d.setEndBefore(t);const m=a.getParent(t,a.isBlock);if(a.remove(t),m&&a.isEmpty(m)){const t=Cw(m);So(mn.fromDom(m)),d.setStart(m,0),d.setEnd(m,0),t||(e=>!!e.getAttribute("data-mce-fragment"))(m)||!(s=(t=>{let n=Ul.fromRangeStart(t);return n=Nu(e.getBody()).next(n),null==n?void 0:n.toRange()})(d))?a.add(m,a.create("br",t?{}:{"data-mce-bogus":"1"})):(d=s,a.remove(m))}i.setRng(d)})(e,s.get("mce_marker")),b=e.getBody(),dn.each(b.getElementsByTagName("*"),(e=>{e.removeAttribute("data-mce-fragment")})),((e,t,n)=>{I.from(e.getParent(t,"td,th")).map(mn.fromDom).each((e=>((e,t)=>{Wn(e).each((n=>{Un(n).each((o=>{t.isBlock(En(e))&&Di(n)&&t.isBlock(En(o))&&_o(n)}))}))})(e,n)))})(s,r.getStart(),e.schema),((e,t,n)=>{const o=In(mn.fromDom(n),(e=>vn(e,mn.fromDom(t))));ie(o,o.length-2).filter(Nn).fold((()=>$s(e,t)),(t=>$s(e,t.dom)))})(e.schema,e.getBody(),r.getStart()),t},xw=(e,t,n)=>{e.dom.setHTML(e.getBody(),t),!0!==n&&(e=>{Dg(e)&&Iu(e.getBody()).each((t=>{const n=t.getNode(),o=ns(n)?Iu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e)},Sw={},_w=Jr(["pre"]);(e=>{Sw[e]||(Sw[e]=[]),Sw[e].push((e=>{if(!e.selection.getRng().collapsed){const t=e.selection.getSelectedBlocks(),n=Y(Y(t,_w),(e=>t=>{const n=t.previousSibling;return _w(n)&&$(e,n)})(t));q(n,(e=>{((e,t)=>{const n=mn.fromDom(t),o=Bn(n).dom;_o(n),go(mn.fromDom(e),[mn.fromTag("br",o),mn.fromTag("br",o),...Hn(n)])})(e.previousSibling,e)}))}}))})("pre");const kw=dn.each,Nw=dn.each,Aw=(e,t,n,o)=>{const r=e.formatter.get(t),s=r[0],a=!o&&e.selection.isCollapsed(),i=e.dom,l=e.selection,d=(t,o)=>{let r=!1;return Nw(t,(t=>!(!Pf(t)||("false"!==i.getContentEditable(o)||t.ceFalseOverride)&&(!C(t.collapsed)||t.collapsed===a)&&i.is(o,t.selector)&&!zu(o)&&(Wb(e,o,t,n,o),r=!0,1)))),r},c=t=>{if(u(t)){const r=i.create(t);return Wb(e,r,s,n,o),r}return null},m=(a,i,l)=>{const m=[];let u=!0;const f=s.inline||s.block,g=c(f);eg(a,i,(o=>{let i;const c=o=>{let p=!1,h=u,b=!1;const v=o.parentNode,y=v.nodeName.toLowerCase(),w=a.getContentEditable(o);C(w)&&(h=u,u="true"===w,p=!0,b=_f(e,o));const E=u&&!p;if(ms(o)&&!((e,t,n,o)=>{if(wc(e)&&Lf(t)&&n.parentNode){const t=Oa(e.schema),r=_r(mn.fromDom(n),(e=>zu(e.dom)));return _e(t,o)&&As(e.schema,n.parentNode,{skipBogus:!1,includeZwsp:!0})&&!r}return!1})(e,s,o,y))return i=null,void(Bf(s)&&a.remove(o));if((o=>(e=>Bf(e)&&!0===e.wrapper)(s)&&Zb(e,o,t,n))(o))i=null;else{if(((t,n,o)=>{const r=(e=>Bf(e)&&!0!==e.wrapper)(s)&&Ef(e.schema,t)&&xf(e,n,f);return o&&r})(o,y,E)){const t=a.rename(o,f);return Wb(e,t,s,n,o),m.push(t),void(i=null)}if(Pf(s)){let e=d(r,o);if(!e&&C(v)&&Mf(s)&&(e=d(r,v)),!Lf(s)||e)return void(i=null)}C(g)&&((t,n,o,r)=>{const i=t.nodeName.toLowerCase(),d=xf(e,f,i)&&xf(e,n,f),c=!l&&ss(t)&&zi(t.data),m=zu(t),u=!Lf(s)||!a.isBlock(t);return(o||r)&&d&&!c&&!m&&u})(o,y,E,b)?(i||(i=a.clone(g,!1),v.insertBefore(i,o),m.push(i)),b&&p&&(u=h),i.appendChild(o)):(i=null,q(ce(o.childNodes),c),p&&(u=h),i=null)}};q(o,c)})),!0===s.links&&q(m,(t=>{const r=t=>{"A"===t.nodeName&&Wb(e,t,s,n,o),q(ce(t.childNodes),r)};r(t)})),((e,t,n)=>{if($(qv,t)){const t=((e,t)=>te(t,(t=>{const n=Er(t,(t=>Wv(e,t)));return Wv(e,t)?[t,...n]:n})))(e.formatter,n);Yv(e,"strikethrough",t)}})(e,t,To(m)),q(m,(o=>{const i=(e=>{let t=0;return q(e.childNodes,(e=>{(e=>C(e)&&ss(e)&&0===e.length)(e)||Ju(e)||t++})),t})(o);!(m.length>1)&&a.isBlock(o)||0!==i?(Lf(s)||Bf(s)&&s.wrapper)&&(s.exact||1!==i||(o=(t=>{const o=Z(t.childNodes,vf).filter((e=>"false"!==a.getContentEditable(e)&&Xb(a,e,s)));return o.map((o=>{const r=a.clone(o,!1);return Wb(e,r,s,n,t),a.replace(r,t,!0),a.remove(o,!0),r})).getOr(t)})(o)),((e,t,n,o)=>{kw(t,(t=>{Lf(t)&&kw(e.dom.select(t.inline,o),(o=>{Cv(o)&&Hv(e,t,n,o,t.exact?o:null)})),((e,t,n)=>{if(t.clear_child_styles){const o=t.links?"*:not(a)":"*";yv(e.select(o,n),(n=>{Cv(n)&&e.isEditable(n)&&yv(t.styles,((t,o)=>{e.setStyle(n,o,"")}))}))}})(e.dom,t,o)}))})(e,r,n,o),((e,t,n,o,r)=>{const s=r.parentNode;Zb(e,s,n,o)&&Hv(e,t,o,r)||t.merge_with_parents&&s&&e.dom.getParent(s,(s=>!!Zb(e,s,n,o)&&(Hv(e,t,o,r),!0)))})(e,s,t,n,o),((e,t,n,o)=>{if(t.styles&&t.styles.backgroundColor){const r=_v(e,"fontSize");Sv(o,(t=>r(t)&&e.isEditable(t)),kv(e,"backgroundColor",kf(t.styles.backgroundColor,n)))}})(a,s,n,o),((e,t,n,o)=>{const r=t=>{if(Qr(t)&&Xr(t.parentNode)&&e.isEditable(t)){const n=Df(e,t.parentNode);e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null)}};t.styles&&(t.styles.color||t.styles.textDecoration)&&(dn.walk(o,r,"childNodes"),r(o))})(a,s,0,o),((e,t,n,o)=>{if(Lf(t)&&("sub"===t.inline||"sup"===t.inline)){const n=_v(e,"fontSize");Sv(o,(t=>n(t)&&e.isEditable(t)),kv(e,"fontSize",""));const r=Y(e.select("sup"===t.inline?"sub":"sup",o),e.isEditable);e.remove(r,!0)}})(a,s,0,o),xv(e,s,0,o)):a.remove(o,!0)}))},f=bf(o)?o:l.getNode();if("false"===i.getContentEditable(f)&&!_f(e,f))return d(r,o=f),void pd(e,t,o,n);if(s){if(o)if(bf(o)){if(!d(r,o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),m(i,Jf(i,e,r),!0)}}else m(i,o,!0);else a&&Lf(s)&&!af(e).length?((e,t,n)=>{let o;const r=e.selection,s=e.formatter.get(t);if(!s)return;const a=r.getRng();let i=a.startOffset;const l=a.startContainer.nodeValue;o=ju(e.getBody(),r.getStart());const d=/[^\s\u00a0\u00ad\u200b\ufeff]/;if(l&&i>0&&i<l.length&&d.test(l.charAt(i))&&d.test(l.charAt(i-1))){const o=r.getBookmark();a.collapse(!0);let i=Jf(e.dom,a,s);i=Gg(i),e.formatter.apply(t,n,i),r.moveToBookmark(o)}else{let s=o?tv(o):null;o&&(null==s?void 0:s.data)===ev||(c=e.getDoc(),m=nv(!0).dom,o=c.importNode(m,!0),s=o.firstChild,a.insertNode(o),i=1,lv(e,o)),e.formatter.apply(t,n,o),r.setCursorLocation(s,i)}var c,m})(e,t,n):(l.setRng(ay(l.getRng())),yf(e,(()=>{gf(e,((e,t)=>{const n=t?e:Jf(i,e,r);m(i,n,!1)}))}),M),e.nodeChanged()),gv(e.formatter,t).each((t=>{q((e=>Y((e=>{const t=e.getSelectedBlocks(),n=e.getRng();if(e.isCollapsed())return[];if(1===t.length)return pv(n,t[0])&&hv(n,t[0])?t:[];{const e=le(t).filter((e=>pv(n,e))).toArray(),o=de(t).filter((e=>hv(n,e))).toArray(),r=t.slice(1,-1);return e.concat(r).concat(o)}})(e),bv(e.dom)))(e.selection),(e=>qb(i,e,t,n)))}));((e,t)=>{Se(Sw,e)&&q(Sw[e],(e=>{e(t)}))})(t,e)}pd(e,t,o,n)},Rw=(e,t,n,o)=>{(o||e.selection.isEditable())&&Aw(e,t,n,o)},Dw=e=>Se(e,"vars"),Tw=e=>e.selection.getStart(),Ow=(e,t,n,o,r)=>Q(t,(t=>{const s=e.formatter.matchNode(t,n,null!=r?r:{},o);return!v(s)}),(t=>!!Yb(e,t,n)||!o&&C(e.formatter.matchNode(t,n,r,!0)))),Bw=(e,t)=>{const n=null!=t?t:Tw(e);return Y(Tf(e.dom,n),(e=>Xr(e)&&!ts(e)))},Pw=(e,t,n)=>{const o=Bw(e,t);pe(n,((n,r)=>{const s=n=>{const s=Ow(e,o,r,n.similar,Dw(n)?n.vars:void 0),a=s.isSome();if(n.state.get()!==a){n.state.set(a);const e=s.getOr(t);Dw(n)?n.callback(a,{node:e,format:r,parents:o}):q(n.callbacks,(t=>t(a,{node:e,format:r,parents:o})))}};q([n.withSimilar,n.withoutSimilar],s),q(n.withVars,s)}))},Lw=(e,t,n)=>({element:e,width:t,rows:n}),Mw=(e,t)=>({element:e,cells:t}),Iw=(e,t)=>({x:e,y:t}),Fw=(e,t)=>yo(e,t).bind(rt).getOr(1),Uw=(e,t,n)=>{const o=e.rows;return!!(o[n]?o[n].cells:[])[t]},zw=e=>X(e,((e,t)=>t.cells.length>e?t.cells.length:e),0),jw=(e,t)=>{const n=e.rows;for(let e=0;e<n.length;e++){const o=n[e].cells;for(let n=0;n<o.length;n++)if(vn(o[n],t))return I.some(Iw(n,e))}return I.none()},$w=(e,t,n,o,r)=>{const s=[],a=e.rows;for(let e=n;e<=r;e++){const n=a[e].cells,r=t<o?n.slice(t,o+1):n.slice(o,t+1);s.push(Mw(a[e].element,r))}return s},Hw=e=>((e,t)=>{const n=Ao(e.element),o=mn.fromTag("tbody");return go(o,t),mo(n,o),n})(e,(e=>V(e.rows,(e=>{const t=V(e.cells,(e=>{const t=Ro(e);return wo(t,"colspan"),wo(t,"rowspan"),t})),n=Ao(e.element);return go(n,t),n})))(e)),Vw=(e,t,n)=>{const o=mn.fromDom(t.commonAncestorContainer),r=ch(o,e),s=Y(r,(e=>n.isWrapper(En(e)))),a=((e,t)=>Z(e,(e=>"li"===En(e)&&mf(e,t))).fold(N([]),(t=>(e=>Z(e,(e=>"ul"===En(e)||"ol"===En(e))))(e).map((e=>{const t=mn.fromTag(En(e)),n=Ce($o(e),((e,t)=>Xe(t,"list-style")));return Fo(t,n),[mn.fromTag("li"),t]})).getOr([]))))(r,t),i=s.concat(a.length?a:(e=>Bi(e)?Ln(e).filter(Oi).fold(N([]),(t=>[e,t])):Oi(e)?[e]:[])(o));return V(i,Ao)},qw=()=>Zo([]),Ww=(e,t)=>((e,t)=>ir(t,"table",D(vn,e)))(e,t[0]).bind((e=>{const n=t[0],o=t[t.length-1],r=(e=>{const t=Lw(Ao(e),0,[]);return q(xr(e,"tr"),((e,n)=>{q(xr(e,"td,th"),((o,r)=>{((e,t,n,o,r)=>{const s=Fw(r,"rowspan"),a=Fw(r,"colspan"),i=e.rows;for(let e=n;e<n+s;e++){i[e]||(i[e]=Mw(Ro(o),[]));for(let o=t;o<t+a;o++)i[e].cells[o]=e===n&&o===t?r:Ao(r)}})(t,((e,t,n)=>{for(;Uw(e,t,n);)t++;return t})(t,r,n),n,e,o)}))})),Lw(t.element,zw(t.rows),t.rows)})(e);return((e,t,n)=>jw(e,t).bind((t=>jw(e,n).map((n=>((e,t,n)=>{const o=t.x,r=t.y,s=n.x,a=n.y,i=r<a?$w(e,o,r,s,a):$w(e,o,a,s,r);return Lw(e.element,zw(i),i)})(e,t,n))))))(r,n,o).map((e=>Zo([Hw(e)])))})).getOrThunk(qw),Kw=(e,t,n)=>{const o=sf(t,e);return o.length>0?Ww(e,o):((e,t,n)=>t.length>0&&t[0].collapsed?qw():((e,t,n)=>((e,t)=>{const n=X(t,((e,t)=>(mo(t,e),t)),e);return t.length>0?Zo([n]):n})(mn.fromDom(t.cloneContents()),Vw(e,t,n)))(e,t[0],n))(e,t,n)},Yw=(e,t)=>t>=0&&t<e.length&&nf(e.charAt(t)),Gw=e=>ji(e.innerText),Xw=e=>Xr(e)?e.outerHTML:ss(e)?ba.encodeRaw(e.data,!1):ls(e)?"\x3c!--"+e.data+"--\x3e":"",Qw=(e,t)=>(((e,t)=>{let n=0;q(e,(e=>{0===e[0]?n++:1===e[0]?(((e,t,n)=>{const o=(e=>{let t;const n=document.createElement("div"),o=document.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)o.appendChild(t);return o})(t);if(e.hasChildNodes()&&n<e.childNodes.length){const t=e.childNodes[n];e.insertBefore(o,t)}else e.appendChild(o)})(t,e[1],n),n++):2===e[0]&&((e,t)=>{if(e.hasChildNodes()&&t<e.childNodes.length){const n=e.childNodes[t];e.removeChild(n)}})(t,n)}))})(((e,t)=>{const n=e.length+t.length+2,o=new Array(n),r=new Array(n),s=(n,o,r,a,l)=>{const d=i(n,o,r,a);if(null===d||d.start===o&&d.diag===o-a||d.end===n&&d.diag===n-r){let s=n,i=r;for(;s<o||i<a;)s<o&&i<a&&e[s]===t[i]?(l.push([0,e[s]]),++s,++i):o-n>a-r?(l.push([2,e[s]]),++s):(l.push([1,t[i]]),++i)}else{s(n,d.start,r,d.start-d.diag,l);for(let t=d.start;t<d.end;++t)l.push([0,e[t]]);s(d.end,o,d.end-d.diag,a,l)}},a=(n,o,r,s)=>{let a=n;for(;a-o<s&&a<r&&e[a]===t[a-o];)++a;return((e,t,n)=>({start:e,end:t,diag:n}))(n,a,o)},i=(n,s,i,l)=>{const d=s-n,c=l-i;if(0===d||0===c)return null;const m=d-c,u=c+d,f=(u%2==0?u:u+1)/2;let g,p,h,b,v;for(o[1+f]=n,r[1+f]=s+1,g=0;g<=f;++g){for(p=-g;p<=g;p+=2){for(h=p+f,p===-g||p!==g&&o[h-1]<o[h+1]?o[h]=o[h+1]:o[h]=o[h-1]+1,b=o[h],v=b-n+i-p;b<s&&v<l&&e[b]===t[v];)o[h]=++b,++v;if(m%2!=0&&m-g<=p&&p<=m+g&&r[h-m]<=o[h])return a(r[h-m],p+n-i,s,l)}for(p=m-g;p<=m+g;p+=2){for(h=p+f-m,p===m-g||p!==m+g&&r[h+1]<=r[h-1]?r[h]=r[h+1]-1:r[h]=r[h-1],b=r[h]-1,v=b-n+i-p;b>=n&&v>=i&&e[b]===t[v];)r[h]=b--,v--;if(m%2==0&&-g<=p&&p<=g&&r[h]<=o[h+m])return a(r[h],p+n-i,s,l)}}return null},l=[];return s(0,e.length,0,t.length,l),l})(V(ce(t.childNodes),Xw),e),t),t),Zw=it((()=>document.implementation.createHTMLDocument("undo"))),Jw=e=>{const t=e.serializer.getTempAttrs(),n=Mp(e.getBody(),t);return(e=>null!==e.querySelector("iframe"))(n)?{type:"fragmented",fragments:Y(V(ce(n.childNodes),_(ji,Xw)),(e=>e.length>0)),content:"",bookmark:null,beforeBookmark:null}:{type:"complete",fragments:null,content:ji(n.innerHTML),bookmark:null,beforeBookmark:null}},eE=(e,t,n)=>{const o=n?t.beforeBookmark:t.bookmark;"fragmented"===t.type?Qw(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw",no_selection:!C(o)||!Hu(o)||!o.isFakeCaret}),o&&(e.selection.moveToBookmark(o),e.selection.scrollIntoView())},tE=e=>"fragmented"===e.type?e.fragments.join(""):e.content,nE=e=>{const t=mn.fromTag("body",Zw());return Bo(t,tE(e)),q(xr(t,"*[data-mce-bogus]"),ko),Oo(t)},oE=(e,t)=>!(!e||!t)&&(!!((e,t)=>tE(e)===tE(t))(e,t)||((e,t)=>nE(e)===nE(t))(e,t)),rE=e=>0===e.get(),sE=(e,t,n)=>{rE(n)&&(e.typing=t)},aE=(e,t)=>{e.typing&&(sE(e,!1,t),e.add())},iE=e=>({init:{bindEvents:S},undoManager:{beforeChange:(t,n)=>((e,t,n)=>{rE(t)&&n.set(ad(e.selection))})(e,t,n),add:(t,n,o,r,s,a)=>((e,t,n,o,r,s,a)=>{const i=Jw(e),l=dn.extend(s||{},i);if(!rE(o)||e.removed)return null;const d=t.data[n.get()];if(e.dispatch("BeforeAddUndo",{level:l,lastLevel:d,originalEvent:a}).isDefaultPrevented())return null;if(d&&oE(d,l))return null;t.data[n.get()]&&r.get().each((e=>{t.data[n.get()].beforeBookmark=e}));const c=Rc(e);if(c&&t.data.length>c){for(let e=0;e<t.data.length-1;e++)t.data[e]=t.data[e+1];t.data.length--,n.set(t.data.length)}l.bookmark=ad(e.selection),n.get()<t.data.length-1&&(t.data.length=n.get()+1),t.data.push(l),n.set(t.data.length-1);const m={level:l,lastLevel:d,originalEvent:a};return n.get()>0?(e.setDirty(!0),e.dispatch("AddUndo",m),e.dispatch("change",m)):e.dispatch("AddUndo",m),l})(e,t,n,o,r,s,a),undo:(t,n,o)=>((e,t,n,o)=>{let r;return t.typing&&(t.add(),t.typing=!1,sE(t,!1,n)),o.get()>0&&(o.set(o.get()-1),r=t.data[o.get()],eE(e,r,!0),e.setDirty(!0),e.dispatch("Undo",{level:r})),r})(e,t,n,o),redo:(t,n)=>((e,t,n)=>{let o;return t.get()<n.length-1&&(t.set(t.get()+1),o=n[t.get()],eE(e,o,!1),e.setDirty(!0),e.dispatch("Redo",{level:o})),o})(e,t,n),clear:(t,n)=>((e,t,n)=>{t.data=[],n.set(0),t.typing=!1,e.dispatch("ClearUndos")})(e,t,n),reset:e=>(e=>{e.clear(),e.add()})(e),hasUndo:(t,n)=>((e,t,n)=>n.get()>0||t.typing&&t.data[0]&&!oE(Jw(e),t.data[0]))(e,t,n),hasRedo:(e,t)=>((e,t)=>t.get()<e.data.length-1&&!e.typing)(e,t),transact:(e,t,n)=>((e,t,n)=>(aE(e,t),e.beforeChange(),e.ignore(n),e.add()))(e,t,n),ignore:(e,t)=>((e,t)=>{try{e.set(e.get()+1),t()}finally{e.set(e.get()-1)}})(e,t),extra:(t,n,o,r)=>((e,t,n,o,r)=>{if(t.transact(o)){const o=t.data[n.get()].bookmark,s=t.data[n.get()-1];eE(e,s,!0),t.transact(r)&&(t.data[n.get()-1].beforeBookmark=o)}})(e,t,n,o,r)},formatter:{match:(t,n,o,r)=>Jb(e,t,n,o,r),matchAll:(t,n)=>((e,t,n)=>{const o=[],r={},s=e.selection.getStart();return e.dom.getParent(s,(s=>{for(let a=0;a<t.length;a++){const i=t[a];!r[i]&&Zb(e,s,i,n)&&(r[i]=!0,o.push(i))}}),e.dom.getRoot()),o})(e,t,n),matchNode:(t,n,o,r)=>Zb(e,t,n,o,r),canApply:t=>((e,t)=>{const n=e.formatter.get(t),o=e.dom;if(n&&e.selection.isEditable()){const t=e.selection.getStart(),r=Tf(o,t);for(let e=n.length-1;e>=0;e--){const t=n[e];if(!Pf(t))return!0;for(let e=r.length-1;e>=0;e--)if(o.is(r[e],t.selector))return!0}}return!1})(e,t),closest:t=>((e,t)=>{const n=t=>vn(t,mn.fromDom(e.getBody()));return I.from(e.selection.getStart(!0)).bind((o=>Ar(mn.fromDom(o),(n=>me(t,(t=>((t,n)=>Zb(e,t.dom,n)?I.some(n):I.none())(n,t)))),n))).getOrNull()})(e,t),apply:(t,n,o)=>Rw(e,t,n,o),remove:(t,n,o,r)=>$v(e,t,n,o,r),toggle:(t,n,o)=>((e,t,n,o)=>{const r=e.formatter.get(t);r&&(!Jb(e,t,n,o)||"toggle"in r[0]&&!r[0].toggle?Rw(e,t,n,o):$v(e,t,n,o))})(e,t,n,o),formatChanged:(t,n,o,r,s)=>((e,t,n,o,r,s)=>(((e,t,n,o,r,s)=>{const a=t.get();q(n.split(","),(t=>{const n=xe(a,t).getOrThunk((()=>{const e={withSimilar:{state:Ne(!1),similar:!0,callbacks:[]},withoutSimilar:{state:Ne(!1),similar:!1,callbacks:[]},withVars:[]};return a[t]=e,e})),i=()=>{const n=Bw(e);return Ow(e,n,t,r,s).isSome()};if(v(s)){const e=r?n.withSimilar:n.withoutSimilar;e.callbacks.push(o),1===e.callbacks.length&&e.state.set(i())}else n.withVars.push({state:Ne(i()),similar:r,vars:s,callback:o})})),t.set(a)})(e,t,n,o,r,s),{unbind:()=>((e,t,n)=>{const o=e.get();q(t.split(","),(e=>xe(o,e).each((t=>{o[e]={withSimilar:{...t.withSimilar,callbacks:Y(t.withSimilar.callbacks,(e=>e!==n))},withoutSimilar:{...t.withoutSimilar,callbacks:Y(t.withoutSimilar.callbacks,(e=>e!==n))},withVars:Y(t.withVars,(e=>e.callback!==n))}})))),e.set(o)})(t,n,o)}))(e,t,n,o,r,s)},editor:{getContent:t=>((e,t)=>I.from(e.getBody()).fold(N("tree"===t.format?new xp("body",11):""),(n=>Up(e,t,n))))(e,t),setContent:(t,n)=>((e,t,n)=>I.from(e.getBody()).map((o=>qC(t)?((e,t,n,o)=>{Zv(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);const r=$p({validate:!1},e.schema).serialize(n),s=ji(Mi(mn.fromDom(t))?r:dn.trim(r));return xw(e,s,o.no_selection),{content:n,html:s}})(e,o,t,n):((e,t,n,o)=>{if(0===(n=ji(n)).length||/^\s+$/.test(n)){const r='<br data-mce-bogus="1">';"TABLE"===t.nodeName?n="<tr><td>"+r+"</td></tr>":/^(UL|OL)$/.test(t.nodeName)&&(n="<li>"+r+"</li>");const s=Ld(e);return e.schema.isValidChild(t.nodeName.toLowerCase(),s.toLowerCase())?(n=r,n=e.dom.createHTML(s,Md(e),n)):n||(n=r),xw(e,n,o.no_selection),{content:n,html:n}}{"raw"!==o.format&&(n=$p({validate:!1},e.schema).serialize(e.parser.parse(n,{isRootContent:!0,insert:!0})));const r=Mi(mn.fromDom(t))?n:dn.trim(n);return xw(e,r,o.no_selection),{content:r,html:r}}})(e,o,t,n))).getOr({content:t,html:qC(n.content)?"":n.content}))(e,t,n),insertContent:(t,n)=>Ew(e,t,n),addVisual:t=>((e,t)=>{const n=e.dom,o=C(t)?t:e.getBody();q(n.select("table,a",o),(t=>{switch(t.nodeName){case"TABLE":const o=Fc(e),r=n.getAttrib(t,"border");r&&"0"!==r||!e.hasVisual?n.removeClass(t,o):n.addClass(t,o);break;case"A":if(!n.getAttrib(t,"href")){const o=n.getAttrib(t,"name")||t.id,r=Uc(e);o&&e.hasVisual?n.addClass(t,r):n.removeClass(t,r)}}})),e.dispatch("VisualAid",{element:t,hasVisual:e.hasVisual})})(e,t)},selection:{getContent:(t,n)=>((e,t,n={})=>{const o=((e,t)=>({...e,format:t,get:!0,selection:!0,getInner:!0}))(n,t);return YC(e,o).fold(A,(t=>{const n=((e,t)=>{if("text"===t.format)return(e=>I.from(e.selection.getRng()).map((t=>{var n;const o=I.from(e.dom.getParent(t.commonAncestorContainer,e.dom.isBlock)),r=e.getBody(),s=(e=>e.map((e=>e.nodeName)).getOr("div").toLowerCase())(o),a=mn.fromDom(t.cloneContents());Ip(a),Fp(a);const i=e.dom.add(r,s,{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},a.dom),l=Gw(i),d=ji(null!==(n=i.textContent)&&void 0!==n?n:"");if(e.dom.remove(i),Yw(d,0)||Yw(d,d.length-1)){const e=o.getOr(r),t=Gw(e),n=t.indexOf(l);return-1===n?l:(Yw(t,n-1)?" ":"")+l+(Yw(t,n+l.length)?" ":"")}return l})).getOr(""))(e);{const n=((e,t)=>{const n=e.selection.getRng(),o=e.dom.create("body"),r=e.selection.getSel(),s=vp(e,rf(r)),a=t.contextual?Kw(mn.fromDom(e.getBody()),s,e.schema).dom:n.cloneContents();return a&&o.appendChild(a),e.selection.serializer.serialize(o,t)})(e,t);return"tree"===t.format?n:e.selection.isCollapsed()?"":n}})(e,t);return GC(e,n,t)}))})(e,t,n)},autocompleter:{addDecoration:S,removeDecoration:S},raw:{getModel:()=>I.none()}}),lE=e=>Se(e.plugins,"rtc"),dE=e=>e.rtcInstance?e.rtcInstance:iE(e),cE=e=>{const t=e.rtcInstance;if(t)return t;throw new Error("Failed to get RTC instance not yet initialized.")},mE=e=>cE(e).init.bindEvents(),uE=(e,t,n)=>{if(Se(e,t)){const o=Y(e[t],(e=>e!==n));0===o.length?delete e[t]:e[t]=o}};const fE=e=>!(!e||!e.ownerDocument)&&yn(mn.fromDom(e.ownerDocument),mn.fromDom(e)),gE=(e,t,n,o)=>{let r,s;const{selectorChangedWithUnbind:a}=((e,t)=>{let n,o;const r=(t,n)=>Z(n,(n=>e.is(n,t))),s=t=>e.getParents(t,void 0,e.getRoot());return{selectorChangedWithUnbind:(e,a)=>(n||(n={},o={},t.on("NodeChange",(e=>{const t=e.element,a=s(t),i={};pe(n,((e,t)=>{r(t,a).each((n=>{o[t]||(q(e,(e=>{e(!0,{node:n,selector:t,parents:a})})),o[t]=e),i[t]=e}))})),pe(o,((e,n)=>{i[n]||(delete o[n],q(e,(e=>{e(!1,{node:t,selector:n,parents:a})})))}))}))),n[e]||(n[e]=[]),n[e].push(a),r(e,s(t.selection.getStart())).each((()=>{o[e]=n[e]})),{unbind:()=>{uE(n,e,a),uE(o,e,a)}})}})(e,o),i=e=>{const t=d();t.collapse(!!e),c(t)},l=()=>t.getSelection?t.getSelection():t.document.selection,d=()=>{let n;const a=(e,t,n)=>{try{return t.compareBoundaryPoints(e,n)}catch(e){return-1}},i=t.document;if(C(o.bookmark)&&!Dg(o)){const e=pg(o);if(e.isSome())return e.map((e=>vp(o,[e])[0])).getOr(i.createRange())}try{const e=l();e&&!Gr(e.anchorNode)&&(n=e.rangeCount>0?e.getRangeAt(0):i.createRange(),n=vp(o,[n])[0])}catch(e){}if(n||(n=i.createRange()),ds(n.startContainer)&&n.collapsed){const t=e.getRoot();n.setStart(t,0),n.setEnd(t,0)}return r&&s&&(0===a(n.START_TO_START,n,r)&&0===a(n.END_TO_END,n,r)?n=s:(r=null,s=null)),n},c=(e,t)=>{if(!(e=>!!e&&fE(e.startContainer)&&fE(e.endContainer))(e))return;const n=l();if(e=o.dispatch("SetSelectionRange",{range:e,forward:t}).range,n){s=e;try{n.removeAllRanges(),n.addRange(e)}catch(e){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),r=n.rangeCount>0?n.getRangeAt(0):null}if(!e.collapsed&&e.startContainer===e.endContainer&&(null==n?void 0:n.setBaseAndExtent)&&e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()){const t=e.startContainer.childNodes[e.startOffset];t&&"IMG"===t.nodeName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(t,0,t,1))}o.dispatch("AfterSetSelectionRange",{range:e,forward:t})},m=()=>{const t=l(),n=null==t?void 0:t.anchorNode,o=null==t?void 0:t.focusNode;if(!t||!n||!o||Gr(n)||Gr(o))return!0;const r=e.createRng(),s=e.createRng();try{r.setStart(n,t.anchorOffset),r.collapse(!0),s.setStart(o,t.focusOffset),s.collapse(!0)}catch(e){return!0}return r.compareBoundaryPoints(r.START_TO_START,s)<=0},u={dom:e,win:t,serializer:n,editor:o,expand:(t={type:"word"})=>c(Xg(e).expand(d(),t)),collapse:i,setCursorLocation:(t,n)=>{const r=e.createRng();C(t)&&C(n)?(r.setStart(t,n),r.setEnd(t,n),c(r),i(!1)):(uf(e,r,o.getBody(),!0),c(r))},getContent:e=>((e,t={})=>((e,t,n)=>cE(e).selection.getContent(t,n))(e,t.format?t.format:"html",t))(o,e),setContent:(e,t)=>((e,t,n={})=>{cw("selectionSetContent"),fw(e,t,n)})(o,e,t),getBookmark:(e,t)=>f.getBookmark(e,t),moveToBookmark:e=>f.moveToBookmark(e),select:(t,n)=>(((e,t,n)=>I.from(t).bind((t=>I.from(t.parentNode).map((o=>{const r=e.nodeIndex(t),s=e.createRng();return s.setStart(o,r),s.setEnd(o,r+1),n&&(uf(e,s,t,!0),uf(e,s,t,!1)),s})))))(e,t,n).each(c),t),isCollapsed:()=>{const e=d(),t=l();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isEditable:()=>{if(o.mode.isReadOnly())return!1;const t=d(),n=o.getBody().querySelectorAll('[data-mce-selected="1"]');return n.length>0?ne(n,(t=>e.isEditable(t.parentElement))):fp(e,t)},isForward:m,setNode:t=>(fw(o,e.getOuterHTML(t)),t),getNode:()=>((e,t)=>{if(!t)return e;let n=t.startContainer,o=t.endContainer;const r=t.startOffset,s=t.endOffset;let a=t.commonAncestorContainer;t.collapsed||(n===o&&s-r<2&&n.hasChildNodes()&&(a=n.childNodes[r]),ss(n)&&ss(o)&&(n=n.length===r?bp(n.nextSibling,!0):n.parentNode,o=0===s?bp(o.previousSibling,!1):o.parentNode,n&&n===o&&(a=n)));const i=ss(a)?a.parentNode:a;return Qr(i)?i:e})(o.getBody(),d()),getSel:l,setRng:c,getRng:d,getStart:e=>pp(o.getBody(),d(),e),getEnd:e=>hp(o.getBody(),d(),e),getSelectedBlocks:(t,n)=>((e,t,n,o)=>{const r=[],s=e.getRoot(),a=e.getParent(n||pp(s,t,t.collapsed),e.isBlock),i=e.getParent(o||hp(s,t,t.collapsed),e.isBlock);if(a&&a!==s&&r.push(a),a&&i&&a!==i){let t;const n=new Hr(a,s);for(;(t=n.next())&&t!==i;)e.isBlock(t)&&r.push(t)}return i&&a!==i&&i!==s&&r.push(i),r})(e,d(),t,n),normalize:()=>{const t=d(),n=l();if(!(rf(n).length>1)&&ff(o)){const n=Kg(e,t);return n.each((e=>{c(e,m())})),n.getOr(t)}return t},selectorChanged:(e,t)=>(a(e,t),u),selectorChangedWithUnbind:a,getScrollContainer:()=>{let t,n=e.getRoot();for(;n&&"BODY"!==n.nodeName;){if(n.scrollHeight>n.clientHeight){t=n;break}n=n.parentNode}return t},scrollIntoView:(e,t)=>{C(e)?((e,t,n)=>{(e.inline?dp:mp)(e,t,n)})(o,e,t):up(o,d(),t)},placeCaretAt:(e,t)=>c(Ug(e,t,o.getDoc())),getBoundingClientRect:()=>{const e=d();return e.collapsed?Ul.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:()=>{t=r=s=null,g.destroy()}},f=ag(u),g=Fg(u,o);return u.bookmarkManager=f,u.controlSelection=g,u},pE=(e,t,n)=>{-1===dn.inArray(t,n)&&(e.addAttributeFilter(n,((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.push(n))},hE=(e,t)=>{const n=["data-mce-selected"],o={entity_encoding:"named",remove_trailing_brs:!0,pad_empty_with_br:!1,...e},r=t&&t.dom?t.dom:li.DOM,s=t&&t.schema?t.schema:Pa(o),a=VC(o,s);return((e,t,n)=>{e.addAttributeFilter("data-mce-tabindex",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];o.attr("tabindex",o.attr("data-mce-tabindex")),o.attr(t,null)}})),e.addAttributeFilter("src,href,style",((e,o)=>{const r="data-mce-"+o,s=t.url_converter,a=t.url_converter_scope;let i=e.length;for(;i--;){const t=e[i];let l=t.attr(r);void 0!==l?(t.attr(o,l.length>0?l:null),t.attr(r,null)):(l=t.attr(o),"style"===o?l=n.serializeStyle(n.parseStyle(l),t.name):s&&(l=s.call(a,l,o,t.name)),t.attr(o,l.length>0?l:null))}})),e.addAttributeFilter("class",(e=>{let t=e.length;for(;t--;){const n=e[t];let o=n.attr("class");o&&(o=o.replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),n.attr("class",o.length>0?o:null))}})),e.addAttributeFilter("data-mce-type",((e,t,n)=>{let o=e.length;for(;o--;){const t=e[o];if("bookmark"===t.attr("data-mce-type")&&!n.cleanup){const e=I.from(t.firstChild).exists((e=>{var t;return!zi(null!==(t=e.value)&&void 0!==t?t:"")}));e?t.unwrap():t.remove()}}})),e.addNodeFilter("script,style",((e,n)=>{var o;const r=e=>e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"");let s=e.length;for(;s--;){const a=e[s],i=a.firstChild,l=null!==(o=null==i?void 0:i.value)&&void 0!==o?o:"";if("script"===n){const e=a.attr("type");e&&a.attr("type","mce-no/type"===e?null:e.replace(/^mce\-/,"")),"xhtml"===t.element_format&&i&&l.length>0&&(i.value="// <![CDATA[\n"+r(l)+"\n// ]]>")}else"xhtml"===t.element_format&&i&&l.length>0&&(i.value="\x3c!--\n"+r(l)+"\n--\x3e")}})),e.addNodeFilter("#comment",(e=>{let o=e.length;for(;o--;){const r=e[o],s=r.value;t.preserve_cdata&&0===(null==s?void 0:s.indexOf("[CDATA["))?(r.name="#cdata",r.type=4,r.value=n.decode(s.replace(/^\[CDATA\[|\]\]$/g,""))):0===(null==s?void 0:s.indexOf("mce:protected "))&&(r.name="#text",r.type=3,r.raw=!0,r.value=unescape(s).substr(14))}})),e.addNodeFilter("xml:namespace,input",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];7===o.type?o.remove():1===o.type&&("input"!==t||o.attr("type")||o.attr("type","text"))}})),e.addAttributeFilter("data-mce-type",(t=>{q(t,(t=>{"format-caret"===t.attr("data-mce-type")&&(t.isEmpty(e.schema.getNonEmptyElements())?t.remove():t.unwrap())}))})),e.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-block,data-mce-type,data-mce-resize,data-mce-placeholder",((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.remove_trailing_brs&&((e,t,n)=>{t.addNodeFilter("br",((t,o,r)=>{const s=dn.extend({},n.getBlockElements()),a=n.getNonEmptyElements(),i=n.getWhitespaceElements();s.body=1;const l=e=>e.name in s||Gs(n,e);for(let o=0,d=t.length;o<d;o++){let d=t[o],c=d.parent;if(c&&l(c)&&d===c.lastChild){let t=d.prev;for(;t;){const e=t.name;if("span"!==e||"bookmark"!==t.attr("data-mce-type")){"br"===e&&(d=null);break}t=t.prev}if(d&&(d.remove(),ty(n,a,i,c))){const t=n.getElementRule(c.name);t&&(t.removeEmpty?c.remove():t.paddEmpty&&Jv(e,r,l,c))}}else{let e=d;for(;c&&c.firstChild===e&&c.lastChild===e&&(e=c,!s[c.name]);)c=c.parent;if(e===c){const e=new xp("#text",3);e.value=dt,d.replace(e)}}}}))})(t,e,e.schema)})(a,o,r),{schema:s,addNodeFilter:a.addNodeFilter,addAttributeFilter:a.addAttributeFilter,serialize:(e,n={})=>{const i={format:"html",...n},l=((e,t,n)=>((e,t)=>C(e)&&e.hasEventListeners("PreProcess")&&!t.no_events)(e,n)?((e,t,n)=>{let o;const r=e.dom;let s=t.cloneNode(!0);const a=document.implementation;if(a.createHTMLDocument){const e=a.createHTMLDocument("");dn.each("BODY"===s.nodeName?s.childNodes:[s],(t=>{e.body.appendChild(e.importNode(t,!0))})),s="BODY"!==s.nodeName?e.body.firstChild:e.body,o=r.doc,r.doc=e}return((e,t)=>{e.dispatch("PreProcess",t)})(e,{...n,node:s}),o&&(r.doc=o),s})(e,t,n):t)(t,e,i),d=((e,t,n)=>{const o=ji(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||Mi(mn.fromDom(t))?o:dn.trim(o)})(r,l,i),c=((e,t,n)=>{const o=n.selection?{forced_root_block:!1,...n}:n,r=e.parse(t,o);return(e=>{const t=e=>"br"===(null==e?void 0:e.name),n=e.lastChild;if(t(n)){const e=n.prev;t(e)&&(n.remove(),e.remove())}})(r),r})(a,d,i);return"tree"===i.format?c:((e,t,n,o,r)=>{const s=((e,t,n)=>$p(e,t).serialize(n))(t,n,o);return((e,t,n)=>{if(!t.no_events&&e){const o=((e,t)=>e.dispatch("PostProcess",t))(e,{...t,content:n});return o.content}return n})(e,r,s)})(t,o,s,c,i)},addRules:s.addValidElements,setRules:s.setValidElements,addTempAttr:D(pE,a,n),getTempAttrs:N(n),getNodeFilters:a.getNodeFilters,getAttributeFilters:a.getAttributeFilters,removeNodeFilter:a.removeNodeFilter,removeAttributeFilter:a.removeAttributeFilter}},bE=(e,t)=>{const n=hE(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs,getNodeFilters:n.getNodeFilters,getAttributeFilters:n.getAttributeFilters,removeNodeFilter:n.removeNodeFilter,removeAttributeFilter:n.removeAttributeFilter}},vE=(e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,content:t}))(n,t);XC(e,o).each((t=>{const n=((e,t,n)=>dE(e).editor.setContent(t,n))(e,t.content,t);QC(e,n.html,t)}))},yE=li.DOM,CE=e=>I.from(e).each((e=>e.destroy())),wE=(()=>{const e={};return{add:(t,n)=>{e[t]=n},get:t=>e[t]?e[t]:{icons:{}},has:t=>Se(e,t)}})(),EE=pi.ModelManager,xE=(e,t)=>t.dom[e],SE=(e,t)=>parseInt(Uo(t,e),10),_E=D(xE,"clientWidth"),kE=D(xE,"clientHeight"),NE=D(SE,"margin-top"),AE=D(SE,"margin-left"),RE=e=>{const t=[],n=()=>{const t=e.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,getArgs:e}})()},o=()=>I.from(t[0]),r=()=>{o().each((e=>{e.reposition()}))},s=e=>{J(t,(t=>t===e)).each((e=>{t.splice(e,1)}))},a=(o,a=!0)=>e.removed||!(e=>{return(t=e.inline?e.getBody():e.getContentAreaContainer(),I.from(t).map(mn.fromDom)).map(Lo).getOr(!1);var t})(e)?{}:(a&&e.dispatch("BeforeOpenNotification",{notification:o}),Z(t,(e=>{return t=n().getArgs(e),r=o,!(t.type!==r.type||t.text!==r.text||t.progressBar||t.timeout||r.progressBar||r.timeout);var t,r})).getOrThunk((()=>{e.editorManager.setActive(e);const a=n().open(o,(()=>{s(a)}),(()=>Tg(e)));return(e=>{t.push(e)})(a),r(),e.dispatch("OpenNotification",{notification:{...a}}),a}))),i=N(t);return(e=>{e.on("SkinLoaded",(()=>{const t=fc(e);t&&a({text:t,type:"warning",timeout:0},!1),r()})),e.on("show ResizeEditor ResizeWindow NodeChange ToggleView FullscreenStateChanged",(()=>{requestAnimationFrame(r)})),e.on("remove",(()=>{q(t.slice(),(e=>{n().close(e)}))})),e.on("keydown",(e=>{var t;const n="f12"===(null===(t=e.key)||void 0===t?void 0:t.toLowerCase())||123===e.keyCode;e.altKey&&n&&(e.preventDefault(),o().map((e=>mn.fromDom(e.getEl()))).each((e=>ro(e))))}))})(e),{open:a,close:()=>{o().each((e=>{n().close(e),s(e),r()}))},getNotifications:i}},DE=pi.PluginManager,TE=pi.ThemeManager,OE=e=>{let t=[];const n=()=>{const t=e.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,openUrl:e,alert:e,confirm:e,close:e}})()},o=(e,t)=>(...n)=>t?t.apply(e,n):void 0,r=n=>{(t=>{e.dispatch("CloseWindow",{dialog:t})})(n);const o=me(t,(({instanceApi:e,triggerElement:t})=>e===n?t:I.none()));t=Y(t,(({instanceApi:e})=>e!==n)),0===t.length?e.focus():o.filter(Lo).each(ro)},s=n=>{e.editorManager.setActive(e),gg(e);const o=ao();e.ui.show();const r=n();return((n,o)=>{t.push({instanceApi:n,triggerElement:o}),(t=>{e.dispatch("OpenWindow",{dialog:t})})(n)})(r,o),r},a=e=>{0!==t.length&&e.each((e=>ro(e)))};return e.on("remove",(()=>{q(t,(({instanceApi:e})=>{n().close(e)}))})),{open:(e,t)=>s((()=>n().open(e,t,r))),openUrl:e=>s((()=>n().openUrl(e,r))),alert:(e,t,r)=>{const s=ao(),i=n();i.alert(e,o(r||i,(()=>{a(s),null==t||t()})))},confirm:(e,t,r)=>{const s=ao(),i=n();i.confirm(e,o(r||i,(e=>{a(s),null==t||t(e)})))},close:()=>{I.from(t[t.length-1]).each((({instanceApi:e})=>{n().close(e),r(e)}))}}},BE=(e,t)=>{e.notificationManager.open({type:"error",text:t})},PE=(e,t)=>{e._skinLoaded?BE(e,t):e.on("SkinLoaded",(()=>{BE(e,t)}))},LE=(e,t,n)=>{gd(e,t,{message:n}),console.error(n)},ME=(e,t,n)=>n?`Failed to load ${e}: ${n} from url ${t}`:`Failed to load ${e} url: ${t}`,IE=(e,...t)=>{const n=window.console;n&&(n.error?n.error(e,...t):n.log(e,...t))},FE=new WeakMap,UE=(e,t)=>{const{type:n,message:o}=t;e.notificationManager.open({type:n,text:o})},zE=e=>{const t=(e=>{switch(e){case"error":return console.error;case"info":return console.info;case"warn":return console.warn;default:return console.log}})(e.type);t(e.message)},jE=(e,t)=>{const{console:n,editor:o}=t;C(o)&&(e._skinLoaded?UE(e,o):e.on("SkinLoaded",(()=>{UE(e,o)}))),C(n)&&zE(n)},$E="Read more: https://www.tiny.cloud/docs/tinymce/latest/license-key/",HE="Make sure to provide a valid license key or add license_key: 'gpl' to the init config to agree to the open source license terms.",VE=(e,t)=>{jE(e,{console:{type:"error",message:[`The "${t}" plugin requires a valid TinyMCE license key.`,$E].join(" ")}})},qE="licensekeymanager",WE=e=>{const t=(e=>u(Em(e))?"online":"offline")(e),n=(e=>{var t;const n=null===(t=wm(e))||void 0===t?void 0:t.toLowerCase();return"gpl"===n?"gpl":y(n)?"no_key":"non_gpl"})(e),o=new Set(Pc(e)).has(qE);return"gpl"!==n||"online"===t||o?{type:"use_plugin",onlineStatus:t,licenseKeyType:n,forcePlugin:o}:{type:"use_gpl",onlineStatus:t,licenseKeyType:n,forcePlugin:o}},KE="manager",YE=qE,GE=(()=>{const e=pi();return{load:(t,n)=>{if("use_plugin"===WE(t).type){const o=`plugins/${YE}/plugin${n}.js`;e.load(KE,o).catch((()=>{((e,t)=>{LE(e,"LicenseKeyManagerLoadError",ME("license key manager",t))})(t,o)}))}},add:t=>{e.add(KE,t)},init:t=>{const n=e=>{Object.defineProperty(t,"licenseKeyManager",{value:e,writable:!1,configurable:!1,enumerable:!0})},o=WE(t),r=e.get(KE);if(C(r))n(r(t,e.urls[KE]));else switch(o.type){case"use_gpl":n((e=>({validate:t=>{const{plugin:n}=t,o=u(n);return o&&VE(e,n),Promise.resolve(!o)}}))(t));break;case"use_plugin":(e=>{FE.has(e)||(FE.set(e,!0),e.initialized?(e.removed||e.mode.set("readonly"),e.options.set("disabled",!0)):e.on("init",(()=>{e.removed||e.mode.set("readonly"),e.options.set("disabled",!0)})),e.on("DisabledStateChange",(e=>{const{state:t}=e;t||e.preventDefault()}),!0),e.on("SwitchMode",(t=>{const{mode:n}=t;"readonly"!==n&&e.mode.set("readonly")})))})(t),n((e=>({validate:t=>{const{plugin:n}=t;return u(n)&&VE(e,n),Promise.resolve(!1)}}))(t)),"offline"===o.onlineStatus&&"no_key"===o.licenseKeyType?(e=>{const t="The editor is disabled because a TinyMCE license key has not been provided.";jE(e,{console:{type:"error",message:[`${t}`,HE,$E].join(" ")},editor:{type:"warning",message:`${t}`}})})(t):((e,t)=>{const n=("online"===t?"API":"license")+" key",o=`The editor is disabled because the TinyMCE ${n} could not be validated.`;jE(e,{console:{type:"error",message:[`${o}`,`The TinyMCE Commercial License Key Manager plugin is required for the provided ${n} to be validated but could not be loaded.`,$E].join(" ")},editor:{type:"warning",message:`${o}`}})})(t,o.onlineStatus)}t.licenseKeyManager.validate({})}}})(),XE=(e,t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},QE=(e,t,n)=>{br(e,t)&&!n?hr(e,t):n&&gr(e,t)},ZE=e=>{const t=mn.fromDom(e.getBody());QE(t,"mce-content-readonly",!0),e.selection.controlSelection.hideResizeRect(),e._selectionOverrides.hideFakeCaret(),(e=>{I.from(e.selection.getNode()).each((e=>{e.removeAttribute("data-mce-selected")}))})(e)},JE=e=>{const t=mn.fromDom(e.getBody());QE(t,"mce-content-readonly",!1),e.hasEditableRoot()&&Cr(t,!0),((e,t)=>{XE(e,"StyleWithCSS",t),XE(e,"enableInlineTableEditing",t),XE(e,"enableObjectResizing",t)})(e,!1),Tg(e)&&e.focus(),(e=>{e.selection.setRng(e.selection.getRng())})(e),e.nodeChanged()},ex=e=>xm(e),tx="data-mce-contenteditable",nx=(e,t)=>{const n=mn.fromDom(e.getBody());t?(ZE(e),Cr(n,!1),q(xr(n,'*[contenteditable="true"]'),(e=>{ho(e,tx,"true"),Cr(e,!1)}))):(q(xr(n,`*[${tx}="true"]`),(e=>{wo(e,tx),Cr(e,!0)})),JE(e))},ox=e=>{e.parser.addAttributeFilter("contenteditable",(t=>{ex(e)&&q(t,(e=>{e.attr(tx,e.attr("contenteditable")),e.attr("contenteditable","false")}))})),e.serializer.addAttributeFilter(tx,(t=>{ex(e)&&q(t,(e=>{e.attr("contenteditable",e.attr(tx))}))})),e.serializer.addTempAttr(tx)},rx=["copy"],sx=e=>"content/"+e+"/content.css",ax=(e,t)=>{const n=e.editorManager.baseURL+"/skins/content",o=`content${e.editorManager.suffix}.css`;return V(t,(t=>(e=>tinymce.Resource.has(sx(e)))(t)?t:(e=>/^[a-z0-9\-]+$/i.test(e))(t)&&!e.inline?`${n}/${t}/${o}`:e.documentBaseURI.toAbsolute(t)))},ix=(e,t)=>{const n={};return{findAll:(o,r=M)=>{const s=Y((e=>e?ce(e.getElementsByTagName("img")):[])(o),(t=>{const n=t.src;return!t.hasAttribute("data-mce-bogus")&&!t.hasAttribute("data-mce-placeholder")&&!(!n||n===rn.transparentSrc)&&(Xe(n,"blob:")?!e.isUploaded(n)&&r(t):!!Xe(n,"data:")&&r(t))})),a=V(s,(e=>{const o=e.src;if(Se(n,o))return n[o].then((t=>u(t)?t:{image:e,blobInfo:t.blobInfo}));{const r=((e,t)=>{const n=()=>Promise.reject("Invalid data URI");if(Xe(t,"blob:")){const s=e.getByUri(t);return C(s)?Promise.resolve(s):(o=t,Xe(o,"blob:")?(e=>fetch(e).then((e=>e.ok?e.blob():Promise.reject())).catch((()=>Promise.reject({message:`Cannot convert ${e} to Blob. Resource might not exist or is inaccessible.`,uriType:"blob"}))))(o):Xe(o,"data:")?(r=o,new Promise(((e,t)=>{my(r).bind((({type:e,data:t,base64Encoded:n})=>uy(e,t,n))).fold((()=>t("Invalid data URI")),e)}))):Promise.reject("Unknown URI format")).then((t=>fy(t).then((o=>py(o,!1,(n=>I.some(hy(e,t,n)))).getOrThunk(n)))))}var o,r;return Xe(t,"data:")?by(e,t).fold(n,(e=>Promise.resolve(e))):Promise.reject("Unknown image data format")})(t,o).then((t=>(delete n[o],{image:e,blobInfo:t}))).catch((e=>(delete n[o],e)));return n[o]=r,r}}));return Promise.all(a)}}},lx=()=>{let e={};const t=(e,t)=>({status:e,resultUri:t}),n=t=>t in e;return{hasBlobUri:n,getResultUri:t=>{const n=e[t];return n?n.resultUri:null},isPending:t=>!!n(t)&&1===e[t].status,isUploaded:t=>!!n(t)&&2===e[t].status,markPending:n=>{e[n]=t(1,null)},markUploaded:(n,o)=>{e[n]=t(2,o)},removeFailed:t=>{delete e[t]},destroy:()=>{e={}}}};let dx=0;const cx=(e,t)=>{const n={},o=(e,n)=>new Promise(((o,r)=>{const s=new XMLHttpRequest;s.open("POST",t.url),s.withCredentials=t.credentials,s.upload.onprogress=e=>{n(e.loaded/e.total*100)},s.onerror=()=>{r("Image upload failed due to a XHR Transport error. Code: "+s.status)},s.onload=()=>{if(s.status<200||s.status>=300)return void r("HTTP Error: "+s.status);const e=JSON.parse(s.responseText);var n,a;e&&u(e.location)?o((n=t.basePath,a=e.location,n?n.replace(/\/$/,"")+"/"+a.replace(/^\//,""):a)):r("Invalid JSON: "+s.responseText)};const a=new FormData;a.append("file",e.blob(),e.filename()),s.send(a)})),r=w(t.handler)?t.handler:o,s=(e,t)=>({url:t,blobInfo:e,status:!0}),a=(e,t)=>({url:"",blobInfo:e,status:!1,error:t}),i=(e,t)=>{dn.each(n[e],(e=>{e(t)})),delete n[e]};return{upload:(l,d)=>t.url||r!==o?((t,o)=>(t=dn.grep(t,(t=>!e.isUploaded(t.blobUri()))),Promise.all(dn.map(t,(t=>e.isPending(t.blobUri())?(e=>{const t=e.blobUri();return new Promise((e=>{n[t]=n[t]||[],n[t].push(e)}))})(t):((t,n,o)=>(e.markPending(t.blobUri()),new Promise((r=>{let l,d;try{const c=()=>{l&&(l.close(),d=S)},m=n=>{c(),e.markUploaded(t.blobUri(),n),i(t.blobUri(),s(t,n)),r(s(t,n))},f=n=>{c(),e.removeFailed(t.blobUri()),i(t.blobUri(),a(t,n)),r(a(t,n))};d=e=>{e<0||e>100||I.from(l).orThunk((()=>I.from(o).map(B))).each((t=>{l=t,t.progressBar.value(e)}))},n(t,d).then(m,(e=>{f(u(e)?{message:e}:e)}))}catch(e){r(a(t,e))}}))))(t,r,o))))))(l,d):new Promise((e=>{e([])}))}},mx=e=>()=>e.notificationManager.open({text:e.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0}),ux=(e,t)=>cx(t,{url:Kd(e),basePath:Yd(e),credentials:Gd(e),handler:Xd(e)}),fx=e=>{const t=(()=>{let e=[];const t=e=>{if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");const t=e.id||"blobid"+dx+++(()=>{const e=()=>Math.round(4294967295*Oe()).toString(36);return"s"+(new Date).getTime().toString(36)+e()+e()+e()})(),n=e.name||t,o=e.blob;var r;return{id:N(t),name:N(n),filename:N(e.filename||n+"."+(r=o.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"}[r.toLowerCase()]||"dat")),blob:N(o),base64:N(e.base64),blobUri:N(e.blobUri||URL.createObjectURL(o)),uri:N(e.uri)}},n=t=>Z(e,t).getOrUndefined(),o=e=>n((t=>t.id()===e));return{create:(e,n,o,r,s)=>{if(u(e))return t({id:e,name:r,filename:s,blob:n,base64:o});if(f(e))return t(e);throw new Error("Unknown input type")},add:t=>{o(t.id())||e.push(t)},get:o,getByUri:e=>n((t=>t.blobUri()===e)),getByData:(e,t)=>n((n=>n.base64()===e&&n.blob().type===t)),findFirst:n,removeByUri:t=>{e=Y(e,(e=>e.blobUri()!==t||(URL.revokeObjectURL(e.blobUri()),!1)))},destroy:()=>{q(e,(e=>{URL.revokeObjectURL(e.blobUri())})),e=[]}}})();let n,o;const r=lx(),s=[],a=t=>n=>e.selection?t(n):[],i=(e,t,n)=>{let o=0;do{o=e.indexOf(t,o),-1!==o&&(e=e.substring(0,o)+n+e.substr(o+t.length),o+=n.length-t.length+1)}while(-1!==o);return e},l=(e,t,n)=>{const o=`src="${n}"${n===rn.transparentSrc?' data-mce-placeholder="1"':""}`;return e=i(e,`src="${t}"`,o),i(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},d=(t,n)=>{q(e.undoManager.data,(e=>{"fragmented"===e.type?e.fragments=V(e.fragments,(e=>l(e,t,n))):e.content=l(e.content,t,n)}))},c=()=>(n||(n=ux(e,r)),p().then(a((o=>{const r=V(o,(e=>e.blobInfo));return n.upload(r,mx(e)).then(a((n=>{const r=[];let s=!1;const a=V(n,((n,a)=>{const{blobInfo:i,image:l}=o[a];let c=!1;return n.status&&Vd(e)?(n.url&&!Ge(l.src,n.url)&&(s=!0),t.removeByUri(l.src),lE(e)||((t,n)=>{const o=e.convertURL(n,"src");var r;d(t.src,n),bo(mn.fromDom(t),{src:Hd(e)?(r=n,r+(-1===r.indexOf("?")?"?":"&")+(new Date).getTime()):n,"data-mce-src":o})})(l,n.url)):n.error&&(n.error.remove&&(d(l.src,rn.transparentSrc),r.push(l),c=!0),((e,t)=>{PE(e,gi.translate(["Failed to upload image: {0}",t]))})(e,n.error.message)),{element:l,status:n.status,uploadUri:n.url,blobInfo:i,removed:c}}));return r.length>0&&!lE(e)?e.undoManager.transact((()=>{q(To(r),(n=>{const o=Ln(n);_o(n),o.each((e=>t=>{((e,t)=>e.dom.isEmpty(t.dom)&&C(e.schema.getTextBlockElements()[En(t)]))(e,t)&&mo(t,mn.fromHtml('<br data-mce-bogus="1" />'))})(e)),t.removeByUri(n.dom.src)}))})):s&&e.undoManager.dispatchChange(),a})))})))),m=()=>$d(e)?c():Promise.resolve([]),g=e=>ne(s,(t=>t(e))),p=()=>(o||(o=ix(r,t)),o.findAll(e.getBody(),g).then(a((t=>{const n=Y(t,(t=>u(t)?(PE(e,t),!1):"blob"!==t.uriType));return lE(e)||q(n,(e=>{d(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")})),n})))),h=n=>n.replace(/src="(blob:[^"]+)"/g,((n,o)=>{const s=r.getResultUri(o);if(s)return'src="'+s+'"';let a=t.getByUri(o);return a||(a=X(e.editorManager.get(),((e,t)=>e||t.editorUpload&&t.editorUpload.blobCache.getByUri(o)),void 0)),a?'src="data:'+a.blob().type+";base64,"+a.base64()+'"':n}));return e.on("SetContent",(()=>{$d(e)?m():p()})),e.on("RawSaveContent",(e=>{e.content=h(e.content)})),e.on("GetContent",(e=>{e.source_view||"raw"===e.format||"tree"===e.format||(e.content=h(e.content))})),e.on("PostRender",(()=>{e.parser.addNodeFilter("img",(e=>{q(e,(e=>{const n=e.attr("src");if(!n||t.getByUri(n))return;const o=r.getResultUri(n);o&&e.attr("src",o)}))}))})),{blobCache:t,addFilter:e=>{s.push(e)},uploadImages:c,uploadImagesAuto:m,scanForImages:p,destroy:()=>{t.destroy(),r.destroy(),o=n=null}}},gx={remove_similar:!0,inherit:!1},px={selector:"td,th",...gx},hx={tablecellbackgroundcolor:{styles:{backgroundColor:"%value"},...px},tablecellverticalalign:{styles:{"vertical-align":"%value"},...px},tablecellbordercolor:{styles:{borderColor:"%value"},...px},tablecellclass:{classes:["%value"],...px},tableclass:{selector:"table",classes:["%value"],...gx},tablecellborderstyle:{styles:{borderStyle:"%value"},...px},tablecellborderwidth:{styles:{borderWidth:"%value"},...px}},bx=N(hx),vx=dn.each,yx=li.DOM,Cx=e=>C(e)&&f(e),wx=(e,t)=>{const n=t&&t.schema||Pa({}),o=e=>{const t=u(e)?{name:e,classes:[],attrs:{}}:e,n=yx.create(t.name);return((e,t)=>{t.classes.length>0&&yx.addClass(e,t.classes.join(" ")),yx.setAttribs(e,t.attrs)})(n,t),n},r=(e,t,s)=>{let a;const i=t[0],l=Cx(i)?i.name:void 0,d=((e,t)=>{const o=n.getElementRule(e.nodeName.toLowerCase()),r=null==o?void 0:o.parentsRequired;return!(!r||!r.length)&&(t&&$(r,t)?t:r[0])})(e,l);if(d)l===d?(a=i,t=t.slice(1)):a=d;else if(i)a=i,t=t.slice(1);else if(!s)return e;const c=a?o(a):yx.create("div");c.appendChild(e),s&&dn.each(s,(t=>{const n=o(t);c.insertBefore(n,e)}));const m=Cx(a)?a.siblings:void 0;return r(c,t,m)},s=yx.create("div");if(e.length>0){const t=e[0],n=o(t),a=Cx(t)?t.siblings:void 0;s.appendChild(r(n,e.slice(1),a))}return s},Ex=e=>{let t="div";const n={name:t,classes:[],attrs:{},selector:e=dn.trim(e)};return"*"!==e&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,((e,t,o,r,s)=>{switch(t){case"#":n.attrs.id=o;break;case".":n.classes.push(o);break;case":":-1!==dn.inArray("checked disabled enabled read-only required".split(" "),o)&&(n.attrs[o]=o)}if("["===r){const e=s.match(/([\w\-]+)(?:\=\"([^\"]+))?/);e&&(n.attrs[e[1]]=e[2])}return""}))),n.name=t||"div",n},xx=(e,t)=>{let n="",o=Cc(e);if(""===o)return"";const r=e=>u(e)?e.replace(/%(\w+)/g,""):"",s=(t,n)=>yx.getStyle(null!=n?n:e.getBody(),t,!0);if(u(t)){const n=e.formatter.get(t);if(!n)return"";t=n[0]}if("preview"in t){const e=t.preview;if(!1===e)return"";o=e||o}let a,i=t.block||t.inline||"span";const l=(d=t.selector,u(d)?(d=(d=d.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),dn.map(d.split(/(?:>|\s+(?![^\[\]]+\]))/),(e=>{const t=dn.map(e.split(/(?:~\+|~|\+)/),Ex),n=t.pop();return t.length&&(n.siblings=t),n})).reverse()):[]);var d;l.length>0?(l[0].name||(l[0].name=i),i=t.selector,a=wx(l,e)):a=wx([i],e);const c=yx.select(i,a)[0]||a.firstChild;vx(t.styles,((e,t)=>{const n=r(e);n&&yx.setStyle(c,t,n)})),vx(t.attributes,((e,t)=>{const n=r(e);n&&yx.setAttrib(c,t,n)})),vx(t.classes,(e=>{const t=r(e);yx.hasClass(c,t)||yx.addClass(c,t)})),e.dispatch("PreviewFormats"),yx.setStyles(a,{position:"absolute",left:-65535}),e.getBody().appendChild(a);const m=s("fontSize"),f=/px$/.test(m)?parseInt(m,10):0;return vx(o.split(" "),(e=>{let t=s(e,c);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=s(e),"#ffffff"===$a(t).toLowerCase())||"color"===e&&"#000000"===$a(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===f)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*f+"px"}"border"===e&&t&&(n+="padding:0 2px;"),n+=e+":"+t+";"}})),e.dispatch("AfterPreviewFormats"),yx.remove(a),n},Sx=e=>{const t=(e=>{const t={},n=(e,o)=>{e&&(u(e)?(p(o)||(o=[o]),q(o,(e=>{v(e.deep)&&(e.deep=!Pf(e)),v(e.split)&&(e.split=!Pf(e)||Lf(e)),v(e.remove)&&Pf(e)&&!Lf(e)&&(e.remove="none"),Pf(e)&&Lf(e)&&(e.mixed=!0,e.block_expand=!0),u(e.classes)&&(e.classes=e.classes.split(/\s+/))})),t[e]=o):pe(e,((e,t)=>{n(t,e)})))};return n((e=>{const t=e.dom,n=e.schema.type,o={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"left"},inherit:!1,preview:!1},{selector:"img,audio,video",collapsed:!1,styles:{float:"left"},preview:"font-family font-size"},{selector:".mce-placeholder",styles:{float:"left"},ceFalseOverride:!0},{selector:"table",collapsed:!1,styles:{marginLeft:"0px",marginRight:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"left"}}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:".mce-placeholder",styles:{display:"block",marginLeft:"auto",marginRight:"auto"},ceFalseOverride:!0},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"},{selector:".mce-preview-object",ceFalseOverride:!0,styles:{display:"table",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{marginLeft:"auto",marginRight:"auto"},preview:!1}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{float:"right"},preview:"font-family font-size"},{selector:".mce-placeholder",styles:{float:"right"},ceFalseOverride:!0},{selector:"table",collapsed:!1,styles:{marginRight:"0px",marginLeft:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"right"},preview:!1}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"justify"},inherit:!1,preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all",preserve_attributes:["class","style"]}],italic:[{inline:"em",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all",preserve_attributes:["class","style"]}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all",preserve_attributes:["class","style"]}],strikethrough:(()=>{const e={inline:"span",styles:{textDecoration:"line-through"},exact:!0},t={inline:"strike",remove:"all",preserve_attributes:["class","style"]},o={inline:"s",remove:"all",preserve_attributes:["class","style"]};return"html4"!==n?[o,e,t]:[e,o,t]})(),forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},lineheight:{selector:"h1,h2,h3,h4,h5,h6,p,li,td,th,div",styles:{lineHeight:"%value"}},fontsize_class:{inline:"span",attributes:{class:"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},samp:{inline:"samp"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:(e,t,n)=>Xr(e)&&e.hasAttribute("href"),onformat:(e,n,o)=>{dn.each(o,((n,o)=>{t.setAttrib(e,o,n)}))}},lang:{inline:"span",clear_child_styles:!0,remove_similar:!0,attributes:{lang:"%value","data-mce-lang":e=>{var t;return null!==(t=null==e?void 0:e.customValue)&&void 0!==t?t:null}}},removeformat:[{selector:"b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return dn.each("p h1 h2 h3 h4 h5 h6 div address pre dt dd".split(/\s/),(e=>{o[e]={block:e,remove:"all"}})),o})(e)),n(bx()),n(yc(e)),{get:e=>C(e)?t[e]:t,has:e=>Se(t,e),register:n,unregister:e=>(e&&t[e]&&delete t[e],t)}})(e),n=Ne({});return(e=>{e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(let t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])})(e),(e=>{e.on("mouseup keydown",(t=>{var n;((e,t,n)=>{const o=e.selection,r=e.getBody();rv(e,null,n),8!==t&&46!==t||!o.isCollapsed()||o.getStart().innerHTML!==ev||rv(e,ju(r,o.getStart()),!0),37!==t&&39!==t||rv(e,ju(r,o.getStart()),!0)})(e,t.keyCode,(n=e.selection.getRng().endContainer,ss(n)&&Qe(n.data,dt)))}))})(e),lE(e)||((e,t)=>{e.set({}),t.on("NodeChange",(n=>{Pw(t,n.element,e.get())})),t.on("FormatApply FormatRemove",(n=>{const o=I.from(n.node).map((e=>bf(e)?e:e.startContainer)).bind((e=>Xr(e)?I.some(e):I.from(e.parentElement))).getOrThunk((()=>Tw(t)));Pw(t,o,e.get())}))})(n,e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:(t,n,o)=>{((e,t,n,o)=>{cE(e).formatter.apply(t,n,o)})(e,t,n,o)},remove:(t,n,o,r)=>{((e,t,n,o,r)=>{cE(e).formatter.remove(t,n,o,r)})(e,t,n,o,r)},toggle:(t,n,o)=>{((e,t,n,o)=>{cE(e).formatter.toggle(t,n,o)})(e,t,n,o)},match:(t,n,o,r)=>((e,t,n,o,r)=>cE(e).formatter.match(t,n,o,r))(e,t,n,o,r),closest:t=>((e,t)=>cE(e).formatter.closest(t))(e,t),matchAll:(t,n)=>((e,t,n)=>cE(e).formatter.matchAll(t,n))(e,t,n),matchNode:(t,n,o,r)=>((e,t,n,o,r)=>cE(e).formatter.matchNode(t,n,o,r))(e,t,n,o,r),canApply:t=>((e,t)=>cE(e).formatter.canApply(t))(e,t),formatChanged:(t,o,r,s)=>((e,t,n,o,r,s)=>cE(e).formatter.formatChanged(t,n,o,r,s))(e,n,t,o,r,s),getCssText:D(xx,e)}},_x=e=>{switch(e.toLowerCase()){case"undo":case"redo":case"mcefocus":return!0;default:return!1}},kx=e=>{const t=We(),n=Ne(0),o=Ne(0),r={data:[],typing:!1,beforeChange:()=>{((e,t,n)=>{cE(e).undoManager.beforeChange(t,n)})(e,n,t)},add:(s,a)=>((e,t,n,o,r,s,a)=>cE(e).undoManager.add(t,n,o,r,s,a))(e,r,o,n,t,s,a),dispatchChange:()=>{e.setDirty(!0);const t=Jw(e);t.bookmark=ad(e.selection),e.dispatch("change",{level:t,lastLevel:ie(r.data,o.get()).getOrUndefined()})},undo:()=>((e,t,n,o)=>cE(e).undoManager.undo(t,n,o))(e,r,n,o),redo:()=>((e,t,n)=>cE(e).undoManager.redo(t,n))(e,o,r.data),clear:()=>{((e,t,n)=>{cE(e).undoManager.clear(t,n)})(e,r,o)},reset:()=>{((e,t)=>{cE(e).undoManager.reset(t)})(e,r)},hasUndo:()=>((e,t,n)=>cE(e).undoManager.hasUndo(t,n))(e,r,o),hasRedo:()=>((e,t,n)=>cE(e).undoManager.hasRedo(t,n))(e,r,o),transact:t=>((e,t,n,o)=>cE(e).undoManager.transact(t,n,o))(e,r,n,t),ignore:t=>{((e,t,n)=>{cE(e).undoManager.ignore(t,n)})(e,n,t)},extra:(t,n)=>{((e,t,n,o,r)=>{cE(e).undoManager.extra(t,n,o,r)})(e,r,o,t,n)}};return lE(e)||((e,t,n)=>{const o=Ne(!1),r=e=>{sE(t,!1,n),t.add({},e)};e.on("init",(()=>{t.add()})),e.on("BeforeExecCommand",(e=>{const o=e.command;_x(o)||(aE(t,n),t.beforeChange())})),e.on("ExecCommand",(e=>{const t=e.command;_x(t)||r(e)})),e.on("ObjectResizeStart cut",(()=>{t.beforeChange()})),e.on("SaveContent ObjectResized blur",r),e.on("dragend",r),e.on("keyup",(n=>{const s=n.keyCode;if(n.isDefaultPrevented())return;const a=rn.os.isMacOS()&&"Meta"===n.key;(s>=33&&s<=36||s>=37&&s<=40||45===s||n.ctrlKey||a)&&(r(),e.nodeChanged()),46!==s&&8!==s||e.nodeChanged(),o.get()&&t.typing&&!oE(Jw(e),t.data[0])&&(e.isDirty()||e.setDirty(!0),e.dispatch("TypingUndo"),o.set(!1),e.nodeChanged())})),e.on("keydown",(e=>{const s=e.keyCode;if(e.isDefaultPrevented())return;if(s>=33&&s<=36||s>=37&&s<=40||45===s)return void(t.typing&&r(e));const a=e.ctrlKey&&!e.altKey||e.metaKey;if((s<16||s>20)&&224!==s&&91!==s&&!t.typing&&!a)return t.beforeChange(),sE(t,!0,n),t.add({},e),void o.set(!0);(rn.os.isMacOS()?e.metaKey:e.ctrlKey&&!e.altKey)&&t.beforeChange()})),e.on("mousedown",(e=>{t.typing&&r(e)})),e.on("input",(e=>{var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data||(e=>"insertFromPaste"===e.inputType||"insertFromDrop"===e.inputType)(e))&&r(e)})),e.on("AddUndo Undo Redo ClearUndos",(t=>{t.isDefaultPrevented()||e.nodeChanged()}))})(e,r,n),(e=>{e.addShortcut("meta+z","","Undo"),e.addShortcut("meta+y,meta+shift+z","","Redo")})(e),r},Nx=[9,27,Bg.HOME,Bg.END,19,20,44,144,145,33,34,45,16,17,18,91,92,93,Bg.DOWN,Bg.UP,Bg.LEFT,Bg.RIGHT].concat(rn.browser.isFirefox()?[224]:[]),Ax="data-mce-placeholder",Rx=e=>"keydown"===e.type||"keyup"===e.type,Dx=e=>{const t=e.keyCode;return t===Bg.BACKSPACE||t===Bg.DELETE},Tx=e=>t=>C(t)&&e.test(t.nodeName),Ox=e=>C(e)&&3===e.nodeType,Bx=e=>C(e)&&1===e.nodeType,Px=Tx(/^(OL|UL|DL)$/),Lx=Tx(/^(OL|UL)$/),Mx=Tx(/^(LI|DT|DD)$/),Ix=Tx(/^(DT|DD)$/),Fx=e=>C(e)&&"br"===e.nodeName.toLowerCase(),Ux=(e,t)=>C(t)&&t.nodeName in e.schema.getTextBlockElements(),zx=(e,t)=>C(e)&&e.nodeName in t,jx=(e,t)=>C(t)&&t.nodeName in e.schema.getVoidElements(),$x=(e,t,n)=>{const o=e.isEmpty(t);return!(n&&e.select("span[data-mce-type=bookmark]",t).length>0)&&o},Hx=(e,t)=>e.isChildOf(t,e.getRoot()),Vx=(e,t)=>{if(Ox(e))return{container:e,offset:t};const n=Xg.getNode(e,t);return Ox(n)?{container:n,offset:t>=e.childNodes.length?n.data.length:0}:n.previousSibling&&Ox(n.previousSibling)?{container:n.previousSibling,offset:n.previousSibling.data.length}:n.nextSibling&&Ox(n.nextSibling)?{container:n.nextSibling,offset:0}:{container:e,offset:t}},qx=e=>{const t=e.cloneRange(),n=Vx(e.startContainer,e.startOffset);t.setStart(n.container,n.offset);const o=Vx(e.endContainer,e.endOffset);return t.setEnd(o.container,o.offset),t},Wx=li.DOM,Kx=e=>{const t={},n=n=>{let o=e[n?"startContainer":"endContainer"],r=e[n?"startOffset":"endOffset"];if(Bx(o)){const e=Wx.create("span",{"data-mce-type":"bookmark"});o.hasChildNodes()?(r=Math.min(r,o.childNodes.length-1),n?o.insertBefore(e,o.childNodes[r]):Wx.insertAfter(e,o.childNodes[r])):o.appendChild(e),o=e,r=0}t[n?"startContainer":"endContainer"]=o,t[n?"startOffset":"endOffset"]=r};return n(!0),e.collapsed||n(),t},Yx=e=>{const t=t=>{let n=e[t?"startContainer":"endContainer"],o=e[t?"startOffset":"endOffset"];if(n){if(Bx(n)&&n.parentNode){const e=n;o=(e=>{var t;let n=null===(t=e.parentNode)||void 0===t?void 0:t.firstChild,o=0;for(;n;){if(n===e)return o;Bx(n)&&"bookmark"===n.getAttribute("data-mce-type")||o++,n=n.nextSibling}return-1})(n),n=n.parentNode,Wx.remove(e),!n.hasChildNodes()&&Wx.isBlock(n)&&n.appendChild(Wx.create("br"))}e[t?"startContainer":"endContainer"]=n,e[t?"startOffset":"endOffset"]=o}};t(!0),t();const n=Wx.createRng();return n.setStart(e.startContainer,e.startOffset),e.endContainer&&n.setEnd(e.endContainer,e.endOffset),qx(n)},Gx=li.DOM,Xx=(e,t)=>{const n=dn.grep(e.select("ol,ul",t));dn.each(n,(t=>{((e,t)=>{const n=t.parentElement;if(n&&"LI"===n.nodeName&&n.firstChild===t){const o=n.previousSibling;o&&"LI"===o.nodeName?(o.appendChild(t),$x(e,n)&&Gx.remove(n)):Gx.setStyle(n,"listStyleType","none")}if(Px(n)){const e=n.previousSibling;e&&"LI"===e.nodeName&&e.appendChild(t)}})(e,t)}))},Qx=["OL","UL","DL"],Zx=Qx.join(","),Jx=(e,t)=>{const n=t||e.selection.getStart(!0);return e.dom.getParent(n,Zx,nS(e,n))},eS=e=>{const t=e.selection.getSelectedBlocks();return Y(((e,t)=>{const n=dn.map(t,(t=>e.dom.getParent(t,"li,dd,dt",nS(e,t))||t));return ue(n)})(e,t),Mx)},tS=(e,t)=>{const n=e.dom.getParents(t,"TD,TH");return n.length>0?n[0]:e.getBody()},nS=(e,t)=>{const n=e.dom.getParents(t,e.dom.isBlock),o=Z(n,(t=>{return(t=>t.nodeName.toLowerCase()!==Ld(e))(t)&&(n=e.schema,!Px(o=t)&&!Mx(o)&&H(Qx,(e=>n.isValidChild(o.nodeName,e))));var n,o}));return o.getOr(e.getBody())},oS=(e,t)=>{const n=e.dom.getParents(t,"ol,ul",nS(e,t));return de(n)},rS=(e,t)=>{const n=V(t,(t=>oS(e,t).getOr(t)));return ue(n)},sS=e=>/\btox\-/.test(e.className),aS=(e,t)=>null!==t&&!e.dom.isEditable(t),iS=(e,t)=>{const n=e.dom.getParent(t,"ol,ul,dl");return aS(e,n)||!e.selection.isEditable()},lS=(e,t,n)=>e.dispatch("ListMutation",{action:t,element:n}),dS=(e,t,n={})=>{const o=e.dom,r=e.schema.getBlockElements(),s=o.createFragment(),a=Ld(e),i=Md(e);let l,d,c=!1;for(d=o.create(a,{...i,...n.style?{style:n.style}:{}}),zx(t.firstChild,r)||s.appendChild(d);l=t.firstChild;){const e=l.nodeName;c||"SPAN"===e&&"bookmark"===l.getAttribute("data-mce-type")||(c=!0),zx(l,r)?(s.appendChild(l),d=null):(d||(d=o.create(a,i),s.appendChild(d)),d.appendChild(l))}return!c&&d&&d.appendChild(o.create("br",{"data-mce-bogus":"1"})),s},cS=e=>Cn(e,"OL,UL"),mS=e=>qn(e).exists(cS),uS=e=>"listAttributes"in e,fS=e=>"isComment"in e,gS=e=>e.depth>0,pS=e=>e.isSelected,hS=e=>{const t=Hn(e),n=Wn(e).exists(cS)?t.slice(0,-1):t;return V(n,Ro)},bS=(e,t)=>{mo(e.item,t.list)},vS=(e,t)=>{const n={list:mn.fromTag(t,e),item:mn.fromTag("li",e)};return mo(n.list,n.item),n},yS=(e,t,n)=>{const o=t.slice(0,n.depth);return de(o).each((t=>{if(uS(n)){const o=((e,t,n)=>{const o=mn.fromTag("li",e);return bo(o,t),go(o,n),o})(e,n.itemAttributes,n.content);((e,t)=>{mo(e.list,t),e.item=t})(t,o),((e,t)=>{En(e.list)!==t.listType&&(e.list=Do(e.list,t.listType)),bo(e.list,t.listAttributes)})(t,n)}else if((e=>"isFragment"in e)(n))go(t.item,n.content);else{const e=mn.fromHtml(`\x3c!--${n.content}--\x3e`);mo(t.list,e)}})),o},CS=e=>(q(e,((t,n)=>{((e,t)=>{const n=e[t].depth,o=e=>e.depth===n&&!e.dirty,r=e=>e.depth<n;return Q(oe(e.slice(0,t)),o,r).orThunk((()=>Q(e.slice(t+1),o,r)))})(e,n).fold((()=>{t.dirty&&uS(t)&&(e=>{e.listAttributes=Ce(e.listAttributes,((e,t)=>"start"!==t))})(t)}),(e=>{return o=e,void(uS(n=t)&&uS(o)&&(n.listType=o.listType,n.listAttributes={...o.listAttributes}));var n,o}))})),e),wS=(e,t,n,o)=>{var r;if(_n(o))return[{depth:e+1,content:null!==(r=o.dom.nodeValue)&&void 0!==r?r:"",dirty:!1,isSelected:!1,isComment:!0}];t.each((e=>{vn(e.start,o)&&n.set(!0)}));const s=((e,t,n)=>Ln(e).filter(Nn).map((o=>({depth:t,dirty:!1,isSelected:n,content:hS(e),itemAttributes:xo(e),listAttributes:xo(o),listType:En(o),isInPreviousLi:!1}))))(o,e,n.get());t.each((e=>{vn(e.end,o)&&n.set(!1)}));const a=Wn(o).filter(cS).map((o=>xS(e,t,n,o))).getOr([]);return s.toArray().concat(a)},ES=(e,t,n,o)=>qn(o).filter(cS).fold((()=>wS(e,t,n,o)),(r=>{const s=X(Hn(o),((o,s,a)=>{if(0===a)return o;if(Cn(s,"LI"))return o.concat(wS(e,t,n,s));{const t={isFragment:!0,depth:e,content:[s],isSelected:!1,dirty:!1,parentListType:En(r)};return o.concat(t)}}),[]);return xS(e,t,n,r).concat(s)})),xS=(e,t,n,o)=>te(Hn(o),(o=>(cS(o)?xS:ES)(e+1,t,n,o))),SS=(e,t)=>{const n=CS(t);return((e,t)=>{let n=I.none();const o=X(t,((t,o,r)=>fS(o)?0===r?(n=I.some(o),t):yS(e,t,o):o.depth>t.length?((e,t,n)=>{const o=((e,t,n)=>{const o=[];for(let r=0;r<n;r++)o.push(vS(e,uS(t)?t.listType:t.parentListType));return o})(e,n,n.depth-t.length);var r;return(e=>{for(let t=1;t<e.length;t++)bS(e[t-1],e[t])})(o),((e,t)=>{for(let t=0;t<e.length-1;t++)Io(e[t].item,"list-style-type","none");de(e).each((e=>{uS(t)&&(bo(e.list,t.listAttributes),bo(e.item,t.itemAttributes)),go(e.item,t.content)}))})(o,n),r=o,je(de(t),le(r),bS),t.concat(o)})(e,t,o):yS(e,t,o)),[]);return n.each((e=>{const t=mn.fromHtml(`\x3c!--${e.content}--\x3e`);le(o).each((e=>{co(e.list,t)}))})),le(o).map((e=>e.list))})(e.contentDocument,n).toArray()},_S=(e,t,n)=>{const o=((e,t)=>{const n=Ne(!1);return V(e,(e=>({sourceList:e,entries:xS(0,t,n,e)})))})(t,(e=>{const t=V(eS(e),mn.fromDom);return je(Z(t,T(mS)),Z(oe(t),T(mS)),((e,t)=>({start:e,end:t})))})(e));q(o,(t=>{((e,t,n)=>{q(Y(t,pS),(t=>((e,t,n)=>{switch(t){case"Indent":if(!((e,t)=>Nm(e).map((e=>e>=t)).getOr(!0))(e,n.depth))return;n.depth++;break;case"Outdent":n.depth--;break;case"Flatten":n.depth=0}n.dirty=!0})(e,n,t)))})(e,t.entries,n);const o=((e,t)=>te(((e,t)=>{if(0===e.length)return[];{let n=t(e[0]);const o=[];let r=[];for(let s=0,a=e.length;s<a;s++){const a=e[s],i=t(a);i!==n&&(o.push(r),r=[]),n=i,r.push(a)}return 0!==r.length&&o.push(r),o}})(t,gS),(t=>le(t).exists(gS)?SS(e,t):((e,t)=>{const n=CS(t);return V(n,(t=>{const n=fS(t)?Zo([mn.fromHtml(`\x3c!--${t.content}--\x3e`)]):Zo(t.content),o=uS(t)?t.itemAttributes:{};return mn.fromDom(dS(e,n.dom,o))}))})(e,t))))(e,t.entries);var r;q(o,(t=>{lS(e,"Indent"===n?"IndentList":"OutdentList",t.dom)})),r=t.sourceList,q(o,(e=>{io(r,e)})),_o(t.sourceList)}))},kS=li.DOM,NS=Tn("dd"),AS=Tn("dt"),RS=e=>{AS(e)&&Do(e,"dd")},DS=(e,t,n)=>{q(n,"Indent"===t?RS:t=>((e,t)=>{NS(t)?Do(t,"dt"):AS(t)&&Mn(t).each((n=>((e,t,n)=>{const o=kS.select('span[data-mce-type="bookmark"]',t),r=dS(e,n),s=kS.createRng();s.setStartAfter(n),s.setEndAfter(t);const a=s.extractContents();for(let t=a.firstChild;t;t=t.firstChild)if("LI"===t.nodeName&&e.dom.isEmpty(t)){kS.remove(t);break}e.dom.isEmpty(a)||kS.insertAfter(a,t),kS.insertAfter(r,t);const i=n.parentElement;i&&$x(e.dom,i)&&(e=>{const t=e.parentNode;t&&dn.each(o,(e=>{t.insertBefore(e,n.parentNode)})),kS.remove(e)})(i),kS.remove(n),$x(e.dom,t)&&kS.remove(t)})(e,n.dom,t.dom)))})(e,t))},TS=(e,t)=>{const n=To((e=>{const t=(e=>{const t=oS(e,e.selection.getStart()),n=Y(e.selection.getSelectedBlocks(),Lx);return t.toArray().concat(n)})(e),n=(e=>{const t=e.selection.getStart();return e.dom.getParents(t,"ol,ul",nS(e,t))})(e);return Z(n,(e=>{return t=mn.fromDom(e),Ln(t).exists((e=>Mx(e.dom)&&qn(e).exists((e=>!Px(e.dom)))&&Wn(e).exists((e=>!Px(e.dom)))));var t})).fold((()=>rS(e,t)),(e=>[e]))})(e)),o=To((e=>Y(eS(e),Ix))(e));let r=!1;if(n.length||o.length){const s=e.selection.getBookmark();_S(e,n,t),DS(e,t,o),e.selection.moveToBookmark(s),e.selection.setRng(qx(e.selection.getRng())),e.nodeChanged(),r=!0}return r},OS=(e,t)=>!(e=>{const t=Jx(e);return aS(e,t)||!e.selection.isEditable()})(e)&&TS(e,t),BS=e=>OS(e,"Indent"),PS=e=>OS(e,"Outdent"),LS=e=>OS(e,"Flatten"),MS=e=>{switch(e){case"UL":return"ToggleUlList";case"OL":return"ToggleOlList";case"DL":return"ToggleDLList"}},IS=(e,t)=>{dn.each(t,((t,n)=>{e.setAttribute(n,t)}))},FS=(e,t,n)=>{((e,t,n)=>{const o=n["list-style-type"]?n["list-style-type"]:null;e.setStyle(t,"list-style-type",o)})(e,t,n),((e,t,n)=>{IS(t,n["list-attributes"]),dn.each(e.select("li",t),(e=>{IS(e,n["list-item-attributes"])}))})(e,t,n)},US=(e,t)=>C(t)&&!zx(t,e.schema.getBlockElements()),zS=(e,t,n,o)=>{let r=t[n?"startContainer":"endContainer"];const s=t[n?"startOffset":"endOffset"];Bx(r)&&(r=r.childNodes[Math.min(s,r.childNodes.length-1)]||r),!n&&Fx(r.nextSibling)&&(r=r.nextSibling);const a=(t,n)=>{var r;const s=new Hr(t,(t=>{for(;!e.dom.isBlock(t)&&t.parentNode&&o!==t;)t=t.parentNode;return t})(t)),a=n?"next":"prev";let i;for(;i=s[a]();)if(!jx(e,i)&&!ct(i.textContent)&&0!==(null===(r=i.textContent)||void 0===r?void 0:r.length))return I.some(i);return I.none()};if(n&&Ox(r))if(ct(r.textContent))r=a(r,!1).getOr(r);else for(null!==r.parentNode&&US(e,r.parentNode)&&(r=r.parentNode);null!==r.previousSibling&&(US(e,r.previousSibling)||Ox(r.previousSibling));)r=r.previousSibling;if(!n&&Ox(r))if(ct(r.textContent))r=a(r,!0).getOr(r);else for(null!==r.parentNode&&US(e,r.parentNode)&&(r=r.parentNode);null!==r.nextSibling&&(US(e,r.nextSibling)||Ox(r.nextSibling));)r=r.nextSibling;for(;r.parentNode!==o;){const t=r.parentNode;if(Ux(e,r))return r;if(/^(TD|TH)$/.test(t.nodeName))return r;r=t}return r},jS=(e,t,n)=>{const o=e.selection.getRng();let r="LI";const s=nS(e,((e,t)=>{const n=e.selection.getStart(!0),o=zS(e,t,!0,e.getBody());return r=mn.fromDom(o),s=mn.fromDom(t.commonAncestorContainer),Sr(r,D(vn,s))?t.commonAncestorContainer:n;var r,s})(e,o)),a=e.dom;if("false"===a.getContentEditable(e.selection.getNode()))return;"DL"===(t=t.toUpperCase())&&(r="DT");const i=Kx(o),l=Y(((e,t,n)=>{const o=[],r=e.dom,s=zS(e,t,!0,n),a=zS(e,t,!1,n);let i;const l=[];for(let e=s;e&&(l.push(e),e!==a);e=e.nextSibling);return dn.each(l,(t=>{var s;if(Ux(e,t))return o.push(t),void(i=null);if(r.isBlock(t)||Fx(t))return Fx(t)&&r.remove(t),void(i=null);const a=t.nextSibling;ag.isBookmarkNode(t)&&(Px(a)||Ux(e,a)||!a&&t.parentNode===n)?i=null:(i||(i=r.create("p"),null===(s=t.parentNode)||void 0===s||s.insertBefore(i,t),o.push(i)),i.appendChild(t))})),o})(e,o,s),e.dom.isEditable);dn.each(l,(o=>{let s;const i=o.previousSibling,l=o.parentNode;Mx(l)||(i&&Px(i)&&i.nodeName===t&&((e,t,n)=>{const o=e.getStyle(t,"list-style-type");let r=n?n["list-style-type"]:"";return r=null===r?"":r,o===r})(a,i,n)?(s=i,o=a.rename(o,r),i.appendChild(o)):(s=a.create(t),l.insertBefore(s,o),s.appendChild(o),o=a.rename(o,r)),((e,t)=>{dn.each(["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"],(n=>e.setStyle(t,n,"")))})(a,o),FS(a,s,n),HS(e.dom,s))})),e.selection.setRng(Yx(i))},$S=(e,t,n)=>{return((e,t)=>Px(e)&&e.nodeName===(null==t?void 0:t.nodeName))(t,n)&&((e,t,n)=>e.getStyle(t,"list-style-type",!0)===e.getStyle(n,"list-style-type",!0))(e,t,n)&&(o=n,t.className===o.className);var o},HS=(e,t)=>{let n,o=t.nextSibling;if($S(e,t,o)){const r=o;for(;n=r.firstChild;)t.appendChild(n);e.remove(r)}if(o=t.previousSibling,$S(e,t,o)){const r=o;for(;n=r.lastChild;)t.insertBefore(n,t.firstChild);e.remove(r)}},VS=(e,t,n,o)=>{if(t.nodeName!==n){const r=e.dom.rename(t,n);FS(e.dom,r,o),lS(e,MS(n),r)}else FS(e.dom,t,o),lS(e,MS(n),t)},qS=(e,t,n,o)=>{if(t.classList.forEach(((e,n,o)=>{e.startsWith("tox-")&&(o.remove(e),0===o.length&&t.removeAttribute("class"))})),t.nodeName!==n){const r=e.dom.rename(t,n);FS(e.dom,r,o),lS(e,MS(n),r)}else FS(e.dom,t,o),lS(e,MS(n),t)},WS=e=>"list-style-type"in e,KS=(e,t,n)=>{const o=Jx(e);if(iS(e,o))return;const r=(e=>{const t=Jx(e),n=e.selection.getSelectedBlocks();return((e,t)=>C(e)&&1===t.length&&t[0]===e)(t,n)?(e=>Y(e.querySelectorAll(Zx),Px))(t):Y(n,(e=>Px(e)&&t!==e))})(e),s=f(n)?n:{};r.length>0?((e,t,n,o,r)=>{const s=Px(t);if(!s||t.nodeName!==o||WS(r)||sS(t)){jS(e,o,r);const a=Kx(e.selection.getRng()),i=s?[t,...n]:n,l=s&&sS(t)?qS:VS;dn.each(i,(t=>{l(e,t,o,r)})),e.selection.setRng(Yx(a))}else LS(e)})(e,o,r,t,s):((e,t,n,o)=>{if(t!==e.getBody())if(t)if(t.nodeName!==n||WS(o)||sS(t)){const r=Kx(e.selection.getRng());sS(t)&&t.classList.forEach(((e,n,o)=>{e.startsWith("tox-")&&(o.remove(e),0===o.length&&t.removeAttribute("class"))})),FS(e.dom,t,o);const s=e.dom.rename(t,n);HS(e.dom,s),e.selection.setRng(Yx(r)),jS(e,n,o),lS(e,MS(n),s)}else LS(e);else jS(e,n,o),lS(e,MS(n),t)})(e,o,t,s)},YS=(e,t,n,o)=>{let r=t.startContainer;const s=t.startOffset;if(Ox(r)&&(n?s<r.data.length:s>0))return r;const a=e.schema.getNonEmptyElements();Bx(r)&&(r=Xg.getNode(r,s));const i=new Hr(r,o);n&&((e,t)=>!!Fx(t)&&e.isBlock(t.nextSibling)&&!Fx(t.previousSibling))(e.dom,r)&&i.next();const l=n?i.next.bind(i):i.prev2.bind(i);for(;r=l();){if("LI"===r.nodeName&&!r.hasChildNodes())return r;if(a[r.nodeName])return r;if(Ox(r)&&r.data.length>0)return r}return null},GS=(e,t)=>{const n=t.childNodes;return 1===n.length&&!Px(n[0])&&e.isBlock(n[0])},XS=(e,t,n)=>{let o;const r=GS(e,n)?n.firstChild:n;if(((e,t)=>{var n;GS(e,t)&&(n=t.firstChild,I.from(n).map(mn.fromDom).filter(kn).exists((e=>vr(e)&&!$(["details"],En(e)))))&&e.remove(t.firstChild,!0)})(e,t),!$x(e,t,!0))for(;o=t.firstChild;)r.appendChild(o)},QS=(e,t,n)=>{let o;const r=t.parentNode;if(!Hx(e,t)||!Hx(e,n))return;Px(n.lastChild)&&(o=n.lastChild),r===n.lastChild&&Fx(r.previousSibling)&&e.remove(r.previousSibling);const s=n.lastChild;s&&Fx(s)&&t.hasChildNodes()&&e.remove(s),$x(e,n,!0)&&So(mn.fromDom(n)),XS(e,t,n),o&&n.appendChild(o);const a=yn(mn.fromDom(n),mn.fromDom(t))?e.getParents(t,Px,n):[];e.remove(t),q(a,(t=>{$x(e,t)&&t!==e.getRoot()&&e.remove(t)}))},ZS=(e,t)=>{const n=e.dom,o=e.selection,r=o.getStart(),s=tS(e,r),a=n.getParent(o.getStart(),"LI",s);if(a){const r=a.parentElement;if(r===e.getBody()&&$x(n,r))return!0;const i=qx(o.getRng()),l=n.getParent(YS(e,i,t,s),"LI",s),d=l&&(t?n.isChildOf(a,l):n.isChildOf(l,a));if(l&&l!==a&&!d)return e.undoManager.transact((()=>{var n,o;t?((e,t,n,o)=>{const r=e.dom;if(r.isEmpty(o))((e,t,n)=>{So(mn.fromDom(n)),QS(e.dom,t,n),e.selection.setCursorLocation(n,0)})(e,n,o);else{const s=Kx(t);QS(r,n,o),e.selection.setRng(Yx(s))}})(e,i,l,a):(null===(o=(n=a).parentNode)||void 0===o?void 0:o.firstChild)===n?PS(e):((e,t,n,o)=>{const r=Kx(t);QS(e.dom,n,o);const s=Yx(r);e.selection.setRng(s)})(e,i,a,l)})),!0;if(d&&!t&&l!==a){const t=i.commonAncestorContainer.parentElement;return!(!t||n.isChildOf(l,t)||(e.undoManager.transact((()=>{const o=Kx(i);XS(n,t,l),t.remove();const r=Yx(o);e.selection.setRng(r)})),0))}if(!l&&!t&&0===i.startOffset&&0===i.endOffset)return e.undoManager.transact((()=>{LS(e)})),!0}return!1},JS=e=>{const t=e.selection.getStart(),n=tS(e,t),o=e.dom.getParent(t,"LI,DT,DD",n);return C(o)||eS(e).length>0},e_=(e,t)=>{const n=e.selection;return!iS(e,n.getNode())&&(n.isCollapsed()?((e,t)=>ZS(e,t)||((e,t)=>{const n=e.dom,o=e.selection.getStart(),r=tS(e,o),s=n.getParent(o,n.isBlock,r);if(s&&n.isEmpty(s,void 0,{checkRootAsContent:!0})){const o=qx(e.selection.getRng()),a=YS(e,o,t,r),i=n.getParent(a,"LI",r);if(a&&i){const l=e=>$(["td","th","caption"],En(e)),d=e=>e.dom===r,c=sr(mn.fromDom(i),l,d),m=sr(mn.fromDom(o.startContainer),l,d);return!!ze(c,m,vn)&&(e.undoManager.transact((()=>{const o=i.parentNode;((e,t,n)=>{const o=e.getParent(t.parentNode,e.isBlock,n);e.remove(t),o&&e.isEmpty(o)&&e.remove(o)})(n,s,r),HS(n,o),e.selection.select(a,!0),e.selection.collapse(t)})),!0)}}return!1})(e,t))(e,t):(e=>!!JS(e)&&(e.undoManager.transact((()=>{let t=!0;const n=()=>t=!1;e.on("input",n),e.execCommand("Delete"),e.off("input",n),t&&e.dispatch("input"),Xx(e.dom,e.getBody())})),!0))(e))},t_=(e,t)=>({from:e,to:t}),n_=(e,t)=>{const n=mn.fromDom(e),o=mn.fromDom(t.container());return lb(n,o).map((e=>((e,t)=>({block:e,position:t}))(e,t)))},o_=(e,t)=>sr(t,(e=>Li(e)||gs(e.dom)),(t=>vn(t,e))).filter(Nn).getOr(e),r_=(e,t)=>{const n=((e,t)=>{const n=Hn(e);return J(n,(e=>t.isBlock(En(e)))).fold(N(n),(e=>n.slice(0,e)))})(e,t);return q(n,_o),n},s_=(e,t,n)=>{const o=ch(n,t);return Z(o.reverse(),(t=>Rs(e,t))).each(_o)},a_=(e,t,n,o,r)=>{if(Rs(o,n))return Fi(n),Iu(n.dom);((e,t)=>0===Y(jn(t),(t=>!Rs(e,t))).length)(o,r)&&Rs(o,t)&&io(r,mn.fromTag("br"));const s=Mu(n.dom,Ul.before(r.dom));return q(r_(t,o),(e=>{io(r,e)})),s_(o,e,t),s},i_=(e,t,n,o)=>{if(Rs(o,n)){if(Rs(o,t)){const e=e=>{const t=(e,n)=>qn(e).fold((()=>n),(e=>((e,t)=>e.isInline(En(t)))(o,e)?t(e,n.concat(Ao(e))):n));return t(e,[])},r=G(e(n),((e,t)=>(uo(e,t),t)),Ii());So(t),mo(t,r)}return _o(n),Iu(t.dom)}const r=Fu(n.dom);return q(r_(t,o),(e=>{mo(n,e)})),s_(o,e,t),r},l_=(e,t)=>{Pu(e,t.dom).bind((e=>I.from(e.getNode()))).map(mn.fromDom).filter(Di).each(_o)},d_=(e,t,n,o)=>(l_(!0,t),l_(!1,n),((e,t)=>yn(t,e)?((e,t)=>{const n=ch(t,e);return I.from(n[n.length-1])})(t,e):I.none())(t,n).fold(D(i_,e,t,n,o),D(a_,e,t,n,o))),c_=(e,t,n,o,r)=>t?d_(e,o,n,r):d_(e,n,o,r),m_=(e,t)=>{const n=mn.fromDom(e.getBody()),o=((e,t,n,o)=>o.collapsed?((e,t,n,o)=>{const r=n_(t,Ul.fromRangeStart(o)),s=r.bind((o=>Tu(n,t,o.position).bind((o=>n_(t,o).map((o=>((e,t,n,o)=>ms(o.position.getNode())&&!Rs(e,o.block)?Pu(!1,o.block.dom).bind((e=>e.isEqual(o.position)?Tu(n,t,e).bind((e=>n_(t,e))):I.some(o))).getOr(o):o)(e,t,n,o)))))));return je(r,s,t_).filter((e=>(e=>!vn(e.from.block,e.to.block))(e)&&((e,t)=>{const n=mn.fromDom(e);return vn(o_(n,t.from.block),o_(n,t.to.block))})(t,e)&&(e=>!1===ps(e.from.block.dom)&&!1===ps(e.to.block.dom))(e)&&(e=>{const t=e=>Ti(e)||Vs(e.dom)||Bi(e);return t(e.from.block)&&t(e.to.block)})(e)&&(e=>!(yn(e.to.block,e.from.block)||yn(e.from.block,e.to.block)))(e)))})(e,t,n,o):I.none())(e.schema,n.dom,t,e.selection.getRng()).map((o=>()=>{c_(n,t,o.from.block,o.to.block,e.schema).each((t=>{e.selection.setRng(t.toRange())}))}));return o},u_=(e,t)=>{const n=mn.fromDom(t),o=D(vn,e);return rr(n,Li,o).isSome()},f_=e=>{const t=mn.fromDom(e.getBody());return((e,t)=>{const n=Mu(e.dom,Ul.fromRangeStart(t)).isNone(),o=Lu(e.dom,Ul.fromRangeEnd(t)).isNone();return!((e,t)=>u_(e,t.startContainer)||u_(e,t.endContainer))(e,t)&&n&&o})(t,e.selection.getRng())?(e=>I.some((()=>{e.setContent(""),e.selection.setCursorLocation()})))(e):((e,t,n)=>{const o=t.getRng();return je(lb(e,mn.fromDom(o.startContainer)),lb(e,mn.fromDom(o.endContainer)),((r,s)=>vn(r,s)?I.none():I.some((()=>{o.deleteContents(),c_(e,!0,r,s,n).each((e=>{t.setRng(e.toRange())}))})))).getOr(I.none())})(t,e.selection,e.schema)},g_=(e,t)=>e.selection.isCollapsed()?I.none():f_(e),p_=(e,t,n,o,r)=>I.from(t._selectionOverrides.showCaret(e,n,o,r)),h_=(e,t)=>e.dispatch("BeforeObjectSelected",{target:t}).isDefaultPrevented()?I.none():I.some((e=>{const t=e.ownerDocument.createRange();return t.selectNode(e),t})(t)),b_=(e,t,n)=>t.collapsed?((e,t,n)=>{const o=mu(1,e.getBody(),t),r=Ul.fromRangeStart(o),s=r.getNode();if($m(s))return p_(1,e,s,!r.isAtEnd(),!1);const a=r.getNode(!0);if($m(a))return p_(1,e,a,!1,!1);const i=Ub(e.dom.getRoot(),r.getNode());return $m(i)?p_(1,e,i,!1,n):I.none()})(e,t,n).getOr(t):t,v_=e=>ih(e)||oh(e),y_=e=>lh(e)||rh(e),C_=(e,t,n,o,r,s)=>{p_(o,e,s.getNode(!r),r,!0).each((n=>{if(t.collapsed){const e=t.cloneRange();r?e.setEnd(n.startContainer,n.startOffset):e.setStart(n.endContainer,n.endOffset),e.deleteContents()}else t.deleteContents();e.selection.setRng(n)})),((e,t)=>{ss(t)&&0===t.data.length&&e.remove(t)})(e.dom,n)},w_=(e,t)=>((e,t)=>{const n=e.selection.getRng();if(!ss(n.commonAncestorContainer))return I.none();const o=t?1:-1,r=Nu(e.getBody()),s=D(pu,t?r.next:r.prev),a=t?v_:y_,i=fu(o,e.getBody(),n),l=s(i),d=l?tb(t,l):l;if(!d||!hu(i,d))return I.none();if(a(d))return I.some((()=>C_(e,n,i.getNode(),o,t,d)));const c=s(d);return c&&a(c)&&hu(d,c)?I.some((()=>C_(e,n,i.getNode(),o,t,c))):I.none()})(e,t),E_=(e,t)=>{const n=e.getBody();return t?Iu(n).filter(ih):Fu(n).filter(lh)},x_=e=>{const t=e.selection.getRng();return!t.collapsed&&(E_(e,!0).exists((e=>e.isEqual(Ul.fromRangeStart(t))))||E_(e,!1).exists((e=>e.isEqual(Ul.fromRangeEnd(t)))))},S_=ke([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),__=(e,t,n,o)=>Tu(t,e,n).bind((r=>{return s=r.getNode(),C(s)&&(Li(mn.fromDom(s))||Bi(mn.fromDom(s)))||((e,t,n,o,r)=>{const s=t=>r.isInline(t.nodeName.toLowerCase())&&!ru(n,o,e);return uu(!t,n).fold((()=>uu(t,o).fold(L,s)),s)})(e,t,n,r,o)?I.none():t&&ps(r.getNode())||!t&&ps(r.getNode(!0))?((e,t,n,o,r)=>{const s=r.getNode(!n);return lb(mn.fromDom(t),mn.fromDom(o.getNode())).map((t=>Rs(e,t)?S_.remove(t.dom):S_.moveToElement(s))).orThunk((()=>I.some(S_.moveToElement(s))))})(o,e,t,n,r):t&&lh(n)||!t&&ih(n)?I.some(S_.moveToPosition(r)):I.none();var s})),k_=(e,t)=>I.from(Ub(e.getBody(),t)),N_=(e,t)=>{const n=e.selection.getNode();return k_(e,n).filter(ps).fold((()=>((e,t,n,o)=>{const r=mu(t?1:-1,e,n),s=Ul.fromRangeStart(r),a=mn.fromDom(e);return!t&&lh(s)?I.some(S_.remove(s.getNode(!0))):t&&ih(s)?I.some(S_.remove(s.getNode())):!t&&ih(s)&&Eh(a,s,o)?xh(a,s,o).map((e=>S_.remove(e.getNode()))):t&&lh(s)&&wh(a,s,o)?Sh(a,s,o).map((e=>S_.remove(e.getNode()))):((e,t,n,o)=>((e,t)=>{const n=t.getNode(!e),o=e?"after":"before";return Xr(n)&&n.getAttribute("data-mce-caret")===o})(t,n)?((e,t)=>y(t)?I.none():e&&ps(t.nextSibling)?I.some(S_.moveToElement(t.nextSibling)):!e&&ps(t.previousSibling)?I.some(S_.moveToElement(t.previousSibling)):I.none())(t,n.getNode(!t)).orThunk((()=>__(e,t,n,o))):__(e,t,n,o).bind((t=>((e,t,n)=>n.fold((e=>I.some(S_.remove(e))),(e=>I.some(S_.moveToElement(e))),(n=>ru(t,n,e)?I.none():I.some(S_.moveToPosition(n)))))(e,n,t))))(e,t,s,o)})(e.getBody(),t,e.selection.getRng(),e.schema).map((n=>()=>n.fold(((e,t)=>n=>(e._selectionOverrides.hideFakeCaret(),Xh(e,t,mn.fromDom(n)),!0))(e,t),((e,t)=>n=>{const o=t?Ul.before(n):Ul.after(n);return e.selection.setRng(o.toRange()),!0})(e,t),(e=>t=>(e.selection.setRng(t.toRange()),!0))(e))))),(()=>I.some(S)))},A_=e=>{const t=e.dom,n=e.selection,o=Ub(e.getBody(),n.getNode());if(gs(o)&&t.isBlock(o)&&t.isEmpty(o)){const e=t.create("br",{"data-mce-bogus":"1"});t.setHTML(o,""),o.appendChild(e),n.setRng(Ul.before(e).toRange())}return!0},R_=(e,t)=>e.selection.isCollapsed()?N_(e,t):((e,t)=>{const n=e.selection.getNode();return ps(n)&&!bs(n)?k_(e,n.parentNode).filter(ps).fold((()=>I.some((()=>{var n;n=mn.fromDom(e.getBody()),q(xr(n,".mce-offscreen-selection"),_o),Xh(e,t,mn.fromDom(e.selection.getNode())),db(e)}))),(()=>I.some(S))):x_(e)?I.some((()=>{ub(e,e.selection.getRng(),mn.fromDom(e.getBody()))})):I.none()})(e,t),D_=e=>e.hasOwnProperty("text"),T_=e=>e.hasOwnProperty("marker"),O_=(e,t)=>{const n=(e,n)=>{if(ss(e))return{text:e,offset:n};{const o=t(),r=e.childNodes;return n<r.length?(e.insertBefore(o,r[n]),{marker:o,before:!0}):(e.appendChild(o),{marker:o,before:!1})}},o=n(e.endContainer,e.endOffset);return{start:n(e.startContainer,e.startOffset),end:o}},B_=e=>{var t,n;const{start:o,end:r}=e,s=new window.Range;return D_(o)?s.setStart(o.text,o.offset):T_(o)&&(o.before?s.setStartBefore(o.marker):s.setStartAfter(o.marker),null===(t=o.marker.parentNode)||void 0===t||t.removeChild(o.marker)),D_(r)?s.setEnd(r.text,r.offset):T_(r)&&(r.before?s.setEndBefore(r.marker):s.setEndAfter(r.marker),null===(n=r.marker.parentNode)||void 0===n||n.removeChild(r.marker)),s},P_=(e,t)=>{var n;const o=e.dom,r=o.getParent(e.selection.getStart(),o.isBlock),s=o.getParent(e.selection.getEnd(),o.isBlock),a=e.getBody();if("div"===(null===(n=null==r?void 0:r.nodeName)||void 0===n?void 0:n.toLowerCase())&&r&&s&&r===a.firstChild&&s===a.lastChild&&!o.isEmpty(a)){const n=r.cloneNode(!1),o=()=>{if(t?ab(e):sb(e),a.firstChild!==r){const t=O_(e.selection.getRng(),(()=>document.createElement("span")));Array.from(a.childNodes).forEach((e=>n.appendChild(e))),a.appendChild(n),e.selection.setRng(B_(t))}};return I.some(o)}return I.none()},L_=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=Ul.fromRangeStart(e.selection.getRng());return Tu(t,e.getBody(),n).filter((e=>t?th(e):nh(e))).bind((e=>su(t?0:-1,e))).map((t=>()=>e.selection.select(t)))})(e,t):I.none(),M_=ss,I_=e=>M_(e)&&e.data[0]===Ui,F_=e=>M_(e)&&e.data[e.data.length-1]===Ui,U_=e=>{var t;return(null!==(t=e.ownerDocument)&&void 0!==t?t:document).createTextNode(Ui)},z_=(e,t)=>e?(e=>{var t;if(M_(e.previousSibling))return F_(e.previousSibling)||e.previousSibling.appendData(Ui),e.previousSibling;if(M_(e))return I_(e)||e.insertData(0,Ui),e;{const n=U_(e);return null===(t=e.parentNode)||void 0===t||t.insertBefore(n,e),n}})(t):(e=>{var t,n;if(M_(e.nextSibling))return I_(e.nextSibling)||e.nextSibling.insertData(0,Ui),e.nextSibling;if(M_(e))return F_(e)||e.appendData(Ui),e;{const o=U_(e);return e.nextSibling?null===(t=e.parentNode)||void 0===t||t.insertBefore(o,e.nextSibling):null===(n=e.parentNode)||void 0===n||n.appendChild(o),o}})(t),j_=D(z_,!0),$_=D(z_,!1),H_=(e,t)=>ss(e.container())?z_(t,e.container()):z_(t,e.getNode()),V_=(e,t)=>{const n=t.get();return n&&e.container()===n&&qi(n)},q_=(e,t)=>t.fold((t=>{Lm(e.get());const n=j_(t);return e.set(n),I.some(Ul(n,n.length-1))}),(t=>Iu(t).map((t=>{if(V_(t,e)){const t=e.get();return Ul(t,1)}{Lm(e.get());const n=H_(t,!0);return e.set(n),Ul(n,1)}}))),(t=>Fu(t).map((t=>{if(V_(t,e)){const t=e.get();return Ul(t,t.length-1)}{Lm(e.get());const n=H_(t,!1);return e.set(n),Ul(n,n.length-1)}}))),(t=>{Lm(e.get());const n=$_(t);return e.set(n),I.some(Ul(n,1))})),W_=(e,t)=>{for(let n=0;n<e.length;n++){const o=e[n].apply(null,t);if(o.isSome())return o}return I.none()},K_=ke([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),Y_=(e,t)=>ou(t,e)||e,G_=(e,t,n)=>{const o=nb(n),r=Y_(t,o.container());return eb(e,r,o).fold((()=>Lu(r,o).bind(D(eb,e,r)).map((e=>K_.before(e)))),I.none)},X_=(e,t)=>null===ju(e,t),Q_=(e,t,n)=>eb(e,t,n).filter(D(X_,t)),Z_=(e,t,n)=>{const o=ob(n);return Q_(e,t,o).bind((e=>Mu(e,o).isNone()?I.some(K_.start(e)):I.none()))},J_=(e,t,n)=>{const o=nb(n);return Q_(e,t,o).bind((e=>Lu(e,o).isNone()?I.some(K_.end(e)):I.none()))},ek=(e,t,n)=>{const o=ob(n),r=Y_(t,o.container());return eb(e,r,o).fold((()=>Mu(r,o).bind(D(eb,e,r)).map((e=>K_.after(e)))),I.none)},tk=e=>!Jh(ok(e)),nk=(e,t,n)=>W_([G_,Z_,J_,ek],[e,t,n]).filter(tk),ok=e=>e.fold(A,A,A,A),rk=e=>e.fold(N("before"),N("start"),N("end"),N("after")),sk=e=>e.fold(K_.before,K_.before,K_.after,K_.after),ak=e=>e.fold(K_.start,K_.start,K_.end,K_.end),ik=(e,t,n,o,r,s)=>je(eb(t,n,o),eb(t,n,r),((t,o)=>t!==o&&((e,t,n)=>{const o=ou(t,e),r=ou(n,e);return C(o)&&o===r})(n,t,o)?K_.after(e?t:o):s)).getOr(s),lk=(e,t)=>e.fold(M,(e=>{return o=t,!(rk(n=e)===rk(o)&&ok(n)===ok(o));var n,o})),dk=(e,t)=>e?t.fold(_(I.some,K_.start),I.none,_(I.some,K_.after),I.none):t.fold(I.none,_(I.some,K_.before),I.none,_(I.some,K_.end)),ck=(e,t,n)=>{const o=e?1:-1;return t.setRng(Ul(n.container(),n.offset()+o).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0};var mk;!function(e){e[e.Br=0]="Br",e[e.Block=1]="Block",e[e.Wrap=2]="Wrap",e[e.Eol=3]="Eol"}(mk||(mk={}));const uk=(e,t)=>-1===e?oe(t):t,fk=(e,t,n)=>1===e?t.next(n):t.prev(n),gk=(e,t,n,o)=>ms(o.getNode(1===t))?mk.Br:!1===ru(n,o)?mk.Block:mk.Wrap,pk=(e,t,n,o)=>{const r=Nu(n);let s=o;const a=[];for(;s;){const n=fk(t,r,s);if(!n)break;if(ms(n.getNode(!1)))return 1===t?{positions:uk(t,a).concat([n]),breakType:mk.Br,breakAt:I.some(n)}:{positions:uk(t,a),breakType:mk.Br,breakAt:I.some(n)};if(n.isVisible()){if(e(s,n)){const e=gk(0,t,s,n);return{positions:uk(t,a),breakType:e,breakAt:I.some(n)}}a.push(n),s=n}else s=n}return{positions:uk(t,a),breakType:mk.Eol,breakAt:I.none()}},hk=(e,t,n,o)=>t(n,o).breakAt.map((o=>{const r=t(n,o).positions;return-1===e?r.concat(o):[o].concat(r)})).getOr([]),bk=(e,t)=>X(e,((e,n)=>e.fold((()=>I.some(n)),(o=>je(le(o.getClientRects()),le(n.getClientRects()),((e,r)=>{const s=Math.abs(t-e.left);return Math.abs(t-r.left)<=s?n:o})).or(e)))),I.none()),vk=(e,t)=>le(t.getClientRects()).bind((t=>bk(e,t.left))),yk=D(pk,Ul.isAbove,-1),Ck=D(pk,Ul.isBelow,1),wk=D(hk,-1,yk),Ek=D(hk,1,Ck),xk=(e,t)=>yk(e,t).breakAt.isNone(),Sk=(e,t)=>Ck(e,t).breakAt.isNone(),_k=(e,t)=>vk(wk(e,t),t),kk=(e,t)=>vk(Ek(e,t),t),Nk=ps,Ak=(e,t)=>Math.abs(e.left-t),Rk=(e,t)=>Math.abs(e.right-t),Dk=(e,t)=>bt(e,((e,n)=>{const o=Math.min(Ak(e,t),Rk(e,t)),r=Math.min(Ak(n,t),Rk(n,t));return r===o&&_e(n,"node")&&Nk(n.node)||r<o?n:e})),Tk=e=>{const t=t=>V(t,(t=>{const n=tl(t);return n.node=e,n}));if(Xr(e))return t(e.getClientRects());if(ss(e)){const n=e.ownerDocument.createRange();return n.setStart(e,0),n.setEnd(e,e.data.length),t(n.getClientRects())}return[]},Ok=e=>te(e,Tk);var Bk;!function(e){e[e.Up=-1]="Up",e[e.Down=1]="Down"}(Bk||(Bk={}));const Pk=(e,t,n,o,r,s)=>{let a=0;const i=[],l=o=>{let s=Ok([o]);e===Bk.Up&&(s=s.reverse());for(let e=0;e<s.length;e++){const o=s[e];if(!n(o,d)){if(i.length>0&&t(o,yt(i))&&a++,o.line=a,r(o))return!0;i.push(o)}}return!1},d=yt(s.getClientRects());if(!d)return i;const c=s.getNode();return c&&(l(c),((e,t,n,o)=>{let r=o;for(;r=nu(r,e,wl,t);)if(n(r))return})(e,o,l,c)),i},Lk=D(Pk,Bk.Up,rl,sl),Mk=D(Pk,Bk.Down,sl,rl),Ik=e=>yt(e.getClientRects()),Fk=e=>t=>((e,t)=>t.line>e)(e,t),Uk=e=>t=>((e,t)=>t.line===e)(e,t),zk=(e,t)=>{e.selection.setRng(t),up(e,e.selection.getRng())},jk=(e,t,n)=>I.some(b_(e,t,n)),$k=(e,t,n,o,r,s)=>{const a=1===t,i=Nu(e.getBody()),l=D(pu,a?i.next:i.prev),d=a?o:r;if(!n.collapsed){const o=il(n);if(s(o))return p_(t,e,o,-1===t,!1);if(x_(e)){const e=n.cloneRange();return e.collapse(-1===t),I.from(e)}}const c=fu(t,e.getBody(),n);if(d(c))return h_(e,c.getNode(!a));let m=l(c);const u=Ji(n);if(!m)return u?I.some(n):I.none();if(m=tb(a,m),d(m))return p_(t,e,m.getNode(!a),a,!1);const f=l(m);return f&&d(f)&&hu(m,f)?p_(t,e,f.getNode(!a),a,!1):u?jk(e,m.toRange(),!1):I.none()},Hk=(e,t,n,o,r,s)=>{const a=fu(t,e.getBody(),n),i=yt(a.getClientRects()),l=t===Bk.Down,d=e.getBody();if(!i)return I.none();if(x_(e)){const e=l?Ul.fromRangeEnd(n):Ul.fromRangeStart(n);return(l?kk:_k)(d,e).orThunk((()=>I.from(e))).map((e=>e.toRange()))}const c=(l?Mk:Lk)(d,Fk(1),a),m=Y(c,Uk(1)),u=i.left,f=Dk(m,u);if(f&&s(f.node)){const n=Math.abs(u-f.left),o=Math.abs(u-f.right);return p_(t,e,f.node,n<o,!1)}let g;if(g=o(a)?a.getNode():r(a)?a.getNode(!0):il(n),g){const n=((e,t,n,o)=>{const r=Nu(t);let s,a,i,l;const d=[];let c=0;e===Bk.Down?(s=r.next,a=sl,i=rl,l=Ul.after(o)):(s=r.prev,a=rl,i=sl,l=Ul.before(o));const m=Ik(l);do{if(!l.isVisible())continue;const e=Ik(l);if(i(e,m))continue;d.length>0&&a(e,yt(d))&&c++;const t=tl(e);if(t.position=l,t.line=c,n(t))return d;d.push(t)}while(l=s(l));return d})(t,d,Fk(1),g);let o=Dk(Y(n,Uk(1)),u);if(o)return jk(e,o.position.toRange(),!1);if(o=yt(Y(n,Uk(0))),o)return jk(e,o.position.toRange(),!1)}return 0===m.length?Vk(e,l).filter(l?r:o).map((t=>b_(e,t.toRange(),!1))):I.none()},Vk=(e,t)=>{const n=e.selection.getRng(),o=t?Ul.fromRangeEnd(n):Ul.fromRangeStart(n),r=(s=o.container(),a=e.getBody(),rr(mn.fromDom(s),(e=>Vm(e.dom)),(e=>e.dom===a)).map((e=>e.dom)).getOr(a));var s,a;if(t){const e=Ck(r,o);return de(e.positions)}{const e=yk(r,o);return le(e.positions)}},qk=(e,t,n)=>Vk(e,t).filter(n).exists((t=>(e.selection.setRng(t.toRange()),!0))),Wk=(e,t)=>{const n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},Kk=(e,t)=>{e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},Yk=(e,t,n)=>q_(t,n).map((t=>(Wk(e,t),n))),Gk=(e,t,n)=>{const o=e.getBody(),r=((e,t,n)=>{const o=Ul.fromRangeStart(e);if(e.collapsed)return o;{const r=Ul.fromRangeEnd(e);return n?Mu(t,r).getOr(r):Lu(t,o).getOr(o)}})(e.selection.getRng(),o,n);return((e,t,n,o)=>{const r=tb(e,o),s=nk(t,n,r);return nk(t,n,r).bind(D(dk,e)).orThunk((()=>((e,t,n,o,r)=>{const s=tb(e,r);return Tu(e,n,s).map(D(tb,e)).fold((()=>o.map(sk)),(r=>nk(t,n,r).map(D(ik,e,t,n,s,r)).filter(D(lk,o)))).filter(tk)})(e,t,n,s,o)))})(n,D(Zh,e),o,r).bind((n=>Yk(e,t,n)))},Xk=(e,t,n)=>!!vc(e)&&Gk(e,t,n).isSome(),Qk=(e,t,n)=>!!vc(t)&&((e,t)=>{const n=t.selection.getRng(),o=e?Ul.fromRangeEnd(n):Ul.fromRangeStart(n);return!!(e=>w(e.selection.getSel().modify))(t)&&(e&&Yi(o)?ck(!0,t.selection,o):!(e||!Gi(o))&&ck(!1,t.selection,o))})(e,t),Zk=e=>{const t=Ne(null),n=D(Zh,e);return e.on("NodeChange",(o=>{vc(e)&&(((e,t,n)=>{const o=V(xr(mn.fromDom(t.getRoot()),'*[data-mce-selected="inline-boundary"]'),(e=>e.dom)),r=Y(o,e),s=Y(n,e);q(re(r,s),D(Kk,!1)),q(re(s,r),D(Kk,!0))})(n,e.dom,o.parents),((e,t)=>{const n=t.get();if(e.selection.isCollapsed()&&!e.composing&&n){const o=Ul.fromRangeStart(e.selection.getRng());Ul.isTextPosition(o)&&!(e=>Yi(e)||Gi(e))(o)&&(Wk(e,Pm(n,o)),t.set(null))}})(e,t),((e,t,n,o)=>{if(t.selection.isCollapsed()){const r=Y(o,e);q(r,(o=>{const r=Ul.fromRangeStart(t.selection.getRng());nk(e,t.getBody(),r).bind((e=>Yk(t,n,e)))}))}})(n,e,t,o.parents))})),t},Jk=D(Qk,!0),eN=D(Qk,!1),tN=(e,t,n)=>{if(vc(e)){const o=Vk(e,t).getOrThunk((()=>{const n=e.selection.getRng();return t?Ul.fromRangeEnd(n):Ul.fromRangeStart(n)}));return nk(D(Zh,e),e.getBody(),o).exists((t=>{const o=sk(t);return q_(n,o).exists((t=>(Wk(e,t),!0)))}))}return!1},nN=(e,t)=>n=>q_(t,n).map((t=>()=>Wk(e,t))),oN=(e,t,n,o)=>{const r=e.getBody(),s=D(Zh,e);e.undoManager.ignore((()=>{e.selection.setRng(((e,t)=>{const n=document.createRange();return n.setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n})(n,o)),sb(e),nk(s,r,Ul.fromRangeStart(e.selection.getRng())).map(ak).bind(nN(e,t)).each(P)})),e.nodeChanged()},rN=(e,t,n)=>{if(e.selection.isCollapsed()&&vc(e)){const o=Ul.fromRangeStart(e.selection.getRng());return((e,t,n,o)=>{const r=((e,t)=>ou(t,e)||e)(e.getBody(),o.container()),s=D(Zh,e),a=nk(s,r,o);return a.bind((e=>n?e.fold(N(I.some(ak(e))),I.none,N(I.some(sk(e))),I.none):e.fold(I.none,N(I.some(sk(e))),I.none,N(I.some(ak(e)))))).map(nN(e,t)).getOrThunk((()=>{const i=Ou(n,r,o),l=i.bind((e=>nk(s,r,e)));return je(a,l,(()=>eb(s,r,o).bind((t=>(e=>je(Iu(e),Fu(e),((t,n)=>{const o=tb(!0,t),r=tb(!1,n);return Lu(e,o).forall((e=>e.isEqual(r)))})).getOr(!0))(t)?I.some((()=>{Xh(e,n,mn.fromDom(t))})):I.none())))).getOrThunk((()=>l.bind((()=>i.map((r=>()=>{n?oN(e,t,o,r):oN(e,t,r,o)}))))))}))})(e,t,n,o)}return I.none()},sN=(e,t)=>{const n=mn.fromDom(e.getBody()),o=mn.fromDom(e.selection.getStart()),r=ch(o,n);return J(r,t).fold(N(r),(e=>r.slice(0,e)))},aN=e=>1===Kn(e),iN=(e,t)=>{const n=D(uv,e);return te(t,(e=>n(e)?[e.dom]:[]))},lN=e=>{const t=(e=>sN(e,(t=>e.schema.isBlock(En(t)))))(e);return iN(e,t)},dN=(e,t)=>{const n=Y((e=>sN(e,(t=>e.schema.isBlock(En(t))||(e=>Kn(e)>1)(t))))(e),aN);return de(n).bind((o=>{const r=Ul.fromRangeStart(e.selection.getRng());return cb(t,r,o.dom)&&!Ff(o)?I.some((()=>((e,t,n,o)=>{const r=iN(t,o);if(0===r.length)Xh(t,e,n);else{const e=mv(n.dom,r);t.selection.setRng(e.toRange())}})(t,e,o,n))):I.none()}))},cN=(e,t)=>{const n=e.selection.getStart(),o=((e,t)=>{const n=t.parentElement;return ms(t)&&!h(n)&&e.dom.isEmpty(n)})(e,n)||Ff(mn.fromDom(n))?mv(n,t):((e,t)=>{const{caretContainer:n,caretPosition:o}=cv(t);return e.insertNode(n.dom),o})(e.selection.getRng(),t);e.selection.setRng(o.toRange())},mN=e=>ss(e.startContainer),uN=e=>{const t=e.selection.getRng();return(e=>0===e.startOffset&&mN(e))(t)&&((e,t)=>{const n=t.startContainer.parentElement;return!h(n)&&uv(e,mn.fromDom(n))})(e,t)&&(e=>(e=>(e=>{const t=e.startContainer.parentNode,n=e.endContainer.parentNode;return!h(t)&&!h(n)&&t.isEqualNode(n)})(e)&&(e=>{const t=e.endContainer;return e.endOffset===(ss(t)?t.length:t.childNodes.length)})(e))(e)||(e=>!e.endContainer.isEqualNode(e.commonAncestorContainer))(e))(t)},fN=(e,t)=>e.selection.isCollapsed()?dN(e,t):(e=>{if(uN(e)){const t=lN(e);return I.some((()=>{sb(e),((e,t)=>{const n=re(t,lN(e));n.length>0&&cN(e,n)})(e,t)}))}return I.none()})(e),gN=e=>((e=>{const t=e.selection.getRng();return t.collapsed&&(mN(t)||e.dom.isEmpty(t.startContainer))&&!(e=>{return t=mn.fromDom(e.selection.getStart()),n=e.schema,Sr(t,(e=>zu(e.dom)),(e=>n.isBlock(En(e))));var t,n})(e)})(e)&&cN(e,[]),!0),pN=(e,t,n)=>C(n)?I.some((()=>{e._selectionOverrides.hideFakeCaret(),Xh(e,t,mn.fromDom(n))})):I.none(),hN=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=t?oh:rh,o=fu(t?1:-1,e.getBody(),e.selection.getRng());return n(o)?pN(e,t,o.getNode(!t)):I.from(tb(t,o)).filter((e=>n(e)&&hu(o,e))).bind((n=>pN(e,t,n.getNode(!t))))})(e,t):((e,t)=>{const n=e.selection.getNode();return Cs(n)?pN(e,t,n):I.none()})(e,t),bN=e=>rt(null!=e?e:"").getOr(0),vN=(e,t)=>(e||"table"===En(t)?"margin":"padding")+("rtl"===Uo(t,"direction")?"-right":"-left"),yN=e=>{const t=wN(e);return!e.mode.isReadOnly()&&(t.length>1||((e,t)=>ne(t,(t=>{const n=vN(nc(e),t),o=jo(t,n).map(bN).getOr(0);return"false"!==e.dom.getContentEditable(t.dom)&&o>0})))(e,t))},CN=e=>Oi(e)||Bi(e),wN=e=>Y(To(e.selection.getSelectedBlocks()),(e=>!CN(e)&&!(e=>Ln(e).exists(CN))(e)&&sr(e,(e=>gs(e.dom)||ps(e.dom))).exists((e=>gs(e.dom))))),EN=(e,t)=>{var n,o;if(e.mode.isReadOnly())return;const{dom:r}=e,s=oc(e),a=null!==(o=null===(n=/[a-z%]+$/i.exec(s))||void 0===n?void 0:n[0])&&void 0!==o?o:"px",i=bN(s),l=nc(e);q(wN(e),(e=>{((e,t,n,o,r,s)=>{const a=vN(n,mn.fromDom(s)),i=bN(e.getStyle(s,a));if("outdent"===t){const t=Math.max(0,i-o);e.setStyle(s,a,t?t+r:"")}else{const t=i+o+r;e.setStyle(s,a,t)}})(r,t,l,i,a,e.dom)})),"indent"===t?BS(e):PS(e)},xN=e=>EN(e,"outdent"),SN=e=>{if(e.selection.isCollapsed()&&yN(e)){const t=e.dom,n=e.selection.getRng(),o=Ul.fromRangeStart(n),r=t.getParent(n.startContainer,t.isBlock);if(null!==r&&ph(mn.fromDom(r),o,e.schema))return I.some((()=>xN(e)))}return I.none()},_N=(e,t)=>e.selection.isCollapsed()?I.none():((e,t)=>{return n=ay(e.selection.getRng()),hf(n,Qr)?I.some((()=>Xh(e,t,mn.fromDom(e.selection.getNode())))):I.none();var n})(e,t),kN=(e,t,n)=>me([SN,R_,w_,(e,n)=>rN(e,t,n),m_,Fb,L_,hN,g_,fN,P_,_N],(t=>t(e,n))).filter((t=>e.selection.isEditable())),NN=(e,t)=>{kN(e,t,!1).fold((()=>{e.selection.isEditable()&&(sb(e),db(e))}),P),JS(e)&&Xx(e.dom,e.getBody())},AN=e=>void 0===e.touches||1!==e.touches.length?I.none():I.some(e.touches[0]),RN=(e,t)=>Se(e,t.nodeName),DN=(e,t)=>!!ss(t)||!!Xr(t)&&!(RN(e.getBlockElements(),t)||Ju(t)||Ks(e,t)||Bs(t)||ys(t)),TN=(e,t)=>{if(ss(t)){if(0===t.data.length)return!0;if(/^\s+$/.test(t.data))return!t.nextSibling||RN(e,t.nextSibling)||Bs(t.nextSibling)}return!1},ON=e=>e.dom.create(Ld(e),Md(e)),BN=(e,t,n)=>{const o=mn.fromDom(ON(e)),r=Ii();mo(o,r),n(t,o);const s=document.createRange();return s.setStartBefore(r.dom),s.setEndBefore(r.dom),s},PN=e=>t=>-1!==(" "+t.attr("class")+" ").indexOf(e),LN=(e,t,n)=>function(o){const r=arguments,s=r[r.length-2],a=s>0?t.charAt(s-1):"";if('"'===a)return o;if(">"===a){const e=t.lastIndexOf("<",s);if(-1!==e&&-1!==t.substring(e,s).indexOf('contenteditable="false"'))return o}return'<span class="'+n+'" data-mce-content="'+e.dom.encode(r[0])+'">'+e.dom.encode("string"==typeof r[1]?r[1]:r[0])+"</span>"},MN=(e,t)=>ne(e,(e=>{const n=t.match(e);return null!==n&&n[0].length===t.length})),IN=(e,t)=>{t.hasAttribute("data-mce-caret")&&(Zi(t),e.selection.setRng(e.selection.getRng()),e.selection.scrollIntoView(t))},FN=(e,t)=>{const n=(e=>lr(mn.fromDom(e.getBody()),"*[data-mce-caret]").map((e=>e.dom)).getOrNull())(e);if(n)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void IN(e,n)):void(Ki(n)&&(IN(e,n),e.undoManager.add()))},UN=ps,zN=(e,t,n)=>{const o=Nu(e.getBody()),r=D(pu,1===t?o.next:o.prev);if(n.collapsed){const o=e.dom.getParent(n.startContainer,"PRE");if(!o)return;if(!r(Ul.fromRangeStart(n))){const n=mn.fromDom((e=>{const t=e.dom.create(Ld(e));return t.innerHTML='<br data-mce-bogus="1">',t})(e));1===t?lo(mn.fromDom(o),n):io(mn.fromDom(o),n),e.selection.select(n.dom,!0),e.selection.collapse()}}},jN=(e,t)=>((e,t)=>{const n=t?1:-1,o=e.selection.getRng();return((e,t,n)=>$k(t,e,n,ih,lh,UN))(n,e,o).orThunk((()=>(zN(e,n,o),I.none())))})(e,((e,t)=>{const n=t?e.getEnd(!0):e.getStart(!0);return Jh(n)?!t:t})(e.selection,t)).exists((t=>(zk(e,t),!0))),$N=(e,t)=>((e,t)=>{const n=t?1:-1,o=e.selection.getRng();return((e,t,n)=>Hk(t,e,n,(e=>ih(e)||sh(e)),(e=>lh(e)||ah(e)),UN))(n,e,o).orThunk((()=>(zN(e,n,o),I.none())))})(e,t).exists((t=>(zk(e,t),!0))),HN=(e,t)=>qk(e,t,t?lh:ih),VN=(e,t)=>E_(e,!t).map((n=>{const o=n.toRange(),r=e.selection.getRng();return t?o.setStart(r.startContainer,r.startOffset):o.setEnd(r.endContainer,r.endOffset),o})).exists((t=>(zk(e,t),!0))),qN=e=>$(["figcaption"],En(e)),WN=(e,t)=>!!e.selection.isCollapsed()&&((e,t)=>{const n=mn.fromDom(e.getBody()),o=Ul.fromRangeStart(e.selection.getRng());return((e,t,n)=>{const o=D(vn,t);return sr(mn.fromDom(e.container()),(e=>n.isBlock(En(e))),o).filter(qN)})(o,n,e.schema).exists((()=>{if(((e,t,n)=>t?Sk(e.dom,n):xk(e.dom,n))(n,t,o)){const o=BN(e,n,t?mo:co);return e.selection.setRng(o),!0}return!1}))})(e,t),KN=(e,t)=>((e,t)=>t?I.from(e.dom.getParent(e.selection.getNode(),"details")).map((t=>((e,t)=>{const n=e.selection.getRng(),o=Ul.fromRangeStart(n);return!(e.getBody().lastChild!==t||!Sk(t,o)||(e.execCommand("InsertNewBlockAfter"),0))})(e,t))).getOr(!1):I.from(e.dom.getParent(e.selection.getNode(),"summary")).bind((t=>I.from(e.dom.getParent(t,"details")).map((n=>((e,t,n)=>{const o=e.selection.getRng(),r=Ul.fromRangeStart(o);return!(e.getBody().firstChild!==t||!xk(n,r)||(e.execCommand("InsertNewBlockBefore"),0))})(e,n,t))))).getOr(!1))(e,t),YN={shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0},GN=(e,t)=>t.keyCode===e.keyCode&&t.shiftKey===e.shiftKey&&t.altKey===e.altKey&&t.ctrlKey===e.ctrlKey&&t.metaKey===e.metaKey,XN=(e,...t)=>()=>e.apply(null,t),QN=(e,t)=>Z(((e,t)=>te((e=>V(e,(e=>({...YN,...e}))))(e),(e=>GN(e,t)?[e]:[])))(e,t),(e=>e.action())),ZN=(e,t)=>me(((e,t)=>te((e=>V(e,(e=>({...YN,...e}))))(e),(e=>GN(e,t)?[e]:[])))(e,t),(e=>e.action())),JN=(e,t)=>{const n=t?1:-1,o=e.selection.getRng();return $k(e,n,o,oh,rh,Cs).exists((t=>(zk(e,t),!0)))},eA=(e,t)=>{const n=t?1:-1,o=e.selection.getRng();return Hk(e,n,o,oh,rh,Cs).exists((t=>(zk(e,t),!0)))},tA=(e,t)=>qk(e,t,t?rh:oh),nA=(e,t,n)=>te(Hn(e),(e=>hn(e,t)?n(e)?[e]:[]:nA(e,t,n))),oA=(e,t)=>dr(e,"table",t),rA=ke([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),sA={...rA,none:e=>rA.none(e)},aA=(e,t,n,o,r=M)=>{const s=1===o;if(!s&&n<=0)return sA.first(e[0]);if(s&&n>=e.length-1)return sA.last(e[e.length-1]);{const s=n+o,a=e[s];return r(a)?sA.middle(t,a):aA(e,t,s,o,r)}},iA=(e,t)=>oA(e,t).bind((t=>{const n=nA(t,"th,td",M);return J(n,(t=>vn(e,t))).map((e=>({index:e,all:n})))}));var lA=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];const dA=(e,t)=>({element:e,offset:t}),cA=(e,t)=>{if(e.property().isText(t))return dA(t,e.property().getText(t).length);{const n=e.property().children(t);return n.length>0?cA(e,n[n.length-1]):dA(t,n.length)}},mA=(e,t,n)=>{const o=e.property().children(t);return o.length>0&&n<o.length?mA(e,o[n],0):o.length>0&&e.property().isElement(t)&&o.length===n?cA(e,o[o.length-1]):dA(t,n)},uA=mA,fA={up:N({selector:ir,closest:dr,predicate:rr,all:In}),down:N({selector:xr,predicate:Er}),styles:N({get:Uo,getRaw:jo,set:Io,remove:Ho}),attrs:N({get:vo,set:ho,remove:wo,copyTo:(e,t)=>{const n=xo(e);bo(t,n)}}),insert:N({before:io,after:lo,afterAll:fo,append:mo,appendAll:go,prepend:co,wrap:uo}),remove:N({unwrap:ko,remove:_o}),create:N({nu:mn.fromTag,clone:e=>mn.fromDom(e.dom.cloneNode(!1)),text:mn.fromText}),query:N({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:Un,nextSibling:zn}),property:N({children:Hn,name:En,parent:Ln,document:e=>Bn(e).dom,isText:An,isComment:_n,isElement:Nn,isSpecial:e=>{const t=En(e);return $(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>Nn(e)?yo(e,"lang"):I.none(),getText:er,setText:tr,isBoundary:e=>!!Nn(e)&&("body"===En(e)||$(lA,En(e))),isEmptyTag:e=>!!Nn(e)&&$(["br","img","hr","input"],En(e)),isNonEditable:e=>Nn(e)&&"false"===vo(e,"contenteditable")}),eq:vn,is:Cn},gA=Pe("image"),pA=Pe("event"),hA=e=>t=>{t[pA]=e},bA=hA(0),vA=hA(2),yA=hA(1),CA=e=>{const t=e;return I.from(t[pA]).exists((e=>0===e))};const wA=Pe("mode"),EA=e=>t=>{t[wA]=e},xA=(e,t)=>EA(t)(e),SA=EA(0),_A=EA(2),kA=EA(1),NA=e=>t=>{const n=t;return I.from(n[wA]).exists((t=>t===e))},AA=NA(0),RA=NA(1),DA=["none","copy","link","move"],TA=["none","copy","copyLink","copyMove","link","linkMove","move","all","uninitialized"],OA=()=>{const e=new window.DataTransfer;let t="move",n="all";const o={get dropEffect(){return t},set dropEffect(e){$(DA,e)&&(t=e)},get effectAllowed(){return n},set effectAllowed(e){CA(o)&&$(TA,e)&&(n=e)},get items(){return((e,t)=>({...t,get length(){return t.length},add:(n,o)=>{if(AA(e)){if(!u(n))return t.add(n);if(!v(o))return t.add(n,o)}return null},remove:n=>{AA(e)&&t.remove(n)},clear:()=>{AA(e)&&t.clear()}}))(o,e.items)},get files(){return RA(o)?Object.freeze({length:0,item:e=>null}):e.files},get types(){return e.types},setDragImage:(t,n,r)=>{var s;AA(o)&&(s={image:t,x:n,y:r},o[gA]=s,e.setDragImage(t,n,r))},getData:t=>RA(o)?"":e.getData(t),setData:(t,n)=>{AA(o)&&e.setData(t,n)},clearData:t=>{AA(o)&&e.clearData(t)}};return SA(o),o},BA=(e,t)=>e.setData("text/html",t),PA=(e,t,n,o,r)=>{const s=xr(mn.fromDom(n),"td,th,caption").map((e=>e.dom)),a=Y(((e,t)=>te(t,(t=>{const n=((e,t)=>({left:e.left-t,top:e.top-t,right:e.right+-2,bottom:e.bottom+-2,width:e.width+t,height:e.height+t}))(tl(t.getBoundingClientRect()),-1);return[{x:n.left,y:e(n),cell:t},{x:n.right,y:e(n),cell:t}]})))(e,s),(e=>t(e,r)));return((e,t,n)=>X(e,((e,o)=>e.fold((()=>I.some(o)),(e=>{const r=Math.sqrt(Math.abs(e.x-t)+Math.abs(e.y-n)),s=Math.sqrt(Math.abs(o.x-t)+Math.abs(o.y-n));return I.some(s<r?o:e)}))),I.none()))(a,o,r).map((e=>e.cell))},LA=D(PA,(e=>e.bottom),((e,t)=>e.y<t)),MA=D(PA,(e=>e.top),((e,t)=>e.y>t)),IA=(e,t,n)=>{const o=e(t,n);return(e=>e.breakType===mk.Wrap&&0===e.positions.length)(o)||!ms(n.getNode())&&(e=>e.breakType===mk.Br&&1===e.positions.length)(o)?!((e,t,n)=>n.breakAt.exists((n=>e(t,n).breakAt.isSome())))(e,t,o):o.breakAt.isNone()},FA=D(IA,yk),UA=D(IA,Ck),zA=(e,t,n,o)=>{const r=e.selection.getRng(),s=t?1:-1;return!(!jm()||!((e,t,n)=>{const o=Ul.fromRangeStart(t);return Pu(!e,n).exists((e=>e.isEqual(o)))})(t,r,n)||(p_(s,e,n,!t,!1).each((t=>{zk(e,t)})),0))},jA=(e,t,n)=>{const o=((e,t)=>{const n=t.getNode(e);return ns(n)?I.some(n):I.none()})(!!t,n),r=!1===t;o.fold((()=>zk(e,n.toRange())),(o=>Pu(r,e.getBody()).filter((e=>e.isEqual(n))).fold((()=>zk(e,n.toRange())),(n=>((e,t,n)=>{t.undoManager.transact((()=>{const o=e?lo:io,r=BN(t,mn.fromDom(n),o);zk(t,r)}))})(t,e,o)))))},$A=(e,t,n,o)=>{const r=e.selection.getRng(),s=Ul.fromRangeStart(r),a=e.getBody();if(!t&&FA(o,s)){const o=((e,t,n)=>((e,t)=>le(t.getClientRects()).bind((t=>LA(e,t.left,t.top))).bind((e=>{return vk(Fu(n=e).map((e=>yk(n,e).positions.concat(e))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>bk(wk(e,Ul.before(t)),n.left))))).getOr(Ul.before(t)))(a,n,s);return jA(e,t,o),!0}if(t&&UA(o,s)){const o=((e,t,n)=>((e,t)=>de(t.getClientRects()).bind((t=>MA(e,t.left,t.top))).bind((e=>{return vk(Iu(n=e).map((e=>[e].concat(Ck(n,e).positions))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>bk(Ek(e,Ul.after(t)),n.left))))).getOr(Ul.after(t)))(a,n,s);return jA(e,t,o),!0}return!1},HA=(e,t,n)=>I.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind((o=>I.from(e.dom.getParent(o,"table")).map((r=>n(e,t,r,o))))).getOr(!1),VA=(e,t)=>HA(e,t,zA),qA=(e,t)=>HA(e,t,$A),WA=(e,t,n)=>n.fold(I.none,I.none,((e,t)=>{return(n=t,ar(n,Dr)).map((e=>(e=>{const t=Lr.exact(e,0,e,0);return Ur(t)})(e)));var n}),(n=>!e.mode.isReadOnly()&&KA(n)&&(e=>XA(e)||Fn(e).some((e=>kn(e)&&XA(e))))(n)?(e.execCommand("mceTableInsertRowAfter"),YA(e,t,n)):I.none())),KA=e=>sr(e,Tn("table")).exists(vr),YA=(e,t,n)=>{return WA(e,t,(r=XA,iA(o=n,void 0).fold((()=>sA.none(o)),(e=>aA(e.all,o,e.index,1,r)))));var o,r},GA=(e,t,n)=>{return WA(e,t,(r=XA,iA(o=n,void 0).fold((()=>sA.none()),(e=>aA(e.all,o,e.index,-1,r)))));var o,r},XA=e=>vr(e)||kr(e,QA),QA=e=>kn(e)&&vr(e),ZA=(e,t)=>{const n=["table","li","dl"],o=mn.fromDom(e.getBody()),r=e=>{const t=En(e);return vn(e,o)||$(n,t)},s=e.selection.getRng();return((e,t)=>((e,t,n=L)=>n(t)?I.none():$(e,En(t))?I.some(t):ir(t,e.join(","),(e=>hn(e,"table")||n(e))))(["td","th"],e,t))(mn.fromDom(t?s.endContainer:s.startContainer),r).map((n=>(oA(n,r).each((t=>{e.model.table.clearSelectedCells(t.dom)})),e.selection.collapse(!t),(t?YA:GA)(e,r,n).each((t=>{e.selection.setRng(t)})),!0))).getOr(!1)},JA=(e,t)=>({container:e,offset:t}),eR=li.DOM,tR=e=>t=>e===t?-1:0,nR=(e,t,n)=>{if(ss(e)&&t>=0)return I.some(JA(e,t));{const o=Ai(eR);return I.from(o.backwards(e,t,tR(e),n)).map((e=>JA(e.container,e.container.data.length)))}},oR=(e,t,n)=>{if(!ss(e))return I.none();const o=e.data;if(t>=0&&t<=o.length)return I.some(JA(e,t));{const o=Ai(eR);return I.from(o.backwards(e,t,tR(e),n)).bind((e=>{const o=e.container.data;return oR(e.container,t+o.length,n)}))}},rR=(e,t,n)=>{if(!ss(e))return I.none();const o=e.data;if(t<=o.length)return I.some(JA(e,t));{const r=Ai(eR);return I.from(r.forwards(e,t,tR(e),n)).bind((e=>rR(e.container,t-o.length,n)))}},sR=(e,t,n,o,r)=>{const s=Ai(e,(e=>t=>e.isBlock(t)||$(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===e.getContentEditable(t))(e));return I.from(s.backwards(t,n,o,r))},aR=e=>""!==e&&-1!==" \xa0\ufeff\f\n\r\t\v".indexOf(e),iR=(e,t)=>e.substring(t.length),lR=(e,t,n,o=!1)=>{if(!(r=t).collapsed||!ss(r.startContainer))return I.none();var r;const s={text:"",offset:0},a=e.getParent(t.startContainer,e.isBlock)||e.getRoot();return sR(e,t.startContainer,t.startOffset,((e,t,r)=>(s.text=r+s.text,s.offset+=t,((e,t,n,o=!1)=>{let r;const s=n.charAt(0);for(r=t-1;r>=0;r--){const a=e.charAt(r);if(!o&&aR(a))return I.none();if(s===a&&Ge(e,n,r,t))break}return I.some(r)})(s.text,s.offset,n,o).getOr(t))),a).bind((e=>{const o=t.cloneRange();if(o.setStart(e.container,e.offset),o.setEnd(t.endContainer,t.endOffset),o.collapsed)return I.none();const r=(e=>ji(e.toString().replace(/\u00A0/g," ")))(o);return 0!==r.lastIndexOf(n)?I.none():I.some({text:iR(r,n),range:o,trigger:n})}))},dR=e=>{if((e=>3===e.nodeType)(e))return JA(e,e.data.length);{const t=e.childNodes;return t.length>0?dR(t[t.length-1]):JA(e,t.length)}},cR=(e,t)=>{const n=e.childNodes;return n.length>0&&t<n.length?cR(n[t],0):n.length>0&&(e=>1===e.nodeType)(e)&&n.length===t?dR(n[n.length-1]):JA(e,t)},mR=(e,t,n,o={})=>{var r;const s=t(),a=null!==(r=e.selection.getRng().startContainer.nodeValue)&&void 0!==r?r:"",i=Y(s.lookupByTrigger(n.trigger),(t=>n.text.length>=t.minChars&&t.matches.getOrThunk((()=>(e=>t=>{const n=cR(t.startContainer,t.startOffset);return!((e,t)=>{var n;const o=null!==(n=e.getParent(t.container,e.isBlock))&&void 0!==n?n:e.getRoot();return sR(e,t.container,t.offset,((e,t)=>0===t?-1:t),o).filter((e=>{const t=e.container.data.charAt(e.offset-1);return!aR(t)})).isSome()})(e,n)})(e.dom)))(n.range,a,n.text)));if(0===i.length)return I.none();const l=Promise.all(V(i,(e=>e.fetch(n.text,e.maxResults,o).then((t=>({matchText:n.text,items:t,columns:e.columns,onAction:e.onAction,highlightOn:e.highlightOn}))))));return I.some({lookupData:l,context:n})};var uR;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(uR||(uR={}));const fR=(e,t,n)=>e.stype===uR.Error?t(e.serror):n(e.svalue),gR=e=>({stype:uR.Value,svalue:e}),pR=e=>({stype:uR.Error,serror:e}),hR=fR,bR=e=>f(e)&&fe(e).length>100?" removed due to size":JSON.stringify(e,null,2),vR=(e,t)=>pR([{path:e,getErrorInfo:t}]),yR=e=>({extract:(t,n)=>{return o=e(n),r=e=>((e,t)=>vR(e,N(t)))(t,e),o.stype===uR.Error?r(o.serror):o;var o,r},toString:N("val")}),CR=yR(gR),wR=N(CR),ER=(e,t)=>yR((n=>{const o=typeof n;return e(n)?gR(n):pR(`Expected type: ${t} but got: ${o}`)})),xR=ER(E,"number"),SR=ER(u,"string");ER(b,"boolean");const _R=ER(w,"function"),kR=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>kR(e[t])));default:return!1}};yR((e=>kR(e)?gR(e):pR("Expected value to be acceptable for sending via postMessage")));const NR=e=>({tag:"defaultedThunk",process:N(e)}),AR=(e,t,n)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return n(e.newKey,e.instantiator)}},RR=e=>{const t=(e=>{const t=[],n=[];return q(e,(e=>{fR(e,(e=>n.push(e)),(e=>t.push(e)))})),{values:t,errors:n}})(e);return t.errors.length>0?(n=t.errors,_(pR,ee)(n)):gR(t.values);var n},DR=(e,t,n,o)=>o(xe(e,t).getOrThunk((()=>n(e)))),TR=(e,t,n,o,r)=>{const s=e=>r.extract(t.concat([o]),e),a=e=>e.fold((()=>gR(I.none())),(e=>{const n=r.extract(t.concat([o]),e);return s=n,a=I.some,s.stype===uR.Value?{stype:uR.Value,svalue:a(s.svalue)}:s;var s,a}));switch(e.tag){case"required":return((e,t,n,o)=>xe(t,n).fold((()=>((e,t,n)=>vR(e,(()=>'Could not find valid *required* value for "'+t+'" in '+bR(n))))(e,n,t)),o))(t,n,o,s);case"defaultedThunk":return DR(n,o,e.process,s);case"option":return((e,t,n)=>n(xe(e,t)))(n,o,a);case"defaultedOptionThunk":return((e,t,n,o)=>o(xe(e,t).map((t=>!0===t?n(e):t))))(n,o,e.process,a);case"mergeWithThunk":return DR(n,o,N({}),(t=>{const o=Ie(e.process(n),t);return s(o)}))}},OR=e=>({extract:(t,n)=>((e,t,n)=>{const o={},r=[];for(const s of n)AR(s,((n,s,a,i)=>{const l=TR(a,e,t,n,i);hR(l,(e=>{r.push(...e)}),(e=>{o[s]=e}))}),((e,n)=>{o[e]=n(t)}));return r.length>0?pR(r):gR(o)})(t,n,e),toString:()=>{const t=V(e,(e=>AR(e,((e,t,n,o)=>e+" -> "+o.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),BR=(e,t,n)=>{return o=((e,t,n)=>((e,t)=>e.stype===uR.Error?{stype:uR.Error,serror:t(e.serror)}:e)(t.extract([e],n),(e=>({input:n,errors:e}))))(e,t,n),fR(o,De.error,De.value);var o},PR=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:N("... (only showing first ten failures)")}]):e;return V(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+bR(e.input),LR=(e,t,n,o)=>({tag:"field",key:e,newKey:t,presence:n,prop:o}),MR=(e,t)=>LR(e,e,{tag:"required",process:{}},t),IR=e=>MR(e,SR),FR=e=>MR(e,_R),UR=(e,t)=>LR(e,e,{tag:"option",process:{}},t),zR=e=>UR(e,SR),jR=(e,t,n)=>LR(e,e,NR(t),n),$R=(e,t)=>jR(e,t,xR),HR=IR("type"),VR=FR("fetch"),qR=FR("onAction");zR("name"),zR("text"),zR("role"),zR("icon"),zR("url"),zR("tooltip"),zR("chevronTooltip"),zR("label"),zR("shortcut");const WR=OR([HR,IR("trigger"),$R("minChars",1),(e=>LR(e,e,NR(1),wR()))("columns"),$R("maxResults",10),UR("matches",_R),VR,qR,(KR=SR,jR("highlightOn",[],(YR=KR,{extract:(e,t)=>{const n=V(t,((t,n)=>YR.extract(e.concat(["["+n+"]"]),t)));return RR(n)},toString:()=>"array("+YR.toString()+")"})))]);var KR,YR;const GR=e=>{const t=We(),n=Ne(!1),o=t.isSet,r=()=>{o()&&((e=>{e.dispatch("AutocompleterEnd")})(e),n.set(!1),t.clear())},s=it((()=>(e=>{const t=e.ui.registry.getAll().popups,n=he(t,(e=>{return(t=e,BR("Autocompleter",WR,t)).fold((e=>{throw new Error(PR(e))}),A);var t})),o=mt(we(n,(e=>e.trigger))),r=Ee(n);return{dataset:n,triggers:o,lookupByTrigger:e=>Y(r,(t=>t.trigger===e))}})(e))),a=a=>{(n=>t.get().map((t=>lR(e.dom,e.selection.getRng(),t.trigger,!0).bind((t=>mR(e,s,t,n))))).getOrThunk((()=>((e,t)=>{const n=t(),o=e.selection.getRng();return((e,t,n)=>me(n.triggers,(n=>lR(e,t,n))))(e.dom,o,n).bind((n=>mR(e,t,n)))})(e,s))))(a).fold(r,(r=>{(e=>{o()||t.set({trigger:e.trigger,matchLength:e.text.length})})(r.context),r.lookupData.then((o=>{t.get().map((s=>{const a=r.context;s.trigger===a.trigger&&(t.set({...s,matchLength:a.text.length}),n.get()?(yd(e,{range:a.range}),((e,t)=>{e.dispatch("AutocompleterUpdate",t)})(e,{lookupData:o})):(n.set(!0),yd(e,{range:a.range}),((e,t)=>{e.dispatch("AutocompleterStart",t)})(e,{lookupData:o})))}))}))}))},i=()=>t.get().bind((({trigger:t})=>{const o=e.selection.getRng();return lR(e.dom,o,t,n.get()).filter((({range:e})=>((e,t)=>{const n=e.compareBoundaryPoints(window.Range.START_TO_START,t),o=e.compareBoundaryPoints(window.Range.END_TO_END,t);return n>=0&&o<=0})(o,e))).map((({range:e})=>e))}));e.addCommand("mceAutocompleterReload",((e,t)=>{const n=f(t)?t.fetchOptions:{};a(n)})),e.addCommand("mceAutocompleterClose",r),e.addCommand("mceAutocompleterRefreshActiveRange",(()=>{i().each((t=>{yd(e,{range:t})}))})),e.editorCommands.addQueryStateHandler("mceAutoCompleterInRange",(()=>i().isSome())),((e,t)=>{const n=at(t.load,50);e.on("input",(t=>{("insertCompositionText"!==t.inputType||e.composing)&&n.throttle()})),e.on("keydown",(e=>{const o=e.which;8===o?n.throttle():27===o?(n.cancel(),t.cancelIfNecessary()):38!==o&&40!==o||n.cancel()}),!0),e.on("remove",n.cancel)})(e,{cancelIfNecessary:r,load:a})},XR=Gt().browser.isSafari(),QR=e=>Fi(mn.fromDom(e)),ZR=(e,t)=>{var n;return 0===e.startOffset&&e.endOffset===(null===(n=t.textContent)||void 0===n?void 0:n.length)},JR=(e,t)=>I.from(e.getParent(t.container(),"details")),eD=(e,t)=>JR(e,t).isSome(),tD=(e,t)=>{const n=t.getNode();v(n)||e.selection.setCursorLocation(n,t.offset())},nD=(e,t,n)=>{const o=e.dom.getParent(t.container(),"details");if(o&&!o.open){const t=e.dom.select("summary",o)[0];t&&(n?Iu(t):Fu(t)).each((t=>tD(e,t)))}else tD(e,t)},oD=(e,t,n)=>{const{dom:o,selection:r}=e,s=e.getBody();if("character"===n){const n=Ul.fromRangeStart(r.getRng()),a=o.getParent(n.container(),o.isBlock),i=JR(o,n),l=a&&o.isEmpty(a),d=h(null==a?void 0:a.previousSibling),c=h(null==a?void 0:a.nextSibling);return!!(l&&(t?c:d)&&Ou(!t,s,n).exists((e=>eD(o,e)&&!ze(i,JR(o,e)))))||Ou(t,s,n).fold(L,(n=>{const r=JR(o,n);if(eD(o,n)&&!ze(i,r)){if(t||nD(e,n,!1),a&&l){if(t&&d)return!0;if(!t&&c)return!0;nD(e,n,t),e.dom.remove(a)}return!0}return!1}))}return!1},rD=(e,t,n,o)=>{const r=e.selection.getRng(),s=Ul.fromRangeStart(r),a=e.getBody();return"selection"===o?((e,t)=>{const n=t.startSummary.exists((t=>t.contains(e.startContainer))),o=t.startSummary.exists((t=>t.contains(e.endContainer))),r=t.startDetails.forall((e=>t.endDetails.forall((t=>e!==t))));return(n||o)&&!(n&&o)||r})(r,t):n?((e,t)=>t.startSummary.exists((t=>((e,t)=>Fu(t).exists((n=>ms(n.getNode())&&Mu(t,n).exists((t=>t.isEqual(e)))||n.isEqual(e))))(e,t))))(s,t)||((e,t,n)=>n.startDetails.exists((n=>Lu(e,t).forall((e=>!n.contains(e.container()))))))(a,s,t):((e,t)=>t.startSummary.exists((t=>((e,t)=>Iu(t).exists((t=>t.isEqual(e))))(e,t))))(s,t)||((e,t)=>t.startDetails.exists((n=>Mu(n,e).forall((n=>t.startSummary.exists((t=>!t.contains(e.container())&&t.contains(n.container()))))))))(s,t)},sD=(e,t,n)=>((e,t,n)=>((e,t)=>{const n=I.from(e.getParent(t.startContainer,"details")),o=I.from(e.getParent(t.endContainer,"details"));if(n.isSome()||o.isSome()){const t=n.bind((t=>I.from(e.select("summary",t)[0])));return I.some({startSummary:t,startDetails:n,endDetails:o})}return I.none()})(e.dom,e.selection.getRng()).fold((()=>oD(e,t,n)),(o=>rD(e,o,t,n)||oD(e,t,n))))(e,t,n)||XR&&((e,t,n)=>{const o=e.selection,r=o.getNode(),s=o.getRng(),a=Ul.fromRangeStart(s);return!!xs(r)&&("selection"===n&&ZR(s,r)||cb(t,a,r)?QR(r):e.undoManager.transact((()=>{const s=o.getSel();let{anchorNode:a,anchorOffset:i,focusNode:l,focusOffset:d}=null!=s?s:{};const c=()=>{C(a)&&C(i)&&C(l)&&C(d)&&(null==s||s.setBaseAndExtent(a,i,l,d))},m=(e,t)=>{q(e.childNodes,(e=>{bf(e)&&t.appendChild(e)}))},u=e.dom.create("span",{"data-mce-bogus":"1"});m(r,u),r.appendChild(u),c(),"word"!==n&&"line"!==n||null==s||s.modify("extend",t?"right":"left",n),!o.isCollapsed()&&ZR(o.getRng(),u)?QR(r):(e.execCommand(t?"ForwardDelete":"Delete"),a=null==s?void 0:s.anchorNode,i=null==s?void 0:s.anchorOffset,l=null==s?void 0:s.focusNode,d=null==s?void 0:s.focusOffset,m(u,r),c()),e.dom.remove(u)})),!0)})(e,t,n)?I.some(S):I.none(),aD=e=>(t,n,o={})=>{const r=t.getBody(),s={bubbles:!0,composed:!0,data:null,isComposing:!1,detail:0,view:null,target:r,currentTarget:r,eventPhase:Event.AT_TARGET,originalTarget:r,explicitOriginalTarget:r,isTrusted:!1,srcElement:r,cancelable:!1,preventDefault:S,inputType:n},a=qa(new InputEvent(e));return t.dispatch(e,{...a,...s,...o})},iD=aD("input"),lD=aD("beforeinput"),dD=Gt(),cD=dD.os,mD=cD.isMacOS()||cD.isiOS(),uD=dD.browser.isFirefox(),fD=(e,t)=>{const n=e.dom,o=e.schema.getMoveCaretBeforeOnEnterElements();if(!t)return;if(/^(LI|DT|DD)$/.test(t.nodeName)){const e=e=>/^(ul|ol|dl)$/.test(En(e)),o=t=>e(t)?I.from(t):ar(t,e),r=e=>n.isEmpty(e.dom);(e=>{for(;e;){if(Xr(e)||ss(e)&&e.data&&/[\r\n\s]/.test(e.data))return I.from(mn.fromDom(e));e=e.nextSibling}return I.none()})(t.firstChild).each((e=>{o(e).fold((()=>{if(r(e)){const t=(e=>uA(fA,e,0))(e).element;Di(t)||mo(t,mn.fromText(dt))}}),(e=>{io(e,mn.fromText(dt))}))}))}const r=n.createRng();if(t.normalize(),t.hasChildNodes()){const e=new Hr(t,t);let n,s=t;for(;n=e.current();){if(ss(n)){r.setStart(n,0),r.setEnd(n,0);break}if(o[n.nodeName.toLowerCase()]){r.setStartBefore(n),r.setEndBefore(n);break}s=n,n=e.next()}n||(r.setStart(s,0),r.setEnd(s,0))}else ms(t)?t.nextSibling&&n.isBlock(t.nextSibling)?(r.setStartBefore(t),r.setEndBefore(t)):(r.setStartAfter(t),r.setEndAfter(t)):(r.setStart(t,0),r.setEnd(t,0));e.selection.setRng(r),up(e,r)},gD=(e,t)=>{const n=e.getRoot();let o,r=t;for(;r!==n&&r&&"false"!==e.getContentEditable(r);){if("true"===e.getContentEditable(r)){o=r;break}r=r.parentNode}return r!==n?o:n},pD=e=>I.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock)),hD=e=>{e.innerHTML='<br data-mce-bogus="1">'},bD=(e,t)=>{Ld(e).toLowerCase()===t.tagName.toLowerCase()&&((e,t,n)=>{const o=e.dom;I.from(n.style).map(o.parseStyle).each((e=>{const n={...$o(mn.fromDom(t)),...e};o.setStyles(t,n)}));const r=I.from(n.class).map((e=>e.split(/\s+/))),s=I.from(t.className).map((e=>Y(e.split(/\s+/),(e=>""!==e))));je(r,s,((e,n)=>{const r=Y(n,(t=>!$(e,t))),s=[...e,...r];o.setAttrib(t,"class",s.join(" "))}));const a=["style","class"],i=Ce(n,((e,t)=>!$(a,t)));o.setAttribs(t,i)})(e,t,Md(e))},vD=(e,t,n,o,r=!0,s,a)=>{const i=e.dom,l=e.schema,d=Ld(e),c=n?n.nodeName.toUpperCase():"";let m=t;const u=l.getTextInlineElements();let f;f=s||"TABLE"===c||"HR"===c?i.create(s||d,a||{}):n.cloneNode(!1);let g=f;if(r){do{if(u[m.nodeName]){if(zu(m)||Ju(m))continue;const e=m.cloneNode(!1);i.setAttrib(e,"id",""),f.hasChildNodes()?(e.appendChild(f.firstChild),f.appendChild(e)):(g=e,f.appendChild(e))}}while((m=m.parentNode)&&m!==o);((e,t)=>{const n=mn.fromDom(e),o=mn.fromDom(t),r=Tn("span"),s=D(vn,n),a=e=>Nn(e)&&jo(e,"font-size").isSome(),i=[...a(o)?[o]:[],...wr(o,a,s)];q(i.slice(1),(e=>{Ho(e,"font-size"),wo(e,"data-mce-style"),r(e)&&Eo(e)&&ko(e)}))})(f,g)}else i.setAttrib(f,"style",null),i.setAttrib(f,"class",null);return bD(e,f),hD(g),f},yD=(e,t)=>{const n=null==e?void 0:e.parentNode;return C(n)&&n.nodeName===t},CD=e=>C(e)&&/^(OL|UL|LI)$/.test(e.nodeName),wD=e=>C(e)&&/^(LI|DT|DD)$/.test(e.nodeName),ED=e=>{const t=e.parentNode;return wD(t)?t:e},xD=(e,t,n)=>{let o=e[n?"firstChild":"lastChild"];for(;o&&!Xr(o);)o=o[n?"nextSibling":"previousSibling"];return o===t},SD=e=>X(we($o(mn.fromDom(e)),((e,t)=>`${t}: ${e};`)),((e,t)=>e+t),""),_D=(e,t)=>t&&"A"===t.nodeName&&e.isEmpty(t),kD=(e,t)=>e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t,ND=(e,t)=>C(t)&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&e.isEditable(t.parentNode)&&"false"!==e.getContentEditable(t),AD=(e,t,n)=>ss(t)?e?1===n&&t.data.charAt(n-1)===Ui?0:n:n===t.data.length-1&&t.data.charAt(n)===Ui?t.data.length:n:n,RD={insert:(e,t)=>{let n,o,r,s,a=!1;const i=e.dom,l=e.schema.getNonEmptyElements(),d=e.selection.getRng(),c=Ld(e),m=mn.fromDom(d.startContainer),f=Vn(m,d.startOffset),g=f.exists((e=>kn(e)&&!vr(e))),p=d.collapsed&&g,b=(t,o)=>vD(e,n,_,S,zd(e),t,o),v=e=>{const t=AD(e,n,o);if(ss(n)&&(e?t>0:t<n.data.length))return!1;if((n.parentNode===_||n===_)&&a&&!e)return!0;if(e&&Xr(n)&&n===_.firstChild)return!0;if(kD(n,"TABLE")||kD(n,"HR"))return(e=>"BR"===e.nodeName||e.nextSibling&&"BR"===e.nextSibling.nodeName)(n)?!e:a&&!e||!a&&e;const r=new Hr(n,_);let s;for(ss(n)&&(e&&0===t?r.prev():e||t!==n.data.length||r.next());s=r.current();){if(Xr(s)){if(!s.getAttribute("data-mce-bogus")){const e=s.nodeName.toLowerCase();if(l[e]&&"br"!==e)return!1}}else if(ss(s)&&!qr(s.data))return!1;e?r.prev():r.next()}return!0},w=()=>{let t;return t=/^(H[1-6]|PRE|FIGURE)$/.test(r)&&"HGROUP"!==k?b(c):b(),((e,t)=>{const n=jd(e);return!y(t)&&(u(n)?$(dn.explode(n),t.nodeName.toLowerCase()):n)})(e,s)&&ND(i,s)&&i.isEmpty(_,void 0,{includeZwsp:!0})?t=i.split(s,_):i.insertAfter(t,_),fD(e,t),t};Kg(i,d).each((e=>{d.setStart(e.startContainer,e.startOffset),d.setEnd(e.endContainer,e.endOffset)})),n=d.startContainer,o=d.startOffset;const E=!(!t||!t.shiftKey),x=!(!t||!t.ctrlKey);Xr(n)&&n.hasChildNodes()&&!p&&(a=o>n.childNodes.length-1,n=n.childNodes[Math.min(o,n.childNodes.length-1)]||n,o=a&&ss(n)?n.data.length:0);const S=gD(i,n);if(!S||((e,t)=>{const n=e.dom.getParent(t,"ol,ul,dl");return null!==n&&"false"===e.dom.getContentEditableParent(n)})(e,n))return;E||(n=((e,t,n,o,r)=>{var s,a;const i=e.dom,l=null!==(s=gD(i,o))&&void 0!==s?s:i.getRoot();let d=i.getParent(o,i.isBlock);if(!d||!ND(i,d)){if(d=d||l,!d.hasChildNodes()){const o=i.create(t);return bD(e,o),d.appendChild(o),n.setStart(o,0),n.setEnd(o,0),o}let s,c=o;for(;c&&c.parentNode!==d;)c=c.parentNode;for(;c&&!i.isBlock(c);)s=c,c=c.previousSibling;const m=null===(a=null==s?void 0:s.parentElement)||void 0===a?void 0:a.nodeName;if(s&&m&&e.schema.isValidChild(m,t.toLowerCase())){const a=s.parentNode,l=i.create(t);for(bD(e,l),a.insertBefore(l,s),c=s;c&&!i.isBlock(c);){const e=c.nextSibling;l.appendChild(c),c=e}n.setStart(o,r),n.setEnd(o,r)}}return o})(e,c,d,n,o));let _=i.getParent(n,i.isBlock)||i.getRoot();s=C(null==_?void 0:_.parentNode)?i.getParent(_.parentNode,i.isBlock):null,r=_?_.nodeName.toUpperCase():"";const k=s?s.nodeName.toUpperCase():"";if("LI"!==k||x||(_=s,s=s.parentNode,r=k),Xr(s)&&((e,t,n)=>!t&&n.nodeName.toLowerCase()===Ld(e)&&e.dom.isEmpty(n)&&((t,n)=>{let o=n;for(;o&&o!==t&&h(o.nextSibling);){const t=o.parentElement;if(!t||(r=t,!Se(e.schema.getTextBlockElements(),r.nodeName.toLowerCase())))return Es(t);o=t}var r;return!1})(e.getBody(),n))(e,E,_))return((e,t,n)=>{var o,r,s;const a=t(Ld(e)),i=((e,t)=>e.dom.getParent(t,Es))(e,n);i&&(e.dom.insertAfter(a,i),fD(e,a),(null!==(s=null===(r=null===(o=n.parentElement)||void 0===o?void 0:o.childNodes)||void 0===r?void 0:r.length)&&void 0!==s?s:0)>1&&e.dom.remove(n))})(e,b,_);if(/^(LI|DT|DD)$/.test(r)&&Xr(s)&&i.isEmpty(_))return void((e,t,n,o,r)=>{const s=e.dom,a=e.selection.getRng(),i=n.parentNode;if(n===e.getBody()||!i)return;var l;CD(l=n)&&CD(l.parentNode)&&(r="LI");const d=wD(o)?SD(o):void 0;let c=wD(o)&&d?t(r,{style:SD(o)}):t(r);if(xD(n,o,!0)&&xD(n,o,!1))if(yD(n,"LI")){const e=ED(n);s.insertAfter(c,e),(e=>{var t;return(null===(t=e.parentNode)||void 0===t?void 0:t.firstChild)===e})(n)?s.remove(e):s.remove(n)}else s.replace(c,n);else if(xD(n,o,!0))yD(n,"LI")?(s.insertAfter(c,ED(n)),c.appendChild(s.doc.createTextNode(" ")),c.appendChild(n)):i.insertBefore(c,n),s.remove(o);else if(xD(n,o,!1))s.insertAfter(c,ED(n)),s.remove(o);else{n=ED(n);const e=a.cloneRange();e.setStartAfter(o),e.setEndAfter(n);const t=e.extractContents();if("LI"===r&&(e=>e.firstChild&&"LI"===e.firstChild.nodeName)(t)){const e=Y(V(c.children,mn.fromDom),T(Tn("br")));c=t.firstChild,s.insertAfter(t,n),q(e,(e=>co(mn.fromDom(c),e))),d&&c.setAttribute("style",d)}else s.insertAfter(t,n),s.insertAfter(c,n);s.remove(o)}fD(e,c)})(e,b,s,_,c);if(!(p||_!==e.getBody()&&ND(i,_)))return;const N=_.parentNode;let A;if(p)A=b(c),f.fold((()=>{mo(m,mn.fromDom(A))}),(e=>{io(e,mn.fromDom(A))})),e.selection.setCursorLocation(A,0);else if(Vi(_))A=Zi(_),i.isEmpty(_)&&hD(_),bD(e,A),fD(e,A);else if(v(!1))A=w();else if(v(!0)&&N){const t=Ul.fromRangeStart(d),n=ah(t),o=mn.fromDom(_),r=Eh(o,t,e.schema)?xh(o,t,e.schema).bind((e=>I.from(e.getNode()))):I.none();A=N.insertBefore(b(),_);const s=kD(_,"HR")||n?A:r.getOr(_);fD(e,s)}else{const t=(e=>{const t=e.cloneRange();return t.setStart(e.startContainer,AD(!0,e.startContainer,e.startOffset)),t.setEnd(e.endContainer,AD(!1,e.endContainer,e.endOffset)),t})(d).cloneRange();t.setEndAfter(_);const n=t.extractContents();(e=>{q(Er(mn.fromDom(e),An),(e=>{const t=e.dom;t.nodeValue=ji(t.data)}))})(n),(e=>{let t=e;do{ss(t)&&(t.data=t.data.replace(/^[\r\n]+/,"")),t=t.firstChild}while(t)})(n),A=n.firstChild,_===A?C(N)&&i.insertAfter(n,N):i.insertAfter(n,_),((e,t,n)=>{var o;const r=[];if(!n)return;let s=n;for(;s=s.firstChild;){if(e.isBlock(s))return;Xr(s)&&!t[s.nodeName.toLowerCase()]&&r.push(s)}let a=r.length;for(;a--;)s=r[a],(!s.hasChildNodes()||s.firstChild===s.lastChild&&""===(null===(o=s.firstChild)||void 0===o?void 0:o.nodeValue)||_D(e,s))&&e.remove(s)})(i,l,A),((e,t)=>{t.normalize();const n=t.lastChild;(!n||Xr(n)&&/^(left|right)$/gi.test(e.getStyle(n,"float",!0)))&&e.add(t,"br")})(i,_),i.isEmpty(_)&&hD(_),A.normalize(),i.isEmpty(A)?(i.remove(A),w()):(bD(e,A),fD(e,A))}i.setAttrib(A,"id",""),e.dispatch("NewBlock",{newBlock:A})},fakeEventName:"insertParagraph"},DD=(e,t,n)=>{const o=e.dom.createRng();n?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),e.selection.setRng(o),up(e,o)},TD=(e,t)=>{const n=mn.fromTag("br");io(mn.fromDom(t),n),e.undoManager.add()},OD=(e,t)=>{BD(e.getBody(),t)||lo(mn.fromDom(t),mn.fromTag("br"));const n=mn.fromTag("br");lo(mn.fromDom(t),n),DD(e,n.dom,!1),e.undoManager.add()},BD=(e,t)=>{return n=Ul.after(t),!!ms(n.getNode())||Lu(e,Ul.after(t)).map((e=>ms(e.getNode()))).getOr(!1);var n},PD=e=>e&&"A"===e.nodeName&&"href"in e,LD=e=>e.fold(L,PD,PD,L),MD=(e,t)=>{t.fold(S,D(TD,e),D(OD,e),S)},ID={insert:(e,t)=>{const n=(e=>{const t=D(Zh,e),n=Ul.fromRangeStart(e.selection.getRng());return nk(t,e.getBody(),n).filter(LD)})(e);n.isSome()?n.each(D(MD,e)):((e,t)=>{const n=e.selection,o=e.dom,r=n.getRng();let s,a=!1;Kg(o,r).each((e=>{r.setStart(e.startContainer,e.startOffset),r.setEnd(e.endContainer,e.endOffset)}));let i=r.startOffset,l=r.startContainer;if(Xr(l)&&l.hasChildNodes()){const e=i>l.childNodes.length-1;l=l.childNodes[Math.min(i,l.childNodes.length-1)]||l,i=e&&ss(l)?l.data.length:0}let d=o.getParent(l,o.isBlock);const c=d&&d.parentNode?o.getParent(d.parentNode,o.isBlock):null,m=c?c.nodeName.toUpperCase():"",u=!(!t||!t.ctrlKey);"LI"!==m||u||(d=c),ss(l)&&i>=l.data.length&&(((e,t,n)=>{const o=new Hr(t,n);let r;const s=e.getNonEmptyElements();for(;r=o.next();)if(s[r.nodeName.toLowerCase()]||ss(r)&&r.length>0)return!0;return!1})(e.schema,l,d||o.getRoot())||(s=o.create("br"),r.insertNode(s),r.setStartAfter(s),r.setEndAfter(s),a=!0)),s=o.create("br"),jl(o,r,s),DD(e,s,a),e.undoManager.add()})(e,t)},fakeEventName:"insertLineBreak"},FD=(e,t)=>pD(e).filter((e=>t.length>0&&hn(mn.fromDom(e),t))).isSome(),UD=ke([{br:[]},{block:[]},{none:[]}]),zD=(e,t)=>(e=>FD(e,Ud(e)))(e),jD=e=>(t,n)=>(e=>pD(e).filter((e=>Bi(mn.fromDom(e)))).isSome())(t)===e,$D=(e,t)=>(n,o)=>{const r=(e=>pD(e).fold(N(""),(e=>e.nodeName.toUpperCase())))(n)===e.toUpperCase();return r===t},HD=e=>{const t=gD(e.dom,e.selection.getStart());return y(t)},VD=e=>$D("pre",e),qD=e=>(t,n)=>Pd(t)===e,WD=(e,t)=>(e=>FD(e,Fd(e)))(e),KD=(e,t)=>t,YD=e=>{const t=Ld(e),n=gD(e.dom,e.selection.getStart());return C(n)&&e.schema.isValidChild(n.nodeName,t)},GD=e=>{const t=e.selection.getRng(),n=mn.fromDom(t.startContainer),o=Vn(n,t.startOffset).map((e=>kn(e)&&!vr(e)));return t.collapsed&&o.getOr(!0)},XD=(e,t)=>(n,o)=>X(e,((e,t)=>e&&t(n,o)),!0)?I.some(t):I.none(),QD=(e,t,n)=>{if(!t.mode.isReadOnly()){if(t.selection.isCollapsed()||(e=>{e.execCommand("delete")})(t),C(n)&&lD(t,e.fakeEventName).isDefaultPrevented())return;e.insert(t,n),C(n)&&iD(t,e.fakeEventName)}},ZD=(e,t)=>{if(e.mode.isReadOnly())return;const n=()=>QD(ID,e,t),o=()=>QD(RD,e,t),r=((e,t)=>W_([XD([zD],UD.none()),XD([VD(!0),HD],UD.none()),XD([$D("summary",!0)],UD.br()),XD([VD(!0),qD(!1),KD],UD.br()),XD([VD(!0),qD(!1)],UD.block()),XD([VD(!0),qD(!0),KD],UD.block()),XD([VD(!0),qD(!0)],UD.br()),XD([jD(!0),KD],UD.br()),XD([jD(!0)],UD.block()),XD([WD],UD.br()),XD([KD],UD.br()),XD([YD],UD.block()),XD([GD],UD.block())],[e,!(!t||!t.shiftKey)]).getOr(UD.none()))(e,t);switch(Id(e)){case"linebreak":r.fold(n,n,S);break;case"block":r.fold(o,o,S);break;case"invert":r.fold(o,n,S);break;default:r.fold(n,o,S)}},JD=Gt(),eT=JD.os.isiOS()&&JD.browser.isSafari(),tT=(e,t)=>{var n;t.isDefaultPrevented()||(t.preventDefault(),(n=e.undoManager).typing&&(n.typing=!1,n.add()),e.undoManager.transact((()=>{ZD(e,t)})))},nT=Gt(),oT=e=>e.stopImmediatePropagation(),rT=e=>e.keyCode===Bg.PAGE_UP||e.keyCode===Bg.PAGE_DOWN,sT=(e,t,n)=>{n&&!e.get()?t.on("NodeChange",oT,!0):!n&&e.get()&&t.off("NodeChange",oT),e.set(n)},aT=(e,t)=>e===t||e.contains(t),iT=(e,t)=>{const n=t.container(),o=t.offset();return ss(n)?(n.insertData(o,e),I.some(Ul(n,o+e.length))):gu(t).map((n=>{const o=mn.fromText(e);return t.isAtEnd()?lo(n,o):io(n,o),Ul(o.dom,e.length)}))},lT=D(iT,dt),dT=D(iT," "),cT=e=>t=>{e.selection.setRng(t.toRange()),e.nodeChanged()},mT=e=>{const t=Ul.fromRangeStart(e.selection.getRng()),n=mn.fromDom(e.getBody());if(e.selection.isCollapsed()){const o=D(Zh,e),r=Ul.fromRangeStart(e.selection.getRng());return nk(o,e.getBody(),r).bind((e=>t=>t.fold((t=>Mu(e.dom,Ul.before(t))),(e=>Iu(e)),(e=>Fu(e)),(t=>Lu(e.dom,Ul.after(t)))))(n)).map((o=>()=>((e,t,n)=>o=>Th(e,o,n)?lT(t):dT(t))(n,t,e.schema)(o).each(cT(e))))}return I.none()},uT=e=>{return $e(rn.browser.isFirefox()&&e.selection.isEditable()&&(t=e.dom,n=e.selection.getRng().startContainer,t.isEditable(t.getParent(n,"summary"))),(()=>{const t=mn.fromDom(e.getBody());e.selection.isCollapsed()||e.getDoc().execCommand("Delete"),((e,t,n)=>Th(e,t,n)?lT(t):dT(t))(t,Ul.fromRangeStart(e.selection.getRng()),e.schema).each(cT(e))}));var t,n},fT=e=>pm(e)?[{keyCode:Bg.TAB,action:XN(ZA,e,!0)},{keyCode:Bg.TAB,shiftKey:!0,action:XN(ZA,e,!1)}]:[],gT=e=>{if(e.addShortcut("Meta+P","","mcePrint"),GR(e),lE(e))return Ne(null);{const t=Zk(e);return(e=>{e.on("beforeinput",(t=>{e.selection.isEditable()&&!H(t.getTargetRanges(),(t=>!((e,t)=>!aT(e.getBody(),t.startContainer)||!aT(e.getBody(),t.endContainer)||fp(e.dom,t))(e,t)))||t.preventDefault()}))})(e),(e=>{e.on("keyup compositionstart",D(FN,e))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=rn.os.isMacOS()||rn.os.isiOS();QN([{keyCode:Bg.RIGHT,action:XN(jN,e,!0)},{keyCode:Bg.LEFT,action:XN(jN,e,!1)},{keyCode:Bg.UP,action:XN($N,e,!1)},{keyCode:Bg.DOWN,action:XN($N,e,!0)},...o?[{keyCode:Bg.UP,action:XN(VN,e,!1),metaKey:!0,shiftKey:!0},{keyCode:Bg.DOWN,action:XN(VN,e,!0),metaKey:!0,shiftKey:!0}]:[],{keyCode:Bg.RIGHT,action:XN(VA,e,!0)},{keyCode:Bg.LEFT,action:XN(VA,e,!1)},{keyCode:Bg.UP,action:XN(qA,e,!1)},{keyCode:Bg.DOWN,action:XN(qA,e,!0)},{keyCode:Bg.UP,action:XN(qA,e,!1)},{keyCode:Bg.UP,action:XN(KN,e,!1)},{keyCode:Bg.DOWN,action:XN(KN,e,!0)},{keyCode:Bg.RIGHT,action:XN(JN,e,!0)},{keyCode:Bg.LEFT,action:XN(JN,e,!1)},{keyCode:Bg.UP,action:XN(eA,e,!1)},{keyCode:Bg.DOWN,action:XN(eA,e,!0)},{keyCode:Bg.RIGHT,action:XN(Xk,e,t,!0)},{keyCode:Bg.LEFT,action:XN(Xk,e,t,!1)},{keyCode:Bg.RIGHT,ctrlKey:!o,altKey:o,action:XN(Jk,e,t)},{keyCode:Bg.LEFT,ctrlKey:!o,altKey:o,action:XN(eN,e,t)},{keyCode:Bg.UP,action:XN(WN,e,!1)},{keyCode:Bg.DOWN,action:XN(WN,e,!0)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{let n=!1;e.on("keydown",(o=>{n=o.keyCode===Bg.BACKSPACE,o.isDefaultPrevented()||((e,t,n)=>{const o=n.keyCode===Bg.BACKSPACE?"deleteContentBackward":"deleteContentForward",r=e.selection.isCollapsed(),s=r?"character":"selection",a=e=>r?e?"word":"line":"selection";ZN([{keyCode:Bg.BACKSPACE,action:XN(SN,e)},{keyCode:Bg.BACKSPACE,action:XN(R_,e,!1)},{keyCode:Bg.DELETE,action:XN(R_,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(w_,e,!1)},{keyCode:Bg.DELETE,action:XN(w_,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(rN,e,t,!1)},{keyCode:Bg.DELETE,action:XN(rN,e,t,!0)},{keyCode:Bg.BACKSPACE,action:XN(Fb,e,!1)},{keyCode:Bg.DELETE,action:XN(Fb,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(sD,e,!1,s)},{keyCode:Bg.DELETE,action:XN(sD,e,!0,s)},...mD?[{keyCode:Bg.BACKSPACE,altKey:!0,action:XN(sD,e,!1,a(!0))},{keyCode:Bg.DELETE,altKey:!0,action:XN(sD,e,!0,a(!0))},{keyCode:Bg.BACKSPACE,metaKey:!0,action:XN(sD,e,!1,a(!1))}]:[{keyCode:Bg.BACKSPACE,ctrlKey:!0,action:XN(sD,e,!1,a(!0))},{keyCode:Bg.DELETE,ctrlKey:!0,action:XN(sD,e,!0,a(!0))}],{keyCode:Bg.BACKSPACE,action:XN(L_,e,!1)},{keyCode:Bg.DELETE,action:XN(L_,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(hN,e,!1)},{keyCode:Bg.DELETE,action:XN(hN,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(g_,e,!1)},{keyCode:Bg.DELETE,action:XN(g_,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(m_,e,!1)},{keyCode:Bg.DELETE,action:XN(m_,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(fN,e,!1)},{keyCode:Bg.DELETE,action:XN(fN,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(P_,e,!1)},{keyCode:Bg.DELETE,action:XN(P_,e,!0)},{keyCode:Bg.BACKSPACE,action:XN(_N,e,!1)},{keyCode:Bg.DELETE,action:XN(_N,e,!0)}],n).filter((t=>e.selection.isEditable())).each((t=>{n.preventDefault(),lD(e,o).isDefaultPrevented()||(t(),iD(e,o))}))})(e,t,o)})),e.on("keyup",(t=>{t.isDefaultPrevented()||((e,t,n)=>{QN([{keyCode:Bg.BACKSPACE,action:XN(A_,e)},{keyCode:Bg.DELETE,action:XN(A_,e)},...mD?[{keyCode:Bg.BACKSPACE,altKey:!0,action:XN(gN,e)},{keyCode:Bg.DELETE,altKey:!0,action:XN(gN,e)},...n?[{keyCode:uD?224:91,action:XN(gN,e)}]:[]]:[{keyCode:Bg.BACKSPACE,ctrlKey:!0,action:XN(gN,e)},{keyCode:Bg.DELETE,ctrlKey:!0,action:XN(gN,e)}]],t)})(e,t,n),n=!1}))})(e,t),(e=>{let t=I.none();e.on("keydown",(n=>{n.keyCode===Bg.ENTER&&(eT&&(e=>{if(!e.collapsed)return!1;const t=e.startContainer;if(ss(t)){const n=/^[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]$/,o=t.data.charAt(e.startOffset-1);return n.test(o)}return!1})(e.selection.getRng())?(e=>{t=I.some(e.selection.getBookmark()),e.undoManager.add()})(e):tT(e,n))})),e.on("keyup",(n=>{n.keyCode===Bg.ENTER&&t.each((()=>((e,n)=>{e.undoManager.undo(),t.fold(S,(t=>e.selection.moveToBookmark(t))),tT(e,n),t=I.none()})(e,n)))}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{ZN([{keyCode:Bg.SPACEBAR,action:XN(mT,e)},{keyCode:Bg.SPACEBAR,action:XN(uT,e)}],t).each((n=>{t.preventDefault(),lD(e,"insertText",{data:" "}).isDefaultPrevented()||(n(),iD(e,"insertText",{data:" "}))}))})(e,t)}))})(e),(e=>{e.on("input",(t=>{t.isComposing||(e=>{const t=mn.fromDom(e.getBody());e.selection.isCollapsed()&&Uh(t,Ul.fromRangeStart(e.selection.getRng()),e.schema).each((t=>{e.selection.setRng(t.toRange())}))})(e)}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{QN([...fT(e)],t).each((e=>{t.preventDefault()}))})(e,t)}))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=rn.os.isMacOS()||rn.os.isiOS();QN([{keyCode:Bg.END,action:XN(HN,e,!0)},{keyCode:Bg.HOME,action:XN(HN,e,!1)},...o?[]:[{keyCode:Bg.HOME,action:XN(VN,e,!1),ctrlKey:!0,shiftKey:!0},{keyCode:Bg.END,action:XN(VN,e,!0),ctrlKey:!0,shiftKey:!0}],{keyCode:Bg.END,action:XN(tA,e,!0)},{keyCode:Bg.HOME,action:XN(tA,e,!1)},{keyCode:Bg.END,action:XN(tN,e,!0,t)},{keyCode:Bg.HOME,action:XN(tN,e,!1,t)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{if(nT.os.isMacOS())return;const n=Ne(!1);e.on("keydown",(t=>{rT(t)&&sT(n,e,!0)})),e.on("keyup",(o=>{o.isDefaultPrevented()||((e,t,n)=>{QN([{keyCode:Bg.PAGE_UP,action:XN(tN,e,!1,t)},{keyCode:Bg.PAGE_DOWN,action:XN(tN,e,!0,t)}],n)})(e,t,o),rT(o)&&n.get()&&(sT(n,e,!1),e.nodeChanged())}))})(e,t),t}},pT=(e,t)=>()=>{const n=Jx(e);return C(n)&&n.nodeName===t},hT=e=>3===e.type,bT=e=>0===e.length,vT=e=>{const t=(t,n)=>{const o=xp.create("li");q(t,(e=>o.append(e))),n?e.insert(o,n,!0):e.append(o)},n=X(e.children(),((e,n)=>hT(n)?[...e,n]:bT(e)||hT(n)?e:(t(e,n),[])),[]);bT(n)||t(n)},yT=e=>{(e=>{e.on("keydown",(t=>{t.keyCode===Bg.BACKSPACE?e_(e,!1)&&t.preventDefault():t.keyCode===Bg.DELETE&&e_(e,!0)&&t.preventDefault()}))})(e),(e=>{e.addCommand("InsertUnorderedList",((t,n)=>{KS(e,"UL",n)})),e.addCommand("InsertOrderedList",((t,n)=>{KS(e,"OL",n)})),e.addCommand("InsertDefinitionList",((t,n)=>{KS(e,"DL",n)})),e.addCommand("RemoveList",(()=>{LS(e)})),e.addCommand("mceListUpdate",((t,n)=>{f(n)&&((e,t)=>{const n=Jx(e);null===n||iS(e,n)||e.undoManager.transact((()=>{f(t.styles)&&e.dom.setStyles(n,t.styles),f(t.attrs)&&pe(t.attrs,((t,o)=>e.dom.setAttrib(n,o,t)))}))})(e,n)})),e.addCommand("mceListBackspaceDelete",((t,n)=>{e_(e,n)})),e.addQueryStateHandler("InsertUnorderedList",pT(e,"UL")),e.addQueryStateHandler("InsertOrderedList",pT(e,"OL")),e.addQueryStateHandler("InsertDefinitionList",pT(e,"DL"))})(e),(e=>{e.on("PreInit",(()=>{const{parser:t}=e;t.addNodeFilter("ul,ol",(e=>q(e,vT)))}))})(e),(e=>{km(e)&&(e=>{e.on("keydown",(t=>{t.keyCode!==Bg.TAB||Bg.metaKeyPressed(t)||e.undoManager.transact((()=>{(t.shiftKey?PS(e):BS(e))&&t.preventDefault()}))}))})(e)})(e)};class CT{constructor(e){let t;this.lastPath=[],this.editor=e;const n=this;"onselectionchange"in e.getDoc()||e.on("NodeChange click mouseup keyup focus",(n=>{const o=e.selection.getRng(),r={startContainer:o.startContainer,startOffset:o.startOffset,endContainer:o.endContainer,endOffset:o.endOffset};"nodechange"!==n.type&&zg(r,t)||e.dispatch("SelectionChange"),t=r})),e.on("contextmenu",(()=>{gg(e),e.dispatch("SelectionChange")})),e.on("SelectionChange",(()=>{const t=e.selection.getStart(!0);t&&ff(e)&&!n.isSameElementPath(t)&&e.dom.isChildOf(t,e.getBody())&&e.nodeChanged({selectionChange:!0})})),e.on("mouseup",(t=>{!t.isDefaultPrevented()&&ff(e)&&("IMG"===e.selection.getNode().nodeName?bg.setEditorTimeout(e,(()=>{e.nodeChanged()})):e.nodeChanged())}))}nodeChanged(e={}){const t=this.editor,n=t.selection;let o;if(t.initialized&&n&&!Dc(t)&&!xm(t)){const r=t.getBody();o=n.getStart(!0)||r,o.ownerDocument===t.getDoc()&&t.dom.isChildOf(o,r)||(o=r);const s=[];t.dom.getParent(o,(e=>e===r||(s.push(e),!1))),t.dispatch("NodeChange",{...e,element:o,parents:s})}}isSameElementPath(e){let t;const n=this.editor,o=oe(n.dom.getParents(e,M,n.getBody()));if(o.length===this.lastPath.length){for(t=o.length;t>=0&&o[t]===this.lastPath[t];t--);if(-1===t)return this.lastPath=o,!0}return this.lastPath=o,!1}}const wT="x-tinymce/html",ET=N(wT),xT="\x3c!-- "+wT+" --\x3e",ST=e=>xT+e,_T=e=>-1!==e.indexOf(xT),kT="%MCEPASTEBIN%",NT=e=>e.dom.get("mcepastebin"),AT=e=>C(e)&&"mcepastebin"===e.id,RT=e=>e===kT,DT=(e,t)=>(dn.each(t,(t=>{e=m(t,RegExp)?e.replace(t,""):e.replace(t[0],t[1])})),e),TT=e=>DT(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,(e,t,n)=>t||n?dt:" "],/<br class="Apple-interchange-newline">/g,/<br>$/i]),OT=(e,t)=>({content:e,cancelled:t}),BT=(e,t)=>(e.insertContent(t,{merge:em(e),paste:!0}),!0),PT=e=>/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(e),LT=(e,t,n)=>!(e.selection.isCollapsed()||!PT(t))&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.execCommand("mceInsertLink",!1,t)})),!0))(e,t,n),MT=(e,t,n)=>!!((e,t)=>PT(t)&&H(gm(e),(e=>Qe(t.toLowerCase(),`.${e.toLowerCase()}`))))(e,t)&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.insertContent('<img src="'+t+'">')})),!0))(e,t,n),IT=(()=>{let e=0;return()=>"mceclip"+e++})(),FT=e=>{const t=OA();return BA(t,e),_A(t),t},UT=(e,t,n,o,r)=>{const s=((e,t,n)=>((e,t,n)=>{const o=((e,t,n)=>e.dispatch("PastePreProcess",{content:t,internal:n}))(e,t,n),r=((e,t)=>{const n=VC({sanitize:um(e),sandbox_iframes:vm(e),sandbox_iframes_exclusions:ym(e),convert_unsafe_embeds:Cm(e)},e.schema);n.addNodeFilter("meta",(e=>{dn.each(e,(e=>{e.remove()}))}));const o=n.parse(t,{forced_root_block:!1,isRootContent:!0});return $p({validate:!0},e.schema).serialize(o)})(e,o.content);return e.hasEventListeners("PastePostProcess")&&!o.isDefaultPrevented()?((e,t,n)=>{const o=e.dom.create("div",{style:"display:none"},t),r=((e,t,n)=>e.dispatch("PastePostProcess",{node:t,internal:n}))(e,o,n);return OT(r.node.innerHTML,r.isDefaultPrevented())})(e,r,n):OT(r,o.isDefaultPrevented())})(e,t,n))(e,t,n);if(!s.cancelled){const t=s.content,n=()=>((e,t,n)=>{n||!tm(e)?BT(e,t):((e,t)=>{dn.each([LT,MT,BT],(n=>!n(e,t,BT)))})(e,t)})(e,t,o);r?lD(e,"insertFromPaste",{dataTransfer:FT(t)}).isDefaultPrevented()||(n(),iD(e,"insertFromPaste")):n()}},zT=(e,t,n,o)=>{const r=n||_T(t);UT(e,(e=>e.replace(xT,""))(t),r,!1,o)},jT=(e,t,n)=>{const o=e.dom.encode(t).replace(/\r\n/g,"\n"),r=((e,t,n)=>{const o=e.split(/\n\n/),r=((e,t)=>{let n="<"+e;const o=we(t,((e,t)=>t+'="'+ba.encodeAllRaw(e)+'"'));return o.length&&(n+=" "+o.join(" ")),n+">"})(t,n),s="</"+t+">",a=V(o,(e=>e.split(/\n/).join("<br />")));return 1===a.length?a[0]:V(a,(e=>r+e+s)).join("")})(Kr(o,om(e)),Ld(e),Md(e));UT(e,r,!1,!0,n)},$T=e=>{const t={};if(e&&e.types)for(let n=0;n<e.types.length;n++){const o=e.types[n];try{t[o]=e.getData(o)}catch(e){t[o]=""}}return t},HT=(e,t)=>t in e&&e[t].length>0,VT=e=>HT(e,"text/html")||HT(e,"text/plain"),qT=(e,t,n)=>{const o="paste"===t.type?t.clipboardData:t.dataTransfer;var r;if(Yc(e)&&o){const s=((e,t)=>{const n=t.items?te(ce(t.items),(e=>"file"===e.kind?[e.getAsFile()]:[])):[],o=t.files?ce(t.files):[];return Y(n.length>0?n:o,(e=>{const t=gm(e);return e=>Xe(e.type,"image/")&&H(t,(t=>(e=>{const t=e.toLowerCase(),n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"};return dn.hasOwn(n,t)?"image/"+n[t]:"image/"+t})(t)===e.type))})(e))})(e,o);if(s.length>0)return t.preventDefault(),(r=s,Promise.all(V(r,(e=>fy(e).then((t=>({file:e,uri:t}))))))).then((t=>{n&&e.selection.setRng(n),q(t,(t=>{((e,t)=>{my(t.uri).each((({data:n,type:o,base64Encoded:r})=>{const s=r?n:btoa(n),a=t.file,i=e.editorUpload.blobCache,l=i.getByData(s,o),d=null!=l?l:((e,t,n,o)=>{const r=IT(),s=Hd(e)&&C(n.name),a=s?((e,t)=>{const n=t.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i);return C(n)?e.dom.encode(n[1]):void 0})(e,n.name):r,i=s?n.name:void 0,l=t.create(r,n,o,a,i);return t.add(l),l})(e,i,a,s);zT(e,`<img src="${d.blobUri()}">`,!1,!0)}))})(e,t)}))})),!0}return!1},WT=(e,t,n,o,r)=>{let s=TT(n);const a=HT(t,ET())||_T(n),i=!a&&(e=>!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(e))(s),l=PT(s);(RT(s)||!s.length||i&&!l)&&(o=!0),(o||l)&&(s=HT(t,"text/plain")&&i?t["text/plain"]:(e=>{const t=Pa(),n=VC({},t);let o="";const r=t.getVoidElements(),s=dn.makeMap("script noscript style textarea video audio iframe object"," "),a=t.getBlockElements(),i=e=>{const n=e.name,l=e;if("br"!==n){if("wbr"!==n)if(r[n]&&(o+=" "),s[n])o+=" ";else{if(3===e.type&&(o+=e.value),!(e.name in t.getVoidElements())){let t=e.firstChild;if(t)do{i(t)}while(t=t.next)}a[n]&&l.next&&(o+="\n","p"===n&&(o+="\n"))}}else o+="\n"};return e=DT(e,[/<!\[[^\]]+\]>/g]),i(n.parse(e)),o})(s)),RT(s)||(o?jT(e,s,r):zT(e,s,a,r))},KT=(e,t,n)=>{((e,t,n)=>{let o;e.on("keydown",(e=>{(e=>Bg.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode)(e)&&!e.isDefaultPrevented()&&(o=e.shiftKey&&86===e.keyCode)})),e.on("paste",(r=>{if(r.isDefaultPrevented()||(e=>{var t,n;return rn.os.isAndroid()&&0===(null===(n=null===(t=e.clipboardData)||void 0===t?void 0:t.items)||void 0===n?void 0:n.length)})(r))return;const s="text"===n.get()||o;o=!1;const a=$T(r.clipboardData);!VT(a)&&qT(e,r,t.getLastRng()||e.selection.getRng())||(HT(a,"text/html")?(r.preventDefault(),WT(e,a,a["text/html"],s,!0)):HT(a,"text/plain")&&HT(a,"text/uri-list")?(r.preventDefault(),WT(e,a,a["text/plain"],s,!0)):(t.create(),bg.setEditorTimeout(e,(()=>{const n=t.getHtml();t.remove(),WT(e,a,n,s,!1)}),0)))}))})(e,t,n),(e=>{const t=e=>Xe(e,"webkit-fake-url"),n=e=>Xe(e,"data:");e.parser.addNodeFilter("img",((o,r,s)=>{if(!Yc(e)&&(e=>{var t;return!0===(null===(t=e.data)||void 0===t?void 0:t.paste)})(s))for(const r of o){const o=r.attr("src");u(o)&&!r.attr("data-mce-object")&&o!==rn.transparentSrc&&(t(o)||!rm(e)&&n(o))&&r.remove()}}))})(e)},YT=(e,t,n,o)=>{((e,t,n)=>{if(!e)return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(ET(),t),!0}catch(e){return!1}})(e.clipboardData,t.html,t.text)?(e.preventDefault(),o()):n(t.html,o)},GT=e=>(t,n)=>{const{dom:o,selection:r}=e,s=o.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=o.create("div",{contenteditable:"true"},t);o.setStyles(s,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),s.appendChild(a),o.add(e.getBody(),s);const i=r.getRng();a.focus();const l=o.createRng();l.selectNodeContents(a),r.setRng(l),bg.setEditorTimeout(e,(()=>{r.setRng(i),o.remove(s),n()}),0)},XT=e=>({html:ST(e.selection.getContent({contextual:!0})),text:e.selection.getContent({format:"text"})}),QT=e=>!e.selection.isCollapsed()||(e=>!!e.dom.getParent(e.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",e.getBody()))(e),ZT=(e,t)=>{var n,o;return Xg.getCaretRangeFromPoint(null!==(n=t.clientX)&&void 0!==n?n:0,null!==(o=t.clientY)&&void 0!==o?o:0,e.getDoc())},JT=(e,t)=>{e.focus(),t&&e.selection.setRng(t)},eO=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,tO=e=>dn.trim(e).replace(eO,$a).toLowerCase(),nO=(e,t,n)=>{const o=Zc(e);if(n||"all"===o||!Jc(e))return t;const r=o?o.split(/[, ]/):[];if(r&&"none"!==o){const n=e.dom,o=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,((e,t,s,a)=>{const i=n.parseStyle(n.decode(s)),l={};for(let e=0;e<r.length;e++){const t=i[r[e]];let s=t,a=n.getStyle(o,r[e],!0);/color/.test(r[e])&&(s=tO(s),a=tO(a)),a!==s&&(l[r[e]]=t)}const d=n.serializeStyle(l,"span");return d?t+' style="'+d+'"'+a:t+a}))}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,((e,t,n,o)=>t+' style="'+n+'"'+o)),t},oO=(e,t)=>{const n=Ne(!1),o=Ne(nm(e)?"text":"html"),r=(e=>{const t=Ne(null);return{create:()=>((e,t)=>{const{dom:n,selection:o}=e,r=e.getBody();t.set(o.getRng());const s=n.add(e.getBody(),"div",{id:"mcepastebin",class:"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},kT);rn.browser.isFirefox()&&n.setStyle(s,"left","rtl"===n.getStyle(r,"direction",!0)?65535:-65535),n.bind(s,"beforedeactivate focusin focusout",(e=>{e.stopPropagation()})),s.focus(),o.select(s,!0)})(e,t),remove:()=>((e,t)=>{const n=e.dom;if(NT(e)){let o;const r=t.get();for(;o=NT(e);)n.remove(o),n.unbind(o);r&&e.selection.setRng(r)}t.set(null)})(e,t),getEl:()=>NT(e),getHtml:()=>(e=>{const t=e.dom,n=(e,n)=>{e.appendChild(n),t.remove(n,!0)},[o,...r]=Y(e.getBody().childNodes,AT);q(r,(e=>{n(o,e)}));const s=t.select("div[id=mcepastebin]",o);for(let e=s.length-1;e>=0;e--){const r=t.create("div");o.insertBefore(r,s[e]),n(r,s[e])}return o?o.innerHTML:""})(e),getLastRng:t.get}})(e);(e=>{(rn.browser.isChromium()||rn.browser.isSafari())&&((e,t)=>{e.on("PastePreProcess",(n=>{n.content=t(e,n.content,n.internal)}))})(e,nO)})(e),((e,t)=>{e.addCommand("mceTogglePlainTextPaste",(()=>{((e,t)=>{"text"===t.get()?(t.set("html"),Cd(e,!1)):(t.set("text"),Cd(e,!0)),e.focus()})(e,t)})),e.addCommand("mceInsertClipboardContent",((t,n)=>{n.html&&zT(e,n.html,n.internal,!1),n.text&&jT(e,n.text,!1)}))})(e,o),(e=>{const t=t=>n=>{t(e,n)},n=Gc(e);w(n)&&e.on("PastePreProcess",t(n));const o=Xc(e);w(o)&&e.on("PastePostProcess",t(o))})(e),e.addQueryStateHandler("mceTogglePlainTextPaste",(()=>"text"===o.get())),e.on("PreInit",(()=>{((e,t)=>{e.on("cut",((e,t)=>n=>{!n.isDefaultPrevented()&&QT(e)&&e.selection.isEditable()&&YT(n,XT(e),GT(e),(()=>{if(rn.browser.isChromium()||rn.browser.isFirefox()){const n=e.selection.getRng();bg.setEditorTimeout(e,(()=>{e.selection.setRng(n),NN(e,t)}),0)}else NN(e,t)}))})(e,t)),e.on("copy",(e=>t=>{!t.isDefaultPrevented()&&QT(e)&&YT(t,XT(e),GT(e),S)})(e))})(e,t),((e,t)=>{Kc(e)&&e.on("dragend dragover draggesture dragdrop drop drag",(e=>{e.preventDefault(),e.stopPropagation()})),Yc(e)||e.on("drop",(e=>{const t=e.dataTransfer;t&&(e=>H(e.files,(e=>/^image\//.test(e.type))))(t)&&e.preventDefault()})),e.on("drop",(n=>{if(n.isDefaultPrevented())return;const o=ZT(e,n);if(y(o))return;const r=$T(n.dataTransfer),s=HT(r,ET());if((!VT(r)||(e=>{const t=e["text/plain"];return!!t&&0===t.indexOf("file://")})(r))&&qT(e,n,o))return;const a=r[ET()],i=a||r["text/html"]||r["text/plain"],l=((e,t,n,o)=>{const r=e.getParent(n,(e=>Ks(t,e)));if(!h(e.getParent(n,"summary")))return!0;if(r&&Se(o,"text/html")){const e=(new DOMParser).parseFromString(o["text/html"],"text/html").body;return!h(e.querySelector(r.nodeName.toLowerCase()))}return!1})(e.dom,e.schema,o.startContainer,r),d=t.get();d&&!l||i&&(n.preventDefault(),bg.setEditorTimeout(e,(()=>{e.undoManager.transact((()=>{(a||d&&l)&&e.execCommand("Delete"),JT(e,o);const t=TT(i);r["text/html"]?zT(e,t,s,!0):jT(e,t,!0)}))})))})),e.on("dragstart",(e=>{t.set(!0)})),e.on("dragover dragend",(n=>{Yc(e)&&!t.get()&&(n.preventDefault(),JT(e,ZT(e,n))),"dragend"===n.type&&t.set(!1)})),(e=>{e.on("input",(t=>{const n=e=>h(e.querySelector("summary"));if("deleteByDrag"===t.inputType){const t=Y(e.dom.select("details"),n);q(t,(t=>{ms(t.firstChild)&&t.firstChild.remove();const n=e.dom.create("summary");n.appendChild(Ii().dom),t.prepend(n)}))}}))})(e)})(e,n),KT(e,r,o)}))},rO=ms,sO=ss,aO=e=>ps(e.dom),iO=e=>t=>vn(mn.fromDom(e),t),lO=(e,t)=>sr(mn.fromDom(e),aO,iO(t)),dO=(e,t,n)=>{const o=new Hr(e,t),r=n?o.next.bind(o):o.prev.bind(o);let s=e;for(let t=n?e:r();t&&!rO(t);t=r())yl(t)&&(s=t);return s},cO=e=>{const t=((e,t,n)=>{const o=Ul.fromRangeStart(e).getNode(),r=((e,t,n)=>sr(mn.fromDom(e),(e=>(e=>gs(e.dom))(e)||n.isBlock(En(e))),iO(t)).getOr(mn.fromDom(t)).dom)(o,t,n),s=dO(o,r,!1),a=dO(o,r,!0),i=document.createRange();return lO(s,r).fold((()=>{sO(s)?i.setStart(s,0):i.setStartBefore(s)}),(e=>i.setStartBefore(e.dom))),lO(a,r).fold((()=>{sO(a)?i.setEnd(a,a.data.length):i.setEndAfter(a)}),(e=>i.setEndAfter(e.dom))),i})(e.selection.getRng(),e.getBody(),e.schema);e.selection.setRng(ay(t))};var mO;!function(e){e.Before="before",e.After="after"}(mO||(mO={}));const uO=(e,t)=>Math.abs(e.left-t),fO=(e,t)=>Math.abs(e.right-t),gO=(e,t)=>(e=>X(e,((e,t)=>e.fold((()=>I.some(t)),(e=>{const n=Math.min(t.left,e.left),o=Math.min(t.top,e.top),r=Math.max(t.right,e.right),s=Math.max(t.bottom,e.bottom);return I.some({top:o,right:r,bottom:s,left:n,width:r-n,height:s-o})}))),I.none()))(Y(e,(e=>{return(n=t)>=(o=e).top&&n<=o.bottom;var n,o}))).fold((()=>[[],e]),(t=>{const{pass:n,fail:o}=K(e,(e=>((e,t)=>{const n=((e,t)=>Math.max(0,Math.min(e.bottom,t.bottom)-Math.max(e.top,t.top)))(e,t)/Math.min(e.height,t.height);return((e,t)=>e.top<t.bottom&&e.bottom>t.top)(e,t)&&n>.5})(e,t)));return[n,o]})),pO=(e,t,n)=>t>e.left&&t<e.right?0:Math.min(Math.abs(e.left-t),Math.abs(e.right-t)),hO=(e,t,n,o)=>{const r=e=>yl(e.node)?I.some(e):Xr(e.node)?hO(ce(e.node.childNodes),t,n,!1):I.none(),s=(e,s)=>{const a=ae(e,((e,o)=>s(e,t,n)-s(o,t,n)));return me(a,r).map((e=>o&&!ss(e.node)&&a.length>1?((e,o,s)=>r(o).filter((o=>Math.abs(s(e,t,n)-s(o,t,n))<2&&ss(o.node))))(e,a[1],s).getOr(e):e))},[a,i]=gO(Ok(e),n),{pass:l,fail:d}=K(i,(e=>e.top<n));return s(a,pO).orThunk((()=>s(d,al))).orThunk((()=>s(l,al)))},bO=(e,t,n)=>((e,t,n)=>{const o=mn.fromDom(e),r=Bn(o),s=mn.fromPoint(r,t,n).filter((e=>yn(o,e))).getOr(o);return((e,t,n,o)=>{const r=(t,s)=>{const a=Y(t.dom.childNodes,T((e=>Xr(e)&&e.classList.contains("mce-drag-container"))));return s.fold((()=>hO(a,n,o,!0)),(e=>{const t=Y(a,(t=>t!==e.dom));return hO(t,n,o,!0)})).orThunk((()=>(vn(t,e)?I.none():Mn(t)).bind((e=>r(e,I.some(t))))))};return r(t,I.none())})(o,s,t,n)})(e,t,n).filter((e=>Hm(e.node))).map((e=>((e,t)=>({node:e.node,position:uO(e,t)<fO(e,t)?mO.Before:mO.After}))(e,t))),vO=e=>{var t,n;const o=e.getBoundingClientRect(),r=e.ownerDocument,s=r.documentElement,a=r.defaultView;return{top:o.top+(null!==(t=null==a?void 0:a.scrollY)&&void 0!==t?t:0)-s.clientTop,left:o.left+(null!==(n=null==a?void 0:a.scrollX)&&void 0!==n?n:0)-s.clientLeft}},yO=e=>({target:e,srcElement:e}),CO=(e,t,n,o)=>{const r=((e,t)=>{const n=(e=>{const t=OA(),n=(e=>{const t=e;return I.from(t[wA])})(e);return _A(e),bA(t),t.dropEffect=e.dropEffect,t.effectAllowed=e.effectAllowed,(e=>{const t=e;return I.from(t[gA])})(e).each((e=>t.setDragImage(e.image,e.x,e.y))),q(e.types,(n=>{"Files"!==n&&t.setData(n,e.getData(n))})),q(e.files,(e=>t.items.add(e))),(e=>{const t=e;return I.from(t[pA])})(e).each((e=>{((e,t)=>{hA(t)(e)})(t,e)})),n.each((n=>{xA(e,n),xA(t,n)})),t})(e);return"dragstart"===t?(bA(n),SA(n)):"drop"===t?(vA(n),_A(n)):(yA(n),kA(n)),n})(n,e);return v(o)?((e,t,n)=>{const o=O("Function not supported on simulated event.");return{bubbles:!0,cancelBubble:!1,cancelable:!0,composed:!1,currentTarget:null,defaultPrevented:!1,eventPhase:0,isTrusted:!0,returnValue:!1,timeStamp:0,type:e,composedPath:o,initEvent:o,preventDefault:S,stopImmediatePropagation:S,stopPropagation:S,AT_TARGET:window.Event.AT_TARGET,BUBBLING_PHASE:window.Event.BUBBLING_PHASE,CAPTURING_PHASE:window.Event.CAPTURING_PHASE,NONE:window.Event.NONE,altKey:!1,button:0,buttons:0,clientX:0,clientY:0,ctrlKey:!1,layerX:0,layerY:0,metaKey:!1,movementX:0,movementY:0,offsetX:0,offsetY:0,pageX:0,pageY:0,relatedTarget:null,screenX:0,screenY:0,shiftKey:!1,x:0,y:0,detail:0,view:null,which:0,initUIEvent:o,initMouseEvent:o,getModifierState:o,dataTransfer:n,...yO(t)}})(e,t,r):((e,t,n,o)=>({...t,dataTransfer:o,type:e,...yO(n)}))(e,o,t,r)},wO=ps,EO=((...e)=>t=>{for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1})(wO,gs),xO=(e,t,n,o)=>{const r=e.dom,s=t.cloneNode(!0);r.setStyles(s,{width:n,height:o}),r.setAttrib(s,"data-mce-selected",null);const a=r.create("div",{class:"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return r.setStyles(a,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:o}),r.setStyles(s,{margin:0,boxSizing:"border-box"}),a.appendChild(s),a},SO=(e,t)=>n=>()=>{const o="left"===e?n.scrollX:n.scrollY;n.scroll({[e]:o+t,behavior:"smooth"})},_O=SO("left",-32),kO=SO("left",32),NO=SO("top",-32),AO=SO("top",32),RO=e=>{e&&e.parentNode&&e.parentNode.removeChild(e)},DO=(e,t,n,o,r)=>{"dragstart"===t&&BA(o,e.dom.getOuterHTML(n));const s=CO(t,n,o,r);return e.dispatch(t,s)},TO=(e,t)=>{const n=st(((e,n)=>((e,t,n)=>{e._selectionOverrides.hideFakeCaret(),bO(e.getBody(),t,n).fold((()=>e.selection.placeCaretAt(t,n)),(o=>{const r=e._selectionOverrides.showCaret(1,o.node,o.position===mO.Before,!1);r?e.selection.setRng(r):e.selection.placeCaretAt(t,n)}))})(t,e,n)),0);t.on("remove",n.cancel);const o=e;return r=>e.on((e=>{const s=Math.max(Math.abs(r.screenX-e.screenX),Math.abs(r.screenY-e.screenY));if(!e.dragging&&s>10){const n=DO(t,"dragstart",e.element,e.dataTransfer,r);if(C(n.dataTransfer)&&(e.dataTransfer=n.dataTransfer),n.isDefaultPrevented())return;e.dragging=!0,t.focus()}if(e.dragging){const s=r.currentTarget===t.getDoc().documentElement,l=((e,t)=>({pageX:t.pageX-e.relX,pageY:t.pageY+5}))(e,((e,t)=>{return n=(e=>e.inline?vO(e.getBody()):{left:0,top:0})(e),o=(e=>{const t=e.getBody();return e.inline?{left:t.scrollLeft,top:t.scrollTop}:{left:0,top:0}})(e),r=((e,t)=>{if(t.target.ownerDocument!==e.getDoc()){const n=vO(e.getContentAreaContainer()),o=(e=>{const t=e.getBody(),n=e.getDoc().documentElement,o={left:t.scrollLeft,top:t.scrollTop},r={left:t.scrollLeft||n.scrollLeft,top:t.scrollTop||n.scrollTop};return e.inline?o:r})(e);return{left:t.pageX-n.left+o.left,top:t.pageY-n.top+o.top}}return{left:t.pageX,top:t.pageY}})(e,t),{pageX:r.left-n.left+o.left,pageY:r.top-n.top+o.top};var n,o,r})(t,r));a=e.ghost,i=t.getBody(),a.parentNode!==i&&i.appendChild(a),((e,t,n,o,r,s,a,i,l,d,c,m)=>{let u=0,f=0;e.style.left=t.pageX+"px",e.style.top=t.pageY+"px",t.pageX+n>r&&(u=t.pageX+n-r),t.pageY+o>s&&(f=t.pageY+o-s),e.style.width=n-u+"px",e.style.height=o-f+"px";const g=l.clientHeight,p=l.clientWidth,h=a+l.getBoundingClientRect().top,b=i+l.getBoundingClientRect().left;c.on((e=>{e.intervalId.clear(),e.dragging&&m&&(a+8>=g?e.intervalId.set(AO(d)):a-8<=0?e.intervalId.set(NO(d)):i+8>=p?e.intervalId.set(kO(d)):i-8<=0?e.intervalId.set(_O(d)):h+16>=window.innerHeight?e.intervalId.set(AO(window)):h-16<=0?e.intervalId.set(NO(window)):b+16>=window.innerWidth?e.intervalId.set(kO(window)):b-16<=0&&e.intervalId.set(_O(window)))}))})(e.ghost,l,e.width,e.height,e.maxX,e.maxY,r.clientY,r.clientX,t.getContentAreaContainer(),t.getWin(),o,s),n.throttle(r.clientX,r.clientY)}var a,i}))},OO=(e,t,n)=>{e.on((e=>{e.intervalId.clear(),e.dragging&&n.fold((()=>DO(t,"dragend",e.element,e.dataTransfer)),(n=>DO(t,"dragend",e.element,e.dataTransfer,n)))})),BO(e)},BO=e=>{e.on((e=>{e.intervalId.clear(),RO(e.ghost)})),e.clear()},PO=e=>{const t=We(),n=li.DOM,o=document,r=((e,t)=>n=>{if((e=>0===e.button)(n)){const o=Z(t.dom.getParents(n.target),EO).getOr(null);if(C(o)&&((e,t,n)=>wO(n)&&n!==t&&e.isEditable(n.parentElement))(t.dom,t.getBody(),o)){const r=t.dom.getPos(o),s=t.getBody(),a=t.getDoc().documentElement;e.set({element:o,dataTransfer:OA(),dragging:!1,screenX:n.screenX,screenY:n.screenY,maxX:(t.inline?s.scrollWidth:a.offsetWidth)-2,maxY:(t.inline?s.scrollHeight:a.offsetHeight)-2,relX:n.pageX-r.x,relY:n.pageY-r.y,width:o.offsetWidth,height:o.offsetHeight,ghost:xO(t,o,o.offsetWidth,o.offsetHeight),intervalId:qe(100)})}}})(t,e),s=TO(t,e),a=((e,t)=>n=>{e.on((e=>{var o;if(e.intervalId.clear(),e.dragging){if(((e,t,n)=>!y(t)&&t!==n&&!e.dom.isChildOf(t,n)&&e.dom.isEditable(t))(t,(e=>{const t=e.getSel();if(C(t)){const e=t.getRangeAt(0).startContainer;return ss(e)?e.parentNode:e}return null})(t.selection),e.element)){const r=null!==(o=t.getDoc().elementFromPoint(n.clientX,n.clientY))&&void 0!==o?o:t.getBody();DO(t,"drop",r,e.dataTransfer,n).isDefaultPrevented()||t.undoManager.transact((()=>{((e,t)=>{const n=e.getParent(t.parentNode,e.isBlock);RO(t),n&&n!==e.getRoot()&&e.isEmpty(n)&&Fi(mn.fromDom(n))})(t.dom,e.element),(e=>{const t=e.getData("text/html");return""===t?I.none():I.some(t)})(e.dataTransfer).each((e=>t.insertContent(e))),t._selectionOverrides.hideFakeCaret()}))}DO(t,"dragend",t.getBody(),e.dataTransfer,n)}})),BO(e)})(t,e),i=((e,t)=>n=>OO(e,t,I.some(n)))(t,e);e.on("mousedown",r),e.on("mousemove",s),e.on("mouseup",a),n.bind(o,"mousemove",s),n.bind(o,"mouseup",i),e.on("remove",(()=>{n.unbind(o,"mousemove",s),n.unbind(o,"mouseup",i)})),e.on("keydown",(n=>{n.keyCode===Bg.ESC&&OO(t,e,I.none())}))},LO=ps,MO=(e,t)=>Ub(e.getBody(),t),IO=e=>{const t=e.selection,n=e.dom,o=e.getBody(),r=zm(e,o,n.isBlock,(()=>Dg(e))),s="sel-"+n.uniqueId(),a="data-mce-selected";let i;const l=e=>e!==o&&(LO(e)||Cs(e))&&n.isChildOf(e,o)&&n.isEditable(e.parentNode),d=(n,o,s,a=!0)=>e.dispatch("ShowCaret",{target:o,direction:n,before:s}).isDefaultPrevented()?null:(a&&t.scrollIntoView(o,-1===n),r.show(s,o)),c=e=>Wi(e)||Xi(e)||Qi(e),m=e=>c(e.startContainer)||c(e.endContainer),u=t=>{const o=e.schema.getVoidElements(),r=n.createRng(),s=t.startContainer,a=t.startOffset,i=t.endContainer,l=t.endOffset;return Se(o,s.nodeName.toLowerCase())?0===a?r.setStartBefore(s):r.setStartAfter(s):r.setStart(s,a),Se(o,i.nodeName.toLowerCase())?0===l?r.setEndBefore(i):r.setEndAfter(i):r.setEnd(i,l),r},f=(r,c)=>{if(!r)return null;if(r.collapsed){if(!m(r)){const e=c?1:-1,t=fu(e,o,r),s=t.getNode(!c);if(C(s)){if(Hm(s))return d(e,s,!!c&&!t.isAtEnd(),!1);if(qi(s)&&ps(s.nextSibling)){const e=n.createRng();return e.setStart(s,0),e.setEnd(s,0),e}}const a=t.getNode(c);if(C(a)){if(Hm(a))return d(e,a,!c&&!t.isAtEnd(),!1);if(qi(a)&&ps(a.previousSibling)){const e=n.createRng();return e.setStart(a,1),e.setEnd(a,1),e}}}return null}let u=r.startContainer,f=r.startOffset;const g=r.endOffset;if(ss(u)&&0===f&&LO(u.parentNode)&&(u=u.parentNode,f=n.nodeIndex(u),u=u.parentNode),!Xr(u))return null;if(g===f+1&&u===r.endContainer){const o=u.childNodes[f];if(l(o))return(o=>{const r=o.cloneNode(!0),l=e.dispatch("ObjectSelected",{target:o,targetClone:r});if(l.isDefaultPrevented())return null;const d=((o,r)=>{const a=mn.fromDom(e.getBody()),i=e.getDoc(),l=lr(a,"#"+s).getOrThunk((()=>{const e=mn.fromHtml('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>',i);return ho(e,"id",s),mo(a,e),e})),d=n.createRng();So(l),go(l,[mn.fromText(dt,i),mn.fromDom(r),mn.fromText(dt,i)]),d.setStart(l.dom.firstChild,1),d.setEnd(l.dom.lastChild,0),Fo(l,{top:n.getPos(o,e.getBody()).y+"px"}),ro(l);const c=t.getSel();return c&&(c.removeAllRanges(),c.addRange(d)),d})(o,l.targetClone),c=mn.fromDom(o);return q(xr(mn.fromDom(e.getBody()),`*[${a}]`),(e=>{vn(c,e)||wo(e,a)})),n.getAttrib(o,a)||o.setAttribute(a,"1"),i=o,p(),d})(o)}return null},g=()=>{i&&i.removeAttribute(a),lr(mn.fromDom(e.getBody()),"#"+s).each(_o),i=null},p=()=>{r.hide()};return lE(e)||(e.on("click",(t=>{n.isEditable(t.target)||(t.preventDefault(),e.focus())})),e.on("blur NewBlock",g),e.on("ResizeWindow FullscreenStateChanged",r.reposition),e.on("tap",(t=>{const n=t.target,o=MO(e,n);LO(o)?(t.preventDefault(),h_(e,o).each(f)):l(n)&&h_(e,n).each(f)}),!0),e.on("mousedown",(r=>{const s=r.target;if(s!==o&&"HTML"!==s.nodeName&&!n.isChildOf(s,o))return;if(!((e,t,n)=>{const o=mn.fromDom(e.getBody()),r=e.inline?o:mn.fromDom(Bn(o).dom.documentElement),s=((e,t,n,o)=>{const r=(e=>e.dom.getBoundingClientRect())(t);return{x:n-(e?r.left+t.dom.clientLeft+AE(t):0),y:o-(e?r.top+t.dom.clientTop+NE(t):0)}})(e.inline,r,t,n);return((e,t,n)=>{const o=_E(e),r=kE(e);return t>=0&&n>=0&&t<=o&&n<=r})(r,s.x,s.y)})(e,r.clientX,r.clientY))return;g(),p();const a=MO(e,s);LO(a)?(r.preventDefault(),h_(e,a).each(f)):bO(o,r.clientX,r.clientY).each((n=>{var o;r.preventDefault(),(o=d(1,n.node,n.position===mO.Before,!1))&&t.setRng(o),Qr(a)?a.focus():e.getBody().focus()}))})),e.on("keypress",(e=>{Bg.modifierPressed(e)||LO(t.getNode())&&e.preventDefault()})),e.on("GetSelectionRange",(e=>{let t=e.range;if(i){if(!i.parentNode)return void(i=null);t=t.cloneRange(),t.selectNode(i),e.range=t}})),e.on("focusin",(t=>{if(e.selection.isCollapsed()&&!Cs(t.target)&&e.getBody().contains(t.target)&&t.target!==e.getBody()&&!e.dom.isEditable(t.target.parentNode)){r.isShowing()&&r.hide(),t.target.contains(e.selection.getNode())||(e.selection.select(t.target,!0),e.selection.collapse(!0));const n=f(e.selection.getRng(),!0);n&&e.selection.setRng(n)}})),e.on("SetSelectionRange",(e=>{e.range=u(e.range);const t=f(e.range,e.forward);t&&(e.range=t)})),e.on("AfterSetSelectionRange",(e=>{const t=e.range,o=t.startContainer.parentElement;var r;m(t)||Xr(r=o)&&"mcepastebin"===r.id||p(),(e=>C(e)&&n.hasClass(e,"mce-offscreen-selection"))(o)||g()})),(e=>{PO(e),Mc(e)&&(e=>{const t=t=>{if(!t.isDefaultPrevented()){const n=t.dataTransfer;n&&($(n.types,"Files")||n.files.length>0)&&(t.preventDefault(),"drop"===t.type&&PE(e,"Dropped file type is not supported"))}},n=n=>{Sg(e,n.target)&&t(n)},o=()=>{const o=li.DOM,r=e.dom,s=document,a=e.inline?e.getBody():e.getDoc(),i=["drop","dragover"];q(i,(e=>{o.bind(s,e,n),r.bind(a,e,t)})),e.on("remove",(()=>{q(i,(e=>{o.unbind(s,e,n),r.unbind(a,e,t)}))}))};e.on("init",(()=>{bg.setEditorTimeout(e,o,0)}))})(e)})(e),(e=>{const t=st((()=>{if(!e.removed&&e.getBody().contains(document.activeElement)){const t=e.selection.getRng();if(t.collapsed){const n=b_(e,t,!1);e.selection.setRng(n)}}}),0);e.on("focus",(()=>{t.throttle()})),e.on("blur",(()=>{t.cancel()}))})(e),(e=>{e.on("init",(()=>{e.on("focusin",(t=>{const n=t.target;if(Cs(n)){const t=Ub(e.getBody(),n),o=ps(t)?t:n;e.selection.getNode()!==o&&h_(e,o).each((t=>e.selection.setRng(t)))}}))}))})(e)),{showCaret:d,showBlockCaretContainer:e=>{e.hasAttribute("data-mce-caret")&&(Zi(e),t.scrollIntoView(e))},hideFakeCaret:p,destroy:()=>{r.destroy(),i=null}}},FO=(e,t)=>{let n=t;for(let t=e.previousSibling;ss(t);t=t.previousSibling)n+=t.data.length;return n},UO=(e,t,n,o,r)=>{if(ss(n)&&(o<0||o>n.data.length))return[];const s=r&&ss(n)?[FO(n,o)]:[o];let a=n;for(;a!==t&&a.parentNode;)s.push(e.nodeIndex(a,r)),a=a.parentNode;return a===t?s.reverse():[]},zO=(e,t,n,o,r,s,a=!1)=>({start:UO(e,t,n,o,a),end:UO(e,t,r,s,a)}),jO=(e,t)=>{const n=t.slice(),o=n.pop();return E(o)?X(n,((e,t)=>e.bind((e=>I.from(e.childNodes[t])))),I.some(e)).bind((e=>ss(e)&&(o<0||o>e.data.length)?I.none():I.some({node:e,offset:o}))):I.none()},$O=(e,t)=>jO(e,t.start).bind((({node:n,offset:o})=>jO(e,t.end).map((({node:e,offset:t})=>{const r=document.createRange();return r.setStart(n,o),r.setEnd(e,t),r})))),HO=(e,t,n)=>{if(t&&e.isEmpty(t)&&!n(t)){const o=t.parentNode;e.remove(t,ss(t.firstChild)&&qr(t.firstChild.data)),HO(e,o,n)}},VO=(e,t,n,o=!0)=>{const r=t.startContainer.parentNode,s=t.endContainer.parentNode;t.deleteContents(),o&&!n(t.startContainer)&&(ss(t.startContainer)&&0===t.startContainer.data.length&&e.remove(t.startContainer),ss(t.endContainer)&&0===t.endContainer.data.length&&e.remove(t.endContainer),HO(e,r,n),r!==s&&HO(e,s,n))},qO=(e,t)=>I.from(e.dom.getParent(t.startContainer,e.dom.isBlock)),WO=(e,t,n)=>{const o=e.dynamicPatternsLookup({text:n,block:t});return{...e,blockPatterns:cd(o).concat(e.blockPatterns),inlinePatterns:md(o).concat(e.inlinePatterns)}},KO=(e,t,n,o)=>{const r=e.createRng();return r.setStart(t,0),r.setEnd(n,o),r.toString()},YO=(e,t)=>e.create("span",{"data-mce-type":"bookmark",id:t}),GO=(e,t)=>{const n=e.createRng();return n.setStartAfter(t.start),n.setEndBefore(t.end),n},XO=(e,t,n)=>{const o=$O(e.getRoot(),n).getOrDie("Unable to resolve path range"),r=o.startContainer,s=o.endContainer,a=0===o.endOffset?s:s.splitText(o.endOffset),i=0===o.startOffset?r:r.splitText(o.startOffset),l=i.parentNode;return{prefix:t,end:a.parentNode.insertBefore(YO(e,t+"-end"),a),start:l.insertBefore(YO(e,t+"-start"),i)}},QO=(e,t,n)=>{HO(e,e.get(t.prefix+"-end"),n),HO(e,e.get(t.prefix+"-start"),n)},ZO=e=>0===e.start.length,JO=(e,t,n,o)=>{const r=t.start;var s;return sR(e,o.container,o.offset,(s=r,(e,t)=>{const n=e.data.substring(0,t),o=n.lastIndexOf(s.charAt(s.length-1)),r=n.lastIndexOf(s);return-1!==r?r+s.length:-1!==o?o+1:-1}),n).bind((o=>{var s,a;const i=null!==(a=null===(s=n.textContent)||void 0===s?void 0:s.indexOf(r))&&void 0!==a?a:-1;if(-1!==i&&o.offset>=i+r.length){const t=e.createRng();return t.setStart(o.container,o.offset-r.length),t.setEnd(o.container,o.offset),I.some(t)}{const s=o.offset-r.length;return oR(o.container,s,n).map((t=>{const n=e.createRng();return n.setStart(t.container,t.offset),n.setEnd(o.container,o.offset),n})).filter((e=>e.toString()===r)).orThunk((()=>JO(e,t,n,JA(o.container,0))))}}))},eB=(e,t,n,o)=>{const r=e.dom,s=r.getRoot(),a=n.pattern,i=n.position.container,l=n.position.offset;return oR(i,l-n.pattern.end.length,t).bind((d=>{const c=zO(r,s,d.container,d.offset,i,l,o);if(ZO(a))return I.some({matches:[{pattern:a,startRng:c,endRng:c}],position:d});{const i=tB(e,n.remainingPatterns,d.container,d.offset,t,o),l=i.getOr({matches:[],position:d}),m=l.position,u=((e,t,n,o,r,s=!1)=>{if(0===t.start.length&&!s){const t=e.createRng();return t.setStart(n,o),t.setEnd(n,o),I.some(t)}return nR(n,o,r).bind((n=>JO(e,t,r,n).bind((e=>{var t;if(s){if(e.endContainer===n.container&&e.endOffset===n.offset)return I.none();if(0===n.offset&&(null===(t=e.endContainer.textContent)||void 0===t?void 0:t.length)===e.endOffset)return I.none()}return I.some(e)}))))})(r,a,m.container,m.offset,t,i.isNone());return u.map((e=>{const t=((e,t,n,o=!1)=>zO(e,t,n.startContainer,n.startOffset,n.endContainer,n.endOffset,o))(r,s,e,o);return{matches:l.matches.concat([{pattern:a,startRng:t,endRng:c}]),position:JA(e.startContainer,e.startOffset)}}))}}))},tB=(e,t,n,o,r,s)=>{const a=e.dom;return nR(n,o,a.getRoot()).bind((i=>{const l=KO(a,r,n,o);for(let a=0;a<t.length;a++){const d=t[a];if(!Qe(l,d.end))continue;const c=t.slice();c.splice(a,1);const m=eB(e,r,{pattern:d,remainingPatterns:c,position:i},s);if(m.isNone()&&o>0)return tB(e,t,n,o-1,r,s);if(m.isSome())return m}return I.none()}))},nB=(e,t,n)=>{e.selection.setRng(n),"inline-format"===t.type?q(t.format,(t=>{e.formatter.apply(t)})):e.execCommand(t.cmd,!1,t.value)},oB=(e,t,n,o,r,s)=>{var a;return((e,t)=>{const n=ne(e,(e=>H(t,(t=>e.pattern.start===t.pattern.start&&e.pattern.end===t.pattern.end))));return e.length===t.length?n?e:t:e.length>t.length?e:t})(tB(e,r.inlinePatterns,n,o,t,s).fold((()=>[]),(e=>e.matches)),tB(e,(a=r.inlinePatterns,ae(a,((e,t)=>t.end.length-e.end.length))),n,o,t,s).fold((()=>[]),(e=>e.matches)))},rB=(e,t)=>{if(0===t.length)return;const n=e.dom,o=e.selection.getBookmark(),r=((e,t)=>{const n=Pe("mce_textpattern"),o=G(t,((t,o)=>{const r=XO(e,n+`_end${t.length}`,o.endRng);return t.concat([{...o,endMarker:r}])}),[]);return G(o,((t,r)=>{const s=o.length-t.length-1,a=ZO(r.pattern)?r.endMarker:XO(e,n+`_start${s}`,r.startRng);return t.concat([{...r,startMarker:a}])}),[])})(n,t);q(r,(t=>{const o=n.getParent(t.startMarker.start,n.isBlock),r=e=>e===o;ZO(t.pattern)?((e,t,n,o)=>{const r=GO(e.dom,n);VO(e.dom,r,o),nB(e,t,r)})(e,t.pattern,t.endMarker,r):((e,t,n,o,r)=>{const s=e.dom,a=GO(s,o),i=GO(s,n);VO(s,i,r),VO(s,a,r);const l={prefix:n.prefix,start:n.end,end:o.start},d=GO(s,l);nB(e,t,d)})(e,t.pattern,t.startMarker,t.endMarker,r),QO(n,t.endMarker,r),QO(n,t.startMarker,r)})),e.selection.moveToBookmark(o)},sB=(e,t,n)=>((e,t,n)=>{if(ss(e)&&0>=e.length)return I.some(JA(e,0));{const t=Ai(eR);return I.from(t.forwards(e,0,tR(e),n)).map((e=>JA(e.container,0)))}})(t,0,t).map((o=>{const r=o.container;return rR(r,n.start.length,t).each((n=>{const o=e.createRng();o.setStart(r,0),o.setEnd(n.container,n.offset),VO(e,o,(e=>e===t))})),r})),aB=e=>(t,n)=>{const o=t.dom,r=n.pattern,s=$O(o.getRoot(),n.range).getOrDie("Unable to resolve path range");return qO(t,s).each((n=>{"block-format"===r.type?((e,t)=>{const n=t.get(e);return p(n)&&le(n).exists((e=>Se(e,"block")))})(r.format,t.formatter)&&t.undoManager.transact((()=>{e(t.dom,n,r),t.formatter.apply(r.format)})):"block-command"===r.type&&t.undoManager.transact((()=>{e(t.dom,n,r),t.execCommand(r.cmd,!1,r.value)}))})),!0},iB=e=>(t,n)=>{const o=(e=>ae(e,((e,t)=>t.start.length-e.start.length)))(t),r=n.replace(dt," ");return Z(o,(t=>e(t,n,r)))},lB=(e,t)=>(n,o,r,s,a)=>{var i;void 0===a&&(a=null!==(i=o.textContent)&&void 0!==i?i:"");const l=n.dom,d=Ld(n);return l.is(o,d)?e(r.blockPatterns,a).map((e=>t&&dn.trim(a).length===e.start.length?[]:[{pattern:e,range:zO(l,l.getRoot(),o,0,o,0,s)}])).getOr([]):[]},dB=aB(((e,t,n)=>{sB(e,t,n).each((e=>{const t=mn.fromDom(e),n=er(t);/^\s[^\s]/.test(n)&&tr(t,n.slice(1))}))})),cB=iB(((e,t,n)=>0===t.indexOf(e.start)||0===n.indexOf(e.start))),mB=lB(cB,!0),uB=aB(sB),fB=iB(((e,t,n)=>t===e.start||n===e.start)),gB=lB(fB,!1),pB=(e,t,n)=>{for(let o=0;o<e.length;o++)if(n(e[o],t))return!0;return!1},hB=e=>{const t=[",",".",";",":","!","?"],n=[32],o=()=>{return t=sm(e).filter((t=>"inline-command"!==t.type&&"block-command"!==t.type||e.queryCommandSupported(t.cmd))),n=am(e),{inlinePatterns:md(t),blockPatterns:cd(t),dynamicPatternsLookup:n};var t,n},r=()=>(e=>e.options.isSet("text_patterns_lookup"))(e);e.on("keydown",(t=>{if(13===t.keyCode&&!Bg.modifierPressed(t)&&e.selection.isCollapsed()&&e.selection.isEditable()){const n=ud(o(),"enter");(n.inlinePatterns.length>0||n.blockPatterns.length>0||r())&&((e,t)=>((e,t)=>{const n=e.selection.getRng();return qO(e,n).map((o=>{var r;const s=Math.max(0,n.startOffset),a=WO(t,o,null!==(r=o.textContent)&&void 0!==r?r:"");return{inlineMatches:oB(e,o,n.startContainer,s,a,!0),blockMatches:mB(e,o,a,!0)}})).filter((({inlineMatches:e,blockMatches:t})=>t.length>0||e.length>0))})(e,t).fold(L,(({inlineMatches:t,blockMatches:n})=>(e.undoManager.add(),e.undoManager.extra((()=>{e.execCommand("mceInsertNewLine")}),(()=>{(e=>{e.insertContent(Ui,{preserve_zwsp:!0})})(e),rB(e,t),((e,t)=>{if(0===t.length)return;const n=e.selection.getBookmark();q(t,(t=>dB(e,t))),e.selection.moveToBookmark(n)})(e,n);const o=e.selection.getRng(),r=nR(o.startContainer,o.startOffset,e.dom.getRoot());e.execCommand("mceInsertNewLine"),r.each((t=>{const n=t.container;n.data.charAt(t.offset-1)===lt&&(n.deleteData(t.offset-1,1),HO(e.dom,n.parentNode,(t=>t===e.dom.getRoot())))}))})),!0))))(e,n)&&t.preventDefault()}}),!0),e.on("keydown",(t=>{if(32===t.keyCode&&e.selection.isCollapsed()&&e.selection.isEditable()){const n=ud(o(),"space");(n.blockPatterns.length>0||r())&&((e,t)=>((e,t)=>{const n=e.selection.getRng();return qO(e,n).map((o=>{const r=Math.max(0,n.startOffset),s=KO(e.dom,o,n.startContainer,r),a=WO(t,o,s);return gB(e,o,a,!1,s)})).filter((e=>e.length>0))})(e,t).fold(L,(t=>(e.undoManager.transact((()=>{((e,t)=>{q(t,(t=>uB(e,t)))})(e,t)})),!0))))(e,n)&&t.preventDefault()}}),!0);const s=()=>{if(e.selection.isCollapsed()&&e.selection.isEditable()){const t=ud(o(),"space");(t.inlinePatterns.length>0||r())&&((e,t)=>{const n=e.selection.getRng();qO(e,n).map((o=>{const r=Math.max(0,n.startOffset-1),s=KO(e.dom,o,n.startContainer,r),a=WO(t,o,s),i=oB(e,o,n.startContainer,r,a,!1);i.length>0&&e.undoManager.transact((()=>{rB(e,i)}))}))})(e,t)}};e.on("keyup",(e=>{pB(n,e,((e,t)=>e===t.keyCode&&!Bg.modifierPressed(t)))&&s()})),e.on("keypress",(n=>{pB(t,n,((e,t)=>e.charCodeAt(0)===t.charCode))&&bg.setEditorTimeout(e,s)}))},bB=e=>{const t=dn.each,n=Bg.BACKSPACE,o=Bg.DELETE,r=e.dom,s=e.selection,a=e.parser,i=rn.browser,l=i.isFirefox(),d=i.isChromium()||i.isSafari(),c=rn.deviceType.isiPhone()||rn.deviceType.isiPad(),m=rn.os.isMacOS()||rn.os.isiOS(),u=(t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},f=e=>e.isDefaultPrevented(),g=()=>{e.shortcuts.add("meta+a",null,"SelectAll")},p=()=>{e.inline||r.bind(e.getDoc(),"mousedown mouseup",(t=>{let n;if(t.target===e.getDoc().documentElement)if(n=s.getRng(),e.getBody().focus(),"mousedown"===t.type){if(Wi(n.startContainer))return;s.placeCaretAt(t.clientX,t.clientY)}else s.setRng(n)}))},h=()=>{Range.prototype.getClientRects||e.on("mousedown",(t=>{if(!f(t)&&"HTML"===t.target.nodeName){const t=e.getBody();t.blur(),bg.setEditorTimeout(e,(()=>{t.focus()}))}}))},b=()=>{const t=Uc(e);e.on("click",(n=>{const o=n.target;/^(IMG|HR)$/.test(o.nodeName)&&r.isEditable(o)&&(n.preventDefault(),e.selection.select(o),e.nodeChanged()),"A"===o.nodeName&&r.hasClass(o,t)&&0===o.childNodes.length&&r.isEditable(o.parentNode)&&(n.preventDefault(),s.select(o))}))},v=()=>{e.on("keydown",(e=>{if(!f(e)&&e.keyCode===n&&s.isCollapsed()&&0===s.getRng().startOffset){const t=s.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}return!0}))},y=()=>{Tc(e)||e.on("BeforeExecCommand mousedown",(()=>{u("StyleWithCSS",!1),u("enableInlineTableEditing",!1),dc(e)||u("enableObjectResizing",!1)}))},C=()=>{e.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}")},w=()=>{e.inline||e.on("keydown",(()=>{document.activeElement===document.body&&e.getWin().focus()}))},E=()=>{e.inline||(e.contentStyles.push("body {min-height: 150px}"),e.on("click",(t=>{let n;"HTML"===t.target.nodeName&&(n=e.selection.getRng(),e.getBody().focus(),e.selection.setRng(n),e.selection.normalize(),e.nodeChanged())})))},x=()=>{m&&e.on("keydown",(t=>{!Bg.metaKeyPressed(t)||t.shiftKey||37!==t.keyCode&&39!==t.keyCode||(t.preventDefault(),e.selection.getSel().modify("move",37===t.keyCode?"backward":"forward","lineboundary"))}))},_=()=>{e.on("click",(e=>{let t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)})),e.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")},k=()=>{e.on("init",(()=>{e.dom.bind(e.getBody(),"submit",(e=>{e.preventDefault()}))}))},N=S;return lE(e)?(d&&(p(),b(),k(),g(),c&&(w(),E(),_())),l&&(h(),y(),C(),x())):(e.on("keydown",(t=>{if(f(t)||t.keyCode!==Bg.BACKSPACE)return;let n=s.getRng();const o=n.startContainer,a=n.startOffset,i=r.getRoot();let l=o;if(n.collapsed&&0===a){for(;l.parentNode&&l.parentNode.firstChild===l&&l.parentNode!==i;)l=l.parentNode;"BLOCKQUOTE"===l.nodeName&&(e.formatter.toggle("blockquote",void 0,l),n=r.createRng(),n.setStart(o,0),n.setEnd(o,0),s.setRng(n))}})),(()=>{const t=e=>{const t=r.create("body"),n=e.cloneContents();return t.appendChild(n),s.serializer.serialize(t,{format:"html"})};e.on("keydown",(s=>{const a=s.keyCode;if(!f(s)&&(a===o||a===n)&&e.selection.isEditable()){const n=e.selection.isCollapsed(),o=e.getBody();if(n&&!As(e.schema,o))return;if(!n&&!(n=>{const o=t(n),s=r.createRng();return s.selectNode(e.getBody()),o===t(s)})(e.selection.getRng()))return;s.preventDefault(),e.setContent(""),o.firstChild&&r.isBlock(o.firstChild)?e.selection.setCursorLocation(o.firstChild,0):e.selection.setCursorLocation(o,0),e.nodeChanged()}}))})(),rn.windowsPhone||e.on("keyup focusin mouseup",(t=>{Bg.modifierPressed(t)||(e=>{const t=e.getBody(),n=e.selection.getRng();return n.startContainer===n.endContainer&&n.startContainer===t&&0===n.startOffset&&n.endOffset===t.childNodes.length})(e)||s.normalize()}),!0),d&&(p(),b(),e.on("init",(()=>{u("DefaultParagraphSeparator",Ld(e))})),k(),v(),a.addNodeFilter("br",(e=>{let t=e.length;for(;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()})),c?(w(),E(),_()):g()),l&&((()=>{const t=Tn("figcaption");e.on("keydown",(n=>{if(n.keyCode===Bg.LEFT||n.keyCode===Bg.RIGHT){const o=mn.fromDom(e.selection.getNode());t(o)&&e.selection.isCollapsed()&&Ln(o).bind((t=>{var r;return 0===e.selection.getRng().startOffset&&n.keyCode===Bg.LEFT?Un(t):e.selection.getRng().endOffset===(null===(r=o.dom.textContent)||void 0===r?void 0:r.length)&&n.keyCode===Bg.RIGHT?zn(t):I.none()})).each((t=>{e.selection.setCursorLocation(t.dom,0)}))}}))})(),e.on("mousedown",(t=>{je(I.from(t.clientX),I.from(t.clientY),((n,o)=>{const r=e.getDoc().caretPositionFromPoint(n,o),s=(null==r?void 0:r.offsetNode.childNodes[r.offset-(r.offset>0?1:0)])||(null==r?void 0:r.offsetNode);if(s&&"IMG"===(a=s).nodeName&&e.dom.isEditable(a)){const n=s.getBoundingClientRect();t.preventDefault(),e.hasFocus()||e.focus(),e.selection.select(s),t.clientX<n.left||t.clientY<n.top?e.selection.collapse(!0):(t.clientX>n.right||t.clientY>n.bottom)&&e.selection.collapse(!1)}var a}))})),e.on("keydown",(t=>{if(!f(t)&&t.keyCode===n){if(!e.getBody().getElementsByTagName("hr").length)return;if(s.isCollapsed()&&0===s.getRng().startOffset){const e=s.getNode(),n=e.previousSibling;if("HR"===e.nodeName)return r.remove(e),void t.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(r.remove(n),t.preventDefault())}}})),h(),(()=>{const n=()=>{const n=r.getAttribs(s.getStart().cloneNode(!1));return()=>{const o=s.getStart();o!==e.getBody()&&(r.setAttrib(o,"style",null),t(n,(e=>{o.setAttributeNode(e.cloneNode(!0))})))}},o=()=>!s.isCollapsed()&&r.getParent(s.getStart(),r.isBlock)!==r.getParent(s.getEnd(),r.isBlock);e.on("keypress",(t=>{let r;return!(!(f(t)||8!==t.keyCode&&46!==t.keyCode)&&o()&&(r=n(),e.getDoc().execCommand("delete",!1),r(),t.preventDefault(),1))})),r.bind(e.getDoc(),"cut",(t=>{if(!f(t)&&o()){const t=n();bg.setEditorTimeout(e,(()=>{t()}))}}))})(),y(),e.on("SetContent ExecCommand",(e=>{"setcontent"!==e.type&&"mceInsertLink"!==e.command||t(r.select("a:not([data-mce-block])"),(e=>{var t;let n=e.parentNode;const o=r.getRoot();if((null==n?void 0:n.lastChild)===e){for(;n&&!r.isBlock(n);){if((null===(t=n.parentNode)||void 0===t?void 0:t.lastChild)!==n||n===o)return;n=n.parentNode}r.add(n,"br",{"data-mce-bogus":1})}}))})),C(),x(),v())),{refreshContentEditable:N,isHidden:()=>{if(!l||e.removed)return!1;const t=e.selection.getSel();return!t||!t.rangeCount||0===t.rangeCount}}},vB=li.DOM,yB=e=>e.inline?e.getElement().nodeName.toLowerCase():void 0,CB=e=>Ce(e,(e=>!1===v(e))),wB=e=>{const t=e.options.get,n=e.editorUpload.blobCache;return CB({allow_conditional_comments:t("allow_conditional_comments"),allow_html_data_urls:t("allow_html_data_urls"),allow_svg_data_urls:t("allow_svg_data_urls"),allow_html_in_named_anchor:t("allow_html_in_named_anchor"),allow_script_urls:t("allow_script_urls"),allow_html_in_comments:t("allow_html_in_comments"),allow_mathml_annotation_encodings:t("allow_mathml_annotation_encodings"),allow_unsafe_link_target:t("allow_unsafe_link_target"),convert_unsafe_embeds:t("convert_unsafe_embeds"),convert_fonts_to_spans:t("convert_fonts_to_spans"),extended_mathml_attributes:t("extended_mathml_attributes"),extended_mathml_elements:t("extended_mathml_elements"),fix_list_elements:t("fix_list_elements"),font_size_legacy_values:t("font_size_legacy_values"),forced_root_block:t("forced_root_block"),forced_root_block_attrs:t("forced_root_block_attrs"),preserve_cdata:t("preserve_cdata"),inline_styles:t("inline_styles"),root_name:yB(e),sandbox_iframes:t("sandbox_iframes"),sandbox_iframes_exclusions:ym(e),sanitize:t("xss_sanitization"),validate:!0,blob_cache:n,document:e.getDoc()})},EB=e=>{const t=e.options.get;return CB({custom_elements:t("custom_elements"),extended_valid_elements:t("extended_valid_elements"),invalid_elements:t("invalid_elements"),invalid_styles:t("invalid_styles"),schema:t("schema"),valid_children:t("valid_children"),valid_classes:t("valid_classes"),valid_elements:t("valid_elements"),valid_styles:t("valid_styles"),verify_html:t("verify_html"),padd_empty_block_inline_children:t("format_empty_lines")})},xB=e=>e.inline?e.ui.styleSheetLoader:e.dom.styleSheetLoader,SB=e=>{const t=xB(e),n=ac(e),o=e.contentCSS,r=()=>{t.unloadAll(o),e.inline||e.ui.styleSheetLoader.unloadAll(n)},s=()=>{e.removed?r():e.on("remove",r)};if(e.contentStyles.length>0){let t="";dn.each(e.contentStyles,(e=>{t+=e+"\r\n"})),e.dom.addStyle(t)}const a=Promise.all(((e,t,n)=>{const{pass:o,fail:r}=K(t,(e=>tinymce.Resource.has(sx(e)))),s=o.map((t=>{const n=tinymce.Resource.get(sx(t));return u(n)?Promise.resolve(xB(e).loadRawCss(t,n)):Promise.resolve()})),a=[...s,xB(e).loadAll(r)];return e.inline?a:a.concat([e.ui.styleSheetLoader.loadAll(n)])})(e,o,n)).then(s).catch(s),i=sc(e);return i&&((e,t)=>{const n=mn.fromDom(e.getBody()),o=Xn(Gn(n)),r=mn.fromTag("style");ho(r,"type","text/css"),mo(r,mn.fromText(t)),mo(o,r),e.on("remove",(()=>{_o(r)}))})(e,i),a},_B=e=>{!0!==e.removed&&((e=>{lE(e)||e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"})})(e),(e=>{e.bindPendingEventDelegates(),e.initialized=!0,(e=>{e.dispatch("Init")})(e),e.focus(!0),(e=>{const t=e.dom.getRoot();e.inline||ff(e)&&e.selection.getStart(!0)!==t||Iu(t).each((t=>{const n=t.getNode(),o=ns(n)?Iu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e),e.nodeChanged({initial:!0});const t=$c(e);w(t)&&t.call(e,e),(e=>{const t=Vc(e);t&&bg.setEditorTimeout(e,(()=>{let n;n=!0===t?e:e.editorManager.get(t),n&&!n.destroyed&&(n.focus(),n.selection.scrollIntoView())}),100)})(e),ex(e)&&nx(e,!0)})(e))},kB=e=>{const t=e.getElement();let n=e.getDoc();e.inline&&(vB.addClass(t,"mce-content-body"),e.contentDocument=n=document,e.contentWindow=window,e.bodyElement=t,e.contentAreaContainer=t);const o=e.getBody();o.disabled=!0,e.readonly=Tc(e),e._editableRoot=Oc(e),!xm(e)&&e.hasEditableRoot()&&(e.inline&&"static"===vB.getStyle(o,"position",!0)&&(o.style.position="relative"),o.contentEditable="true"),o.disabled=!1,e.editorUpload=fx(e),e.schema=Pa(EB(e)),e.dom=li(n,{keep_values:!0,url_converter:e.convertURL,url_converter_scope:e,update_styles:!0,root_element:e.inline?e.getBody():null,collect:e.inline,schema:e.schema,contentCssCors:Qd(e),referrerPolicy:Zd(e),crossOrigin:Jd(e),onSetAttrib:t=>{e.dispatch("SetAttrib",t)}}),e.parser=(e=>{const t=VC(wB(e),e.schema);return t.addAttributeFilter("src,href,style,tabindex",((t,n)=>{const o=e.dom,r="data-mce-"+n;let s=t.length;for(;s--;){const a=t[s];let i=a.attr(n);if(i&&!a.attr(r)){if(0===i.indexOf("data:")||0===i.indexOf("blob:"))continue;"style"===n?(i=o.serializeStyle(o.parseStyle(i),a.name),i.length||(i=null),a.attr(r,i),a.attr(n,i)):"tabindex"===n?(a.attr(r,i),a.attr(n,null)):a.attr(r,e.convertURL(i,n,a.name))}}})),t.addNodeFilter("script",(e=>{let t=e.length;for(;t--;){const n=e[t],o=n.attr("type")||"no/type";0!==o.indexOf("mce-")&&n.attr("type","mce-"+o)}})),cm(e)&&t.addNodeFilter("#cdata",(t=>{var n;let o=t.length;for(;o--;){const r=t[o];r.type=8,r.name="#comment",r.value="[CDATA["+e.dom.encode(null!==(n=r.value)&&void 0!==n?n:"")+"]]"}})),t.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",(t=>{let n=t.length;const o=e.schema.getNonEmptyElements();for(;n--;){const e=t[n];e.isEmpty(o)&&0===e.getAll("br").length&&e.append(new xp("br",1))}})),t})(e),e.serializer=bE((e=>{const t=e.options.get;return{...wB(e),...EB(e),...CB({remove_trailing_brs:t("remove_trailing_brs"),pad_empty_with_br:t("pad_empty_with_br"),url_converter:t("url_converter"),url_converter_scope:t("url_converter_scope"),element_format:t("element_format"),entities:t("entities"),entity_encoding:t("entity_encoding"),indent:t("indent"),indent_after:t("indent_after"),indent_before:t("indent_before")})}})(e),e),e.selection=gE(e.dom,e.getWin(),e.serializer,e),e.annotator=sg(e),e.formatter=Sx(e),e.undoManager=kx(e),e._nodeChangeDispatcher=new CT(e),e._selectionOverrides=IO(e),yT(e),(e=>{const t=We(),n=Ne(!1),o=at((t=>{e.dispatch("longpress",{...t,type:"longpress"}),n.set(!0)}),400);e.on("touchstart",(e=>{AN(e).each((r=>{o.cancel();const s={x:r.clientX,y:r.clientY,target:e.target};o.throttle(e),n.set(!1),t.set(s)}))}),!0),e.on("touchmove",(r=>{o.cancel(),AN(r).each((o=>{t.on((r=>{((e,t)=>{const n=Math.abs(e.clientX-t.x),o=Math.abs(e.clientY-t.y);return n>5||o>5})(o,r)&&(t.clear(),n.set(!1),e.dispatch("longpresscancel"))}))}))}),!0),e.on("touchend touchcancel",(r=>{o.cancel(),"touchcancel"!==r.type&&t.get().filter((e=>e.target.isEqualNode(r.target))).each((()=>{n.get()?r.preventDefault():e.dispatch("tap",{...r,type:"tap"})}))}),!0)})(e),(e=>{(e=>{e.on("click",(t=>{e.dom.getParent(t.target,"details")&&t.preventDefault()}))})(e),(e=>{e.parser.addNodeFilter("details",(t=>{const n=hm(e);q(t,(e=>{"expanded"===n?e.attr("open","open"):"collapsed"===n&&e.attr("open",null)}))})),e.serializer.addNodeFilter("details",(t=>{const n=bm(e);q(t,(e=>{"expanded"===n?e.attr("open","open"):"collapsed"===n&&e.attr("open",null)}))}))})(e)})(e),(e=>{const t="contenteditable",n=" "+dn.trim(lm(e))+" ",o=" "+dn.trim(im(e))+" ",r=PN(n),s=PN(o),a=dm(e);a.length>0&&e.on("BeforeSetContent",(t=>{((e,t,n)=>{let o=t.length,r=n.content;if("raw"!==n.format){for(;o--;)r=r.replace(t[o],LN(e,r,im(e)));n.content=r}})(e,a,t)})),e.parser.addAttributeFilter("class",(e=>{let n=e.length;for(;n--;){const o=e[n];r(o)?o.attr(t,"true"):s(o)&&o.attr(t,"false")}})),e.serializer.addAttributeFilter(t,(e=>{let n=e.length;for(;n--;){const o=e[n];if(!r(o)&&!s(o))continue;const i=o.attr("data-mce-content");a.length>0&&i?MN(a,i)?(o.name="#text",o.type=3,o.raw=!0,o.value=i):o.remove():o.attr(t,null)}}))})(e),lE(e)||((e=>{e.on("mousedown",(t=>{t.detail>=3&&(t.preventDefault(),cO(e))}))})(e),(e=>{hB(e)})(e));const r=gT(e);((e,t)=>{e.addCommand("delete",(()=>{NN(e,t)})),e.addCommand("forwardDelete",(()=>{((e,t)=>{kN(e,t,!0).fold((()=>{e.selection.isEditable()&&ab(e)}),P),JS(e)&&Xx(e.dom,e.getBody())})(e,t)}))})(e,r),(e=>{e.on("NodeChange",(()=>(e=>{const t=e.dom,n=e.selection,o=e.schema,r=o.getBlockElements(),s=n.getStart(),a=e.getBody();let i,l,d=null;const c=Ld(e);if(!s||!Xr(s))return;const m=a.nodeName.toLowerCase();if(!o.isValidChild(m,c.toLowerCase())||((e,t,n)=>H(dh(mn.fromDom(n),mn.fromDom(t)),(t=>RN(e,t.dom))))(r,a,s))return;if(a.firstChild===a.lastChild&&ms(a.firstChild))return i=ON(e),i.appendChild(Ii().dom),a.replaceChild(i,a.firstChild),e.selection.setCursorLocation(i,0),void e.nodeChanged();let u=a.firstChild;for(;u;)if(Xr(u)&&Hs(o,u),DN(o,u)){if(TN(r,u)){l=u,u=u.nextSibling,t.remove(l);continue}if(!i){if(!d&&e.hasFocus()&&(d=O_(e.selection.getRng(),(()=>document.createElement("span")))),!u.parentNode){u=null;break}i=ON(e),a.insertBefore(i,u)}l=u,u=u.nextSibling,i.appendChild(l)}else i=null,u=u.nextSibling;d&&(e.selection.setRng(B_(d)),e.nodeChanged())})(e)))})(e),(e=>{var t;const n=e.dom,o=Ld(e),r=null!==(t=mc(e))&&void 0!==t?t:"",s=(t,a)=>{if((e=>{if(Rx(e)){const t=e.keyCode;return!Dx(e)&&(Bg.metaKeyPressed(e)||e.altKey||t>=112&&t<=123||$(Nx,t))}return!1})(t))return;const i=e.getBody(),l=!(e=>Rx(e)&&!(Dx(e)||"keyup"===e.type&&229===e.keyCode))(t)&&((e,t,n)=>{if(e.isEmpty(t,void 0,{skipBogus:!1,includeZwsp:!0})){const o=t.firstElementChild;return!o||!e.getStyle(t.firstElementChild,"padding-left")&&!e.getStyle(t.firstElementChild,"padding-right")&&n===o.nodeName.toLowerCase()}return!1})(n,i,o);(""!==n.getAttrib(i,Ax)!==l||a)&&(n.setAttrib(i,Ax,l?r:null),((e,t)=>{e.dispatch("PlaceholderToggle",{state:t})})(e,l),e.on(l?"keydown":"keyup",s),e.off(l?"keyup":"keydown",s))};nt(r)&&e.on("init",(t=>{s(t,!0),e.on("change SetContent ExecCommand",s),e.on("paste",(t=>bg.setEditorTimeout(e,(()=>s(t)))))}))})(e),oO(e,r);const s=(e=>{const t=e;return(e=>xe(e.plugins,"rtc").bind((e=>I.from(e.setup))))(e).fold((()=>(t.rtcInstance=iE(e),I.none())),(e=>(t.rtcInstance=(()=>{const e=N(null),t=N("");return{init:{bindEvents:S},undoManager:{beforeChange:S,add:e,undo:e,redo:e,clear:S,reset:S,hasUndo:L,hasRedo:L,transact:e,ignore:S,extra:S},formatter:{match:L,matchAll:N([]),matchNode:N(void 0),canApply:L,closest:t,apply:S,remove:S,toggle:S,formatChanged:N({unbind:S})},editor:{getContent:t,setContent:N({content:"",html:""}),insertContent:N(""),addVisual:S},selection:{getContent:t},autocompleter:{addDecoration:S,removeDecoration:S},raw:{getModel:N(I.none())}}})(),I.some((()=>e().then((e=>(t.rtcInstance=(e=>{const t=e=>f(e)?e:{},{init:n,undoManager:o,formatter:r,editor:s,selection:a,autocompleter:i,raw:l}=e;return{init:{bindEvents:n.bindEvents},undoManager:{beforeChange:o.beforeChange,add:o.add,undo:o.undo,redo:o.redo,clear:o.clear,reset:o.reset,hasUndo:o.hasUndo,hasRedo:o.hasRedo,transact:(e,t,n)=>o.transact(n),ignore:(e,t)=>o.ignore(t),extra:(e,t,n,r)=>o.extra(n,r)},formatter:{match:(e,n,o,s)=>r.match(e,t(n),s),matchAll:r.matchAll,matchNode:r.matchNode,canApply:e=>r.canApply(e),closest:e=>r.closest(e),apply:(e,n,o)=>r.apply(e,t(n)),remove:(e,n,o,s)=>r.remove(e,t(n)),toggle:(e,n,o)=>r.toggle(e,t(n)),formatChanged:(e,t,n,o,s)=>r.formatChanged(t,n,o,s)},editor:{getContent:e=>s.getContent(e),setContent:(e,t)=>({content:s.setContent(e,t),html:""}),insertContent:(e,t)=>(s.insertContent(e),""),addVisual:s.addVisual},selection:{getContent:(e,t)=>a.getContent(t)},autocompleter:{addDecoration:i.addDecoration,removeDecoration:i.removeDecoration},raw:{getModel:()=>I.some(l.getRawModel())}}})(e),e.rtc.isRemote))))))))})(e);(e=>{const t=e.getDoc(),n=e.getBody();(e=>{e.dispatch("PreInit")})(e),qc(e)||(t.body.spellcheck=!1,vB.setAttrib(n,"spellcheck","false")),e.quirks=bB(e),(e=>{e.dispatch("PostRender")})(e);const o=ic(e);void 0!==o&&(n.dir=o);const r=Wc(e);r&&e.on("BeforeSetContent",(e=>{dn.each(r,(t=>{e.content=e.content.replace(t,(e=>"\x3c!--mce:protected "+escape(e)+"--\x3e"))}))})),e.on("SetContent",(()=>{e.addVisual(e.getBody())})),e.on("compositionstart compositionend",(t=>{e.composing="compositionstart"===t.type}))})(e),s.fold((()=>{const t=(e=>{let t=!1;const n=setTimeout((()=>{t||e.setProgressState(!0)}),500);return()=>{clearTimeout(n),t=!0,e.setProgressState(!1)}})(e);SB(e).then((()=>{_B(e),t()}))}),(t=>{e.setProgressState(!0),SB(e).then((()=>{t().then((t=>{e.setProgressState(!1),_B(e),mE(e)}),(t=>{e.notificationManager.open({type:"error",text:String(t)}),_B(e),mE(e)}))}))}))},NB=li.DOM,AB=li.DOM,RB=(e,t)=>({editorContainer:e,iframeContainer:t,api:{}}),DB=e=>{const t=e.getElement();return e.inline?RB(null):(e=>{const t=AB.create("div");return AB.insertAfter(t,e),RB(t,t)})(t)},TB=async e=>{e.dispatch("ScriptsLoaded"),(e=>{const t=dn.trim(qd(e)),n=e.ui.registry.getAll().icons,o={...wE.get("default").icons,...wE.get(t).icons};pe(o,((t,o)=>{Se(n,o)||e.ui.registry.addIcon(o,t)}))})(e),(e=>{const t=t=>{t.keyCode!==Bg.ESC||t.defaultPrevented||(e=>e.dispatch("CloseActiveTooltips"))(e).isDefaultPrevented()&&t.preventDefault()};document.addEventListener("keyup",t),e.inline||e.on("keyup",t),e.on("remove",(()=>{document.removeEventListener("keyup",t),e.inline||e.off("keyup",t)}))})(e),(e=>{const t=gc(e);if(u(t)){const n=TE.get(t);e.theme=n(e,TE.urls[t])||{},w(e.theme.init)&&e.theme.init(e,TE.urls[t]||e.editorManager.documentBaseURL.replace(/\/$/,""))}else e.theme={}})(e),(e=>{const t=hc(e),n=EE.get(t);e.model=n(e,EE.urls[t])})(e),(e=>{GE.init(e)})(e),(e=>{const t=[];q(Pc(e),(n=>{((e,t,n)=>{const o=DE.get(n),r=DE.urls[n]||e.editorManager.documentBaseURL.replace(/\/$/,"");if(n=dn.trim(n),o&&-1===dn.inArray(t,n)){if(e.plugins[n])return;try{const s=o(e,r)||{};e.plugins[n]=s,w(s.init)&&(s.init(e,r),t.push(n))}catch(t){((e,t,n)=>{const o=gi.translate(["Failed to initialize plugin: {0}",t]);gd(e,"PluginLoadError",{message:o}),IE(o,n),PE(e,o)})(e,n,t)}}})(e,t,(e=>e.replace(/^\-/,""))(n))}))})(e);const t=await(e=>{const t=e.getElement();return e.orgDisplay=t.style.display,u(gc(e))?(e=>{const t=e.theme.renderUI;return t?t():DB(e)})(e):w(gc(e))?(e=>{const t=e.getElement(),n=gc(e)(e,t);return n.editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||e.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||e.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:t.offsetHeight,n})(e):DB(e)})(e);((e,t)=>{const n={show:I.from(t.show).getOr(S),hide:I.from(t.hide).getOr(S),isEnabled:I.from(t.isEnabled).getOr(M),setEnabled:n=>{n&&("readonly"===e.mode.get()||ex(e))||I.from(t.setEnabled).each((e=>e(n)))}};e.ui={...e.ui,...n}})(e,I.from(t.api).getOr({})),e.editorContainer=t.editorContainer,(e=>{e.contentCSS=e.contentCSS.concat((e=>ax(e,rc(e)))(e),(e=>ax(e,ac(e)))(e))})(e),e.inline?kB(e):((e,t)=>{((e,t)=>{const n=rn.browser.isFirefox()?zc(e):"Rich Text Area",o=e.translate(n),r=yo(mn.fromDom(e.getElement()),"tabindex").bind(rt),s=((e,t,n,o)=>{const r=mn.fromTag("iframe");return o.each((e=>ho(r,"tabindex",e))),bo(r,n),bo(r,{id:e+"_ifr",frameBorder:"0",allowTransparency:"true",title:t}),gr(r,"tox-edit-area__iframe"),r})(e.id,o,Ad(e),r).dom;s.onload=()=>{s.onload=null,e.dispatch("load")},e.contentAreaContainer=t.iframeContainer,e.iframeElement=s,e.iframeHTML=(e=>{let t=Rd(e)+"<html><head>";Dd(e)!==e.editorManager.documentBaseURL&&(t+='<base href="'+e.documentBaseURI.getURI()+'" />'),t+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />';const n=Td(e),o=Od(e),r=e.translate(zc(e));return Bd(e)&&(t+='<meta http-equiv="Content-Security-Policy" content="'+Bd(e)+'" />'),t+=`</head><body id="${n}" class="mce-content-body ${o}" data-id="${e.id}" aria-label="${r}"><br></body></html>`,t})(e),NB.add(t.iframeContainer,s)})(e,t),t.editorContainer&&(t.editorContainer.style.display=e.orgDisplay,e.hidden=NB.isHidden(t.editorContainer)),e.getElement().style.display="none",NB.setAttrib(e.id,"aria-hidden","true"),e.getElement().style.visibility=e.orgVisibility,(e=>{const t=e.iframeElement,n=()=>{e.contentDocument=t.contentDocument,kB(e)};if(fm(e)||rn.browser.isFirefox()){const t=e.getDoc();t.open(),t.write(e.iframeHTML),t.close(),n()}else{const r=(o=mn.fromDom(t),eo(o,"load",no,(()=>{r.unbind(),n()})));t.srcdoc=e.iframeHTML}var o})(e)})(e,{editorContainer:t.editorContainer,iframeContainer:t.iframeContainer})},OB=li.DOM,BB=e=>"-"===e.charAt(0),PB=(e,t,n)=>I.from(t).filter((e=>nt(e)&&!wE.has(e))).map((t=>({url:`${e.editorManager.baseURL}/icons/${t}/icons${n}.js`,name:I.some(t)}))),LB=(e,t)=>{const n=ci.ScriptLoader,o=()=>{!e.removed&&(e=>{const t=gc(e);return!u(t)||C(TE.get(t))})(e)&&(e=>{const t=hc(e);return C(EE.get(t))})(e)&&TB(e)};((e,t)=>{const n=gc(e);if(u(n)&&!BB(n)&&!Se(TE.urls,n)){const o=pc(e),r=o?e.documentBaseURI.toAbsolute(o):`themes/${n}/theme${t}.js`;TE.load(n,r).catch((()=>{((e,t,n)=>{LE(e,"ThemeLoadError",ME("theme",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=hc(e);if("plugin"!==n&&!Se(EE.urls,n)){const o=bc(e),r=u(o)?e.documentBaseURI.toAbsolute(o):`models/${n}/model${t}.js`;EE.load(n,r).catch((()=>{((e,t,n)=>{LE(e,"ModelLoadError",ME("model",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{GE.load(e,t)})(e,t),((e,t)=>{const n=ec(t),o=tc(t);if(!gi.hasCode(n)&&"en"!==n){const r=nt(o)?o:`${t.editorManager.baseURL}/langs/${n}.js`;e.add(r).catch((()=>{((e,t,n)=>{LE(e,"LanguageLoadError",ME("language",t,n))})(t,r,n)}))}})(n,e),((e,t,n)=>{const o=PB(t,"default",n),r=(e=>I.from(Wd(e)).filter(nt).map((e=>({url:e,name:I.none()}))))(t).orThunk((()=>PB(t,qd(t),"")));q((e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t})([o,r]),(n=>{e.add(n.url).catch((()=>{((e,t,n)=>{LE(e,"IconsLoadError",ME("icons",t,n))})(t,n.url,n.name.getOrUndefined())}))}))})(n,e,t),((e,t)=>{const n=(t,n)=>{"licensekeymanager"!==t&&DE.load(t,n).catch((()=>{((e,t,n)=>{LE(e,"PluginLoadError",ME("plugin",t,n))})(e,n,t)}))};pe(Lc(e),((t,o)=>{n(o,t),e.options.set("plugins",Pc(e).concat(o))})),q(Pc(e),(e=>{!(e=dn.trim(e))||DE.urls[e]||BB(e)||n(e,`plugins/${e}/plugin${t}.js`)}))})(e,t),n.loadQueue().then(o,o)},MB=["#E41B60","#AD1457","#1939EC","#001CB5","#648000","#465B00","#006CE7","#0054B4","#00838F","#006064","#00866F","#004D40","#51742F","#385021","#CF4900","#A84600","#CC0000","#6A1B9A","#9C27B0","#6A00AB","#3041BA","#0A1877","#774433","#452B24","#607D8B","#455A64"],IB=e=>{const t=((e,t,n=36)=>{const o=n/2;return`<svg height="${n}" width="${n}" xmlns="http://www.w3.org/2000/svg"><circle cx="${o}" cy="${o}" r="${o}" fill="${t}"/><text x="50%" y="50%" text-anchor="middle" dominant-baseline="central" fill="#FFF" font-family="sans-serif" font-size="${o}">`+(e=>{var t;return Intl.Segmenter?`${null===(t=(new Intl.Segmenter).segment(e)[Symbol.iterator]().next().value)||void 0===t?void 0:t.segment}`:e.trim()[0]})(e)+"</text></svg>"})(e,(()=>{const e=Math.floor(Oe()*MB.length);return MB[e]})());return"data:image/svg+xml,"+encodeURIComponent(t)},FB=OR([LR("id","id",{tag:"required",process:{}},wR()),zR("name"),zR("avatar"),(e=>LR(e,e,{tag:"option",process:{}},wR()))("custom")]),UB=e=>{const t={};return pe(e,((e,n)=>{e.each((e=>{t[n]=e}))})),t},zB=e=>{if(!Array.isArray(e))throw new Error("fetch_users must return an array");const t=V(e,(e=>BR("Invalid user object",FB,e))),{errors:n,values:o}=Ve(t);if(n.length>0){const e=V(n,((e,t)=>`User at index ${t}: ${PR(e)}`));console.warn("User validation errors:\n"+e.join("\n"))}return V(o,(e=>{const{id:t,name:n,avatar:o,...r}=e;return{id:t,name:n.getOr(t),avatar:o.getOr(IB(n.getOr(t))),...UB(r)}}))},jB=Gt().deviceType,$B=jB.isPhone(),HB=jB.isTablet(),VB=e=>{if(y(e))return[];{const t=p(e)?e:e.split(/[ ,]/),n=V(t,Je);return Y(n,nt)}},qB=(e,t)=>{const n=(t=>{const n={},o={};return ye(t,((t,n)=>$(e,n)),ve(n),ve(o)),{t:n,f:o}})(t);return o=n.t,r=n.f,{sections:N(o),options:N(r)};var o,r},WB=(e,t)=>Se(e.sections(),t),KB=(e,t)=>({table_grid:!1,object_resizing:!1,resize:!1,toolbar_mode:xe(e,"toolbar_mode").getOr("scrolling"),toolbar_sticky:!1,...t?{menubar:!1}:{}}),YB=(e,t)=>{var n;const o=null!==(n=t.external_plugins)&&void 0!==n?n:{};return e&&e.external_plugins?dn.extend({},e.external_plugins,o):o},GB=(e,t,n,o,r)=>{var s;const a=e?{mobile:KB(null!==(s=r.mobile)&&void 0!==s?s:{},t)}:{},i=qB(["mobile"],Ie(a,r)),l=dn.extend(n,o,i.options(),((e,t)=>e&&WB(t,"mobile"))(e,i)?((e,t,n={})=>{const o=e.sections(),r=xe(o,t).getOr({});return dn.extend({},n,r)})(i,"mobile"):{},{external_plugins:YB(o,i.options())});return((e,t,n,o)=>{const r=VB(n.forced_plugins),s=VB(o.plugins),a=((e,t)=>WB(e,t)?e.sections()[t]:{})(t,"mobile"),i=((e,t,n,o)=>e&&WB(t,"mobile")?o:n)(e,t,s,a.plugins?VB(a.plugins):s),l=((e,t)=>[...VB(e),...VB(t)])(r,i);return dn.extend(o,{forced_plugins:r,plugins:l})})(e,i,o,l)},XB=e=>{(e=>{const t=t=>()=>{q("left,center,right,justify".split(","),(n=>{t!==n&&e.formatter.remove("align"+n)})),"none"!==t&&(t=>{e.formatter.toggle(t,void 0),e.nodeChanged()})("align"+t)};e.editorCommands.addCommands({JustifyLeft:t("left"),JustifyCenter:t("center"),JustifyRight:t("right"),JustifyFull:t("justify"),JustifyNone:t("none")})})(e),(e=>{const t=t=>()=>{const n=e.selection,o=n.isCollapsed()?[e.dom.getParent(n.getNode(),e.dom.isBlock)]:n.getSelectedBlocks();return H(o,(n=>C(e.formatter.matchNode(n,t))))};e.editorCommands.addCommands({JustifyLeft:t("alignleft"),JustifyCenter:t("aligncenter"),JustifyRight:t("alignright"),JustifyFull:t("alignjustify")},"state")})(e)},QB=(e,t)=>{const n=e.selection,o=e.dom;return/^ | $/.test(t)?((e,t,n,o)=>{const r=mn.fromDom(e.getRoot());return n=Oh(r,Ul.fromRangeStart(t),o)?n.replace(/^ /,"&nbsp;"):n.replace(/^&nbsp;/," "),Bh(r,Ul.fromRangeEnd(t),o)?n.replace(/(&nbsp;| )(<br( \/)>)?$/,"&nbsp;"):n.replace(/&nbsp;(<br( \/)?>)?$/," ")})(o,n.getRng(),t,e.schema):t},ZB=(e,t)=>{if(e.selection.isEditable()){const{content:n,details:o}=(e=>{if("string"!=typeof e){const t=dn.extend({paste:e.paste,data:{paste:e.paste}},e);return{content:e.content,details:t}}return{content:e,details:{}}})(t);XC(e,{...o,content:QB(e,n),format:"html",set:!1,selection:!0}).each((t=>{const n=((e,t,n)=>dE(e).editor.insertContent(t,n))(e,t.content,o);QC(e,n,t),e.addVisual()}))}},JB={"font-size":"size","font-family":"face"},eP=Tn("font"),tP=e=>(t,n)=>I.from(n).map(mn.fromDom).filter(Nn).bind((n=>((e,t,n)=>Ar(mn.fromDom(n),(t=>(t=>jo(t,e).orThunk((()=>eP(t)?xe(JB,e).bind((e=>yo(t,e))):I.none())))(t)),(e=>vn(mn.fromDom(t),e))))(e,t,n.dom).or(((e,t)=>I.from(li.DOM.getStyle(t,e,!0)))(e,n.dom)))).getOr(""),nP=tP("font-size"),oP=_((e=>e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")),tP("font-family")),rP=e=>Iu(e.getBody()).bind((e=>{const t=e.container();return I.from(ss(t)?t.parentNode:t)})),sP=(e,t)=>((e,t)=>(e=>I.from(e.selection.getRng()).bind((t=>{const n=e.getBody();return t.startContainer===n&&0===t.startOffset?I.none():I.from(e.selection.getStart(!0))})))(e).orThunk(D(rP,e)).map(mn.fromDom).filter(Nn).bind(t))(e,k(I.some,t)),aP=(e,t)=>{if(/^[0-9.]+$/.test(t)){const n=parseInt(t,10);if(n>=1&&n<=7){const o=(e=>dn.explode(e.options.get("font_size_style_values")))(e),r=(e=>dn.explode(e.options.get("font_size_classes")))(e);return r.length>0?r[n-1]||t:o[n-1]||t}return t}return t},iP=e=>{const t=e.split(/\s*,\s*/);return V(t,(e=>-1===e.indexOf(" ")||Xe(e,'"')||Xe(e,"'")?e:`'${e}'`)).join(",")},lP=e=>{e.editorCommands.addCommands({Indent:()=>{(e=>{EN(e,"indent")})(e)},Outdent:()=>{xN(e)}}),e.editorCommands.addCommands({Outdent:()=>yN(e),Indent:()=>(e=>!e.mode.isReadOnly()&&(e=>Nm(e).forall((t=>{const n=e.selection.getSelectedBlocks();return H(n,(e=>dr(mn.fromDom(e),"li").forall((e=>{return(n=e,wr(n,(e=>hn(e,"ol,ul")),void 0)).length<=t;var n}))))})))(e))(e)},"state")},dP=(e,t)=>{if(e.mode.isReadOnly())return;const n=e.dom,o=e.selection.getRng(),r=t?e.selection.getStart():e.selection.getEnd(),s=t?o.startContainer:o.endContainer,a=gD(n,s);if(!a||!a.isContentEditable)return;const i=t?io:lo,l=Ld(e);((e,t,n,o)=>{const r=e.dom,s=e=>r.isBlock(e)&&e.parentElement===n,a=s(t)?t:r.getParent(o,s,n);return I.from(a).map(mn.fromDom)})(e,r,a,s).each((t=>{const n=vD(e,s,t.dom,a,!1,l);i(t,mn.fromDom(n)),e.selection.setCursorLocation(n,0),e.dispatch("NewBlock",{newBlock:n}),iD(e,"insertParagraph")}))},cP=e=>{XB(e),(e=>{e.editorCommands.addCommands({"Cut,Copy,Paste":t=>{const n=e.getDoc();let o;try{n.execCommand(t)}catch(e){o=!0}if("paste"!==t||n.queryCommandEnabled(t)||(o=!0),o||!n.queryCommandSupported(t)){let t=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");(rn.os.isMacOS()||rn.os.isiOS())&&(t=t.replace(/Ctrl\+/g,"\u2318+")),e.notificationManager.open({text:t,type:"error"})}}})})(e),(e=>{e.editorCommands.addCommands({mceAddUndoLevel:()=>{e.undoManager.add()},mceEndUndoLevel:()=>{e.undoManager.add()},Undo:()=>{e.undoManager.undo()},Redo:()=>{e.undoManager.redo()}})})(e),(e=>{e.editorCommands.addCommands({mceSelectNodeDepth:(t,n,o)=>{let r=0;e.dom.getParent(e.selection.getNode(),(t=>!Xr(t)||r++!==o||(e.selection.select(t),!1)),e.getBody())},mceSelectNode:(t,n,o)=>{e.selection.select(o)},selectAll:()=>{const t=e.dom.getParent(e.selection.getStart(),gs);if(t){const n=e.dom.createRng();n.selectNodeContents(t),e.selection.setRng(n)}}})})(e),(e=>{e.editorCommands.addCommands({mceCleanup:()=>{const t=e.selection.getBookmark();e.setContent(e.getContent()),e.selection.moveToBookmark(t)},insertImage:(t,n,o)=>{ZB(e,e.dom.createHTML("img",{src:o}))},insertHorizontalRule:()=>{e.execCommand("mceInsertContent",!1,"<hr>")},insertText:(t,n,o)=>{ZB(e,e.dom.encode(o))},insertHTML:(t,n,o)=>{ZB(e,o)},mceInsertContent:(t,n,o)=>{ZB(e,o)},mceSetContent:(t,n,o)=>{e.setContent(o)},mceReplaceContent:(t,n,o)=>{e.execCommand("mceInsertContent",!1,o.replace(/\{\$selection\}/g,e.selection.getContent({format:"text"})))},mceNewDocument:()=>{e.setContent(Qc(e))}})})(e),(e=>{const t=(t,n,o)=>{if(e.mode.isReadOnly())return;const r=u(o)?{href:o}:o,s=e.dom.getParent(e.selection.getNode(),"a");f(r)&&u(r.href)&&(r.href=r.href.replace(/ /g,"%20"),s&&r.href||e.formatter.remove("link"),r.href&&e.formatter.apply("link",r,s))};e.editorCommands.addCommands({unlink:()=>{if(e.selection.isEditable()){if(e.selection.isCollapsed()){const t=e.dom.getParent(e.selection.getStart(),"a");return void(t&&e.dom.remove(t,!0))}e.formatter.remove("link")}},mceInsertLink:t,createLink:t})})(e),lP(e),(e=>{e.editorCommands.addCommands({InsertNewBlockBefore:()=>{(e=>{dP(e,!0)})(e)},InsertNewBlockAfter:()=>{(e=>{dP(e,!1)})(e)}})})(e),(e=>{e.editorCommands.addCommands({insertParagraph:()=>{QD(RD,e)},mceInsertNewLine:(t,n,o)=>{ZD(e,o)},InsertLineBreak:(t,n,o)=>{QD(ID,e)}})})(e),(e=>{(e=>{const t=(t,n)=>{e.formatter.toggle(t,n),e.nodeChanged()};e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>{t(e)},"ForeColor,HiliteColor":(e,n,o)=>{t(e,{value:o})},BackColor:(e,n,o)=>{t("hilitecolor",{value:o})},FontName:(t,n,o)=>{((e,t)=>{const n=aP(e,t);e.formatter.toggle("fontname",{value:iP(n)}),e.nodeChanged()})(e,o)},FontSize:(t,n,o)=>{((e,t)=>{e.formatter.toggle("fontsize",{value:aP(e,t)}),e.nodeChanged()})(e,o)},LineHeight:(t,n,o)=>{((e,t)=>{e.formatter.toggle("lineheight",{value:String(t)}),e.nodeChanged()})(e,o)},Lang:(e,n,o)=>{var r;t(e,{value:o.code,customValue:null!==(r=o.customCode)&&void 0!==r?r:null})},RemoveFormat:t=>{e.formatter.remove(t)},mceBlockQuote:()=>{t("blockquote")},FormatBlock:(e,n,o)=>{t(u(o)?o:"p")},mceToggleFormat:(e,n,o)=>{t(o)}})})(e),(e=>{const t=t=>e.formatter.match(t);e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>t(e),mceBlockQuote:()=>t("blockquote")},"state"),e.editorCommands.addQueryValueHandler("FontName",(()=>(e=>sP(e,(t=>oP(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("FontSize",(()=>(e=>sP(e,(t=>nP(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("LineHeight",(()=>(e=>sP(e,(t=>{const n=mn.fromDom(e.getBody()),o=Ar(t,(e=>jo(e,"line-height")),D(vn,n));return o.getOrThunk((()=>{const e=parseFloat(Uo(t,"line-height")),n=parseFloat(Uo(t,"font-size"));return String(e/n)}))})).getOr(""))(e)))})(e)})(e),(e=>{e.editorCommands.addCommands({mceRemoveNode:(t,n,o)=>{const r=null!=o?o:e.selection.getNode();if(r!==e.getBody()){const t=e.selection.getBookmark();e.dom.remove(r,!0),e.selection.moveToBookmark(t)}},mcePrint:()=>{e.getWin().print()},mceFocus:(t,n,o)=>{((e,t)=>{e.removed||(t?Og(e):(e=>{const t=e.selection,n=e.getBody();let o=t.getRng();e.quirks.refreshContentEditable();const r=e=>{pg(e).each((t=>{e.selection.setRng(t),o=t}))};!Dg(e)&&e.hasEditableRoot()&&r(e);const s=((e,t)=>e.dom.getParent(t,(t=>"true"===e.dom.getContentEditable(t))))(e,t.getNode());if(s&&e.dom.isChildOf(s,n))return((e,t)=>null!==e.dom.getParent(t,(t=>"false"===e.dom.getContentEditable(t))))(e,s)||Rg(n),Rg(s),e.hasEditableRoot()||r(e),Ag(e,o),void Og(e);e.inline||(rn.browser.isOpera()||Rg(n),e.getWin().focus()),(rn.browser.isFirefox()||e.inline)&&(Rg(n),Ag(e,o)),Og(e)})(e))})(e,!0===o)},mceToggleVisualAid:()=>{e.hasVisual=!e.hasVisual,e.addVisual()}})})(e)},mP=["toggleview"],uP=e=>$(mP,e.toLowerCase());class fP{constructor(e){this.commands={state:{},exec:{},value:{}},this.editor=e}execCommand(e,t=!1,n,o){const r=this.editor,s=e.toLowerCase(),a=null==o?void 0:o.skip_focus;if(r.removed)return!1;if("mcefocus"!==s&&(/^(mceAddUndoLevel|mceEndUndoLevel)$/i.test(s)||a?(e=>{pg(e).each((t=>e.selection.setRng(t)))})(r):r.focus()),r.dispatch("BeforeExecCommand",{command:e,ui:t,value:n}).isDefaultPrevented())return!1;const i=this.commands.exec[s];return!!w(i)&&(i(s,t,n,o),r.dispatch("ExecCommand",{command:e,ui:t,value:n,args:o}),!0)}queryCommandState(e){if(!uP(e)&&this.editor.quirks.isHidden()||this.editor.removed)return!1;const t=e.toLowerCase(),n=this.commands.state[t];return!!w(n)&&n(t)}queryCommandValue(e){if(!uP(e)&&this.editor.quirks.isHidden()||this.editor.removed)return"";const t=e.toLowerCase(),n=this.commands.value[t];return w(n)?n(t):""}addCommands(e,t="exec"){const n=this.commands;pe(e,((e,o)=>{q(o.toLowerCase().split(","),(o=>{n[t][o]=e}))}))}addCommand(e,t,n){const o=e.toLowerCase();this.commands.exec[o]=(e,o,r,s)=>t.call(null!=n?n:this.editor,o,r,s)}queryCommandSupported(e){const t=e.toLowerCase();return!!this.commands.exec[t]}addQueryStateHandler(e,t,n){this.commands.state[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}addQueryValueHandler(e,t,n){this.commands.value[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}}const gP=dn.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input beforeinput contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend touchcancel"," ");class pP{static isNative(e){return!!gP[e.toLowerCase()]}constructor(e){this.bindings={},this.settings=e||{},this.scope=this.settings.scope||this,this.toggleEvent=this.settings.toggleEvent||L}fire(e,t){return this.dispatch(e,t)}dispatch(e,t){const n=e.toLowerCase(),o=Wa(n,null!=t?t:{},this.scope);this.settings.beforeFire&&this.settings.beforeFire(o);const r=this.bindings[n];if(r)for(let e=0,t=r.length;e<t;e++){const t=r[e];if(!t.removed){if(t.once&&this.off(n,t.func),o.isImmediatePropagationStopped())return o;if(!1===t.func.call(this.scope,o))return o.preventDefault(),o}}return o}on(e,t,n,o){if(!1===t&&(t=L),t){const r={func:t,removed:!1};o&&dn.extend(r,o);const s=e.toLowerCase().split(" ");let a=s.length;for(;a--;){const e=s[a];let t=this.bindings[e];t||(t=[],this.toggleEvent(e,!0)),t=n?[r,...t]:[...t,r],this.bindings[e]=t}}return this}off(e,t){if(e){const n=e.toLowerCase().split(" ");let o=n.length;for(;o--;){const r=n[o];let s=this.bindings[r];if(!r)return pe(this.bindings,((e,t)=>{this.toggleEvent(t,!1),delete this.bindings[t]})),this;if(s){if(t){const e=K(s,(e=>e.func===t));s=e.fail,this.bindings[r]=s,q(e.pass,(e=>{e.removed=!0}))}else s.length=0;s.length||(this.toggleEvent(e,!1),delete this.bindings[r])}}}else pe(this.bindings,((e,t)=>{this.toggleEvent(t,!1)})),this.bindings={};return this}once(e,t,n){return this.on(e,t,n,{once:!0})}has(e){e=e.toLowerCase();const t=this.bindings[e];return!(!t||0===t.length)}}const hP=e=>(e._eventDispatcher||(e._eventDispatcher=new pP({scope:e,toggleEvent:(t,n)=>{pP.isNative(t)&&e.toggleNativeEvent&&e.toggleNativeEvent(t,n)}})),e._eventDispatcher),bP={fire(e,t,n){return cw("fire"),this.dispatch(e,t,n)},dispatch(e,t,n){const o=this;if(o.removed&&"remove"!==e&&"detach"!==e)return Wa(e.toLowerCase(),null!=t?t:{},o);const r=hP(o).dispatch(e,t);if(!1!==n&&o.parent){let t=o.parent();for(;t&&!r.isPropagationStopped();)t.dispatch(e,r,!1),t=t.parent?t.parent():void 0}return r},on(e,t,n){return hP(this).on(e,t,n)},off(e,t){return hP(this).off(e,t)},once(e,t){return hP(this).once(e,t)},hasEventListeners(e){return hP(this).has(e)}},vP=li.DOM;let yP;const CP=(e,t)=>{if("selectionchange"===t)return e.getDoc();if(!e.inline&&/^(?:mouse|touch|click|contextmenu|drop|dragover|dragend)/.test(t))return e.getDoc().documentElement;const n=uc(e);return n?(e.eventRoot||(e.eventRoot=vP.select(n)[0]),e.eventRoot):e.getBody()},wP=(e,t,n)=>{(e=>!e.hidden&&!ex(e))(e)?e.dispatch(t,n):ex(e)&&((e,t)=>{if((e=>"click"===e.type)(t)&&!Bg.metaKeyPressed(t)){const n=mn.fromDom(t.target);((e,t)=>dr(t,"a",(t=>vn(t,mn.fromDom(e.getBody())))).bind((e=>yo(e,"href"))))(e,n).each((n=>{if(t.preventDefault(),/^#/.test(n)){const t=e.dom.select(`${n},[name="${Ye(n,"#")}"]`);t.length&&e.selection.scrollIntoView(t[0],!0)}else window.open(n,"_blank","rel=noopener noreferrer,menubar=yes,toolbar=yes,location=yes,status=yes,resizable=yes,scrollbars=yes")}))}else(e=>$(rx,e.type))(t)&&e.dispatch(t.type,t)})(e,n)},EP=(e,t)=>{if(e.delegates||(e.delegates={}),e.delegates[t]||e.removed)return;const n=CP(e,t);if(uc(e)){if(yP||(yP={},e.editorManager.on("removeEditor",(()=>{e.editorManager.activeEditor||yP&&(pe(yP,((t,n)=>{e.dom.unbind(CP(e,n))})),yP=null)}))),yP[t])return;const o=n=>{const o=n.target,r=e.editorManager.get();let s=r.length;for(;s--;){const e=r[s].getBody();(e===o||vP.isChildOf(o,e))&&wP(r[s],t,n)}};yP[t]=o,vP.bind(n,t,o)}else{const o=n=>{wP(e,t,n)};vP.bind(n,t,o),e.delegates[t]=o}},xP={...bP,bindPendingEventDelegates(){const e=this;dn.each(e._pendingNativeEvents,(t=>{EP(e,t)}))},toggleNativeEvent(e,t){const n=this;"focus"!==e&&"blur"!==e&&(n.removed||(t?n.initialized?EP(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&n.delegates&&(n.dom.unbind(CP(n,e),e,n.delegates[e]),delete n.delegates[e])))},unbindAllNativeEvents(){const e=this,t=e.getBody(),n=e.dom;e.delegates&&(pe(e.delegates,((t,n)=>{e.dom.unbind(CP(e,n),n,t)})),delete e.delegates),!e.inline&&t&&n&&(t.onload=null,n.unbind(e.getWin()),n.unbind(e.getDoc())),n&&(n.unbind(t),n.unbind(e.getContainer()))}},SP=e=>u(e)?{value:e.split(/[ ,]/),valid:!0}:x(e,u)?{value:e,valid:!0}:{valid:!1,message:"The value must be a string[] or a comma/space separated string."},_P=(e,t)=>e+(ot(t.message)?"":`. ${t.message}`),kP=e=>e.valid,NP=(e,t,n="")=>{const o=t(e);return b(o)?o?{value:e,valid:!0}:{valid:!1,message:n}:o},AP=e=>e.readonly,RP=["design","readonly"],DP=(e,t,n,o)=>{const r=n[t.get()],s=n[o];try{s.activate()}catch(e){return void console.error(`problem while activating editor mode ${o}:`,e)}r.deactivate(),r.editorReadOnly!==s.editorReadOnly&&((e,t)=>{const n=mn.fromDom(e.getBody());t?(e.readonly=!0,e.hasEditableRoot()&&(n.dom.contentEditable="true"),ZE(e)):(e.readonly=!1,JE(e))})(e,s.editorReadOnly),t.set(o),((e,t)=>{e.dispatch("SwitchMode",{mode:t})})(e,o)},TP=e=>{const t=Ne("design"),n=Ne({design:{activate:S,deactivate:S,editorReadOnly:!1},readonly:{activate:S,deactivate:S,editorReadOnly:!0}});return(e=>{const t=t=>{AP(e)&&(e=>H(e,(e=>"characterData"===e.type||"childList"===e.type)))(t)&&(e=>{const t=e.undoManager.add();C(t)&&(e.undoManager.undo(),e.undoManager.reset())})(e)},n=new MutationObserver(t);e.on("beforeinput paste cut dragend dragover draggesture dragdrop drop drag",(t=>{AP(e)&&t.preventDefault()})),e.on("BeforeExecCommand",(t=>{"Undo"!==t.command&&"Redo"!==t.command||!AP(e)||t.preventDefault()})),e.on("compositionstart",(()=>{AP(e)&&n.observe(e.getBody(),{characterData:!0,childList:!0,subtree:!0})})),e.on("compositionend",(()=>{if(AP(e)){const e=n.takeRecords();t(e)}n.disconnect()}))})(e),(e=>{(e=>{e.serializer?ox(e):e.on("PreInit",(()=>{ox(e)}))})(e),(e=>{e.on("ShowCaret ObjectSelected",(t=>{ex(e)&&t.preventDefault()})),e.on("DisabledStateChange",(t=>{t.isDefaultPrevented()||nx(e,t.state)}))})(e)})(e),{isReadOnly:()=>AP(e),set:o=>((e,t,n,o)=>{if(!(o===n.get()||e.initialized&&ex(e))){if(!Se(t,o))throw new Error(`Editor mode '${o}' is invalid`);e.initialized?DP(e,n,t,o):e.on("init",(()=>DP(e,n,t,o)))}})(e,n.get(),t,o),get:()=>t.get(),register:(e,t)=>{n.set(((e,t,n)=>{if($(RP,t))throw new Error(`Cannot override default mode ${t}`);return{...e,[t]:{...n,deactivate:()=>{try{n.deactivate()}catch(e){console.error(`problem while deactivating editor mode ${t}:`,e)}}}}})(n.get(),e,t))}}},OP=dn.each,BP=dn.explode,PP={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},LP=dn.makeMap("alt,ctrl,shift,meta,access"),MP=e=>{const t={},n=rn.os.isMacOS()||rn.os.isiOS();OP(BP(e.toLowerCase(),"+"),(e=>{(e=>e in LP)(e)?t[e]=!0:/^[0-9]{2,}$/.test(e)?t.keyCode=parseInt(e,10):(t.charCode=e.charCodeAt(0),t.keyCode=PP[e]||e.toUpperCase().charCodeAt(0))}));const o=[t.keyCode];let r;for(r in LP)t[r]?o.push(r):t[r]=!1;return t.id=o.join(","),t.access&&(t.alt=!0,n?t.ctrl=!0:t.shift=!0),t.meta&&(n?t.meta=!0:(t.ctrl=!0,t.meta=!1)),t};class IP{constructor(e){this.shortcuts={},this.pendingPatterns=[],this.editor=e;const t=this;e.on("keyup keypress keydown",(e=>{!t.hasModifier(e)&&!t.isFunctionKey(e)||e.isDefaultPrevented()||(OP(t.shortcuts,(n=>{t.matchShortcut(e,n)&&(t.pendingPatterns=n.subpatterns.slice(0),"keydown"===e.type&&t.executeShortcutAction(n))})),t.matchShortcut(e,t.pendingPatterns[0])&&(1===t.pendingPatterns.length&&"keydown"===e.type&&t.executeShortcutAction(t.pendingPatterns[0]),t.pendingPatterns.shift()))}))}add(e,t,n,o){const r=this,s=r.normalizeCommandFunc(n);return OP(BP(dn.trim(e)),(e=>{const n=r.createShortcut(e,t,s,o);r.shortcuts[n.id]=n})),!0}remove(e){const t=this.createShortcut(e);return!!this.shortcuts[t.id]&&(delete this.shortcuts[t.id],!0)}normalizeCommandFunc(e){const t=this,n=e;return"string"==typeof n?()=>{t.editor.execCommand(n,!1,null)}:dn.isArray(n)?()=>{t.editor.execCommand(n[0],n[1],n[2])}:n}createShortcut(e,t,n,o){const r=dn.map(BP(e,">"),MP);return r[r.length-1]=dn.extend(r[r.length-1],{func:n,scope:o||this.editor}),dn.extend(r[0],{desc:this.editor.translate(t),subpatterns:r.slice(1)})}hasModifier(e){return e.altKey||e.ctrlKey||e.metaKey}isFunctionKey(e){return"keydown"===e.type&&e.keyCode>=112&&e.keyCode<=123}matchShortcut(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)}executeShortcutAction(e){return e.func?e.func.call(e.scope):null}}const FP=()=>{const e=(()=>{const e={},t={},n={},o={},r={},s={},a={},i={},l={},d=(e,t)=>(n,o)=>{e[n.toLowerCase()]={...o,type:t}};return{addButton:d(e,"button"),addGroupToolbarButton:d(e,"grouptoolbarbutton"),addToggleButton:d(e,"togglebutton"),addMenuButton:d(e,"menubutton"),addSplitButton:d(e,"splitbutton"),addMenuItem:d(t,"menuitem"),addNestedMenuItem:d(t,"nestedmenuitem"),addToggleMenuItem:d(t,"togglemenuitem"),addAutocompleter:d(n,"autocompleter"),addContextMenu:d(r,"contextmenu"),addContextToolbar:d(s,"contexttoolbar"),addContextForm:(c=s,(e,t)=>{c[e.toLowerCase()]={type:"contextform",...t}}),addSidebar:d(i,"sidebar"),addView:d(l,"views"),addIcon:(e,t)=>o[e.toLowerCase()]=t,addContext:(e,t)=>a[e.toLowerCase()]=t,getAll:()=>({buttons:e,menuItems:t,icons:o,popups:n,contextMenus:r,contextToolbars:s,sidebars:i,views:l,contexts:a})};var c})();return{addAutocompleter:e.addAutocompleter,addButton:e.addButton,addContextForm:e.addContextForm,addContextMenu:e.addContextMenu,addContextToolbar:e.addContextToolbar,addIcon:e.addIcon,addMenuButton:e.addMenuButton,addMenuItem:e.addMenuItem,addNestedMenuItem:e.addNestedMenuItem,addSidebar:e.addSidebar,addSplitButton:e.addSplitButton,addToggleButton:e.addToggleButton,addGroupToolbarButton:e.addGroupToolbarButton,addToggleMenuItem:e.addToggleMenuItem,addView:e.addView,addContext:e.addContext,getAll:e.getAll}},UP=li.DOM,zP=dn.extend,jP=dn.each;class $P{constructor(e,t,n){this.plugins={},this.contentCSS=[],this.contentStyles=[],this.loadedCSS={},this.isNotDirty=!1,this.composing=!1,this.destroyed=!1,this.hasHiddenInput=!1,this.iframeElement=null,this.initialized=!1,this.readonly=!1,this.removed=!1,this.startContent="",this._pendingNativeEvents=[],this._skinLoaded=!1,this._editableRoot=!0,this.editorManager=n,zP(this,xP);const o=this;this.id=e,this.editorUid=Le(),this.hidden=!1;const r=((e,t)=>{const n=Fe(t);return GB($B||HB,$B,n,e,n)})(n.defaultOptions,t);this.options=((e,t,n=t)=>{const o={},r={},s=(e,t,n)=>{const o=NP(t,n);return kP(o)?(r[e]=o.value,!0):(console.warn(_P(`Invalid value passed for the ${e} option`,o)),!1)},a=e=>Se(o,e);return{register:(e,n)=>{const a=(e=>u(e.processor))(n)?(e=>{const t=(()=>{switch(e){case"array":return p;case"boolean":return b;case"function":return w;case"number":return E;case"object":return f;case"string":return u;case"string[]":return SP;case"object[]":return e=>x(e,f);case"regexp":return e=>m(e,RegExp);default:return M}})();return n=>NP(n,t,`The value must be a ${e}.`)})(n.processor):n.processor,i=((e,t,n)=>{if(!v(t)){const o=NP(t,n);if(kP(o))return o.value;console.error(_P(`Invalid default value passed for the "${e}" option`,o))}})(e,n.default,a);o[e]={...n,default:i,processor:a},xe(r,e).orThunk((()=>xe(t,e))).each((t=>s(e,t,a)))},isRegistered:a,get:e=>xe(r,e).orThunk((()=>xe(o,e).map((e=>e.default)))).getOrUndefined(),set:(e,t)=>{if(a(e)){const n=o[e];return n.immutable?(console.error(`"${e}" is an immutable option and cannot be updated`),!1):s(e,t,n.processor)}return console.warn(`"${e}" is not a registered option. Ensure the option has been registered before setting a value.`),!1},unset:e=>{const t=a(e);return t&&delete r[e],t},isSet:e=>Se(r,e),debug:()=>{try{console.log(JSON.parse(JSON.stringify(n,((e,t)=>b(t)||E(t)||u(t)||h(t)||p(t)||g(t)?t:Object.prototype.toString.call(t)))))}catch(e){console.error(e)}}}})(0,r,t),(e=>{const t=e.options.register;t("id",{processor:"string",default:e.id}),t("selector",{processor:"string"}),t("target",{processor:"object"}),t("suffix",{processor:"string"}),t("cache_suffix",{processor:"string"}),t("base_url",{processor:"string"}),t("referrer_policy",{processor:"string",default:""}),t("crossorigin",{processor:"function",default:N(void 0)}),t("language_load",{processor:"boolean",default:!0}),t("inline",{processor:"boolean",default:!1}),t("iframe_attrs",{processor:"object",default:{}}),t("doctype",{processor:"string",default:"<!DOCTYPE html>"}),t("document_base_url",{processor:"string",default:e.editorManager.documentBaseURL}),t("body_id",{processor:Nd(e,"tinymce"),default:"tinymce"}),t("body_class",{processor:Nd(e),default:""}),t("content_security_policy",{processor:"string",default:""}),t("br_in_pre",{processor:"boolean",default:!0}),t("forced_root_block",{processor:e=>{const t=u(e)&&nt(e);return t?{value:e,valid:t}:{valid:!1,message:"Must be a non-empty string."}},default:"p"}),t("forced_root_block_attrs",{processor:"object",default:{}}),t("newline_behavior",{processor:e=>{const t=$(["block","linebreak","invert","default"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: block, linebreak, invert or default."}},default:"default"}),t("br_newline_selector",{processor:"string",default:".mce-toc h2,figcaption,caption"}),t("no_newline_selector",{processor:"string",default:""}),t("keep_styles",{processor:"boolean",default:!0}),t("end_container_on_empty_block",{processor:e=>b(e)||u(e)?{valid:!0,value:e}:{valid:!1,message:"Must be boolean or a string"},default:"blockquote"}),t("font_size_style_values",{processor:"string",default:"xx-small,x-small,small,medium,large,x-large,xx-large"}),t("font_size_legacy_values",{processor:"string",default:"xx-small,small,medium,large,x-large,xx-large,300%"}),t("font_size_classes",{processor:"string",default:""}),t("automatic_uploads",{processor:"boolean",default:!0}),t("images_reuse_filename",{processor:"boolean",default:!1}),t("images_replace_blob_uris",{processor:"boolean",default:!0}),t("icons",{processor:"string",default:""}),t("icons_url",{processor:"string",default:""}),t("images_upload_url",{processor:"string",default:""}),t("images_upload_base_path",{processor:"string",default:""}),t("images_upload_credentials",{processor:"boolean",default:!1}),t("images_upload_handler",{processor:"function"}),t("language",{processor:"string",default:"en"}),t("language_url",{processor:"string",default:""}),t("entity_encoding",{processor:"string",default:"named"}),t("indent",{processor:"boolean",default:!0}),t("indent_before",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_after",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_use_margin",{processor:"boolean",default:!1}),t("indentation",{processor:"string",default:"40px"}),t("content_css",{processor:e=>{const t=!1===e||u(e)||x(e,u);return t?u(e)?{value:V(e.split(","),Je),valid:t}:p(e)?{value:e,valid:t}:!1===e?{value:[],valid:t}:{value:e,valid:t}:{valid:!1,message:"Must be false, a string or an array of strings."}},default:Sc(e)?[]:["default"]}),t("content_style",{processor:"string"}),t("content_css_cors",{processor:"boolean",default:!1}),t("font_css",{processor:e=>{const t=u(e)||x(e,u);return t?{value:p(e)?e:V(e.split(","),Je),valid:t}:{valid:!1,message:"Must be a string or an array of strings."}},default:[]}),t("extended_mathml_attributes",{processor:"string[]"}),t("extended_mathml_elements",{processor:"string[]"}),t("inline_boundaries",{processor:"boolean",default:!0}),t("inline_boundaries_selector",{processor:"string",default:"a[href],code,span.mce-annotation"}),t("object_resizing",{processor:e=>{const t=b(e)||u(e);return t?!1===e||wd.isiPhone()||wd.isiPad()?{value:"",valid:t}:{value:!0===e?"table,img,figure.image,div,video,iframe":e,valid:t}:{valid:!1,message:"Must be boolean or a string"}},default:!Ed}),t("resize_img_proportional",{processor:"boolean",default:!0}),t("event_root",{processor:"string"}),t("service_message",{processor:"string"}),t("onboarding",{processor:"boolean",default:!0}),t("tiny_cloud_entry_url",{processor:"string"}),t("theme",{processor:e=>!1===e||u(e)||w(e),default:"silver"}),t("theme_url",{processor:"string"}),t("formats",{processor:"object"}),t("format_empty_lines",{processor:"boolean",default:!1}),t("format_noneditable_selector",{processor:"string",default:""}),t("preview_styles",{processor:e=>{const t=!1===e||u(e);return t?{value:!1===e?"":e,valid:t}:{valid:!1,message:"Must be false or a string"}},default:"font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow"}),t("custom_ui_selector",{processor:"string",default:""}),t("hidden_input",{processor:"boolean",default:!0}),t("submit_patch",{processor:"boolean",default:!0}),t("encoding",{processor:"string"}),t("add_form_submit_trigger",{processor:"boolean",default:!0}),t("add_unload_trigger",{processor:"boolean",default:!0}),t("custom_undo_redo_levels",{processor:"number",default:0}),t("disable_nodechange",{processor:"boolean",default:!1}),t("disabled",{processor:t=>b(t)?(e.initialized&&xm(e)!==t&&Promise.resolve().then((()=>{((e,t)=>{e.dispatch("DisabledStateChange",{state:t})})(e,t)})),{valid:!0,value:t}):{valid:!1,message:"The value must be a boolean."},default:!1}),t("readonly",{processor:"boolean",default:!1}),t("editable_root",{processor:"boolean",default:!0}),t("plugins",{processor:"string[]",default:[]}),t("external_plugins",{processor:"object"}),t("forced_plugins",{processor:"string[]"}),t("model",{processor:"string",default:e.hasPlugin("rtc")?"plugin":"dom"}),t("model_url",{processor:"string"}),t("block_unsupported_drop",{processor:"boolean",default:!0}),t("visual",{processor:"boolean",default:!0}),t("visual_table_class",{processor:"string",default:"mce-item-table"}),t("visual_anchor_class",{processor:"string",default:"mce-item-anchor"}),t("iframe_aria_text",{processor:"string",default:"Rich Text Area".concat(e.hasPlugin("help")?". Press ALT-0 for help.":"")}),t("setup",{processor:"function"}),t("init_instance_callback",{processor:"function"}),t("url_converter",{processor:"function",default:e.convertURL}),t("url_converter_scope",{processor:"object",default:e}),t("urlconverter_callback",{processor:"function"}),t("allow_conditional_comments",{processor:"boolean",default:!1}),t("allow_html_data_urls",{processor:"boolean",default:!1}),t("allow_svg_data_urls",{processor:"boolean"}),t("allow_html_in_named_anchor",{processor:"boolean",default:!1}),t("allow_html_in_comments",{processor:"boolean",default:!1}),t("allow_script_urls",{processor:"boolean",default:!1}),t("allow_unsafe_link_target",{processor:"boolean",default:!1}),t("allow_mathml_annotation_encodings",{processor:e=>{const t=x(e,u);return t?{value:e,valid:t}:{valid:!1,message:"Must be an array of strings."}},default:[]}),t("convert_fonts_to_spans",{processor:"boolean",default:!0,deprecated:!0}),t("fix_list_elements",{processor:"boolean",default:!1}),t("preserve_cdata",{processor:"boolean",default:!1}),t("remove_trailing_brs",{processor:"boolean",default:!0}),t("pad_empty_with_br",{processor:"boolean",default:!1}),t("inline_styles",{processor:"boolean",default:!0,deprecated:!0}),t("element_format",{processor:"string",default:"html"}),t("entities",{processor:"string"}),t("schema",{processor:"string",default:"html5"}),t("convert_urls",{processor:"boolean",default:!0}),t("relative_urls",{processor:"boolean",default:!0}),t("remove_script_host",{processor:"boolean",default:!0}),t("custom_elements",{processor:kd}),t("extended_valid_elements",{processor:"string"}),t("invalid_elements",{processor:"string"}),t("invalid_styles",{processor:kd}),t("valid_children",{processor:"string"}),t("valid_classes",{processor:kd}),t("valid_elements",{processor:"string"}),t("valid_styles",{processor:kd}),t("verify_html",{processor:"boolean",default:!0}),t("auto_focus",{processor:e=>u(e)||!0===e}),t("browser_spellcheck",{processor:"boolean",default:!1}),t("protect",{processor:"array"}),t("images_file_types",{processor:"string",default:"jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp"}),t("deprecation_warnings",{processor:"boolean",default:!0}),t("a11y_advanced_options",{processor:"boolean",default:!1}),t("api_key",{processor:"string"}),t("license_key",{processor:"string"}),t("paste_block_drop",{processor:"boolean",default:!1}),t("paste_data_images",{processor:"boolean",default:!0}),t("paste_preprocess",{processor:"function"}),t("paste_postprocess",{processor:"function"}),t("paste_webkit_styles",{processor:"string",default:"none"}),t("paste_remove_styles_if_webkit",{processor:"boolean",default:!0}),t("paste_merge_formats",{processor:"boolean",default:!0}),t("smart_paste",{processor:"boolean",default:!0}),t("paste_as_text",{processor:"boolean",default:!1}),t("paste_tab_spaces",{processor:"number",default:4}),t("text_patterns",{processor:e=>x(e,f)||!1===e?{value:fd(!1===e?[]:e),valid:!0}:{valid:!1,message:"Must be an array of objects or false."},default:[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"#",format:"h1",trigger:"space"},{start:"##",format:"h2",trigger:"space"},{start:"###",format:"h3",trigger:"space"},{start:"####",format:"h4",trigger:"space"},{start:"#####",format:"h5",trigger:"space"},{start:"######",format:"h6",trigger:"space"},{start:"1.",cmd:"InsertOrderedList",trigger:"space"},{start:"*",cmd:"InsertUnorderedList",trigger:"space"},{start:"-",cmd:"InsertUnorderedList",trigger:"space"},{start:">",cmd:"mceBlockQuote",trigger:"space"},{start:"---",cmd:"InsertHorizontalRule",trigger:"space"}]}),t("text_patterns_lookup",{processor:e=>{return w(e)?{value:(t=e,e=>{const n=t(e);return fd(n)}),valid:!0}:{valid:!1,message:"Must be a single function"};var t},default:e=>[]}),t("noneditable_class",{processor:"string",default:"mceNonEditable"}),t("editable_class",{processor:"string",default:"mceEditable"}),t("noneditable_regexp",{processor:e=>x(e,Sd)?{value:e,valid:!0}:Sd(e)?{value:[e],valid:!0}:{valid:!1,message:"Must be a RegExp or an array of RegExp."},default:[]}),t("table_tab_navigation",{processor:"boolean",default:!0}),t("highlight_on_focus",{processor:"boolean",default:!0}),t("xss_sanitization",{processor:"boolean",default:!0}),t("details_initial_state",{processor:e=>{const t=$(["inherited","collapsed","expanded"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),t("details_serialized_state",{processor:e=>{const t=$(["inherited","collapsed","expanded"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),t("init_content_sync",{processor:"boolean",default:!1}),t("newdocument_content",{processor:"string",default:""}),t("sandbox_iframes",{processor:"boolean",default:!0}),t("sandbox_iframes_exclusions",{processor:"string[]",default:["youtube.com","youtu.be","vimeo.com","player.vimeo.com","dailymotion.com","embed.music.apple.com","open.spotify.com","giphy.com","dai.ly","codepen.io"]}),t("convert_unsafe_embeds",{processor:"boolean",default:!0}),t("user_id",{processor:"string",default:"Anonymous"}),t("fetch_users",{processor:e=>void 0===e?{valid:!0,value:void 0}:w(e)?{valid:!0,value:e}:{valid:!1,message:"fetch_users must be a function that returns a Promise<ExpectedUser[]>"}}),e.on("ScriptsLoaded",(()=>{t("directionality",{processor:"string",default:gi.isRtl()?"rtl":void 0}),t("placeholder",{processor:"string",default:xd.getAttrib(e.getElement(),"placeholder")})})),t("lists_indent_on_tab",{processor:"boolean",default:!0}),t("list_max_depth",{processor:e=>{const t=E(e);if(t){if(e<0)throw new Error("list_max_depth cannot be set to lower than 0");return{value:e,valid:t}}return{valid:!1,message:"Must be a number"}}})})(o),this.userLookup=(e=>{const t=new Map,n=new Map,o=e=>I.from(t.get(e)),r=(e,t)=>I.from(n.get(e)).each((({reject:o})=>{o(t),n.delete(e)})),s=Sm(e);return Object.freeze({userId:s,fetchUsers:s=>{const a=_m(e);if(!Array.isArray(s))return{};if(!a)return se(s,(e=>Promise.resolve({id:e,name:e,avatar:IB(e)})));const i=ue(Y(s,(e=>!o(e).isSome())));return q(i,(e=>{const o=new Promise(((t,o)=>{n.set(e,{resolve:t,reject:o})}));((e,n)=>{t.set(n,e)})(o,e)})),i.length>0&&a(i).then(zB).then((e=>{const t=new Set(V(e,(e=>e.id)));q(e,(e=>((e,t)=>I.from(n.get(e)).each((({resolve:o})=>{o(t),n.delete(e)})))(e.id,e))),q(i,(e=>{t.has(e)||r(e,new Error(`User ${e} not found`))}))})).catch((e=>{q(i,(t=>r(t,e instanceof Error?e:new Error("Network error"))))})),X(s,((e,t)=>(e[t]=o(t).getOr(Promise.resolve({id:t,name:t,avatar:IB(t)})),e)),{})}})})(this);const s=this.options.get;s("deprecation_warnings")&&((e,t)=>{((e,t)=>{const n=ow(e),o=aw(t),r=o.length>0,s=n.length>0,a="mobile"===t.theme;if(r||s||a){const e="\n- ",t=a?`\n\nThemes:${e}mobile`:"",i=r?`\n\nPlugins:${e}${o.join(e)}`:"",l=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled and have been removed in TinyMCE 8.0. These features will no longer work and should be removed from the TinyMCE configuration. See https://www.tiny.cloud/docs/tinymce/8/migration-from-7x/ for more information."+t+i+l)}})(e,t),((e,t)=>{const n=rw(e),o=iw(t),r=o.length>0,s=n.length>0;if(r||s){const e="\n- ",t=r?`\n\nPlugins:${e}${o.map(lw).join(e)}`:"",a=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled but will be removed soon."+t+a)}})(e,t)})(t,r);const a=s("suffix");a&&(n.suffix=a),this.suffix=n.suffix;const i=s("base_url");i&&n._setBaseUrl(i),this.baseUri=n.baseURI;const l=Zd(o);l&&(ci.ScriptLoader._setReferrerPolicy(l),li.DOM.styleSheetLoader._setReferrerPolicy(l)),ci.ScriptLoader._setCrossOrigin((e=>Jd(o)(e,"script"))),li.DOM.styleSheetLoader._setCrossOrigin((e=>Jd(o)(e,"stylesheet")));const d=Bc(o);C(d)&&li.DOM.styleSheetLoader._setContentCssCors(d),pi.languageLoad=s("language_load"),pi.baseURL=n.baseURL,this.setDirty(!1),this.documentBaseURI=new AC(Dd(o),{base_uri:this.baseUri}),this.baseURI=this.baseUri,this.inline=Sc(o),this.hasVisual=Ic(o),this.shortcuts=new IP(this),this.editorCommands=new fP(this),cP(this);const c=s("cache_suffix");c&&(rn.cacheSuffix=c.replace(/^[\?\&]+/,"")),this.ui={registry:FP(),styleSheetLoader:void 0,show:S,hide:S,setEnabled:S,isEnabled:M},this.mode=TP(o),Object.defineProperty(this,"editorUid",{writable:!1,configurable:!1,enumerable:!0}),n.dispatch("SetupEditor",{editor:this});const y=jc(o);w(y)&&y.call(o,o)}render(){(e=>{const t=e.id;gi.setCode(ec(e));const n=()=>{OB.unbind(window,"ready",n),e.render()};if(!Za.Event.domLoaded)return void OB.bind(window,"ready",n);if(!e.getElement())return;const o=mn.fromDom(e.getElement()),r=xo(o);e.on("remove",(()=>{W(o.dom.attributes,(e=>wo(o,e.name))),bo(o,r)})),e.ui.styleSheetLoader=((e,t)=>ea.forElement(e,{contentCssCors:Bc(t),referrerPolicy:Zd(t)}))(o,e),Sc(e)?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");const s=e.getElement().form||OB.getParent(t,"form");s&&(e.formElement=s,_c(e)&&!rs(e.getElement())&&(OB.insertAfter(OB.create("input",{type:"hidden",name:t}),t),e.hasHiddenInput=!0),e.formEventDelegate=t=>{e.dispatch(t.type,t)},OB.bind(s,"submit reset",e.formEventDelegate),e.on("reset",(()=>{e.resetContent()})),!kc(e)||s.submit.nodeType||s.submit.length||s._mceOldSubmit||(s._mceOldSubmit=s.submit,s.submit=()=>(e.editorManager.triggerSave(),e.setDirty(!1),s._mceOldSubmit(s)))),e.windowManager=OE(e),e.notificationManager=RE(e),(e=>"xml"===e.options.get("encoding"))(e)&&e.on("GetContent",(e=>{e.save&&(e.content=OB.encode(e.content))})),Nc(e)&&e.on("submit",(()=>{e.initialized&&e.save()})),Ac(e)&&(e._beforeUnload=()=>{!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),LB(e,e.suffix)})(this)}focus(e){this.execCommand("mceFocus",!1,e)}hasFocus(){return Dg(this)}translate(e){return gi.translate(e)}getParam(e,t,n){const o=this.options;return o.isRegistered(e)||(C(n)?o.register(e,{processor:n,default:t}):o.register(e,{processor:M,default:t})),o.isSet(e)||v(t)?o.get(e):t}hasPlugin(e,t){return!(!$(Pc(this),e)||t&&void 0===DE.get(e))}nodeChanged(e){this._nodeChangeDispatcher.nodeChanged(e)}addCommand(e,t,n){this.editorCommands.addCommand(e,t,n)}addQueryStateHandler(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)}addQueryValueHandler(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)}addShortcut(e,t,n,o){this.shortcuts.add(e,t,n,o)}execCommand(e,t,n,o){return this.editorCommands.execCommand(e,t,n,o)}queryCommandState(e){return this.editorCommands.queryCommandState(e)}queryCommandValue(e){return this.editorCommands.queryCommandValue(e)}queryCommandSupported(e){return this.editorCommands.queryCommandSupported(e)}show(){const e=this;e.hidden&&(e.hidden=!1,e.inline?e.getBody().contentEditable="true":(UP.show(e.getContainer()),UP.hide(e.id)),e.load(),e.dispatch("show"))}hide(){const e=this;e.hidden||(e.save(),e.inline?(e.getBody().contentEditable="false",e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(UP.hide(e.getContainer()),UP.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.dispatch("hide"))}isHidden(){return this.hidden}setProgressState(e,t){this.dispatch("ProgressState",{state:e,time:t})}load(e={}){const t=this,n=t.getElement();if(!t.removed&&n){const o={...e,load:!0},r=rs(n)?n.value:n.innerHTML;t.setContent(r,o),o.no_events||t.dispatch("LoadContent",{...o,element:n})}}save(e={}){const t=this;let n=t.getElement();if(!n||!t.initialized||t.removed)return"";const o={...e,save:!0,element:n};let r=t.getContent(o);const s={...o,content:r};if(s.no_events||t.dispatch("SaveContent",s),"raw"===s.format&&t.dispatch("RawSaveContent",s),r=s.content,rs(n))n.value=r;else{!e.is_removing&&t.inline||(n.innerHTML=r);const o=UP.getParent(t.id,"form");o&&jP(o.elements,(e=>e.name!==t.id||(e.value=r,!1)))}return s.element=o.element=n=null,!1!==s.set_dirty&&t.setDirty(!1),r}setContent(e,t){vE(this,e,t)}getContent(e){return((e,t={})=>{const n=((e,t)=>({...e,format:t,get:!0,getInner:!0}))(t,t.format?t.format:"html");return YC(e,n).fold(A,(t=>{const n=((e,t)=>dE(e).editor.getContent(t))(e,t);return GC(e,n,t)}))})(this,e)}insertContent(e,t){t&&(e=zP({content:e},t)),this.execCommand("mceInsertContent",!1,e)}resetContent(e){void 0===e?vE(this,this.startContent,{initial:!0,format:"raw"}):vE(this,e,{initial:!0}),this.undoManager.reset(),this.setDirty(!1),this.nodeChanged()}isDirty(){return!this.isNotDirty}setDirty(e){const t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.dispatch("dirty")}getContainer(){const e=this;return e.container||(e.container=e.editorContainer||UP.get(e.id+"_parent")),e.container}getContentAreaContainer(){return this.contentAreaContainer}getElement(){return this.targetElm||(this.targetElm=UP.get(this.id)),this.targetElm}getWin(){const e=this;if(!e.contentWindow){const t=e.iframeElement;t&&(e.contentWindow=t.contentWindow)}return e.contentWindow}getDoc(){const e=this;if(!e.contentDocument){const t=e.getWin();t&&(e.contentDocument=t.document)}return e.contentDocument}getBody(){var e,t;const n=this.getDoc();return null!==(t=null!==(e=this.bodyElement)&&void 0!==e?e:null==n?void 0:n.body)&&void 0!==t?t:null}convertURL(e,t,n){const o=this,r=o.options.get,s=Hc(o);if(w(s))return s.call(o,e,n,!0,t);if(!r("convert_urls")||"link"===n||f(n)&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length)return e;const a=new AC(e);return"http"!==a.protocol&&"https"!==a.protocol&&""!==a.protocol?e:r("relative_urls")?o.documentBaseURI.toRelative(e):e=o.documentBaseURI.toAbsolute(e,r("remove_script_host"))}addVisual(e){((e,t)=>{((e,t)=>{cE(e).editor.addVisual(t)})(e,t)})(this,e)}setEditableRoot(e){((e,t)=>{e._editableRoot!==t&&(e._editableRoot=t,ex(e)||(e.getBody().contentEditable=String(e.hasEditableRoot()),e.nodeChanged()),((e,t)=>{e.dispatch("EditableRootStateChange",{state:t})})(e,t))})(this,e)}hasEditableRoot(){return this._editableRoot}remove(){(e=>{if(!e.removed){const{_selectionOverrides:t,editorUpload:n}=e,o=e.getBody(),r=e.getElement();o&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&C(null==r?void 0:r.nextSibling)&&yE.remove(r.nextSibling),(e=>{e.dispatch("remove")})(e),e.editorManager.remove(e),!e.inline&&o&&(e=>{yE.setStyle(e.id,"display",e.orgDisplay)})(e),(e=>{e.dispatch("detach")})(e),yE.remove(e.getContainer()),CE(t),CE(n),e.destroy()}})(this)}destroy(e){((e,t)=>{const{selection:n,dom:o}=e;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),CE(n),CE(o)),(e=>{const t=e.formElement;t&&(t._mceOldSubmit&&(t.submit=t._mceOldSubmit,delete t._mceOldSubmit),yE.unbind(t,"submit reset",e.formEventDelegate))})(e),(e=>{const t=e;t.contentAreaContainer=t.formElement=t.container=t.editorContainer=null,t.bodyElement=t.contentDocument=t.contentWindow=null,t.iframeElement=t.targetElm=null;const n=e.selection;if(n){const e=n.dom;t.selection=n.win=n.dom=e.doc=null}})(e),e.destroyed=!0):e.remove())})(this,e)}uploadImages(){return this.editorUpload.uploadImages()}_scanForImages(){return this.editorUpload.scanForImages()}}const HP=li.DOM,VP=dn.each;let qP,WP=!1,KP=[];const YP=e=>{const t=e.type;VP(ZP.get(),(n=>{switch(t){case"scroll":n.dispatch("ScrollWindow",e);break;case"resize":n.dispatch("ResizeWindow",e)}}))},GP=e=>{if(e!==WP){const t=li.DOM;e?(t.bind(window,"resize",YP),t.bind(window,"scroll",YP)):(t.unbind(window,"resize",YP),t.unbind(window,"scroll",YP)),WP=e}},XP=e=>{const t=KP;return KP=Y(KP,(t=>e!==t)),ZP.activeEditor===e&&(ZP.activeEditor=KP.length>0?KP[0]:null),ZP.focusedEditor===e&&(ZP.focusedEditor=null),t.length!==KP.length},QP="CSS1Compat"!==document.compatMode,ZP={...bP,baseURI:null,baseURL:null,defaultOptions:{},documentBaseURL:null,suffix:null,pageUid:Le(),majorVersion:"8",minorVersion:"0.2",releaseDate:"2025-08-14",i18n:gi,activeEditor:null,focusedEditor:null,setup(){const e=this;let t="",n="",o=AC.getDocumentBaseUrl(document.location);/^[^:]+:\/\/\/?[^\/]+\//.test(o)&&(o=o.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(o)||(o+="/"));const r=window.tinymce||window.tinyMCEPreInit;if(r)t=r.base||r.baseURL,n=r.suffix;else{const e=document.getElementsByTagName("script");for(let o=0;o<e.length;o++){const r=e[o].src||"";if(""===r)continue;const s=r.substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==s.indexOf(".min")&&(n=".min"),t=r.substring(0,r.lastIndexOf("/"));break}}if(!t&&document.currentScript){const e=document.currentScript.src;-1!==e.indexOf(".min")&&(n=".min"),t=e.substring(0,e.lastIndexOf("/"))}}var s;e.baseURL=new AC(o).toAbsolute(t),e.documentBaseURL=o,e.baseURI=new AC(e.baseURL),e.suffix=n,(s=e).on("AddEditor",D(kg,s)),s.on("RemoveEditor",D(Ng,s)),q(["majorVersion","minorVersion","releaseDate","pageUid","_addLicenseKeyManager"],(t=>Object.defineProperty(e,t,{writable:!1,configurable:!1,enumerable:!0})))},overrideDefaults(e){const t=e.base_url;t&&this._setBaseUrl(t);const n=e.suffix;n&&(this.suffix=n),this.defaultOptions=e;const o=e.plugin_base_urls;void 0!==o&&pe(o,((e,t)=>{pi.PluginManager.urls[t]=e}))},init(e){const t=this;let n;const o=dn.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");let r=e=>{n=e};const s=()=>{let n=0;const a=[];let i;HP.unbind(window,"ready",s),(()=>{const n=e.onpageload;n&&n.apply(t,[])})(),i=ue((e=>rn.browser.isIE()||rn.browser.isEdge()?(IE("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tiny.cloud/docs/tinymce/8/support/#supportedwebbrowsers"),[]):QP?(IE("Failed to initialize the editor as the document is not in standards mode. TinyMCE requires standards mode."),[]):u(e.selector)?HP.select(e.selector):C(e.target)?[e.target]:[])(e)),dn.each(i,(e=>{var n;(n=t.get(e.id))&&n.initialized&&!(n.getContainer()||n.getBody()).parentNode&&(XP(n),n.unbindAllNativeEvents(),n.destroy(!0),n.removed=!0)})),i=dn.grep(i,(e=>!t.get(e.id))),0===i.length?r([]):VP(i,(s=>{((e,t)=>e.inline&&t.tagName.toLowerCase()in o)(e,s)?IE("Could not initialize inline editor on invalid inline target element",s):((e,o,s)=>{const l=new $P(e,o,t);a.push(l),l.on("init",(()=>{++n===i.length&&r(a)})),l.targetElm=l.targetElm||s,l.render()})((e=>{let t=e.id;return t||(t=xe(e,"name").filter((e=>!HP.get(e))).getOrThunk(HP.uniqueId),e.setAttribute("id",t)),t})(s),e,s)}))};return HP.bind(window,"ready",s),new Promise((e=>{n?e(n):r=t=>{e(t)}}))},get(e){return 0===arguments.length?KP.slice(0):u(e)?Z(KP,(t=>t.id===e)).getOr(null):E(e)&&KP[e]?KP[e]:null},add(e){const t=this,n=t.get(e.id);return n===e||(null===n&&KP.push(e),GP(!0),t.activeEditor=e,t.dispatch("AddEditor",{editor:e}),qP||(qP=e=>{const n=t.dispatch("BeforeUnload");if(n.returnValue)return e.preventDefault(),e.returnValue=n.returnValue,n.returnValue},window.addEventListener("beforeunload",qP))),e},createEditor(e,t){return this.add(new $P(e,t,this))},remove(e){const t=this;let n;if(e){if(!u(e))return n=e,h(t.get(n.id))?null:(XP(n)&&t.dispatch("RemoveEditor",{editor:n}),0===KP.length&&window.removeEventListener("beforeunload",qP),n.remove(),GP(KP.length>0),n);VP(HP.select(e),(e=>{n=t.get(e.id),n&&t.remove(n)}))}else for(let e=KP.length-1;e>=0;e--)t.remove(KP[e])},execCommand(e,t,n){var o;const r=this,s=f(n)?null!==(o=n.id)&&void 0!==o?o:n.index:n;switch(e){case"mceAddEditor":if(!r.get(s)){const e=n.options;new $P(s,e,r).render()}return!0;case"mceRemoveEditor":{const e=r.get(s);return e&&e.remove(),!0}case"mceToggleEditor":{const e=r.get(s);return e?(e.isHidden()?e.show():e.hide(),!0):(r.execCommand("mceAddEditor",!1,n),!0)}}return!!r.activeEditor&&r.activeEditor.execCommand(e,t,n)},triggerSave:()=>{VP(KP,(e=>{e.save()}))},addI18n:(e,t)=>{gi.add(e,t)},translate:e=>gi.translate(e),setActive(e){const t=this.activeEditor;this.activeEditor!==e&&(t&&t.dispatch("deactivate",{relatedTarget:e}),e.dispatch("activate",{relatedTarget:t})),this.activeEditor=e},_setBaseUrl(e){this.baseURL=new AC(this.documentBaseURL).toAbsolute(e.replace(/\/+$/,"")),this.baseURI=new AC(this.baseURL)},_addLicenseKeyManager:e=>GE.add(e)};ZP.setup();const JP=(()=>{const e=We();return{FakeClipboardItem:e=>({items:e,types:fe(e),getType:t=>xe(e,t).getOrUndefined()}),write:t=>{e.set(t)},read:()=>e.get().getOrUndefined(),clear:e.clear}})(),eL=Math.min,tL=Math.max,nL=Math.round,oL=(e,t,n)=>{let o=t.x,r=t.y;const s=e.w,a=e.h,i=t.w,l=t.h,d=(n||"").split("");return"b"===d[0]&&(r+=l),"r"===d[1]&&(o+=i),"c"===d[0]&&(r+=nL(l/2)),"c"===d[1]&&(o+=nL(i/2)),"b"===d[3]&&(r-=a),"r"===d[4]&&(o-=s),"c"===d[3]&&(r-=nL(a/2)),"c"===d[4]&&(o-=nL(s/2)),rL(o,r,s,a)},rL=(e,t,n,o)=>({x:e,y:t,w:n,h:o}),sL={inflate:(e,t,n)=>rL(e.x-t,e.y-n,e.w+2*t,e.h+2*n),relativePosition:oL,findBestRelativePosition:(e,t,n,o)=>{for(let r=0;r<o.length;r++){const s=oL(e,t,o[r]);if(s.x>=n.x&&s.x+s.w<=n.w+n.x&&s.y>=n.y&&s.y+s.h<=n.h+n.y)return o[r]}return null},intersect:(e,t)=>{const n=tL(e.x,t.x),o=tL(e.y,t.y),r=eL(e.x+e.w,t.x+t.w),s=eL(e.y+e.h,t.y+t.h);return r-n<0||s-o<0?null:rL(n,o,r-n,s-o)},clamp:(e,t,n)=>{let o=e.x,r=e.y,s=e.x+e.w,a=e.y+e.h;const i=t.x+t.w,l=t.y+t.h,d=tL(0,t.x-o),c=tL(0,t.y-r),m=tL(0,s-i),u=tL(0,a-l);return o+=d,r+=c,n&&(s+=d,a+=c,o-=m,r-=u),s-=m,a-=u,rL(o,r,s-o,a-r)},create:rL,fromClientRect:e=>rL(e.left,e.top,e.width,e.height)},aL=(()=>{const e={},t={},n={};return{load:(n,o)=>{const r=`Script at URL "${o}" failed to load`,s=`Script at URL "${o}" did not call \`tinymce.Resource.add('${n}', data)\` within 1 second`;if(void 0!==e[n])return e[n];{const a=new Promise(((e,a)=>{const i=((e,t,n=1e3)=>{let o=!1,r=null;const s=e=>(...t)=>{o||(o=!0,null!==r&&(window.clearTimeout(r),r=null),e.apply(null,t))},a=s(e),i=s(t);return{start:(...e)=>{o||null!==r||(r=window.setTimeout((()=>i.apply(null,e)),n))},resolve:a,reject:i}})(e,a);t[n]=i.resolve,ci.ScriptLoader.loadScript(o).then((()=>i.start(s)),(()=>i.reject(r)))}));return e[n]=a,a}},add:(o,r)=>{void 0!==t[o]&&(t[o](r),delete t[o]),e[o]=Promise.resolve(r),n[o]=r},has:e=>e in n,get:e=>n[e],unload:t=>{delete e[t],delete n[t]}}})();let iL;try{const e="__storage_test__";iL=window.localStorage,iL.setItem(e,e),iL.removeItem(e)}catch(e){iL=(()=>{let e={},t=[];const n={getItem:t=>e[t]||null,setItem:(n,o)=>{t.push(n),e[n]=String(o)},key:e=>t[e],removeItem:n=>{t=t.filter((e=>e===n)),delete e[n]},clear:()=>{t=[],e={}},length:0};return Object.defineProperty(n,"length",{get:()=>t.length,configurable:!1,enumerable:!1}),n})()}const lL={geom:{Rect:sL},util:{Delay:bg,Tools:dn,VK:Bg,URI:AC,EventDispatcher:pP,Observable:bP,I18n:gi,LocalStorage:iL,ImageUploader:e=>{const t=lx(),n=ux(e,t);return{upload:(t,o=!0)=>n.upload(t,o?mx(e):void 0)}}},dom:{EventUtils:Za,TreeWalker:Hr,TextSeeker:Ai,DOMUtils:li,ScriptLoader:ci,RangeUtils:Xg,Serializer:bE,StyleSheetLoader:Js,ControlSelection:Fg,BookmarkManager:ag,Selection:gE,Event:Za.Event},html:{Styles:Ha,Entities:ba,Node:xp,Schema:Pa,DomParser:VC,Writer:jp,Serializer:$p},Env:rn,AddOnManager:pi,Annotator:sg,Formatter:Sx,UndoManager:kx,EditorCommands:fP,WindowManager:OE,NotificationManager:RE,EditorObservable:xP,Shortcuts:IP,Editor:$P,FocusManager:hg,EditorManager:ZP,DOM:li.DOM,ScriptLoader:ci.ScriptLoader,PluginManager:DE,ThemeManager:TE,ModelManager:EE,IconManager:wE,Resource:aL,FakeClipboard:JP,trim:dn.trim,isArray:dn.isArray,is:dn.is,toArray:dn.toArray,makeMap:dn.makeMap,each:dn.each,map:dn.map,grep:dn.grep,inArray:dn.inArray,extend:dn.extend,walk:dn.walk,resolve:dn.resolve,explode:dn.explode,_addCacheSuffix:dn._addCacheSuffix},dL=dn.extend(ZP,lL);(e=>{window.tinymce=e,window.tinyMCE=e})(dL),(e=>{if("object"==typeof module)try{module.exports=e}catch(e){}})(dL)}();