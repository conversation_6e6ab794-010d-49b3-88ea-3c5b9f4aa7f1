#!/bin/bash

# 需要检查的资源键列表
declare -a keys=(
    # Header组件字段
    "FormFields_CompanyName"
    "FormFields_CompanyNameHelpText"
    "FormFields_Logo"
    "FormFields_LogoHelpText"
    "FormFields_ContactInfo"
    "FormFields_ContactInfoHelpText"
    "FormFields_Phone"
    "FormFields_PhoneHelpText"
    "FormFields_Email"
    "FormFields_EmailHelpText"
    "FormFields_MenuItems"
    "FormFields_MenuItemsHelpText"
    "FormFields_MenuItemText"
    "FormFields_MenuItemTextHelpText"
    "FormFields_MenuItemUrl"
    "FormFields_MenuItemUrlHelpText"
    "FormFields_IsActive"
    "FormFields_IsActiveHelpText"
    "FormFields_SubMenuItems"
    "FormFields_SubMenuItemsHelpText"
    "FormFields_SubMenuItemText"
    "FormFields_SubMenuItemTextHelpText"
    "FormFields_SubMenuItemUrl"
    "FormFields_SubMenuItemUrlHelpText"
    "FormFields_MobileMenuButtonText"
    "FormFields_MobileMenuButtonTextHelpText"
    "FormFields_ShowLanguageSelector"
    "FormFields_ShowLanguageSelectorHelpText"
    "FormFields_ShowDarkModeToggle"
    "FormFields_ShowDarkModeToggleHelpText"
    "FormFields_ShowSearchBox"
    "FormFields_ShowSearchBoxHelpText"

    # Footer组件字段
    "FormFields_CompanyDescription"
    "FormFields_CompanyDescriptionHelpText"
    "FormFields_Copyright"
    "FormFields_CopyrightHelpText"
    "FormFields_FooterSections"
    "FormFields_FooterSectionsHelpText"
    "FormFields_SectionTitle"
    "FormFields_SectionTitleHelpText"
    "FormFields_SectionLinks"
    "FormFields_SectionLinksHelpText"
    "FormFields_LinkText"
    "FormFields_LinkTextHelpText"
    "FormFields_LinkUrl"
    "FormFields_LinkUrlHelpText"
    "FormFields_OpenInNewTab"
    "FormFields_OpenInNewTabHelpText"
    "FormFields_SocialLinks"
    "FormFields_SocialLinksHelpText"
    "FormFields_SocialPlatform"
    "FormFields_SocialPlatformHelpText"
    "FormFields_SocialUrl"
    "FormFields_SocialUrlHelpText"
    "FormFields_SocialIcon"
    "FormFields_SocialIconHelpText"
    "FormFields_ShowAdminLinks"
    "FormFields_ShowAdminLinksHelpText"
    "FormFields_BorderColor"
    "FormFields_BorderColorHelpText"
    "FormFields_LinkHoverColor"
    "FormFields_LinkHoverColorHelpText"
    "FormFields_PleaseSelect"
    "FormFields_Other"

    # 表单分组
    "FormGroups_BasicInfo"
    "FormGroups_MediaContent"
    "FormGroups_ContactInfo"
    "FormGroups_Navigation"
    "FormGroups_MobileSettings"
    "FormGroups_CompanyInfo"
    "FormGroups_SocialMedia"
)

RESOURCE_FILE="/mnt/d/Codes/MlSoft.Sites/src/Web/Resources/FormResource.resx"

echo "检查缺失的资源键..."
echo "=========================="

missing_keys=()

for key in "${keys[@]}"; do
    if ! grep -q "name=\"$key\"" "$RESOURCE_FILE"; then
        echo "缺失: $key"
        missing_keys+=("$key")
    fi
done

echo ""
echo "找到 ${#missing_keys[@]} 个缺失的键"
echo "缺失的键列表："
printf '%s\n' "${missing_keys[@]}"